export default {
    'primary-color': '#5478EE', // 全局主色 '#EC6F00' #5596F1
    // 'link-color': '#000', // 链接色
    'success-color': '#52c41a', // 成功色
    'warning-color': '#faad14', // 警告色
    'error-color': '#f5222d', // 错误色
    // 'font-size-base': '16px', // 主字号
    // 'btn-font-size-sm': '14px', // 因为@btn-font-size-sm: @font-size-base; 这里恢复14px大小
    'heading-color': 'rgba(0, 0, 0, 0.85)', // 标题色
    'text-color': 'rgba(0, 0, 0, 0.65)', // 主文本色
    'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
    'disabled-color': 'rgba(0, 0, 0, 0.25)', // 失效色
    'border-radius-base': '8px', // 组件/浮层圆角
    'border-color-base': '#d9d9d9', // 边框色
    'box-shadow-base':
        '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),0 9px 28px 8px rgba(0, 0, 0, 0.05)', // 浮层阴影 
    'background-color-light': '#f0f4fe',


    // Buttons
    'btn-border-radius-base': '8px',

    // Card
    'card-radius': '8px',
    'card-shadow': '0px 1px 3px 0px rgba(0,0,0,0.10)',

    // Modal
    'modal-header-title-font-size': '22px',
    'modal-header-border-width': '0',
    'modal-header-padding-vertical': '32px',
    'modal-header-padding-horizontal': '32px',
    'modal-body-padding': '32px',
    'modal-footer-border-width': '0',
    'modal-footer-padding-vertical': '24px',
    'modal-header-close-size': '86px',

    // Checkbox
    'checkbox-border-radius': '2px'
}