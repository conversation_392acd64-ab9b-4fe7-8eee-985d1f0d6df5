<script setup lang="ts">
// 接收 props  emit 事件
</script>

<template>
  <div class="bg-white rounded-lg shadow p-6 flex flex-col h-full">
    <div class="flex justify-between items-center mb-2">
      <span class="font-bold text-lg">前端工程师</span>
      <div class="space-x-2">
        <a class="text-blue-500 cursor-pointer">编辑</a>
        <a class="text-blue-500 cursor-pointer">删除</a>
      </div>
    </div>
    <div class="text-sm text-gray-700 mb-1">
      技术栈：精通 HTML5、CSS3、JavaScript（含 ES6+），熟悉至少一种主流框架（Vue/React/Angular）
    </div>
    <div class="text-sm text-gray-700 mb-1">
      工具使用：熟悉 Webpack、Vite 等构建工具，熟悉 Git、Jest 等开发工具。
    </div>
    <div class="text-sm text-gray-700 truncate">
      经验/加分：有重构设计、开源项目或实习经验，能独立完成前端开发和项目交付。
    </div>
  </div>
</template>

<style scoped>
</style>
