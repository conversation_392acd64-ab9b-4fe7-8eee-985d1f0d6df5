<template>
  <!-- width="820px" -->
  <a-modal
    class="paper-type-modal"
    v-model:visible="visible"
    title="请选择试卷类型"
    width="820px"
    style="margin-top: 100px"
    :footer="null"
    :maskClosable="false"
    @cancel="close"
  >
    <div class="type-cards">
     <div class="item" v-for="(item,index) in typeList" :key="item.value" @click="handleClick(index)">
        <svg-icon v-if="!item.checked" :name="item.name" width="100px" height="100px" />
        <svg-icon v-else :name="item.activeName" width="100px" height="100px" />
        <a-radio class="select-radio" v-model:checked="item.checked">{{item.label}}</a-radio>
        <div class="type-desc">
          <p>{{item.desc1}}</p>
          <p>{{item.desc2}}</p>
        </div>
     </div>
    </div>
    <div class="btn-group">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" @click="createPaperType" :disabled="!type">下一步</a-button>
     </div>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  paperTypeModalVisible: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['closeModal', 'getPaperType'])


const typeList = ref([
  {
    name: 'ol-paper',
    activeName: 'ol-paper-active',
    label: '顺序试卷',
    desc1: '选择特定试题',
    desc2: '生成一份试卷(题目、选项顺序固定)',
    checked: false,
    value: 'regular'
  },
  {
    name: 'draw-paper',
    activeName: 'draw-paper-active',
    label: '抽题试卷',
    desc1: '给定试卷范围和抽题数量',
    desc2: '抽题生成试卷',
    checked: false,
    value: 'drawer'
  },
  {
    name: 'ul-paper',
    activeName: 'ul-paper-active',
    label: '随机试卷',
    desc1: '选择特定试题',
    desc2: '生成一份试卷(题目、选项顺序乱序)',
    checked: false,
    value: 'random'
  }
])

const handleClick = (index:number) => {
  typeList.value.forEach(item => {
    item.checked = false
  })
  typeList.value[index].checked = true
  type.value = typeList.value[index].value
}

const close = () => {
  emits('closeModal')
}

const type = ref('')
const createPaperType = () => {
  emits('getPaperType', type)
}

watch(
  () => props.paperTypeModalVisible,
  (val) => {
    visible.value = val
  }
)

const visible = ref(false)
</script>

<style lang="less" scoped>
.paper-type-modal {
  .type-cards {
    display: flex;
    justify-content: space-between;
    padding: 20px 50px;
    .item {
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      width: 220px;
      text-align: center;
      .select-radio {
        margin: 20px 0;
        color: #121633;
        font-size: 15px;
      }
      .type-desc p{
        font-size: 14px;
        color: #b5b5b7;
      }
    }
    
  }
  .btn-group {
    padding: 30px 0 20px;
    text-align: center;
    .ant-btn {
      width: 80px;
      font-size: 15px;
      border-radius: 3px;
      margin-right: 20px;
      &:first-child {
        border-color: #3158BD;
        color: #3158BD;
      }
    }
  }
}

</style>
<style lang="less">
.paper-type-modal .ant-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #121633;
}
</style>
