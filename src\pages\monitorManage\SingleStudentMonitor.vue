<template>
  <div>
    <div class="stu-info">
      <div class="stu-info-main">
        <div style="display: flex; align-items: center;">
          <span class="stu-name" :title="formState.studentName">{{ formState.studentName }}</span>
          <span class="stu-tag">{{ formState.sex == 1 ? '男' : '女' }}</span>
          <span class="stu-tag">{{ formState.age }}岁</span>
          <span class="stu-school" style="margin-left: 24px;">{{ formState.graduated_school }}</span>
          <a-divider type="vertical"></a-divider>
          <span class="stu-school">{{ formState.heducation }}</span>
        </div>
        <div class="stu-info-item">
          <div class="label">手机号</div>
          <div class="value">{{ formState.phone }}</div>
        </div>
        <div class="stu-info-item">
          <div class="label">电子邮箱</div>
          <div class="value">{{ formState.email }}</div>
        </div>
        <div class="stu-info-item">
          <div class="label">报考单位</div>
          <div class="value">{{ formState.deptName }}</div>
        </div>
        <div class="stu-info-item">
          <div class="label">身份证号</div>
          <div class="value">{{ formState.idCardNum }}</div>
        </div>
      </div>
      <div class="stu-info-img">
        <a-image v-if="formState.facecheckComputerUrl" :src="formState.facecheckComputerUrl" alt=""
          :fallback="NoFaceImgUrl" />
        <img v-else :src="NoFaceImgUrl"/>
        <p>人脸核验</p>
      </div>
      <div class="stu-info-img" style="margin-left: 24px;">
        <a-image v-if="formState.idCardImg" :src="formState.idCardImg" alt="" />
        <img v-else :src="NoIDCardImgUrl"/>
        <p>身份证人像面</p>
      </div>
    </div>
    <template v-if="status == 1 || status == -1">
      <div class="big-screen" v-if="computerMonitor || phoneMonitor">
        <img v-if="livePhotos[activeKey]" :src="livePhotos[activeKey]" alt="">
        <div v-else class="no-signal-img"></div>
        <div class="message-box" @click="messageVisible = true">
          <div class="message-ipt">
            <span>常用语</span>
            <caret-down-outlined />
            <a-divider type="vertical" style="background-color: #fff;"></a-divider>
            <span>请输入消息</span>
          </div>
          <div class="message-btn">发送消息</div>
        </div>
      </div>
      <div class="small-screen">
        <div v-if="computerMonitor" :class="{ active: activeKey === 'now_face' }"
          @click="activeKey = 'now_face'">
          <div class="corner"></div>
          <div class="corner"></div>
          <div class="corner"></div>
          <div class="corner"></div>
          <img class="small-img" v-if="livePhotos.now_face" :src="livePhotos.now_face" alt="">
          <div v-else class="small-img no-signal-img"></div>
        </div>
        <div v-if="phoneMonitor" :class="{ active: activeKey === 'monitor_phone' }"
          @click="activeKey = 'monitor_phone'">
          <div class="corner"></div>
          <div class="corner"></div>
          <div class="corner"></div>
          <div class="corner"></div>
          <img class="small-img" v-if="livePhotos.monitor_phone" :src="livePhotos.monitor_phone" alt="">
          <div v-else class="small-img no-signal-img"></div>
        </div>
      </div>
    </template>
    <div  v-else style="aspect-ratio: 16/9;display: flex;align-items: center;justify-content: center;flex-direction: column;">
      <img src="@/assets/images/svg/no_monitor.svg" width="300" alt="">
      <p style="color: rgba(0,0,0,0.45);line-height: 40px;">暂无监控画面</p>
    </div>
    <MessageSendModal :title="`发送消息给${formState.studentName}`" v-model:visible="messageVisible" :stupid="stupid"></MessageSendModal>
  </div>
</template>
  
<script lang="ts" setup>
import { candsinfo, monitorscreen } from '@/api/admin/paperManage'
import { onUnmounted, ref, watch, reactive } from 'vue'
import { CaretDownOutlined } from '@ant-design/icons-vue'
import MessageSendModal from '@/pages/monitorManage/components/MessageSendModal.vue'
import { useIntervalFn } from '@vueuse/core'
const NoIDCardImgUrl = new URL('../../assets/images/notUploadIDcard.png', import.meta.url).href
const NoFaceImgUrl = new URL('../../assets/images/faceVerification.png', import.meta.url).href

const props = defineProps<{
  stupid: string
  paperId: string
  paperName: string
  computerMonitor: boolean
  phoneMonitor: boolean
  /** -2 等待中  -1 异常  0 缺考 1 考试中 2 已结束 */
  status: number
}>()

const formState = ref({
  age: 0,
  deptName: '',
  email: '',
  facecheckComputerUrl: '',
  graduated_school: '',
  heducation: '',
  idCardNum: '',
  idCardImg: '',
  phone: '',
  sex: '',
  stupid: '',
  studentName: ''
})

// 轮询获取监控画面
const livePhotos = reactive({
  now_face: '',
  monitor_phone: ''
})
const activeKey = ref('')
function init() {
  if (props.computerMonitor) return activeKey.value = 'now_face'
  if (props.phoneMonitor) return activeKey.value = 'monitor_phone'
}
init()

async function getLivePhoto() {
  let res = await monitorscreen({
    templeteIdList: [props.paperId],
    basePager:{
      current: 1,
      size: 10
    },
    id: props.stupid,
  }) as any
  console.log(res, 111)
  let liveObj = res?.cand_status?.[0]
  if (liveObj) {
    livePhotos.now_face = liveObj.now_face?.[0]
    livePhotos.monitor_phone = liveObj.monitor_phone?.[0]
  }
}

// 发送消息
const messageVisible = ref(false)

// 每隔3s获取一次实时画面
const { pause, resume, isActive } = useIntervalFn(getLivePhoto, 3000, { immediateCallback: true })

async function getDetail() {
  try {
    let res = await candsinfo({ id: props.stupid })
    formState.value = res
  } catch (error) {
    console.log(error)
  }
}
getDetail()

</script>
  
<style lang="less" scoped>
.stu-info {
  display: flex;

  .stu-info-main {
    .stu-name {
      font-size: 24px;
      margin-right: 4px;
      max-width: 200px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .stu-tag {
      margin-left: 8px;
      color: #D96E00;
      background-color: #FAF4EE;
      font-size: 12px;
      padding: 2px 4px;
      border-radius: 4px;
    }

    .stu-school {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }

    .stu-info-item {
      margin-top: 16px;
      display: flex;

      .label {
        width: 80px;
        font-size: 12px;
        color: #626262;
      }

      .value {
        color: #181818;
      }
    }
  }

  .stu-info-img {
    margin-right: 0;
    margin-left: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;

    :deep(.ant-image) {
      width: 259px;
      height: 146px;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      img {
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.big-screen {
  margin-top: 24px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  aspect-ratio: 16/9;

  img {
    height: 100%;
    width: 100%;
    background-color: #D9D9D9;
    object-fit: contain;
  }

  &:hover .message-box {
    bottom: 0;
  }

  .message-box {
    transition: all ease 0.1s;
    width: 100%;
    position: absolute;
    bottom: -64px;
    left: 0;
    height: 64px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;

    .message-ipt {
      flex: 1;
      height: 40px;
      line-height: 40px;
      text-indent: 12px;
      font-size: 12px;
      background: rgba(255, 255, 255, 0.45);
      border-radius: 8px;
    }

    .message-btn {
      background-color: #5478EE;
      height: 40px;
      line-height: 40px;
      font-size: 12px;
      text-align: center;
      width: 88px;
      border-radius: 8px;
      border-radius: 8px;
      margin-left: 8px;
    }
  }
}

.small-screen {
  margin-top: 16px;
  display: flex;

  >div {
    margin-right: 24px;
    cursor: pointer;
    position: relative;

    .corner {
      width: 8px;
      height: 8px;
      background-color: #5478EE;
      position: absolute;
      transition: all ease 0.1s;

      &::after {
        content: "";
        position: absolute;
        width: 6px;
        height: 6px;
        background-color: #fff;
      }

      // z-index: -1;
      &:nth-child(1) {
        left: 0;
        top: 0;

        &::after {
          right: 0;
          bottom: 0;
        }
      }

      &:nth-child(2) {
        right: 0;
        top: 0;

        &::after {
          left: 0;
          bottom: 0;
        }
      }

      &:nth-child(3) {
        right: 0;
        bottom: 0;

        &::after {
          left: 0;
          top: 0;
        }
      }

      &:nth-child(4) {
        left: 0;
        bottom: 0;

        &::after {
          right: 0;
          top: 0;
        }
      }
    }

    &.active {
      img {
        border: 1px solid #5478EE;
      }

      .corner {
        &:nth-child(1) {
          left: -3px;
          top: -3px;
        }

        &:nth-child(2) {
          right: -3px;
          top: -3px;
        }

        &:nth-child(3) {
          right: -3px;
          bottom: -3px;
        }

        &:nth-child(4) {
          left: -3px;
          bottom: -3px;
        }
      }
    }
  }

  .small-img {
    position: relative;
    z-index: 99;
    width: 200px;
    height: 112.5px;
    object-fit: contain;
    &img {
      background-color: #D9D9D9;
    }
  }
  .no-signal-img {
    position: relative;
    z-index: 99;
    width: 200px;
    height: 112.5px;
  }
}

.no-signal-img {
  height: 100%;
  background: url('@/assets/icons/svg/no-signal.svg') center no-repeat, rgb(252, 240, 240);
  background-size: 30%;
}
</style>
  
<style lang="less">
// .ant-image-preview-img {
//   transform: scale3d(2, 2, 2) rotate(0deg) !important;
// }
</style>
  