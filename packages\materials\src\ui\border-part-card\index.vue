<script setup lang="ts">
import { cn } from '@/lib/utils'
import {
  AnimatePresence,
  motion,
  useMotionTemplate,
  useMotionValue,
} from 'motion-v'
import { type HTMLAttributes, ref } from 'vue'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const isHovering = ref(false)
const mouseX = useMotionValue(0)
const mouseY = useMotionValue(0)
const radius = useMotionValue(0)
const cardRef = ref<HTMLElement | null>(null)

const maskBackground = useMotionTemplate`radial-gradient(circle ${radius}px at ${mouseX}px ${mouseY}px, black 40%, transparent)`

function handleMouseMove(event: MouseEvent) {
  if (!cardRef.value)
    return

  const bounds = cardRef.value.getBoundingClientRect()
  mouseX.set(event.clientX - bounds.left)
  mouseY.set(event.clientY - bounds.top)
  radius.set(Math.sqrt(bounds.width ** 2 + bounds.height ** 2) / 2)
}

function handleMouseEnter() {
  isHovering.value = true
}

function handleMouseLeave() {
  isHovering.value = false
}
</script>

<template>
  <div
    ref="cardRef" 
    :class="
      cn(
        'relative rounded-md border border-zinc-100 dark:border-zinc-700/40 ',
        props.class,
      )
    "
    @mouseenter="handleMouseEnter" @mousemove="handleMouseMove" @mouseleave="handleMouseLeave"
  >
    <slot />

    <!-- Animated Footer -->
    <AnimatePresence>
      <motion.footer
        v-if="isHovering" 
        :class="
          cn(
            'pointer-events-none absolute -inset-x-px -inset-y-px z-[99999] select-none',
          )
        "
        :initial="{ opacity: 0 }" 
        :animate="{ opacity: 1 }" 
        :exit="{ opacity: 0 }" 
        :style="{
          webkitMaskImage: maskBackground,
          maskImage: maskBackground,
        }"
      >
        <div 
          :class="
            cn(
              'absolute inset-x-px inset-y-px rounded-md border border-dashed border-black',
              props.class,
            )
          "
        />

        <!-- 这里面的内容会被 maskBackground 遮罩 -->
        <!-- 所以如果有需要 hover 显示同时被遮罩的元素，可以放在这里 -->
      </motion.footer>
    </AnimatePresence>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
