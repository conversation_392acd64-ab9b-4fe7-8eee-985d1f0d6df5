<template>
    <div class="survey-analysis-table">
        <div class="filter-bar">
            <div class="filter-item">
                <span class="filter-label">提交时间</span>
                <a-range-picker class="time-range-wrap" v-model:value="params.submit_time_range"
                    :placeholder="['开始时间', '结束时间']" valueFormat="YYYY-MM-DD" @change="getList(1)">
                    <template #suffixIcon>
                        <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                    </template>
                </a-range-picker>
            </div>
            <div class="filter-item">
                <a-dropdown v-model:visible="dropdownVisible" :trigger="['click']" overlayClassName="colums-dropdown">
                    <a-button :icon="h(UnorderedListOutlined)" style="margin-left: 8px;">
                        更多列
                    </a-button>
                    <template #overlay>
                        <a-menu>
                            <a-menu-item>
                                <a-checkbox v-model:checked="checkAll" :indeterminate="indeterminate"
                                    @change="handleCheckAll"></a-checkbox>
                                <span class="dropdown-menu-item-title">全选</span>
                            </a-menu-item>
                            <a-menu-item v-for="item in originalColums.filter((i) => !fixedColumnKeys.includes(i.key))">
                                <a-checkbox v-model:checked="item.checked" @change="onColumnsChange"></a-checkbox>
                                <span class="dropdown-menu-item-title" :title="item.title">
                                    {{ item.title }}
                                </span>
                            </a-menu-item>
                        </a-menu>
                    </template>
                </a-dropdown>
            </div>
        </div>
        <div class="survey-analysis-content">
            <div class="table-filter-bar">
                <a-tabs v-model:activeKey="efficient">
                    <a-tab-pane key="0" tab="全部"></a-tab-pane>
                    <a-tab-pane key="1" tab="有效"></a-tab-pane>
                    <a-tab-pane key="2" tab="无效"></a-tab-pane>
                </a-tabs>
                <div style="height: 100%;display: flex; align-items: center;">
                    <a-button v-if="params.efficient === false" @click="setSurveyEfficient(true)">恢复为有效问卷</a-button>
                    <a-button v-else @click="setSurveyEfficient(false)">标记为无效问卷</a-button>
                </div>
            </div>
            <div class="table-wrapper">
                <a-table :columns="columns" :data-source="list" :scroll="{ x: 1200, y: 500 }"
                    :row-key="(record: any) => record.id" :row-selection="{
                        fixed: true,
                        selectedRowKeys,
                        onChange: onSelectChange
                    }" :pagination="paginationConfig" @resizeColumn="(w: any, col: any) => col.width = w"
                    @change="handleTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'efficient'">
                            <div style="display: flex; align-items: center">
                                <span style="
                      width: 8px;
                      height: 8px;
                      display: inline-block;
                      border-radius: 50%;
                      margin-right: 4px;
                    " :style="{
                        backgroundColor: record.efficient ? '#52C41A' : '#D9D9D9'
                    }"></span>
                                {{ record.efficient ? '有效' : '无效' }}
                            </div>
                        </template>
                        <template v-else-if="column.key === 'action'">
                            <a-button type="link" @click="showDrawer(record)">查看详情</a-button>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>

        <a-drawer v-model:visible="drawerVisible" class="survey-answers-drawer" title="问卷详情" placement="right"
            :destroyOnClose="true" width="550px">
            <SurveyAnswersPanel ref="surveyAnswersPanelRef" :data="[currentSurveyAnswer]"></SurveyAnswersPanel>
        </a-drawer>
    </div>
</template>
<script lang="ts" setup>
import { reactive, ref, h, computed, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router'
import { Modal, message, type TableColumnsType } from 'ant-design-vue';
import { delassociate, relatestu } from '@/api/admin/paperManage';
import SurveyAnswersPanel from './SurveyAnswersPanel.vue'
import { markanswerquestionnaire, questionnairedetail, questionnairereports } from '@/api/admin/survey';
import { QuestionEnum } from '@/models/questionModel';
import _ from 'lodash';
import { UnorderedListOutlined } from '@ant-design/icons-vue'
import moment from 'moment';

const router = useRouter()
const route = useRoute()

let originalColums = ref<any[]>([])
const dropdownVisible = ref(false)
const checkAll = ref(true)
/** 是否是半选状态 */
const indeterminate = ref(false)
const refreshIndeterminate = () => {
    let flag = originalColums.value.some(i => i.checked) && !originalColums.value.every(i => i.checked)
    indeterminate.value = flag
}
function onColumnsChange() {
    columns.value = originalColums.value.filter(item => item.checked)
    refreshIndeterminate()

    checkAll.value = originalColums.value.every(i => i.checked)
}
function handleCheckAll() {
    let checkedLength = originalColums.value.filter(i => i.checked).length
    if (checkedLength < originalColums.value.length) {
        originalColums.value.forEach(item => item.checked = true)
    } else {
        originalColums.value.forEach(item => !fixedColumnKeys.includes(item.key) && (item.checked = false))
    }
    columns.value = originalColums.value.filter(item => item.checked)
    refreshIndeterminate()
}

/** 列配置 */
const columns = ref<TableColumnsType>([
    {
        title: '序号',
        dataIndex: 'serialNumber',
        key: 'serialNumber',
        ellipsis: true,
        width: 70,
        resizable: true,
        sorter: true
    },
    {
        title: '问卷标记',
        dataIndex: 'efficient',
        key: 'efficient',
        ellipsis: true,
        width: 120,
        resizable: true,
    },
    {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        ellipsis: true,
        width: 200,
        resizable: true,
        sorter: true
    },
    {
        dataIndex: 'submitTime',
        key: 'submitTime',
        title: '提交时间',
        width: 200,
        ellipsis: true,
        resizable: true,
        sorter: true
    },
    {
        title: '填写时长',
        dataIndex: 'answerDuration',
        key: 'answerDuration',
        width: 200,
        ellipsis: true,
        resizable: true,
        sorter: true,
        customRender({ text }) {
            let duration = moment.duration(text, 'seconds')
            let ans = ''
            if (duration.hours() > 0) {
                ans += duration.hours() + '时'
            }
            if (duration.minutes() > 0) {
                ans += duration.minutes() + '分'
            }
            ans += duration.seconds() + '秒'
            return ans
        }
    },
    {
        title: '操作',
        width: 120,
        fixed: 'right',
        key: 'action'
    }
])

/** 固定展示的列 */
const fixedColumnKeys = ['serial_number', 'efficient', 'nickname', 'action']

/** 获取问卷详情，补充列配置 */
async function getDetail() {
    try {
        let { content, loginMethod } = await questionnairedetail({ id: route.query.id as any })
        content.forEach((q: any, i: number) => {
            if (q.type > 100) return

            columns.value.splice(-1, 0, {
                title: `${q.number}.${q.body}`,
                dataIndex: `q${i}answer`,
                key: `q${i}answer`,
                ellipsis: true,
                width: 350,
                resizable: true
            })
        })

        // 如果该问卷是要求微信授权登录则添加微信昵称
        if (loginMethod === 'wx') {
            columns.value.splice(2, 0, {
                title: '微信昵称',
                dataIndex: 'nickname',
                key: 'nickname',
                ellipsis: true,
                width: 120,
                resizable: true,
            })
        }

        originalColums.value = _.cloneDeep(columns.value)
        originalColums.value.forEach((item: any) => item.checked = true)

    } catch (error) {
        console.log(error)
    }
}
getDetail()

// 分页处理
const paginationConfig = ref({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    total: 0,
    showTotal: (total: number) => '总条数：' + total,
    pageSizeOptions: ['10', '20', '50', '100'],
    size: 'small'
})

const otherParams = ref<{
    orderField?: string
    orderType?: 'asc' | 'desc'
}>({})

const handleTableChange = (pagination: any, filters: any = {}, sorter: any = {}) => {
    // 处理分页
    paginationConfig.value.current = pagination.current
    paginationConfig.value.pageSize = pagination.pageSize
    // 处理排序
    otherParams.value.orderType = sorter.order
    otherParams.value.orderField = sorter.order ? sorter.field : undefined
    // 处理筛选
    Object.assign(otherParams.value, filters)
    getList()
}

// 行选择
const selectedRowKeys = ref<string[]>([])
const onSelectChange = (keys: string[]) => {
    selectedRowKeys.value = keys
};

const efficient = ref<'0' | '1' | '2'>('1')
watch(efficient, (val) => {
    params.efficient = [null, true, false][val]
    getList(1)
})

const params = reactive<{
    submit_time_range: any
    efficient: null | boolean
}>({
    submit_time_range: [],
    efficient: true
})

const list = ref([])
function format(data) {
    data.forEach((item: any) => item.answerDetail.forEach((q: any, i: number) => {
        let answer = ''
        if (q.type === QuestionEnum['单选题']) {
            if (q.answer !== '-1') {
                answer = q.options.find((o: any) => o.value == q.answer)?.content
            } else {
                // answer = q.otherContent
                answer = '其他' + (q.otherContent ? `：${q.otherContent}` : '')
            }

        } else if (q.type === QuestionEnum['多选题']) {
            answer = q.answer?.split?.(',')?.map((a: string) => {
                if (a !== '-1') {
                    return q.options.find((o: any) => o.value == a)?.content
                } else {
                    // 其他
                    // return q.otherContent
                    return '其他' + (q.otherContent ? `：${q.otherContent}` : '')
                }
            })?.join(' | ')
        } else if (q.type === QuestionEnum['问答题']) {
            answer = q.answer
        } else if (q.type === QuestionEnum['评分题']) {
            answer = q.answer === undefined ? '' : q.answer + '分'
        }

        Reflect.set(item, `q${i}answer`, answer)
    }))
}
async function getList(page?: number) {
    if (page) paginationConfig.value.current = page

    let para = {
        ...params,
        basePager:{
            current: paginationConfig.value.current,
            size: paginationConfig.value.pageSize
        },
        questionnaireId: route.query.id,
        page: paginationConfig.value.current,
        per_page: paginationConfig.value.pageSize,
        ...otherParams.value,
        startTimeStr: params.submit_time_range === null ? null : params.submit_time_range[0],
        endTimeStr: params.submit_time_range === null ? null : params.submit_time_range[1],
        efficient: params.efficient ? 1 : 0
    }
    
    let res = await questionnairereports(para)
    format(res.records)
    list.value = res.records
    paginationConfig.value.total = res.total

}

getList()

const currentSurveyAnswer = ref()

async function setSurveyEfficient(efficient: boolean) {
    if (!selectedRowKeys.value.length) return message.warning('请选择问卷')
    Modal.confirm({
        title: '注意',
        content: efficient ? `确定将勾选的${selectedRowKeys.value.length}份问卷恢复为有效问卷？` : `确定将勾选的${selectedRowKeys.value.length}份问卷标记为无效问卷？`,
        async onOk() {
            await markanswerquestionnaire({
                ids: selectedRowKeys.value,
                efficient: 0
            })
            message.success(efficient ? '恢复为有效问卷成功' : '标记为无效问卷成功')
            selectedRowKeys.value = []
            getList(1)
        }
    })

}

const drawerVisible = ref(false);
const surveyAnswersPanelRef = ref<InstanceType<typeof SurveyAnswersPanel>>()
async function showDrawer(item: string) {
    drawerVisible.value = true;
    currentSurveyAnswer.value = item
};

</script>
<style lang="less" scoped>
:deep(.ant-table) {
    .ant-table-thead>tr>th {
        font-weight: bold;
        text-align: left;

        &:last-child {
            padding-left: 22px !important;
        }

        background: #f1f4fe !important;

        .ant-table-column-title {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            font-weight: bold;
            color: #121633;
        }
    }

    .ant-table-tbody>tr>td {
        padding: 10px;
        font-size: 14px;
    }

    .ant-table-row>td {
        text-align: left;
    }
}

.survey-analysis-table {
    display: flex;
    flex-direction: column;

    .filter-bar {
        margin: 20px 20px 0;
        height: 80px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
        padding: 24px;
        display: flex;
        align-items: center;

        .filter-item {
            display: flex;
            align-items: center;

            .filter-label {
                margin-right: 24px;
                color: #626262;
            }
        }
    }

    .survey-analysis-content {
        flex: 1;
        min-height: 0;
        padding: 0 24px 24px 24px;
        margin: 20px;
        background: #ffffff;
        overflow: auto;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);

        .table-filter-bar {
            height: 52px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            border-bottom: 1px solid #dfe1e6;

            :deep(.ant-tabs) {
                color: rgba(0, 0, 0, 0.85);

                .ant-tabs-nav {
                    margin: 0;

                    &::before {
                        display: none;
                    }

                    .ant-tabs-ink-bar {
                        height: 3px;
                    }

                    .ant-tabs-tab {
                        padding-bottom: 7px;

                        .ant-tabs-tab-btn {
                            line-height: 22px;
                        }
                    }
                }
            }
        }

        .table-wrapper {
            padding-top: 16px;
        }
    }
}
</style>
<style lang="less">
.colums-dropdown {
    .ant-dropdown-menu {
        max-height: 400px;
        overflow: auto;
    }

    .ant-dropdown-content {
        .ant-dropdown-menu-title-content {
            display: flex;
            align-items: center;
        }
    }

    .dropdown-menu-item-title {
        margin-left: 8px;
        display: inline-block;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
    }
}
</style>