<script setup lang="ts">
import type { CertificateItemProps } from './shared'
import BaseExperienceCard from './BaseExperienceCard.vue'
// 模拟工作经历数据
withDefaults(defineProps<{
  certificateExperienceData: CertificateItemProps
}>(), {
  certificateExperienceData: () => ([]),
})
</script>

<template>
  <BaseExperienceCard v-show="certificateExperienceData.length > 0" title="奖项/证书">
    <div v-for="(item, index) in certificateExperienceData" :key="index" class="mb-6 last:mb-0">
      <div class="flex items-start mb-2 ">
        <div class="flex-1">
          <div class="flex justify-between">
            <div>
              <span class="font-medium text-base">{{ item.name }}</span>
              <span class="text-gray-500 mx-2" />
              <span class="text-gray-600 text-xs">{{ item.issuer }}</span>
              <span class="text-gray-500 mx-2">|</span>
              <span class="text-gray-600 text-xs">{{ item.issueDate }}</span>
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600 whitespace-pre-line leading-[18px]">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </BaseExperienceCard>
</template>
