export enum questionType {
  SINGLE_CHOICE,
  MULTIPLE_CHOICE,
  TRUE_OR_FALSE,
  SHORT_ANSWER,
  AlGORITHM_CODING,
  FILL_BLOCK,
  SORT_ORDER,
  COMPOSITE
}

export enum QuestionEnum {
  '单选题',
  '多选题',
  '判断题',
  '问答题',
  '算法题',
  '填空题',
  '排序题',
  '复合题',
  '评分题',
  '分割线' = 101
}

export interface QuestionModel {
  id?: string
  body: string
  complexcontent?: string
  category: string
  type: QuestionEnum
  score: number
  new_score?: number
  points: string
  difficulty: string | any
  options?: Array<any>
  answer?: Array<any>
  subject?: number
  complicatedediting?: boolean
  tags?: Array<string>
  tests?: Array<any> | string
  exampleCode: Array<string>
  exampleIO: Array<string>
  parameters: Array<any>
  func_name: string
  language: string | null
  rtype: RType
  stuanswer?: string
  stuscore?: number
  scorebasis?: string
  passcase?: string
  sepscore?: boolean
  official_cert?: boolean
  btestcase?: any
  tagname?: string[]
  proofreading_info?: {
    create_at: string
    id: string
    proofreading: number
    ques: string
    teacher: string
    teacher_name: string
    update_at: string
    wrong_reason: string
    is_ai: boolean
  }
  /** 0 未校对 1 校对正确 2 校对错误 */
  proofreading: 0 | 1 | 2
  [k: string]: any
}

export interface RType {
  type: string
  is_list: boolean
}

export interface Subject {
  id?: string
  name: string
  label?: string
  category: string
}

export interface LanguageFunc {
  C: string[]
  'C++': string[]
  Java: string[]
  Python: string[]
  JavaScript: string[]
  Go: string[]
}
