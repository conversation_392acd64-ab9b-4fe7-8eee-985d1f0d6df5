<template>
  <div class="paper-card">
    <div class="header">
      <div class="left">
        <img src="@/assets/icons/svg/file.svg" alt="">

        <a-tooltip placement="bottom" overlayClassName="light" :getPopupContainer="(t: any) => t.parentElement">
          <template #title>
            <span>{{ cardInfo.name }}</span>
          </template>
          <SearchHighLight class="title" :text="cardInfo.name" :search-text="searchText" @click="emits('preview')">
          </SearchHighLight>
        </a-tooltip>
        <div class="status" :style="statusStyles">
          {{ !cardInfo.published ? '待发布' : cardInfo.going ? '进行中' : '已停止' }}
        </div>
      </div>
      <check-circle-filled class="check" @click="onCheck(false)" v-if="props.checked"
        style="font-size: 16px; color: rgba(84,120,238,1)" />
      <div :class="['circle', 'check']" @click="onCheck(true)" v-else></div>
    </div>

    <div class="time">{{ cardInfo.teacherName }}于{{ cardInfo.createTime }}创建</div>
    <div style="display: flex;gap: 18px;margin-top: 24px;">
      <div class="status-wrap">
        <div class="label">今日新增</div>
        <div class="number">{{ cardInfo.published ? cardInfo.submitToday : '-' }}</div>
      </div>
      <div class="status-wrap">
        <div class="label">提交总数</div>
        <div class="number">{{ cardInfo.published ? cardInfo.submitSum : '-' }}</div>
      </div>
    </div>
    <div class="bottom">
      <template v-if="!cardInfo.published">
        <div class="bottom-btn" @click="handlePublish">发布</div>
        <div class="bottom-btn" @click="showForm('edit')">编辑</div>
      </template>
      <template v-else-if="cardInfo.going">
        <div class="bottom-btn" @click="emits('share')">分享</div>
        <div class="bottom-btn" @click="showDataAnalysis">数据</div>
      </template>
      <template v-else>
        <div class="bottom-btn" @click="handleStatusChange(true)">开始</div>
        <div class="bottom-btn" @click="showDataAnalysis">数据</div>
      </template>
      <div class="bottom-btn">
        <a-dropdown :getPopupContainer="(t: any) => t.parentElement" placement="bottomLeft"
          :overlayStyle="{ width: '88px' }">
          <span>更多</span>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" v-if="cardInfo.going" @click="handleStatusChange(false)">暂停</a-menu-item>
              <a-menu-item key="1" @click="showForm('copy')">克隆</a-menu-item>
              <a-menu-item key="2" @click="emits('remove')">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CheckCircleFilled } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import SearchHighLight from '@/components/SearchHighLight.vue';
import { pauseorunpause, publishquestionnaire } from '@/api/admin/survey';
import { QuestionEnum } from '@/models/questionModel';
import { valideSurvey } from './utils';

const router = useRouter()
const props = defineProps<{
  cardInfo: any // 考试信息
  checked?: boolean // 选中
  searchText: string
}>()

const emits = defineEmits<{
  (e: 'check', checked: boolean): void
  (e: 'preview'): void
  (e: 'remove'): void
  (e: 'update'): void
  (e: 'share'): void
}>()

const statusStyles = computed(() => {
  const { published, going } = props.cardInfo
  if (!published) {
    return {
      backgroundColor: '#f3f7f1',
      color: '#2F8C00',
    }
  } else if (going) {
    return {
      backgroundColor: '#fcf0f0',
      color: '#D71310',
    }
  } else {
    return {
      backgroundColor: '#f5f5f5',
      color: '#626262',
    }
  }
})

// 选中or取消选中，emit事件
const onCheck = (checked: boolean) => {
  emits('check', checked)
}

// 编辑/克隆
function showForm(mode: 'edit' | 'copy') {
  router.push({
    name: 'surveyForm',
    query: {
      mode,
      id: props.cardInfo.id
    }
  })
}

// 发布
async function handlePublish() {
  try {
    await valideSurvey(props.cardInfo)

    try {

      await publishquestionnaire({ id: props.cardInfo.id })
      message.success('发布成功')
      emits('update')

    } catch (error) {
      console.log(error)
    }
  } catch (error) {
    message.error('当前问卷内容不完整，请编辑完整后再发布')
  }
}

// 开始/暂停
async function handleStatusChange(going: boolean) {
  try {
    let goingParam = going==true?1:0
    await pauseorunpause({ id: props.cardInfo.id, going: goingParam})
    message.success(going ? '已开始' : '已停止')
    emits('update')

  } catch (error) {
    console.log(error)
  }
}

function showDataAnalysis() {
  router.push(`/admin/surveyAnalysis?id=${props.cardInfo.id}`)
}

</script>

<style lang="less" scoped>
.paper-card {
  width: 100%;
  padding: 24px 24px 0 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px #0000001a;
  transition: all .3s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.10);

    .header {
      .operation {
        display: flex;
      }

      .circle {
        display: block !important;
      }
    }
  }

  box-sizing: border-box;
  position: relative;
  user-select: none;

  .header {
    height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      min-width: 0;
      flex: auto;
      display: flex;
      align-items: center;

      .title {
        min-width: 0;
        height: 22px;
        font-size: 18px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        text-align: left;
        line-height: 22px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 8px;
        cursor: pointer;
        transition: all ease .2s;

        &:hover {
          color: #5478ee;
        }
      }

      .status {
        flex: none;
        width: 44px;
        height: 18px;
        margin-left: 16px;
        background: #fcf0f0;
        color: #d71310;
        border-radius: 4px;
        font-size: 12px;
        font-weight: Regular;
        text-align: center;
        line-height: 18px;
      }

      .operation {
        height: 16px;
        margin-left: 16px;
        align-items: center;
        display: none;

        .btn {
          flex: none;
          width: 16px;
          height: 16px;
          margin-left: 16px;
          object-fit: contain;
          cursor: pointer;

          &:first-child {
            margin-left: 0;
          }

          &.disabled {
            cursor: not-allowed;
          }
        }
      }
    }

    .check {
      flex: none;
      margin-left: 16px;
      cursor: pointer;

      &.circle {
        width: 16px;
        height: 16px;
        border: 1px solid rgba(217, 217, 217, 1);
        background: #fff;
        border-radius: 50%;
        display: none;

        &:hover {
          border-color: rgba(84, 120, 238, 1);
        }
      }

      &.disabled {
        cursor: not-allowed;

        &:hover {
          border-color: rgba(217, 217, 217, 1);
        }
      }
    }
  }

  .time {
    height: 18px;
    margin-top: 16px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 18px;
  }

  .status-wrap {
    flex: 1;
    height: 90px;
    padding: 16px 24px;
    background: #F1F4FE;
    border-radius: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .label {
      height: 18px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 18px;
    }

    .number {
      height: 40px;
      font-size: 36px;
      font-weight: 600;
      color: #5478ee;
      line-height: 40px;
    }

    .manual-img {
      flex: none;
      width: 58px;
      height: 58px;
    }
  }

  .bottom {
    height: 42px;
    margin-top: 24px;
    border-top: 1px solid #E8E8E8;
    display: flex;
    justify-content: center;

    .bottom-btn {
      flex: auto;
      width: 50%;
      height: 100%;
      cursor: pointer;
      text-align: center;
      line-height: 42px;
      color: #5478EE;
      position: relative;

      &:hover {
        color: rgba(140, 167, 255, 1);
      }

      &.disabled {
        color: rgba(0, 0, 0, 0.25);
        cursor: not-allowed;
      }

      &::before {
        display: block;
        content: '';
        width: 1px;
        height: 14px;
        background: #dfe1e6;
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 0;
      }

      &:first-of-type::before {
        display: none;
      }
    }
  }

  &:hover {
    box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.10);
  }
}
</style>