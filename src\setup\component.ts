import type { App } from 'vue'
// 注入svg
import svgIcon from '@/components/SvgIcon.vue'

//  引入字体图标库
import { Icon } from '@/Icon/ICON'

// 引入富文本编辑器
import { QuillEditor } from '@vueup/vue-quill'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 全局组件
import VChart from 'vue-echarts'

import '@vueup/vue-quill/dist/vue-quill.snow.css'
import 'quill-image-uploader/dist/quill.imageUploader.min.css'

export function setupGlobalComponent(app: App) {
  app.component('Icon', Icon)
  app.component('svg-icon', svgIcon)
  app.component('QuillEditor', QuillEditor)
  app.component('VChart', VChart)

  app.use(ElementPlus, {
    locale: zhCn,
  })
}
