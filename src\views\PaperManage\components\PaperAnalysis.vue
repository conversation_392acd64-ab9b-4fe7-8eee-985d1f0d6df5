<!-- 考试分析弹窗 -->
<template>
   <a-modal
    :visible="props.visible"
    centered
    :footer="null"
    :closable="false"
    :width="720"
    destroyOnClose
    wrapClassName="paper-analysis-global-modal" >
    <template #title>
      <div class="analysis-modal-title">
        <div class="title">图灵考试</div>
        <CloseOutlined class="close" style="fontSIze: 13px; color: #fff" @click="emits('close')" />
      </div>
    </template>
    <div class="analysis-modal-content">
      <template v-if="!props.loading">
        <div class="radar-wrap" v-if="props.texts.length">
          <PaperAnalysisRadar v-bind="props" />
        </div>
        <div class="texts-list">
          <template v-if="props.texts.length">
            <div class="text-item" v-for="text in props.texts" :key="text" v-html="text"></div>
          </template>
          <div class="error-tip" v-else>数据不充分，无法进行分析</div>
        </div>
      </template>
      <div class="spin-wrap" v-else>
        <a-spin />
        <div class="label">分析中...</div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { CloseOutlined } from '@ant-design/icons-vue'
import PaperAnalysisRadar from './PaperAnalysisRadar.vue'

const props = defineProps<{
  visible: boolean
  loading: boolean
  texts: string[]
  joinRate: number
  passRate: number
  saturationRate: number
  diffRate: number
}>()

const emits = defineEmits(['close'])
</script>

<style lang="less" scoped>
.analysis-modal-title {
  height: 44px;
  background: #5478ee;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    height: 28px;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    line-height: 28px;
  }
}
.analysis-modal-content {
  min-height: 150px;
  padding: 10px 72px 32px;
  .radar-wrap {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
  .texts-list {
    .text-item {
      margin-bottom: 10px;
      font-size: 14px;
      color: rgba(0,0,0,0.85);
      line-height: 22px;
    }
    .error-tip {
      height: 100px;
      font-size: 14px;
      color: rgba(0,0,0,0.85);
      text-align: center;
      line-height: 100px;
    }
  }
  .spin-wrap {
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>
<style lang="less">
.paper-analysis-global-modal {
  .ant-modal{
    overflow: hidden;
    border-radius: 8px;
  }
  .ant-modal-content {
    max-height: 100vh;
  }
  .ant-modal-header {
    padding: 0;
  }
  .ant-modal-body {
    padding: 0;
  }
}
</style>
