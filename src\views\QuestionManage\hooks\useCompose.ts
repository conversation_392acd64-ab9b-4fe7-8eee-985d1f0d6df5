export default (subQuestionList: any) => {
  // 校验复合题
  const checkSubQuestionList = async () => {
    if (!subQuestionList.value.length) {
      return Promise.reject('请添加子试题')
    } else {
      return Promise.resolve()
    }
  }
  // 复合题添加题目
  const addCompoundQuestions = (item: any) => {
    subQuestionList.value.push({
      type: item.questionKey,
      label: item.questionCate,
      formState: { type: item.questionKey, body: '' }
    })
  }

  // 删除复合题中的一项
  const handleDelete = (index: number) => {
    subQuestionList.value.splice(index, 1)
  }

  // 向上移动复合题
  const handleUp = (e: any, index: number) => {
    if (index > 0) {
      const tempProxy = JSON.stringify(subQuestionList.value[index - 1])
      subQuestionList.value[index - 1] = JSON.parse(JSON.stringify(subQuestionList.value[index]))
      subQuestionList.value[index] = JSON.parse(tempProxy)
      // e.target.parentNode.parentNode.activeKey(index)
      e.stopPropagation()
      e.preventDefault()
    } else {
      return
    }
  }

  // 向下移动复合题
  const handleDown = (index: number) => {
    const tempProxy = JSON.stringify(subQuestionList.value[index + 1])
    subQuestionList.value[index + 1] = JSON.parse(JSON.stringify(subQuestionList.value[index]))
    subQuestionList.value[index] = JSON.parse(tempProxy)
  }

  return {
    checkSubQuestionList,
    addCompoundQuestions,
    handleDelete,
    handleUp,
    handleDown
  }
}
