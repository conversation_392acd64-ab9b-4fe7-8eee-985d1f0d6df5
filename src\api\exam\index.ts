import request from '@/utils/http'
import { AxiosPromise } from 'axios'
import { url } from 'inspector'
import { json } from 'stream/consumers'

// 获取教师账号列表
export function getTeachers(data?: object) {
  return request({
    url: '/teachers',
    data
  })
}

// 上传简历
export function uploadResume(data?: object) {
  return request({
    url: '/resumes',
    data
  })
}

// 获取考试列表
export function queryExamList(data?: object) {
  return request({
    url: '/mytest',
    data
  })
}

// 算法题编译
export function getCodeResult(data?: object) {
  return request({
    url: '/judge',
    data
  })
}

// 提交试卷
export function submitTestPaper(data?: object) {
  return request({
    url: '/testresult',
    data
  })
}

export function finishPaper(data?: object) {
  return request({
    url: '/submit',
    data
  })
}

// 考生抓拍图片
export function takePhotos(data?: object) {
  return request({
    url: '/facecheck',
    data
  })
}

// 获取试卷所有试题id
export function testPaperIds(data?: object) {
  return request({
    url: '/papercontent',
    data
  })
}

// 根据试题id获取试题
export function testQuestion(data?: object) {
  return request({
    url: '/questionBankWrite/get',
    data
  })
}

// 手机辅助摄像头定时拍照
export function auxMonitor(data?: object) {
  return request({
    url: '/aux_monitor',
    data
  })
}

// 生成手机二维码
export function genQrcode(data?: object) {
  return request({
    url: '/genqrcode',
    data
  })
}

// 心跳探测
export function heartBeat(data?: object) {
  return request({
    url: '/heartbeat',
    data
  })
}

export function heartget(data?: object) {
  return request({
    url: '/heartget',
    data
  })
}

// 实时保存 问答题和算法题答案
export function questemp(data?: object) {
  return request({
    url: '/questemp',
    data
  })
}

// 获取考试第一次进入时间
export function startexam(data?: object) {
  return request({
    url: '/startexam',
    data
  })
}

// 获取服务器时间
export function servicetime(data?: object) {
  return request({
    url: '/backendtime',
    data
  })
}

// 算法题提交记录
export function judgeRecord(data?: object) {
  return request({
    url: '/judgeresult',
    data
  })
}

// 获取试卷key
export function getSecretKey(data?: object) {
  return request({
    url: '/getkey',
    data
  })
}

// 根据id获取试卷信息
export function stuPaper(data?: object) {
  return request({
    url: '/stupaper',
    data
  })
}

// 查看是否有身份证
export function stuID(data?: object) {
  return request({
    url: '/idupload',
    data
  })
}

// 生成OSS key
export function ststoken(data?: object) {
  return request({
    url: '/ststoken',
    data
  })
}

// 核验身份证,照片是否匹配
export function idcardcheck(data?: object) {
  return request({
    url: '/idcheck',
    data
  })
}

// 提交试卷
export function completePaper(data?: object) {
  return request({
    url: '/completetest',
    data
  })
}

// 保存试卷答案
export function saveAnswer(data?: object) {
  return request({
    url: '/tempanswer',
    data
  })
}

// 邀请码报名
export function codeEnroll(data?: object) {
  return request({
    url: '/coderegstr',
    data
  })
}

// 教育经历
export function eduexp(data?: object) {
  return request({
    url: '/eduexp',
    data
  })
}

// 获取微信二维码
export function wechatcode(data?: object) {
  return request({
    url: '/createwxbindqrcode',
    data
  })
}

// 监听绑定微信
export function checkbind(data?: object) {
  return request({
    url: '/checkbind',
    data
  })
}

// 校园经历
export function schoolexp(data?: object) {
  return request({
    url: '/schoolexp',
    data
  })
}

// 项目经历
export function projexp(data?: object) {
  return request({
    url: '/projexp',
    data
  })
}

// 技能特长
export function skills(data?: object) {
  return request({
    url: '/skills',
    data
  })
}

// 奖项证书
export function certificate(data?: object) {
  return request({
    url: '/certificate',
    data
  })
}

// 置顶考试
export function testtop(data?: object) {
  return request({
    url: '/testtop',
    data
  })
}

export function idupload(data?: object) {
  return request({
    url: '/idupload',
    data
  })
}

// 记录切屏次数
export function stuslicecount(data?: object) {
  return request({
    url: '/stuslicecount',
    data
  })
}
// 报考单位
export function units(data?: object) {
  return request({
    url: '/units',
    data
  })
}

// 延长考试
export function examdelay(data?: object) {
  return request({
    url: '/examdelay',
    data
  })
}

// 发送消息给某位考生
export function sendmsg(data?: object) {
  return request({
    url: '/papertemplate/examcenter/candidateStatus/sendmsg',
    data
  })
}

// 获取试卷成绩信息
export function examscoreinfo(data: { paper: string }) {
  return request({
    url: '/papertemplate/examscoreinfo',
    data
  })
}

// 发布成绩
export function publishscores(data: {
  templeteId: string
  publishType: 'score' | 'rank',
  passScore: number,
  range: number[]
}) {
  return request({
    url: '/papertemplate/stupaper/publish',
    data
  })
}
// 撤销发布
export function revokescore(data: { templeteId: string }) {
  return request({
    url: '/papertemplate/stupaper/revoke',
    data
  })
}
// 查询某张试卷是否已发布
export function ispublished(data: { paper: string }) {
  return request({
    url: '/papertemplate/ispublished',
    data
  })
}
// 题目校对
export function proofreadingrecords(data: {
  action: 'add' | 'query'
  id: string
  teacher?: string
  proofreading?: 0 | 1 | 2
  wrong_reason?: string
}) {
  return request({
    // url: '/proofreadingrecords',
    // url: '/proofreadingrecordsWrite/get',
    url: "/written/proofreading/get",
    data
  })
}

export function proofreadingrecordsSave(data: {
  action: 'add' | 'query'
  id: string
  teacher?: string
  proofreading?: 0 | 1 | 2
  wrong_reason?: string
}) {
  return request({
    // url: '/proofreadingrecords',
    url: '/written/proofreading/save',
    data
  })
}

// AI生成干扰项
export async function generateOptions(data: {
  /** 题型 */
  type: number
  /** 题干 */
  body: string
  /** 多少条 */
  limit: number
  /** 要生成选项的正确性 */
  isTrue?: boolean
  retry?: boolean
}): Promise<{
  content: string
  isTrue: boolean
}[]> {
  const res = await request({
    // url: '/generateoptions',
    // url: '/ai/aigenerateoptions',
    url: "/written/question/genOptions",
    data
  })
  return Promise.resolve(res.distractorOptions)
}

// 获取ai答案
export async function getAiAnswer(data: {
  /** 题目id，如果传了题目id则会保存该答案结果 */
  question_id?: string
  /** 题型 */
  type: number
  /** 题干 */
  body: string
  /** 选项 */
  options: {
    value: string
    content: string
  }[]
}): Promise<{
  answer: string
  explain: string
}> {
  if(data.options){
    data.optionsJson = data.options
    data.options = JSON.stringify(data.options)
  }
  const res = await request({
    // url: '/ai/getaianswer',
    url: "/written/question/genAnswer",
    data
  })
  return Promise.resolve({results:res})
}

// AI校对
export function aiproofreadingbycatg(data: {
  /** 题库id */
  catg: string
}) {
  return request({
    // url: '/ai/aiproofreadingbycatg',
    url: "/written/proofreading/proofreading",
    data
  })
}

// 查询正在执行的ai校对任务信息
export function getaiproofreadingtask(data: {
  /** 题库id */
  id: string
}) {
  return request({
    // url: '/getaiproofreadingtask',
    // url: 'ai/getaiproofreadingbycatg',
    url: "written/proofreading/getProofreading",
    data
  })
}

// 查询当前部门已有的校对任务id列表
export function getaiproofreadingtaskids(data): Promise<string[]> {
  return request({
    // url: '/ai/getaiproofreadingtaskids',
    url: "written/proofreading/getProofreadingTaskIds",
    data
  })
}

// 查询某一道题目的ai答题记录
export function getaianswerrecord(data: {
  /** 题目id */
  quesUUID: string
}): Promise<{
  answer: string
  explain: string
  ques: string
  update_at: string
  create_at: string
  create_by: string
}> {
  return request({
    url: '/questionBankWrite/getaianswerrecord',
    data
  })
}

// AI生成题目
export function aiGenerateQuestion(data: {
  describe: string
  type: number
  limit: number
}) {
  return request({
    url: '/ai/aigeneratequestion',
    data
  })
}

// AI答卷分析
export function aistudentpaperanalysis(data: {
  studentId: string
  templeteId: string
}) {
  return request({
    url: '/ai/stupaper/analyze',
    data
  })
}

// AI答卷分析
export function aiquesansweranalysis(data: {
  answer_info: any
}) {
  return request({
    // url: '/aiquesansweranalysis',
    url: '/ai/aiquesansweranalysis',
    data
  })
}

// AI更改题型
export function aichangequestype(data: {
  old_ques: string
  new_type: number
}) {
  return request({
    // url: '/aichangequestype',
    url: '/ai/aichangequestion',
    data
  })
}

// AI反馈
export function aifeedback(data: {
  apiname: string
  input: string
  output: any
  feedback: 0 | 1
}) {
  return request({
    url: '/papertemplate/exam/check/aifeedback',
    data,
    throttleTime: 1000
  })
}