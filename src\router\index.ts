import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { createRouterGuard } from './guard'
import { App } from 'vue'

export const examMigrateRoutes = [
  {
    path: 'writtenQuestionManage',
    name: 'writtenQuestionManage',
    meta: {
      title: '题目管理',
      icon: 'ques-i',
      roles: ['teacher'],
      keepAlive: true
    },
    component: () => import('@/views/writtenQuestionManage/index.vue')
  },
  {
    path: 'writtenQuestionManage/questionList',
    name: 'writtenQuestionList',
    meta: {
      title: '题目管理',
      icon: 'ques-i',
      roles: ['teacher'],
      keepAlive: true
    },
    component: () => import('@/views/writtenQuestionManage/QuestionManage.vue')
  },
  {
    path: 'writtenQuestionManage/draft',
    name: 'draft',
    meta: {
      title: '题目管理',
      icon: 'ques-i',
      roles: ['teacher'],
      keepAlive: true
    },
    component: () => import('@/views/writtenQuestionManage/draft/index.vue')
  },
    {
    path: 'positionManagement',
    name: 'positionManagement',
    meta: {
      title: '题目管理',
      icon: 'ques-i',
      roles: ['teacher'],
      keepAlive: true
    },
    component: () => import('@/views/positionManagement/index.vue')
  },
]
//默认路由，不需要权限
export const constRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login',
  },
  // 登录
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/Login2/index.vue')
  },
  {
    path: '/register/service',
    name: 'service',
    component: () => import('@/pages/registration-protocol/legal-notice.vue')
  },
  {
    path: '/register/privacy-policy',
    name: 'privacy-policy',
    component: () => import('@/pages/registration-protocol/privacy-policy.vue')
  },
  // 忘记密码
  {
    path: '/login/forgetPwd',
    name: 'forgetPwd',
    component: () => import('@/pages/system/ForgetPwd.vue')
  },
  // 修改密码
  {
    path: '/modify/:id',
    name: 'modifyPwd',
    component: () => import('@/pages/system/ChangePwdFromEmail.vue')
  },
  // 修改密码token失效
  {
    path: '/loseEfficacy',
    name: 'loseEfficacy',
    component: () => import('@/pages/system/loseEfficacy.vue')
  },

  // 绑定微信
  {
    path: '/register/bindwx',
    name: 'bindwx',
    component: () => import('@/views/Login2/bindwx.vue')
  },
  // 完善信息
  {
    path: '/register/perinfo',
    name: 'perinfo',
    component: () => import('@/views/Login2/bindwx.vue')
  },
  // 扫码成功
  {
    path: '/login/scansucc',
    name: 'scansucc',
    component: () => import('@/views/Login2/scansucc.vue')
  },

  {
    path: '/admin-login',
    name: 'admin-login',
    component: () => import('@/views/Login/adminLogin.vue')
  },
  {
    path: '/monitor-records/:id',
    name: 'monitor-records-id',
    meta: {
      title: '查看监控',
      roles: ['admin', 'teacher'],
    },
    props: true,
    component: () => import('@/views/Monitor/index.vue')
  },
  {
    path: '/multipleMonitorScreen',
    name: 'multipleMonitorScreen',
    props: (route) => ({
      updateFrequency: parseInt(route.query.updateFrequency as string),
      filter: {
        paperIds: route.query.paperIds
      },
    }),
    meta: {
      title: '多屏监考',
      roles: ['teacher'],
      tag: 'papermag'
    },
    // component: () => import('@/pages/monitorManage/monitor/MonitorLive.vue')
    component: () => import('@/pages/monitorManage/monitor/Monitor.vue')
  },
  {
    path: '/interview/interview-helper-runner',
    name: 'interview-helper-runner',
    props: (route) => JSON.parse(JSON.stringify(route.query)),
    meta: {
      roles: ['teacher'],
      tag: 'quesmag',
    },
    component: () => import('@/pages/interview-helper-runner/index.vue')
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/ErrorPage/index.vue')
  },
  {
    path: '/admin',
    name: 'admin',
    redirect: { name: 'welcome' },
    component: () => import('@/layouts/Admin/index.vue'),
    meta: {
      roles: ['padmin', 'teacher'],
      tag: 'all'
    },
    children: [
      {
        path: 'welcome',
        name: 'welcome',
        meta: {
          roles: ['padmin', 'teacher'],
          tag: 'all'
        },
        redirect: { name: "questionManage" },
      },
      {
        path: 'monitor-records',
        name: 'monitor-records',
        meta: {
          title: '查看监控',
          roles: ['padmin', 'teacher'],
          tag: 'all'
        },
        component: () => import('@/pages/monitorManage/monitorRecords/MonitorRecords.vue')
      },
      {
        path: 'account',
        name: 'account',
        meta: {
          title: '教师管理',
          icon: 'resume-gray',
          roles: ['padmin']
        },
        component: () => import('@/views/AccountManage/index.vue')
      },
      {
        path: 'positionManagementM',
        name: 'positionManagementM',
        meta: {
          title: '回收站',
          roles: ['teacher'],
          tag: 'all'
        },
        component: () => import('@/pages/recycle/index.vue')
      },
      {
        path: 'subjectManage/packageDetail',
        name: 'packageDetail',
        component: () => import('@/pages/questionManage/QuestionManage.vue')
      },
      {
        path: 'subjectManage/question-generator',
        name: 'question-generator',
        meta: {
          title: 'AI批量出题',
          icon: 'ques-i',
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/questionManage/QuestionGenerator.vue')
      },
      {
        path: 'subjectManage/draft',
        name: 'draft',
        meta: {
          title: '草稿箱',
          icon: 'draft',
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/views/QuestionManage/draft.vue')
      },
      {
        path: 'questionForm',
        name: 'questionForm',
        meta: {
          title: '新建题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/pages/questionManage/QuestionForm/index.vue')
      },
      {
        path: 'subjectManage/createQuestion',
        name: 'createQuestion',
        meta: {
          title: '新建题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/views/QuestionManage/createQuestion.vue')
      },
      {
        path: 'subjectManage/editQuestion',
        name: 'editQuestion',
        meta: {
          title: '编辑题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/views/QuestionManage/editQuestion.vue')
      },
      {
        path: 'examManage/correctPaper',
        name: 'correctPaper',
        meta: {
          title: '阅卷',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/correctPaperPlus.vue')
      },

      {
        path: 'examManage/paperManage',
        name: 'paperManage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
          icon: 'paper-m'
        },
        component: () => import('@/pages/paperManage/index.vue')
      },

      // —————————————————————————————————————————————————————————————————————————————————— 能力测评👇
      {
        path: 'examManage/ability-test-manage',
        name: 'ability-test-manage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
        },
        component: () => import('@/pages/abilityTest/AbilityTestManage.vue')
      },
      {
        path: 'examManage/ability-test-form',
        name: 'ability-test-form',
        meta: {
          title: '能力测评',
          roles: ['teacher'],
          tag: 'papermag',
          icon: 'paper-m'
        },
        component: () => import('@/pages/abilityTest/AbilityTestForm.vue')
      },
      {
        path: 'examManage/ability-test-allocate',
        name: 'ability-test-allocate',
        meta: {
          title: '关联考生',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/abilityTest/AbilityTestAllocate.vue')
      },
      {
        path: 'examManage/ability-test-grade-table',
        name: 'ability-test-grade-table',
        meta: {
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/abilityTest/AbilityTestGradeTable.vue')
      },
      // —————————————————————————————————————————————————————————————————————————————————— 能力测评👆


      // —————————————————————————————————————————————————————————————————————————————————— 面试👇
      {
        path: 'interview-question/interview-question-manage',
        name: 'interview-question-manage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
          keepAlive: true
        },
        component: () => import('@/pages/interview-question-manage/InterviewQuestionManage.vue')
      },
      {
        path: 'interview-question/interview-question-draft',
        name: 'interview-question-draft',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/interview-question-manage/InterviewQuestionDraft.vue')
      },
      {
        path: 'interview/interview-helper-manage',
        name: 'interview-helper-manage',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/interview-helper-manage/index.vue')
      },
      {
        path: 'interview/interview-helper-form',
        name: 'interview-helper-form',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        props: (route) => JSON.parse(JSON.stringify(route.query)),
        component: () => import('@/pages/interview-helper-manage/interview-helper-form/index.vue')
      },
      {
        path: 'interview/interview-room',
        name: 'interview-room',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/todo/Index.vue')
      },

      // —————————————————————————————————————————————————————————————————————————————————— 面试👆

      {
        path: 'examManage/monitorManage',
        name: 'monitorManage',
        meta: {
          title: '监考中心',
          roles: ['teacher'],
          tag: 'scoring',
          icon: 'm-monitor'
        },
        component: () => import('@/pages/monitorManage/MonitorManage.vue')
      },
      {
        path: 'examManage/manualMark',
        name: 'manualMark',
        meta: {
          title: '人工阅卷',
          roles: ['teacher'],
          tag: 'scoring',
          icon: 'm-correct'
        },
        component: () => import('@/pages/manualMark/ManualMark.vue')
        // component: () => import('@/views/ManualMark/index.vue')
      },
      {
        path: 'examManage/createPaper',
        name: 'createPaper',
        meta: {
          title: '新建考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/editPaper',
        name: 'editPaper',
        meta: {
          title: '编辑考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/copyPaper',
        name: 'copyPaper',
        meta: {
          title: '克隆考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/oneMorePaper',
        name: 'oneMorePaper',
        meta: {
          title: '再来一卷',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/monitor',
        name: 'monitor',
        meta: {
          title: '监考',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/monitorManage/monitor/Monitor.vue')
      },
      {
        path: 'examManage/paperEmailSend',
        name: 'paperEmailSend',
        meta: {
          title: '发送邀请邮件',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/paperEmailSend.vue')
      },
      {
        path: 'examManage/stuInfo',
        name: 'stuInfo',
        meta: {
          title: '考生信息',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/stuInfo.vue')
      },
      {
        path: 'examManage/examManagement',
        name: 'examManagement',
        meta: {
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/examManagement.vue')
      },
      {
        path: 'examManage/paperAllocate',
        name: 'paperAllocate',
        meta: {
          title: '关联考生',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperAllocate.vue')
      },
      {
        // 单场考试详情分析 // 考试分析中的单场考试分析使用同一个页面组件
        path: 'examManage/paperManage/detailAnalysis/:id',
        name: 'AnalysisDetail',
        props: true,
        meta: {
          roles: ['teacher'],
          tag: 'paper',
          from: 'paperManage',
        },
        component: () => import('@/pages/analysisManage/PaperDetailAnalysis.vue'),
        beforeEnter: (to, from, next) => {
          to.meta.from = from.name
          next()
        }
      },
      {
        path: 'studentManage/resumeManage',
        name: 'resumeManage',
        meta: {
          title: '简历管理',
          roles: ['teacher'],
          tag: 'stumag',
          icon: 'resume-m'
        },
        component: () => import('@/views/ResumeManage/index.vue')
      },
      {
        path: 'studentManage/createInterview',
        name: 'createInterview',
        component: () => import('@/views/ResumeManage/createInterview/index.vue')
      },
      {
        path: 'studentManage/interviewDetails',
        name: 'interviewDetails',
        meta: {
          title: '简历管理',
          roles: ['teacher'],
          tag: 'stumag',
          icon: 'resume-m'
        },
        component: () => import('@/pages/interviewDetails/index.vue')
      },
      {
        path: 'studentManage/resumeDetail',
        name: 'resumeDetail',
        meta: {
          roles: ['teacher'],
          tag: 'stumag',
        },
        component: () => import('@/views/ResumeManage/resumeDetail.vue')
      },
      {
        path: 'statisticAnalysis/catalogAnalysis',
        name: 'catalogAnalysis',
        meta: {
          title: '题库分析',
          roles: ['teacher'],
          tag: 'ques',
          icon: 'ques-anlysis'
        },
        component: () => import('@/pages/analysisManage/CatalogAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/testPaperAnalysis',
        name: 'testPaperAnalysis',
        meta: {
          title: '考试分析',
          roles: ['teacher'],
          tag: 'paper',
          icon: 'exam-anlysis'
        },
        component: () => import('@/pages/analysisManage/PaperAnalysis.vue')
      },
      {
        // 单场考试详情分析
        path: 'statisticAnalysis/testPaperAnalysis/detail/:id',
        name: 'testPaperAnalysisDetail',
        props: true,
        meta: {
          roles: ['teacher'],
          tag: 'paper',
          from: 'testPaperAnalysis',
        },
        component: () => import('@/pages/analysisManage/PaperDetailAnalysis.vue'),
        beforeEnter: (to, from, next) => {
          to.meta.from = from.name
          next()
        }
      },
      {
        path: 'statisticAnalysis/studentAnalysis',
        name: 'studentAnalysis',
        meta: {
          title: '考生分析',
          roles: ['teacher'],
          tag: 'stu',
          icon: 'stu-anlysis'
        },
        component: () => import('@/pages/analysisManage/StudentAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/studentSingleAnalysis',
        name: 'studentSingleAnalysis',
        meta: {
          title: '考生分析',
          roles: ['teacher'],
          tag: 'stu',
          icon: 'stu-anlysis',
        },
        component: () => import('@/pages/analysisManage/StudentSingleAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/paperAnalysis',
        name: 'paperAnalysis',
        meta: {
          title: '试卷分析',
          roles: ['teacher'],
          tag: 'paper',
        },
        component: () => import('@/views/StatisticAnalysis/paperAnalysis.vue')
      },
      {
        path: 'surveyManage',
        name: 'surveyManage',
        meta: {
          title: '问卷管理',
          icon: 'survey-m',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyManage.vue')
      },
      {
        path: 'surveyForm',
        name: 'surveyForm',
        meta: {
          title: '创建问卷',
          icon: 'm-resource',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyForm.vue'),
        props: (route) => JSON.parse(JSON.stringify(route.query))
      },
      {
        path: 'surveyAnalysis',
        name: 'surveyAnalysis',
        meta: {
          title: '问卷管理',
          icon: 'survey-m',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyAnalysis.vue'),
        redirect: { name: 'surveyAnalysisChart' },
        children: [{
          path: 'chart',
          name: 'surveyAnalysisChart',
          component: () => import('@/pages/survey/SurveyAnalysisChart.vue'),
          meta: {
            title: '数据分析',
            roles: ['teacher']
          },
        }, {
          path: 'table',
          name: 'surveyAnalysisTable',
          component: () => import('@/pages/survey/SurveyAnalysisTable.vue'),
          meta: {
            title: '问卷报表',
            roles: ['teacher']
          }
        }]
      },
      {
        path: 'systemSetting',
        name: 'systemSetting',
        meta: {
          title: '部门管理',
          icon: 'test-gray',
          roles: ['padmin']
        },
        component: () => import('@/views/SystemSetting/index.vue')
      },
      {
        path: 'resourceManage',
        name: 'resourceManage',
        meta: {
          title: '资源组管理',
          icon: 'm-resource',
          roles: ['padmin']
        },
        component: () => import('@/pages/resourceGroupManage/ResourceGroupManage.vue')
      },
      {
        path: 'resourceManage',
        name: 'resourceManage',
        meta: {
          title: '资源组管理',
          icon: 'm-resource',
          roles: ['padmin']
        },
        component: () => import('@/pages/resourceGroupManage/ResourceGroupManage.vue')
      },
    ]
  },
]

// 动态路由
export const asyncRoutes: RouteRecordRaw[] = [
  {
    path: '/admin',
    name: 'admin',
    redirect: { name: 'welcome' },
    component: () => import('@/layouts/Admin/index.vue'),
    meta: {
      roles: ['padmin', 'teacher'],
      tag: 'all'
    },
    children: [
      {
        path: 'welcome',
        name: 'welcome',
        meta: {
          roles: ['padmin', 'teacher'],
          tag: 'all'
        },
        redirect: { name: "questionManage" },
      },
      {
        path: 'monitor-records',
        name: 'monitor-records',
        meta: {
          title: '查看监控',
          roles: ['padmin', 'teacher'],
          tag: 'all'
        },
        component: () => import('@/pages/monitorManage/monitorRecords/MonitorRecords.vue')
      },
      {
        path: 'account',
        name: 'account',
        meta: {
          title: '教师管理',
          icon: 'resume-gray',
          roles: ['padmin']
        },
        component: () => import('@/views/AccountManage/index.vue')
      },
      {
        path: 'positionManagementM',
        name: 'positionManagementM',
        meta: {
          title: '回收站',
          roles: ['teacher'],
          tag: 'all'
        },
        component: () => import('@/pages/recycle/index.vue')
      },
      {
        path: 'subjectManage/questionManage',
        name: 'questionManage',
        meta: {
          title: '题目管理',
          icon: 'ques-i',
          roles: ['teacher'],
          tag: 'quesmag',
          keepAlive: true
        },
        // component: () => import('@/pages/questionManage/QuestionManage.vue')
        component: () => import('@/pages/questionManage/QuestionPackageManage.vue')
      },
      ...examMigrateRoutes,
      {
        path: 'subjectManage/packageDetail',
        name: 'packageDetail',
        component: () => import('@/pages/questionManage/QuestionManage.vue')
      },
      {
        path: 'subjectManage/question-generator',
        name: 'question-generator',
        meta: {
          title: 'AI批量出题',
          icon: 'ques-i',
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/questionManage/QuestionGenerator.vue')
      },
      {
        path: 'subjectManage/draft',
        name: 'draft',
        meta: {
          title: '草稿箱',
          icon: 'draft',
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/views/QuestionManage/draft.vue')
      },
      {
        path: 'questionForm',
        name: 'questionForm',
        meta: {
          title: '新建题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/pages/questionManage/QuestionForm/index.vue')
      },
      {
        path: 'subjectManage/createQuestion',
        name: 'createQuestion',
        meta: {
          title: '新建题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/views/QuestionManage/createQuestion.vue')
      },
      {
        path: 'subjectManage/editQuestion',
        name: 'editQuestion',
        meta: {
          title: '编辑题目',
          roles: ['teacher'],
          tag: 'quesmag'
        },
        component: () => import('@/views/QuestionManage/editQuestion.vue')
      },
      {
        path: 'examManage/correctPaper',
        name: 'correctPaper',
        meta: {
          title: '阅卷',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/correctPaperPlus.vue')
      },

      {
        path: 'examManage/paperManage',
        name: 'paperManage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
          icon: 'paper-m'
        },
        component: () => import('@/pages/paperManage/index.vue')
      },

      // —————————————————————————————————————————————————————————————————————————————————— 能力测评👇
      {
        path: 'examManage/ability-test-manage',
        name: 'ability-test-manage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
        },
        component: () => import('@/pages/abilityTest/AbilityTestManage.vue')
      },
      {
        path: 'examManage/ability-test-form',
        name: 'ability-test-form',
        meta: {
          title: '能力测评',
          roles: ['teacher'],
          tag: 'papermag',
          icon: 'paper-m'
        },
        component: () => import('@/pages/abilityTest/AbilityTestForm.vue')
      },
      {
        path: 'examManage/ability-test-allocate',
        name: 'ability-test-allocate',
        meta: {
          title: '关联考生',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/abilityTest/AbilityTestAllocate.vue')
      },
      {
        path: 'examManage/ability-test-grade-table',
        name: 'ability-test-grade-table',
        meta: {
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/abilityTest/AbilityTestGradeTable.vue')
      },
      // —————————————————————————————————————————————————————————————————————————————————— 能力测评👆


      // —————————————————————————————————————————————————————————————————————————————————— 面试👇
      {
        path: 'interview-question/interview-question-manage',
        name: 'interview-question-manage',
        meta: {
          roles: ['teacher'],
          tag: 'papermag',
          keepAlive: true
        },
        component: () => import('@/pages/interview-question-manage/InterviewQuestionManage.vue')
      },
      {
        path: 'interview-question/interview-question-draft',
        name: 'interview-question-draft',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/interview-question-manage/InterviewQuestionDraft.vue')
      },
      {
        path: 'interview/interview-helper-manage',
        name: 'interview-helper-manage',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/interview-helper-manage/index.vue')
      },
      {
        path: 'interview/interview-helper-form',
        name: 'interview-helper-form',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        props: (route) => JSON.parse(JSON.stringify(route.query)),
        component: () => import('@/pages/interview-helper-manage/interview-helper-form/index.vue')
      },
      {
        path: 'interview/interview-room',
        name: 'interview-room',
        meta: {
          roles: ['teacher'],
          tag: 'quesmag',
        },
        component: () => import('@/pages/todo/Index.vue')
      },

      // —————————————————————————————————————————————————————————————————————————————————— 面试👆

      {
        path: 'examManage/monitorManage',
        name: 'monitorManage',
        meta: {
          title: '监考中心',
          roles: ['teacher'],
          tag: 'scoring',
          icon: 'm-monitor'
        },
        component: () => import('@/pages/monitorManage/MonitorManage.vue')
      },
      {
        path: 'examManage/manualMark',
        name: 'manualMark',
        meta: {
          title: '人工阅卷',
          roles: ['teacher'],
          tag: 'scoring',
          icon: 'm-correct'
        },
        component: () => import('@/pages/manualMark/ManualMark.vue')
        // component: () => import('@/views/ManualMark/index.vue')
      },
      {
        path: 'examManage/createPaper',
        name: 'createPaper',
        meta: {
          title: '新建考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/editPaper',
        name: 'editPaper',
        meta: {
          title: '编辑考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/copyPaper',
        name: 'copyPaper',
        meta: {
          title: '克隆考试',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/oneMorePaper',
        name: 'oneMorePaper',
        meta: {
          title: '再来一卷',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperForm.vue')
      },
      {
        path: 'examManage/monitor',
        name: 'monitor',
        meta: {
          title: '监考',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/monitorManage/monitor/Monitor.vue')
      },
      {
        path: 'examManage/paperEmailSend',
        name: 'paperEmailSend',
        meta: {
          title: '发送邀请邮件',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/paperEmailSend.vue')
      },
      {
        path: 'examManage/stuInfo',
        name: 'stuInfo',
        meta: {
          title: '考生信息',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/stuInfo.vue')
      },
      {
        path: 'examManage/examManagement',
        name: 'examManagement',
        meta: {
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/views/PaperManage/examManagement.vue')
      },
      {
        path: 'examManage/paperAllocate',
        name: 'paperAllocate',
        meta: {
          title: '关联考生',
          roles: ['teacher'],
          tag: 'papermag'
        },
        component: () => import('@/pages/paperManage/PaperAllocate.vue')
      },
      {
        // 单场考试详情分析 // 考试分析中的单场考试分析使用同一个页面组件
        path: 'examManage/paperManage/detailAnalysis/:id',
        name: 'AnalysisDetail',
        props: true,
        meta: {
          roles: ['teacher'],
          tag: 'paper',
          from: 'paperManage',
        },
        component: () => import('@/pages/analysisManage/PaperDetailAnalysis.vue'),
        beforeEnter: (to, from, next) => {
          to.meta.from = from.name
          next()
        }
      },
      {
        path: 'studentManage/resumeManage',
        name: 'resumeManage',
        meta: {
          title: '简历管理',
          roles: ['teacher'],
          tag: 'stumag',
          icon: 'resume-m'
        },
        component: () => import('@/views/ResumeManage/index.vue')
      },
      {
        path: 'studentManage/createInterview',
        name: 'createInterview',
        component: () => import('@/views/ResumeManage/createInterview/index.vue')
      },
      {
        path: 'studentManage/interviewDetails',
        name: 'interviewDetails',
        meta: {
          title: '简历管理',
          roles: ['teacher'],
          tag: 'stumag',
          icon: 'resume-m'
        },
        component: () => import('@/pages/interviewDetails/index.vue')
      },
      {
        path: 'studentManage/resumeDetail',
        name: 'resumeDetail',
        meta: {
          roles: ['teacher'],
          tag: 'stumag',
        },
        component: () => import('@/views/ResumeManage/resumeDetail.vue')
      },
      {
        path: 'statisticAnalysis/catalogAnalysis',
        name: 'catalogAnalysis',
        meta: {
          title: '题库分析',
          roles: ['teacher'],
          tag: 'ques',
          icon: 'ques-anlysis'
        },
        component: () => import('@/pages/analysisManage/CatalogAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/testPaperAnalysis',
        name: 'testPaperAnalysis',
        meta: {
          title: '考试分析',
          roles: ['teacher'],
          tag: 'paper',
          icon: 'exam-anlysis'
        },
        component: () => import('@/pages/analysisManage/PaperAnalysis.vue')
      },
      {
        // 单场考试详情分析
        path: 'statisticAnalysis/testPaperAnalysis/detail/:id',
        name: 'testPaperAnalysisDetail',
        props: true,
        meta: {
          roles: ['teacher'],
          tag: 'paper',
          from: 'testPaperAnalysis',
        },
        component: () => import('@/pages/analysisManage/PaperDetailAnalysis.vue'),
        beforeEnter: (to, from, next) => {
          to.meta.from = from.name
          next()
        }
      },
      {
        path: 'statisticAnalysis/studentAnalysis',
        name: 'studentAnalysis',
        meta: {
          title: '考生分析',
          roles: ['teacher'],
          tag: 'stu',
          icon: 'stu-anlysis'
        },
        component: () => import('@/pages/analysisManage/StudentAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/studentSingleAnalysis',
        name: 'studentSingleAnalysis',
        meta: {
          title: '考生分析',
          roles: ['teacher'],
          tag: 'stu',
          icon: 'stu-anlysis',
        },
        component: () => import('@/pages/analysisManage/StudentSingleAnalysis.vue')
      },
      {
        path: 'statisticAnalysis/paperAnalysis',
        name: 'paperAnalysis',
        meta: {
          title: '试卷分析',
          roles: ['teacher'],
          tag: 'paper',
        },
        component: () => import('@/views/StatisticAnalysis/paperAnalysis.vue')
      },
      {
        path: 'surveyManage',
        name: 'surveyManage',
        meta: {
          title: '问卷管理',
          icon: 'survey-m',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyManage.vue')
      },
      {
        path: 'surveyForm',
        name: 'surveyForm',
        meta: {
          title: '创建问卷',
          icon: 'm-resource',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyForm.vue'),
        props: (route) => JSON.parse(JSON.stringify(route.query))
      },
      {
        path: 'surveyAnalysis',
        name: 'surveyAnalysis',
        meta: {
          title: '问卷管理',
          icon: 'survey-m',
          roles: ['teacher']
        },
        component: () => import('@/pages/survey/SurveyAnalysis.vue'),
        redirect: { name: 'surveyAnalysisChart' },
        children: [{
          path: 'chart',
          name: 'surveyAnalysisChart',
          component: () => import('@/pages/survey/SurveyAnalysisChart.vue'),
          meta: {
            title: '数据分析',
            roles: ['teacher']
          },
        }, {
          path: 'table',
          name: 'surveyAnalysisTable',
          component: () => import('@/pages/survey/SurveyAnalysisTable.vue'),
          meta: {
            title: '问卷报表',
            roles: ['teacher']
          }
        }]
      },
      {
        path: 'systemSetting',
        name: 'systemSetting',
        meta: {
          title: '部门管理',
          icon: 'test-gray',
          roles: ['padmin']
        },
        component: () => import('@/views/SystemSetting/index.vue')
      },
      {
        path: 'resourceManage',
        name: 'resourceManage',
        meta: {
          title: '资源组管理',
          icon: 'm-resource',
          roles: ['padmin']
        },
        component: () => import('@/pages/resourceGroupManage/ResourceGroupManage.vue')
      },

    ]
  },
  // {
  //   path: '/:catchAll(.*)',
  //   name: 'not-found',
  //   redirect: '/404',
  //   meta: { tag: 'all' }
  // }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: [...constRoutes]
})


export async function setupRouter(app: App) {
  app.use(router)
  createRouterGuard(router)
  await router.isReady()
}

export default router
