<template>
  <div>
    <a-modal
      width="1000px"
      title="添加图表"
      v-model:visible="visible"
      :footer="null"
    >
      <div class="graphWrapper">
        <template v-for="item in graphList" :key="item.id">
          <div class="item" :class="{ active: item.checked }">
            <img v-if="item.charttype === null" src="@/assets/icons/svg/num_icon.svg" />
            <img v-if="item.charttype === 'pie'" src="@/assets/icons/svg/circle.svg" />
            <img v-if="item.charttype === 'table'" src="@/assets/icons/svg/table_icon.svg" />
            <p>{{ item.name }}</p>
            <a-checkbox
              class="checkbox"
              v-model:checked="item.checked"
              :class="{ grayStyle: !item.checked }"
            ></a-checkbox>
          </div>
        </template>
      </div>
      <div class="btn-group">
          <a-button type="primary" @click="handleOk">确定</a-button>
          <a-button @click="handleClose">取消</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps({
  data: {
    type: Array
  }
})

const emits = defineEmits(['addGraph', 'closeGraph'])

// const checked = ref(false);
const visible = ref<boolean>(true)

const handleOk = () => {
  const selectedGraph = graphList.value.filter((item: any) => item.checked)
  emits('addGraph', selectedGraph)
  handleClose()
}

const handleClose = () => {
  emits('closeGraph')
}

const graphList = ref<any>([])
watch(
  () => props.data,
  (val) => {
    graphList.value = val
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
// width: 100%;
.graphWrapper {
  display: flex;
  flex-wrap: wrap;
  .item {
    background-color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin-right: 20px;
    display: flex;
    width: 31%;
    align-items: center;
    margin-bottom: 20px;
    padding: 9px 16px 9px 9px;
    &.active {
      border-color: #5478ee;
    }
    img {
      width: 32px;
      height: 32px;
      margin-right: 4px;
    }
    p {
      flex: 1;
      font-size: 18px;
    }
    .checkbox {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-bottom: 5px;
    }
    :deep(.grayStyle .ant-checkbox-inner) {
      border-color: #d9d9d9 !important;
    }
  }
}

.btn-group {
    display: flex;
    align-items: center;
    justify-content: center;

    .ant-btn {
        border-radius: 8px;

        &:first-child {
            margin-right: 8px;
        }
    }
}
</style>
