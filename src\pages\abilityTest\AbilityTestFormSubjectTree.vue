<template>
    <div class="subjects">
        <div class="category-container" :style="{ width: boxWidth + 'px' }">
            <div class="header">
                <h2>选择题库范围</h2>
                <div class="search-ipt-wrapper" :class="{ active: searchOpen }">
                    <a-input ref="searchIptRef" class="search-ipt" v-model:value.trim="searchValue"
                        @blur="searchOpen = false" :bordered="false" placeholder="请输入关键词" allowClear>
                    </a-input>
                    <SearchOutlined @click="handleSearch" />
                </div>
            </div>
            <div class="body">
                <a-spin v-if="fetchLoading" class="loading-style"></a-spin>
                <img v-else-if="!fetchLoading && !treeData.length" style="width: 200px;margin-top: 100px;"
                    src="@/assets/images/nodata.png" alt="">
                <a-tree v-else checkable show-line :blockNode="true" :tree-data="treeData"
                    :autoExpandParent="autoExpandParent" :field-names="{ title: 'name', key: 'id' }"
                    v-model:expandedKeys="expandedKeys" :selectedKeys="selectedKeys" v-model:checkedKeys="checkedKeys"
                    @select="onSelect" @expand="onExpand" @check="onCheck">
                    <template #title="item">
                        <div class="custom-title" @mouseover="(e: any) => onHover(e, item)" @mouseleave="start">
                            <SearchHighLight class="title" :text="item.name" :search-text="searchValue">
                            </SearchHighLight>
                        </div>
                    </template>

                    <template #switcherIcon="{ switcherCls }">
                        <Icon icon="DownOutlined" :class="switcherCls" />
                    </template>
                </a-tree>

            </div>
            <div class="specific-info" v-if="hoverItem" :style="{ top: hoverItem.top + 'px' }" @mouseover="stop"
                @mouseleave="start">
                <div class="specific-info-row" style="height: 28px;">
                    <div style="background-color: #f1f4fe;">题型</div>
                    <div style="background-color: #f1f4fe;">题量</div>
                </div>
                <ul>
                    <li v-for="t in Object.keys(hoverItem.type_ques_count)" class="specific-info-row"
                        style="height: 24px;">
                        <div class="label">
                            {{ QuestionEnum[t] }}
                        </div>
                        <div class="value" style="color: rgba(0,0,0,0.45);">{{ hoverItem.type_ques_count[t] }}道</div>
                    </li>
                </ul>
                <a-divider style="margin: 4px 0;"></a-divider>
                <div class="specific-info-row" style="height: 28px;">
                    <span><svg-icon name="exam-m" style="margin-right: 4px;font-size: 16px;"></svg-icon>合计</span>
                    <span>{{ hoverItem.num }}道</span>
                </div>
            </div>
            <div id="resizer" @mousedown="startResize"
                style="width:4px;height:100%;position:absolute;right:0;top:0;cursor:e-resize;border-right:2px solid #5478EE;transition:all ease 0.2s;opacity: 0;">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import { subjcatgs, catgquestypecount } from '@/api/admin/questionManage'

import { SearchOutlined } from '@ant-design/icons-vue'
import SearchHighLight from '@/components/SearchHighLight.vue'
import { useTimeoutFn } from '@vueuse/core'
import { QuestionEnum } from '@/models/questionModel'

const props = defineProps<{
    checkedKeys?: string[]
}>()

type TreeNode = {
    key?: string
    id: string
    name: string
    is_official_cert: boolean
    children: TreeNode[]
    pathName: string
    num: number
    type_ques_count: Record<number, number>
    isLeaf: boolean
    disableCheckbox?: boolean
}

const emits = defineEmits<{
    /** 这里只会统计当前选中的节点，选中节点的子节点将不被统计 */
    (e: 'updateChecked', value: any[]): void
}>()

const treeData = ref<TreeNode[]>([])
const expandedKeys = ref<any[]>([''])
const selectedKeys = ref([''])

const onSelect = (keys: string[], { node }: any) => {
    if (keys.length === 0) return
    selectedKeys.value = keys
}

const onExpand = (keys: string[]) => {
    expandedKeys.value = keys;
    autoExpandParent.value = false;
}

// 节点选中
const checkedKeys = ref<string[]>([])
watch(() => props.checkedKeys, (v) => {
    if (v) {
        checkedKeys.value = v
        onCheck(v)
    }
})
function onCheck(checkedKeys: any) {
    let ans: any[] = []
    let fn = (arr: any[]) => {
        if (!arr?.length) return
        arr.forEach(item => {
            if (checkedKeys.includes(item.id)) {
                ans.push(item)
            } else {
                fn(item.children)
            }
        })
    }
    fn(treeData.value)
    emits('updateChecked', ans)
}
function setCheckedKeys(v: string[]) {
    checkedKeys.value = v
    onCheck(v) // 执行时很可能源数据还没请求完,导致执行无效
    if (originTreeData.value.length === 0) {
        getSubjectDataCallBackFn = () => onCheck(v)
    }
}

// 获取源数据的回调(在获取之前或者过程中可能需要执行一个依赖与源数据的方法,此方法会在获取完源数据后被调用)
let getSubjectDataCallBackFn: Function | null = null

const originTreeData = ref<TreeNode[]>([])
const fetchLoading = ref(false)
async function getSubjectData() {
    fetchLoading.value = true
    try {
        let res: any = await catgquestypecount()
        res = Array.isArray(res) ? res : []
        setPathNameAndNum(res)
        treeData.value = res
        originTreeData.value = res
        getSubjectDataCallBackFn?.()
        getSubjectDataCallBackFn = null
    } finally {
        fetchLoading.value = false
    }
}

// 设置每一层级的树节点的路劲名称
function setPathNameAndNum(arr: TreeNode[], parentPathName = '') {
    if (!arr?.length) return
    arr.forEach(item => {
        item.pathName = parentPathName ? parentPathName + '>' + item.name : item.name
        item.num = Object.values(item.type_ques_count).reduce((p, c) => p + c, 0)
        if (item.num === 0) {
            item.disableCheckbox = true
        }
        if (item.children?.length) {
            setPathNameAndNum(item.children, item.pathName)
            item.isLeaf = false
        } else {
            item.isLeaf = true
        }
    })
}

// 科目搜索
const searchIptRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const autoExpandParent = ref(false)
function handleSearch() {
    searchOpen.value = true
    searchIptRef.value.focus()
}

function getParentId(id: string | number, tree: TreeNode[]): string | number | undefined {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
            if (node.children.some(item => item.id === id)) {
                parentKey = node.id;
            } else if (getParentId(id, node.children)) {
                parentKey = getParentId(id, node.children);
            }
        }
    }
    return parentKey;
}

watch(searchValue, (val) => {
    treeData.value = filterTree(originTreeData.value, val)
    autoExpandParent.value = true
    if (val) return
    // 如果搜索词为空字符串，则只展开选中科目
    let id = getParentId(selectedKeys.value[0], originTreeData.value)
    expandedKeys.value = [id]
})

function filterTree(roots: TreeNode[], filterText: string): TreeNode[] {
    return roots.map((root) => filterSingleNode(root, filterText)).filter(Boolean) as TreeNode[];
}

function filterSingleNode(node: TreeNode, filterText: string): TreeNode | null {
    if (node.name.includes(filterText)) {
        // 如果当前节点符合条件，返回当前节点
        if (filterText) {
            let id = getParentId(node.id, originTreeData.value)
            expandedKeys.value.push(id)
        }
        return {
            ...node,
            children: filterTree(node.children, filterText),
        };
    } else {
        // 如果当前节点不符合条件，递归过滤子节点
        const filteredChildren = filterTree(node.children, filterText);
        if (filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren,
            };
        } else {
            return null; // 如果当前节点及其子节点都不符合条件，返回null
        }
    }
}

// 查看每个题库的题目数量分布
const hoverItem = ref<any>()
function onHover(e: any, item: any) {
    if (isPending) stop()
    console.log(e.target.offsetTop, e)
    item.top = e.pageY - 150
    hoverItem.value = item
}
const { start, stop, isPending } = useTimeoutFn(() => {
    hoverItem.value = null
}, 200)


// 宽度拖拽
const lastBoxWidth = ref(240)
const BOX_MAX_WIDTH = 400
const boxWidth = ref(240)
const startX = ref(0);
const isMoving = ref(false)
const startResize = (e: MouseEvent) => {
    startX.value = e.clientX;
    console.log('startX:', startX.value)
    isMoving.value = true
    window.addEventListener('mousemove', resize);
    window.addEventListener('mouseup', stopResize);
};

const resize = (e: MouseEvent) => {
    if (!isMoving.value) return
    const newWidth = lastBoxWidth.value + e.clientX - startX.value;
    boxWidth.value = Math.min(newWidth, BOX_MAX_WIDTH);
};

const stopResize = () => {
    isMoving.value = false
    lastBoxWidth.value = boxWidth.value
    window.removeEventListener('mousemove', resize);
    window.removeEventListener('mouseup', stopResize);
};



onMounted(async () => {
    await getSubjectData()
    selectedKeys.value = [treeData.value[0].id]
    expandedKeys.value = [treeData.value[0].id]
})

onUnmounted(() => {
    stopResize()
})

defineExpose({
    setCheckedKeys
})

</script>

<style lang="less" scoped>
:deep(.ant-tree) {
    width: min-content;

    .ant-tree-checkbox {
        margin-top: 8px !important;
    }

    .ant-tree-node-content-wrapper {
        padding-left: 4px !important;
    }

}

.subjects {
    height: 100%;
    user-select: none;
    border: 1px solid #e8e8e8;

    .collapse-menu-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 48px;
        background: #ffffff;
        border: 1px solid #e8e8e8;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
        z-index: 999;
        cursor: pointer;
    }

    .menu-icon {
        cursor: pointer;
    }

    .category-container {
        height: 100%;
        min-width: 240px;
        flex-shrink: 0 !important;
        background: #fff;
        display: flex;
        flex-direction: column;
        position: relative;

        .specific-info {
            width: 200px;
            padding: 12px 8px;
            position: absolute;
            left: calc(100% + 3px);
            border-radius: 4px;
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
            font-size: 12px;
            background-color: #fff;
            transition: top ease .1s;

            .specific-info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;

                >div,
                span {
                    flex: 1;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    padding: 0 12px;

                    &:first-child {
                        margin-right: 2px;
                    }
                }

                >ul {
                    line-height: 28px
                }
            }
        }

        #resizer:hover {
            opacity: 1 !important;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            line-height: 48px;
            padding: 0 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;

            h2 {
                font-family: PingFang SC;
                font-size: 14px;
                line-height: inherit;
                color: #121633;
                font-weight: bold;
            }

            .plus-icon {
                cursor: pointer;
            }

            .addsubject {
                display: none;
            }

            .search-ipt-wrapper {
                position: absolute;
                right: 0;
                background-color: #fff;
                padding: 4px;
                border-radius: 8px;
                display: flex;

                &.active {
                    border: 1px solid #5478ee;

                    .search-ipt {
                        padding: 0 2px;
                        width: 200px;
                    }
                }

                .anticon {
                    cursor: pointer;
                    padding: 4px;
                }

                .anticon-search {
                    z-index: 999;
                    border-radius: 4px;
                    background-color: #fff;

                    &:hover {
                        background-color: #e8e8e8;
                    }
                }

                .search-ipt {
                    width: 0;
                    height: 22px;
                    padding: 0;
                    border: none;
                    box-shadow: none;

                    :deep(.ant-input) {
                        font-size: 13px;
                    }
                }
            }
        }
    }

    .loading-style {
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body {
        padding-left: 16px;
        padding-top: 8px;
        overflow: auto;
        // height: calc(100% - 50px);
        flex: 1;
        min-height: 0;
    }

    :deep(.ant-tree-node-content-wrapper) {
        flex: auto;
        min-width: 0;
    }

    :deep(.ant-tree-title) {
        flex: auto;
        min-width: 0;
    }

    .custom-title {
        max-width: 100%;
        height: 24px;
        padding-right: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            min-width: 0;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>