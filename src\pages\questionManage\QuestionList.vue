<script lang="ts" setup>
// @ts-nocheck
import type { QuestionModel } from '@/models/questionModel'
import { addQuestion, addCategory, editCategory,alltags, befredelques, delQuestion } from '@/api/admin/questionManage'
import { aiproofreadingbycatg, getaiproofreadingtaskids } from '@/api/exam/index'
import { QuestionEnum } from '@/models/questionModel'
import { queryQuestion, quesionDelete, addAiCheckByQidApi, addAiCheckByQPathApi, quesionDeleteBatch,getBankProgress } from './hooks/api'
import ProofreadingTask from '@/pages/questionManage/components/ProofreadingTask.vue'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
import QuestionProofreadForm from '@/pages/questionManage/QuestionProofread.vue'
import { getQuestionContentByRecord } from '@/utils'
import emitter from '@/utils/bus'
// import QuestionAddForm from '@/views/QuestionManage/createQuestion.vue'
import QuestionAddForm from '@/views/QuestionManage/_components/QuestionBody/index.vue'
import QuestionEditForm from '@/views/QuestionManage/editQuestion.vue'
import { CheckCircleFilled, ExclamationCircleFilled, InfoCircleFilled, StarFilled, StarOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, createVNode, h, nextTick, onActivated, onDeactivated, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import ProofreadingTaskFinished from './components/ProofreadingTaskFinished.vue'
import PreviewVideo from './components/previewVideo.vue'
import { useRoute } from 'vue-router'
import { objectEntries } from '@vueuse/core'
import { useQuestionManage } from '@/views/QuestionManage/_components/composable'


const { clearFormState } = useQuestionManage()
const props = withDefaults(
  defineProps<{
    subjectId?: string
    subjectPath?: string
    /** 是否是官方科目 */
    isOfficialSubject?: boolean
    defaultType?: number | null
    defaultSelectedRows?: any[]
    defaultExcludedRowKeys?: string[]
    disabledRowKeys?: string[]
    writable?: boolean
    current?: number,
    subjectUUID?:any,
    operationFlag?:any
    selectItem?:any,
  }>(),
  {
    subjectId: '',
    isOfficialSubject: false,
    defaultType: null,
    defaultSelectedRows: () => [],
    defaultExcludedRowKeys: () => [],
    disabledRowKeys: () => [],
    writable: false,
    current: 1,
    subjectUUID:"",
    operationFlag:false
  },
)


const emits = defineEmits<{
  (e: 'select', record: any, selected: boolean): void
}>()

const store = useStore()

// 可选/已选/已排除
const listTypeEnum = ['可选', '已选', '已排除']
const listType = ref('可选')

// 是否包含子科目
const includeSubCatg = ref(true)

// 快速搜索
const searchContent = ref('')
function getFirstPageList() {
  otherParams.value.includeChildrenUUIDs = includeSubCatg.value
  paginationConfig.current = 1
  const params = {
    entity: {
      content: `*${searchContent.value}*`,
    },
  }
  getList(params)
}

// 标签
const activeTagIds = reactive<string[]>([])
const selectedTags = computed(() =>
  tagsList.value.filter((item: any) => activeTagIds.includes(item.id)).map((i: any) => i.name),
)
function handleClickTag(id: string) {
  const index = activeTagIds.findIndex(i => i === id)
  if (index === -1) {
    activeTagIds.push(id)
  }
  else {
    activeTagIds.splice(index, 1)
  }
  getList()
}

// 其他参数
const otherParams = ref<{
  types?: number[] | null
  proofreading?: (0 | 1 | 2)[]
  order_field?: string
  order_type?: 'ascend' | 'descend'
  /** 是否仅显示当前科目的试题（不包含子科目） */
  includeChildrenUUIDs: boolean
  create_by?: number[] | null
}>({
      types: typeof props.defaultType === 'number' ? [props.defaultType] : null,
      includeChildrenUUIDs: true,
    })

function handleTableChange(pagination: any, filters: any = {}, sort: any = {},actionInfo:any) {
  const { action } = actionInfo
  const params = {
    entity:{},
    params:{},
  }
  if (action === 'filter' || action === 'sort') {
    paginationConfig.current = 1

    if(filters.proofreading){
      params.entity.checkStatus = filters.proofreading.join(",")
    }
    if(filters.difficulty) {
      // params.params['difficulty'] = filters.difficulty
      params.params['difficulty_MultiString'] = filters.difficulty.join(",")

    }

    if(sort && sort.columnKey) {
      params.params = {
        order: sort.order === "descend" ? "desc" : "asc",
        column: sort.columnKey,
      }
    }
  } else if(action === "paginate"){
    paginationConfig.current = pagination.current
    paginationConfig.pageSize = pagination.pageSize
  }

  getList(params)
}

// 获取所有标签
const tagBtnState = ref<'隐藏' | '更多' | '收起'>('隐藏')
const tagsHeight = computed(() => (tagBtnState.value === '更多' ? 'auto' : '30px'))
const tagsRotate = computed(() =>
  tagBtnState.value === '更多' ? 'rotate(0deg)' : 'rotate(180deg)',
)

const allTagsContainerRef = ref<HTMLDivElement>()
const tagsList = ref<any>([])
async function getAllTags() {
  // layoutTags()
  // const res = await alltags({ tag: 'question', categoryId: props.subjectId })
  // tagsList.value = res
  // dom更新后查看高度，如果超过一行则展示“更多”，并设置为单行高度
  // nextTick().then(layoutTags)
}
function handleTagBtnStateChange() {
  tagBtnState.value = tagBtnState.value === '更多' ? '收起' : '更多'
}
async function layoutTags() {
  return
  tagBtnState.value = '更多' // 先自适应高度
  await nextTick()
  const initialHeight = allTagsContainerRef.value!.offsetHeight // 获取实际高度
  if (initialHeight > 35) {
    // 当一行宽度不够展示所有标签时，才展示展开和收起的按钮
    // 当有选择标签时,默认展开
    if (selectedTags.value.length) {
      tagBtnState.value = '更多'
    }
    else {
      tagBtnState.value = '收起'
    }
  }
  else {
    tagBtnState.value = '隐藏'
  }
}

onMounted(() => {
  // getAllTags()
})

// 表格配置
const proofreadingColumn = {
  title: '校对状态',
  dataIndex: 'proofreading',
  key: 'proofreading',
  width: 120,
  ellipsis: true,
  resizable: true,
  filters: [
    { text: '校对正确', value: 1 },
    { text: '校对错误', value: 2 },
    { text: '未校对', value: 0 },
  ],
}
const actionColumn = {
  title: '操作',
  key: 'action',
  width: 380,
  align: 'center',
  fixed: 'right',
}
const rawColumsData = [
  {
    title: '题型',
    dataIndex: 'type',
    key: 'types',
    width: 100,
    ellipsis: false,
    // filters: [
    //   { text: '单选题', value: 0 },
    //   { text: '多选题', value: 1 },
    //   { text: '判断题', value: 2 },
    //   { text: '填空题', value: 5 },
    //   { text: '问答题', value: 3 },
    //   { text: '算法题', value: 4 },
    //   { text: '排序题', value: 6 },
    // ],
    resizable: true,
  },
  {
    title: '题干',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
    resizable: true,
    width: 500,
  },
  proofreadingColumn,
  {
    title: '科目',
    dataIndex: 'bankPath',
    key: 'bankPath',
    width: 160,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '难度',
    dataIndex: 'difficulty',
    key: 'difficulty',
    width: 160,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '一星/入门', value: 1 },
      { text: '二星/基础', value: 2 },
      { text: '三星/中阶', value: 3 },
      { text: '四星/高阶', value: 4 },
      { text: '五星/专家', value: 5 },
    ],
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    width: 100,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '本人', value: 1 },
      { text: '其他', value: 0 },
    ],
  },
  actionColumn,
]
const tableColumns = ref([
  {
    title: '题型',
    dataIndex: 'type',
    key: 'types',
    width: 100,
    ellipsis: false,
    // filters: [
    //   { text: '单选题', value: 0 },
    //   { text: '多选题', value: 1 },
    //   { text: '判断题', value: 2 },
    //   { text: '填空题', value: 5 },
    //   { text: '问答题', value: 3 },
    //   { text: '算法题', value: 4 },
    //   { text: '排序题', value: 6 },
    // ],
    resizable: true,
  },
  {
    title: '题干',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
    resizable: true,
    width: 500,
  },
  proofreadingColumn,
  {
    title: '科目',
    dataIndex: 'bankPath',
    key: 'bankPath',
    width: 160,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '难度',
    dataIndex: 'difficulty',
    key: 'difficulty',
    width: 160,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '一星/入门', value: 1 },
      { text: '二星/基础', value: 2 },
      { text: '三星/中阶', value: 3 },
      { text: '四星/高阶', value: 4 },
      { text: '五星/专家', value: 5 },
    ],
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    width: 100,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '本人', value: 1 },
      { text: '其他', value: 0 },
    ],
  },
  actionColumn,
])
// 如果传入默认题型，则不展示题型过滤下拉框
if (props.defaultType !== null) 
  Reflect.deleteProperty(tableColumns.value[0], 'filters')
// 根据是否官方科目和是否可写控制操作栏显隐
watch([() => props.isOfficialSubject, () => props.writable], ([isOfficialSubject, writable]) => {
  const officialCert = store.getters.userInfo.officialCert
  const hideAction = !officialCert && isOfficialSubject
  const index = tableColumns.value.findIndex(item => item.key === 'action')
  if (writable && !hideAction) {
    if (index === -1) 
      tableColumns.value.push(actionColumn)
  }
  else {
    if (index !== -1) 
      tableColumns.value.splice(index, 1)
  }
  // 官方科目题目不展示校对结果列
  const proofreadIndex = tableColumns.value.findIndex(item => item.key === 'proofreading')
  if (!isOfficialSubject && !officialCert || isOfficialSubject && officialCert) { // 同或运算
    if (proofreadIndex === -1) {
      const authorIndex = tableColumns.value.findIndex(item => item.title === '题干')
      // tableColumns.value.splice(authorIndex + 1, 0, proofreadingColumn) // 添加到在题干后面
    }
  }
  else {
    if (proofreadIndex !== -1) 
      tableColumns.value.splice(proofreadIndex, 1)
  }
}, { immediate: true })

const paginationConfig = reactive({
  current: props.current,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total: number) => `总条数：${total}`,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small',
})

// 获取已排除列表长度
const excludedListLength = ref(0)
async function getExcludedListLength() {
  excludedListLength.value = props.defaultExcludedRowKeys.length
  if (props.defaultExcludedRowKeys.length) {
    const params: any = {
      // action: 'query',
      tagList: selectedTags.value,
      basePager:{
        current: paginationConfig.current,
        size: paginationConfig.pageSize,
      },
      body: searchContent.value,
      categoryId: props.subjectId || null,
      ids: props.defaultExcludedRowKeys,
      ...otherParams.value,
    }
    params.typeList = params.types
    params.proofreadingList = params.proofreading
    // params.notQuestionBankUUIDList = params.notids
    // params.questionBankUUIDList = params.ids
    // delete params.ids
    // delete params.notids
    delete params.proofreading
    const res = (await queryQuestion(params)) as any
    excludedListLength.value = res.total
  }
}
// getExcludedListLength()

// 表格数据
const list = ref<any[]>([])
const loading = ref(false)
const route = useRoute()

const generate = ref(false)
let inttervalId = null
const getProgress = async () => {
  if(!route.query.id) return
  const res = await getBankProgress({
    id:route.query.id
  })
  if(res && !res.status) {
    generate.value = true
    await getList()
  }else{
    generate.value = false
    clearInterval(inttervalId)
  }
}
inttervalId = setInterval(() => getProgress(),1000)

onUnmounted(() => {
  clearInterval(inttervalId)
})

async function getList(receiveParams) {
  const rootPath = route.query.path
  try {
    let bankPath = `${props.subjectPath}${includeSubCatg.value && props.selectItem.children && props.selectItem.children.length ? '*' : ''}` 

    // if(includeSubCatg.value){
    //   bankPath = `,${props.selectItem.path},${bankPath},`
    // }
    const params = {  
      entity: {
        bankPath: bankPath,
      },
      pageNo: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    }
    if(receiveParams) {
      params.entity = Object.assign(params.entity,receiveParams.entity)
      params.params = receiveParams.params
    }
    const res = (await queryQuestion(params)) as any
    list.value = res.records
    paginationConfig.total = res.total
  }
  finally {
    loading.value = false
  }
}

// 已选列表
const selectedRows = ref<any[]>([...props.defaultSelectedRows])
const selectedRowKeys = computed(() => selectedRows.value.map(i => i.id))

function updateSelectedRowKeys(record: any) {
  const key = record.id
  const index = selectedRowKeys.value.findIndex(i => i === key)
  if (index === -1) {
    selectedRows.value.push(record)
    emits('select', record, true)
  }
  else {
    selectedRows.value.splice(index, 1)
    emits('select', record, false)
  }
}
function onSelectAll(selected: boolean, selectedRows: any, changeRows: any[]) {
  return changeRows.forEach(updateSelectedRowKeys)
}

const getType = (type: number) => QuestionEnum[type]

function formatAccuracy(accuracy: any) {
  if (accuracy === null) {
    return '-'
  }
  else {
    return `${Math.floor(accuracy * 100)}%`
  }
}

// 新建题目
const questionAddFormVisible = ref(false)
function createQuestion() {
  questionAddFormVisible.value = true
}
function onAddFormClose(needRefresh?: boolean) {
  // 如果需要刷新，则重新获取标签和列表数据
  if (needRefresh) {
    getList(); // 获取题目列表
  }
  
  clearFormState()
  // 隐藏新增题目表单
  questionAddFormVisible.value = false;
}

// 查询题目ids关联的（未开始考试）试卷
function getPapersWithQids(ids: string[]) {
  const h = createVNode;
  return new Promise((resolve, reject) => {
    quesionDeleteBatch({ ids , confirm: false })
      .then((res: any) => {
        const showFlag = res.results.flatMap(item => item.warnings)

        if (showFlag.length) {
          // 如果有试卷关联，弹出确认框
          Modal.confirm({
            title: '删除题目风险提醒',
            content: h('div', {}, [
              h('p', {}, '删除当前题目后，会影响以下考试：'),
              ...res.results.map((item: any) =>
                h('div', { style: 'margin-top: 6px' }, item.warnings.map((warn) => h('p',{style: 'margin-top: 6px' }, warn.title)))
              ),
              h('p', { style: 'margin-top: 6px' }, '确认删除吗？'),
            ]),
            icon: () => h(InfoCircleFilled),
            onOk() {
              resolve(res); // 确认删除
            },
            onCancel() {
              reject(); // 取消删除
            },
          });
        } else {
          resolve(res); // 没有关联试卷，直接通过
        }
      })
      .catch((error) => {
        reject(error); // 捕获错误
      });
  });
}

// 删除题目
async function deleteQuestion(record: QuestionModel) {
  try {
    // await getPapersWithQids([record.id!])
    await quesionDelete({ ids: [record.id] })
    message.success('删除成功!')
    getList()
  }
  catch (error) {
    
  }
}

// 批量删除题目
function deleteMultiQuestion() {
  if (!selectedRows.value.length) 
    return message.error('请勾选要删除的题目')
  Modal.confirm({
    title: () => `确定删除勾选的${selectedRows.value.length}个题目?`,
    icon: () => createVNode(ExclamationCircleFilled),
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await getPapersWithQids(selectedRowKeys.value)
        await quesionDeleteBatch({
          ids: selectedRowKeys.value,
          confirm:true
        })
        message.success('批量删除成功!')
        selectedRows.value = []
        await getAllTags()
        getList()
      }
      catch (error) {
        
      }
    },
    onCancel() {},
  })
}

// 编辑题目
const qid = ref('')
const questionEditFormVisible = ref(false)
function editQuestion(record) {
  qid.value = record.id
  questionEditFormVisible.value = true
}
function onEditFormClose(needRefresh?: boolean) {
  if (needRefresh) {
    getList()
  }
  clearFormState()
  questionEditFormVisible.value = false
}

// 题目校对
const questionProofreadFormVisible = ref(false)
function proofreadQuestion(record: any) {
  qid.value = record.id
  questionProofreadFormVisible.value = true
}

// 视频预览
const previewVideoVisible = ref(false);
const previewVideoTaskId = ref('');
const previewItem = ref(null)
function showPreviewVideo(record) {
  previewVideoTaskId.value = record.taskId; // 假设 record 中有 taskId 字段
  previewItem.value = record;
  previewVideoVisible.value = true;
}

function onPreviewVideoClose() {
  previewVideoVisible.value = false;
  previewItem.value = null;
  previewVideoTaskId.value = '';
}

// 监听切换科目
const debounceTimer = ref<any>(null)
watch(
  () => props.subjectPath,
  () => {
    paginationConfig.current = 1
    if (debounceTimer.value) 
      clearTimeout(debounceTimer.value)
    debounceTimer.value = setTimeout(async () => {
      console.log('切换科目',props.subjectPath)
      tableColumns.value = [...rawColumsData]
      selectedRows.value = [...props.defaultSelectedRows]
      getList()
    }, 200)
  },
)

// 获取上一题/下一题并高亮展示
const currentQIndex = computed(() => list.value.findIndex(item => item.id === qid.value))
const lastBtnDisabled = computed(() => (paginationConfig.current === 1) && (currentQIndex.value === 0))
const nextBtnDisabled = computed(() => (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) && (currentQIndex.value === list.value.length - 1))
async function getSiblingQuestion(direction: 'last' | 'next') {
  if (!qid.value) 
    return
  const len = list.value.length
  const index = currentQIndex.value
  let newQid = qid.value
  let newIndex = index

  if (direction === 'last') {
    if (index !== 0) {
      newIndex = index - 1
    }
    else {
      // 获取上一页最后一条
      if (paginationConfig.current === 1) {
        throw new Error('没有上一题了')
      }
      else {
        paginationConfig.current -= 1
        await getList()
        newIndex = list.value.length - 1
      }
    }
  }
  else {
    if (index !== len - 1) {
      newIndex = index + 1
    }
    else {
      // 获取下一页第一条
      if (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) {
        throw new Error('没有下一题了')
      }
      else {
        paginationConfig.current += 1
        await getList()
        newIndex = 0
      }
    }
  }
  try {
    newQid = list.value[newIndex].id
  }
  catch (error) {
    throw new Error('题目列表为空')
  }
  nextTick(() => highLightRowByIndex(newIndex))
  return newQid
}

// 根据index将某一行高亮
function highLightRowByIndex(index: number) {
  const rows = document.querySelectorAll('.questions-table .ant-table-tbody tr.ant-table-row')
  rows.forEach((ele, i) => {
    if (i === index) {
      ele.classList.add('active')
    }
    else {
      ele.classList.remove('active')
    }
  })
}

// getList()
emitter.on('updateQuestionList', getList) // 其他菜单（回收站、草稿箱、ai批量出题）可能会对当前科目的题目进行更新

// ——————————————————————————————————————————————ai校对——————————————————————————————————————————————————————
const aiProofreadLoading = ref(false)
const taskVisible = ref(false) // 进度条弹层显隐
const taskId = ref('')
const proofreadingTaskRef = ref<InstanceType<typeof ProofreadingTask>>()
const aiProofreadingConfirmVisible = ref(false) // 批量校对确认框显隐控制

// ai校对按钮的点击事件
async function handleAiProofreadingBtnClick(e: Event) {
  if (aiProofreadLoading.value || proofreadingTaskRef.value?.isActive) {
    // 如果AI校对按钮正在转圈则展示信息
    const taskIds = await getaiproofreadingtaskids({})
    if (taskIds.includes(taskId.value) && taskIds[0] === taskId.value) {
      taskVisible.value = true
    }
    else {
      message.warning('排队处理中')
    }
  }
}

// 创建一个ai校对任务
async function handleAiproofreading() {
  // if(props.selectItem.path.split('/').filter(item => item).length <= 2) {
  //   aiProofreadLoading.value = false
  //   aiProofreadingConfirmVisible.value = false
  //   return message.error('请选择科目')
  // }
  // const taskIds = await addAiCheckByQPathApi({
  //   path: props.selectItem.path,
  // })
  // if (taskIds && taskIds.length) 
  //   message.info('当前部门已有AI校对任务，正在排队等候处理')
  try {
    aiProofreadLoading.value = true
    taskId.value = await addAiCheckByQPathApi({
      path: props.selectItem.path, // bab1072e-cf74-4088-bb7e-81cd8a789c88
    })
    console.log('taskId', taskId.value)
    // if (!taskIds.length) 
      taskVisible.value = true // 只有不需要排队立即执行的任务才展示进度条
  }
  catch (error) {
    console.log(error)
  }
  finally {
    aiProofreadLoading.value = false
    aiProofreadingConfirmVisible.value = false
  }
}

// 查看ai批量校对的结果
const taskFinishedModalVisible = ref(false)
const taskFinishDetail = ref<any>()
function showAiProofreadResult(result: any) {
  taskFinishDetail.value = result
  taskFinishedModalVisible.value = true
}

onDeactivated(() => {
  questionAddFormVisible.value = false
  questionEditFormVisible.value = false
  questionProofreadFormVisible.value = false
})

defineExpose({
  selectedRows,
  layoutTags,
})
</script>

<template>
  <div class="question-manage">
    <div class="question-manage-header">
      <div class="header-left">
        <a-input-search
          v-model:value.trim="searchContent"
          placeholder="请输入题干"
          allow-clear
          @search="getFirstPageList"
        />
        <div style="display: flex;align-items: center;">
          <a-checkbox v-model:checked="includeSubCatg" @change="getFirstPageList">
            包含子科目试题
          </a-checkbox>
          <a-tooltip placement="right" overlay-class-name="light">
            <template #title>
              <span>勾选后，题目列表包含所选科目及其子科目的题目</span>
            </template>
            <svg-icon class="common-info-icon" name="info2" />
          </a-tooltip>
          <div class="w-[200px] ml-[30px] flex flex-nowrap items-center" v-if="generate">
            <img src="../questionManage/assets/spin.svg" class="mr-[12px]  animate-spin">
            题目正在生成中
          </div>
        </div>

        <a-radio-group
          v-if="!writable"
          v-model:value="listType"
          size="large"
          @change="getFirstPageList"
        >
          <a-radio v-for="item in listTypeEnum" :value="item">
            <div style="display: flex; align-items: center; white-space: nowrap;">
              <span>{{ item }}</span>
              <span v-if="item !== '可选'" style="padding-left: 2px; line-height: 1.8">
                (<a-badge
                  :count="item === '已选' ? selectedRowKeys.length : defaultExcludedRowKeys.length"
                  :overflow-count="999"
                  :number-style="{
                    backgroundColor: '#fff',
                    color: '#5478EE',
                    padding: '0',
                    fontSize: '14px',
                    display: 'inline-block',
                  }"
                  :show-zero="true"
                />)
              </span>
            </div>
          </a-radio>
        </a-radio-group>
      </div>
      
      <div
        class="header-right"
        v-if="operationFlag"
      >
        <a-button type="primary" @click="createQuestion">
          新增题目
        </a-button>
        <!-- <div style="margin-left: 2px;" @click="$router.push(`/admin/subjectManage/question-generator?categoryId=${subjectId}`)">
          <a-button class="common-ai-button" style="border-radius: 8px;">
            <template #icon>
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
            </template>
            批量出题
          </a-button>
        </div> -->
        <a-popconfirm
          v-model:visible="aiProofreadingConfirmVisible"
          title="确定对当前科目及其子科目进行AI校对吗？"
          ok-text="确定"
          cancel-text="取消"
          placement="bottom"
          :disabled="aiProofreadLoading || proofreadingTaskRef?.isActive"
          @confirm="handleAiproofreading"
          @cancel="aiProofreadingConfirmVisible = false"
        >
          <div style="margin-left: 2px;" @click="handleAiProofreadingBtnClick">
            <!-- 这里必须用div包裹，不然loading状态的a-btn不会响应点击 -->
            <a-button :loading="aiProofreadLoading || proofreadingTaskRef?.isActive" class="common-ai-button" style="border-radius: 8px;">
              <template #icon>
                <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
              </template>
              校对
            </a-button>
          </div>
        </a-popconfirm>
        <a-button @click="deleteMultiQuestion">
          批量删除
        </a-button>
      </div>
    </div>
    <!-- <div v-show="tagsList.length && includeSubCatg" ref="allTagsContainerRef" class="all-tags-container">
      <span
        v-if="tagsList.length && tagBtnState !== '隐藏'"
        class="more-btn"
        @click="handleTagBtnStateChange"
      >{{ tagBtnState === '更多' ? '收起' : '更多' }} <svg-icon name="uparrow" class="arrow-icon" /></span>
      <template v-for="(item, index) in tagsList" :key="item.id">
        <span
          class="item"
          :class="{ active: activeTagIds.includes(item.id) }"
          @click="handleClickTag(item.id)"
        >{{ item.name }}</span>
      </template>
    </div> -->
    <a-table
      class="questions-table common-table"
      sticky
      :key="subjectPath"
      :columns="tableColumns"
      :row-key="(record: any) => record.id"
      :data-source="list"
      :row-selection="{
        selectedRowKeys,
        onSelect: updateSelectedRowKeys,
        onSelectAll,
        getCheckboxProps: (record: any) => ({ disabled: disabledRowKeys.includes(record.id) || (isOfficialSubject && !store.getters.userInfo.officialCert) }),
      }"
      :loading="loading"
      :scroll="{ x: 1200, y: 'calc(98% - 20px)' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      @resize-column="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'body'">
          <div style="display: flex;align-items: start;">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>{{
                  record.complicatedediting ? record.body?.slice(0, 100) : record.body
                }}</template>
                <span class="question-content" v-html="getQuestionContentByRecord(record)" />
              </a-tooltip>
            </span>
            <span v-if="record.recover && writable" class="recover-tag">已恢复</span>
            <span v-if="defaultExcludedRowKeys.includes(record.id)" class="exclude-tag">已排除</span>
          </div>
        </template>
        <template v-else-if="column.key === 'types'">
          <span style="font-family: PingFang-SC-Regular;">{{ getType(record.type) || '问答题' }}</span>
        </template>
        <template v-else-if="column.key === 'accuracy'">
          <span style="font-family: PingFang-SC-Regular;">
            {{ formatAccuracy(record.accuracy) }}
          </span>
        </template>
        <template v-else-if="column.key === 'bankPath'">
          <span>
            <!-- 去 ‘/’ 分割最后一级 -->
            {{ record.bankPath.split('/').pop() }}
          </span>
        </template>
        <template v-else-if="column.key === 'difficulty'">
          <StarFilled v-for="item in record.difficulty" style="color:#FAAD14"/>
          <!-- <span v-if="record.difficulty >= 87">
            <StarFilled v-for="item in 5" style="color:#FAAD14"/>
          </span>
          <span v-else-if="record.difficulty >= 74">
            <StarFilled v-for="item in 4" style="color:#FAAD14"/>
          </span>
          <span v-else-if="record.difficulty >= 61">
            <StarFilled v-for="item in 3" style="color:#FAAD14"/>
          </span>
          <span v-else-if="record.difficulty >= 48">
            <StarFilled v-for="item in 2" style="color:#FAAD14"/>
          </span>
          <span v-else-if="record.difficulty >= 35">
            <StarFilled v-for="item in 1" style="color:#FAAD14"/>
          </span>
          <span v-else>
            <StarFilled v-for="item in 1" style="color:#FAAD14"/>
          </span> -->
        </template>
        <template v-else-if="column.key === 'proofreading'">
          <div style="display: flex; align-items: center;">
            <span :class="[['status_gray', 'status_green', 'status_red'][record.check && (record.check.isCheck|| record.check.isAi) ? record.check.isCorrect ? 1 : 2 : 0]]" />
            <img v-if="record.check && record.check?.isAi" style="margin-right: 4px;" src="@/assets/icons/svg/ai.svg" alt="">
            <span style="font-family: PingFang-SC-Regular;">{{ ['未校对', '校对正确', '校对错误'][record.check && (record.check.isCheck|| record.check.isAi) ? record.check.isCorrect ? 1 : 2 : 0] }}</span>
          </div>
        </template> 
        <template v-else-if="column.key === 'action'">
          <span>
            <a-button type="text" size="small" @click="showPreviewVideo(record)" :disabled="!record.videoUrl">
              <span :style="{ color: record.videoUrl ?'#5478EE' : '#b3b1b1' }">视频预览</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="proofreadQuestion(record)">
              <span style="color:#5478EE">校对</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="editQuestion(record)" >
              <span style="color:#5478EE">编辑</span>
            </a-button>
            
            <a-divider type="vertical" />
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <a-popconfirm
                title="确定删除该题目？"
                ok-text="确定"
                cancel-text="取消"
                placement="left"
                :disabled="!store.getters.userInfo.officialCert && record.official_cert"
                @confirm="() => { deleteQuestion(record) }"
              >
                <a-button type="text" size="small">
                  <span style="color:#5478EE">删除</span>
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded-row">
          <QuestionItemDisplay
            :question-detail="record"
            show-score
            show-type
            show-tag
            show-correct-answer
            show-question-difficulty
            show-question-points-and-basis
            show-question-proofreading-record
            option-letter-type="text"
          />
        </div>
      </template>
    </a-table>
  </div>

    <a-modal v-model:visible="questionAddFormVisible" title="新增题目" wrap-class-name="full-screen-modal" width="100%" :mask-closable="false" :closable="false" :keyboard="false" :footer="null" destroy-on-close>
     <QuestionAddForm type="create" v-if="questionAddFormVisible" :selectItem='selectItem' :subject-id="$props.subjectId" :subjectUUID="subjectUUID" @close="onAddFormClose" />
    </a-modal>

    <a-modal v-model:visible="questionEditFormVisible" title="编辑题目" wrap-class-name="full-screen-modal" width="100%" :mask-closable="false" :closable="false" :keyboard="false" :footer="null" destroy-on-close>
      <!-- <QuestionEditForm v-if="questionEditFormVisible" :qid="qid" :can-convert="true" @close="onEditFormClose" /> -->
      <QuestionAddForm type="edit" v-if="questionEditFormVisible" :qid="qid" :subject-id="$props.subjectId" :subjectUUID="subjectUUID" @close="onEditFormClose" />
    </a-modal>

    <a-modal v-model:visible="questionProofreadFormVisible" title="题目校对" wrap-class-name="full-screen-modal proofread-modal" width="100%" :mask-closable="false" :keyboard="false" :footer="null" @cancel="getList">
      <QuestionProofreadForm v-if="questionProofreadFormVisible" v-model:qid="qid" :get-sibling-question-fn="getSiblingQuestion" :last-btn-disabled="lastBtnDisabled" :next-btn-disabled="nextBtnDisabled" @close="(questionProofreadFormVisible = false), getList()" />
    </a-modal>

    <a-modal v-model:visible="taskVisible" title="AI校对" :footer="null">
      <ProofreadingTask ref="proofreadingTaskRef" :task-id="taskId" @close="taskVisible = false" @finish="getList" @show-result="showAiProofreadResult" />
    </a-modal>

    <a-modal v-model:visible="previewVideoVisible" title="视频预览" :footer="null" wrap-class-name="full-screen-modal preview-video-modal" destroy-on-close>
      <PreviewVideo :previewItem="previewItem"/>
    </a-modal>

    <a-modal v-model:visible="taskFinishedModalVisible" :footer="null" :width="300" :z-index="9999">
      <template #title>
        <CheckCircleFilled style="font-size: 20px; color: #52c41a;" />
        <span style="margin-left: 8px;">AI校对完成</span>
      </template>
      <ProofreadingTaskFinished :data="taskFinishDetail" @close="taskFinishedModalVisible = false" />
    </a-modal>
</template>

<style lang="less">
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.ques-drawer {
  .ant-drawer-title {
    font-weight: bold;
  }
}

.proofread-modal {
  .ant-modal-header {
    padding: 24px;
    padding-top: 0;
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-close{
    .ant-modal-close-x{
        line-height: 24px;
    }
  }
}
</style>

<style lang="less" scoped>
.question-manage {
  height: 100%;
  overflow: auto;
  padding: 0 10px 0 16px;
  display: flex;
  flex-direction: column;
}
.question-manage-header {
  margin: 0px 0;
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;

    :deep(.ant-checkbox-wrapper) {
      margin-left: 16px;
      span {
        font-size: 14px;
        white-space: nowrap;
      }
    }

    .ant-radio-group {
      margin-left: 24px;
      display: flex;
      align-items: center;

      :deep(span) {
        font-size: 14px;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    .ant-btn {
      border-radius: 8px;
      margin-left: 8px;
      font-size: 14px;
    }
  }
}

.recover-tag {
  font-size: 12px !important;
  color: #2f8c00 !important;
  background-color: #f3f7f1;
  border-radius: 4px;
  padding: 2px 4px;
}

.exclude-tag {
  font-size: 12px;
  color: #d71310 !important;
  background-color: #fcf0f0;
  border-radius: 4px;
  padding: 2px 4px;
}

.all-tags-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding: 0 100px 0 0;
  height: v-bind(tagsHeight);
  overflow: hidden;

  .more-btn {
    cursor: pointer;
    position: absolute;
    top: 3px;
    right: 16px;
    color: #626262;
    font-size: 12px;

    .arrow-icon {
      transform: v-bind(tagsRotate);
    }
  }

  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  }

  .active {
    background: #5478ee;
    color: #fff;
  }
}

.table-bar {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-checkbox-wrapper {
    font-size: 15px;
  }

  .show-columns {
    font-size: 15px;

    span {
      margin-right: 10px;
    }
  }
}

:deep(.ant-table) {
  .ant-table-expanded-row td:nth-child(1) {
    box-shadow: 2px 0px 0px 0px #5478ee inset;
  }
}

.expanded-row {
  padding: 16px;
}
.tooltip-style {
  display: block;
  // width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left !important;
  margin-right: 8px;
}

:deep(.question-content) {
  font-family: PingFang-SC-Regular !important;
  img {
    height: 50px;
    width: auto;
    cursor: initial!important;
    display: inline-block!important;
    float: none!important;
  }
  p {
    display: inline-block;
  }
}
.full-screen-modal{
  .ant-modal-body{
    .create-question-container
    :deep(.close-btn){
      line-height: 38px;
    }
  }
}
.preview-video-modal{
  :deep(.ant-modal-body){
    // background: red;
  }
  :deeo(.ant-modal-content){
    padding: 0;
  }
}
</style>

<style lang="less">
.preview-video-modal{
  .ant-modal-body {
    background: #E9F4FB
  }
  .ant-modal-header{
    background: #E9F4FB;
    margin-bottom: 0;
    padding: 24px;
  }
  .ant-modal-content{
    padding: 0;
  }
  .ant-modal-close-x {
    height: '';
    width: 22px!important;
    line-height: 22px!important;
  }
}
</style>
