import { watch } from 'vue'
export default (formState: any) => {
  watch(
    () => formState.value.parameters,
    (val, old) => {
      if(!val) return
      const paramNameList = val.map((item: any) => item.name)
      formState.value.btestcase.forEach((item: any) => {
        if (paramNameList.length === 0) {
          item.input = []
          item.output.length = 0
        }
        if (!item.input.length || JSON.stringify(item.input) === '[{"":""}]') {
          item.input = []
          paramNameList.forEach((name: any) => {
            item.input.push({ [name]: '' })
          })
        } else {
          paramNameList.forEach((name: any, index: number) => {
            if (item.input.length > paramNameList.length) {
              item.input.pop()
            } else if (item.input.length < paramNameList.length) {
              item.input.push({ [name]: '' })
            } else {
              item.input[index] = { [name]: Object.values(item.input[index])[0] }
            }
          })
        }

        if (
          !item.output.length ||
          JSON.stringify(item.output) === '[{"type":"value","value":""}]'
        ) {
          item.output.length = 0
          item.output.push({ type: 'value', value: '' })
        }
      })

      formState.value.ptestcase.forEach((item: any) => {
        if (paramNameList.length === 0) {
          item.input = []
          item.output.length = 0
        }
        if (!item.input.length || JSON.stringify(item.input) === '[{"":""}]') {
          item.input = []
          paramNameList.forEach((name: any) => {
            item.input.push({ [name]: '' })
          })
        } else {
          paramNameList.forEach((name: any, index: number) => {
            if (item.input.length > paramNameList.length) {
              item.input.pop()
            } else if (item.input.length < paramNameList.length) {
              item.input.push({ [name]: '' })
            } else {
              item.input[index] = { [name]: Object.values(item.input[index])[0] }
            }
          })
        }

        if (
          !item.output.length ||
          JSON.stringify(item.output) === '[{"type":"value","value":""}]'
        ) {
          item.output.length = 0
          item.output.push({ type: 'value', value: '' })
        }
      })
    },
    { immediate: true, deep: true }
  )
}
