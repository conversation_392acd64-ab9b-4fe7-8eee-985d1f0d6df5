<template>
  <a-modal
    class="joinmark-paper-modal"
    v-model:visible="visible"
    title="联合阅卷"
    width="550px"
    :footer="null"
    :centered="true"
    :maskClosable="false"
    @cancel="close"
  >
    <div style="font-size: 12px;color: #626262;margin-bottom: 20px;">
      <span style="margin-right: 20px;">考试名称</span>
      <span>{{ papername }}</span>
    </div>
    <div>
      <a-input-search
        style="width: 240px;"
        v-model:value.trim="searchContent"
        placeholder="请输入教师名称"
        allow-clear
        @search="getSearchData"
      />
    </div>
    <a-table
      class="paper-table"
      :columns="columns"
      :row-key="(record:any) => record.id"
      :data-source="teacherList"
      :row-selection="{
        selectedRowKeys: selectedTeacher,
        onChange: onSelectChange,
        getCheckboxProps: handleGetCheckboxProps
      }"
      :loading="questionLoading"
      :locale="{ filterConfirm: '确定', filterReset: '重置', emptyText: '暂无数据' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      :scroll="{ x: 400 }"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <span>{{ record.id === teacher ? record.name + '（创建人）' : record.name }}</span>
        </template>
      </template>
    </a-table>
    <div class="btn-group" v-if="!props.readonly">
      <a-button type="primary" :loading="loading" @click="addJoinTeacher">确认</a-button>
      <a-button @click="close">取消</a-button>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { allteacher, joinmarking, addjoinmarking } from '@/api/admin/paperManage'
import { message } from 'ant-design-vue'

const props = withDefaults(defineProps<{
  id: string
  teacher: string
  joinMarkModalVisible: boolean
  papername: string
  readonly?: boolean
}>(), {
  id: '',
  joinMarkModalVisible: false,
  papername: '',
  readonly: false
})

const emits = defineEmits(['closeModal'])

const searchContent = ref('')
const getSearchData = () => {
  queryJoinTeacher()
}
const columns = ref([
  {
    title: '教师名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: '所属部门',
    dataIndex: 'dept_name',
    key: 'dept_name',
    align: 'left',
    width: 200,
    ellipsis: true,
    resizable: true
  }
])
// 分页配置
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})
const teacherList = ref([])
const questionLoading = ref(false)

const selectedTeacher = ref<any>([])
const onSelectChange = (selectedRowKeys: (string | number)[]) => {
  selectedTeacher.value.length = 0
  selectedTeacher.value.push(...selectedRowKeys)
}

const handleTableChange = (pagination: any, filters: any = {}, sort: any = {}) => {
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  allteacher({ 
    basePager:{
      current: paginationConfig.value.current,
        ize: paginationConfig.value.pageSize
    },
    username: searchContent.value 
  }).then((res: any) => {
    teacherList.value = res.records
    paginationConfig.value.total = res.total
  })
}

const close = () => {
  paginationConfig.value.current = 1
  emits('closeModal')
}
const handleGetCheckboxProps = (payload: any) => {
  return {
    selectedRowKeys: selectedTeacher.value.includes(payload.id),
    disabled: props.readonly || props.teacher === payload.id,
  }
}

const loading = ref(false)
const addJoinTeacher = async () => {
  if (!selectedTeacher.value.length) {
    message.error('请添加参与联合阅卷的教师')
    return
  }
  loading.value = true
  const params = {
    teacherUUIDList: selectedTeacher.value,
    templeteId: props.id,
    action: 'add'
  }
  try {
    await addjoinmarking(params)
    message.success('添加成功!')
    close()
  } finally {
    loading.value = false
  }
}
const queryJoinTeacher = () => {
  joinmarking({ action: 'query', templeteId: props.id })
    .then((res: any) => {
      selectedTeacher.value.length = 0
      selectedTeacher.value.push(...res.map((item: any) => item.id))
    })
    .then(() => {
      allteacher({ 
        basePager:{
          current: paginationConfig.value.current,
          size: paginationConfig.value.pageSize
        },
        username: searchContent.value 
      }).then((res: any) => {
        teacherList.value = res.records
        paginationConfig.value.total = res.total
      })
    })
}

const visible = ref(false)
watch(
  () => props.joinMarkModalVisible,
  (val) => {
    visible.value = val
    if (val) {
      queryJoinTeacher()
    }
  }
)

onMounted(() => {})
</script>

<style lang="less" scoped>
.joinmark-paper-modal {

  .btn-group {
    text-align: center;
    .ant-btn {
      width: 60px;
      height: 32px;
      font-size: 14px;
      margin-right: 8px;
      border-radius: 8px;
    }
  }
}

.paper-table {
  margin-top: 16px;
}
</style>
<style lang="less">
.joinmark-paper-modal .ant-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #121633;
}
.joinmark-paper-modal .ant-table-thead > tr > th {
  &:first-child {
    padding-left: 8px !important;
  }
  background: #f0f4fe !important;
  .ant-table-column-title {
    font-family: PingFangSC-Regular;
    font-size: 15px;
    font-weight: bold;
    color: #121633;
  }
}
</style>
