import { addQuestion, compoundques, draftques , deldraftques , draftEdit, draftCreate} from '@/api/admin/questionManage'
import { questionType } from '@/models/questionModel'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import { judge, lettersAlpht, options, sourceForm } from '../data'
import { v4 as uuidv4 } from 'uuid'
import { ref, Ref } from 'vue'
import { scrollToFirstErrFormItem } from '@/utils/common'
import { htmlAddSimulate } from '@/utils/index'

export default (
  formState: any,
  subQuestionList: any,
  createQuestionFormRef: any,
  isSubmit: any,
  scorepts: any,
  fillBlank: any,
  fillBlankCount: any,
  oldFillBlank: any,
  scoreInputTotalRef: any,
  funcTemplate: () => any,
  disabledKeywords: Ref<boolean>,
  activeTab: Ref<string>,
  props: any,
  emits: any
) => {
  const loading = ref(false)
  const addLoading = ref(false)
  // 格式化答案
  const formatQuestionAnswer = (type: number, formState: any, isCompound = false) => {
    // 如果不是复杂编辑，则将复杂编辑的题干字段清空，以便后端判断是否应该清空校对状态
    if (!formState.complicatedediting) formState.complexcontent = ''
    // 如果算法题不存在禁用关键词，则将禁用关键词清空
    if (!disabledKeywords.value) Object.keys(formState.disablekws).forEach(key => formState.disablekws[key] = [])
    switch (type) {
      case questionType.SINGLE_CHOICE:
        if (formState.answer === '') {
          message.error('请选择正确答案')
          return true
        }

        formState.options.forEach((item: any, index: number) => {
          item.value = lettersAlpht[index]
        })

        break

      case questionType.MULTIPLE_CHOICE:
        if (formState.answer.length < 2) {
          message.error('多选题至少勾选2个正确答案')
          return true
        }
        formState.options.forEach((item: any, index: number) => {
          item.value = lettersAlpht[index]
        })
        formState.answer = Array.isArray(formState.answer)
          ? formState.answer.join('')
          : formState.answer
        break

      case questionType.TRUE_OR_FALSE:
        formState.options = JSON.parse(JSON.stringify(judge))
        if (formState.answer === '') {
          message.error('请选择正确答案')
          return true
        }
        break

      case questionType.FILL_BLOCK:
        if (isCompound) {
          formState.answer = formState.fillBlank
        } else {
          formState.answer = fillBlank.value
        }
        if (formState.answer.length === 0 || formState.answer.some((item: any) => !item.keyword)) {
          message.error('请添加填空题答案')
          return true
        }
        if (formState.sepscore && formState.answer.some((item: any) => item.score == 0)) {
          message.error('请添加填空题分值')
          return true
        }
        formState.answer = Array.isArray(formState.answer)
          ? JSON.stringify(formState.answer)
          : formState.answer
        break

      case questionType.SHORT_ANSWER:
        if (!isCompound) {
          formState.answer = JSON.stringify(scorepts.value)
        }
        // 校验keyword是否为空
        const answerArr = formState.answer
          ? JSON.parse(formState.answer)
          : [{ keyword: [], score: 0 }]
        if (!answerArr.length) {
          message.error('请添加得分点')
          return true
        }
        if (formState.sepscore && answerArr.some((item: any) => !item.keyword.length)) {
          message.error('请添加得分点内容')
          return true
        }
        break

      case questionType.AlGORITHM_CODING:
        break

      case questionType.SORT_ORDER:
        formState.options.forEach((item: any, index: number) => {
          item.value = lettersAlpht[index]
        })
        formState.answer = JSON.stringify(formState.options.map((item: any) => item.value))
        if (formState.answer === '') {
          message.error('请添加排序题答案')
          return true
        }
        break

      case questionType.COMPOSITE:
        if (!subQuestionList.value.length) {
          message.error('请添加子试题')
          return true
        } else {
          for (let i = 0; i < subQuestionList.value.length; i++) {
            if (!subQuestionList.value[i].formState.body) {
              message.error('请输入题干')
              return true
            }

            if (
              subQuestionList.value[i].type == '0' ||
              subQuestionList.value[i].type == '1' ||
              subQuestionList.value[i].type == '6'
            ) {
              let isDup = false
              const allContents: any = []
              for (let j = 0; j < subQuestionList.value[i].formState.options.length; j++) {
                if (allContents.includes(subQuestionList.value[i].formState.options[j].content)) {
                  isDup = true
                } else {
                  allContents.push(subQuestionList.value[i].formState.options[j].content)
                }
                if (!subQuestionList.value[i].formState.options[j].content) {
                  message.error(`请输入第${i + 1}个子题的第${j + 1}个选项`)
                  return true
                }
              }
              if (isDup) {
                message.error('选项内容不能相同')
                return true
              }
            }
          }
        }

        // return subQuestionList.value.some((item: any) => {
        //   return formatQuestionAnswer(Number(item.type), item.formState, true)
        // })
    }
  }
  function convertFieldJsonContent(data, toJson = false) {
    const defaultKeys = ['options', 'disablekws', 'funcMain', 'funcTemplate', 'parameters', 'ptestcase', 'rtype', 'btestcase', 'validatecode']
    defaultKeys.forEach(key => {
      if (toJson) {
        if (data[key]) {
          const _temp = data[key]
          data[key] = JSON.stringify(_temp)
          data[`${key}Json`] = _temp
        }
      } else {
        if (data[`${key}Json`]) {
          data[`${key}`] = data[`${key}Json`]
        }
      }
    })
    data.func_name = data.funcName 
    data.func_templete = data.funcTemplete
  }
  function convertBoolean(data) {
    Object.keys(data).forEach(key => {
      if (typeof data[key] === 'boolean') {
        data[key] = data[key] ? 1 : 0
      }
    })
  }
  const submitQuestion = (validateCode?: Object, isSaveAdd = false, successCb: () => void) => {
    if (formState.value.type === 7) {
      if (isSaveAdd) {
        addLoading.value = true
      } else {
        loading.value = true
      }
      compoundques({
        action: 'add',
        question: {
          ...formState.value,
          validatecode: validateCode,
          children: subQuestionList.value.map((item: any) => {
            return {
              type: item.type,
              ...item.formState
            }
          })
        }
      })
        .then(() => {
          successCb()
          if (isSaveAdd) {
            addLoading.value = false
            // 编辑草稿发布
            if (props.draftId) {
              deldraftques({ action: 'del', ids: [props.draftId] }).then(() => {
                emits('close', true)
              })
            } else {
              resetQuestionForm()
            }
          } else {
            loading.value = false
            emits('close', true)
          }
          // isSubmit.value = true
          message.success('保存成功！')
        })
        .catch(() => {
          if (isSaveAdd) {
            addLoading.value = false
          } else {
            loading.value = false
          }
        })
      return
    } else {
      if (isSaveAdd) {
        addLoading.value = true
      } else {
        loading.value = true
      }
      const reg = /[(（]\s*[)）]/
      let complexcontent = formState.value.complexcontent
      if ([0, 1, 2, 6].includes(formState.value.type) && complexcontent && !reg.test(complexcontent)) {
        // 富文本中找到最后一个内容，在其后面加上( )
        complexcontent = htmlAddSimulate(complexcontent)
      }
      let body = formState.value.body
      console.log('formState.value.type', formState.value.type)
      if ([0, 1, 2, 6].includes(formState.value.type) && body && !reg.test(body)) {
        body = `${formState.value.body}( )`
      }
      const param: any = {
        action: 'add',
        question: {
          ...formState.value,
          body, // 题干
          complexcontent, // 富文本题干
          funcName: formState.value.func_name,
          validatecode: validateCode,
        }
      }

      convertFieldJsonContent(param.question,true)
      convertBoolean(param.question)
      param.question.categoryUUID = param.question.category
      param.question.tagList = param.question.tags
      delete param.question.tags  
      param.question.subtopic = param.question.subtopic && (typeof param.question.subtopic == 'object' || Array.isArray(param.question.subtopic)) ? param.question.subtopic.toString() : param.question.subtopic
      delete param.question.categoryId
      addQuestion({questionList:[{...param.question}]})
        .then(() => {
          successCb()
          if (isSaveAdd) {
            addLoading.value = false

            // 编辑草稿发布
            if (props.draftId) {
              deldraftques({ action: 'del', ids: [props.draftId] }).then(() => {
                emits('close', true)
              })
            } else {
              resetQuestionForm()
            }
          } else {
            loading.value = false
            emits('close', true)
          }
          // isSubmit.value = true
          message.success('保存成功！')
        })
        .catch(() => {
          if (isSaveAdd) {
            addLoading.value = false
          } else {
            loading.value = false
          }
        })
    }
  }

  const store = useStore()
  const resetQuestionForm = () => {
    const savedFormState = {
      type: formState.value.type,
      category: formState.value.category,
      score: formState.value.score,
      tags: formState.value.tags
    }
    formState.value.answer = ''
    // createQuestionFormRef.value.resetFields()
    formState.value = {}
    Object.assign(formState.value, sourceForm, savedFormState)
    formState.value.official_cert = store.getters.userInfo.is_official_cert
    fillBlank.value = []
    fillBlankCount.value = 0
    oldFillBlank.value = ''
    scorepts.value = [{ keyword: [], score: 0 }]
    subQuestionList.value.length = 0
    if (formState.value.type === 2) {
      formState.value.options = JSON.parse(JSON.stringify(judge))
    } else {
      formState.value.options = JSON.parse(JSON.stringify(options))
    }

    if (formState.value.type === 3 || formState.value.type === 5 || formState.value.type === 7) {
      scoreInputTotalRef.value.score = 0
    }

    // 若是算法题,需要清空编辑器内容
    if (formState.value.type === 4) {
      formState.value.parameters = [{ name: '', type: '', is_list: false }]
      formState.value.btestcase = [{ input: [], output: [] }]
      formState.value.ptestcase = [{ input: [], output: [] }]
      delete formState.value.options
      funcTemplate()
    }
    disabledKeywords.value = false
  }

  // 保存并新增
  const saveAdd = (cb: () => any, validateCodeObj: any, successCb: () => void) => {
    return createQuestionFormRef.value
      .validate()
      .then(async (res) => {
        await cb()
  
        isSubmit.value = false

        if (formatQuestionAnswer(formState.value.type as any, formState.value)) {
          addLoading.value = false
          return Promise.reject('题目格式错误')
        }
  
        // 算法题需要验证
        if (formState.value.type === 4) {
          validateCodeObj.value.currentStep = 0
          await validateCodeObj.value.autoValidateCode((validateCode: any) =>
            submitQuestion(validateCode, true, successCb)
          )
          addLoading.value = false
          return
        }

        else if (formState.value.type === 5 && formState.value.ignorecase) {
          const checkKeyWordDuplicates = (keyword: string[]) => {
            const set = new Set()
            for (let i = 0; i < keyword.length; i++) {
              if (set.has(keyword[i].toLowerCase())) 
                return true
              set.add(keyword[i].toLowerCase())
            }
            return false
          }
          if (formState.value.answer) {
            const answers = JSON.parse(formState.value.answer)
            const isDuplicate = answers.some((answer) => {
              const { keyword } = answer
              return checkKeyWordDuplicates(keyword)
            })  
            if (isDuplicate) {
              message.error('答案中存在重复的关键字,请手动移除或者移除勾选"忽略大小写"')
              addLoading.value = false
              return
            }
          }
        }

        submitQuestion(undefined, true, successCb)
      })
      .catch(async function (e) {
        addLoading.value = false
        if (activeTab.value === '2') {
          message.error('题目表单校验失败,请检查输入项内容')
        }
        await cb()
      })
      .finally(() => {
        scrollToFirstErrFormItem()
      })
  }

  const saveDraft = (validateCodeObj: any, successCb: () => void, isDraft = false) => {
    loading.value = true
    const { score, type, category } = formState.value
    formState.value.type === 3 && (formState.value.answer = JSON.stringify(scorepts.value))
    formState.value.type === 5 && (formState.value.answer = JSON.stringify(fillBlank.value))
    const params = {
      action: 'add',
      score,
      type,
      categoryUUID: category,
      title: formState.value.body,
      questionBankWrite:{...formState.value,  funcName: formState.value.func_name,  validatecode: getValidateCode(validateCodeObj)}
    }
    if (props.draftId) {
      params.id = props.draftId
    }
    convertFieldJsonContent(params.questionBankWrite,true)
    convertBoolean(params.questionBankWrite)
    params.questionBankWrite.categoryUUID = params.questionBankWrite.category
    params.questionBankWrite.tagList = params.questionBankWrite.tags
    if (params.questionBankWrite.subtopic) {
      params.questionBankWrite.subtopic = params.questionBankWrite.subtopic.toString()
    }
    if(params.questionBankWrite.answer && Array.isArray(params.questionBankWrite.answer)){
      params.questionBankWrite.answer = JSON.stringify(params.questionBankWrite.answer)
    }
    delete params.questionBankWrite.tags 
    const api = isDraft ? draftEdit : draftCreate
    api(params)
      .then(() => {
        successCb()
        loading.value = false
        if (props.draftId) {
          emits('close', true)
        } else {
          resetQuestionForm()
        }
        message.success('已成功保存至草稿箱')
      })
      .catch(() => {
        loading.value = false
      })
  }

  const getValidateCode = (validateCodeObj: any) => {
    if (validateCodeObj.value) {
      const validatecode = {}
      Object.keys(validateCodeObj.value.langMap).forEach((key) => {
        validatecode[validateCodeObj.value.langMap[key]['lang']] =
          validateCodeObj.value.langMap[key].src
      })
      return validatecode
    } else {
      return null
    }
  }
  return {
    loading,
    addLoading,
    saveAdd,
    saveDraft,
    submitQuestion
  }
}
