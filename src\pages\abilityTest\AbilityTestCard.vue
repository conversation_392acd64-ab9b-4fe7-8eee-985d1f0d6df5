<template>
    <div :class="['paper-card', props.checked ? 'checked' : '']">
        <div class="header">
            <div class="left">
                <SearchHighLight class="title" :text="paperInfo.name" :search-text="searchText"></SearchHighLight>
                <!-- <StarFilled class="focus-icon" :class="{ active: isFocus }" @click="handleFocus" /> -->
                <div class="status" :style="statusStyles[paperInfo.status]">{{ PaperStatusList.find(el => el.value ===
        paperInfo.status)?.label || paperInfo.status }}</div>
                <div class="operation">
                    <div :class="['btn-wrap']">
                        <img class="btn" src="@/assets/images/paper/edit.svg" title="编辑试卷" alt="" @click="handleEdit">
                    </div>
                    <div :class="['btn-wrap']">
                        <img class="btn" src="@/assets/images/paper/delete.svg" title="删除试卷" alt=""
                            @click="handleDeletePaper">
                    </div>
                    <!-- <div class="btn-wrap">
                        <img class="btn" title="克隆试卷" src="@/assets/images/paper/copy.png" alt="" @click="handleCopy">
                    </div> -->
                    <!-- <div :class="['btn-wrap', props.paperInfo.is_self ? '' : 'disabled']" @click="handleSharePaper">
                        <img title="共享试卷" class="btn" v-if="props.paperInfo.is_self && isShared" width="14"
                            src="@/assets/icons/svg/shared.svg" alt="">
                        <share-alt-outlined v-else class="btn" title="共享试卷"
                            :style="{ fontSize: '14px', color: props.paperInfo.is_self ? '#1C1C1C' : '#C1C1C1' }" />
                    </div> -->
                </div>
            </div>
            <check-circle-filled class="check" @click="handleChangeCheck(false)" v-if="props.checked"
                style="fontSize: 16px; color: rgba(84,120,238,1)" />
            <div :class="['circle', 'check']" @click="handleChangeCheck(true)" v-else></div>
        </div>
        <div class="content">
            <div class="left">
                <div class="item">
                    <div class="label">考试时间：</div>
                    <div class="value">{{ dayjs(paperInfo.startTime).format('YYYY-MM-DD HH:mm') }} 至 {{
        dayjs(paperInfo.endTime).format('YYYY-MM-DD HH:mm') }}</div>
                </div>
                <div class="item">
                    <div class="label"
                        :title="`${props.paperInfo.author}于${dayjs(paperInfo.create_at).format('YYYY-MM-DD HH:mm')}创建`">
                        <SearchHighLight class="author" :text="paperInfo.author" :search-text="searchText">
                        </SearchHighLight>于{{ dayjs(paperInfo.create_at).format('YYYY-MM-DD HH:mm') }}创建
                    </div>
                </div>
                <div class="operation">
                    <div class="btn" v-if="paperInfo.status !== PaperStatus.OVER" @click="allocatePaper">关联考生</div>
                    <!-- <div class="btn" v-if="paperInfo.status !== PaperStatus.OVER" @click="sendEmail">邮件邀请</div> -->
                    <div class="btn" v-if="paperInfo.status !== PaperStatus.NOT_START" @click="handleViewStuGrade">查看成绩</div>
                    <!-- <div class="btn invite" v-if="paperInfo.status !== PaperStatus.OVER">
                        <div class="label">邀请码</div>
                        <img class="icon" src="@/assets/images/paper/down.png" alt="">
                        <div class="extend-wrap">
                            <div class="extend-list">
                                <div :class="['extend-item', hasWrite ? '' : 'disabled']"
                                    @click="handleCreatePaperCode">
                                    创建考试邀请码</div>
                                <div class="extend-item" @click="handleViewPaperCodeList">查看考试邀请码</div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="right">
                <div class="item">
                    <div class="value">{{ paperInfo.candidates_count || 0 }}</div>
                    <div class="label">应考人数</div>
                </div>
                <div class="item" v-if="[PaperStatus.DURING_EXAMS, PaperStatus.OVER].includes(paperInfo.status)">
                    <div class="value">{{ paperInfo.actual_count || 0 }}</div>
                    <div class="label">实考人数</div>
                </div>
                <div class="item" v-if="paperInfo.status === PaperStatus.DURING_EXAMS">
                    <div class="value">{{ paperInfo.completed_count || 0 }}</div>
                    <div class="label">交卷人数</div>
                </div>
            </div>
        </div>
        <div class="type" v-if="paperInfo.uniexam === PaperType.NOT_UEC">非统考</div>
    </div>
    <!-- 创建考试邀请码 -->
    <CreateInvitationCode v-if="invitationCodeModalVisible" :paper="props.paperInfo.id"
        :validtime="[props.paperInfo.startTime, props.paperInfo.endTime]" :papername="props.paperInfo.name"
        :uniexam="props.paperInfo.uniexam" :duration="props.paperInfo.duration"
        :limitlateness="props.paperInfo.limitlateness" :invitationCodeModalVisible="invitationCodeModalVisible"
        @closeModal="invitationCodeModalVisible = false" @getPaperInvitationCode="getPaperInvitationCode" />
    <!-- 查看考试邀请码 -->
    <InvitationCode v-if="papercodeListVisible" :invitationCodeModalVisible="papercodeListVisible"
        :paper="props.paperInfo.id" :papername="props.paperInfo.name" :hiddenAction="!hasWrite"
        @closeModal="papercodeListVisible = false" />
    <!-- <SharePaper v-model:isShared="isShared" :visible="sharePaperVisible" :paperId="props.paperInfo.id"
        :paperName="props.paperInfo.name" :teacher="props.paperInfo.teacher" @close="sharePaperVisible = false" /> -->
</template>

<script setup lang="ts">
import { ref, computed, createVNode } from 'vue'
import {
    EyeOutlined,
    ShareAltOutlined,
    CheckCircleFilled,
    InfoCircleOutlined,
    StarFilled
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import clipboard3 from 'vue-clipboard3'
import dayjs from 'dayjs'
import SearchHighLight from '@/components/SearchHighLight.vue';
import {
    PaperStatus,
    PaperStatusList,
    PaperType,
} from '@/models/paperModel'
import { delTestPaper, mockexam, beforeclonepaper, addpaperfocus, delpaperfocus } from '@/api/admin/paperManage'
import CreateInvitationCode from '@/views/PaperManage/components/CreateInvitationCode.vue'
import InvitationCode from '@/views/PaperManage/components/InvitationCode.vue'
import SharePaper from '@/pages/paperManage/components/SharePaper.vue'
import { delabilityassessment } from '@/api/admin/abilityTest';

const router = useRouter()
const props = defineProps<{
    paperInfo: any // 考试信息
    checked?: boolean // 选中
    searchText: string
}>()

const emits = defineEmits(['changeCheck', 'deletePaperOk'])

const hasWrite = computed(() => (!!props.paperInfo.permission && props.paperInfo.permission.indexOf('write') !== -1) || props.paperInfo.is_self)

const statusStyles = { // 考试状态样式
    [PaperStatus.NOT_START]: {
        backgroundColor: '#f3f7f1',
        color: '#2F8C00',
    },
    [PaperStatus.DURING_EXAMS]: {
        backgroundColor: '#fcf0f0',
        color: '#D71310',
    },
    [PaperStatus.OVER]: {
        backgroundColor: '#f5f5f5',
        color: '#626262',
    },

}
const allocatePaper = () => { // 关联考生
    router.push({ name: 'ability-test-allocate', query: { id: props.paperInfo.id, name: props.paperInfo.name } })
}

const sendEmail = () => { // 发送邮箱邀请
    router.push({ name: 'paperEmailSend', query: { id: props.paperInfo.id, name: props.paperInfo.name, hiddenAction: hasWrite.value ? 'false' : 'true' } })
}

// 编辑考试
const canEdit = computed(() => { // 考试前3分钟不允许编辑
    if (props.paperInfo.name === '考前流程模拟') return true
    if (!hasWrite.value) return false
    return new Date(props.paperInfo.startTime).getTime() - Date.now() - 3 * 60 * 1000 > 0
})
const handleEdit = () => {
    // if (props.paperInfo.name === '考前流程模拟') {
    //     router.push({ name: 'editPaper', query: { id: props.paperInfo.id } })
    //     return
    // }
    // if (!hasWrite.value) {
    //     message.info('很抱歉，因考试创建人未授予您编辑权限，您暂时无法编辑考试')
    //     return
    // }
    // if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    //     message.info('考试中禁止编辑')
    //     return
    // }
    // if (props.paperInfo.status === PaperStatus.OVER) {
    //     message.info('考试结束后禁止编辑')
    //     return
    // }
    // if (new Date(props.paperInfo.startTime).getTime() - Date.now() - 3 * 60 * 1000 <= 0) {
    //     message.info('考试开始前3分钟禁止编辑')
    //     return
    // }
    router.push({ name: 'ability-test-form', query: { id: props.paperInfo.id, mode: '编辑' } })
}

// 判断试题是否在9月之后创建的
function isCreateAfter(createDate: any, date = '2023-09-01') {
    return dayjs(createDate).isAfter(date)
}
// 克隆试卷
const handleCopy = async () => {
    if (isCreateAfter(props.paperInfo.create_at)) {
        try {
            let hasDeletedQuestions = await beforeclonepaper({ paper: props.paperInfo.id })
            if (!hasDeletedQuestions) {
                router.push({ name: 'copyPaper', query: { id: props.paperInfo.id } })
            } else {
                let h = createVNode
                Modal.info({
                    title: '试卷内容变更提醒',
                    content: '因部分题目被删除，试卷内容已自动更新。',
                    icon: () => h(InfoCircleOutlined),
                    onOk() {
                        router.push({ name: 'copyPaper', query: { id: props.paperInfo.id } })
                    },
                    okText: '确认'
                })
            }
        } catch (error) {
            message.error('克隆出错，请联系管理员')
        }
    } else {
        message.warning(
            '由于系统更新，暂时无法兼容此试卷的格式。克隆失败，给您造成不便，请谅解。请您重新创建试卷。'
        )
    }
}

// 创建人 && 非考试中状态 可删除试卷
const canDelete = computed(() => {
    return props.paperInfo.is_self && props.paperInfo.status !== PaperStatus.DURING_EXAMS
})
// 选中or取消选中，emit事件
const handleChangeCheck = (check: boolean) => {
    if (!check) {
        emits('changeCheck')
        return
    }
    // if (!props.paperInfo.is_self) {
    //     message.info('很抱歉，您不是此考试的创建人，所以无法进行删除操作')
    //     return
    // }
    // if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    //     message.info('考试中禁止删除')
    //     return
    // }
    emits('changeCheck')
}
// 删除试卷
const handleDeletePaper = async () => {
    // if (!props.paperInfo.is_self) {
    //     message.info('很抱歉，您不是此考试的创建人，所以无法进行删除操作')
    //     return
    // }
    // if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    //     message.info('考试中禁止删除')
    //     return
    // }
    Modal.confirm({
        title: '确定删除该考试？',
        async onOk() {
            try {
                await delabilityassessment({ action: 'del', id: [props.paperInfo.id] })
                message.success('删除成功！')
                emits('deletePaperOk')
            } catch (error) {
                // do nothing
            }
        }
    })
}

// 查看考试成绩
const handleViewStuGrade = () => {
    router.push({
        name: 'ability-test-grade-table',
        query: {
            id: props.paperInfo.id,
            name: props.paperInfo.name,
            hiddenPublish: hasWrite.value || props.paperInfo.is_self ? 'false' : 'true',
        }
    })
}

// 创建考试邀请码
const invitationCodeModalVisible = ref(false)
const handleCreatePaperCode = () => {
    if (!hasWrite.value) {
        message.info('很抱歉，因考试创建人未授予您编辑权限，您暂时无法创建考试邀请码')
        return
    }
    invitationCodeModalVisible.value = true
}
const { toClipboard } = clipboard3()
const getPaperInvitationCode = (code: string) => { // 生成考试邀请码成功回调
    invitationCodeModalVisible.value = false
    const text = `${code}`
    Modal.confirm({
        title: () => '提示',
        content: () => `你已成功生成考试邀请码：${code}`,
        width: '450px',
        okText: '复制',
        cancelText: '关闭',
        onOk: () => {
            toClipboard(text)
            message.info('考试邀请码已成功复制至剪切板!')
        }
    })
}

// 查看考试邀请码
const papercodeListVisible = ref(false)
const handleViewPaperCodeList = () => {
    papercodeListVisible.value = true
}

// 共享试卷
const isShared = ref(props.paperInfo.is_shared)
const sharePaperVisible = ref(false)
const handleSharePaper = () => {
    if (!props.paperInfo.is_self) {
        message.warning('很抱歉，您不是此考试的创建人，所以无法进行共享操作')
        return
    }
    sharePaperVisible.value = true
}

// 收藏
const isFocus = ref<boolean>(props.paperInfo.focus)
async function handleFocus() {
    isFocus.value = !isFocus.value
    try {
        if (isFocus.value) {
            // 收藏
            await addpaperfocus({ paper: props.paperInfo.id })
        } else {
            // 取消收藏
            await delpaperfocus({ paper: props.paperInfo.id })
        }
    } catch (error) {
        isFocus.value = !isFocus.value
    }
}

</script>

<style lang="less" scoped>
.paper-card {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px #0000001a;
    transition: all .3s ease;

    &:hover {
        box-shadow: 0 6px 18px #0003;
    }

    border: 1px solid #fff;
    box-sizing: border-box;
    position: relative;
    user-select: none;

    &.checked {
        border: 1px solid #5478ee;
    }

    .header {
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            min-width: 0;
            flex: auto;
            display: flex;
            align-items: center;

            .title {
                min-width: 0;
                height: 24px;
                font-size: 18px;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.85);
                text-align: left;
                line-height: 24px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .focus-icon {
                margin-left: 8px;
                cursor: pointer;
                color: #D8D8D8;

                &.active {
                    color: #FAAD14;
                }
            }

            .status {
                flex: none;
                width: 44px;
                height: 18px;
                margin-left: 8px;
                background: #fcf0f0;
                color: #d71310;
                border-radius: 4px;
                font-size: 12px;
                font-weight: Regular;
                text-align: center;
                line-height: 18px;
            }

            .operation {
                height: 24px;
                margin-left: 16px;
                align-items: center;
                display: none;

                .btn-wrap {
                    width: 24px;
                    height: 24px;
                    margin-left: 8px;
                    border-radius: 4px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &:hover {
                        background: #f5f5f5;
                    }

                    &:first-child {
                        margin-left: 0;
                    }

                    .btn {
                        flex: none;
                        width: 14px;
                        height: 14px;
                        object-fit: contain;
                        cursor: pointer;
                    }

                    &.disabled {
                        &:hover {
                            background: #fff;
                        }

                        .btn {
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }

        .check {
            flex: none;
            margin-left: 16px;
            cursor: pointer;

            &.circle {
                width: 16px;
                height: 16px;
                border: 1px solid rgba(217, 217, 217, 1);
                background: #fff;
                border-radius: 50%;
                display: none;

                &:hover {
                    border-color: rgba(84, 120, 238, 1);
                }
            }

            &.disabled {
                cursor: not-allowed;

                &:hover {
                    border-color: rgba(217, 217, 217, 1);
                }
            }
        }
    }

    .content {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;

        .left {
            .item {
                height: 18px;
                margin-bottom: 8px;
                display: flex;

                .label {
                    height: 18px;
                    color: rgba(0, 0, 0, 0.45);
                    font-size: 12px;
                    line-height: 18px;
                }

                .value {
                    height: 18px;
                    color: #181818;
                    font-size: 12px;
                    line-height: 18px;
                }
            }
        }

        .right {
            display: flex;
            justify-content: flex-end;

            .item {
                margin-right: 24px;
                display: flex;
                flex-direction: column;
                justify-content: center;

                &:last-child {
                    margin-right: 0;
                }

                .value {
                    height: 28px;
                    font-size: 24px;
                    font-weight: 600;
                    color: #5478ee;
                    text-align: center;
                    line-height: 28px;
                }

                .label {
                    height: 18px;
                    margin-top: 4px;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.45);
                    text-align: center;
                    line-height: 18px;
                }
            }
        }

        .operation {
            display: flex;

            .btn {
                height: 18px;
                margin-left: 8px;
                padding-right: 8px;
                font-size: 12px;
                color: #5478ee;
                line-height: 18px;
                position: relative;
                cursor: pointer;

                &::after {
                    display: block;
                    content: '';
                    width: 1px;
                    height: 12px;
                    background: #dfe1e6;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    right: 0;
                }

                &:first-child {
                    margin-left: 0;
                }

                &:last-child {
                    padding-right: 0;

                    &::after {
                        display: none;
                    }
                }
            }

            .invite {
                display: flex;
                align-items: center;
                position: relative;

                .label {
                    font-size: 12px;
                    height: 18px;
                    line-height: 18px;
                }

                .icon {
                    width: 16px;
                    height: 16px;
                    object-fit: contain;
                    margin-left: 4px;
                }

                .extend-wrap {
                    position: absolute;
                    top: 18px;
                    right: 0;
                    width: 100px;
                    height: 70px;
                    padding-top: 6px;
                    z-index: 2;
                    display: none;

                    .extend-list {
                        background: #ffffff;
                        border-radius: 2px;
                        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.10);

                        .extend-item {
                            height: 32px;
                            font-size: 12px;
                            text-align: center;
                            color: rgba(0, 0, 0, 0.84);
                            line-height: 32px;

                            &:hover {
                                background: #f1f4fe;
                                color: #5478EE;
                            }

                            &.disabled {
                                cursor: not-allowed;

                                &:hover {
                                    background: #ffffff;
                                    color: rgba(0, 0, 0, 0.84);
                                }
                            }
                        }
                    }
                }

                &:hover {
                    .extend-wrap {
                        display: block;
                    }
                }
            }
        }
    }

    .type {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 60px;
        height: 20px;
        background: linear-gradient(90deg, #e8eeff, #cdd9ff);
        border-radius: 0px 8px 0px 8px;
        font-size: 12px;
        text-align: center;
        color: #5478ee;
        line-height: 20px;
    }

    &:hover {
        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.10);

        .header {
            .operation {
                display: flex;
            }

            .circle {
                display: block;
            }
        }
    }
}
</style>