<template>
    <div ref="tagEllipsisRef" class="tag-ellipsis">
        <template v-for="(name, index) in _data">
            <span v-if="index < ellipsisIndex" class="tag-ellipsis__tag">{{ name }}</span>
            <span v-else-if="index === ellipsisIndex" class="tag-ellipsis__tag">...</span>
        </template>
    </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, reactive, onMounted } from 'vue'
import _ from 'lodash'
const props = withDefaults(defineProps<{
    data: string[]
}>(), {
    data: () => [],
})

const _data = ref(_.cloneDeep(props.data))

const tagEllipsisRef = ref()

const ellipsisIndex = ref(9999)
function calcEllipsisIndex() {
    let maxWidth = tagEllipsisRef.value?.clientWidth
    // 遍历
    let len = 0
    for (let i = 0; i < _data.value.length; i++) {
        let name = _data.value[i]
        len += 24 + name.length * 14
        if (len > maxWidth) {
            ellipsisIndex.value = i
            break
        }
    }
}

onMounted(() => {
    calcEllipsisIndex()
})

</script>

<style lang="less" scoped>
.tag-ellipsis {
    width: 100%;
    height: 100%;
    flex-wrap: nowrap;
    // overflow: hidden;

    .tag-ellipsis__tag {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        color: #2f8c00;
        background-color: #f3f7f1;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-shrink: 0;
    }
}
</style>