/* eslint-disable */
import { ref, unref } from 'vue'
import { VALIDATE_FAILED, validateFormModelAndTables } from '/@/utils/common/vxeUtils'
import { defHttp } from '/@/utils/http/axios'

export function useJvxeMethod(requestAddOrEdit, classifyIntoFormData, tableRefs, activeKey, refKeys, validateSubForm) {
      const formRef = ref()
      /** 查询某个tab的数据 */
      function requestSubTableData(url, params, tab, success) {
            tab.loading = true
            defHttp
                  .get({ url, params }, { isTransformResponse: false })
                  .then((res) => {
                        let { result } = res
                        if (res.success && result) {
                              if (Array.isArray(result)) {
                                    tab.dataSource = result
                              }
                              else if (Array.isArray(result.records)) {
                                    tab.dataSource = result.records
                              }
                        }
                        typeof success === 'function' ? success(res) : ''
                  })
                  .finally(() => {
                        tab.loading = false
                  })
      }

      /* --- handle 事件 --- */

      /** ATab 选项卡切换事件 */
      function handleChangeTabs(key) {
            // 自动重置scrollTop状态，防止出现白屏
            tableRefs[key]?.value?.resetScrollTop(0)
      }

      /** 获取所有的editableTable实例 */
      function getAllTable() {
            let values = Object.values(tableRefs)
            return Promise.all(values)
      }
      /** 确定按钮点击事件 */
      function handleSubmit() {
            /** 触发表单验证 */
            getAllTable()
                  .then((tables) => {
                        let values = formRef.value.getFieldsValue()
                        return validateFormModelAndTables(formRef.value.validate, values, tables, formRef.value.getProps, false)
                  })
                  .then((allValues) => {
                        /** 一次性验证一对一的所有子表 */
                        return validateSubForm && typeof validateSubForm === 'function' ? validateSubForm(allValues) : validateAllSubOne(allValues)
                  })
                  .then((allValues) => {
                        if (typeof classifyIntoFormData !== 'function') {
                              throw throwNotFunction('classifyIntoFormData')
                        }
                        let formData = classifyIntoFormData(allValues)
                        // 发起请求
                        return requestAddOrEdit(formData)
                  })
                  .catch((e) => {
                        if (e.error === VALIDATE_FAILED) {
                              // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                              // update-begin-author:taoyan date:2022-11-22 for: VUEN-2866【代码生成】Tab风格 一对多子表校验不通过时，点击提交表单空白了，流程附加页面也有此问题
                              if (e.paneKey) {
                                    activeKey.value = e.paneKey
                              }
                              else {
                                    // update-begin-author:liusq date:2024-06-12 for: TV360X-478 一对多tab，校验未通过时，tab没有跳转
                                    activeKey.value = e.subIndex == null ? (e.index == null ? unref(activeKey) : refKeys.value[e.index]) : Object.keys(tableRefs)[e.subIndex]
                                    // update-end-author:liusq date:2024-06-12  for: TV360X-478 一对多tab，校验未通过时，tab没有跳转
                              }
                              // update-end-author:taoyan date:2022-11-22 for: VUEN-2866【代码生成】Tab风格 一对多子表校验不通过时，点击提交表单空白了，流程附加页面也有此问题
                              // update-begin---author:wangshuai---date:2024-06-17---for:【TV360X-1064】非原生提交表单滚动校验没通过的项---
                              if (e?.errorFields) {
                                    const firstField = e.errorFields[0]
                                    if (firstField) {
                                          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'end' })
                                    }
                              }
                              return Promise.reject(e?.errorFields)
                              // update-end---author:wangshuai---date:2024-06-17---for:【TV360X-1064】非原生提交表单滚动校验没通过的项---
                        }
                        else {
                              console.error(e)
                        }
                  })
      }
      // 校验所有子表表单
      function validateAllSubOne(allValues) {
            return new Promise((resolve) => {
                  resolve(allValues)
            })
      }
      /* --- throw --- */

      /** not a function */
      function throwNotFunction(name) {
            return `${name} 未定义或不是一个函数`
      }

      /** not a array */
      function throwNotArray(name) {
            return `${name} 未定义或不是一个数组`
      }
      return [handleChangeTabs, handleSubmit, requestSubTableData, formRef]
}

export function listToTree<T = any>(list: any[], config: Partial<TreeHelperConfig> = {}): T[] {
      const conf = getConfig(config) as TreeHelperConfig;
      const nodeMap = new Map();
      const result: T[] = [];
      const { id, children, pid } = conf;

      for (const node of list) {
            node[children] = node[children] || [];
            nodeMap.set(node[id], node);
      }
      for (const node of list) {
            const parent = nodeMap.get(node[pid]);
            (parent ? parent[children] : result).push(node);
      }
      return result;
}

export function treeToList<T = any>(tree: any, config: Partial<TreeHelperConfig> = {}): T {
      config = getConfig(config);
      const { children } = config;
      const result: any = [...tree];
      for (let i = 0; i < result.length; i++) {
            if (!result[i][children!]) continue;
            result.splice(i + 1, 0, ...result[i][children!]);
      }
      return result;
}

export function findNode<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T | null {
      config = getConfig(config);
      const { children } = config;
      const list = [...tree];
      for (const node of list) {
            if (func(node)) return node;
            node[children!] && list.push(...node[children!]);
      }
      return null;
}

export function findNodeAll<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T[] {
      config = getConfig(config);
      const { children } = config;
      const list = [...tree];
      const result: T[] = [];
      for (const node of list) {
            func(node) && result.push(node);
            node[children!] && list.push(...node[children!]);
      }
      return result;
}

export function findPath<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T | T[] | null {
      config = getConfig(config);
      const path: T[] = [];
      const list = [...tree];
      const visitedSet = new Set();
      const { children } = config;
      while (list.length) {
            const node = list[0];
            if (visitedSet.has(node)) {
                  path.pop();
                  list.shift();
            } else {
                  visitedSet.add(node);
                  node[children!] && list.unshift(...node[children!]);
                  path.push(node);
                  if (func(node)) {
                        return path;
                  }
            }
      }
      return null;
}

export function findPathAll(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}) {
      config = getConfig(config);
      const path: any[] = [];
      const list = [...tree];
      const result: any[] = [];
      const visitedSet = new Set(),
            { children } = config;
      while (list.length) {
            const node = list[0];
            if (visitedSet.has(node)) {
                  path.pop();
                  list.shift();
            } else {
                  visitedSet.add(node);
                  node[children!] && list.unshift(...node[children!]);
                  path.push(node);
                  func(node) && result.push([...path]);
            }
      }
      return result;
}

export function useValidateAntFormAndTable(activeKey, refMap) {
      /**
       * 获取所有子表数据
       */
      async function getSubFormAndTableData() {
            let formData = {}
            let all = Object.keys(refMap)
            let key = ''
            for (let i = 0; i < all.length; i++) {
                  key = all[i]
                  let instance = refMap[key].value
                  if (instance.isForm) {
                        let subFormData = await validateFormAndGetData(instance, key)
                        if (subFormData) {
                              formData[`${key}List`] = [subFormData]
                        }
                  }
                  else {
                        let arr = await validateTableAndGetData(instance, key)
                        if (arr && arr.length > 0) {
                              formData[`${key}List`] = arr
                        }
                  }
            }
            return formData
      }

      /**
       * 转换数据用 如果有数组转成逗号分割的格式
       * @param data
       */
      function transformData(data) {
            if (data) {
                  Object.keys(data).map((k) => {
                        if (Array.isArray(data[k])) {
                              data[k] = data[k].join(',')
                        }
                  })
            }
            return data
      }

      /**
       * 子表table
       * @param instance
       * @param key
       */
      async function validateTableAndGetData(instance, key) {
            const errors = await instance.validateTable()
            if (!errors) {
                  return instance.getTableData()
            }
            else {
                  activeKey.value = key
                  // 自动重置scrollTop状态，防止出现白屏
                  instance.resetScrollTop(0)
                  return Promise.reject(1)
            }
      }

      /**
       * 子表表单
       * @param instance
       * @param key
       */
      async function validateFormAndGetData(instance, key) {
            try {
                  let data = await instance.getFormData()
                  transformData(data)
                  return data
            }
            catch (e) {
                  activeKey.value = key
                  return Promise.reject(e)
            }
      }

      return {
            getSubFormAndTableData,
            transformData,
      }
}

export const rules = {
  rule(type, required) {
    if (type === 'email') {
      return this.email(required);
    }
    if (type === 'phone') {
      return this.phone(required);
    }
  },
  email(required) {
    return [
      {
        required: required ? required : false,
        validator: async (_rule, value) => {
          if (required == true && !value) {
            return Promise.reject('请输入邮箱!');
          }
          if (
            value &&
            !new RegExp(
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            ).test(value)
          ) {
            return Promise.reject('请输入正确邮箱格式!');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ] as ArrayRule;
  },
  phone(required) {
    return [
      {
        required: required,
        validator: async (_, value) => {
          if (required && !value) {
            return Promise.reject('请输入手机号码!');
          }
          if (!/^1[3456789]\d{9}$/.test(value)) {
            return Promise.reject('手机号码格式有误');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ];
  },
  startTime(endTime, required) {
    return [
      {
        required: required ? required : false,
        validator: (_, value) => {
          if (required && !value) {
            return Promise.reject('请选择开始时间');
          }
          if (endTime && value && dateUtil(endTime).isBefore(value)) {
            return Promise.reject('开始时间需小于结束时间');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ];
  },
  endTime(startTime, required) {
    return [
      {
        required: required ? required : false,
        validator: (_, value) => {
          if (required && !value) {
            return Promise.reject('请选择结束时间');
          }
          if (startTime && value && dateUtil(value).isBefore(startTime)) {
            return Promise.reject('结束时间需大于开始时间');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ];
  },
  confirmPassword(values, required) {
    return [
      {
        required: required ? required : false,
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('密码不能为空');
          }
          if (value !== values.password) {
            return Promise.reject('两次输入的密码不一致!');
          }
          return Promise.resolve();
        },
      },
    ];
  },
  duplicateCheckRule(tableName, fieldName, model, schema, required?) {
    return [
      {
        validator: (_, value) => {
          if (!value && required) {
            return Promise.reject(`请输入${schema.label}`);
          }
          return new Promise<void>((resolve, reject) => {
            duplicateCheck({
              tableName,
              fieldName,
              fieldVal: value,
              dataId: model.id,
            })
              .then((res) => {
                res.success ? resolve() : reject(res.message || '校验失败');
              })
              .catch((err) => {
                reject(err.message || '验证失败');
              });
          });
        },
      },
    ] as ArrayRule;
  },
};

export async function duplicateValidate(tableName, fieldName, fieldVal, dataId) {
  try {
    let params = {
      tableName,
      fieldName,
      fieldVal,
      dataId: dataId,
    };
    const res = await duplicateCheck(params);
    if (res.success) {
      return Promise.resolve();
    } else {
      return Promise.reject(res.message || '校验失败');
    }
  } catch (e) {
    return Promise.reject('校验失败,可能是断网等问题导致的校验失败');
  }
}