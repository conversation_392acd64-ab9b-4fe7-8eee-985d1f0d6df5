import type { LanguageFunc, QuestionEnum, questionType } from '@/models/questionModel'
import useGetPoints from '../../hooks/useGetPoints'
import useOption from '../../hooks/useOption'
import {
  CHECK_VARIATE_NAME_REG,
  checkBody,
  checkBtestcase,
  checkCategory,
  checkDisablekws,
  checkOptions,
  checkParameters,
  checkPtestcase,
  checkRtype,
  checkType,
  checkVariateName,
} from '../../rules'
import { checkScore } from '@/utils/validate'
import useCompose from '../../hooks/useCompose'
// @ts-ignore
import cloneDeep from 'lodash/cloneDeep'

const rawFormState = {
  type: 3,
  "bankPath": "",
  "recordDuration": 3,
  "depth": "",
  "skewness": "",
  "difficulty": 0,
  "complexFlag": 0,
  "content": "",
  "readFlag": "STEM",
  "readContent": "",
  "videoUrl": "",
  "scoreBasis": "",
  "reference": "",
  "recordDuration": 0,
  "templateCode": "",
  "configSchema": {},
  "correctAnswer": {}
}
const formState = ref<{
  disablekws: LanguageFunc
  [propName: string]: any
}>(cloneDeep(rawFormState))

const createQuestionFormRef = ref()
const activeTab = ref('1')


const scorepts = ref<any>([{ keyword: [], score: 0 }])
const scoreInputTotalRef = ref()
const fillBlank = ref<any>([])
const fillBlankCount = ref(0)
let oldFillBlank = ref('')
const { addOption, delOption } = useOption(formState)
const { addPoints, delPoints } = useGetPoints(formState, scorepts, scoreInputTotalRef)
const validateAlgorithmRef = ref()
const subQuestionList = ref<any>([])
const { checkSubQuestionList, addCompoundQuestions, handleDelete, handleUp, handleDown } = useCompose(subQuestionList)
const rules: any = {
  type: [{ required: true, validator: checkType, trigger: 'change' }],
  category: [{ required: true, validator: checkCategory, trigger: 'blur' }],
  bankPath: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  depth: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  readContent: [{ required: true, validator: checkBody, trigger: 'blur' }],
  skewness: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  content: [{ required: true, validator: checkBody, trigger: 'blur' }],
  complexContent: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }],
  subQuestionList: [{ required: true, validator: checkSubQuestionList, trigger: 'blur' }],
  func_name: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  rtype: [{ required: true, validator: checkRtype, trigger: 'blur' }],
  difficulty: [{ required: true, message: '请选择难度', trigger: 'change', type: 'number' }],
  disablekws: [{ required: true, validator: checkDisablekws, trigger: 'blur' }],
  parameters: [{ required: true, validator: checkParameters, trigger: 'blur' }],
  btestcase: [{ required: true, validator: checkBtestcase, trigger: 'blur' }],
  ptestcase: [{ required: true, validator: checkPtestcase, trigger: 'blur' }],
}
const varType = [
  { label: 'string', value: 'string' },
  { label: 'int', value: 'int' },
]

const funcMainObj = ref({})
const funcMainParams = ref({})
const funcTemplateObj = ref({})

function delOptionWrap(a: any, b: any) {
  delOption(a, b)
  createQuestionFormRef.value.validateFields(['options'])
}

function handleOptionBlur() {
  createQuestionFormRef.value.validateFields(['options'])
}

function clearFormState() {
  formState.value = cloneDeep(rawFormState)
  fillBlank.value = []
  fillBlankCount.value = 0
  oldFillBlank.value = ''
  scorepts.value = [{ keyword: [], score: 0 }]
  subQuestionList.value = []
  activeTab.value = '1'
}

export function useFillBlank() {
  return { fillBlank, fillBlankCount, oldFillBlank }
}

export function useAlgo() {
  return { varType, validateAlgorithmRef, activeTab, funcMainObj, funcMainParams, funcTemplateObj }
}

export function useQuestionRef() {
  return { createQuestionFormRef, scoreInputTotalRef }
}

export function useQuestionOption() {
  return { addOption, delOption, addPoints, delPoints, delOptionWrap }
}

export function useQuestionManage() {
  return { subQuestionList, formState, rules, scorepts, createQuestionFormRef, scoreInputTotalRef, clearFormState, handleOptionBlur }
}