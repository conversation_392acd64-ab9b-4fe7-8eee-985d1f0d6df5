<template>
    <div>
        <a-form-item label="评分依据">
            <a-textarea v-model:value="proxy.scorebasis" :rows="4" placeholder="点击编辑" />
            <p class="standard-tip">(仅阅卷老师可见)</p>
        </a-form-item>
        <a-form-item label="得分点" name="answer" class="points-wrapper">
            <template v-for="(item, index) in proxy.answer" :key="index">
                <div class="item">
                    <div class="index">{{ index + 1 }}</div>
                    <div class="tags-select">
                        <JTagInput class="tag-ipt" v-model="item.keyword" placeholder="输入完成后按回车添加多个关键词"></JTagInput>
                    </div>
                    <div class="score-wrapper">
                        <span class="score">分值</span>
                        <a-input-number v-model:value="item.score" :min="1" :max="100" :precision="0" />
                    </div>
                    <div class="del-icon-wrap">
                        <svg-icon class="del-icon" name="circle-del" width="16px" height="16px" @click="delPoints(index)" />
                    </div>
                </div>
            </template>
            <div class="addpoints-btn">
                <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
            </div>
        </a-form-item>
    </div>
</template>
  
<script lang="ts" setup>
import { watch } from 'vue'
import { useVModel } from '@vueuse/core';
import JTagInput from '@/components/JTagInput.vue';
import _ from 'lodash';

const props = defineProps<{
    formState: any
}>();

const emits = defineEmits<{
    (e: 'update:formState', v: any): void
}>();

const proxy = useVModel(props, 'formState', emits, {
    passive: true,
    deep: true
});

// 添加得分点
function addPoints() {
    proxy.value.answer.push({ keyword: [], score: 1 })
}

// 删除问答题得分点
function delPoints(index: number) {
    proxy.value.answer.splice(index, 1)
}

watch(() => proxy.value.answer, (val) => {
    proxy.value.score = val.reduce((prev: number, curr: any) => prev + curr.score, 0)
}, { deep: true })

</script>
  
<style lang="less" scoped>
.standard-tip {
    margin-top: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

.points-wrapper {
    .item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 12px;

        .index {
            flex: none;
            width: 10px;
            height: 28px;
            text-align: left;
            line-height: 28px;
        }

        .tags-select {
            width: 80%;
            margin: 0 16px;
        }

        :deep(.ant-select-selection-item) {
            font-size: 12px;
        }
    }

    .score-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        margin-right: 16px;

        .score {
            flex-grow: 0;
            flex-shrink: 0;
            margin-right: 16px;
        }

        .score-input {
            flex-grow: 0;
            flex-shrink: 0;
        }
    }

    .del-icon-wrap {
        flex: none;
        width: 16px;
        height: 32px;
        display: flex;
        align-items: center;

        .del-icon {
            cursor: pointer;
        }
    }

    .addpoints-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 82px;
        height: 24px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        margin-top: 10px;
    }
}

:deep(.tag-ipt) {
    margin: 0 10px;
    border: none;
    font-size: 12px;
    align-items: center;

    .tagify__input {
        &::before {
            font-size: 12px;
        }
    }

    .tagify__tag-text {
        font-size: 12px;
    }
}
</style>
  