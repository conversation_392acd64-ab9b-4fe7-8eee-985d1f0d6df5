import request from '@/utils/http'

/** 新增测评 */
export function addabilityassessment(data?: any) {
    return request({
        url: '/addabilityassessment',
        data
    })
}

/** 测评列表 */
export function getabilityassessment(data?: any) {
    return request({
        url: '/getabilityassessment',
        data
    })
}

/** 测评详情 */
export function assessmentdetail(data?: { id: string }) {
    return request({
        url: '/assessmentdetail',
        data
    })
}

/** 查询关联考生 */
export function assessmentrelatestu(data?: any) {
    return request({
        url: '/assessmentrelatestu',
        data
    })
}

/** 查询测评报考部门 */
export function getassessmentstudept(data?: any) {
    return request({
        url: '/getassessmentstudept',
        data
    })
}

/** 关联考生 */
export function associateabilityassessment(data?: {
    students: string[]
    assessment: string
}) {
    return request({
        url: '/associateabilityassessment',
        data
    })
}

/** 删除关联考生 */
export function delassociateabilityassessment(data?: {
    students: string[]
    assessment: string
}) {
    return request({
        url: '/delassociateabilityassessment',
        data
    })
}

/** 删除测评 */
export function delabilityassessment(data?: any) {
    return request({
        url: '/delabilityassessment',
        data
    })
}

/** 编辑测评 */
export function modifyabilityassessment(data?: any) {
    return request({
        url: '/modifyabilityassessment',
        data
    })
}

/** 测评成绩列表 */
export function getAssessmentresults(data?: object) {
    return request({
        url: '/assessmentresults',
        data
    })
}