<template>
  <div class="tag-tree">
    <a-tree 
      :tree-data="tagList"
      :expandedKeys="expandedKeys"
      :auto-expand-parent="autoExpandParent"
      @expand="onExpand"
      @select="onSelectTag">
      <template #title="{ id, name, pname, category }">
        <a-dropdown :trigger="['contextmenu']">
          <span v-if="name.indexOf(props.searchValue) > -1">
            {{ name.substr(0, name.indexOf(props.searchValue)) }}
            <span style="color: #f50">{{ props.searchValue }}</span>
            {{ name.substr(name.indexOf(props.searchValue) + props.searchValue.length) }}
          </span>
          <span v-else>{{ name }}</span>
          <template #overlay>
            <a-menu @click="({ key: menuKey }) => onContextMenuClick(menuKey, id, name,pname, category)">
              <a-menu-item key="add"
                ><span style="padding: 0 8px; font-size: 14px; color:#3158BD"
                  >创建</span
                ></a-menu-item
              >
              <a-menu-item v-if="pname" key="edit"
                ><span style="padding: 0 8px; font-size: 14px; color: #419ad8"
                  >编辑</span
                ></a-menu-item
              >
              <a-menu-item v-if="pname" key="del">
                <span style="padding: 0 8px; font-size: 14px; color: #f5222d">删除</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
      <template #switcherIcon>
        <Icon icon="DownOutlined" />
      </template>
      <template #child>
        <Icon icon="BookOutlined" />
      </template>
    </a-tree>
  </div>
</template>
<script lang="ts" setup>
import { createVNode, ref, watch } from 'vue'
import { tagsMange } from '@/api/admin/tagsManage'
import { message, Modal } from 'ant-design-vue'
import { SelectEvent } from 'ant-design-vue/lib/tree/Tree'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  searchValue: {
    type: String
  }
})

const emits = defineEmits(['getTagInfo', 'refreshTagList'])

const expandedKeys = ref<string[]>([])
const autoExpandParent = ref<boolean>(true)
const onExpand = (keys: string[]) => {
  expandedKeys.value = keys
  autoExpandParent.value = false
}

const onSelectTag = (selectedKeys: string[], info: SelectEvent) => {
  const tagId = info.node.dataRef
  emits('getTagInfo', {...tagId, refresh:true})
}

const onContextMenuClick = (key: string, parent: string, pname: string,rname:string, category: string) => {
  if (key === 'add') {
    emits('getTagInfo', { id: null, name: '', parent, pname, category })
    return
  }
  if (key === 'edit') {
    emits('getTagInfo', { id: parent, name: pname, pname: rname,category})
    return
  }
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除 ${pname} 标签?`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      delTag(parent)
    }
  })
}

// 删除标签
const delTag = (id: string, force = false) => {
  const params = {
    action: 'del',
    id,
    force
  }
  tagsMange(params).then((res: any) => {
    if (!res.cascade) {
      message.success('标签删除成功!')
      emits('refreshTagList')
      return
    }
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '该标签已关联内容,是否强制删除?',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        delTag(id, true)
      }
    })
  })
}

// const expandedKeys = ref<string[]>(['0-0-0', '0-0-1'])

// watch(expandedKeys, () => {
//   console.log('expandedKeys', expandedKeys)
// })

// const onLoadData = (treeNode: any) => {
//   return new Promise(async (resolve: (value?: unknown) => void) => {
//     if (treeNode.dataRef.children) {
//       resolve()
//       return
//     }
//     const childData: any = await queryTags(treeNode.dataRef.id)
//     if (!childData.length) {
//       treeNode.dataRef.slots = { switcherIcon: 'child' }
//       message.info('该标签没有子标签')
//     }
//     treeNode.dataRef.children = childData
//     tagList.value = [...tagList.value]
//     resolve()
//   })
// }

// const queryTags = async (parentId: string) => {
//   return await tagsMange({ action: 'query', parent: parentId })
// }

const getParentKey = (key: string, tree: any[]): string | number | undefined => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    console.log(node)
    if (node.children) {
      if (node.children.some((item:any) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}

const tagList = ref([])
watch(
  () => props.treeData,
  () => {
    tagList.value = JSON.parse(JSON.stringify(props.treeData))
  },
  {
    immediate: true,
    deep: true
  }
)
watch(() => props.searchValue, (value:any) => {
  const expanded = tagList.value
    .map((item: any) => {
      if ((item.name as string).indexOf(value) > -1) {
        return getParentKey(item.id as string, tagList.value);
      }
      return null;
    })
    .filter((item, i, self) => item && self.indexOf(item) === i);
  expandedKeys.value = expanded as string[];
  // searchValue.value = value;
  autoExpandParent.value = true;
});
</script>

<style lang="less" scoped>
.tag-tree {
  height: calc(100vh - 240px);
  overflow: auto;
  &::-webkit-scrollbar-track-piece {
    background: #d3d4d4;
  }

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #e6e9ec;
    border-radius: 20px;
  }
}
</style>
