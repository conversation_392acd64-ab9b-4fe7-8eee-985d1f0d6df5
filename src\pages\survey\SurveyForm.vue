<template>
    <div class="survey-form-page">
        <div class="title-wrap">
            <div>
                <span style="cursor: pointer;color: rgba(0,0,0,0.45);" @click="router.back">问卷管理 / </span>
                <span class="title">{{ ACTION[props.mode] }}问卷</span>
            </div>
            <div class="btns">
                <a-button type="primary" @click="handlePreview">预览</a-button>
            </div>
        </div>
        <div class="form-container">
            <a-form class="form" ref="formRef" :model="formState" :rules="rules" :hideRequiredMark="true"
                :label-col="labelCol" :colon="false" labelAlign="left">
                <a-form-item label="问卷名称" name="name">
                    <a-input style="width: 200px;" v-model:value="formState.name" autocomplete="off" />
                </a-form-item>
                <a-form-item label="问卷描述">
                    <a-textarea v-model:value="formState.describeContent" :maxlength="200" show-count
                        placeholder="请输入问卷描述（非必填）&#10;如：感谢您抽出宝贵的时间填写问卷，请根据实际情况认真作答。" autocomplete="off" :rows="4" />
                </a-form-item>

                <a-collapse :accordion="false" v-model:activeKey="activeKey" v-if="formState.content.length"
                    style="margin-bottom: 24px;">
                    <template v-for="(qItem, qIndex) in formState.content" :key="qItem.id">
                        <a-collapse-panel
                            :header="qItem.type > 100 ? QuestionEnum[qItem.type] : `${getRealIndex(qIndex, formState.content)}.${QuestionEnum[qItem.type]}`">
                            <template #extra>
                                <img src="@/assets/images/delete.png" alt="删除" @click="handleDelete(qIndex)" />
                                <img style="margin-left: 16px" v-if="qIndex !== 0"
                                    src="@/assets/icons/svg/compoundUp.svg" alt="上移"
                                    @click="handleMove($event, qIndex, 'up')" />
                                <img style="margin-left: 16px" v-if="qIndex !== formState.content.length - 1"
                                    src="@/assets/icons/svg/compoundDown.svg" alt="下移"
                                    @click="e => handleMove(e, qIndex, 'down')" />
                            </template>
                            <template v-if="qItem.type <= 100">
                                <a-form-item label="题干内容" :name="['content', qIndex, 'body']" :rules="{
                                    required: true,
                                    message: '请输入题干内容',
                                }">
                                    <a-textarea v-model:value="qItem.body" placeholder="点击编辑" autocomplete="off"
                                        :rows="4" show-count :maxlength="1000" />
                                </a-form-item>
                                <template v-if="qItem.type == QuestionEnum['单选题'] || qItem.type == QuestionEnum['多选题']">
                                    <a-form-item label=" " :name="['content', qIndex, 'options']"
                                        :rules="[{ required: true, validator: checkOptions(formState) }]">
                                        <a-radio-group v-if="qItem.type === QuestionEnum['单选题']" disabled
                                            style="max-width:100%;">
                                            <div class="option-item" v-for="(item, index) in qItem.options"
                                                :key="item.id">
                                                <a-radio :value="item.value">
                                                    <!-- <span class="option-radio">{{ item.value }}</span> -->
                                                </a-radio>
                                                <a-textarea v-model:value.trim="item.content"
                                                    :auto-size="{ minRows: 1 }" :disabled="item.other"
                                                    placeholder="点击，编辑选项"
                                                    @blur="formRef?.validateFields([['content', qIndex, 'options']])" />
                                                <svg-icon class="del-icon" name="circle-del" width="16px" height="16px"
                                                    @click.prevent="handleOptionRemove($event, qItem, index)" />
                                            </div>
                                        </a-radio-group>
                                        <a-checkbox-group v-if="qItem.type === QuestionEnum['多选题']" disabled
                                            style="max-width:100%;">
                                            <div class="option-item" v-for="(item, index) in qItem.options"
                                                :key="item.id">
                                                <a-checkbox :value="item.value">
                                                    <!-- <span class="option-radio">{{ item.value }}</span> -->
                                                </a-checkbox>
                                                <a-textarea v-model:value.trim="item.content"
                                                    :auto-size="{ minRows: 1 }" :disabled="item.other"
                                                    placeholder="点击，编辑选项"
                                                    @blur="formRef?.validateFields([['content', qIndex, 'options']])" />
                                                <svg-icon class="del-icon" name="circle-del" width="16px" height="16px"
                                                    @click.prevent="handleOptionRemove($event, qItem, index)" />
                                            </div>
                                        </a-checkbox-group>
                                    </a-form-item>
                                    <a-form-item label=" " style="margin-bottom: 0;">
                                        <div class="add-option-btn" @click="handleOptionAdd(qItem)">
                                            <svg-icon name="plus" />
                                            <span>添加选项</span>
                                        </div>
                                        <div style="margin-top: 18px;">
                                            <a-checkbox v-model:checked="qItem.other">显示其他</a-checkbox>
                                            <a-tooltip placement="right" overlayClassName="light">
                                                <template #title>
                                                    <span>勾选后，回答者可自行输入合适的答案</span>
                                                </template>
                                                <svg-icon class="common-info-icon" name="info2"></svg-icon>
                                            </a-tooltip>
                                        </div>
                                    </a-form-item>
                                </template>
                                <template v-else-if="qItem.type == QuestionEnum['评分题']">
                                    <div style="height: 32px;margin-left: 104px;">
                                        <svg-icon v-for="i in qItem.score" name="deactive_star" width="32px"
                                            height="32px" style="margin-right: 16px;"></svg-icon>
                                    </div>
                                    <a-form-item label=" " :name="['content', qIndex, 'min_desc']"
                                        :rules="{ required: true, message: '请输入文案' }">
                                        <p class="option-item" style="margin-top: 16px;">
                                            <span style="margin-right: 13px;font-size: 12px;">1分文案</span>
                                            <a-textarea v-model:value.trim="qItem.min_desc" :auto-size="{ minRows: 1 }"
                                                :maxLength="8" placeholder="点击，编辑文案" />
                                        </p>
                                    </a-form-item>
                                    <a-form-item label=" " :name="['content', qIndex, 'max_desc']"
                                        :rules="{ required: true, message: '请输入文案' }" style="margin-top: -8px;">
                                        <p class="option-item">
                                            <span style="margin-right: 13px;font-size: 12px;">5分文案</span>
                                            <a-textarea v-model:value.trim="qItem.max_desc" :auto-size="{ minRows: 1 }"
                                                :maxLength="8" placeholder="点击，编辑文案" />
                                        </p>
                                    </a-form-item>
                                </template>
                                <a-form-item label=" ">
                                    <div :style="{ marginTop: qItem.type === QuestionEnum['评分题'] ? '0' : '8px' }">
                                        <a-checkbox v-model:checked="qItem.required">必答</a-checkbox>
                                    </div>
                                </a-form-item>
                            </template>
                            <template v-else>
                                <a-divider dashed style="border-color: #e8e8e8;">
                                    <a-input style="width: 200px;" v-model:value="qItem.dividerTitle" :maxLength="15"
                                        autocomplete="off" placeholder="请输入分割线标题（非必填）" />
                                </a-divider>
                            </template>
                        </a-collapse-panel>
                    </template>
                </a-collapse>

                <a-dropdown trigger="click">
                    <template #overlay>
                        <a-menu class="compoundItem">
                            <template v-for="item in columns" :key="item.key">
                                <a-menu-item @click="handleCreate(item)">{{ item.label }}</a-menu-item>
                            </template>
                        </a-menu>
                    </template>
                    <a-button type="primary" style="display: flex; align-items: center;gap: 4px;">
                        <svg-icon name="plus3" width="16px" height="16px"></svg-icon>
                        <span>添加</span>
                    </a-button>
                </a-dropdown>

                <div class="checkbox-wrapper">
                    <a-checkbox v-model:checked="isWxLogin">需要微信授权登录</a-checkbox>
                    <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
                        <template #title>
                            <span>勾选后，必须通过微信授权才可以填写该问卷</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                    </a-tooltip>
                </div>
                <div class="checkbox-wrapper" v-if="isWxLogin">
                    <a-checkbox v-model:checked="formState.multipleSubmit">允许用户重复提交问卷</a-checkbox>
                    <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
                        <template #title>
                            <span>勾选后，用户可重复提交该问卷，仅保留最新一次的提交记录</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                    </a-tooltip>
                </div>
            </a-form>
            <a-divider style="margin-bottom: 0;"></a-divider>
            <div class="form-footer">
                <a-button type="primary" @click="handleSave" :loading="submitLoading">保存</a-button>
                <a-button @click="handlePublish" :loading="publishLoading">发布</a-button>
                <a-button @click="router.back">取消</a-button>
            </div>
        </div>
        <PagePreview v-model:visible="previewVisible" :back-to-top-container="surveyPreviewRef?.contentEle">
            <SurveyPreview ref="surveyPreviewRef" :data="formState"></SurveyPreview>
        </PagePreview>
    </div>
</template>
<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { setSurveyQuestionNumber, uuid } from '@/utils'
import { QuestionEnum } from '@/models/questionModel';
import { Modal, message } from 'ant-design-vue';
import _ from 'lodash';
import { addquestionnaire, modifyquestionnaire, publishquestionnaire, questionnairedetail } from '@/api/admin/survey';
import { FormInstance } from 'ant-design-vue/es/form';
import { ACTION } from '@/config/constants'
import { scrollToFirstErrFormItem } from '@/utils/common';
import { valideSurvey } from './utils';
import PagePreview from '@/components/PagePreview.vue';
import SurveyPreview from './SurveyPreview.vue';

const props = withDefaults(defineProps<{
    mode?: 'create' | 'edit' | 'copy'
    id?: string
}>(), {
    mode: 'create',
})

const router = useRouter()

const columns = [
    { value: QuestionEnum['单选题'], label: '单选题' },
    { value: QuestionEnum['多选题'], label: '多选题' },
    { value: QuestionEnum['问答题'], label: '问答题' },
    { value: QuestionEnum['评分题'], label: '评分题' },
    { value: QuestionEnum['分割线'], label: '分割线' },
]

const labelCol = {
    style: { width: '74px', textAlign: 'right', marginRight: '30px' },
}

const formState = ref<{
    id: string
    name: string
    describeContent: string
    content: SurveyQuestionType[]
    loginMethod: '' | 'wx'
    multipleSubmit?: boolean
}>({
    id: '',
    name: '',
    describeContent: '',
    content: [],
    loginMethod: '',
    multipleSubmit: false
})
let initialFormState = ref(_.cloneDeep(formState.value))


// 是否微信授权登录
const isWxLogin = computed({
    get() {
        return formState.value.loginMethod === 'wx'
    },
    set(val) {
        formState.value.loginMethod = val ? 'wx' : ''
    }
})

// 获取题号
function getRealIndex(index, arr) {
    let previousSpecialTypeCount = 0

    // 遍历数组，在当前index之前统计特殊类型的题目数量
    for (let i = 0; i < index; i++) {
        if (arr[i].type > 100) {
            previousSpecialTypeCount++;
        }
    }

    return index + 1 - previousSpecialTypeCount
}

// 预览相关
const previewVisible = ref(false)
const surveyPreviewRef = ref<InstanceType<typeof SurveyPreview>>()
function handlePreview() {
    previewVisible.value = true
}

/**
 * 处理移动事件
 *
 * @param e 事件对象
 * @param index 当前元素的索引
 * @param type 移动方向，可选值为 'up' 或 'down'
 */
function handleMove(e: any, index: number, type: 'up' | 'down') {
    e.stopPropagation()
    let targetIndex = type === 'up' ? index - 1 : index + 1
    const tempProxy = JSON.stringify(formState.value.content[targetIndex])
    formState.value.content[targetIndex] = JSON.parse(JSON.stringify(formState.value.content[index]))
    formState.value.content[index] = JSON.parse(tempProxy)
}


/**
 * 处理删除操作
 *
 * @param index 要删除的元素的索引
 */
const handleDelete = (index: number) => {
    formState.value.content.splice(index, 1)
}

/** 手风琴激活项 */
const activeKey = ref<string[]>([])

/**
 * 处理创建问卷问题
 *
 * @param item 包含问题和类型的对象
 * @param item.value 问题的类型，数字类型
 * @param item.label 问题的标签，字符串类型
 */
function handleCreate(item: { value: number, label: string }) {
    let obj: SurveyQuestionType = {
        id: uuid(),
        type: item.value,
        body: '',
        required: false
    }
    if (item.value === QuestionEnum['单选题'] || item.value === QuestionEnum['多选题']) {
        Reflect.set(obj, 'options', [
            { id: _.uniqueId(), value: '0', content: '' },
            { id: _.uniqueId(), value: '1', content: '' },
            { id: _.uniqueId(), value: '2', content: '' },
        ])
        Reflect.set(obj, 'other', false)
    } else if (item.value === QuestionEnum['评分题']) {
        Reflect.set(obj, 'score', 5)
        Reflect.set(obj, 'min_desc', '')
        Reflect.set(obj, 'max_desc', '')
    }

    activeKey.value.push(obj.id)
    formState.value.content.push(obj)
}

/**
 * 处理选项添加
 *
 * @param item SurveyQuestionType类型的选项对象
 * @returns 无返回值
 */
function handleOptionAdd(item: SurveyQuestionType) {
    if (!item.options?.length) return

    const currentOptionsNumber = item.options?.length
    item.options.push({ id: _.uniqueId(), value: String(currentOptionsNumber), content: '' })
}

/**
 * 处理选项移除操作
 *
 * @param e 事件对象
 * @param qItem 调查问题类型
 * @param optionIndex 要移除的选项索引
 */
function handleOptionRemove(e: Event, qItem: SurveyQuestionType, optionIndex: number) {
    e.preventDefault()
    e.stopPropagation()
    if (!qItem.options) return

    if ((qItem.type === QuestionEnum['单选题'] || qItem.type === QuestionEnum['多选题']) && qItem.options.length <= 1) {
        return message.warning('至少保留1个选项')
    }
    qItem.options.splice(optionIndex, 1)

    // 重新设置选项值
    for (let i = optionIndex; i < qItem.options.length; i++) {
        qItem.options[i].value = i
    }
}

async function getDetail() {
    if (!props.id) return

    let res = await questionnairedetail({ id: props.id })

    res.multipleSubmit = res.multipleSubmit == 1 ? true : false
    res.going = res.going == 1 ? true : false

    res.content.forEach(item => item.options?.forEach(o => o.value = String(o.value)))


    if (props.mode === 'copy') {
        Object.assign(formState.value, _.pick(res, ['name', 'describeContent', 'content', 'loginMethod', 'multipleSubmit']));
    } else {
        formState.value = res
    }
    initialFormState.value = _.cloneDeep(formState.value)

    // 题目手风琴全部展开
    activeKey.value = formState.value.content.map(item => item.id)
}
getDetail()

// ————————————————  表单部分  ———————————————

// 定义规则
const checkOptions = (formState: any) => {
    return async (rule: any, value: any[]) => {
        if (!value?.length) return Promise.reject('请添加选项')

        if (value.some((item: any) => item.content.trim() === '')) return Promise.reject('选项内容都不能为空')

        return Promise.resolve()
    }
}
const rules = {
    name: [
        { required: true, message: '请输入问卷名称' },
        { max: 50, message: '问卷名称超过50个字符' }
    ],
}

const formRef = ref<FormInstance>()
/** 是否已提交 */
const isSubmitted = ref(false)
function save() {
    return new Promise(async (resolve, reject) => {
        // 设置题号
        setSurveyQuestionNumber(formState.value.content)

        // 删除选择题选项中的多余字段id
        formState.value.content.forEach((item: SurveyQuestionType) => {
            item.options?.forEach(option => {
                Reflect.deleteProperty(option, 'id')
            })
        })

        formState.value.multipleSubmit = formState.value.multipleSubmit?1:0
        formState.value.going = formState.value.going?1:0
        delete formState.value.contentStr
        try {

            if (props.mode === 'create' || props.mode === 'copy') {
                await addquestionnaire(formState.value)
            } else if (props.mode === 'edit') {
                await modifyquestionnaire(formState.value)
            }

            isSubmitted.value = true
            resolve('保存成功')

        } catch (error: any) {
            console.log('添加问卷失败 error', error)
            reject(error)
        }

    })
}

// 保存
const submitLoading = ref(false)
async function handleSave() {
    if (!formRef.value) return

    try {
        // 校验
        await formRef.value.validate(['name'])

        submitLoading.value = true
        await save()

        message.success('保存成功')
        router.back()

    } catch (error) {
        console.log('检验失败 error', error);
        scrollToFirstErrFormItem()

    } finally {
        submitLoading.value = false
    }
}

// 发布
const publishLoading = ref(false)
async function handlePublish() {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        await valideSurvey(formState.value)

        publishLoading.value = true

        try {
            Reflect.set(formState.value, 'published', true)
            await save()
        } catch (error) {
            Reflect.deleteProperty(formState.value, 'published')
        }

        message.success('发布成功')
        router.back()

    } catch (error) {
        console.log('检验失败 error', error);
        scrollToFirstErrFormItem()

        // 如果是自定义校验逻辑失败，则提示
        if (typeof error === 'string')
            message.error(error)

    } finally {
        publishLoading.value = false
    }
}

function MyExitConfirm() {
    return new Promise((resolve, reject) => {
        const modal = Modal.confirm({
            title: '是否需要保存当前问卷？',
            content: '直接退出系统不会保存当前内容',
            okText: '保存',
            cancelText: '直接退出',
            closable: true,
            onOk() {
                resolve('ok')
            },
            onCancel(e) {
                if (!e.triggerCancel) {
                    modal.destroy()
                    // 点击了“直接退出”
                    reject('cancel')
                } else {
                    reject('close')
                }
            }
        })
    })
}

const isModified = computed(() => {
    return !_.isEqual(formState.value, initialFormState.value)
})

onBeforeRouteLeave(async (to, from, next) => {

    // 如果没有改动或者已经提交了，直接放行
    if (!isModified.value || isSubmitted.value)
        return next()

    try {
        await MyExitConfirm()
        // 保存
        await save()
        next()
    } catch (error) {
        console.log(error)
        if (error === 'cancel') {
            // 直接退出
            next()
        } else {
            next(false)
        }
    }
})

</script>
<style lang="less" scoped>
.survey-form-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0 20px 20px;

    .title-wrap {
        height: 64px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }

        .btns {
            height: 32px;

            :deep(.ant-btn) {
                margin-left: 8px;
            }
        }
    }

    .form-container {
        flex: 1;
        min-height: 0;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
        padding: 24px 8px 0 24px;
        display: flex;
        flex-direction: column;

        :deep(.form) {
            flex: 1;
            min-height: 0;
            overflow: scroll;
            padding-right: 16px;

            label {
                font-size: 12px;
            }

            .ant-form-item-label>label {
                font-size: 12px;
                color: #626262;
            }

            .add-option-btn {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 82px;
                height: 24px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-radius: 4px;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.65);
                cursor: pointer;
            }

            .option-item {
                display: flex;
                align-items: center;

                .ant-radio-wrapper {
                    margin-right: -2px;
                }

                .option-radio {
                    display: inline-block;
                    width: 24px;
                }

                .ant-input {
                    border: none;
                    box-shadow: none !important;
                    width: 240px;
                    background: transparent !important;
                    cursor: text;
                }

                .del-icon {
                    opacity: 0;
                    transition: all 0.2s;
                    cursor: pointer;
                }

                &:hover .del-icon {
                    position: relative;
                    opacity: 1;
                }
            }

        }

        .form-footer {
            height: 80px;
            display: flex;
            align-items: center;

            .ant-btn {
                margin-left: 10px;
            }
        }
    }
}

:deep(.ant-collapse) {
    background: none;
    border: none;

    .ant-collapse-item {
        border: 1px solid #d9d9d9;
        border-radius: 0 !important;

        .ant-collapse-header {
            background: #f2f5fc;
        }

        &+.ant-collapse-item {
            margin-top: 24px;
        }

        .ant-collapse-content-box {
            padding-bottom: 0;
        }
    }
}

:deep(.ant-input) {
    font-size: 12px !important;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-top: 16px;
}
</style>