{
  "compilerOptions": { // 解析非相对模块名的基准目录
    "target": "esnext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": [
      "esnext",
      "dom"
    ],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "node",
    // 模块名到基于 baseUrl的路径映射的列表
    "paths": {
      "@/*": [
        "src/*"
      ]
    }, // 生成相应的 .map文件
    "resolveJsonModule": true,
    "types": [
      "vite/client"
    ],
    "strict": true,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/**/**/*.vue",
    "src/test.js"
  ]
}