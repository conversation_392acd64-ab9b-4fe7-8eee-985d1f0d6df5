<template>
    <div>
        <div v-if="list?.length === 0" class="common-no-data"></div>
        <a-spin v-else :spinning="loading">
            <div ref="chartRef" style="height: 100%;"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, watch } from 'vue'
import { quescatgtypeproportion } from '@/api/admin/statisticAnalysis'
import { ECharts, init } from 'echarts'
import type { ZRColor } from 'echarts/types/src/util/types'
import { QUESTIONS } from '@/config/constants'

const props = defineProps<{
    id?: string
}>()

// 调色板
const colorList: ZRColor[] = QUESTIONS.map((item) => ({
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color: item.color
    }, {
        offset: 1, color: item.color + '80'
    }],
    global: false
}))

// 获取数据
const list = ref<{
    name: string
    value: number
}[]>()

const loading = ref(false)
async function getList() {
    if (!props.id) return
    loading.value = true
    try {
        let ques = await quescatgtypeproportion({ categoryId: props.id}) as any
        // 将数据根据QUESTIONS的顺序进行排序
        ques = ques.sort((a: any, b: any) => QUESTIONS.findIndex((q: any) => q.label === a.name) - QUESTIONS.findIndex((q: any) => q.label === b.name))
        list.value = ques
    } finally {
        loading.value = false
    }
}

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
    // 检查图表容器是否存在
    if (!chartRef.value) return;

    // 初始化图表实例
    chart.value = init(chartRef.value);

    // 设置图表选项
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}题` // 格式化提示框中的值
        },
        grid: {
            left: 0,
            right: 0,
            top: 0 // 设置图表的网格边距
        },
        color: colorList, // 使用预定义的调色板
        series: [
            {
                type: 'pie', // 设置图表类型为饼图
                radius: '70%', // 设置饼图的半径
                minAngle: 10, // 设置最小角度，避免数据过小不可见
                label: {
                    formatter: '{b}: {d}%', // 格式化标签显示内容
                    alignTo: 'labelLine', // 标签对齐到引导线
                    bleedMargin: 0, // 防止标签溢出
                    distanceToLabelLine: 5, // 标签与引导线的距离
                },
                labelLine: {
                    length: 5, // 引导线的第一段长度
                    length2: 0, // 引导线的第二段长度
                },
                itemStyle: {
                    borderColor: 'white', // 设置分隔线颜色
                    borderWidth: 1, // 设置分隔线宽度
                },
                emphasis: {
                    itemStyle: {
                        borderWidth: 0, // 高亮时取消边框宽度
                        shadowOffsetX: 0, // 阴影偏移
                        shadowColor: 'rgba(0, 0, 0, 0.5)' // 阴影颜色
                    }
                },
                data: list.value // 设置饼图数据
            }
        ]
    });

    // 添加窗口大小变化监听器以调整图表大小
    window.addEventListener('resize', resizeChart);
}

function resizeChart() {
    chart.value?.resize()
}

watch(() => props.id, async (val) => {
    if (!val) return
    await getList()
    draw()
}, { immediate: true })

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
.ant-spin-container > div {
    // overflow: clip;
    display: flex;
    justify-content: center;
}
</style>