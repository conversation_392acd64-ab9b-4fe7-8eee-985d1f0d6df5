<template>
    <div>
        <a-spin :spinning="loading">
            <div class="sex-chart">
                <div>
                    <div v-for="item in list" class="sex-chart-row" :class="[item.name === '男' ? 'male' : 'female']">
                        <span class="label">{{ item.name }}</span>
                        <div class="wrapper">
                            <img :src="item.name === '男' ? maleImg : femaleImg" v-for="i in 5"
                                :style="{ background: calcBackground(i, item.rate, item.name) }" alt="">
                        </div>
                        <span class="rate">{{ (item.rate * 100).toFixed(2) }}%</span>
                    </div>
                </div>
                <div v-if="!loading && !list?.length" class="common-no-data"></div>
            </div>
        </a-spin>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { sexproportion } from '@/api/admin/statisticAnalysis'
import maleImg from '@/assets/images/svg/male.svg'
import femaleImg from '@/assets/images/svg/female.svg'

const list = ref<{
    name: '男' | '女'
    value: number
    rate: number
}[]>([])

const loading = ref(false)
async function getList() {
    loading.value = true
    try {
        let res = await sexproportion() as any
        let total = (res[0]?.value ?? 0) + (res[1]?.value ?? 0)
        if (total === 0) return
        res.forEach((item: any) => {
            item.rate = item.value / total
        })
        list.value = res
    } catch (error) {

    } finally {
        loading.value = false
    }
}
getList()

function calcBackground(index: number, rate: number, name: '男' | '女'): string {
    let color1 = name === '男' ? '#5478EE' : '#FABF45'
    let color2;
    if ((rate / 0.2) > index) {
        color2 = color1
    } else {
        color2 = name === '男' ? '#CDD9FF' : '#FFEDC7'
    }
    let num = (rate - (index - 1) * 0.2) * 100 * 5
    return `linear-gradient(to right, ${color1} ${num}%, ${color2} ${num}% 100%)`
}

</script>
<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}

.sex-chart {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .sex-chart-row {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .label {
            color: rgba(0, 0, 0, 0.45);
            font-size: 16px;
        }

        .wrapper {
            flex: 1;
            display: flex;
            justify-content: space-around;
        }

        &.male {
            margin-bottom: 40px;
            @color: #5478EE;

            .rate {
                color: @color;
                font-size: 14px;
                font-weight: bold;
            }
        }

        &.female {
            @color: #FABF45;

            .rate {
                color: @color;
            }
        }
    }
}
</style>