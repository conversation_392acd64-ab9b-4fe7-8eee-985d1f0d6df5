<template>
  <div
    class="cal-container"
    ref="calculatorRef"
    
  >
    <div class="calculator"
      draggable="true"
      @dragstart="handleDragStart($event)"
      @dragend="handleDragEnd($event)"
    >
      <div class="cal-header"
    
      >
        <span>科学计算器</span>
      </div>
      <div class="results">
        <input class="input" readonly v-model="current" />
      </div>
      <div class="mode" v-if="changeMode">
        <button class="button" @click.stop="press">7</button>
        <button class="button" @click.stop="press">8</button>
        <button class="button" @click.stop="press">9</button>
        <button class="button" @click.stop="press">*</button>
        <button class="button" @click.stop="press">Del</button>
        <button class="button" @click.stop="press">C</button>
        <button class="button" @click.stop="press">4</button>
        <button class="button" @click.stop="press($event)">5</button>
        <button class="button" @click.stop="press">6</button>
        <button class="button" @click.stop="press">/</button>
        <button class="button" @click.stop="press">(</button>
        <button class="button" @click.stop="press">)</button>
        <button class="button" @click.stop="press">1</button>
        <button class="button" @click.stop="press">2</button>
        <button class="button" @click.stop="press">3</button>
        <button class="button" @click.stop="press">-</button>
        <button class="button" @click.stop="press">x ²</button>
        <button class="button" @click.stop="press">±</button>
        <button class="button" @click.stop="press">0</button>
        <button class="button" @click.stop="press">.</button>
        <button class="button" @click.stop="press">%</button>
        <button class="button" @click.stop="press">+</button>
        <button class="button equal-sign" @click.stop="press">=</button>
      </div>
      <div class="mode" v-else>
        <button class="button" @click.stop="press">sin</button>
        <button class="button" @click.stop="press">cos</button>
        <button class="button" @click.stop="press">tan</button>
        <button class="button" @click.stop="press">x^</button>
        <button class="button" @click.stop="press">Del</button>
        <button class="button" @click.stop="press">C</button>
        <button class="button" @click.stop="press">log</button>
        <button class="button" @click.stop="press">ln</button>
        <button class="button" @click.stop="press">e</button>
        <button class="button" @click.stop="press">∘</button>
        <button class="button" @click.stop="press">rad</button>
        <button class="button" @click.stop="press">√</button>
        <button class="button" @click.stop="press">7</button>
        <button class="button" @click.stop="press">8</button>
        <button class="button" @click.stop="press">9</button>
        <button class="button" @click.stop="press">/</button>
        <button class="button" @click.stop="press">x ²</button>
        <button class="button" @click.stop="press">x !</button>
        <button class="button" @click.stop="press">4</button>
        <button class="button" @click.stop="press">5</button>
        <button class="button" @click.stop="press">6</button>
        <button class="button" @click.stop="press">*</button>
        <button class="button" @click.stop="press">(</button>
        <button class="button" @click.stop="press">)</button>
        <button class="button" @click.stop="press">1</button>
        <button class="button" @click.stop="press">2</button>
        <button class="button" @click.stop="press">3</button>
        <button class="button" @click.stop="press">-</button>
        <button class="button" @click.stop="press">%</button>
        <button class="button" @click.stop="press">±</button>
        <button class="button" @click.stop="press">0</button>
        <button class="button" @click.stop="press">.</button>
        <button class="button" @click.stop="press">&#x003C0;</button>
        <button class="button" @click.stop="press">+</button>
        <button class="button equal-sign" @click.stop="press">=</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const current = ref<any>('')
const changeMode = ref(false)

const press = (event: any) => {
  const key = event.target.textContent

  if (
    key != '=' &&
    key != 'C' &&
    key != '*' &&
    key != '/' &&
    key != '√' &&
    key != 'x ²' &&
    key != '%' &&
    key != 'Del' &&
    key != '±' &&
    key != 'sin' &&
    key != 'cos' &&
    key != 'tan' &&
    key != 'log' &&
    key != 'ln' &&
    key != 'x^' &&
    key != 'x !' &&
    key != 'π' &&
    key != 'e' &&
    key != 'rad' &&
    key != '∘'
  ) {
    current.value += key
  } else if (key === '=') {
    if (current.value.indexOf('^') > -1) {
      var base = current.value.slice(0, current.value.indexOf('^'))
      var exponent = current.value.slice(current.value.indexOf('^') + 1)
      current.value = eval('Math.pow(' + base + ',' + exponent + ')')
    } else {
      current.value = eval(current.value)
    }
  } else if (key === 'C') {
    current.value = ''
  } else if (key === '*') {
    current.value += '*'
  } else if (key === '/') {
    current.value += '/'
  } else if (key === '+') {
    current.value += '+'
  } else if (key === '-') {
    current.value += '-'
  } else if (key === '±') {
    if (current.value.charAt(0) === '-') {
      current.value = current.value.slice(1)
    } else {
      current.value = '-' + current.value
    }
  } else if (key === 'Del') {
    current.value = current.value.substring(0, current.value.length - 1)
  } else if (key === '%') {
    current.value = current.value / 100
  } else if (key === 'π') {
    current.value = Math.PI
  } else if (key === 'x ²') {
    current.value = eval(current.value * current.value + '')
  } else if (key === '√') {
    current.value = Math.sqrt(current.value)
  } else if (key === 'sin') {
    current.value = Math.sin(current.value)
  } else if (key === 'cos') {
    current.value = Math.cos(current.value)
  } else if (key === 'tan') {
    current.value = Math.tan(current.value)
  } else if (key === 'log') {
    current.value = Math.log10(current.value)
  } else if (key === 'ln') {
    current.value = Math.log(current.value)
  } else if (key === 'x^') {
    current.value += '^'
  } else if (key === 'x !') {
    if (current.value === 0) {
      current.value = '1'
    } else if (current.value < 0) {
      current.value = NaN
    } else {
      var _number = 1
      for (var i = current.value; i > 0; i--) {
        _number *= i
      }
      current.value = _number
    }
  } else if (key === 'e') {
    current.value = Math.E
  } else if (key === 'rad') {
    current.value = current.value * (Math.PI / 180)
  } else if (key === '∘') {
    current.value = current.value * (180 / Math.PI)
  }
}
// const changeModeEvent = () => {
//   changeMode.value = !changeMode.value
// }


// 拖拽
const calculatorRef = ref<any>(null)
  const offsetX = ref(0)
  const offsetY = ref(0)
  const handleDragStart = (e: any) => {
      offsetX.value = e.offsetX
      offsetY.value = e.offsetY
  }
  const handleDragEnd = (e: any) => {
      let x = e.pageX
      let y = e.pageY
      if (x === 0 && y === 0) return
      x -= offsetX.value
      y -= offsetY.value
      calculatorRef.value.style.left = x + 'px'
      calculatorRef.value.style.top = y + 'px'
  }

// 拖拽计算器
// const calculatorRef = ref<any>(null)
// const offsetX = ref(0)
// const offsetY = ref(0)
// const handleDragStart = (e: any) => {
//   offsetX.value = e.offsetX
//   offsetY.value = e.offsetY
// }
// const handleDragEnter = (e: any) => {
//   // let x = e.pageX
//   // let y = e.pageY
//   // if (x === 0 && y === 0) return
//   // x -= offsetX.value
//   // y -= offsetY.value
//   // if (
//   //   x < 0 ||
//   //   y < 0 ||
//   //   x > document.documentElement.clientWidth - 460 ||
//   //   y > document.documentElement.clientHeight - 360
//   // )
//   //   return
//   // calculatorRef.value.style.left = x + 'px'
//   // calculatorRef.value.style.top = y + 'px'
// }

// const handleDragEnd = (e: any) => {
//   let x = e.pageX
//   let y = e.pageY
//   // debugger
//   if (x === 0 && y === 0) return
//   x -= offsetX.value
//   y -= offsetY.value

//   // if (
//   //   x < 0 ||
//   //   y < 0 ||
//   //   x > document.documentElement.clientWidth - 460 ||
//   //   y > document.documentElement.clientHeight - 360
//   // )
//   //   return
//   calculatorRef.value.style.left = x + 'px'
//   calculatorRef.value.style.top = y + 'px'
//   console.log(calculatorRef.value)
//   console.log(calculatorRef.value.style.left, calculatorRef.value.style.top)
// }
</script>

<style lang="less" scoped>
.cal-container {
  position: absolute;
  bottom: 8%;
  right: 8%;
  width: 460px;
  height: 365px;
  // pointer-events: none;
}
.calculator {
  // width: 100%;
  // height: 100%;
  padding: 20px;
  border-radius: 5px;
  // margin: 20px auto;
  font-size: 16px;
  background-color: hsl(0, 0%, 20%);
  .cal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #fff;
    margin-bottom: 20px;
    .clock-icon {
      cursor: pointer;
    }
  }
  .input {
    width: 420px;
    height: 50px;
    border-radius: 0px;
    border: 1px solid hsl(0, 0%, 0%);
    background-color: #333333;
    color: #d9d9d9;
    padding: 0 5px 0 5px;
    margin: 0 0px 10px 0px;
    font-size: 30px;
  }

  .input:focus,
  .input:active {
    border-color: #03a9f4;
    box-shadow: 0 0 4px #03a9f4;
    outline: none 0;
  }

  .button {
    margin: 3px;
    width: 63px;
    border: 1px solid hsl(0, 0%, 5%);
    height: 30px;
    border-radius: 4px;
    color: hsl(0, 0%, 85%);
    background-color: hsl(0, 0%, 10%);
    cursor: pointer;
    outline: none;
  }

  .mode {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
  }

  .equal-sign {
    background-color: #5478ee;
    width: 133px;
  }

  .toggle-button {
    width: 100%;
    padding-bottom: 10px;
    border: none;
    background-color: hsl(0, 0%, 20%);
    cursor: pointer;
    outline: none;
    font-size: 1rem;
    color: #fff;
    // text-align: center;
    text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.35);
  }

  // p {
  //   margin-top: 0;
  // }

  button::-moz-focus-inner {
    border-color: transparent;
  }
}
</style>
