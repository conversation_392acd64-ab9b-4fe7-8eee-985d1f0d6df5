<template>
  <div :class="['paper-card', props.checked ? 'checked' : '']">
    <div class="header">
      <div class="left">
        <a-tooltip>
          <template #title>{{ paperInfo.name }}</template>
          <SearchHighLight class="title" :text="paperInfo.name" :search-text="searchText"></SearchHighLight>
        </a-tooltip>  
        <StarFilled class="focus-icon" :class="{ active: isFocus }" @click="handleFocus" />
        <div class="status" :style="statusStyles[paperInfo.status]">{{ PaperStatusList.find(el => el.value == paperInfo.status)?.label || paperInfo.status }}</div>
        <div class="operation">
          <div class="btn-wrap">
            <eye-outlined
              class="btn"
              title="查看试卷"
              style="fontSize: 14px"
              @click="handlePreviewPaper" />
          </div>
          <div :class="['btn-wrap', canEdit ? '' : 'disabled']">
            <img
              v-if="canEdit"
              class="btn"
              src="@/assets/images/paper/edit.svg"
              title="编辑试卷"
              alt=""
              @click="handleEdit">
            <img
              v-else
              class="btn"
              src="@/assets/images/paper/edit-disabled.svg"
              title="编辑试卷"
              alt=""
              @click="handleEdit">
          </div>
          <div :class="['btn-wrap', canDelete ? '' : 'disabled']">
            <img
              v-if="canDelete"
              class="btn"
              src="@/assets/images/paper/delete.svg"
              title="删除试卷"
              alt=""
              @click="handleDeletePaper">
            <img
              v-else
              class="btn"
              src="@/assets/images/paper/delete-disabled.svg"
              title="删除试卷"
              alt=""
              @click="handleDeletePaper">
          </div>
          <div class="btn-wrap">
            <img
              class="btn"
              title="克隆试卷"
              src="@/assets/images/paper/copy.png"
              alt=""
              @click="handleCopy">
          </div>
          <div class="btn-wrap">
            <img
              class="btn"
              title="再来一卷"
              src="@/assets/icons/svg/icon_onemore.svg"
              alt=""
              @click="getOneMorePaper">
          </div>
          <div class="btn-wrap">
            <img
              class="btn"
              title="模拟考试"
              src="@/assets/images/paper/mock.png"
              alt=""
              @click="imitationExam">
          </div>
          <div :class="['btn-wrap', props.paperInfo.createBy ? '' : 'disabled']"  @click="handleSharePaper">
            <img title="共享试卷" class="btn" v-if="props.paperInfo.createBy && isShared" width="14" src="@/assets/icons/svg/shared.svg" alt="">
            <share-alt-outlined v-else class="btn" title="共享试卷" :style="{ fontSize: '14px', color: props.paperInfo.createBy ? '#1C1C1C' : '#C1C1C1' }" />
          </div>
        </div>
      </div>
      <check-circle-filled class="check" @click="handleChangeCheck(false)" v-if="props.checked" style="font-size: 16px; color: rgba(84,120,238,1)" />
      <div :class="['circle', 'check', canDelete ? '' : 'disabled']" @click="handleChangeCheck(true)" v-else></div>
    </div>
    <div class="content">
      <div class="left">
        <div class="item">
          <div class="label">考试时间：</div>
          <div class="value">{{ dayjs(paperInfo.startTime).format('YYYY-MM-DD HH:mm') }} 至 {{ dayjs(paperInfo.endTime).format('YYYY-MM-DD HH:mm') }}</div>
        </div>
        <div class="item">
          <div class="label">考试时长：</div>
          <div class="value">{{ paperInfo.duration }}分钟</div>
        </div>
        <div class="item">
          <div class="label">卷面总分：</div>
          <div class="value">{{ paperInfo.score }}分</div>
        </div>
      </div>
      <div class="right">
        <div class="item">
          <div class="value">{{ paperInfo.candidatesCount || 0 }}</div>
          <div class="label">应考人数</div>
        </div>
        <div class="item" v-if="[PaperStatus.DURING_EXAMS, PaperStatus.OVER].includes(paperInfo.status)">
          <div class="value">{{ paperInfo.actualCount || 0 }}</div>
          <div class="label">实考人数</div>
        </div>
        <div class="item" v-if="paperInfo.status === PaperStatus.DURING_EXAMS">
          <div class="value">{{ paperInfo.completedCount || 0 }}</div>
          <div class="label">交卷人数</div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="create" :title="`${props.paperInfo.teacherName}于${ dayjs(paperInfo.createTime).format('YYYY-MM-DD HH:mm') }创建`"><SearchHighLight class="author" :text="paperInfo.teacherName" :search-text="searchText"></SearchHighLight>于{{ dayjs(paperInfo.createTime).format('YYYY-MM-DD HH:mm') }}创建</div>
      <div class="operation">
        <div class="btn" v-if="paperInfo.status !== PaperStatus.OVER" @click="allocatePaper">关联考生</div>
        <div class="btn" v-if="paperInfo.status !== PaperStatus.OVER" @click="sendEmail">邮件邀请</div>
        <div class="btn" v-if="paperInfo.status === PaperStatus.DURING_EXAMS && hasWrite" @click="handleExtendPaperTime">延长考试</div>
        <div class="btn" v-if="paperInfo.status !== PaperStatus.NOT_START" @click="handleViewStuGrade">查看成绩</div>
        <div class="btn invite" v-if="paperInfo.status !== PaperStatus.OVER">
          <div class="label">邀请码</div>
          <img class="icon" src="@/assets/images/paper/down.png" alt="">
          <div class="extend-wrap">
            <div class="extend-list">
              <div :class="['extend-item', hasWrite ? '' : 'disabled']" @click="handleCreatePaperCode">创建考试邀请码</div>
              <div class="extend-item" @click="handleViewPaperCodeList">查看考试邀请码</div>
            </div>
          </div>
        </div>
        <div class="btn" v-if="paperInfo.status === PaperStatus.OVER" @click="handleToAnalysis">考试分析</div>
      </div>
    </div>
    <div class="type" v-if="paperInfo.uniexam === PaperType.NOT_UEC">非统考</div>
  </div>
  <!-- 延长考试 -->
  <ExtendPaperTime
    :paper="props.paperInfo.id"
    :papername="props.paperInfo.name"
    :extendPaperModalVisible="extendPaperModalVisible"
    @closeModal="extendPaperModalVisible = false" />
  <!-- 预览试卷 -->
  <PreviewPaper
    print
    showConfig
    :paperInfo="props.paperInfo"
    :isVisible="detailTopicIsVisible"
    :data="selectedQuestion"
    @closePreviewPaper="detailTopicIsVisible = false" />
  <!-- 创建考试邀请码 -->
  <CreateInvitationCode
    v-if="invitationCodeModalVisible"
    :paper="props.paperInfo.id"
    :templeteUUID="props.paperInfo.templeteUUID"
    :validtime="[props.paperInfo.startTime, props.paperInfo.endTime]"
    :papername="props.paperInfo.name"
    :uniexam="props.paperInfo.uniexam"
    :duration="props.paperInfo.duration"
    :limitlateness="props.paperInfo.limitlateness"
    :invitationCodeModalVisible="invitationCodeModalVisible"
    @closeModal="invitationCodeModalVisible = false"
    @getPaperInvitationCode="getPaperInvitationCode" />
  <!-- 查看考试邀请码 -->
  <InvitationCode
    v-if="papercodeListVisible"
    :invitationCodeModalVisible="papercodeListVisible"
    :paper="props.paperInfo.templeteUUID"
    :papername="props.paperInfo.name"
    :hiddenAction="!hasWrite"
    @closeModal="papercodeListVisible = false" />
  <SharePaper
    v-model:isShared="isShared"
    :visible="sharePaperVisible"
    :paperId="props.paperInfo.id"
    :templeteUUID="props.paperInfo.templeteUUID"
    :paperName="props.paperInfo.name"
    :teacher="props.paperInfo.teacherUUID"
    @close="sharePaperVisible = false" />
</template>

<script setup lang="ts">
import { ref, computed, createVNode } from 'vue'
import {
  EyeOutlined,
  ShareAltOutlined,
  CheckCircleFilled,
  InfoCircleOutlined,
  InfoCircleFilled,
  StarFilled,
  ExclamationCircleFilled
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import clipboard3 from 'vue-clipboard3'
import dayjs from 'dayjs'
import SearchHighLight from '@/components/SearchHighLight.vue';
import {
  PaperStatus,
  PaperStatusList,
  PaperType,
} from '@/models/paperModel'
import { delTestPaper, mockexam, beforeclonepaper, addpaperfocus, delpaperfocus } from '@/api/admin/paperManage'
import { queryPaperQuestions } from '@/api/admin/questionManage'
import ExtendPaperTime from '@/views/PaperManage/components/ExtendPaperTime.vue'
import PreviewPaper from '@/views/PaperManage/components/PreviewPaper.vue'
import CreateInvitationCode from '@/views/PaperManage/components/CreateInvitationCode.vue'
import InvitationCode from '@/views/PaperManage/components/InvitationCode.vue'
import SharePaper from './SharePaper.vue'

const router = useRouter()
const props = defineProps<{
  paperInfo: any // 考试信息
  checked?: boolean // 选中
  searchText: string
}>()
const emits = defineEmits(['changeCheck', 'deletePaperOk'])

const hasWrite = computed(() => (!!props.paperInfo.permission && props.paperInfo.permission.indexOf('write') !== -1) || props.paperInfo.createBy)

const statusStyles = { // 考试状态样式
  [PaperStatus.NOT_START]: {
    backgroundColor: '#f3f7f1',
    color: '#2F8C00',
  },
  [PaperStatus.DURING_EXAMS]: {
    backgroundColor: '#fcf0f0',
    color: '#D71310',
  },
  [PaperStatus.OVER]: {
    backgroundColor: '#f5f5f5',
    color: '#626262',
  },
}

// 再来一卷
function getOneMorePaper(record: any) {
  if (props.paperInfo.show_type === '1') {
    message.warning('很抱歉，当前版本暂不支持逐题考试类型，再来一卷失败，请您重新创建考试')
    return
  }
  router.push({ name: 'oneMorePaper', query: { id: props.paperInfo.id } })
}

const allocatePaper = () => { // 关联考生
  router.push({ name: 'paperAllocate', query: { id: props.paperInfo.id, UUID: props.paperInfo.templeteUUID, name: props.paperInfo.name, hiddenAction: hasWrite.value ? 'false' : 'true' } })
}

const sendEmail = () => { // 发送邮箱邀请
  router.push({ name: 'paperEmailSend', query: { id: props.paperInfo.id, UUID: props.paperInfo.templeteUUID, name: props.paperInfo.name, hiddenAction: hasWrite.value ? 'false' : 'true' } })
}

// 延长考试
const extendPaperModalVisible = ref(false)
const handleExtendPaperTime = () => {
  extendPaperModalVisible.value = true
}

// 查看考试
const detailTopicIsVisible = ref(false)
const selectedQuestion = ref({})
const handlePreviewPaper = async () => {
  try {
    const res: any = await queryPaperQuestions({ id: props.paperInfo.id })
    res.fbody = res.fbodyJson
    res.ojlan = res.ojlanList
    selectedQuestion.value = res
    detailTopicIsVisible.value = true
  } catch (error) {
    selectedQuestion.value = {}
    // do nothing
  }
}

// 编辑考试
const canEdit = computed(() => { // 考试前3分钟不允许编辑
  if (props.paperInfo.name === '考前流程模拟') return true
  if (!hasWrite.value) return false
  return new Date(props.paperInfo.startTime).getTime() - Date.now() - 3 * 60 * 1000 > 0
})
const handleEdit = () => {
  if (props.paperInfo.show_type === '1') {
    message.warning('很抱歉，当前版本暂不支持逐题考试类型，编辑失败，请您重新创建考试')
    return
  }

  if (props.paperInfo.name === '考前流程模拟') {
    router.push({ name: 'editPaper', query: { id: props.paperInfo.id } })
    return
  }
  if (!hasWrite.value) {
    message.info('很抱歉，因考试创建人未授予您编辑权限，您暂时无法编辑考试')
    return
  }
  if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    message.info('考试中禁止编辑')
    return
  }
  if (props.paperInfo.status === PaperStatus.OVER) {
    message.info('考试结束后禁止编辑')
    return
  }
  if (new Date(props.paperInfo.startTime).getTime() - Date.now() - 3 * 60 * 1000 <= 0) {
    message.info('考试开始前3分钟禁止编辑')
    return
  }
  router.push({ name: 'editPaper', query: { id: props.paperInfo.id } })
}

// 判断试题是否在9月之后创建的
function isCreateAfter(createDate: any, date = '2023-09-01') {
  return dayjs(createDate).isAfter(date)
}
// 克隆试卷
const handleCopy = async () => {
  if (props.paperInfo.show_type === '1') {
    message.warning('很抱歉，当前版本暂不支持逐题考试类型，克隆失败，请您重新创建考试')
    return
  }

  if (isCreateAfter(props.paperInfo.create_at)) {
    try {
      let hasDeletedQuestions = await beforeclonepaper({ templeteId: props.paperInfo.id })
      if (!hasDeletedQuestions) {
        router.push({ name: 'copyPaper', query: { id: props.paperInfo.id } })
      } else {
        let h = createVNode
        Modal.info({
          title: '试卷内容变更提醒',
          content: '因部分题目被删除，试卷内容已自动更新。',
          icon: () => h(InfoCircleOutlined),
          onOk() {
            router.push({ name: 'copyPaper', query: { id: props.paperInfo.id } })
          },
          okText: '确认'
        })
      }
    } catch (error) {
      // message.error('克隆出错，请联系管理员')
    }
  } else {
    message.warning(
      '由于系统更新，暂时无法兼容此试卷的格式。克隆失败，给您造成不便，请谅解。请您重新创建试卷。'
    )
  }
}

// 模拟考试
const imitationExam = async () => {
  let baseUrl: string
  if (location.href.includes('localhost') || location.href.includes('192.168')) {
    // baseUrl = 'https://localhost:9001/'
    baseUrl = 'https://*************:9001/'
  } else if (location.href.includes('teacher-preview')) {
    baseUrl = 'https://preview.exam.isrc.ac.cn/'
  } else if (location.href.includes('teacher-dev')){
    baseUrl = 'https://dev.exam.isrc.ac.cn/'
  } else {
    baseUrl = 'https://exam.isrc.ac.cn/'
  }
  try {
    const res: any = await mockexam({ templeteId: props.paperInfo.id })
    window.open(
      `${baseUrl}#/pretest?id=${res.stupid}&stuid=${res.stuid}&token=${res.access_token}&stuname=${res.stuname}`,
      '_blank'
    )
  } catch (error) {
    // do nothing
  }
}

// 创建人 && 非考试中状态 可删除试卷
const canDelete = computed(() => {
  return props.paperInfo.createBy && props.paperInfo.status !== PaperStatus.DURING_EXAMS
})
// 选中or取消选中，emit事件
const handleChangeCheck = (check: boolean) => {
  if (!check) {
    emits('changeCheck')
    return
  }
  if (!props.paperInfo.createBy) {
    message.info('很抱歉，您不是此考试的创建人，所以无法进行删除操作')
    return
  }
  if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    message.info('考试中禁止删除')
    return
  }
  emits('changeCheck')
}
// 删除试卷
const handleDeletePaper = async () => {
  if (!props.paperInfo.createBy) {
    message.info('很抱歉，您不是此考试的创建人，所以无法进行删除操作')
    return
  }
  if (props.paperInfo.status === PaperStatus.DURING_EXAMS) {
    message.info('考试中禁止删除')
    return
  }
  let h = createVNode
  Modal.confirm({
    title: '确定删除该考试？',
    icon:()=>h(ExclamationCircleFilled),
    async onOk() {
      try {
        await delTestPaper({  ids: [props.paperInfo.id] })
        message.success('删除成功！')
        emits('deletePaperOk')
      } catch (error) {
        // do nothing
      }
    }
  })
}

// 查看考试成绩
const handleViewStuGrade = () => {
  router.push({
    name: 'examManagement',
    query: {
      id: props.paperInfo.id,
      UUID: props.paperInfo.templeteUUID,
      name: props.paperInfo.name,
      hiddenPublish: hasWrite.value || props.paperInfo.createBy ? 'false' : 'true',
    }
  })
}

// 创建考试邀请码
const invitationCodeModalVisible = ref(false)
const handleCreatePaperCode = () => {
  if (!hasWrite.value) {
    message.info('很抱歉，因考试创建人未授予您编辑权限，您暂时无法创建考试邀请码')
    return
  }
  // 非统考
  const NOT_UEC = props.paperInfo.uniexam === PaperType.NOT_UEC
  const baseTime = NOT_UEC ? props.paperInfo.endTime : props.paperInfo.startTime
  if (dayjs(baseTime).isBefore(dayjs())) {
    message.info('考试已经开始，不能创建考试邀请码')
    return
  }
  invitationCodeModalVisible.value = true
}
const { toClipboard } = clipboard3()
const getPaperInvitationCode = (code: string) => { // 生成考试邀请码成功回调
  invitationCodeModalVisible.value = false
  const text = `${code}`
  Modal.confirm({
    title: () => '提示',
    icon: () => createVNode(InfoCircleFilled),
    content: () => `你已成功生成考试邀请码：${code}`,
    width: '450px',
    okText: '复制',
    cancelText: '关闭',
    onOk: () => {
      toClipboard(text)
      message.info('考试邀请码已成功复制至剪切板!')
    }
  })
}

// 查看考试邀请码
const papercodeListVisible = ref(false)
const handleViewPaperCodeList = () => {
  papercodeListVisible.value = true
}

// 跳转考试分析页面
const handleToAnalysis = () => {
  router.push({
    name: 'AnalysisDetail',
    params: {
      id: props.paperInfo.id,
    },
    query: {
      name: props.paperInfo.name,
    }
  })
}

// 共享试卷
const isShared = ref(props.paperInfo.is_shared)
const sharePaperVisible = ref(false)
const handleSharePaper = () => {
  if (!props.paperInfo.createBy) {
    message.warning('很抱歉，您不是此考试的创建人，所以无法进行共享操作')
    return
  }
  sharePaperVisible.value = true
}

// 收藏
const isFocus = ref<boolean>(props.paperInfo.focus)
async function handleFocus() {
  isFocus.value = !isFocus.value
  try {
    if (isFocus.value) {
      // 收藏
      await addpaperfocus({ templeteId: props.paperInfo.id })
    } else {
      // 取消收藏
      await delpaperfocus({ id: props.paperInfo.focusId })
    }
  } catch (error) {
    isFocus.value = !isFocus.value
  }
}

</script>

<style lang="less" scoped>
@import '../../../styles/mixin.less';
.paper-card {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px #0000001a;
  transition: all .3s ease;
  border: 1px solid #fff;
  box-sizing: border-box;
  position: relative;
  user-select: none;
  &.checked {
    border: 1px solid #5478ee;
  }
  .header {
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      min-width: 0;
      flex: auto;
      display: flex;
      align-items: center;
      .title {
        min-width: 0;
        height: 24px;
        font-size: 18px;
        font-weight: 600;
        color: rgba(0,0,0,0.85);
        text-align: left;
        line-height: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .focus-icon {
        margin-left: 8px;
        cursor: pointer;
        color: #D8D8D8;
        &.active {
          color: #FAAD14;
        }
      }
      .status {
        flex: none;
        width: 44px;
        height: 18px;
        margin-left: 8px;
        background: #fcf0f0;
        color: #d71310;
        border-radius: 4px;
        font-size: 12px;
        font-weight: Regular;
        text-align: center;
        line-height: 18px;
      }
      .operation {
        height: 24px;
        margin-left: 16px;
        align-items: center;
        display: none;
        .btn-wrap {
          width: 24px;
          height: 24px;
          margin-left: 8px;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:hover {
            background: #f5f5f5;
          }
          &:first-child {
            margin-left: 0;
          }
          .btn {
            flex: none;
            width: 14px;
            height: 14px;
            object-fit: contain;
            cursor: pointer;
          }
          &.disabled {
            &:hover {
              background: #fff;
            }
            .btn {
              cursor: not-allowed;
            }
          }
        }
      }
    }
    .check {
      flex: none;
      margin-left: 16px;
      cursor: pointer;
      &.circle {
        width: 16px;
        height: 16px;
        border: 1px solid rgba(217,217,217,1);
        background: #fff;
        border-radius: 50%;
        display: none;
        &:hover {
          border-color: rgba(84,120,238,1);
        }
      }
      &.disabled {
        cursor: not-allowed;
        &:hover {
          border-color: rgba(217,217,217,1);
        }
      }
    }
  }
  .content {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    .left {
      .item {
        height: 18px;
        margin-bottom: 8px;
        display: flex;
        .label {
          height: 18px;
          color: rgba(0,0,0,0.45);
          font-size: 12px;
          line-height: 18px;
        }
        .value {
          height: 18px;
          color: #181818;
          font-size: 12px;
          line-height: 18px;
        }
      }
    }
    .right {
      display: flex;
      justify-content: flex-end;
      .item {
        margin-right: 24px;
        &:last-child {
          margin-right: 0;
        }
        .value {
          height: 28px;
          font-size: 24px;
          font-weight: 600;
          color: #5478ee;
          text-align: center;
          line-height: 28px;
        }
        .label {
          height: 18px;
          margin-top: 4px;
          font-size: 12px;
          color: rgba(0,0,0,0.45);
          text-align: center;
          line-height: 18px;
        }
      }
    }
  }
  .bottom {
    height: 18px;
    display: flex;
    justify-content: space-between;
    .create {
      min-width: 0;
      flex: 1;
      height: 18px;
      font-size: 12px;
      text-align: left;
      color: rgba(0,0,0,0.45);
      line-height: 18px;
      .ellipsis();
    }
    .operation {
      flex: none;
      margin-left: 10px;
      display: flex;
      justify-content: flex-end;
      .btn {
        height: 18px;
        margin-left: 8px;
        padding-right: 8px;
        font-size: 12px;
        color: #5478ee;
        line-height: 18px;
        position: relative;
        cursor: pointer;
        &::after {
          display: block;
          content: '';
          width: 1px;
          height: 12px;
          background: #dfe1e6;
          position: absolute;
          top: 0;
          bottom: 0;
          margin: auto;
          right: 0;
        }
        &:last-child {
          padding-right: 0;
          &::after {
            display: none;
          }
        }
      }
      .invite {
        display: flex;
        align-items: center;
        position: relative;
        .label {
          font-size: 12px;
          height: 18px;
          line-height: 18px;
        }
        .icon {
          width: 16px;
          height: 16px;
          object-fit: contain;
          margin-left: 4px;
        }
        .extend-wrap {
          position: absolute;
          top: 18px;
          right: 0;
          width: 100px;
          height: 70px;
          padding-top: 6px;
          z-index: 2;
          display: none;
          .extend-list {
            background: #ffffff;
            border-radius: 2px;
            box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.10);
            .extend-item {
              height: 32px;
              font-size: 12px;
              text-align: center;
              color: rgba(0,0,0,0.84);
              line-height: 32px;
              &:hover {
                background: #f1f4fe;
                color: #5478EE;
              }
              &.disabled {
                cursor: not-allowed;
                &:hover {
                  background: #ffffff;
                  color: rgba(0,0,0,0.84);
                }
              }
            }
          }
        }
        &:hover {
          .extend-wrap {
            display: block;
          }
        }
      }
    }
  }
  .type {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 60px;
    height: 20px;
    background: linear-gradient(90deg,#e8eeff, #cdd9ff);
    border-radius: 0px 8px 0px 8px;
    font-size: 12px;
    text-align: center;
    color: #5478ee;
    line-height: 20px;
  }
  &:hover {
    box-shadow: 0px 6px 18px 0px rgba(0,0,0,0.10);
    .header {
      .operation {
        display: flex;
      }
      .circle {
        display: block;
      }
    }
  }
}

</style>
<style lang="less">
.ant-modal .ant-modal-body .ant-modal-confirm-body .anticon-info-circle {
    color: #faad14;
}
</style>
