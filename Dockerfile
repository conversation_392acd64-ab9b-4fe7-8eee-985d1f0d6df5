# FROM swr.cn-north-4.myhuaweicloud.com/iscas-isrc/front-base:latest AS build
# FROM node:latest AS build
# WORKDIR /source
# COPY . /source
# RUN npm install && npm run build
# RUN cp -r dist /opt/exam

# FROM nginx:latest
# COPY --from=build /opt/exam /usr/share/nginx/html
# ENV LC_ALL en_US.UTF-8
# ENV LANG en_US.UTF-8

# FROM swr.cn-north-4.myhuaweicloud.com/iscas-isrc/front-base:latest AS build
# WORKDIR /source
# FROM nginx:alpine
# COPY dist /usr/share/nginx/html
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# ENV LC_ALL en_US.UTF-8
# ENV LANG en_US.UTF-8


FROM nginx
COPY dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]