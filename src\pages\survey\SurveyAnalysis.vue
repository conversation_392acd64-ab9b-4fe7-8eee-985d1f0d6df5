<template>
    <div class="survey-analysis-page">
        <div class="common-page-title-wrapper">
            <div class="title">
                <span @click="router.push('/admin/surveyManage')">问卷管理 / </span>
                <span>问卷分析《{{ surveyName }}》</span>
            </div>
            <div class="btns">
                <a-button type="primary" @click="handleDownload">下载</a-button>
            </div>
        </div>
        <nav class="nav-wrapper">
            <RouterLink class="nav-item" :to="`/admin/surveyAnalysis/chart?id=${route.query.id}`">
                <span>数据统计</span>
            </RouterLink>
            <RouterLink class="nav-item" :to="`/admin/surveyAnalysis/table?id=${route.query.id}`">
                <span>问卷报表</span>
            </RouterLink>
        </nav>
        <router-view class="router-view"></router-view>
    </div>
</template>
<script lang="ts" setup>
import { downloadquestionnairedata, questionnairedetail } from '@/api/admin/survey';
import { downloadFile } from '@/utils/common';
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const surveyName= ref('')
async function getDetail() {
    try {
        let { name } = await questionnairedetail({ id: route.query.id as string })
        surveyName.value = name
        
    } catch (error) {
        console.log(error)
    }
}
getDetail()

async function handleDownload() {
    try {
        let res = await downloadquestionnairedata({
            id: route.query.id
        })
        if (!res) return
        downloadFile(res)
    } catch (error) {
        console.log(error)
    }
}

</script>
<style lang="less" scoped>
.survey-analysis-page {
    height: 100%;
    display: flex;
    flex-direction: column;

    .nav-wrapper {
        margin-left: 20px;
        display: flex;
        gap: 16px;

        .nav-item {
            display: inline-block;
            width: 130px;
            height: 40px;
            background: #ffffff;
            border: 1px solid #e4e7ee;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            position: relative;

            &.router-link-exact-active {
                background: rgba(84, 120, 238, 0.10);

                span {
                    /* 设置字体透明 */
                    color: transparent;
                    /* 设置线性渐变，从红色渐变到蓝色 */
                    background-image: linear-gradient(270deg, #5dedf7 0%, #5491ff 29%, #835aff 62%, #8b54ff 100%);
                    /* 使用 -webkit-background-clip 属性将背景剪裁至文本形状 */
                    -webkit-background-clip: text;
                    /* 非Webkit内核浏览器需要使用标准前缀 */
                    background-clip: text;
                }

                &::before {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    background-color: #fff;
                    display: inline-block;
                    width: 24px;
                    height: 2px;
                    background: linear-gradient(270deg, #5dedf7 0%, #5491ff 29%, #835aff 62%, #8b54ff 100%);
                }
            }
        }
    }

    .router-view {
        flex: 1;
        min-height: 0
    }
}
</style>