<script setup lang="ts">
import { ref } from 'vue'

const candidates = ref([
  { id: 1, name: '张三三', age: 24, education: '本科', school: '清华大学', position: '尚可', status: '待定', selected: false },
  { id: 2, name: '张三三', age: 24, education: '本科', school: '清华大学', position: '尚可', status: '待定', selected: true },
  { id: 3, name: '张三三', age: 24, education: '本科', school: '清华大学', position: '尚可', status: '未筛选', selected: false },
  { id: 4, name: '张三三', age: 24, education: '本科', school: '清华大学', position: '尚可', status: '未筛选', selected: false },
  { id: 5, name: '张三三', age: 24, education: '本科', school: '清华大学', position: '尚可', status: '待定', selected: false },
  { id: 6, name: '张三三', age: 30, education: '本科', school: '清华大学', position: '尚可', status: '待定', selected: false },
  { id: 7, name: '张三三', age: 30, education: '硕士', school: '北京大学', position: '胜任', status: '待定', selected: false },
  { id: 8, name: '张三三', age: 30, education: '硕士', school: '北京大学', position: '卓越', status: '待定', selected: false },
  { id: 9, name: '张三三', age: 30, education: '硕士', school: '北京大学', position: '卓越', status: '待定', selected: false },
  { id: 10, name: '张三三', age: 30, education: '硕士', school: '北京大学', position: '卓越', status: '待定', selected: false },
])

const searchText = ref('')
const currentPage = ref(3)
const pageSize = ref(10)
const totalCount = ref(153)

const statistics = ref([
  { icon: '🔽', value: 1122, label: '待筛选', color: 'text-blue-600' },
  { icon: '👤', value: 50, label: '人面试', color: 'text-purple-600' },
  { icon: '🌲', value: 50, label: '人面试', color: 'text-green-600' },
  { icon: '📄', value: 50, label: '混合考试', color: 'text-blue-600' },
  { icon: '📝', value: 11, label: '笔试', color: 'text-purple-600' },
  { icon: '💚', value: 5, label: '心理测评', color: 'text-green-600' },
  { icon: '📊', value: 5, label: '测试', color: 'text-red-600' },
  { icon: '📋', value: 2233, label: '已录', color: 'text-blue-600' },
])

const selectAll = ref(false)
const selectedRows = ref([])

function tableRowClassName(rowInfo: any) {
  const { row, rowIndx } = rowInfo
  return row.selected ? 'selected-row' : ''
}

function handleSelectionChange(selection: any) {
  selectedRows.value = selection
}

function handleScreenResume(row: any) {
  console.log('筛选简历', row)
}

function handleProcessManage(row: any) {
  console.log('流程管理', row)
}

function handleDelete(row: any) {
  console.log('删除', row)
}

function toggleSelectAll() {
  candidates.value.forEach((candidate) => {
    candidate.selected = selectAll.value
  })
}

function handleCandidateSelect() {
  selectAll.value = candidates.value.every(c => c.selected)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-2">
        <span class="text-gray-500">让位</span>
        <h1 class="text-xl font-medium text-gray-900">
          Java前端工程师
        </h1>
      </div>
      <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
        编辑流程
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-8 gap-4 mb-6">
      <div
        v-for="(stat, index) in statistics"
        :key="index"
        class="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <span class="text-lg">{{ stat.icon }}</span>
            </div>
            <div>
              <div class="text-2xl font-semibold" :class="stat.color">
                {{ stat.value }}
              </div>
              <div class="text-sm text-gray-500">
                {{ stat.label }}
              </div>
            </div>
          </div>
          <button v-if="index < 7" class="text-gray-400 hover:text-gray-600">
            →
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <div class="relative">
            <input
              v-model="searchText"
              type="text"
              placeholder="请输入姓名"
              class="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
            >
            <button class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            筛选简历
          </button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50">
            添加候选人
          </button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50">
            批量删除
          </button>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-lg shadow-sm">
      <el-table
        :data="candidates"
        style="width: 100%"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        
        <el-table-column
          prop="name"
          label="姓名"
          width="120"
        />
        <el-table-column
          prop="age"
          label="年龄"
          width="80"
          sortable
        />
        <el-table-column
          prop="education"
          label="最高学历"
          width="100"
          sortable
        />
        <el-table-column
          prop="school"
          label="毕业学校"
          width="120"
        />
        <el-table-column
          prop="position"
          label="岗位匹配度"
          width="120"
          sortable
        />
        <el-table-column
          prop="status"
          label="筛选结果"
          width="100"
          sortable
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '待定' ? 'warning' : 'success'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="220"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleScreenResume(scope.row)"
            >
              筛选简历
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleProcessManage(scope.row)"
            >
              流程管理
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- Pagination -->
      <div class="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <el-select
            v-model="pageSize"
            placeholder="选择"
            size="small"
            style="width: 80px"
          >
            <el-option label="10" :value="10" />
            <el-option label="20" :value="20" />
            <el-option label="50" :value="50" />
          </el-select>
          <span class="text-sm text-gray-700">条/页</span>
        </div>
        
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="false"
          :background="true"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>