<script setup lang="ts">
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue'
import { InfoFilled } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

const emits = defineEmits(['focusItem'])
const contentList = defineModel<any>()

// 高亮状态管理
const highlightedItemId = ref<string | null>(null)

// 点击项目定位并高亮
function handleItemClick(item: any) {
  return emits('focusItem', item)
}

// 查找目标元素
function findTargetElement(item: any) {
  // 根据不同类型查找对应的DOM元素
  if (item.type === 'pkg') {
    // 查找题库元素
    return document.querySelector(`[data-pkg-id="${item.id}"]`)
  }
  else if (item.type === 'question') {
    // 查找题目元素
    return document.querySelector(`[data-question-id="${item.id}"]`)
  }
  else if (item.type === 'manual') {
    // 查找手动输入题目元素
    return document.querySelector(`[data-manual-id="${item.id}"]`)
  }
  return null
}

// 计算总体量
const totalVolume = computed(() => {
  if (!contentList.value || !Array.isArray(contentList.value)) {
    return 0
  }

  return contentList.value.reduce((total: number, item: any) => {
    if (item.type === 'pkg') {
      // 如果是题库，体量为 extractNum
      return total + (Number(item.extractNum) || 0)
    }
    else if (item.type === 'question' || item.type === 'manual') {
      // 如果是单个题目或手动输入题目，体量为 1
      return total + 1
    }
    return total
  }, 0)
})

// 根据题目数量判断状态和提示
const volumeStatus = computed(() => {
  const volume = totalVolume.value
  if (volume < 10) {
    return {
      color: 'red',
      message: '题目数量过少可能影响面试效果',
      showWarning: true,
    }
  }
  else if (volume > 20) {
    return {
      color: 'red',
      message: '题目数量过多可能影响面试时长',
      showWarning: true,
    }
  }
  else {
    return {
      color: 'green',
      message: '题目数量合适',
      showWarning: false,
    }
  }
})

// 计算题目 tag 在进度条上的位置（left 百分比）
const tagPosition = computed(() => {
  const volume = totalVolume.value

  // 进度条布局：左侧25% (0-10题) + 中间48% (10-20题) + 右侧25% (20+题)
  // 最大值：30题（用于计算比例）
  const maxVolume = 30
  const recommendMin = 10
  const recommendMax = 20

  let leftPercent = 0

  if (volume <= recommendMin) {
    // 在左侧区域 (0-25%)
    const ratio = Math.min(volume / recommendMin, 1)
    leftPercent = ratio * 25
  }
  else if (volume <= recommendMax) {
    // 在推荐区域 (25%-73%)
    const ratio = (volume - recommendMin) / (recommendMax - recommendMin)
    leftPercent = 25 + ratio * 48
  }
  else {
    // 在右侧区域 (73%-100%)
    const ratio = Math.min((volume - recommendMax) / (maxVolume - recommendMax), 1)
    leftPercent = 73 + ratio * 25
  }

  // 确保在合理范围内，并留出 tag 宽度的一半空间
  return Math.max(5, Math.min(leftPercent, 95))
})

// 计算提示标签的位置和样式
const warningTagStyle = computed(() => {
  const volume = totalVolume.value
  const tagPos = tagPosition.value

  if (volume < 10) {
    // 题目过少，提示标签出现在主标签右边
    return {
      left: `${Math.min(tagPos + 15, 85)}%`, // 主标签右边15%的位置，但不超过85%
      transform: 'translateX(0)', // 不居中，从左边开始
    }
  }
  else if (volume > 20) {
    // 题目过多，提示标签出现在主标签左边
    return {
      left: `${Math.max(tagPos - 15, 15)}%`, // 主标签左边15%的位置，但不少于15%
      transform: 'translateX(-100%)', // 从右边开始，完全显示在左侧
    }
  }
  else {
    // 题目数量合适，不显示警告（这种情况下 showWarning 为 false）
    return {
      left: '50%',
      transform: 'translateX(-50%)',
    }
  }
})
// 监听 contentList 变化，用于调试（可选）
// watch(() => contentList.value?.length, () => {
//   console.log('contentList changed:', contentList.value)
//   console.log('totalVolume:', totalVolume.value)
// })
</script>

<template>
  <div v-if="contentList.length" class="dminw ml-[16px] container flex flex-wrap h-full overflow-hidden box-border">
    <div class="h-[90%] overflow-auto w-full">
      <VueDraggable
        v-model="contentList"
        :animation="150"
        handle=".handle"
        class="w-full"
      >
        <div
          v-for="(item) in contentList"
          :key="item.id"
          class="content-item pl-[8px] flex relative items-center justify-between border-[1px] border-solid border-[rgba(0,0,0,0.15)] w-full mb-[8px] px-[16px] py-[8px] transition-all duration-300"
          :class="{
            highlighted: highlightedItemId === item.id,
          }"
          @click="handleItemClick(item)"
        >
          <span class="w-[92%] text-ellipsis overflow-hidden whitespace-nowrap" :class="{ 'text-[#ff4d4f]': !item.name && !item.configSchema.content }">
            {{ item.name || item.configSchema.content || '未填写题干' }}
          </span>
          <svg class="sort-icon handle" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><!-- Icon from Lets Icons by Leonid Tsvetkov - https://creativecommons.org/licenses/by/4.0/ --><g fill="none"><path stroke="#888888" stroke-linejoin="round" d="M5 8v8" /><path fill="#888888" d="M5.224 4.447L6.71 7.421A.4.4 0 0 1 6.353 8H3.647a.4.4 0 0 1-.358-.579l1.487-2.974a.25.25 0 0 1 .448 0m0 15.106l1.487-2.974A.4.4 0 0 0 6.353 16H3.647a.4.4 0 0 0-.358.579l1.487 2.974a.25.25 0 0 0 .448 0" /><path stroke="#888888" stroke-linecap="round" d="M11 8h8m-8 4h8m-8 4h8" /></g></svg>
        </div>
      </VueDraggable>
    </div>

    <div class="w-full h-[6px] flex flex-nowrap justify-between relative">
      <!-- 动态定位的题目数量标签 -->
      <div class="w-full absolute h-[20px] top-[-40px]">
        <div class="absolute" :style="{ left: `${tagPosition}%`, transform: 'translateX(-50%)' }">
          <a-tag :bordered="false" :color="volumeStatus.color" class="relative">
            {{ totalVolume }}题
            <CaretDownOutlined class="absolute left-[35%] top-[100%]" />
          </a-tag>
        </div>
        <!-- 警告提示标签（动态位置） -->
        <div
          v-if="volumeStatus.showWarning"
          class="absolute"
          :style="warningTagStyle"
        >
          <a-tag :bordered="false" :color="volumeStatus.color">
            <el-icon><InfoFilled :color="volumeStatus.color === 'red' ? '#ff4d4f' : '#faad14'" /></el-icon>
            {{ volumeStatus.message }}
          </a-tag>
        </div>
      </div>

      <div class="w-[25%] bg-[#f6e4e4] rounded-[3px] h-full flex justify-center relative">
        <div class="top-[5px] right-[-50%] w-full absolute bottom-[-32px] text-[rgba(0,0,0,0.45)] flex flex-wrap justify-center">
          <div>
            <CaretUpOutlined />
          </div>
          <div class="w-full text-center">
            10题
          </div>
        </div>
      </div>
      <div class="w-[48%] bg-liner rounded-[3px] h-full flex justify-center relative">
        <span class="top-[24px] absolute bottom-[-32px] text-[rgba(0,0,0,0.45)]">推荐题量</span>
      </div>
      <div class="w-[25%] bg-[#e9e9e9] rounded-[3px] h-full flex justify-center relative">
        <div class="top-[5px] left-[-50%] w-full absolute bottom-[-32px] text-[rgba(0,0,0,0.45)] flex flex-wrap justify-center">
          <div>
            <CaretUpOutlined />
          </div>
          <div class="w-full text-center">
            20题
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container{
  position: relative;
  width: 100%;
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  display: flex;
  align-content: space-between;
  padding-bottom: 56px;
}
.bg-liner{
  background: linear-gradient(90deg,#78c5ff 1%, rgba(84,120,238,0.85)); 
}
.sort-icon{
  visibility: hidden;
}
.content-item{
      cursor: pointer;
      border-radius: 2px;

      &:hover{
            border: 1px solid #5478ee;
            background: #f1f4fe;
      }
      &:hover .sort-icon{
            visibility: visible;
      }

      // 高亮状态
      // &.highlighted {
      //   background-color: rgba(84, 120, 238, 0.1);
      //   border-color: #5478ee !important;
      // }
}

@keyframes highlight-flash {
  0% {
    background-color: rgba(84, 120, 238, 0.1);
    box-shadow: 0 0 0 2px rgba(84, 120, 238, 0.2);
  }
  50% {
    background-color: rgba(84, 120, 238, 0.3);
    box-shadow: 0 0 0 4px rgba(84, 120, 238, 0.4);
  }
  100% {
    background-color: rgba(84, 120, 238, 0.1);
    box-shadow: 0 0 0 2px rgba(84, 120, 238, 0.2);
  }
}
.dminw{
  @media screen and (max-width: 1440px) {
    width: 265px;
  }
  @media screen and (min-width: 1441px) {
    width: 344px;
  }
}
</style>