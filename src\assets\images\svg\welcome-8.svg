<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="-43.7421986%" y1="-4.67186709%" x2="58.6913615%" y2="55.8568962%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="-11.5042533%" x2="13.2823176%" y2="100%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.085,5 L18.8521232,11.5929276 C19.4679832,13.8913484 18.1039982,16.2538398 15.8055774,16.8696998 L8.40707241,18.8521232 C7.2068689,19.1737168 5.98919473,18.9554494 5.0118737,18.3461758 C5.00396867,18.231614 5,18.1162705 5,18 L5,10 C5,7.23857625 7.23857625,5 10,5 L17.085,5 Z" id="path-3"></path>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="11.2021508%" y1="23.58476%" x2="57.4737082%" y2="55.8568962%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.3425777%" y1="3.3834%" x2="26.2989383%" y2="95.9599574%" id="linearGradient-6">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M16.8696998,4.19442263 L17.085,5 L10,5 C7.3112453,5 5.11818189,7.12230671 5.00461951,9.78311038 L5,10 L5,18 C5,18.1162705 5.00396867,18.231614 5.01177793,18.3459026 C4.11772039,17.7888288 3.42459227,16.9038904 3.1303002,15.8055774 L1.14787677,8.40707241 C0.532016767,6.10865159 1.89600181,3.7461602 4.19442263,3.1303002 L11.5929276,1.14787677 C13.8913484,0.532016767 16.2538398,1.89600181 16.8696998,4.19442263 Z" id="path-7"></path>
        <linearGradient x1="8.81880296%" y1="9.30569435%" x2="93.0787331%" y2="93.1157159%" id="linearGradient-8">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-9" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-10"></use>
        </pattern>
        <image id="image-10" width="18" height="18" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEqADAAQAAAABAAAAEgAAAACaqbJVAAABq0lEQVQ4Ea2TYWrjMBCFx/UQ00AKPcD+38vsOXuEHqN/9wgLG+JQgxepEun3RlHp/iikTQfLksYzT+/NyMPfQ/mVq1nKjGKWc7GUzFYNfFnzWmMdPvaKzakaT8RWZh/dbGPYqBdW3cpYTP6RBBn4bZBQGBXHexAR8AkADmlgJFb2DojHKYJppuQY+BMU3tb4M3k+TQQq6JzAki2sChI7S3yiLxaSJLlRCuYoC34XrQkGAujylFShtToSAdM+JDGHJBioXlpX5nJKgy8LDlhNFGrL3EkQYxNBfU8PWtHXBqJGlJc06HyZLyunZg+gDUA7AHfb9rEkt3lBIqgrAAtjZkjaCRYtqr19htFIl1TgDccv6Nzdmt3ftYA93wWyP1bbz03Ge4C+DiDxBydMoGr9Fmb3d24/f7g9PGb7c/ifQQfosx+fVQ2Kdn6p6NEJ9MxLtqff0QYbhvGkkI/M53+A0JJo77kTqsVxLiZZl1owUmvjV1BbKX4v6KUgimvtByhxoXQ3dB2a2M/AAPQMgzc2AH3VuEewQZIu3DXmuqH6Aa+1m+8AEYmba5n0/FcYBxXi9kPYOQAAAABJRU5ErkJggg=="></image>
        <rect id="path-11" x="5" y="5" width="18" height="18" rx="5"></rect>
        <linearGradient x1="34.3857943%" y1="13.4364236%" x2="124.063702%" y2="118.763784%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M8,17.5 C8.41421356,17.5 8.75,17.8357864 8.75,18.25 L8.75,20.25 C8.75,20.6642136 8.41421356,21 8,21 C7.58578644,21 7.25,20.6642136 7.25,20.25 L7.25,18.25 C7.25,17.8357864 7.58578644,17.5 8,17.5 Z M12,14 C12.4142136,14 12.75,14.3357864 12.75,14.75 L12.75,20.25 C12.75,20.6642136 12.4142136,21 12,21 C11.5857864,21 11.25,20.6642136 11.25,20.25 L11.25,14.75 C11.25,14.3357864 11.5857864,14 12,14 Z M16,15.5 C16.4142136,15.5 16.75,15.8357864 16.75,16.25 L16.75,20.25 C16.75,20.6642136 16.4142136,21 16,21 C15.5857864,21 15.25,20.6642136 15.25,20.25 L15.25,16.25 C15.25,15.8357864 15.5857864,15.5 16,15.5 Z M20,11.5 C20.4142136,11.5 20.75,11.8357864 20.75,12.25 L20.75,20.25 C20.75,20.6642136 20.4142136,21 20,21 C19.5857864,21 19.25,20.6642136 19.25,20.25 L19.25,12.25 C19.25,11.8357864 19.5857864,11.5 20,11.5 Z M20,7.9 C20.6627417,7.9 21.2,8.4372583 21.2,9.1 C21.2,9.7627417 20.6627417,10.3 20,10.3 C19.7790861,10.3 19.5721148,10.2403046 19.394337,10.1361648 L17.0414513,12.5034533 C17.1423224,12.6791747 17.2,12.8828517 17.2,13.1 C17.2,13.7627417 16.6627417,14.3 16,14.3 C15.3372583,14.3 14.8,13.7627417 14.8,13.1 L14.805,12.992 L12.9780664,12.2954237 C12.7605287,12.6008255 12.4035242,12.8 12,12.8 C11.7487995,12.8 11.5156267,12.7228145 11.3229042,12.5908664 L9.07226642,14.5606769 C9.15398129,14.7228213 9.2,14.9060365 9.2,15.1 C9.2,15.7627417 8.6627417,16.3 8,16.3 C7.3372583,16.3 6.8,15.7627417 6.8,15.1 C6.8,14.4372583 7.3372583,13.9 8,13.9 C8.25120048,13.9 8.48437334,13.9771855 8.67709584,14.1091336 L10.9277336,12.1393231 C10.8460187,11.9771787 10.8,11.7939635 10.8,11.6 C10.8,10.9372583 11.3372583,10.4 12,10.4 C12.6627417,10.4 13.2,10.9372583 13.2,11.6 C13.2,11.6452502 13.1974954,11.6899155 13.1926173,11.7338647 L15.0063345,12.4270107 C15.2221608,12.1089648 15.5866772,11.9 16,11.9 C16.2284451,11.9 16.4419807,11.9638349 16.6237424,12.0746402 L18.9685994,9.71372363 C18.861518,9.53415386 18.8,9.32426108 18.8,9.1 C18.8,8.4372583 19.3372583,7.9 20,7.9 Z M8,14.5 C7.66862915,14.5 7.4,14.7686292 7.4,15.1 C7.4,15.4313708 7.66862915,15.7 8,15.7 C8.33137085,15.7 8.6,15.4313708 8.6,15.1 C8.6,14.7686292 8.33137085,14.5 8,14.5 Z M16,12.5 C15.6686292,12.5 15.4,12.7686292 15.4,13.1 C15.4,13.4313708 15.6686292,13.7 16,13.7 C16.3313708,13.7 16.6,13.4313708 16.6,13.1 C16.6,12.7686292 16.3313708,12.5 16,12.5 Z M12,11 C11.6686292,11 11.4,11.2686292 11.4,11.6 C11.4,11.9313708 11.6686292,12.2 12,12.2 C12.3313708,12.2 12.6,11.9313708 12.6,11.6 C12.6,11.2686292 12.3313708,11 12,11 Z M20,8.5 C19.6686292,8.5 19.4,8.76862915 19.4,9.1 C19.4,9.43137085 19.6686292,9.7 20,9.7 C20.3313708,9.7 20.6,9.43137085 20.6,9.1 C20.6,8.76862915 20.3313708,8.5 20,8.5 Z" id="path-13"></path>
        <filter x="-6.9%" y="-3.8%" width="113.9%" height="115.3%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-500.000000, -130.000000)">
            <g id="icon/06毛玻璃/24*24/08考试分析" transform="translate(500.000000, 130.000000)">
                <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="url(#linearGradient-5)" xlink:href="#path-7"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                </g>
                <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                    <rect stroke="url(#linearGradient-8)" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                    <rect stroke="url(#pattern-9)" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    <use fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                </g>
            </g>
        </g>
    </g>
</svg>