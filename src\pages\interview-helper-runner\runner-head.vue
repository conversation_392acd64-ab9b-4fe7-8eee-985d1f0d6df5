<template>
    <div class="runner-head">
        <div class="head-left">
            <img src="@/assets/images/admin/admin-logo.svg" alt="" />
            <span class="title">图灵智面</span>
        </div>
        <div class="head-center">{{ name }}</div>
        <div class="head-right">
            <span class="time">
                <svg-icon name="clock2" style="margin-right: 4px;"></svg-icon>
                {{ formattedTime }}
            </span>
            <span class="over" @click="handleOver">
                <svg-icon name="over" style="color: #FF4D4F;margin-right: 8px;"></svg-icon>
                结束对话
            </span>
        </div>
    </div>
</template>

<script lang="ts" setup>

import { ref, computed } from 'vue';
import { useIntervalFn, useStorage } from '@vueuse/core';
import moment from 'moment';
import { MyConfirm } from '@/utils/antdUtil';
import { delaiinterviewassistantrecord } from '@/api/interview';

const props = defineProps<{
    name: string
    id: string
}>()

// 使用 useStorage 将时间持久化到 localStorage
const time = ref(0);

// 使用 moment.js 格式化时间
const formattedTime = computed(() => {
    return moment.utc(time.value * 1000).format('HH:mm:ss');
});

// 使用 VueUse 的 useIntervalFn 每秒更新一次时间
useIntervalFn(() => {
    time.value++;
}, 1000);

function adjustTime(timeStr: string) {
    try {
        // 计算现在和timeStr的时间差
        const diff = moment().diff(moment(timeStr, 'YYYY-MM-DD HH:mm:ss'));
        time.value = Math.floor(diff / 1000);
    } catch (error) {
        console.log(error)
    }
}

async function handleOver() {
    await MyConfirm({
        title: '提示',
        content: '确定结束吗？结束后会话记录不保存',
    })
    // 重置
    await delaiinterviewassistantrecord({ assistant_id: props.id })
    window.close()
}

defineExpose({
    adjustTime
})

</script>

<style lang="less" scoped>
.runner-head {
    width: 100%;
    height: 48px;
    background-color: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 0 24px;
    display: flex;
    align-items: center;
    position: relative;
    /* 添加position相对定位 */
}

.head-left {
    display: flex;
    align-items: center;
    flex: 1;
    /* 让左边区域占据剩余空间 */

    .title {
        font-size: 20px;
        font-weight: 600;
        margin-left: 16px;
    }
}

.head-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
}

.head-right {
    flex: 0;
    /* 右边区域根据内容自适应宽度 */
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    gap: 11px;
    line-height: 24px;

    .time,
    .over {
        display: flex;
        align-items: center;
        white-space: nowrap;
    }

    .over {
        margin-left: 32px;
        cursor: pointer;
    }
}
</style>