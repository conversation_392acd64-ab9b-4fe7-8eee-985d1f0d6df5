type MenuModel = {
    icon: string
    title: string
    /** 如果不是叶子节点，name要和路由对应位置的单词一致，用于菜单的展开控制 如：interview-question/interview-question-manage  面试题库的name要是interview-question */
    name: string
    children?: MenuModel[]
}

export const teacherMenuList: MenuModel[] = [
    {
        icon: 'ques_bank',
        title: '题库管理',
        name: 'questionManageBOx',
        children:[
            {
                icon: 'ques_bank',
                title: '笔试题库',
                name: 'writtenQuestionManage',
            },
            {
                icon: 'ques_bank',
                title: '面试题库',
                name: 'questionManage',
            },
            {
                icon: 'ques_bank',
                title: '草稿箱',
                name: 'draft',
            },
            
        ]
    },
    {
        icon: 'ques_bank',
        title: '招聘管理',
        name: 'inviteManageBOx',
        children:[
            {
                icon: 'talent_pool',
                title: '人才库',
                name: 'resumeManage',
            },   
            {
                icon: 'talent_pool',
                title: '岗位管理',
                name: 'positionManagement',
            },      
        ]
    },
    // {
    //     icon: 'Interview_assistant',
    //     title: '面试助手',
    //     name: 'surveyManage',
    // },

    // {
    //     icon: 'post_management',
    //     title: '岗位管理',
    //     name: 'positionManagement',
    // }
]