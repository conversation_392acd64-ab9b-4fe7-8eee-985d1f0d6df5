<template>
    <div>
        <a-spin :spinning="loading">
            <div ref="chartRef" style="height: 100%; overflow: hidden;"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { eduproportion } from '@/api/admin/statisticAnalysis'
import { ECharts, init } from 'echarts'
import type { ZRColor } from 'echarts/types/src/util/types'

// 调色板
const colorList: ZRColor[] = ['#356BFC', '#5EC1CD', '#AD90F5', '#E7609C'].map((color) => ({
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color
    }, {
        offset: 1, color: color + '80'
    }],
    global: false
}))

// 获取数据
const list = ref<{
    name: string
    value: number
}[]>([])

const loading = ref(false)
async function getList() {
    loading.value = true
    try {
        list.value = await eduproportion() as any
    } finally {
        loading.value = false
    }
}

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function setCenterText(selected? : Record<string, boolean>) {
    chart.value?.setOption({
        graphic: {
            elements: [
                {
                    type: 'text',
                    left: window.innerWidth < 1690 ? '30%' : '31%', // 改为与饼图 center 一致
                    top: '90px',
                    style: {
                        text: list.value.reduce((pre, cur) => {
                            if (selected && !selected[cur.name]) return pre
                            return pre + cur.value
                        }, 0),
                        fill: 'rgba(0,0,0,0.85)',
                        fontSize: 36,
                        fontWeight: 'bold'
                    },
                },
                {
                    type: 'text',
                    left: '34%', // 改为与饼图 center 一致
                    top: '120px',
                    style: {
                        text: '总计人数',
                        fill: 'rgba(0,0,0,0.45)',
                        fontSize: 14,
                        lineHeight: 40,
                    },
                },
            ],
        },
    })
}
function draw() {
    chart.value = init(chartRef.value)
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}人`
        },
        color: colorList,
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '0',
            icon: 'circle',
            formatter: (params: any) => {
                let total = list.value.reduce((pre, cur) => pre + cur.value, 0)
                const percent = ((list.value.find(i => i.name === params)!.value / total) * 100).toFixed(2)
                return `${params} ${percent}%`
            }
        },
        grid: {
            left: 0,
            right: 0,
            top: 0
        },
        series: [
            {
                type: 'pie',
                radius: ['70%', '80%'],
                center: ['40%', '50%'],
                label: {
                    show: false,
                },
                itemStyle: {
                    borderWidth: 2,
                    borderRadius: '50%',
                    borderColor: '#fff',
                },
                data: list.value
            }
        ]
    })
    setCenterText()
    window.addEventListener('resize', resizeChart)

    chart.value.on('legendselectchanged', function(params: any) {
        setCenterText(params.selected)
    });
}

function resizeChart(e) {
    chart.value?.resize()
    setCenterText()
}

onMounted(async () => {
    await getList()
    draw()
})

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
</style>