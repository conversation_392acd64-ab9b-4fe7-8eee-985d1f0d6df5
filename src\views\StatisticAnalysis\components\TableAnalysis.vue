<template>
  <div class="table-analysis">
    <a-table
      :columns="columns"
      :row-key="(record:any) => record.id"
      :data-source="data"
      :scroll="{ x: 1200 }"
      :pagination="{ size: 'small'}"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'sex'">
          <span>
            {{ record.sex }}
          </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <span @click="analyze(record)" style="color: #5478ee">分析</span>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const props = withDefaults(defineProps<{
  data?: any[]
  width?: number | string
  height?: number | string
}>(), {
  data: () => [],
  width: '100%',
  height: '100%'
})

const emits = defineEmits<{
  (e: 'changeDraggable', isDraggable: boolean): void
}>()

const columns = ref([
  {
    title: '学生姓名',
    dataIndex: 'username',
    key: 'username',
    width: 400,
    ellipsis: true,
    resizable: true
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex',
    width: 300,
    ellipsis: true,
    resizable: true
  },
  {
    title: '年龄',
    width: 300,
    dataIndex: 'age',
    key: 'age',
    ellipsis: true,
    resizable: true
  },
  {
    title: '电子邮箱',
    width: 500,
    dataIndex: 'email',
    key: 'email',
    ellipsis: true,
    resizable: true
  },
  {
    title: '操作',
    fixed: 'right',
    key: 'action',
    width: 200
  }
])

const router = useRouter()
const analyze = (record: any) => {
  router.push('/admin/statisticAnalysis/capacityAnalysis?id=' + record.id)
}

const dynamicWidth = ref('100%')
const dynamicHeight = ref('100%')
watch(
  () => props.width,
  (val) => {
    if (typeof val === 'string') {
      dynamicWidth.value = val
    } else {
      dynamicWidth.value = val + 'px'
    }
  },
  {
    immediate: true
  }
)
watch(
  () => props.height,
  (val) => {
    if (typeof val === 'string') {
      dynamicHeight.value = val
    } else {
      dynamicHeight.value = val + 'px'
    }
  },
  {
    immediate: true
  }
)


onMounted(() => {
  const resizeHandles = document.querySelectorAll('.ant-table-resize-handle');
  resizeHandles.forEach((item) => {
    item.addEventListener('mousemove', () => {
      emits('changeDraggable', false)
    })
    item.addEventListener('mouseout', () => {
      emits('changeDraggable', true)
    })
  })
})
</script>

<style lang="less">
.table-analysis {
  padding: 10px 24px;
  .ant-table {
    width: calc(v-bind(dynamicWidth) - 24px);
    height: calc(v-bind(dynamicHeight) -70px);
  }

  background: #fff;
  .ant-table-thead > tr > th {
    font-weight: bold;
    &:first-child {
      padding-left: 8px !important;
    }
    background: #f1f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
}
</style>
