<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>浏览器不兼容</title>
    <style>
      * {
        padding: 0;
        margin: 0;
      }
      h2 {
        font-size: 32px;
      }
      .container {
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
      .logo {
        position: relative;
        margin-bottom: 90px;
      }
      .logo span {
        font-size: 72px;
        font-weight: bold;
        color: #f3f3f3;
      }
      .logo img {
        position: absolute;
        top: -50%;
        left: 50%;
        transform: translate(-50%);
      }
      .desc-wrapper {
        margin-bottom: 120px;
      }
      .desc {
        font-size: 24px;
        line-height: 36px;
      }
      .support-browser {
        display: flex;
        align-items: center;
      }
      .support-browser .item {
        display: flex;
        margin-right: 64px;
        text-align: left;
        font-size: 14px;
      }
      .support-browser .item:last-child {
        margin-right: 0;
      }
      .support-browser .item img {
        margin-right: 8px;
      }
      .support-browser .item a {
        text-decoration: none;
        color: #5478ee;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <span>UPDATE</span>
        <img src="./update.png" alt="" />
      </div>
      <div class="desc-wrapper">
        <div class="desc">很抱歉！您的浏览器版本过低，无法完全兼容本网站</div>
        <div class="desc">请选择以下浏览器进行下载后使用，谢谢！</div>
      </div>
      <div class="support-browser">
        <div class="item">
          <img src="./chrome.png" width="40px" alt="" />
          <div class="text">
            <div>Chrome下载地址：</div>
            <a href="https://www.google.cn/chrome/index.html"
              >https://www.google.cn/chrome/index.html</a
            >
          </div>
        </div>
        <div class="item">
          <img src="./edge.png" width="40px" alt="" />
          <div class="text">
            <div>Edge下载地址：</div>
            <a href="https://www.microsoft.com/zh-cn/edge">https://www.microsoft.com/zh-cn/edge</a>
          </div>
        </div>
      </div>
    </div>
    <script>
      (function () {
        let isBrowserSupported = false
        let ua = navigator.userAgent || navigator.vendor || window.opera
        if (/iPhone|iPod|iPad|Android|BlackBerry|Windows Mobile/i.test(ua)) {
          return
        }

        // 检查Chrome版本
        const chromeMatch = ua.match(/Chrome\/(\d+)/)
        if (chromeMatch && parseInt(chromeMatch[1], 10) >= 112) {
          isBrowserSupported = true
        }

        // 检查Edge版本
        const edgeMatch = ua.match(/Edg\/(\d+)/)
        if (edgeMatch && parseInt(edgeMatch[1], 10) >= 94) {
          isBrowserSupported = true
        }

        // 检查Safari版本
        const safariMatch = ua.match(/Version\/([0-9\.]+) Safari/)
        if (safariMatch && parseFloat(safariMatch[1]) >= 605) {
          isBrowserSupported = true
        }

        // 检查ECMAScript 2022支持
        try {
          // 选择了ECMAScript 2022的一些新特性进行检测，例如BigInt
          eval('0n')
        } catch (err) {
          isBrowserSupported = false
        }

        // 如果浏览器版本不满足要求，跳转到Chrome下载页面
        if (isBrowserSupported) {
          try {
            // 获取redirecturi参数
            const urlParams = new URLSearchParams(window.location.search)
            const redirecturi = urlParams.get('redirecturi')
            if (!redirecturi) return
            // 跳转
            window.location.href = redirecturi
          } catch (error) {
            console.error(error)            
          }
        }
      })()
    </script>
  </body>
</html>
