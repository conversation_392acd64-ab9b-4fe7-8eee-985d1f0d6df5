<template>
    <div class="ai-aide-wrap" ref="aiAideRef" :style="aiCreateCircleStyle">
        <img src="@/assets/images/ai-aide.png" class="ai-aide" alt="">
        <div class="tip">Hi, 点我试试</div>
    </div>
</template>

<script lang="ts" setup>
import { useDraggable, useWindowSize } from '@vueuse/core'
import { ref, watch } from 'vue'

const emits = defineEmits<{
    (e: 'click'): void
}>()

// ai创建题目悬浮球，可拖拽
const aiAideRef = ref<HTMLElement | null>(null)
let dragStartTimestamp = 0
const { x, y, style: aiCreateCircleStyle } = useDraggable(aiAideRef, {
    initialValue: { x: window.innerWidth - 200, y: 240 },
    onMove(position, event) {
        x.value = Math.max(0, Math.min(position.x, windowWidth.value - 80))
        y.value = Math.max(0, Math.min(position.y, windowHeight.value - 80))
    },
    preventDefault: true,
    onStart() {
        dragStartTimestamp = Date.now()
    },
    onEnd() {
        let dragDuration = Date.now() - dragStartTimestamp
        if (dragDuration <= 200) {
            emits('click')
        }
    }
})

const { width: windowWidth, height: windowHeight } = useWindowSize()

watch([windowWidth, windowHeight], ([w, h]) => {
    // 0 ~ w - 80
    // 0 ~ h - 80
    x.value = Math.min(x.value, w - 80)
    y.value = Math.min(y.value, h - 80)
})

</script>

<style lang="less" scoped>
.ai-aide-wrap {
    position: fixed;

    &:hover .tip {
        opacity: 1;
        animation: sway .3s ease-in-out;
    }

    .tip {
        transition: all ease .2s;
        opacity: 0;
        position: absolute;
        top: -30px;
        left: -32px;
        width: 88px;
        height: 27px;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
        border-radius: 8px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        text-align: center;
        line-height: 27px;

        &::after {
            content: '';
            display: block;
            position: absolute;
            bottom: -6px;
            right: 9px;
            width: 0;
            height: 0;
            border-right: 8px solid transparent;
            border-left: 8px solid transparent;
            border-top: 8px solid #fff;
        }
    }

    .ai-aide {
        width: 80px;
        height: 80px;
        cursor: pointer;
    }
}
</style>