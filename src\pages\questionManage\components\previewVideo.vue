<script setup lang="ts">
// import Hls from 'hls.js'
import { onMounted } from 'vue'

const props = defineProps({
  previewItem: {
    type: Object,
    default: () => null,
  },
})

// const videoUrl 
// = 'https://metastudio.obs.cn-north-4.myhuaweicloud.com:443/981332f48193428d8275d6dbb196d78f/0a52ddae188eeb6c54bf4aa35292b9bd/b46852e4e7b3cfa907820c979da1f58f.mp4?AWSAccessKeyId=HST3FGS9RTCGF8HE2HWP&Expires=1748506624&response-content-disposition=attachment%3B+filename*%3Dutf-8%27%27ai_interview_bf61f778-e4a4-4014-baf3-5b44d0e80906.mp4&x-amz-security-token=ggpjbi1ub3J0aC00Twx7ImFjY2VzcyI6IkhTVDNGR1M5UlRDR0Y4SEUySFdQIiwibWV0aG9kcyI6WyJ0b2tlbiJdLCJyb2xlIjpbXSwicm9sZXRhZ2VzIjpbXSwidGltZW91dF9hdCI6MTc0ODUxMjc5NjU0MCwidXNlciI6eyJPUy1GRURFUkFUSU9OIjp7Imdyb3VwcyI6W3siaWQiOiIyMjZkYTBmNzQyZDc0MjEzOTAwNjFkZDA1YmUyMGYwZCIsIm5hbWUiOiJjc21zLW9pZGMifV0sImlkZW50aXR5X3Byb3ZpZGVyIjp7ImlkIjoib2lkYy1vcC1NU1MifSwicHJvdG9jb2wiOnsiaWQiOiJvaWRjIn19LCJkb21haW4iOnsiaWQiOiIyZGQwYjdlMTI0MWY0ZDNmOWVlNGE3NjZiMmI1MTAxMSIsIm5hbWUiOiJvcF9zdmNfTWV0YVN0dWRpb19jb250YWluZXIwIn0sImlkIjoiNGJVNzd4SklNMU1Ya1RXWlVtcUc2S2ZZWkNCMkZjM2QiLCJuYW1lIjoiRmVkZXJhdGlvblVzZXIiLCJwYXNzd29yZF9leHBpcmVzX2F0IjoiIiwidXNlcl90eXBlIjo0OH19YPqAklZM6Rr9Osbe0m5cQvLC92DmyKzDi0KXl1gZs6T_FXDIAvp8pDuqBK31Rky_iMhgxWQs1UqYhZYUTftlWBJJP3k2z98qwEvdxdinhoUlsANzKfQSk5xzDmqG4io0s2Y7i_2qrvbcmWzQiT3m_PFHRdyxPB3wQpfakwwb5SdL6MLev9oXjP8TD5IsHsTwd5zsoq3UkVMtcKDvRWPp_7Y4VmDTiVzBeVX9zXHTrE2_OCIeiLlQ8_TskkWmKxL0odhJHzA-rhLnXrwF7z9PhSSQLm4D2ynn3-JOMvD0VtH3gkOlG6OKQ1E6435sBVh3e275HgygoZFyFhHalitSWw%3D%3D&Signature=ODvKI8wfLnfp8qcmDh1T6289Eis%3D'

const loading = ref(false)
const videoRef = useTemplateRef('video')
onMounted(async () => {
  loading.value = true
  // const res = await createPreviewVideo({
  //   id: props.previewItem.id,
  // })
  const video = videoRef.value as HTMLVideoElement
  const videoSrc = props.previewItem.videoUrl
  // if (Hls.isSupported()) {
  //   const hls = new Hls()
  //   await hls.loadSource(videoSrc)
  //   await hls.attachMedia(video)
  //   video.play()
  // }
  video.src = videoSrc
  video.play()
  loading.value = false
})
</script>

<template>
  <div class="container-bg flex justify-center items-center">
    <div class="w-[440px] h-[852px] border box-border relative flex items-center justify-center">
      <div class="grow rounded-[25px] box-border bg-[white] w-[105%] h-[102%] margin-[-20px] overflow-hidden relative">
        <div class="w-full mt-[30px] mb-[15px] text-[16px] text-[#333] font-bold text-center">
          图灵智面
        </div>
        <div v-loading="loading" class="w-[100%] h-[211px] relative mb-[15px]">
          <video id="video" ref="video" controls crossorigin="anonymous" class="w-full h-full bg-[#000]" />
        </div>
        <div class="p-[20px]">
          {{ previewItem.content }}
        </div>
        <div class="circle absolute cursor-pointer bottom-[20px] left-[50%] flex items-center justify-center w-[56px] h-[56px] rounded-[50%] border-[2px] border-solid border-[#f26868]">
          <div class="w-[24px] h-[24px] bg-[#f26868] rounded-[8px]" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-bg {
      width: 100%;
      height: 100%;
      background:#E9F4FB
}
.border {
      position: relative;
      border: 28px solid #cdd9ff;
      border-radius: 30px;
}
.grow {
      flex-shrink: 0
}
.circle {
      transform: translateX(-50%);
}
</style>