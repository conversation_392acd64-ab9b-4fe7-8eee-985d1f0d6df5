<!-- eslint-disable ts/no-use-before-define -->
<script lang="ts" setup>
import type { LanguageFunc } from '@/models/questionModel'

import type { RuleObject } from 'ant-design-vue/es/form/interface'
import { addQuestion, alltags, compoundques, ojtemplate, quescontent, subjcatgs , editQuestion } from '@/api/admin/questionManage'
import { getAiAnswer, proofreadingrecords } from '@/api/exam/index'
import FoldText from '@/components/FoldText.vue'
import JSwitch from '@/components/JSwitch.vue'
import JTagInput from '@/components/JTagInput.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'

import languageFunctions from '@/config/languageFunctions'
import { QuestionEnum, questionType } from '@/models/questionModel'
import SetKeyword from '@/pages/questionManage/components/SetKeyword.vue'
import QuestionConvertor from '@/pages/questionManage/QuestionConvertor.vue'
import OptionGenerator from '@/pages/questionManage/QuestionForm/components/OptionGenerator.vue'
import { findDeletedBlanks, hasImageOrFormula, insetParenthesis, uuid } from '@/utils'
import { scrollToFirstErrFormItem } from '@/utils/common'
import { htmlAddSimulate } from '@/utils/index'
import { checkScore } from '@/utils/validate'

import { CloseOutlined, ExclamationCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'
import { useEventListener } from '@vueuse/core'

import { Form, message, Modal } from 'ant-design-vue'
import JsonEditorVue from 'json-editor-vue'
import { computed, createVNode, nextTick, onMounted, ref, watch, watchEffect } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import BlankFilling from './components/question/BlankFilling.vue'
import JudgingQuestions from './components/question/JudgingQuestions.vue'
import MultipleChoice from './components/question/MultipleChoice.vue'
import OrderQuestion from './components/question/OrderQuestion.vue'
import ParamList from './components/question/ParamList.vue'
import QuestionAndAnswer from './components/question/QuestionAndAnswer.vue'
import ScoreInput from './components/question/ScoreInput.vue'
import SingleChoice from './components/question/SingleChoice.vue'
import TestInstance from './components/question/TestInstance.vue'
import ValidateAlgorithm from './components/question/ValidateAlgorithm.vue'
import useWatchForm from './hooks/useWatchForm'
import { CHECK_VARIATE_NAME_REG, checkVariateName, judgeIsKeyword } from './rules'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import 'vue-json-pretty/lib/styles.css'
import { dataTool } from 'echarts'

const props = withDefaults(defineProps<{
  qid: string
  canConvert?: boolean
}>(), {
  canConvert: false,
})

const emits = defineEmits<{
  (e: 'close', needRefresh?: boolean): void
}>()

const store = useStore()

// 对浏览器自带的返回按钮的处理
onBeforeRouteLeave(async (to, from, next) => {
  if (formChanged.value) {
    await showConfirmExitModal()
    next()
  }
  else {
    next()
  }
})

useEventListener('beforeunload', (event) => {
  if (formChanged.value) {
    event.preventDefault()
  }
})

// 询问用户是否确认退出编辑
function showConfirmExitModal() {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '确定退出此页面？',
      icon: () => createVNode(InfoCircleFilled),
      content: '系统可能不会保存您所做的更改',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        resolve('')
      },
    })
  })
}

// 关闭事件
async function handleCancel() {
  if (formChanged.value) {
    await showConfirmExitModal()
    emits('close')
  }
  else {
    emits('close')
  }
}

const useForm = Form.useForm

const subQuestionList = ref<any>([])
// 复合题添加题目
function addCompoundQuestions(item: any) {
  subQuestionList.value.push({
    type: item.questionKey,
    label: item.questionCate,
    formState: {},
  })
}

const questionsObj = ref([
  {
    questionKey: '0',
    questionCate: '单选题',
  },
  {
    questionKey: '1',
    questionCate: '多选题',
  },
  {
    questionKey: '2',
    questionCate: '判断题',
  },
  {
    questionKey: '3',
    questionCate: '问答题',
  },
  {
    questionKey: '5',
    questionCate: '填空题',
  },
  {
    questionKey: '6',
    questionCate: '排序题',
  },
])
const varType = [
  { label: 'string', value: 'string' },
  { label: 'int', value: 'int' },
]
const numberAlpht = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
const lettersAlpht = 'ABCDEFGHIJK'
const typeName = ['单选题', '多选题', '判断题', '问答题', '算法题', '填空题', '排序题']
const categoryList = ref<any>([])
const disabledKeywords = ref(false)
const judge = [
  {
    content: '对',
    value: 'A',
  },
  {
    content: '错',
    value: 'B',
  },
]
const options = [
  {
    content: '',
    value: 'A',
  },
  {
    content: '',
    value: 'B',
  },
  {
    content: '',
    value: 'C',
  },
  {
    content: '',
    value: 'D',
  },
]
const scorepts = ref<any>([])
const quesContentRaw = ref({})
const formState = ref<{
  disablekws: LanguageFunc
  [propName: string]: any
}>({
  // children: [],
  type: undefined,
  category: undefined,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  func_name: '',
  scorebasis: '',
  rtype: { type: undefined, is_list: false },
  difficulty: '',
  parameters: [{ name: '', type: '', is_list: false }],
  btestcase: [{ input: [], output: [] }],
  ptestcase: [{ input: [], output: [] }],
  sepscore: true,
  ordered: true,
  ignorecase: true,
  complicatedediting: false,
  disablekws: {
    'C': [],
    'C++': [],
    'Java': [],
    'Python': [],
    'JavaScript': [],
    'Go': [],
  },

  options: [
    {
      content: '',
      value: 'A',
    },
    {
      content: '',
      value: 'B',
    },
    {
      content: '',
      value: 'C',
    },
    {
      content: '',
      value: 'D',
    },
  ],
  tags: [],
  subtopic: [],
  validatecode: null,
  proofreading_info: {
    proofreading: 1,
    teacher_name: '',
    update_at: '',
    wrong_reason: '',
  },
})

const replaceFields = ref({
  children: 'children',
  key: 'subjectCategoryUUID',
  value: 'subjectCategoryUUID',
  label: 'name',
})
const createQuestionFormRef = ref()
const scoreInputRef = ref()
const router = useRouter()
const formChanged = ref(false) // 表单是否有变动

// 判断方法名是否是禁用关键词中的一个
function isFnNameEqualKeyword(keywords: any[], fnName: string) {
  return keywords.includes?.(fnName)
}

// 删除复合题中的一项
function handleDelete(index: number) {
  subQuestionList.value.splice(index, 1)
}

// 向上移动复合题
function handleUp(index: number) {
  if (index > 0) {
    const tempProxy = subQuestionList.value[index - 1]
    subQuestionList.value[index - 1] = subQuestionList.value[index]
    subQuestionList.value[index] = tempProxy
  }
  else {
    
  }
}

// 向下移动复合题
function handleDown(index: number) {
  const tempProxy = subQuestionList.value[index + 1]
  subQuestionList.value[index + 1] = subQuestionList.value[index]
  subQuestionList.value[index] = tempProxy
}

// 校验题目类型
async function checkType(rule: RuleObject, value: string) {
  if (value === undefined) {
    return Promise.reject('请选择题目类型')
  }
  else {
    return Promise.resolve()
  }
}

// 校验所属题库
async function checkCategory(rule: RuleObject, value: string) {
  if (value === undefined) {
    return Promise.reject('请选择所属题库')
  }
  else {
    return Promise.resolve()
  }
}

// 校验选项内容
async function checkOptions(rule: RuleObject, value: []) {
  let isDup = false
  const allContents: any = []
  value.forEach((item: any) => {
    if (allContents.includes(item.content)) {
      isDup = true
    }
    else {
      allContents.push(item.content)
    }
  })
  if (!value.length) {
    return Promise.reject('请添加选项')
  }
  if (!value.every((item: any) => item.content)) {
    return Promise.reject('选项内容都不能为空')
  }
  if (isDup) {
    return Promise.reject('选项内容不能相同')
  }
  else {
    return Promise.resolve()
  }
}

// 校验题干
async function checkBody(rule: RuleObject, value: '') {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  }
  else {
    return Promise.resolve()
  }
}

async function checkSubQuestionList(rule: RuleObject) {
  if (!subQuestionList.value.length) {
    return Promise.reject('请添加子试题')
  }
  else {
    return Promise.resolve()
  }
}
async function checkRtype(rule: RuleObject, value: {}) {
  if (!value.type) {
    return Promise.reject('请选择返回值类型')
  }
  else {
    return Promise.resolve()
  }
}

// 校验警用关键词
async function checkDisablekws(rule: RuleObject, value: {}) {
  if (!Object.values(value).flat().length) 
    return Promise.reject('请输入禁用关键词')
  return Promise.resolve()
}

async function checkParameters(rule: RuleObject, value: []) {
  if (value.length === 0) {
    return Promise.reject('请添加参数列表')
  }
  if (value.some((item: any) => !item.name || !item.type)) {
    return Promise.reject('请完善参数列表内容')
  }
  if (value.some((item: any) => judgeIsKeyword(item.name))) {
    return Promise.reject('变量名称不能是关键字')
  }
  return Promise.resolve()
}
async function checkBtestcase(rule: RuleObject, value: []) {
  for (let i = 0; i < value.length; i++) {
    const item: any = value[i]
    for (let j = 0; j < item.input.length; j++) {
      if (!Object.keys(item.input[j])[0]) {
        return Promise.reject('请完善题干测试用例')
      }
      if (!item.input[j][Object.keys(item.input[j])[0]]) {
        return Promise.reject('请完善题干测试用例')
      }
    }
    for (let k = 0; k < item.output.length; k++) {
      if (!item.output[k].value) {
        return Promise.reject('请完善题干测试用例')
      }
    }
  }
  return Promise.resolve()
}
async function checkPtestcase(rule: RuleObject, value: []) {
  for (let i = 0; i < value.length; i++) {
    const item: any = value[i]
    for (let j = 0; j < item.input.length; j++) {
      if (!Object.keys(item.input[j])[0]) {
        return Promise.reject('请完善批卷测试用例')
      }
      if (!item.input[j][Object.keys(item.input[j])[0]]) {
        return Promise.reject('请完善批卷测试用例')
      }
    }
    for (let k = 0; k < item.output.length; k++) {
      if (!item.output[k].value) {
        return Promise.reject('请完善批卷测试用例')
      }
    }
  }
  return Promise.resolve()
}

// 校验规则
const rules = {
  type: [{ required: true, validator: checkType, trigger: 'change' }],
  category: [{ required: true, validator: checkCategory, trigger: 'blur' }],
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }],
  complexcontent: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }],
  subQuestionList: [{ required: true, validator: checkSubQuestionList, trigger: 'blur' }],
  func_name: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  rtype: [{ required: true, validator: checkRtype, trigger: 'blur' }],
  difficulty: [{ required: true, message: '请选择难度', trigger: 'blur', type: 'number' }],
  disablekws: [{ required: true, validator: checkDisablekws, trigger: 'blur' }],
  parameters: [{ required: true, validator: checkParameters, trigger: 'blur' }],
  btestcase: [{ required: true, validator: checkBtestcase, trigger: 'blur' }],
  ptestcase: [{ required: true, validator: checkPtestcase, trigger: 'blur' }],
}

const { validate, validateInfos } = useForm(formState, rules)

function getSubjectData() {
  subjcatgs({ action: 'query' }).then((res: any) => {
    categoryList.value = JSON.parse(JSON.stringify(res))[0].children
    // res.forEach((item) =>{
    //     if(item.name = '部门题库'){
    //         categoryList.value = item.children
    //     }
    // }) 
  })
}

watch(
  () => formState.value.category,
  (val: string) => {
    if (firstLoad.value) {
      const flag = findSubjectId(val, categoryList.value)
      formState.value.category = flag ? val : undefined
    }
  },
)

function findSubjectId(id: string, subjectTree: any) {
  let result = false
  for (let i = 0; i < subjectTree.length; i++) {
    const item = subjectTree[i]
    if (item.subjectCategoryUUID === id) {
      return true // 如果找到了匹配的id，立即返回true
    }
    else if (item.children && item.children.length) {
      // 如果有子节点，则递归查找
      result = findSubjectId(id, item.children)
      if (result) {
        return true // 如果子节点中找到了匹配的id，立即返回true
      }
    }
  }
  return result
}

function handleChangeSelect(item: any, tagsValue: string[]) {
  const result = tagsValue.map((tag: string) => tag.trim())
  item.keyword = result
}

// 获取分值
function getScore(score: any) {
  formState.value.score = score
}
// 获取填空题分值
function getFillBlankScore(score: any, item: any) {
  item.score = score
  let totalScore = 0
  fillBlank.value.forEach((item: any) => {
    totalScore += Number(item.score)
  })
  // scoreInputTotalRef.value.score = totalScore
  formState.value.score = totalScore
}
function getPointsScore(score: any, item: any) {
  item.score = score
  let totalScore = 0
  scorepts.value.forEach((item: any) => {
    totalScore += Number(item.score)
  })
  // scoreInputTotalRef.value.score = totalScore
  formState.value.score = totalScore
}

// 算法题添加变量
function handleAddParameter() {
  formState.value.parameters.push({ name: '', type: '', is_list: false })
}
// 算法题删除变量
function handleDelParameter(payload: number) {
  formState.value.parameters.splice(payload, 1)
}

function handleAddInstance() {
  const newItem: any = { input: [], output: [] }
  const paramNameList = formState.value.parameters.map((item: any) => item.name)
  paramNameList.forEach((name) => {
    newItem.input.push({ [name]: '' })
  })
  newItem.output.push({ type: 'value', value: '' })
  formState.value.btestcase.push(newItem)
}

function handleDelInstance(payload: number) {
  formState.value.btestcase.splice(payload, 1)
}

function addTestCase() {
  const newItem: any = { input: [], output: [] }
  const paramNameList = formState.value.parameters.map((item: any) => item.name)
  paramNameList.forEach((name) => {
    newItem.input.push({ [name]: '' })
  })
  newItem.output.length = 0
  newItem.output.push({ type: 'value', value: '' })
  formState.value.ptestcase.push(newItem)
}

function delTestCase(payload: number) {
  formState.value.ptestcase.splice(payload, 1)
}

function handleBodyBlur() {
  return validate('body', { trigger: 'blur' })
}

function handleComplexContentBlur() {
  return validate('complexcontent', { trigger: 'blur' })
}
const handleOptionBlur = () =>{
  createQuestionFormRef.value.validateFields(['options'])
}
// 保存按钮
const saveBtnLoading = ref(false)
async function handleSaveBtnClick() {
  saveBtnLoading.value = true
  try {
    await checkForm()

    // 判断原题目是否已校对，已校对的题目才需要弹框提醒（res为null或者res.proofreading === 0都是未校对）
    // const res = await proofreadingrecords({ action: 'query', id: props.qid }) as any
    if (formChanged.value && !isNoMoreReminders.value && quesContentRaw.value.proofreading) {
      confirmSaveVisible.value = true
    }
    else {
      await save()
    }
  }
  catch (error) {
    console.log(error)
    scrollToFirstErrFormItem()
    saveBtnLoading.value = false
    if (activeTab.value === '2') {
      message.error('题目表单校验失败,请检查输入项内容')
    }
  }
}

// 表单校验
async function checkForm() {
  await createQuestionFormRef.value.validate()
  const cb = formState.value.complicatedediting ? handleComplexContentBlur : handleBodyBlur
  await cb()
  if (formatQuestionAnswer(formState.value.type as any, formState.value)) {
    saveBtnLoading.value = false
    throw new Error('')
  }
}

// 通过表单校验的保存
async function save() {
  // eslint-disable-next-line ts/no-use-before-define
  confirmSaveVisible.value = false
  // 算法题需要验证
  if (formState.value.type === 4) {
    // eslint-disable-next-line ts/no-use-before-define
    validateAlgorithmRef.value.currentStep = 0

    if (isValidateCodeChanged(formState.value.validatecode)) {
      // 如果校验代码有变更，则需要重新校验
      // eslint-disable-next-line ts/no-use-before-define
      await validateAlgorithmRef.value.autoValidateCode((validateCode: any) =>
        submitQuestion(validateCode),
      )
      saveBtnLoading.value = false
    }
    else {
      submitQuestion(rawValidatecode.value)
    }
    return
  }
  else if (formState.value.type === 5 && formState.value.ignorecase) {
    const checkKeyWordDuplicates = (keyword: string[]) => {
      const set = new Set()
      for (let i = 0; i < keyword.length; i++) {
        if (set.has(keyword[i].toLowerCase())) 
          return true
        set.add(keyword[i].toLowerCase())
      }
      return false
    }
    if (formState.value.answer) {
      const answers = JSON.parse(formState.value.answer)
      const isDuplicate = answers.some((answer) => {
        const { keyword } = answer
        return checkKeyWordDuplicates(keyword)
      })  
      if (isDuplicate) {
        message.error('答案中存在重复的关键字,请手动移除或者移除勾选"忽略大小写"')
        saveBtnLoading.value = false
        return
      }
    }
  }
  submitQuestion(undefined)
}

function submitQuestion(validateCode?: object) {
  if (formState.value.type === 7) {
    saveBtnLoading.value = true
    compoundques({
      action: 'modify',
      id: props.qid,
      question: {
        ...formState.value,
        validatecode: validateCode,
        children: subQuestionList.value.map((item: any) => {
          return {
            type: item.type,
            ...item.formState,
          }
        }),
      },
    })
      .then(() => {
        saveBtnLoading.value = false
        message.success('保存成功！')
        emits('close', true)
      })
      .catch(() => {
        saveBtnLoading.value = false
      })
  }
  else {
    saveBtnLoading.value = true
    const reg = /[(（]\s*[)）]/
    let complexcontent = formState.value.complexcontent
    if ([0, 1, 2, 6].includes(formState.value.type) && complexcontent && !reg.test(complexcontent)) {
      // 富文本中找到最后一个内容，在其后面加上( )
      complexcontent = htmlAddSimulate(complexcontent)
    }
    let body = formState.value.body
    if ([0, 1, 2, 6].includes(formState.value.type) && body && !reg.test(body)) {
      body = `${formState.value.body}( )`
    }
    const param: any = {
      action: 'modify',
      id: props.qid,
      question: {
        body, // 题干
        complexcontent, // 富文本题干
        ...formState.value,
        validatecode: validateCode,
        funcName: formState.value.func_name,
      },
    }
    if (hasConverted.value) {
      param.action = 'add'
      param.del_id = props.qid
    }
    convertFieldJsonContent(param.question,true)
    convertBoolean(param.question)
    param.question.id = quesContentRaw.value.id
    param.question.categoryUUID = param.question.category
    param.question.tagList = param.question.tags
    param.question.subtopic = param.question.subtopic && (typeof param.question.subtopic == 'object' || Array.isArray(param.question.subtopic)) ? param.question.subtopic.toString() : param.question.subtopic
    delete param.question.tags  
    editQuestion({...param.question})
      .then(() => {
        saveBtnLoading.value = false
        formChanged.value = false
        message.success('保存成功！')
        emits('close', true)
      })
      .catch(() => {
        saveBtnLoading.value = false
      })
  }
}

// 可能清空校对状态的保存前的确认
const confirmSaveVisible = ref(false)
const isNoMoreReminders = ref(false)
isNoMoreReminders.value = store.getters.appConfig.isEditQuestionResetProofreadConfirm || false
function handleNoMoreRemindersChange() {
  store.commit('SET_APP_CONFIG_WITH_OBJ', { isEditQuestionResetProofreadConfirm: isNoMoreReminders.value })
}

// 格式化答案
function formatQuestionAnswer(type: number, formState: any, isCompound = false) {
  // 如果不是复杂编辑，则将复杂编辑的题干字段清空，以便后端判断是否应该清空校对状态
  if (!formState.complicatedediting) 
    formState.complexcontent = ''
  // 如果算法题不存在禁用关键词，则将禁用关键词清空
  if (!disabledKeywords.value && formState.type === QuestionEnum['算法题']) 
    Object.keys(formState.disablekws).forEach(key => formState.disablekws[key] = [])
  switch (type) {
    case questionType.SINGLE_CHOICE:
      if (formState.answer === '') {
        message.error('请选择正确答案')
        return true
      }

      formState.options.forEach((item: any, index: number) => {
        item.value = lettersAlpht[index]
      })

      break

    case questionType.MULTIPLE_CHOICE:
      if (formState.answer.length < 2) {
        message.error('多选题至少勾选2个正确答案')
        return true
      }
      formState.options.forEach((item: any, index: number) => {
        item.value = lettersAlpht[index]
      })

      formState.answer = Array.isArray(formState.answer)
        ? formState.answer.join('')
        : formState.answer
      break

    case questionType.TRUE_OR_FALSE:
      formState.options = JSON.parse(JSON.stringify(judge))
      if (formState.answer === '') {
        message.error('请选择正确答案')
        return true
      }
      break

    case questionType.FILL_BLOCK:
      // fix
      if (isCompound) {
        formState.answer = formState.fillBlank
      }
      else {
        formState.answer = fillBlank.value
      }
      if (!Array.isArray(formState.answer)) 
        break
      if (
        formState.answer.length === 0
        || formState.answer.some((item: any) => !item.keyword?.length)
      ) {
        message.error('请添加填空题答案')
        return true
      }
      if (formState.sepscore && formState.answer.some((item: any) => item.score == 0)) {
        message.error('请添加填空题分值')
        return true
      }
      formState.answer = Array.isArray(formState.answer)
        ? JSON.stringify(formState.answer)
        : formState.answer
      break

    case questionType.SHORT_ANSWER:
      if (!isCompound) {
        formState.answer = JSON.stringify(scorepts.value)
      }
      // 校验keyword是否为空

      const answerArr = formState.answer
        ? JSON.parse(formState.answer)
        : [{ keyword: [], score: 0 }]

      if (!answerArr.length) {
        message.error('请添加得分点')
        return true
      }
      if (formState.sepscore && answerArr.some((item: any) => !item.keyword.length)) {
        message.error('请添加得分点内容')
        return true
      }
      break

    case questionType.AlGORITHM_CODING:
      const keywords = Object.values(formState.disablekws).flat()
      if (disabledKeywords.value && (!keywords.length || isFnNameEqualKeyword(keywords, formState.func_name))) {
        // if (activeTab.value === '2') message.error('函数名不能是禁用关键词')
        return true
      }
      break

    case questionType.SORT_ORDER:
      formState.options.forEach((item: any, index: number) => {
        item.value = lettersAlpht[index]
      })
      formState.answer = JSON.stringify(formState.options.map((item: any) => item.value))
      if (formState.answer === '') {
        message.error('请添加排序题答案')
        return true
      }
      break

    case questionType.COMPOSITE:
      if (!subQuestionList.value.length) {
        message.error('请添加子试题')
        return true
      }
      else {
        for (let i = 0; i < subQuestionList.value.length; i++) {
          if (!subQuestionList.value[i].formState.body) {
            message.error('请输入题干')
            return true
          }

          if (
            subQuestionList.value[i].type == '0'
            || subQuestionList.value[i].type == '1'
            || subQuestionList.value[i].type == '6'
          ) {
            for (let j = 0; j < subQuestionList.value[i].formState.options.length; j++) {
              if (!subQuestionList.value[i].formState.options[j].content) {
                message.error(`请输入第${i + 1}个子题的第${j + 1}个选项`)
                return true
              }
            }
            if (subQuestionList.value[i].formState.options.length < 3) {
              message.error('选项个数不能少于3')
              return true
            }
          }
        }
      }

      return subQuestionList.value.some((item: any) => {
        return formatQuestionAnswer(Number(item.type), item.formState, true)
      })
  }
}

const scoreInputTotalRef = ref()
const validateAlgorithmRef = ref()
const activeTab = ref('1')

// 增加选项
function addOption() {
  if (formState.value.options.length >= 11) 
    return message.warn('已达最多选项个数')
  formState.value.options.push({
    content: '',
    value: '',
  })
  formState.value.options.forEach((item, index) => {
    item.value = lettersAlpht[index]
  })
}

// 删除选项
function delOption(e: any, index: number) {
  e.preventDefault()
  e.stopPropagation()
  queueMicrotask(async () => {
    await nextTick()
    createQuestionFormRef.value.validateFields(['options'])
  })
  if (formState.value.type === QuestionEnum['单选题'] && formState.value.options.length <= 2) {
    return message.warning('至少保留2个选项')
  }
  if ([QuestionEnum['多选题'], QuestionEnum['排序题']].includes(formState.value.type) && formState.value.options.length <= 3) {
    return message.warning('至少保留3个选项')
  }
  formState.value.options.splice(index, 1)

  // 重新给每一个选项value赋值
  formState.value.options.forEach((option: any, i: number) => {
    option.value = lettersAlpht[i]
  })

  // 处理已选答案
  if (!formState.value.answer?.length) 
    return // 如果没有答案，则直接返回
  if (formState.value.type === QuestionEnum['单选题']) {
    if (lettersAlpht[index] === formState.value.answer) {
      // 如果删除的是答案，需要清空答案
      formState.value.answer = ''
    }
    else {
      // 如果删除的在答案之前，答案需要减1
      const answerIndex = lettersAlpht.indexOf(formState.value.answer)
      if (answerIndex > index) {
        formState.value.answer = lettersAlpht[answerIndex - 1]
      }
    }
  }
  else if (formState.value.type === QuestionEnum['多选题']) {
    const newAnswer: string[] = []
    formState.value.answer.forEach((item: string) => {
      const answerIndex = lettersAlpht.indexOf(item)
      if (answerIndex > index) {
        newAnswer.push(lettersAlpht[answerIndex - 1])
      }
      else if (answerIndex < index) {
        newAnswer.push(lettersAlpht[answerIndex])
      }
    })
    formState.value.answer = newAnswer
  }

}

// 添加得分点
function addPoints() {
  scorepts.value.push({ keyword: [], score: 0 })
}

// 删除问答题得分点
function delPoints(index: number) {
  scorepts.value.splice(index, 1)
  if (formState.value.sepscore) {
    let totalScore = 0
    scorepts.value.forEach((item: any) => {
      totalScore += Number(item.score)
    })
    scoreInputTotalRef.value.score = totalScore
  }
}

// 监听题干内容
const fillBlank = ref<any>([])
const fillBlankCount = ref(0)
const oldFillBlank = ref('')
// let oldFillBlankCount = ref(0)
const isNeedBodyWatcher = ref(true)
watch(
  () => formState.value.body,
  (val) => {
    if (!isNeedBodyWatcher.value) 
      return
    if (formState.value.type === 5) {
      if (val.length === 0) {
        fillBlank.value.length = 0
        fillBlankCount.value = 0
        oldFillBlank.value = ''
        return
      }
      const res = val.match(/_{2,}/g)
      if (res) {
        if (fillBlankCount.value > res.length) {
          // 说明删除了空格
          let deletedBlankIndexArray = []
          if (res.length === 0) {
            deletedBlankIndexArray.push(0)
          }
          else {
            deletedBlankIndexArray = findDeletedBlanks(oldFillBlank.value, val)
          }
          deletedBlankIndexArray.forEach((item) => {
            fillBlank.value.splice(item, 1)
          })
          // 动态更新该题总分
          if (fillBlank.value.length > 0) {
            formState.value.score = fillBlank.value.reduce(
              (prev: any, current: any) => prev + Number(current.score),
              0,
            )
          }
          else {
            formState.value.score = 0
          }
          console.log('deletedBlankIndexArray', deletedBlankIndexArray)
        }
        else if (fillBlankCount.value < res.length) {
          // 说明增加了空格, 于删除空格的逻辑相反
          let addedBlankIndexArray = []
          if (res.length === 1) {
            addedBlankIndexArray.push(0)
          }
          else {
            addedBlankIndexArray = findDeletedBlanks(val, oldFillBlank.value)
          }
          console.log('addedBlankIndexArray', addedBlankIndexArray)
          addedBlankIndexArray.forEach((item) => {
            fillBlank.value.splice(item, 0, { score: 0, id: uuid() })
          })
        }

        fillBlankCount.value = res.length
      }
      else {
        if (fillBlank.value.length > 0) {
          fillBlank.value.length = 0
          fillBlankCount.value = 0
        }
      }
      oldFillBlank.value = val
    }
  },
)

watch(
  () => formState.value.sepscore,
  (val) => {
    let totalScore = 0
    if (val) {
      fillBlank.value.forEach((item: any) => {
        totalScore += Number(item.score)
        formState.value.score = totalScore
      })
    }
  },
)

const firstLoad = ref(false)
watch(
  () => formState.value.parameters,
  (val) => {
    if (!val) 
      return
    if (firstLoad.value) {
      const paramNameList = val.filter(item => item).map((item: any) => item.name)
      formState.value.btestcase.forEach((item: any) => {
        if (paramNameList.length === 0) {
          item.input = []
          item.output.length = 0
        }
        if (!item.input.length || JSON.stringify(item.input) === '[{"":""}]') {
          item.input = []
          paramNameList.forEach((name: any) => {
            item.input.push({ [name]: '' })
          })
        }
        else {
          paramNameList.forEach((name: any, index: number) => {
            if (item.input.length > paramNameList.length) {
              item.input.pop()
            }
            else if (item.input.length < paramNameList.length) {
              item.input.push({ [name]: '' })
            }
            else {
              item.input[index] = { [name]: Object.values(item.input[index])[0] }
            }
          })
        }

        if (
          !item.output.length
          || JSON.stringify(item.output) === '[{"type":"value","value":""}]'
        ) {
          item.output.length = 0
          item.output.push({ type: 'value', value: '' })
        }
      })

      formState.value.ptestcase.forEach((item: any) => {
        if (paramNameList.length === 0) {
          item.input = []
          item.output.length = 0
        }
        if (!item.input.length || JSON.stringify(item.input) === '[{"":""}]') {
          item.input = []
          paramNameList.forEach((name: any) => {
            item.input.push({ [name]: '' })
          })
        }
        else {
          paramNameList.forEach((name: any, index: number) => {
            if (item.input.length > paramNameList.length) {
              item.input.pop()
            }
            else if (item.input.length < paramNameList.length) {
              item.input.push({ [name]: '' })
            }
            else {
              item.input[index] = { [name]: Object.values(item.input[index])[0] }
            }
          })
        }

        if (
          !item.output.length
          || JSON.stringify(item.output) === '[{"type":"value","value":""}]'
        ) {
          item.output.length = 0
          item.output.push({ type: 'value', value: '' })
        }
      })
    }
  },
  { immediate: true, deep: true },
)

const funcMainObj = ref({})
const funcMainParams = ref({})
const funcTemplateObj = ref({})
const tagsOptions = ref<any>([])
const route = useRoute()

async function updateTemplate(lang: string, item: any) {
  const result: any = await ojtemplate({
    func_name: formState.value.func_name,
    rtype: JSON.stringify(formState.value.rtype) ,
    parameters: JSON.stringify(formState.value.parameters),
  })
  funcTemplateObj.value[item] = JSON.parse(JSON.stringify(result.func_templete[item]))
  validateAlgorithmRef.value.langMap[lang].counter++
  // validateAlgorithmRef.value.counter++
  // console.log(result.func_templete)
}

function updateValidateCode(validatecode: any) {
  if (firstLoad.value) {
    formState.value.validatecode = validatecode
  }
}

const initFormDataFinished = ref(false)
const pannelLoading = ref(false)
/** 原始的检验代码 */
const rawValidatecode = ref<Record<string, string>>()
// 判断算法校验代码是否有变更
function isValidateCodeChanged(validatecode: Record<string, string>) {
  return Object.keys(validatecode).some(key => rawValidatecode.value?.[key] !== validatecode[key])
}

function convertFieldJsonContent(data, toJson = false) {
    const defaultKeys = ['options', 'disablekws', 'funcMain', 'funcTemplate', 'parameters', 'ptestcase', 'rtype', 'btestcase', 'validatecode']
    defaultKeys.forEach(key => {
      if (toJson) {
        if (data[key]) {
          const _temp = data[key]
          data[key] = JSON.stringify(_temp)
          data[`${key}Json`] = _temp
        }
      } else {
        if (data[`${key}Json`]) {
          data[`${key}`] = data[`${key}Json`]
        }
      }
    })
    data.func_name = data.funcName 
    data.func_templete = data.funcTemplete
}
function convertBoolean(data) {
  Object.keys(data).forEach(key => {
    if (typeof data[key] === 'boolean') {
      data[key] = data[key] ? 1 : 0
    }
  })
}
const quesId = ref('')
watch(
  () => props.qid,
  (id) => {
    if (id) {
      pannelLoading.value = true
      quescontent({ questionBankUUID: id }).then((data: any) => {
        data.category = data.categoryUUID
        if (data.tagList) {
          data.tags = data.tagname = data.tagList
        }
        quesId.value = data.id
        convertFieldJsonContent(data)

        rawType.value = data.type
        pasteValidatecode = data.validatecode
        quesContentRaw.value = data
        Object.keys(formState.value).forEach((key) => {
          formState.value[key] = data[key]
        })
        formState.value.tags = data.tagname
        if (!formState.value.subtopic) {
          formState.value.subtopic = ""
        }
        const score = formState.value.score

        // 是否存在禁用关键字的checkbox回显
        if (formState.value.type === QuestionEnum['算法题']) {
          disabledKeywords.value = Boolean(Object.values(formState.value.disablekws).flat().length)
        }
        formState.value.difficulty = Number(formState.value.difficulty)
        subQuestionList.value = formState.value.subtopic && formState.value.subtopic.map((item: any) => {
          return {
            type: `${item.type}`,
            label: typeName[item.type],
            formState: { ...item },
          }
        })
        if (formState.value.type === 5) {
          const answer = JSON.parse(formState.value.answer)
          answer.forEach((item: any) => {
            if (!item.id) 
              item.id = uuid()
          })
          fillBlank.value = answer
        }
        fillBlankCount.value = fillBlank.value.length
        formState.value.type === 3 && (scorepts.value = JSON.parse(formState.value.answer))
        formState.value.categoryId = data.categoryId
        getAllTags()
        nextTick(() => {
          scoreInputTotalRef.value.score = score
          formState.value.options = data.options
          formState.value.answer = data.answer
          firstLoad.value = true
          if (formState.value.type === 4) {
            delete formState.value.options
          }
          if (data.validatecode) {
            setTimeout(() => {
              rawValidatecode.value = JSON.parse(JSON.stringify(data.validatecode))
              funcTemplateObj.value = data.validatecode
              formState.value.validatecode = data.validatecode
              initFormDataFinished.value = true
            }, 500)
          }
          else {
            initFormDataFinished.value = true
          }
        })
      }).finally(() => pannelLoading.value = false)
    }
  },
  { immediate: true },
)

watch(
  subQuestionList,
  (val) => {
    if(!val) return
    scoreInputTotalRef.value.score = 0
    val.forEach((item: any) => {
      scoreInputTotalRef.value.score += Number(item.formState.score || 0)
    })
  },
  {
    deep: true,
  },
)

let funcName: any, rtype: any, params: any
let pasteValidatecode = null
watchEffect(async () => {
  if (formState.value.type !== QuestionEnum['算法题']) 
    return
  console.log(JSON.stringify(params) !== JSON.stringify(formState.value.parameters))
  if (
    funcName !== formState.value.func_name
    || JSON.stringify(rtype) !== JSON.stringify(formState.value.rtype)
    || JSON.stringify(params) !== JSON.stringify(formState.value.parameters)
  ) {
    // 有效的方法名称
    const validFuncname
      = CHECK_VARIATE_NAME_REG.test(formState.value.func_name)
      && !isFnNameEqualKeyword(
        Object.values(formState.value.disablekws).flat(),
        formState.value.func_name,
      )
    // 有效的返回值类型
    const validRtype = !!formState.value.rtype.type
    // 有效的参数列表
    let validParameters
    try {
      await checkParameters({}, formState.value.parameters)
      validParameters = true
    }
    catch (error) {
      validParameters = false
    }

    if (validFuncname && validRtype && validParameters) {
      funcMainParams.value = {
        funcName: formState.value.func_name,
        rtype: JSON.stringify(formState.value.rtype),
        parameters: JSON.stringify(formState.value.parameters),
      }
      const result: any = await ojtemplate(funcMainParams.value)
      funcMainObj.value = result.func_main
      funcTemplateObj.value = pasteValidatecode || result.func_templete
      funcName = JSON.parse(JSON.stringify(formState.value.func_name))
      rtype = JSON.parse(JSON.stringify(formState.value.rtype))
      params = JSON.parse(JSON.stringify(formState.value.parameters))
      pasteValidatecode = null
    }
  }
})
watch(
  () => formState.value.validatecode,
  (val) => {
    if (activeTab.value === '2' && !pasteValidatecode) {
      setTimeout(() => {
        funcTemplateObj.value = val
      }, 500)
    }
  },
  {
    deep: true,
  },
)

const showJsonEditor = ref(true)
watch(initFormDataFinished, (val) => {
  if (val) {
    setTimeout(() => {
      useWatchForm(formState, formChanged, showJsonEditor)
    }, 500)
  }
})

function getAllTags() {
  alltags({ tag: 'question', /** categoryId: formState.value.categoryId **/ }).then((res: any) => {
    tagsOptions.value = res.map((item: any) => ({ value: item.name, label: item.name }))
  })
}

const subQuestionsRef = ref()
function handleComplexSwitchChange(formState: any, value: boolean, index?: number) {
  if (!value) {
    try {
      handleComplexToNormal(formState)
      if (subQuestionsRef.value?.length && index !== undefined) {
        subQuestionsRef.value[index].handleComplexToNormal?.(formState)
      }
    }
    catch (error) {
      console.log(error)
      message.error('出错了，请刷新重试')
    }
  }
  else {
    formState.complexcontent = formState.body.replaceAll(/</g, '&lt;').replaceAll(/>/g, '&gt;').split('\n').reduce((acc: string, cur: string) => acc += `<p>${cur}</p>`)
  }
}

const scorebasisEditorRef = ref()
const optionEditorsRef = ref()
function handleComplexToNormal(formState: any) {
  if (optionEditorsRef.value && optionEditorsRef.value.length) {
    formState.options.forEach((option: any, index: number) => {
      option.content = optionEditorsRef.value[index].getText()
    })
  }
  if (scorebasisEditorRef.value) {
    formState.scorebasis = scorebasisEditorRef.value.getText()
  }
}

// 插入空位
const bodyInputRef = ref()
const bodyEditorRef = ref<InstanceType<typeof VueQuillEditor>>()
function insetBlank() {
  const TEXT = '__ '
  if (formState.value.complicatedediting) {
    bodyEditorRef.value?.insetText(TEXT)
  }
  else {
    const textareaEle = bodyInputRef.value.$el
    const startPos = textareaEle.selectionStart
    const endPos = textareaEle.selectionEnd
    const rawText = formState.value.body
    formState.value.body = rawText.substring(0, startPos) + TEXT + rawText.substring(endPos)
  }
}

// 添加关键词
const setKeywordVisible = ref<boolean>(false)
const addKeyword = () => setKeywordVisible.value = true
function handleSetKeywordOk(data: LanguageFunc) {
  formState.value.disablekws = data
  setKeywordVisible.value = false
}
// —————————————————————————————————————————————————— AI 生成干扰项 ———————————————————————————————————————————————————————
const generateOptionModalVisible = ref(false)
const generateParams = ref()
const optionGeneratorRef = ref<InstanceType<typeof OptionGenerator>>()
function showGenerateOptionModal() {
  const type = formState.value.type
  const body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  let options = formState.value.options.map((option: any, index: number) => ({ content: option.content, isTrue: formState.value.answer?.includes(option.value) })).filter((i: any) => !!i.content)
  if (![QuestionEnum['单选题'], QuestionEnum['多选题']].includes(type)) {
    return message.error('只有单选题和多选题才能生成干扰项')
  }
  if (!body) {
    return message.error('题干内容不能为空')
  }
  if (options.filter((i: any) => i.isTrue).length === 0) {
    return message.error('使用该功能需要先输入正确的选项，如果您已经输入了正确的选项，请勾选它。')
  }
  if(formState.value.complicatedediting){
    return message.error('很抱歉，由于AI能力限制，暂不支持基于公式或图片的干扰项生成。')
  }
  if (options.some((i: any) => hasImageOrFormula(i.content))) {
    return message.error('很抱歉，由于AI能力限制，暂不支持基于公式或图片的干扰项生成。')
  }
  // 剔除所有选项的所有标签，将纯文本提交
  options = options.map((option: any, index: number) => {
    const content = formState.value.complicatedediting ? optionEditorsRef.value[index].getText() : option.content
    return { content, ...option }
  })
  generateParams.value = { 
    type, 
    body, 
    // options
    optionJson:formState.value.options,
   }
  generateOptionModalVisible.value = true
}

function handleGenerateOptionsChoose() {
  const arr = optionGeneratorRef.value?.list.filter(i => i.checked)
  if (!arr?.length) 
    return message.error('请至少选择一个干扰项')
  const options = formState.value.options
  let arrIndex = 0; let optionIndex = 0
  while (optionIndex < options.length && arrIndex < arr.length) {
    if (options[optionIndex].content.trim() === '') {
      options[optionIndex].content = arr[arrIndex].content
      arrIndex++
      continue
    }
    optionIndex++
  }
  const restOptionNum = 11 - options.length // 最多可添加11个
  // 如果arr剩下的比option剩下还多，就提示
  if (restOptionNum < arr.length - arrIndex) {
    message.warning('已达最多选项个数')
  }
  
  // 如果有多余干扰项，则直接添加到选项末尾
  if (arrIndex < arr.length && restOptionNum > 0) {
    options.push(...arr.slice(arrIndex, arrIndex + restOptionNum).map((i, k) => ({ content: i.content, value: lettersAlpht[options.length + k] })))
  }
  generateOptionModalVisible.value = false
}

// —————————————————————————————————————————————————— AI 答题 ———————————————————————————————————————————————————————
const aiAnswer = ref('')
const aiExplain = ref('')
const aiResult = ref<any>(null)
const getAiAnswerLoading = ref(false)
const getaianswerInput = computed(() => {
  const type = formState.value.type
  const body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  const options = formState.value.options
  return { body, type, options }
})
async function handleGetAiAnswer() {
  aiAnswer.value = ''
  aiExplain.value = ''
  const type = formState.value.type
  const body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  if (!body) 
    return message.error('题干内容不能为空')
  if (!formState.value.options?.filter((i: any) => i.content.trim())?.length && [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['排序题']].includes(type)) 
    return message.error('选项内容不能都为空')
  getAiAnswerLoading.value = true
  try {
    let res:any = await getAiAnswer({ body, type, options: formState.value.options })
    aiResult.value = res.results
    aiAnswer.value = res.answer
    aiExplain.value = res.explain
  }
  catch (error) {
    console.log(error)
  }
  finally {
    getAiAnswerLoading.value = false
  }
}

// —————————————————————————————————————————————————— AI 更改题型 ———————————————————————————————————————————————————————
const isQuestionConvertorShow = ref(false)
const hasConverted = ref(false)
const rawType = ref(0)

function handleConvertBtnClick() {
  if (formState.value.complicatedediting) 
    return message.warning('很抱歉，复杂编辑不支持更改题型')
  isQuestionConvertorShow.value = true
}

async function onConvertConfirm(newFormState: any) {
  isNeedBodyWatcher.value = false
  Object.assign(formState.value, newFormState)
  formState.value.tags =  formState.value.tagList ?? []
  if (newFormState.type === QuestionEnum['填空题']) {
    const answer = JSON.parse(newFormState.answer)
    fillBlank.value = answer.map((i: any) => ({ id: uuid(), ...i }))
    fillBlankCount.value = answer.length
  }

  hasConverted.value = true
  isQuestionConvertorShow.value = false

  await nextTick()
  isNeedBodyWatcher.value = true
}

// 将转换的题目保存为新题目
const convertSaveLoading = ref(false)
async function handleConvertSave() {
  convertSaveLoading.value = true
  try {
    await checkForm()
    // 加括号
    insetParenthesis(formState.value)
    let body = formState.value.body
    let param = {
      // action: 'add',
      // old_id: props.qid,
      // oldId: props.qid,
      question: {
        ...formState.value,
        body, // 题干
        funcName: formState.value.func_name,
      },
    }

    convertFieldJsonContent(param.question,true)
    convertBoolean(param.question)
    param = {
      ...param,
      ...param.question
    }
    delete param.question
    delete param.id
    param.tags = param.tags == '' ? '' : param.tags.join(',')
    await addQuestion([{  
      ...param  
    }])
    message.success('保存成功！')
    emits('close', true)
  }
  catch (error) {
    console.log(error)
    scrollToFirstErrFormItem()
    convertSaveLoading.value = false
    if (activeTab.value === '2') {
      message.error('题目表单校验失败,请检查输入项内容')
    }
  }
}

const disalbedBth = ref(false)
provide('ImageAlignActionHook',{
  before:() =>{
    disalbedBth.value = true
  },
  after:() => {
    disalbedBth.value = false
  }
})
onMounted(() => {
  // getAllTags()
  getSubjectData()
})
</script>

<template>
  <div class="create-question-container">
    <CloseOutlined class="close-btn" @click="handleCancel" />
    <div class="create-question-content">
      <a-form
        ref="createQuestionFormRef"
        class="form"
        :model="formState"
        hide-required-mark="true"
        :rules="rules"
        :colon="false"
        label-align="left"
      >
        <a-spin :spinning="pannelLoading">
          <a-row :gutter="44">
            <a-col :span="8">
              <a-form-item label="题目类型" name="type">
                <div style="display: flex;align-items: center;">
                  <a-select v-model:value="formState.type" disabled placeholder="请选择">
                    <a-select-option :value="questionType.SINGLE_CHOICE">
                      单选题
                    </a-select-option>
                    <a-select-option :value="questionType.MULTIPLE_CHOICE">
                      多选题
                    </a-select-option>
                    <a-select-option :value="questionType.TRUE_OR_FALSE">
                      判断题
                    </a-select-option>
                    <a-select-option :value="questionType.FILL_BLOCK">
                      填空题
                    </a-select-option>
                    <a-select-option :value="questionType.SHORT_ANSWER">
                      问答题
                    </a-select-option>
                    <a-select-option :value="questionType.AlGORITHM_CODING">
                      算法题
                    </a-select-option>
                    <a-select-option :value="questionType.SORT_ORDER">
                      排序题
                    </a-select-option>
                    <!-- <a-select-option :value="questionType.COMPOSITE">复合题</a-select-option> -->
                  </a-select>
                  <!-- <a-button v-if="canConvert && [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['判断题'], QuestionEnum['填空题']].includes(formState.type)" :class="{ disabled: formState.complicatedediting }" class="common-ai-button" style="font-size: 14px; border-radius: 8px;padding: 0 8px; height: 32px;margin-left: 10px;" @click="handleConvertBtnClick">
                    <template #icon>
                      <img
                        src="@/assets/icons/svg/ai.svg"
                        style="margin-right: 4px;width: 14px;"
                      >
                    </template>
                    更改题型
                  </a-button> -->
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="所属题库" name="category">
                <a-tree-select
                  v-if="categoryList.length"
                  v-model:value="formState.category"
                  show-search
                  :tree-data="categoryList"
                  placeholder="添加题库"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  allow-clear
                  :field-names="replaceFields"
                  tree-node-filter-prop="name"
                  :virtual="false"
                  :get-popup-container="
                    (triggerNode: HTMLElement) => {
                      return triggerNode.parentNode
                    }
                  "
                />
              </a-form-item>
            </a-col>
            <a-col :span="4" style="min-width: 180px">
              <a-form-item label="&nbsp;&nbsp;&nbsp;&nbsp;分值" name="score">
                <ScoreInput
                  ref="scoreInputTotalRef"
                  v-model="formState.score"
                  :disabled="
                    ((formState.type === 5 || formState.type === 3) && formState.sepscore)
                      || formState.type === 7
                  "
                  @get-score="getScore"
                />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item>
                <template #label>
                  <span>复杂编辑</span>
                  <a-tooltip placement="right" overlay-class-name="light">
                    <template #title>
                      <span>开启此功能后，可以在文本中插入图片和公式，并对文本进行特殊样式编辑</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2" style="width: 54%;height: 54%;" />
                  </a-tooltip>
                </template>
                <JSwitch
                  v-model:checked="formState.complicatedediting"
                  @change="handleComplexSwitchChange(formState, $event)"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 编程 -->
          <a-tabs v-if="formState.type === 4" v-model:active-key="activeTab" type="card">
            <a-tab-pane key="1" tab="普通录入" />
            <a-tab-pane key="2" tab="高级录入" />
          </a-tabs>

          <div v-show="activeTab === '1'">
            <a-form-item
              v-if="!formState.complicatedediting"
              label="题干内容"
              v-bind="validateInfos.body"
            >
              <a-textarea
                ref="bodyInputRef"
                v-model:value="formState.body"
                :rows="4"
                placeholder="点击编辑"
                @blur="handleBodyBlur"
              />
              <div v-if="formState.type === 5" class="body-tip">
                <svg-icon name="tip" class="tip-icon" />
                <span>点击</span>
                <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
                <span>连续输入两个下划线"__"可增加空位</span>
              </div>
            </a-form-item>
            <a-form-item v-else label="题干内容" v-bind="validateInfos.complexcontent">
              <VueQuillEditor
                ref="bodyEditorRef"
                v-model:text="formState.body"
                v-model:content="formState.complexcontent"
                @blur="handleComplexContentBlur"
              />
              <div v-if="formState.type === 5" class="body-tip">
                <svg-icon name="tip" class="tip-icon" />
                <span>点击</span>
                <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
                <span>连续输入两个下划线"__"可增加空位</span>
              </div>
            </a-form-item>
          </div>
          <!-- 问答题 -->
          <a-form-item v-if="formState.type === 3" label="评分依据" name="scorebasis">
            <a-textarea
              v-if="!formState.complicatedediting"
              v-model:value="formState.scorebasis"
              :rows="4"
              placeholder="点击编辑"
            />
            <VueQuillEditor
              v-else
              ref="scorebasisEditorRef"
              v-model:content="formState.scorebasis"
            />
            <p class="standard-tip">
              (仅阅卷老师可见)
            </p>
          </a-form-item>
          <a-form-item
            v-if="formState.type === 3"
            label="得分点"
            name="answer"
            class="points-wrapper"
          >
            <template v-for="(item, index) in scorepts" :key="index">
              <div class="item">
                <div class="index">
                  {{ index + 1 }}
                </div>
                <div class="tags-select">
                  <JTagInput
                    v-model="item.keyword"
                    :ignorecase="formState.ignorecase"
                    placeholder="输入完成后按回车添加多个关键词"
                  />
                </div>
                <div v-if="formState.sepscore" class="score-wrapper">
                  <span class="score">分值</span>
                  <ScoreInput
                    v-model="item.score"
                    class="score-input"
                    @get-score="getPointsScore($event, item)"
                  />
                </div>
                <div class="del-icon-wrap">
                  <svg-icon
                    class="del-icon"
                    name="circle-del"
                    width="16px"
                    height="16px"
                    @click="delPoints(index)"
                  />
                </div>
              </div>
            </template>
            <div class="addpoints-btn">
              <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
            </div>
          </a-form-item>

          <template v-if="formState.type === 4">
            <div v-show="activeTab === '1'">
              <a-form-item label="方法名称" name="func_name">
                <div class="func-name-wrapper">
                  <a-input
                    v-model:value.lazy.trim="formState.func_name"
                    class="form-input"
                    placeholder="方法名称"
                    autocomplete="off"
                  />
                  <span class="func-extra-info">(以字母、下划线或美元符号开头，可包含字母、数字、下划线或美元符号，且大小写敏感，避免使用关键字，推荐采用驼峰命名法)</span>
                </div>
              </a-form-item>
              <a-form-item label="返回值类型" name="rtype">
                <div class="rtype-wrapper">
                  <a-select
                    v-model:value="formState.rtype.type"
                    placeholder="返回值类型"
                    class="form-input"
                  >
                    <template v-for="option in varType" :key="value">
                      <a-select-option :value="option.value">
                        {{ option.label }}
                      </a-select-option>
                    </template>
                  </a-select>
                  <!-- <a-input class="form-input" v-model:value="formState.rtype.type" /> -->
                  <a-checkbox v-model:checked="formState.rtype.is_list" class="form-checkbox" />
                  <span class="rtype-extra-info">&nbsp;此变量是数组或列表</span>
                </div>
              </a-form-item>
              <a-form-item label="参数列表" name="parameters">
                <ParamList
                  :parameters="formState.parameters"
                  @add-param="handleAddParameter"
                  @del-param="handleDelParameter"
                />
              </a-form-item>
              <a-form-item label="题干测试用例" name="btestcase">
                <TestInstance
                  :example-i-o="formState.btestcase"
                  @add-instance="handleAddInstance"
                  @del-instance="handleDelInstance"
                />
              </a-form-item>
              <a-form-item label="批卷测试用例" name="ptestcase">
                <TestInstance
                  :example-i-o="formState.ptestcase"
                  @add-instance="addTestCase"
                  @del-instance="delTestCase"
                />
              </a-form-item>
              <a-form-item label="题目验证" name="validateCase">
                <ValidateAlgorithm
                  ref="validateAlgorithmRef"
                  :func-main="funcMainObj"
                  :func-main-params="funcMainParams"
                  :func-template="funcTemplateObj"
                  :form-state="formState"
                  @update-template="updateTemplate"
                  @update-validate-code="updateValidateCode"
                />
              </a-form-item>
              <a-form-item label="难度" name="difficulty">
                <a-radio-group v-model:value="formState.difficulty">
                  <a-radio :value="0">
                    简单
                  </a-radio>
                  <a-radio :value="1">
                    中等
                  </a-radio>
                  <a-radio :value="2">
                    困难
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <div v-if="activeTab === '2'">
              <JsonEditorVue
                v-if="showJsonEditor"
                v-model="formState"
                mode="tree"
                class="jse-theme-dark"
              />
            </div>
          </template>

          <!-- 添加复合题 -->
          <a-collapse v-if="formState.type == 7 && subQuestionList.length">
            <template v-for="(item, index) in subQuestionList" :key="index">
              <a-collapse-panel :header="`【${item.label}】 ${item.formState.body ?? ''}`">
                <template #extra>
                  <span class="complex-switch-box" @click.stop>
                    <span class="label">复杂编辑</span>
                    <JSwitch
                      v-model:checked="item.formState.complicatedediting"
                      @change="handleComplexSwitchChange(item.formState, $event, index)"
                    />
                  </span>
                  <img src="@/assets/images/delete.png" alt="删除" @click="handleDelete(index)">
                  <img
                    v-if="index !== 0"
                    style="margin-left: 16px"
                    src="@/assets/icons/svg/compoundUp.svg"
                    alt="上移"
                    @click="handleUp(index)"
                  >
                  <img
                    v-if="index !== subQuestionList.length - 1"
                    style="margin-left: 16px"
                    src="@/assets/icons/svg/compoundDown.svg"
                    alt="下移"
                    @click="handleDown(index)"
                  >
                </template>
                <template v-if="item.type == 0">
                  <SingleChoice ref="subQuestionsRef" v-model="item.formState" />
                </template>
                <template v-else-if="item.type == 1">
                  <MultipleChoice ref="subQuestionsRef" v-model="item.formState" />
                </template>
                <template v-else-if="item.type == 2">
                  <JudgingQuestions
                    ref="subQuestionsRef"
                    v-model="item.formState"
                  />
                </template>
                <template v-else-if="item.type == 3">
                  <QuestionAndAnswer
                    ref="subQuestionsRef"
                    v-model="item.formState"
                  />
                </template>
                <template v-else-if="item.type == 5">
                  <BlankFilling ref="subQuestionsRef" v-model="item.formState" />
                </template>
                <template v-else-if="item.type == 6">
                  <OrderQuestion ref="subQuestionsRef" v-model="item.formState" />
                </template>
              </a-collapse-panel>
            </template>
          </a-collapse>
          <!-- 单选题 -->
          <a-form-item v-if="formState.type == 0" name="options" class="question-options">
            <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
              <template v-for="(item, index) in formState.options" :key="index">
                <div class="item-option">
                  <a-radio :value="lettersAlpht[index]">
                    <span class="option-radio" :style="{ 'margin-right': formState.complicatedediting ? '10px' : '18px' }">{{ lettersAlpht[index] }}</span>
                  </a-radio>
                  <a-textarea
                    v-if="!formState.complicatedediting"
                    v-model:value="item.content"
                    class="option-content"
                    :auto-size="{ minRows: 1 }"
                    @blur="handleOptionBlur"
                    placeholder="点击，编辑选项；选中即正确答案"
                  />
                  <div v-else class="editor-wrapper">
                    <VueQuillEditor
                      ref="optionEditorsRef"
                      v-model:content="item.content"
                      @blur="handleOptionBlur"
                    />
                  </div>
                  <svg-icon
                    class="del-icon"
                    name="circle-del"
                    width="16px"
                    height="16px"
                    @click.capture="delOption($event, index)"
                  />
                </div>
              </template>
            </a-radio-group>
            <div style="display: flex; align-items: center; margin-top: 16px;">
              <div class="add-option-btn" @click="addOption">
                <svg-icon name="plus" class="mr-[5px]"/>
                <span>添加选项</span>
              </div>
              <a-button class="common-ai-button" style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;" @click="showGenerateOptionModal">
                <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;">
                生成干扰项
              </a-button>
              <a-tooltip placement="right" overlay-class-name="light">
                <template #title>
                  <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
            </div>
          </a-form-item>
          <!-- 多选题 -->
          <a-form-item v-else-if="formState.type == 1" name="options" class="question-options">
            <a-checkbox-group v-model:value="formState.answer" style="max-width:100%;">
              <template v-for="(item, index) in formState.options" :key="index">
                <div class="item-option">
                  <a-checkbox :value="lettersAlpht[index]">
                    <span class="option-radio">{{ lettersAlpht[index] }}</span>
                  </a-checkbox>
                  <a-textarea
                    v-if="!formState.complicatedediting"
                    v-model:value="item.content"
                    class="option-content"
                    :auto-size="{ minRows: 1 }"
                    @blur="handleOptionBlur"
                    placeholder="点击，编辑选项；选中即正确答案"
                  />
                  <div v-else class="editor-wrapper">
                    <VueQuillEditor
                      ref="optionEditorsRef"
                      @blur="handleOptionBlur"
                      v-model:content="item.content"
                    />
                  </div>
                  <svg-icon
                    class="del-icon"
                    name="circle-del"
                    width="16px"
                    height="16px"
                    @click.capture="delOption($event, index)"
                  />           
                </div>
              </template>
            </a-checkbox-group>
            <div style="display: flex; align-items: center; margin-top: 16px;">
              <div class="add-option-btn" @click="addOption">
                <svg-icon name="plus" />
                <span>添加选项</span>
              </div>
              <a-button class="common-ai-button" style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;" @click="showGenerateOptionModal">
                <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;">
                生成干扰项
              </a-button>
              <a-tooltip placement="right" overlay-class-name="light">
                <template #title>
                  <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
            </div>
          </a-form-item>
          <!-- 判断题 -->
          <a-form-item v-else-if="formState.type == 2" class="question-options">
            <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
              <template v-for="(item, index) in judge" :key="index">
                <div class="item-option">
                  <a-radio :value="item.value">
                    <span class="option-radio">{{ item.value }}</span>
                    <a-input v-model:value="item.content" class="option-content" readonly />
                  </a-radio>
                </div>
              </template>
            </a-radio-group>
          </a-form-item>
          <!-- 填空 -->
          <a-form-item v-else-if="formState.type == 5" class="question-options">
            <template v-for="(item, index) in fillBlank" :key="item.id">
              <div class="fill-blank-item">
                <span class="label">填空{{ index + 1 }}答案</span>
                <!-- <a-select
                  v-model:value="item.keyword"
                  mode="tags"
                  placeholder="点击，编辑选项；点击回车设置多个关键词"
                  @change="handleChangeSelect(item, $event)"
                >
                </a-select> -->
                <JTagInput
                  v-model="item.keyword"
                  :ignorecase="formState.ignorecase"
                  class="tag-ipt"
                  placeholder="点击，编辑选项；点击回车设置多个关键词"
                />
                <div v-if="formState.sepscore" class="score-wrapper">
                  <span class="score">分值</span>
                  <ScoreInput
                    ref="scoreInputRef"
                    v-model="item.score"
                    class="score-input"
                    @get-score="getFillBlankScore($event, item)"
                  />
                </div>
              </div>
            </template>
          </a-form-item>
          <!-- 排序题 -->
          <a-form-item v-if="formState.type === 6" name="options" class="question-options">
            <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
              <span class="optionContent">选项内容</span>
              <template v-for="(item, index) in formState.options" :key="index">
                <div class="sortingQuestionOptions item-option">
                  <a-radio :value="numberAlpht[index]">
                    <span class="option-radio">{{ numberAlpht[index] }}</span>
                  </a-radio>
                  <a-textarea
                    v-if="!formState.complicatedediting"
                    v-model:value="item.content"
                    class="option-content order-option-content"
                    :auto-size="{ minRows: 1 }"
                    placeholder="点击，编辑选项"
                    @blur="handleOptionBlur"
                  />
                  <div v-else class="editor-wrapper">
                    <VueQuillEditor
                      ref="optionEditorsRef"
                      v-model:content="item.content"
                      @blur="handleOptionBlur"
                    />
                  </div>
                  <svg-icon
                    class="del-icon"
                    name="circle-del"
                    width="16px"
                    height="16px"
                    @click.capture="delOption($event, index)"
                  />
                </div>
              </template>
              <span class="rightOrder">（按正确顺序设置）</span>
            </a-radio-group>
            <div class="add-option-btn sort-question" @click="addOption">
              <svg-icon name="plus" />
              <span>添加选项</span>
            </div>
          </a-form-item>
          <!-- 复合题 -->
          <a-form-item
            v-else-if="formState.type == 7"
            class="question-options compound"
            name="subQuestionList"
          >
            <a-radio-group v-model:value="formState.answer">
              <a-dropdown trigger="click" class="compoundQuestions">
                <template #overlay>
                  <a-menu class="compoundItem">
                    <template v-for="(item, index) in questionsObj" :key="item.key">
                      <a-menu-item @click="addCompoundQuestions(item)">
                        {{
                          item.questionCate
                        }}
                      </a-menu-item>
                    </template>
                  </a-menu>
                </template>
                <!-- <img src="@/assets/images/add.png" alt=""> -->
                <a-button class="compoundQuestionsBtn" type='text'>
                  <img src="@/assets/icons/svg/addItem.svg" alt="请选择子试题" class="addItem">
                  请选择子试题
                </a-button>
              </a-dropdown>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-show="activeTab === '1'" name="tags" style="width: 220px">
            <a-select
              v-model:value="formState.tags"
              class="tag-select"
              mode="tags"
              dropdown-class-name="tag-select-wrapper"
              :options="tagsOptions"
              placeholder="添加标签"
              :get-popup-container="
                (triggerNode: HTMLElement) => {
                  return triggerNode.parentNode
                }"
            />
          </a-form-item>
          <template v-if="formState.type === 4">
            <div v-show="activeTab === '1'" class="fill-blank-config">
              <div style="margin-bottom: 6px">
                <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
                <span>本题适用于“部分测试用例通过可得分”</span>
                <a-tooltip placement="right">
                  <template #title>
                    <span>若勾选“部分测试用例通过可得分”，则算法题得分按通过测试用例占总数的百分比计算；未勾选时，需全部通过测试用例才能得分。</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2" />
                </a-tooltip>
              </div>
              <div style="margin-bottom: 4px">
                <a-checkbox v-model:checked="disabledKeywords" class="check-box" />
                <span>存在禁用关键词</span>
                <a-tooltip placement="right">
                  <template #title>
                    <span>禁止学生答题时使用一些系统内置的方法或关键字</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2" />
                </a-tooltip>
                <div v-if="disabledKeywords" class="set-keyword-btn" @click="addKeyword">
                  批量设置
                </div>
              </div>
              <a-form-item
                v-if="disabledKeywords"
                label="禁用关键词"
                class="disablekws-wrapper"
              >
                <template v-for="item in Object.keys(formState.disablekws)" :key="item">
                  <div class="item">
                    <span class="label">{{ item }}</span>
                    <a-select
                      v-model:value="formState.disablekws[item]"
                      class="tags-select"
                      mode="tags"
                      placeholder="输入完成后按回车添加多个关键词"
                      :get-popup-container="
                        (triggerNode: HTMLElement) => {
                          return triggerNode.parentNode
                        }"
                    >
                      <a-select-opt-group
                        v-for="group in languageFunctions.find(el => el.language === item)?.groups"
                        :key="group.key"
                        :label="group.label"
                      >
                        <a-select-option
                          v-for="func in group.functions"
                          :key="`${group.key}#${func.funcname}`"
                          :value="func.funcname"
                        >
                          {{ func.funcname }}
                        </a-select-option>
                      </a-select-opt-group>
                    </a-select>
                  </div>
                </template>
                <div v-if="!Object.values(formState.disablekws).flat().length" class="err-tip">
                  请输入禁用关键词
                </div>
                <div
                  v-if="
                    isFnNameEqualKeyword(
                      Object.values(formState.disablekws).flat(),
                      formState.func_name,
                    )
                  "
                  class="err-tip"
                >
                  方法名称不能是禁用关键词之一，请修改方法名
                </div>
              </a-form-item>
            </div>
          </template>

          <div v-if="formState.type === 3" class="fill-blank-config">
            <div>
              <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
              <span>忽略大小写</span>
            </div>
          </div>
          <div v-if="formState.type === 1" class="fill-blank-config">
            <div>
              <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
              <span>本题适用于“部分回答正确时可得分”</span>
              <a-tooltip placement="right">
                <template #title>
                  <span>当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
            </div>
          </div>
          <div v-if="formState.type === 5" class="fill-blank-config">
            <div>
              <a-checkbox v-model:checked="formState.ordered" class="check-box" />
              <span>判分时区分答案先后顺序</span>
            </div>
            <div>
              <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
              <span>忽略大小写</span>
            </div>
          </div>
          <div v-if="formState.type === 6" class="fill-blank-config">
            <div>
              <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
              <span>本题适用于“部分回答正确时可得分”</span>
              <a-tooltip placement="right">
                <template #title>
                  <span>当考试设置为“部分回答正确时可得分”，并且本题勾选了此选项，则学生的答案中包含连续正确的顺序不少于2个
                    时，可按照比例得分；否则，在没有完全回答正确的情况下不得分。</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
            </div>
          </div>
          <!-- 校对状态 -->
          <template v-if="formState.proofreading_info?.proofreading">
            <p class="proofread-record">
              <img v-if="formState.proofreading_info.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
              <img v-if="formState.proofreading_info.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
              <span style="margin-left: 4px;">由{{ formState.proofreading_info.is_ai ? 'AI' : formState.proofreading_info.teacher_name }}于{{ formState.proofreading_info.update_at }}{{ formState.proofreading_info.is_ai ? '判断' : '确认' }}该题</span>
              <span v-if="formState.proofreading_info.proofreading === 1" style="color: #38910b;font-weight: bold;">正确</span>
              <span v-if="formState.proofreading_info.proofreading === 2" style="color: #dc2b28;font-weight: bold;">错误</span>
            </p>
            <div v-if="formState.proofreading_info.proofreading === 2" class="proofread-reason">
              <span class="label" style="font-size: 12px;">错误原因</span>
              <div style="font-size: 12px;">
                {{ formState.proofreading_info.wrong_reason ? formState.proofreading_info.wrong_reason : "未填写" }}
              </div>
            </div>
          </template>
          <div v-if="![QuestionEnum['算法题']].includes(formState.type)" style="display: flex;align-items: center;margin-top: 16px;">
            <a-button :loading="getAiAnswerLoading" class="common-ai-button" style="font-size: 14px; margin-left: 2px;border-radius: 8px;" @click="handleGetAiAnswer">
              <template #icon>
                <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
              </template>
              答题
            </a-button>
            <a-tooltip placement="right" overlay-class-name="light">
              <template #title>
                <span>自动生成当前题目的答案解析</span>
              </template>
              <svg-icon class="common-info-icon" name="info2" />
            </a-tooltip>
          </div>
          <template v-if="aiAnswer || aiExplain || (aiResult && aiResult.length)">
            <template v-for="({answer: aiAnswer, explain:aiExplain}) in aiResult" :key="aiAnswer">
            <div class="ai-answer-panel">
              <div>
                <div class="label">
                  AI 答案
                </div>
                <div class="value">
                  {{ aiAnswer }}
                </div>
              </div>
              <div style="margin-top: 8px;">
                <div class="label">
                  答题解析
                </div>
                <div class="value">
                  <FoldText
                    :text="aiExplain" 
                    :fold-line-clamp="3" 
                    background-color="#f5f5f5"
                    :feedback-params="{
                      apiname: 'getaianswer',
                      input: getaianswerInput,
                      output: {
                        answer: aiAnswer,
                        explain: aiExplain,
                      },
                    }"
                  />
                </div>
              </div>
            </div>
            <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
              <template #icon>
                <InfoCircleFilled />
              </template>
              <template #message>
                温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
              </template>
            </a-alert>
          </template>
          </template>
        </a-spin>
      </a-form>
      <div class="footer">
        <a-button type="primary" :loading="saveBtnLoading" :disabled='disalbedBth' @click="handleSaveBtnClick">
          {{ hasConverted ? '更新原题目' : '保存' }}
        </a-button>
        <a-button v-if="hasConverted" :loading="convertSaveLoading" @click="handleConvertSave">
          保存为新题目
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </div>
    </div>
    <a-modal v-model:visible="generateOptionModalVisible" title="生成干扰项" :destroy-on-close="true" :keyboard="false" :mask-closable="false" @ok="handleGenerateOptionsChoose" @cancel="optionGeneratorRef?.setList([])">
      <OptionGenerator ref="optionGeneratorRef" :generate-params="generateParams" />
    </a-modal>
    <a-modal
      v-model:visible="confirmSaveVisible"
      :mask-closable="false"
      wrap-class-name="confirm-modal"
      @cancel="saveBtnLoading = false"
    >
      <template #title>
        <p style="display: flex; align-items: center">
          <InfoCircleFilled style="color: #faad14;font-size: 20px;" />
          <span style="margin-left: 8px; font-size: 18px; font-weight: bold">确定保存当前编辑？</span>
        </p>
      </template>
      保存后可能需要重新校对该题目
      <template #footer>
        <div>
          <a-button @click="confirmSaveVisible = false, saveBtnLoading = false">
            取消
          </a-button>
          <a-button type="primary" @click="save">
            确认
          </a-button>
        </div>
        <a-checkbox v-model:checked="isNoMoreReminders" @change="handleNoMoreRemindersChange">
          不再提醒
        </a-checkbox>
      </template>
    </a-modal>
    <SetKeyword
      :visible="setKeywordVisible"
      :checked-functions="formState.disablekws"
      @close="setKeywordVisible = false"
      @ok="handleSetKeywordOk"
    />
    <QuestionConvertor v-if="isQuestionConvertorShow" :id="qid" :quesId="quesId" v-model:visible="isQuestionConvertorShow" :raw-type="rawType" :form-state="formState" @confirm="onConvertConfirm" />
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-spin-nested-loading) {
  .ant-spin-container {
    padding-bottom: 20px;
    padding-left: 2px;
  }
}
.ai-answer-panel {
  background-color: #f5f5f5;
  width: 752px;
  border-radius: 8px;
  padding: 16px 48px;
  margin-top: 16px;
  > div {
    display: flex;
    .label {
      width: 80px;
      font-size: 12px;
      color: #626262;
      margin-right: 16px;
    }
    .value {
      flex: 1;
      min-width: 0;
      font-size: 12px;
    }
  }
}
.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 16px;
  display: flex;
  align-items: center;
  position: relative;
  left: -4px;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
    flex-shrink: 0;
  }
}
:deep(.tagify) {
  border: none;
  font-size: 12px;
  align-items: center;
  .tagify__input {
    &::before {
      font-size: 12px;
    }
  }
  .tagify__tag-text {
    font-size: 12px;
  }
}
:deep(.ant-tree-select-dropdown) {
  .ant-select-tree-node-content-wrapper {
    display: flex;
    align-items: center;
    flex-shrink: 0 !important;
  }
}
.err-tip {
  font-size: 14px;
  color: #f5222d;
}
.addItem {
  margin-top: -1px;
}
:deep(.ant-collapse-item) {
  background-color: #f2f5fc;
}
:deep(.ant-collapse-header) {
  font-size: 12px;
}
:deep(.ant-dropdown-menu-item-only-child) {
  font-size: 12px;
}
:deep(.ant-dropdown-menu-item-only-child):hover {
  background-color: #f1f4fe;
}
:deep(.sortingQuestionOptions .ant-radio) {
  display: none;
}
.ant-form label {
  font-size: 12px;
}
.create-question-container {
  position: relative;
  height: 100%;
  padding: 0 20px;
  .close-btn {
    position: absolute;
    top: -60px;
    font-size: 18px;
    right: 10px;
    cursor: pointer;
    color: #929294;
    transition: all ease 0.2s;
    &:hover {
      color: #696969;
    }
  }
  .create-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .create-question-content {
    display: flex;
    flex-direction: column;
    position: relative;
    min-width: 968px;
    overflow: auto;
    height: 100%;
    background-color: #fff;
    .form-input {
      width: 220px;
      height: 32px;
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.tag-select .ant-select-selector) {
      width: 220px;
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.ant-select-tree-title) {
      font-size: 12px;
    }
    :deep(.ant-form-item-label > label) {
      font-size: 12px;
      height: 26px;
      color: rgba(0, 0, 0, 0.65);
    }
    :deep(.ant-select-selector) {
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(textarea.ant-input) {
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.ant-tabs-tab) {
      font-size: 12px;
      background-color: #f5f5f5;
    }
    :deep(.ant-tabs-tab-active){
      color: #5478EE;
      background: #fff;
    }
    .form {
      margin-bottom: auto;
      flex: 1;
      min-height: 0;
      overflow: hidden auto;
      padding-right: 20px;
      :deep(.ant-col) {
        width: 80px;
      }
      .disablekws-wrapper {
        display: flex;
        align-items: flex-start;
        .item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          line-height: 26px;
          &:last-of-type {
            margin-bottom: 0;
          }
          .label {
            width: 70px;
            line-height: 32px;
          }
        }
        .tags-select {
          display: block;
          width: 520px;
          :deep(.ant-select-selection-item-content) {
            font-size: 12px;
          }
        }
      }
      .standard-tip {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
      .points-wrapper {
        .item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-size: 12px;
          .index {
            flex: none;
            width: 20px;
            height: 28px;
            text-align: left;
            line-height: 28px;
          }
          .tags-select {
            width: 80%;
            margin: 0 16px 0 6px;
          }
          :deep(.ant-select-selection-item) {
            font-size: 12px;
          }
        }
        .score-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          margin-right: 16px;
          .score {
            flex-grow: 0;
            flex-shrink: 0;
            margin-right: 16px;
          }
          .score-input {
            flex-grow: 0;
            flex-shrink: 0;
          }
        }
        .del-icon-wrap {
          flex: none;
          width: 16px;
          height: 32px;
          display: flex;
          align-items: center;
          .del-icon {
            cursor: pointer;
          }
        }
        .addpoints-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 82px;
          height: 24px;
          background: #ffffff;
          border: 1px solid rgba(0, 0, 0, 0.15);
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          margin-top: 10px;
        }
      }
      .complex-switch-box {
        padding: 0 5px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
        margin-right: 20px;
        .label {
          margin-right: 10px;
        }
      }
    }
    .func-name-wrapper,
    .rtype-wrapper {
      display: flex;
      align-items: center;
      .func-extra-info {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-left: 12px;
      }
      .rtype-extra-info {
        font-size: 12px;
      }
      .form-checkbox {
        position: relative;
        bottom: -1px;
        margin: 0 10px 0 12px;
      }
    }

    .fill-blank-config {
      div {
        display: flex;
        align-items: center;
        line-height: 28px;
      }
      .check-box {
        margin-right: 8px;
      }
      span {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
      }
      .set-keyword-btn {
        width: 70px;
        height: 24px;
        margin-left: 16px;
        border: 1px solid rgba(0,0,0,0.15);
        border-radius: 4px;
        justify-content: center;
        font-size: 12px;
        color: rgba(0,0,0,0.65);
        text-align: center;
        line-height: 23px;
        cursor: pointer;
      }
      .tip-icon {
        margin-left: 8px;
      }
    }
    .body-tip {
      display: flex;
      align-items: center;
      background: #f1f4fe;
      border-radius: 4px;
      font-size: 12px;
      line-height: 24px;
      margin-top: 8px;
      .tip-icon {
        margin: 0 8px;
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
    .ant-btn {
      padding: 5px 16px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
      margin-right: 8px;
    }
    .submit-btn {
      border: 1px solid rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.compound {
  margin-top: 24px;
}
.question-options {
  .sortingQuestionOptions {
    margin-left: 72px;
    margin-top: -5px;
  }
  .rightOrder {
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.5);
    margin-left: 70px;
  }
  .optionContent {
    position: absolute;
    width: 48px;
    height: 18px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #626262;
    line-height: 18px;
  }
  .compoundQuestions {
    width: 120px;
    height: 32px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #5478ee;
    line-height: 18px;
    border-radius: 8px;
  }
  .item-option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    position: relative;
    max-width: 100%;
    .option-radio {
      // margin-right: 18px;
      display: inline-block;
      width: 20px;
    }
    .option-content {
      font-size: 12px;
      border: none;
      box-shadow: none;
      width: 220px;
    }
    .order-option-content {
      width: 220px;
    }
    .editor-wrapper {
      padding: 3px 11px;
      min-width: 0;
    }
    .del-icon {
      visibility: hidden;
      cursor: pointer;
      flex-shrink: 0;
    }
  }
  .fill-blank-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .label {
      width: 70px;
      font-size: 12px;
    }
    .score-wrapper {
      width: 160px;
      // min-width: 160px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .score {
        flex-grow: 0;
        flex-shrink: 0;
      }
      .score-input {
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
    :deep(.ant-select-show-search) {
      // width: 600px !important;
    }
    :deep(.ant-select-selector) {
      border: none;
      box-shadow: none;
    }
  }

  :deep(.item-option > span:nth-child(2)) {
    display: flex;
    align-items: center;
  }
  :deep(.item-option > span:nth-child(2) > .ant-input) {
    font-size: 12px;
    border: 0px;
  }
  .item-option:hover .del-icon {
    position: relative;
    visibility: visible;
  }
  .add-option-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 82px;
    height: 24px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
  }
  .sort-question {
    margin-left: 77px;
  }
}
// textarea图标间距
:deep(.ql-toolbar.ql-snow .ql-formats) {
    margin-right: 0px;
}

.ant-alert-info {
  border: none;
}
</style>

<style lang="less">
.tag-select-wrapper {
  .rc-virtual-list-holder-inner {
    padding-left: 4px;
    display: flex;
    // flex-direction: row !important;
    flex-wrap: wrap;
  }
  .ant-select-item {
    margin-right: 4px;
    margin-bottom: 4px;
  }
  .ant-select-item-option-content {
    margin-right: 4px;
  }
  .ant-select-item-option-state {
    display: none;
  }
}
.ant-select-item-option-content {
  font-size: 12px;
}
.jse-menu {
  display: none !important;
}
.jse-main {
  height: 560px !important;
  margin-bottom: 16px;
}

.confirm-modal {
  .ant-modal-header {
    border: none;
  }
  .ant-modal-body {
    padding: 10px 0 10px 55px;
  }
  .ant-modal-footer {
    border: none;
    padding-bottom: 22px;
    padding-top: 22px;
    padding-left: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .ant-btn {
      border-radius: 8px;
    }
  }
}
// 确定按钮和取消按钮交换位置
.ant-modal .ant-modal-footer {
    flex-direction: row-reverse;
}
.ql-toolbar.ql-snow {
  border-radius: 8px 8px 0 0;
}
.ql-container.ql-snow {
  border-radius: 0 0 8px 8px;
}
</style>
