<script setup lang="tsx">
import BlankFilling from '@/views/QuestionManage/components/BlankFilling.vue'
import JudgingQuestions from '@/views/QuestionManage/components/JudgingQuestions.vue'
import MultipleChoice from '@/views/QuestionManage/components/MultipleChoice.vue'
import SortQuestions from '@/views/QuestionManage/components/OrderQuestion.vue'
import AnswerQiestions from '@/views/QuestionManage/components/QuestionAndAnswer.vue'
import SingleChoice from '@/views/QuestionManage/components/SingleChoice.vue'

const props = defineProps<{
  componentType: number
  composite?: boolean
}>()

enum QuestionEnum {
  SingleChoice,
  MultipleChoice,
  JudgingQuestions,
  AnswerQiestions,
  AlgorithmQiestions,
  BlankFilling,
  SortQuestions,
  compositeQuestions,
}
const subCompFileMap = import.meta.glob('./*.vue')
const CompMap = Object.create(null)
const EditorRealCompRef = useTemplateRef('EditorRealCompRef')
Object.entries(subCompFileMap).forEach(([key, value]) => {
  CompMap[key.replace('./', '').replace('.vue', '')] = value
})
const RenderComponent = ref(null)
const comopsiteBackupComp = {
  SingleChoice,
  MultipleChoice,
  JudgingQuestions,
  AnswerQiestions,
  BlankFilling,
  SortQuestions,
}
watch(() => props.componentType, () => {
  if (props.composite) {
    RenderComponent.value = comopsiteBackupComp[QuestionEnum[props.componentType]]
  }
  else {
    CompMap[QuestionEnum[props.componentType]]().then((comp) => {
      RenderComponent.value = comp.default
    })
  }
}, {
  immediate: true,
})
defineExpose({
  EditorRealCompRef,
})
</script>

<template>
  <RenderComponent ref="EditorRealCompRef" class="render-container" />
</template>

<style lang="less">
@import url("./style/common.less");
</style>
