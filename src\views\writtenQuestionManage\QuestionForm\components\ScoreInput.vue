<template>
    <div class="score-input-container">
        <div @click="subScore" class="substract-btn" :class="{ disabled: score <= 1 || disabled }">
            <svg-icon name="substract" />
        </div>
        <a-input-number class="score-input" :controls="false" :disabled="disabled" :min="1" v-model:value="score"
            :formatter="(value: number) => Math.floor(value)" />
        <div @click="addScore" class="add-btn" :class="{ disabled: disabled }">
            <svg-icon name="plus" />
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { debounce } from '@/utils/common'
import { ref, watch, watchEffect } from 'vue'

const props = withDefaults(defineProps<{
    disabled: boolean
    modelValue: number
}>(), {
    disabled: false,
    modelValue: 1
})

const emits = defineEmits(['update:modelValue'])

const score = ref(1)
watchEffect(() => {
    score.value = props.modelValue
})
watch(score, debounce((val: number) => {
    emits('update:modelValue', val)
}, 500))

const subScore = () => {
    if (score.value <= 1 || props.disabled) return
    score.value--
}

const addScore = () => {
    if (props.disabled) return
    score.value++
}
</script>
  
<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
}

/* 火狐 */
input {
    -moz-appearance: textfield;
}

.score-input-container {
    overflow: hidden;
    user-select: none;
    display: flex;
    align-items: center;
    width: 96px;
    height: 32px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;

    .substract-btn,
    .add-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        height: 100%;
        cursor: pointer;
    }

    .substract-btn {
        border-right: 1px solid #d9d9d9;
    }

    .disabled {
        background: #f5f5f5;
    }

    .add-btn {
        border-left: 1px solid #d9d9d9;
    }

    .score-input {
        flex: 1;
        height: 30px;
        border: none;
        box-shadow: none;

        :deep(.ant-input-number-input) {
            font-size: 12px;
        }

        text-align: center;
    }
}
</style>
  