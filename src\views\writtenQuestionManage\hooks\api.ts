import request from '@/utils/http'


////////////////////////////////////////////
export function getPackageList(data?: any) {
  return request({
    url: '/written/subCategory/packagesStatistic',
    data,
  })
}

export function deletePackageItem(data: any) {
  return request({
    url: '/q/bank/deleteByPath',
    data,
  })
}

export function getAIRecommend(data: any) {
  return request({
    url: '/q/bank/genSubdirectory',
    data,
  })
}

export function createPackageApi(data: any) {
  return request({
    url: '/q/bank/createPackage',
    data,
  })
}

export function createPackageWrittenApi(data: any) {
  return request({
    url: '/written/subCategory/create',
    data,
  })
}

export function createSubjectApi(data: any) {
  return request({
    url: '/q/bank/createSubdirectory',
    data,
  })
}

export function updatePackageApi(data: any) {
  return request({
    url: '/q/bank/edit',
    data,
  })
}

export function deletePackageApi(data: any) {
  return request({
    url: '/q/bank/deleteByPath',
    data,
  })
}

export function createCommonPhrase(data: any) {
  return request({
    url: '/q/commonPhrase/add',
    data,
  })
}

export function getCommonPhrase(data: any) {
  return request({
    url: '/q/commonPhrase/list',
    data,
  })
}

export function editCommonPhrase(data: any) {
  return request({
    url: '/q/commonPhrase/edit',
    data,
  })
}

export function deleteCommonPhrase(data: any) {
  return request({
    url: '/q/commonPhrase/deleteBatch',
    data,
  })
}

export function quesionAdd(data: any) {
  return request({
    url: '/q/question/add',
    data,
  })
}

export function quesionEdit(data: any) {
  return request({
    url: '/q/question/edit',
    data,
  })
}

export function quesionDelete(data: any) {
  return request({
    url: '/q/question/deleteBatch',
    data,
  })
}

export function quesionDeleteBatch(data: any) {
  return request({
    url: '/q/question/batchToRecycle',
    data,
  })
}

export function getQuestionById(data?: any) {
  return request({
    url: '/q/question/queryById',
    data,
  })
}

export function createPreviewVideo(data?: any) {
  return request({
    url: '/q/question/createVideoSync',
    data,
  })
}
export function queryQuestion(data?: object) {
  return request({
    url: '/q/question/list',
    data,
  })
}

export function getPostList(data?: object) {
  return request({
    url: '/position/list',
    data,
  })
}

export function uploadFile(data?: object) {
  return request({
    url: '/resume/uploadResumeFiles',
    data,
  })
}

export function deleteFile(data?: object) {
  return request({
    url: '/resume/batchDeleteTasks',
    data,
  })
}

export function getResumeTaskList(data?: object) {
  return request({
    url: '/resume/queryTaskList',
    data,
  })
}

export function getResumeTaskCount(data?: object) {
  return request({
    url: '/resume/countResumes',
    data,
  })
}

export function deleteCandidate(data?: object) {
  return request({
    url: '/resume/batchDeleteResumes',
    data,
  })
}

export function createInterviewApi(data?: object) {
  return request({
    url: '/exam/add',
    data,
  })
}

export function createTemplateApi(data?: object) {
  return request({
    url: '/exam/createTemplate',
    data,
  })
}

export function getExamDetail(data?: object) {
  return request({
    url: '/exam/queryByResumeId',
    data,
  })
}

export function addAiCheckByQidApi(data?: object) {
  return request({
    url: '/q/check/addAiCheckByQid',
    data,
  })
}

export function addAiCheckByQPathApi(data?: object) {
  return request({
    url: '/q/check/aiCheck',
    data,
  })
}

export function getCheckProcess(data?: object) {
  return request({
    url: '/q/check/progress',
    data,
  })
}

export function getCheckOne(data?: object) {
  return request({
    url: '/q/check/aiCheckOne',
    data,
  })
}

export function manualVerify(data?: object) {
  return request({
    url: '/q/check/save',
    data,
  })
}

export function aiAnswerApi(data?: object) {
  return request({
    url: '/q/check/aiAnswer',
    data,
  })
}

export function getBankProgress(data?: object) {
  return request({
    url: '/q/bank/progress',
    data,
  })
}