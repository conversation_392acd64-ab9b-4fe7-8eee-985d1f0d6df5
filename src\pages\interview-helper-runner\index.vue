<template>
    <div class="interview-helper-runner">
        <RunnerHead ref="headRef" :id="id" :name="runnerDetail?.name" />
        <div class="runner-main">
            <RunnerAside :id="id" :selectable="!loading" @select="handleSelect" />
            <div class="runner-content">
                <div class="runner-content-main">
                    <div :id="'wrapper-' + item.uuid" class="list-item-wrapper" :class="['ai', 'user'][item.type]"
                        v-for="item in list">
                        <!-- ai -->
                        <template v-if="item.type === ConversationRole.AI">
                            <img style="width: 48px;margin-right: 8px;margin-top: -4px;"
                                src="@/assets/images/interview-helper-avatar.png" alt="">
                            <template v-if="item.aiStatus === ConversationAiStatus.回答完毕">
                                <div class="list-item" :class="{ 'full-width': item.qlist?.length }">
                                    <template v-if="item.qlist?.length">
                                        <div class="list-item-content"
                                            :style="{ width: item.qlist?.length ? '100%' : 'auto' }">
                                            <p class="list-item-text">{{ item.text }}</p>
                                            <div class="q-list">
                                                <div class="q-list-item" v-for="(q, i) in item.qlist" :key="q.id">
                                                    <div
                                                        style="display: flex;align-items: center;margin-bottom: 8px;font-weight: bold;">
                                                        <svg-icon class="q-arrow" :class="{ expand: q.expand }"
                                                            name="arrow-down" @click="q.expand = !q.expand"></svg-icon>
                                                        问题{{ i + 1 }}：
                                                    </div>
                                                    <p class="q-body" :class="{ ellipsis: !q.expand }">{{ q.body }}</p>
                                                    <div style="margin-top: 12px;">
                                                        <div style="font-weight: bold;">评分依据：</div>
                                                        <p>{{ q.scorebasis }}</p>
                                                    </div>
                                                    <div class="q-config" style="margin-top: 16px;">
                                                        <div class="q-config-item">
                                                            <span class="label">所属题库：</span>
                                                            <span class="value">{{ q.category_name }}</span>
                                                        </div>
                                                        <div class="q-config-item">
                                                            <span class="label">建议时长：</span>
                                                            <span>{{ q.suggested_time }}分钟</span>
                                                        </div>
                                                        <div class="q-config-item">
                                                            <span class="label">难度：</span>
                                                            <span><svg-icon v-for="i in getStarByScore(q.difficulty)"
                                                                    name="active_star" width="16px" height="16px"
                                                                    style="margin-top: 4px;"></svg-icon></span>
                                                        </div>
                                                    </div>
                                                    <div class="q-footer">
                                                        <a-button :disabled="loading"
                                                            @click="getMoreQues(q, ConversationAction.追问题目)">追问题目</a-button>
                                                        <a-button :disabled="loading"
                                                            @click="getMoreQues(q, ConversationAction.相关题目)">相关题目</a-button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="margin-top: 16px;">若以上题目都不满意，可点击
                                            <a-button type="link" :disabled="loading" style="padding: 0 2px;"
                                                @click="refresh(item)">
                                                <span style="text-decoration: underline;">换一批</span>
                                            </a-button>
                                        </div>
                                    </template>
                                    <div class="list-item-content" v-else>
                                        <p class="list-item-text">抱歉，未能找到匹配的题目</p>
                                    </div>
                                </div>
                            </template>
                            <div v-else-if="item.aiStatus === ConversationAiStatus.正在回答"
                                style="line-height: 40px;margin-left: 16px;">正在生成...</div>
                            <div class="list-item" v-else>
                                <div class="list-item-content">
                                    {{ item.text }}</div>
                            </div>
                        </template>
                        <!-- user -->
                        <template v-else>
                            <div class="list-item">
                                <div class="list-item-content">
                                    <p class="list-item-text">{{ item.text }}</p>
                                </div>
                            </div>
                            <img style="width: 40px;margin-left: 12px;" src="@/assets/images/default-avatar.png" alt="">
                        </template>
                    </div>
                </div>
                <div class="runner-ipt-wrapper">
                    <a-textarea v-model:value="inputValue" placeholder="请输入题目关键词" :bordered="false"
                        @keydown.enter.prevent="handleSend" />
                    <span class="send-btn">
                        <img v-if="!inputValue || loading" src="@/assets/images/send.png" alt="">
                        <img style="cursor: pointer;" v-else src="@/assets/images/send1.png" alt="" @click="handleSend">
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import RunnerHead from './runner-head.vue';
import RunnerAside from './runner-aside.vue';
import { addaiinterviewassistantrecord, aiinterviewassistantcontent, delaiinterviewassistantrecord, queryaiinterviewassistantrecord } from '@/api/interview';
import * as interviewApi from '@/api/interview';
import { nextTick, onMounted, ref } from 'vue';
import { ConversationAction, ConversationAiStatus, ConversationDetail, ConversationRole, InterviewQuestionDetail } from '@/types/interviewQuestion';
import { getStarByScore, uuid as getUuid } from '@/utils/index';
import { MyConfirm } from '@/utils/antdUtil';
import _ from 'lodash';

const props = defineProps<{
    id: string
}>()


// 详情回显
const runnerDetail = ref()
async function getDetail() {
    if (props.id) {
        runnerDetail.value = await aiinterviewassistantcontent({ id: props.id })
    }
}
getDetail()

const list = ref<ConversationDetail[]>([
    {
        uuid: getUuid(),
        type: ConversationRole.AI,
        text: '请问有什么需要帮助的吗？可点击左侧【题库列表】选择知识点范围，或者输入【题目关键词】搜索相关题目',
        aiStatus: ConversationAiStatus.初始化,
        saved: false
    },
    // {
    //     type: ConversationRole.User,
    //     text: '请推荐几道关于《计算机网络》的面试题目'
    // },
    // {
    //     type: ConversationRole.AI,
    //     text: '以下是为您推荐的关于《计算机网络》的面试题目：',
    //     qlist: [
    //         {
    //             body: '在计算机科学中，堆是一种特殊的完全二叉树结构，其中每个节点的值都大于或等于（最大堆）或小于或等于（最小堆）其子节点的值。请详细解释什么是“堆”（Heap）在数据结构中的概念，并说明它在计算机科学是一个具有独立功能的程序关于某个数据集合的一次运行活动。线程是进程的执行单元，是CPU调度和分',
    //             scorebasis: '1.定义：进程是操作系统进行资源分配和调度的基本单位，它是一个具有独立功能的程序关于某个数据集合的一次运行活动。线程是进程的执行单元，是CPU调度和分派的基本单位，它是比进程更小的能独立运行的基本单位，并且线程自己不拥有系统资源，只拥有一点在运行中必不可少的资源（如程序计数器、一组寄存器和栈），但是它可以与同属一个进程的其他线程共享进程所拥有的全部资源。2.资源占用：进程间是独立的，每个进程都有自己完整的系统资源，如虚拟地址空间、文件描述符等。线程间共享进程的资源，如内存空间和文件描述符等。3.创建和销毁开销：创建进程需要分配资源，开销较大；而创建线程开销较小，因为线程共享进程的资源。同样，销毁进程也需要释放资源，开销较大，而销毁线程开销较小。4. 通信方式：进程间通信（IPC）需要特定的机制，如管道、消息队列、共享内存、信号量等。线程间可以直接通过读写共享内存进行通信，因为它们共享同一进程的资源。5. 调度和切换：线程的切换和调度通常比进程更高效，因为线程的上下文比进程小，线程间的切换不需要涉及内核态和用户态的转换。',
    //             category: '计算机网络',
    //             suggested_time: 2.5,
    //             difficulty: 80,
    //         }
    //     ]
    // }
])

// 回显聊天记录
const headRef = ref<InstanceType<typeof RunnerHead>>()
async function getRecord() {
    try {
        let res = await queryaiinterviewassistantrecord({ assistant_id: props.id })
        let record = res.record
        if (record?.length) {
            try {
                await MyConfirm({
                    title: '提示',
                    content: '该面试助手正在使用中，请问是恢复会话还是重新开始？',
                    okText: '恢复会话',
                    cancelText: '重新开始',
                })
                // 恢复
                list.value = record
                headRef.value?.adjustTime(res.create_at)

            } catch (error) {
                // 重置
                await delaiinterviewassistantrecord({ assistant_id: props.id })
            }
        }

    } catch (error) {
        console.log(error)
    }
}
onMounted(() => {
    getRecord()
})

function handleUserSay(config: {
    word: string,
    ConversationActionType: ConversationAction
}) {
    let { word, ConversationActionType } = config
    // 用户
    let textTemp = {
        [ConversationAction.选择题库]: `请推荐几道关于《${word}》的面试题目`,
        [ConversationAction.关键词搜索]: `请推荐几道有关“${word}”的题目`,
        [ConversationAction.追问题目]: `请针对“${word}”进行追问`,
        [ConversationAction.相关题目]: `请推荐几道与“${word}”相关的题目`,
        [ConversationAction.换一批]: ''
    }
    let uuid = getUuid()
    list.value.push({
        uuid,
        type: ConversationRole.User,
        text: textTemp[ConversationActionType],
        saved: false
    })
    return uuid
}

function handleAiSay(config: {
    word: string,
    ConversationActionType: ConversationAction
}) {
    let { word, ConversationActionType } = config
    let textTemp = {
        [ConversationAction.选择题库]: `以下是为您推荐的关于《${word}》的面试题目`,
        [ConversationAction.关键词搜索]: `以下是为您推荐的关于“${word}”的面试题目`,
        [ConversationAction.追问题目]: `以下是针对“${word}”追问的题目`,
        [ConversationAction.相关题目]: `以下是与“${word}”相关的题目`,
        [ConversationAction.换一批]: ''
    }
    let uuid = getUuid()
    list.value.push({
        uuid,
        type: ConversationRole.AI,
        text: textTemp[ConversationActionType],
        aiStatus: ConversationAiStatus.正在回答,
        saved: false
    })
    return uuid
}

// 题库选择
async function handleSelect(item: any) {
    let uuid1 = handleUserSay({
        word: item.name,
        ConversationActionType: ConversationAction.选择题库
    })

    let uuid2 = handleAiSay({
        word: item.name,
        ConversationActionType: ConversationAction.选择题库
    })

    fetchAndSetData({
        conversationActionType: ConversationAction.选择题库,
        apiParams: {
            id: props.id,
            select_category_tiling: [item.id],
            keys: ""
        },
        scrollToUuid: uuid1,
        targetUuid: uuid2
    })
}

// 输入框
const inputValue = ref('')
async function handleSend() {
    if (!inputValue.value) return
    let word = inputValue.value
    inputValue.value = ''
    let uuid1 = handleUserSay({
        word,
        ConversationActionType: ConversationAction.关键词搜索
    })

    let uuid2 = handleAiSay({
        word,
        ConversationActionType: ConversationAction.关键词搜索
    })

    fetchAndSetData({
        conversationActionType: ConversationAction.关键词搜索,
        apiParams: {
            id: props.id,
            select_category_tiling: [],
            keys: word
        },
        scrollToUuid: uuid1,
        targetUuid: uuid2
    })
}

// 相关题目
async function getMoreQues(question: InterviewQuestionDetail, conversationActionType: ConversationAction) {
    let word = question.body?.length > 20 ? question.body.slice(0, 20) + '...' : question.body
    let uuid1 = handleUserSay({
        word,
        ConversationActionType: conversationActionType
    })

    let uuid2 = handleAiSay({
        word,
        ConversationActionType: conversationActionType
    })

    fetchAndSetData({
        conversationActionType,
        apiParams: {
            id: props.id,
            question_id: question.id
        },
        scrollToUuid: uuid1,
        targetUuid: uuid2
    })
}


const loading = ref(false)
/** 
 * 公共请求方法和回调：查询符合搜索的题目列表，并塞入list中
 */
async function fetchAndSetData(config: {
    conversationActionType: ConversationAction
    apiParams: any
    scrollToUuid: string
    targetUuid: string
}) {
    loading.value = true
    let tempApi = {
        [ConversationAction.选择题库]: 'aiinterviewassistantrecommendques',
        [ConversationAction.关键词搜索]: 'aiinterviewassistantrecommendques',
        [ConversationAction.追问题目]: 'aiinterviewassistantappendques',
        [ConversationAction.相关题目]: 'aiinterviewassistantrelatedques',
        // [ConversationAction.换一批]: ''
    }

    try {
        let apiName = tempApi[config.conversationActionType]
        let res = await interviewApi[apiName](config.apiParams)
        let target = list.value.find(i => i.uuid === config.targetUuid)!
        target.qlist = res
        target.aiStatus = ConversationAiStatus.回答完毕

        // 记录本次查询的接口和入参
        if (target.type === ConversationRole.AI) {
            // 直接存函数在JSON.Stringify后会丢失，因此这里存函数名
            target.apiName = apiName
            target.apiParams = config.apiParams
        }

        // 保存记录
        saveRecord()

    } catch (error) {

    } finally {
        loading.value = false
        await nextTick()
        document.querySelector('#wrapper-' + config.scrollToUuid)?.scrollIntoView({
            behavior: 'smooth',
        })
    }
}

// 换一批
async function refresh(item: ConversationDetail) {
    if (item.type === ConversationRole.User) return
    loading.value = true
    item.qlist = []
    item.aiStatus = ConversationAiStatus.正在回答
    const { apiName, apiParams } = item
    try {
        let res = await interviewApi[apiName!]?.(apiParams)
        item.qlist = res
        item.aiStatus = ConversationAiStatus.回答完毕

        // 变更记录
        await addaiinterviewassistantrecord({
            assistant_id: props.id,
            record: [item],
            uuid: item.uuid
        })

    } catch (error) {

    } finally {
        loading.value = false
    }
}


// 保存记录
async function saveRecord() {
    try {
        let recordlistToAdd = _.cloneDeep(list.value.filter(i => !i.saved))
        recordlistToAdd.forEach(i => i.saved = true)
        await addaiinterviewassistantrecord({
            assistant_id: props.id,
            record: recordlistToAdd
        })

        list.value.forEach(i => i.saved = true)

    } catch (error) {
        console.log(error)
    }
}

</script>
<style lang="less" scoped>
.interview-helper-runner {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .runner-main {
        display: flex;
        flex: 1;
        min-height: 0;

        .runner-content {
            background: linear-gradient(230deg, #e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);
            flex: 1;
            display: flex;
            flex-direction: column;

            .runner-content-main {
                margin: 20px 0;
                flex: 1;
                min-height: 0;
                overflow: scroll;

                .list-item-wrapper {
                    display: flex;
                    align-items: flex-start;
                    margin: 24px auto 0;
                    width: 768px;

                    &.ai {
                        justify-content: flex-start;
                    }

                    &.user {
                        justify-content: flex-end;
                    }

                    .list-item {
                        width: auto;

                        &.full-width {
                            flex: 1;
                            min-width: 0;
                        }


                        .list-item-content {
                            background-color: #fff;
                            display: inline-block;
                            border-radius: 12px;
                            padding: 10px 16px;

                            &:is(.ai .list-item-content) {
                                border-top-left-radius: 0;
                            }

                            &:is(.user .list-item-content) {
                                border-top-right-radius: 0;
                                background: linear-gradient(45deg, #57b2fc, #835aff 47%, #bc89f8);
                                color: #fff;
                            }
                        }


                        .list-item-text {
                            display: inline-block;
                        }

                        .q-list {
                            .q-list-item {
                                padding: 16px 16px 0 16px;
                                background: #f0f4fa;
                                margin-top: 16px;
                                line-height: 20px;
                                border-radius: 8px;

                                &:first-child {
                                    margin-top: 12px;
                                }

                                .q-arrow {
                                    width: 16px !important;
                                    height: 16px !important;
                                    margin-right: 8px;
                                    transition: all .2s ease;
                                    transform: rotate(-90deg);
                                    cursor: pointer;

                                    &.expand {
                                        transform: rotate(0);
                                    }
                                }

                                .q-body {
                                    color: #000;

                                    &.ellipsis {
                                        display: -webkit-box;
                                        -webkit-box-orient: vertical;
                                        -webkit-line-clamp: 3;
                                        overflow: hidden;
                                    }
                                }

                                .q-config {
                                    display: flex;
                                    justify-content: space-between;
                                    gap: 20px;

                                    .q-config-item {
                                        flex: 1;
                                        display: flex;
                                        align-items: center;
                                        min-width: 0;

                                        &:first-child {
                                            flex: 1.5;
                                        }
                                    }

                                    .label {
                                        font-weight: bold;
                                    }

                                    .value {
                                        flex: 1;
                                        min-width: 0;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                    }
                                }

                                .q-footer {
                                    margin-top: 16px;
                                    border-top: 1px solid #c6c6d0;
                                    height: 60px;
                                    display: flex;
                                    align-items: center;

                                    .ant-btn {
                                        width: 78px;
                                        height: 28px;
                                        border-radius: 4px;
                                        padding: 0;
                                        margin-right: 12px;
                                        color: rgba(0, 0, 0, 0.65);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .runner-ipt-wrapper {
                width: 840px;
                height: 120px;
                margin: 0 auto 32px;
                border: 1px solid #d9d9d9;
                border-radius: 8px;
                padding: 16px;
                display: flex;
                flex-direction: column;

                textarea {
                    resize: none;
                    flex: 1;
                    min-height: 0;
                    padding: 0;
                }

                .send-btn {
                    align-self: flex-end;
                }}
        }
    }
}
</style>