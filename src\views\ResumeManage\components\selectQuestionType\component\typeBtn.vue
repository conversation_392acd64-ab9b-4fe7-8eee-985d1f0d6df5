<script setup lang="ts">
import ExtractIcon from '@/assets/icons/svg/extract_pkg.svg'
import ExtractQuestion from '@/assets/icons/svg/extrat_question.svg'
import ManualInput from '@/assets/icons/svg/manual_input.svg'
import clsx from 'clsx'

const props = defineProps({
  w: {
    type: String,
    default: '260',
  },
  h: {
    type: String,
    default: '120',
  },
})
const emits = defineEmits(['switchType'])
const btnClass = clsx(`transition-shadow duration-[0.5s] ease-[ease] hover:shadow-[0px_10px_20px_0px_rgba(60,60,60,0.13)] cursor-pointer relative mr-[32px] rounded-[8px] flex content-center justify-start flex-wrap pl-[20px]  w-[260px] h-[120px] `)
const activeType = defineModel()
const types = [
  {
    type: 1,
    typeIcon: ExtractIcon,
    typeName: '新增抽题范围',
    bg: 'linear-gradient(180deg,#dde6ff, #f8faff)',
    typeDesc: '选择一个或多个题库作为抽题范围',
  },  
  {
    type: 2,
    typeIcon: ExtractQuestion,
    typeName: '选择已有题目',
    bg: 'linear-gradient(0deg,#f6fcfc, #eaf8f8)',
    typeDesc: '从题库中选择一个或多个题目',
  },  
  {
    type: 3,
    typeIcon: ManualInput,
    typeName: '手动输入题目',
    bg: 'linear-gradient(0deg,#fbf9ff, #f4f1fe)',
    typeDesc: '在本试卷中录入一道临时题目',
  },  
]

function switchType(type: number) {
  emits('switchType', type)
}
</script>

<template>
  <div class="w-full flex-nowrap flex justify-center">
    <div
      v-for="item in types"
      :key="item.type"
      :class="`${btnClass} ${activeType === item.type ? 'active_bg' : ''}`"
      :style="{ background: item.bg }"
      tabindex="0"
      @click="switchType(item.type)"
      @keydown.enter="switchType(item.type)"
      @keydown.space.prevent="switchType(item.type)"
    >
      <img :src="item.typeIcon" class="w-[80px] h-[80px] absolute right-0 top-0">
      <span class="mb-[12px] font-bold text-[16px]">{{ item.typeName }}</span>
      <span class="z-[3] text-[rgba(0,0,0,0.35)] text-[12px]">{{ item.typeDesc }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dh {
      height: calc(100vh - 400px);
}
.normal_bg{
      background: linear-gradient(180deg,#f1f4ff, #f8faff);
      border: 1px solid transparent;
}
.active_bg{
      // background: linear-gradient(180deg,#dde6ff, #f8faff);
      border: 1px solid #5478ee;
      // border-radius: 8px;
      // box-shadow: 0px 10px 40px 0px rgba(0,0,0,0.10);
}

// 修复点击时的 focus 样式覆盖问题
.transition-all {
  outline: none !important; // 移除默认的 focus outline

  &:focus {
    outline: none !important;
    border: 1px solid transparent !important;
  }

  &:focus.active_bg {
    border: 1px solid #5478ee !important;
  }

  // 确保 active 状态的边框优先级最高
  &.active_bg {
    border: 1px solid #5478ee !important;
  }
}
</style>