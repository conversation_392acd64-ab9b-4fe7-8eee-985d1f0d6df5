export default {
  C_Keywords: [
    'auto',
    'int',
    'double',
    'long',
    'char',
    'float',
    'short',
    'signed',
    'unsigned',
    'struct',
    'union',
    'enum',
    'static',
    'switch',
    'case',
    'default',
    'break',
    'register',
    'const',
    'volatile',
    'typedef',
    'extern',
    'return',
    'void',
    'continue',
    'do',
    'while',
    'if',
    'else',
    'for',
    'goto',
    'sizeof'
  ],
  CPP_Keywords: [
    'auto',
    'bool',
    'char',
    'int',
    'short',
    'long',
    'float',
    'double',
    'signed',
    'unsigned',
    'false',
    'true',
    'enum',
    'union',
    'struct',
    'class',
    'wchar_t',
    'sizeof',
    'typeid',
    'typedef',
    'static',
    'public',
    'protected',
    'private',
    'virtual',
    'override',
    'final',
    'operator',
    'const',
    'constexpr',
    'using',
    'namespace',
    'inline',
    'new',
    'delete',
    'this',
    'nullptr',
    'void',
    'friend',
    'template',
    'if',
    'else',
    'for',
    'while',
    'do',
    'switch',
    'case',
    'default',
    'break',
    'continue',
    'goto',
    'and',
    'not',
    'or',
    'xor',
    'return',
    'try',
    'catch',
    'throw',
    'noexcept',
    'static_cast',
    'const_cast',
    'dynamic_cast',
    'reinterpret_cast',
    'static_assert',
    'register',
    'explicit',
    'extern'
  ],
  Java_Keywords: [
    'abstract',
    'assert',
    'boolean',
    'break',
    'byte',
    'case',
    'catch',
    'char',
    'class',
    'continue',
    'default',
    'do',
    'double',
    'else',
    'enum',
    'extends',
    'final',
    'finally',
    'float',
    'for',
    'if',
    'implements',
    'import',
    'int',
    'interface',
    'instanceof',
    'long',
    'native',
    'new',
    'package',
    'private',
    'protected',
    'public',
    'return',
    'short',
    'static',
    'strictfp',
    'super',
    'switch',
    'synchronized',
    'this',
    'throw',
    'throws',
    'transient',
    'try',
    'void',
    'volatile',
    'while',
    'true',
    'false',
    'null',
    'goto',
    'const'
  ],
  Javascript_Keywords: [
    'break',
    'do',
    'instanceof',
    'typeof',
    'case',
    'else',
    'new',
    'var',
    'catch',
    'finally',
    'return',
    'void',
    'continue',
    'for',
    'switch',
    'while',
    'default',
    'if',
    'throw',
    'delete',
    'in',
    'try',
    'function',
    'this',
    'with',
    'debugger',
    'false',
    'true',
    'null',
    'class',
    'enum',
    'extends',
    'super',
    'export',
    'import',
    'impelements',
    'let',
    'public',
    'yield',
    'interface',
    'package',
    'static',
    'const'
  ],
  Python_Keywords: [
    'False',
    'await',
    'else',
    'import',
    'pass',
    'None',
    'break',
    'except',
    'in',
    'raise',
    'True',
    'class',
    'finally',
    'is',
    'return',
    'and',
    'continue',
    'for',
    'lambda',
    'try',
    'as',
    'def',
    'from',
    'nonlocal',
    'while',
    'assert',
    'del',
    'global',
    'not',
    'with',
    'async',
    'elif',
    'if',
    'or',
    'yield'
  ],
  Go_Keywords: [
    'break',
    'case',
    'chan',
    'const',
    'continue',
    'default',
    'defer',
    'else',
    'fallthrough',
    'for',
    'func',
    'go',
    'goto',
    'if',
    'import',
    'interface',
    'map',
    'package',
    'range',
    'return',
    'select',
    'struct',
    'switch',
    'type',
    'var',
  ]
}
