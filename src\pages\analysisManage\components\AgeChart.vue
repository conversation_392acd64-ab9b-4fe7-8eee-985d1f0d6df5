<template>
    <div>
        <a-spin :spinning="state == State.loading">
            <div v-if="state === State.initial" class="common-no-data"></div>
            <div v-else ref="chartRef" style="height: 100%; overflow: hidden;"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ageproportion } from '@/api/admin/statisticAnalysis'
import { ECharts, init } from 'echarts'
import type { ZRColor } from 'echarts/types/src/util/types'
import { QUESTIONS } from '@/config/constants'

// 调色板
const colorList: ZRColor[] = QUESTIONS.map((item) => ({
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color: item.color
    }, {
        offset: 1, color: item.color + '80'
    }],
    global: false
}))

// 获取数据
const list = ref<{
    name: string
    value: number
}[]>([])

enum State {
    'initial',
    'loading',
    'complete'
}
const state = ref<State>(0)
const loading = ref(false)
async function getList() {
    state.value = State.loading
    try {
        let res = await ageproportion() as any
        if (res.reduce((a: any, b: any) => a + b.value, 0) === 0) {
            state.value = State.initial
        } else {
            state.value = State.complete
            list.value = res
        }
    } catch {
        state.value = State.initial
    }
}

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
    chart.value = init(chartRef.value)
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}人`
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '0',
            icon: 'circle'
        },
        grid: {
            left: 0,
            right: 0,
            top: 0
        },
        color: colorList,
        series: [
            {
                type: 'pie',
                radius: '80%',
                center: ['45%', '48%'],
                label: {
                    show: false
                },
                itemStyle: {
                    borderColor: 'white',
                    borderWidth: 1,
                },
                emphasis: {
                    itemStyle: {
                        borderWidth: 0,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                data: list.value
            }
        ]
    })
    window.addEventListener('resize', resizeChart)
}

function resizeChart() {
    chart.value?.resize()
}

onMounted(async () => {
    await getList()
    draw()
})

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
</style>