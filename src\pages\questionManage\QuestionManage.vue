<template>
  <div class="question-manage-container">
    <div class="flex flex-nowrap items-center">
      <h3 class="question-manage-title text-[#848789] cursor-pointer" @click="router.back()">面试题库</h3><span class="mx-[4px] text-[#848789]">/</span> <h3 class="question-manage-title">题库 《{{route.query.name}}》</h3>
    </div>
    <div class="question-manage-content">
      <div class="questions-subjects">
        <SubjectTree
          :editable="true"
          @select="handleSelectSubject"
          @visible-change="handleTreeVisibleChange"
          @delete="handleSubjectDelete"
        ></SubjectTree>
      </div>
      <div class="questions-list">
        <div v-if="isOfficialSubject && store.getters.userInfo?.tryout" class="locked-box">
          <div class="locked">
            <img src="@/assets/images/locked.png" alt="" style="width: fit-content;" />
            <span class="desc">升级正式版可解锁</span>
          </div>
        </div>

        <QuestionList v-else ref="listRef" :writable="true" :operationFlag="true" :current="route.params.page ? Number(route.params.page) : 1"
          :subject-id="subjectId" :subject-path="subjectPath" :selectItem="selectItem" :subjectUUID="subjectUUID" :is-official-subject="isOfficialSubject"></QuestionList>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import SubjectTree from './SubjectTree.vue'
import QuestionList from './QuestionList.vue'
import { useRoute,useRouter } from 'vue-router'
import { useStore } from 'vuex'

const store = useStore()
const router = useRouter()
const listRef = ref<InstanceType<typeof QuestionList>>()
const route = useRoute()
// 题库树
const subjectId = ref('')
const subjectPath = ref('')
const subjectUUID = ref('')
const selectItem = ref(null)
const isOfficialSubject = ref(store.getters.userInfo.officialCert ? true : false)
function handleSelectSubject(selecItem: any) {
  subjectId.value = selecItem.id
  subjectPath.value = selecItem.path
  selectItem.value = selecItem
}
async function handleTreeVisibleChange() {
  if (!listRef.value) return
  await nextTick()
  listRef.value.layoutTags()
}
function handleSubjectDelete(parentId: string) {
  subjectId.value = parentId
}
</script>

<style lang="less" scoped>
.question-manage-container {
  height: 100%;
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;

  .question-manage-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }

  .question-manage-content {
    flex: 1;
    min-height: 0;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    overflow: hidden;
    display: flex;

    .questions-subjects {
      top: 96px;
      bottom: 22px;
      z-index: 2;
      background-color: #fff;
      flex-shrink: 0;
      border-right: 1px solid #e8e8e8;
    }

    .questions-list {
      padding-right: 16px;
      background-color: #fff;
      flex: 1;
      min-width: 530px;
      padding-top: 16px;

      .locked-box {
        height: 100%;

        .locked {
          display: flex;
          flex-direction: column;
          height: 100%;
          justify-content: center;
          align-items: center;

          .desc {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
