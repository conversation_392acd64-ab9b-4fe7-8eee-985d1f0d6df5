import request from '@/utils/http'

// 查询新闻
export function getNews(data?: object) {
  return request({
    url: '/admin/news',
    data
  })
}

// 新增新闻
export function addNews(data?: object) {
  return request({
    url: '/admin/news',
    data
  })
}

// 编辑新闻
export function editNews(data?: object) {
  return request({
    url: '/admin/news',
    data
  })
}

// 删除新闻
export function delNews(data?: object) {
  return request({
    url: '/admin/news',
    data
  })
}
