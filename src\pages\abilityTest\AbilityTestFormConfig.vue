<template>
    <a-form class="form" ref="basicInfoFormRef" :model="formState" :hideRequiredMark="true" :rules="rules"
        :colon="false" labelAlign="left" :labelWrap="true">
        <p class="subTitle">基本信息</p>
        <a-form-item label="考试名称" name="name">
            <a-input v-model:value="formState.name" class="form-input" autocomplete="off" allow-clear></a-input>
        </a-form-item>
        <a-form-item label="考试开始时间" name="startTime">
            <a-range-picker class="my_range_picker" v-model:value="startEndTime" :disabled-date="disabledStartDate"
                :disabled-time="disabledDateTime" format="YYYY-MM-DD HH:mm" :placeholder="['选择最早开始时间', '选择最晚开始时间']"
                :allowEmpty="[false, true]" :show-time="{
        defaultValue: defaultTimeRange,
    }" @change="selectedStartRangeDate">
                <template #renderExtraFooter v-if="isCurrentTimeShow">
                    <span style="cursor: pointer;" @click="getCurrentTime">此刻</span>
                </template>
            </a-range-picker>
        </a-form-item>

        <p class="subTitle">考试前</p>
        <a-form-item label="考试须知">
            <a-radio-group v-model:value="examnoteTab" :disabled="preview">
                <a-radio :value="0">默认</a-radio>
                <a-radio :value="1">自定义</a-radio>
            </a-radio-group>
            <a-editor ref="editorRef" v-show="examnoteTab === 1" style="margin-top: 48px" :content="formState.examnotes"
                :exclude-menus="[
        'head',
        'todo',
        'quote',
        'code',
        'table',
        'emoticon',
        'video',
        'fontName',
        'strikeThrough',
        'lineHeight',
        'backColor',
        'undo',
        'redo',
        'indent',
        'link',
        'list',
        'justify',
        'fullscreen'
    ]" @getHtml="getHtml" />
        </a-form-item>
        <a-form-item label="开启手机监控">
            <a-radio-group v-model:value="formState.phonemonitor" :disabled="preview">
                <a-radio value="yes">是</a-radio>
                <a-radio value="no">否</a-radio>
            </a-radio-group>
        </a-form-item>
        <a-form-item label="开启电脑监控">
            <a-radio-group v-model:value="formState.computermonitor" :disabled="preview">
                <a-radio value="yes">是</a-radio>
                <a-radio value="no">否</a-radio>
            </a-radio-group>
        </a-form-item>

        <a-form-item v-if="!formState.examvideo" class="video">

            <template #label>
                <span>宣传视频（mp4格式）</span>
                <a-tooltip placement="right" overlayClassName="light">
                    <template #title>
                        <span>上传的视频将在考试等待页面中展示，可用于宣传报考单位和导师，仅支持mp4格式</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2"></svg-icon>
                </a-tooltip>
            </template>
            <a-upload v-show="!isshowvideo && !preview" v-model="formState.examvideo" :beforeUpload="beforeUploadmp4"
                accept=".mp4" name="avatar" list-type="picture" class="avatar-uploader" :show-upload-list="false"
                :file-list="downloadVideo" :customRequest="customRequestVideo">
                <plus-outlined></plus-outlined>
            </a-upload>
            <span v-if="preview" style="font-size: 12px">无</span>
        </a-form-item>

        <a-form-item v-else-if="formState.examvideo && downloadVideo.length" class="video">

            <template #label>
                <span>宣传视频（mp4格式）</span>
                <a-tooltip placement="right" overlayClassName="light">
                    <template #title>
                        <span>上传的视频将在考试等待页面中展示，可用于宣传报考单位和导师，仅支持mp4格式</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2"></svg-icon>
                </a-tooltip>
            </template>
            <div v-show="isshowvideo" class="video-wrapper">
                <video controls width="200" height="150" :src="downloadVideo[0].url"></video>
                <div class="mask">
                    <delete-outlined class="iconimg" style="color: #ffffffd9" @click="closevideo" />
                </div>
            </div>
        </a-form-item>

        <p class="subTitle">考试中</p>
        <a-form-item>

            <template #label>
                <span>计算器</span>
                <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
                    <template #title>
                        <span>提供计算器辅助计算，考生可在考试中自主使用</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2"></svg-icon>
                </a-tooltip>
            </template>
            <a-radio-group v-model:value="formState.calculator" :disabled="preview">
                <a-radio :value="true">有</a-radio>
                <a-radio :value="false">无</a-radio>
            </a-radio-group>
        </a-form-item>
        <p class="subTitle">反作弊</p>
        <a-form-item label="考试规范" class="anomie">
            <div class="anomieItem">
                <a-checkbox v-model:checked="formState.isneterror" :disabled="preview" class="check-box"
                    @change="handleChangeIsneterror" />
                <span>断网检测：允许学生最多因网络原因离开考试
                    <a-input-number type="number" :disabled="preview || !formState.isneterror"
                        v-model:value.number="formState.neterror" :min="1" :controls="false" class="insertInput" />
                    次
                </span>
            </div>
            <div class="anomieItem" name="cutscreen">
                <a-checkbox v-model:checked="formState.iscutscreen" :disabled="preview" class="check-box"
                    @change="handleChangeIscutscreen" />
                <span>禁止切屏：允许学生最多切屏
                    <a-input-number type="number" :disabled="preview || !formState.iscutscreen"
                        v-model:value="formState.cutscreen" :min="1" :controls="false" class="insertInput" />
                    次，超出次数后强制交卷</span>
            </div>
            <div class="anomieItem">
                <a-checkbox :disabled="preview" v-model:checked="formState.watermark" class="check-box" />
                <span>答题水印：考生作答页面，使用考生姓名和考生编号作为背景页面</span>
            </div>
            <div class="anomieItem">
                <a-checkbox :disabled="preview" v-model:checked="formState.nocopy" class="check-box" />
                <span>禁止复制：考试期间，禁止考生对试卷内容进行复制粘贴，且不得粘贴外部内容</span>
            </div>
        </a-form-item>
        <!-- <p class="subTitle">其他</p>
        <a-form-item label="" labelWidth="0">
            <a-checkbox :disabled="preview || mode === '编辑'" v-model:checked="formState.share_to_dept"
                class="check-box">
                本部门教师可编辑
                <a-tooltip placement="right" overlayClassName="light">

                    <template #title>
                        <span>勾选后，试卷将共享给本部门所有教师，本部门教师均可查看和编辑该试卷</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2"></svg-icon>
                </a-tooltip>
            </a-checkbox>
        </a-form-item> -->
    </a-form>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, reactive, ref, watch, onUnmounted, computed } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import store from '@/store'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import AEditor from '@/components/Editor/index.vue'
import { client } from '@/utils/common'
import { message } from 'ant-design-vue'
import { previewfile } from '@/api/admin/paperManage'
import { Paper } from '@/models/paperModel'
import dayjs, { Dayjs } from 'dayjs'
import { getTotalSeconds } from '@/utils'
import { checkPaperName } from '@/utils/validate'

const props = withDefaults(defineProps<{
    paperInfo?: any
    preview?: boolean
    mode?: string
}>(), {
    preview: false,
    mode: '新增'
})

const emits = defineEmits<{
    (e: 'change', value: any): void
}>()


// 考试开始时间
const startEndTime = ref<[Dayjs?, Dayjs?]>([])
const disabledStartDate = (current: Dayjs) => {
    return current && current < dayjs().endOf('day').subtract(1, 'day')
}
const range = (start: number, end: number) => {
    const result = []

    for (let i = start; i < end; i++) {
        result.push(i)
    }

    return result
}
const disabledDateTime = () => {
    return {
        disabledHours: () => range(0, 0),
        disabledMinutes: () => range(0, 0),
        disabledSeconds: () => []
    }
}
const defaultTimeRange = [dayjs('09:00', 'HH:mm'), dayjs('09:00', 'HH:mm')]
const selectedStartRangeDate = (value: [Dayjs, Dayjs]) => {
    if (value) {
        formState.value.startTime = value[0]?.second(0).millisecond(0)
        formState.value.endTime = value[1]?.second(0).millisecond(0)
    } else {
        formState.value.startTime = null as unknown as Dayjs
        formState.value.endTime = null as unknown as Dayjs
    }
    console.log(formState.value.startTime, formState.value.endTime)
}
const isCurrentTimeShow = ref(false)
function myRangePickerClickHandler() {
    let startTimeEle = document.querySelector('.my_range_picker .ant-picker-input-active #form_item_start_time')
    isCurrentTimeShow.value = !!startTimeEle
}
onMounted(() => {
    document.addEventListener('click', myRangePickerClickHandler)
})
onUnmounted(() => {
    document.removeEventListener('click', myRangePickerClickHandler)
})
// “此刻”（非统考考试开始时间才显示）
function getCurrentTime() {
    let startTimeEle = document.querySelector('.my_range_picker .ant-picker-input-active #form_item_start_time')
    if (startTimeEle) {
        if (startEndTime.value === null) {
            // 点击清空icon，会将值赋为null
            startEndTime.value = [dayjs()]
        } else {
            startEndTime.value[0] = dayjs()
        }
    }
}




let downloadVideo = reactive<any[]>([])

const isshowvideo = ref(false)

const closevideo = () => {
    isshowvideo.value = false
    formState.value.examvideo = ''
    downloadVideo.length = 0
}

const customRequestVideo = ({ file }: any) => {
    new Promise((resolve) => {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(file)
        fileReader.onload = () => {
            resolve(fileReader.result)
            const time = Date.now() + ''
            let clientObj = client(
                store.getters.ststoken.accessKeyId,
                store.getters.ststoken.accessKeySecret,
                store.getters.ststoken.stsToken
            )
            const format = file.type.split('/')[1]
            const response = clientObj.putObject({
                Bucket: 'zhanglibo-exam',
                Key: `examvideo/${time}.${format}`,
                SourceFile: file
            })
            response
                .then(() => {
                    downloadVideo[0] = {
                        name: `examvideo/${time}.${format}`,
                        url: ''
                    }
                    formState.value.examvideo = `examvideo/${time}.${format}`
                    return previewfile({ filename: `examvideo/${time}.${format}` })
                })
                .then((resdata: any) => {
                    isshowvideo.value = true
                    downloadVideo[0].url = resdata.file
                })
                .catch(() => {
                    message.error('视频上传失败,请重新上传!')
                })
        }
    })
}

const beforeUploadmp4 = (file: any) => {
    const isVideo = file.type === 'video/mp4'
    if (!isVideo) {
        message.error('请上传mp4文件')
    }

    return isVideo
}

const examnoteTab = ref(0)
const formState = ref<{
    /** 测评名称 */
    name: string
    /** 最早开始时间 */
    startTime: Dayjs | null
    /** 最晚开始时间 */
    endTime: Dayjs | null
    /** 考试须知 */
    examnotes: string
    neterror: number | null
    cutscreen: number | null
    [propName: string]: any
}>({
    name: '',
    startTime: null,
    endTime: null,
    examnotes: '',
    phonemonitor: 'yes',
    computermonitor: 'yes',
    examvideo: '',
    limitlateness: 0,
    presubmit: 0,
    quesorder: true,
    opsorder: true,
    calculator: false,
    hiddenname: true,
    share_to_dept: true,
    uploadanswer: false,
    reference: '',
    neterror: null,
    cutscreen: null,
    iscutscreen: false,
    isneterror: false,
    watermark: false,
    nocopy: false,
})

const handleChangeIsneterror = (value: boolean) => { // 断网状态修改
    if (value) {
        formState.value.neterror = null
    } else {
        formState.value.neterror = 0
    }
}
const handleChangeIscutscreen = (value: boolean) => { // 切屏状态修改
    if (value) {
        formState.value.cutscreen = null
    } else {
        formState.value.cutscreen = 0
    }
}

const getHtml = (val: any) => {
    formState.value.examnotes = val
}

watch(() => formState.value.cutscreen, (value) => {
    if (value && value > 0) {
        formState.value.iscutscreen = true
    } else {
        formState.value.iscutscreen = false
    }
})
watch(() => formState.value.neterror, (value) => {
    if (value && value > 0) {
        formState.value.isneterror = true
    } else {
        formState.value.isneterror = false
    }
})


function checkTime(rule: RuleObject, value: string) {
    if (!formState.value.startTime)
        return Promise.reject('请选择最早考试开始时间')
    if (!formState.value.endTime)
        return Promise.reject('请选择最晚考试开始时间')
    if (formState.value.endTime?.isSame(formState.value.startTime) || formState.value.endTime?.isBefore(formState.value.startTime))
        return Promise.reject('最早考试开始时间不能大于或等于最晚考试开始时间')
    if (formState.value.startTime?.isValid() && formState.value.startTime?.isBefore(dayjs()))
        return Promise.reject('请选择有效考试开始时间')

    return Promise.resolve()
}

// 定义规则
const rules = {
    name: [{ required: true, validator: checkPaperName, trigger: 'blur' }],
    startTime: [{ required: true, validator: checkTime, trigger: 'blur' }],
}


const basicInfoFormRef = ref()
const validateForm = () => {
    return new Promise((resolve, reject) => {
        return basicInfoFormRef.value.validate().then(() => {
            if (formState.value.isneterror && !formState.value.neterror) {
                message.warning('请输入断网检测次数')
                return reject('请输入断网检测次数')
            }
            if (formState.value.iscutscreen && !formState.value.cutscreen) {
                message.warning('请输入禁止切屏次数')
                return reject('请输入禁止切屏次数')
            }
            const paperInfo: any = { ...formState.value }
            if (!paperInfo.iscutscreen) paperInfo.cutscreen = 0
            if (!paperInfo.isneterror) paperInfo.neterror = 0
            if (!examnoteTab.value) paperInfo.examnotes = ''

            emits('change', {
                ...paperInfo,
                downloadVideo: JSON.parse(JSON.stringify(downloadVideo))
            })
            resolve('')
        })
    })
}

const editorRef = ref()
watch(examnoteTab, (val) => {
    if (val === 1) {
        editorRef.value.updateContent()
    }
})

watch(() => props.paperInfo, (val) => {
    // 编辑、克隆回显
    Object.keys(formState.value).forEach((key) => {
        formState.value[key] = val[key]
    })

    startEndTime.value = [formState.value.startTime!, formState.value.endTime!]
    if (formState.value.examnotes) {
        examnoteTab.value = 1
    }
    downloadVideo.push({
        url: val.examvideo_url,
        name: val.examvideo_url
    })
    if (downloadVideo[0].url) isshowvideo.value = true
})

const ststokenTimer = ref<any>(null)
onMounted(() => {
    // getIdcardInfo()
    // 获取ststoken密钥
    store.dispatch('CHANGE_STS_TOKEN', '')
    ststokenTimer.value = setInterval(() => {
        store.dispatch('CHANGE_STS_TOKEN', '')
    }, 1000 * 60 * 120)
})

onBeforeUnmount(() => {
    clearInterval(ststokenTimer.value)
})

defineExpose({
    validateForm,
    formState: formState.value
})
</script>

<style lang="less" scoped>
.ant-picker {
    width: 220px;
    height: 32px;
    border-radius: 8px;

    :deep(.ant-picker-input > input) {
        font-size: 12px;
    }

    &.ant-picker-range {
        width: 340px;
    }
}

.video-wrapper {
    width: 100px;
    height: 100px;

    video {
        height: 100%;
        width: 100%;
    }

    &:hover .mask {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        width: 102px;
        top: 0;
        left: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        pointer-events: none;
        cursor: pointer;

        .iconimg {
            display: block;
            pointer-events: visible;
        }
    }
}

:deep(.avatar-uploader) {
    border: 1px dashed #ccc;
    border-radius: 2px;
    cursor: pointer;
    display: inline-block;
    width: 100px;
    height: 100px;

    .ant-upload-select {
        height: 100%;
        display: block;

        span.ant-upload {
            display: flex;
            height: 100%;
            justify-content: center;
            align-items: center;
        }
    }
}

.form {
    overflow: auto;
    margin-bottom: auto;
    flex: 1;
    min-height: 0;
    overflow: auto;

    :deep(.form-input) {
        width: 220px;
        font-size: 12px;
        border-radius: 8px;

        .ant-input-number-input {
            font-size: 12px;
        }
    }

    :deep(.ant-form-item) {
        margin-bottom: 10px;
    }

    :deep(.ant-col) {
        width: 160px;
    }

    :deep(.ant-input) {
        font-size: 12px;
    }

    :deep(.ant-form-item-no-colon) {
        color: #626262;
        font-size: 12px;
    }

    .uploadFile {
        width: 88px;
        height: 32px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        font-size: 12px;
    }

    .exam-time {
        font-size: 12px;
    }

    .subTitle {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: rgb(0, 0, 0);
        line-height: 20px;
        margin-bottom: 19px;
    }

    :deep(.insertInput) {
        width: 40px;
        height: 24px;
        border-radius: 4px;
        margin: 0 4px;

        .ant-input-number-input-wrap {
            height: 100%;

            .ant-input-number-input {
                height: 100%;
            }
        }
    }

    .tip-icon {
        margin-left: 10px;
    }

    label {
        font-size: 12px;
    }

    .anomie span {
        font-size: 12px;
    }

    .anomie .anomieItem {
        height: 32px;
        display: flex;
        align-items: center;

        .check-box {
            margin-right: 8px;
        }

        label+span {
            display: flex;
            align-items: center;
        }
    }

    .presubmit-wrapper {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .form-input {
        font-size: 12px;
        border-radius: 8px;
        height: 32px;

        :deep(.ant-input-number-input) {
            font-size: 12px;
        }

        &.presubmit {
            width: 88px;
        }
    }
}
</style>