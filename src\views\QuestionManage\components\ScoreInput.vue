<template>
  <div class="score-input-container">
    <div @click="subScore" class="substract-btn" :class="{ disabled: score <= 1 || disabled }">
      <svg-icon name="substract" />
    </div>
    <a-input-number
      class="score-input"
      :controls="false"
      :disabled="disabled"
      v-model:value="score"
      :min="0.5"
      :max="60"
      :step="0.5"
      :formatter="formatter as any"
      @input="$emit('update:modelValue', score)"
    />
    <div @click="addScore" class="add-btn" :class="{ disabled: score >= 60 || disabled }">
      <svg-icon name="plus2" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: String,
    default: ''
  },
  type: {
    type: Number
  }
})

const score = ref(1)

const emit = defineEmits(['getScore','update:modelValue'])
const subScore = () => {
  if (score.value <= 0.5 || props.disabled) return
  score.value -= 0.5
}
const addScore = () => {
  if (score.value >= 60 || props.disabled) return
  score.value += 0.5
}
const formatter = (value: number) => {
  if (value % 1 === 0) {
    return value.toString()
  } else {
    const int =  value.toString().split('.')[0]
    const decimal = value.toString().split('.')[1]
    return `${int}${decimal ? '.5' : ''}`
  }
}
defineExpose({
  score
})

watch(score, (val) => {
  console.log('score', val)
  if (val < 0.5) {
    score.value = 0.5
  }
  emit('getScore', score.value)
})

watch(
  () => props.modelValue,
  (val) => {
    console.log(val)

    score.value = Number(val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

/* 火狐 */
input {
  -moz-appearance: textfield;
}
.score-input-container {
  overflow: hidden;
  user-select: none;
  display: flex;
  align-items: center;
  width: 102px;
  height: 32px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  .substract-btn,
  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 100%;
    cursor: pointer;
  }
  .substract-btn {
    border-right: 1px solid #d9d9d9;
  }
  .disabled {
    background: #f5f5f5;
  }
  .add-btn {
    border-left: 1px solid #d9d9d9;
  }
  .score-input {
    flex: 1;
    height: 30px;
    border: none;
    box-shadow: none;
    :deep(.ant-input-number-input) {
      font-size: 12px;
    }
    text-align: center;
  }
}
</style>
