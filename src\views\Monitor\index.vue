<template>
  <div class="monitor">
    <div class="loading-area">
      <a-spin :spinning="loadingData" />
    </div>
    <a-header />
    <div class="expand-img-wrapper">
      <a-row class="userinfo">
        <a-col :span="12">
          <div class="basic-info">
            <span class="username">{{ stuBasicInfo?.username }}</span>
            <span class="sex">{{ stuBasicInfo?.sex }}</span>
            <span class="age">{{ stuBasicInfo?.age }}岁</span>
          </div>
          <div class="bachor-info">
            <span class="school">{{ stuBasicInfo?.graduated_school }}</span>
            <span class="master">{{ stuBasicInfo?.heducation }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="other-info-item">
            <span class="label">手机号</span>
            <span class="value">{{ stuBasicInfo?.phone }}</span>
          </div>
          <div class="other-info-item">
            <span class="label">电子邮箱</span>
            <span class="value">{{ stuBasicInfo?.email }}</span>
          </div>
          <div class="other-info-item">
            <span class="label">报考单位</span>
            <span class="value">{{ stuBasicInfo?.dept_name }}</span>
          </div>
          <div class="other-info-item">
            <span class="label">身份证号</span>
            <span class="value">{{ stuBasicInfo?.idcard }}</span>
          </div>
        </a-col>
        <a-col :span="4">
          <img
            v-if="stuBasicInfo?.idcard_img"
            :src="stuBasicInfo?.idcard_img[0]"
            style="height: 125px"
          />
          <img v-else src="@/assets/images/admin/idcard_example.png" style="height: 125px" />
        </a-col>
      </a-row>
      <a-row class="reserve-info" v-if="!isReserved && stuBasicInfo?.clearMonitorTime">
        <InfoCircleOutlined style="color: #e78e35;margin-right: 8px;" />
        <span v-if="moment(stuBasicInfo.clearMonitorTime).isBefore(moment())">该考生的监控画面已清除</span>
        <template v-else>
          该考生的监控画面将在<span>{{ stuBasicInfo.clearMonitorTime }}</span>清除，如需保留，请点击<span style="color: #2cb5f2;text-decoration: underline;cursor: pointer;" @click="handleReserve(true)">保留此监控</span>
        </template>
      </a-row>
    </div>
    <div class="monitor-tabbar">
      <a-tabs class="tabbar-left" type="card" v-model:activeKey="monitorType">
        <a-tab-pane key="0">
          <template #tab>
            <span class="tabbar-title">
              <svg-icon name="computercan" width="22px" height="22px" />
              <span>前置摄像头</span>
            </span>
          </template>
        </a-tab-pane>
        <a-tab-pane key="1">
          <template #tab>
            <span class="tabbar-title">
              <svg-icon name="phonecan" width="22px" height="22px" />
              <span>手机摄像头</span>
            </span>
          </template>
        </a-tab-pane>
      </a-tabs>
      <div class="tabbar-right">
        <a-radio-group class="size-btn-group" v-model:value="current" @change="handleBtnClick">
          <a-radio-button value="1">小</a-radio-button>
          <a-radio-button value="2">中</a-radio-button>
          <a-radio-button value="3">大</a-radio-button>
        </a-radio-group>

        <a-radio-group class="pic-model-group" v-model:value="showModel">
          <a-radio-button value="1" title="展开图">
            <svg-icon name="thumbnail" class="icon-btn" />
          </a-radio-button>
          <a-radio-button value="0" title="缩略图">
            <svg-icon name="unfold" class="icon-btn" />
          </a-radio-button>
        </a-radio-group>
      </div>
    </div>
    <div class="body">
      <a-timeline v-if="showModel === '1'" mode="left">
        <a-timeline-item v-for="(item, index) of getTimeline" :key="item">
          <span style="line-height: 30px"
            >{{ current === '1' ? item * 10 : current === '2' ? item * 5 : item }}分钟</span
          >
          <div style="padding: 0 10px">
            <a-image
              :width="imgSize"
              v-for="img in monitorPhotoList.slice(
                current === '1' ? index * 200 : current === '2' ? index * 100 : index * 20,
                current === '1' ? item * 200 : current === '2' ? item * 100 : item * 20
              )"
              :key="img"
              :src="img"
              :preview="false"
            />
          </div>
        </a-timeline-item>
      </a-timeline>
      <div v-else style="padding: 0 10px">
        <a-image
          :width="imgSize"
          v-for="(img, index) in monitorPhotoListByMinute"
          :key="img"
          :src="img"
          :preview="false"
          style="cursor: pointer"
          @click="showPicContainter(index)"
        />
      </div>
      <div v-if="hasNoData" class="no-data">
        <img class="nodata" src="@/assets/images/nodata.png" />
      </div>
      <a-row class="reserve-info" v-if="isReserved">
        <CheckCircleFilled style="color: #52c41a;margin-right: 8px;" />
        该考生的监控画面已保留，如不再需要，请点击<span style="color: #2cb5f2;text-decoration: underline;cursor: pointer;" @click="handleReserve(false)">取消保留</span>
      </a-row>
    </div>
  </div>
  <div v-if="picsContainerVisible" class="show-pic-container">
    <Icon class="close-icon" icon="CloseCircleOutlined" @click="close" />
    <div class="pics">
      <a-image
        width="33%"
        v-for="img in monitorAllPhotoListByMinute"
        :key="img"
        :src="img"
        :preview="false"
        style="cursor: pointer"
      />
    </div>
  </div>

  <a-back-top visibilityHeight="100" />
</template>

<script setup lang="ts">
import AHeader from '@/layouts/Admin/components/AHeader.vue'
import { monitorPhotos, candsinfo, reserveMonitor } from '@/api/admin/paperManage'
import { auxMonitor } from '@/api/exam'
import { ref, watch, computed, createVNode } from 'vue'
import { InfoCircleOutlined, CheckCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import moment from 'moment'

const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const imgSize = ref('10%')
const current = ref('2')
const showModel = ref('1')
const monitorType = ref('0')
const stu_id = ref('')
const stupid = ref('')
const paper_id = ref('')
const picIndex = ref(0)
const requestParams = ref({})
const monitorPhotoList = ref<any[]>([])
const picsContainerVisible = ref(false)
const getTimeline = computed(() => {
  switch (current.value) {
    case '1':
      return Math.ceil(monitorPhotoList.value.length / 20 / 10)
    case '2':
      return Math.ceil(monitorPhotoList.value.length / 20 / 5)
    case '3':
      return Math.ceil(monitorPhotoList.value.length / 20)
    default:
      return 0
  }
})
const monitorPhotoListByMinute = computed(() => {
  const arr = []
  if (current.value[0] === '1') {
    const minutes = Math.ceil(monitorPhotoList.value.length / 20 / 10)
    for (let i = 0; i < minutes; i++) {
      arr.push(monitorPhotoList.value[0 + 200 * i])
    }
  }
  if (current.value[0] === '2') {
    const minutes = Math.ceil(monitorPhotoList.value.length / 20 / 5)
    for (let i = 0; i < minutes; i++) {
      arr.push(monitorPhotoList.value[0 + 100 * i])
    }
  }
  if (current.value[0] === '3') {
    const minutes = Math.ceil(monitorPhotoList.value.length / 20)
    for (let i = 0; i < minutes; i++) {
      arr.push(monitorPhotoList.value[0 + 20 * i])
    }
  }
  return arr
})
const monitorAllPhotoListByMinute = computed(() => {
  // const arr = []
  if (current.value === '1') {
    // const minutes = Math.ceil(monitorPhotoList.value.length / 6 / 10)
    return monitorPhotoList.value.slice(picIndex.value * 200, (picIndex.value + 1) * 200)
  }
  if (current.value === '2') {
    return monitorPhotoList.value.slice(picIndex.value * 100, (picIndex.value + 1) * 100)
  }
  if (current.value === '3') {
    return monitorPhotoList.value.slice(picIndex.value * 20, (picIndex.value + 1) * 20)
  }

  return []
})

const handleBtnClick = (e: any) => {
  current.value = e.target.value
  switch (current.value) {
    case '1':
      imgSize.value = '5%'
      break
    case '2':
      imgSize.value = '10%'
      break
    case '3':
      imgSize.value = '20%'
      break
  }
}
const showPicContainter = (index: number) => {
  picsContainerVisible.value = true
  picIndex.value = index
}
const close = () => {
  picsContainerVisible.value = false
}

const loadingData = ref(false)
const getMonitorPhotos = (params: any) => {
  loadingData.value = true
  monitorPhotos(params)
    .then((res: any) => {
      monitorPhotoList.value.push(...res)
    })
    .finally(() => {
      loadingData.value = false
    })
}

const getAuxMonitorPhotos = (params: any) => {
  loadingData.value = true
  auxMonitor(params)
    .then((res: any) => {
      console.log(res)
      monitorPhotoList.value.push(...res)
    })
    .finally(() => {
      loadingData.value = false
    })
}

const hasNoData = computed(() => {
  return monitorPhotoList.value.length === 0
})

const stuBasicInfo = ref<any>()
async function getStuInfo(stupid: string) {
  try {
    let data = await candsinfo({ stupid }) as any
    stuBasicInfo.value = data.cand_status[0]
    isReserved.value = data.cand_status[0].reserve
  } catch (error) {
    console.log(error)
  }
}

watch(
  () => props.id,
  (val) => {
    stu_id.value = val.split('&')[0]
    paper_id.value = val.split('&')[1]
    stupid.value = val.split('&')[2]
    const params = {
      action: 'query',
      student: stu_id.value,
      paper: paper_id.value,
      is_more: true
    }
    getStuInfo(stupid.value)
    Object.assign(requestParams.value, params)
    getMonitorPhotos(params)
    // getAuxMonitorPhotos(params)
  },
  { immediate: true }
)

watch(monitorType, getList)
function getList(val: string) {
  monitorPhotoList.value.length = 0
  if (val === '0') {
    getMonitorPhotos(requestParams.value)
  } else {
    getAuxMonitorPhotos(requestParams.value)
  }
}

// 保留监控
const isReserved = ref(false)
async function handleReserve(reserve: boolean) {
  try {
    if (!reserve && moment(stuBasicInfo.value.clearMonitorTime).isBefore(moment())) {
      Modal.confirm({
        title: '提醒',
        icon: () => createVNode(InfoCircleFilled),
        content: '取消保留后将清空所有监控图片，是否确认？',
        onOk: () => {
          reserveSubmit(reserve)
          monitorPhotoList.value = []
        }
      })
    } else {
      reserveSubmit(reserve)
    }
  } catch (error) {
    
  }
}

async function reserveSubmit(reserve: boolean) {
  await reserveMonitor({
    reserveMonitor: reserve ? 1 : 0,
    id: stupid,
    // paper: paper_id.value,
    // student: stu_id.value,
  })
  message.success('操作成功')
  isReserved.value = reserve
}

</script>

<style lang="less" scoped>
.monitor {
  padding-top: 48px;
  min-height: 100vh;
  position: relative;
  // background-color: #f0f2f5;
  .loading-area {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
  }
  .stu-info {
    display: flex;
    align-items: center;
    border-bottom: 2px solid #f0f2f5;
    margin: 30px 100px 0;
    .item {
      margin-bottom: 8px;
      text-wrap: nowrap;
    }
  }
  .monitor-tabbar {
    position: relative;
    z-index: 2;
    padding: 30px 50px 30px 80px;
    background-color: #fff;
    .tabbar-title {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #121633;
    }
    .tabbar-right {
      position: absolute;
      top: 30px;
      right: 50px;
      display: flex;
      .size-btn-group {
        .ant-radio-button-wrapper {
          border: none;
          box-shadow: none;
          &:not(:first-child)::before {
            width: 0;
          }
        }
      }
      .pic-model-group {
        .ant-radio-button-wrapper {
          width: 38px;
          height: 34px;
        }
        .icon-btn {
          position: relative;
          right: 4px;
          top: 4px;
        }
      }
    }
  }
  .body {
    position: relative;
    height: calc(100vh - 205px);
    background-color: #fff;
    flex: 1;
    padding: 0 40px 30px 80px;
    .no-data {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.ant-back-top {
  right: 18px;
  bottom: 18px;
}
.reserve-info {
  display: flex;
  align-items: center;
  font-size: 16px;
}
.expand-img-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 24px;
  padding-left: 80px;
  .userinfo {
    margin-bottom: 30px;
    .basic-info {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      .username {
        font-size: 24px;
        font-weight: bold;
        margin-right: 16px;
      }
      .sex {
        width: 20px;
        height: 18px;
        background: #faf4ee;
        border-radius: 4px;
        text-align: center;
        color: #d96e00;
        margin-right: 8px;
      }
      .age {
        width: 35px;
        height: 18px;
        background: #faf4ee;
        border-radius: 4px;
        text-align: center;
      }
    }
    .bachor-info {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 8px;
      .school {
        margin-right: 24px;
      }
    }
    .send-btn {
      border-radius: 8px;
      font-size: 14px;
    }
  }
  .other-info-item {
    display: flex;
    margin-bottom: 16px;
    font-size: 12px;
    .label {
      width: 80px;

      color: #626262;
    }
    .value {
      color: #181818;
    }
  }
}
</style>
<style lang="less">
div.ant-image {
  transition: width 0.5s !important;
}
.show-pic-container {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.8);
  .close-icon {
    position: absolute;
    right: 20px;
    top: 30px;
    font-size: 30px;
    color: #ccc;
    cursor: pointer;
  }

  .pics {
    width: calc(100% - 60px);
    height: 400px;
    overflow-y: auto;
    margin: 0 auto;
    background-color: #fff;
    &::-webkit-scrollbar {
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgba(144, 147, 153, 0.5);
    }
    &::-webkit-scrollbar-track {
      // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 5px;
      background: transparent;
    }
  }
}
</style>
