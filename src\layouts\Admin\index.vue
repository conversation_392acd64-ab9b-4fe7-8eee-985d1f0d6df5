<template>
  <a-layout class="portal-admin">
    <a-sider />
    <a-main />
  </a-layout>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import ASider from './components/Aside.vue'
import AHeader from './components/AHeader.vue'
import AMain from './components/AMain.vue'
export default defineComponent({
  components: {
    ASider,
    AHeader,
    AMain
  },

  setup() {}
})
</script>
<style lang="less" scoped>
.portal-admin {
  height: 100vh;
  font-size: 16px;
  overflow-x: hidden;
}
</style>
