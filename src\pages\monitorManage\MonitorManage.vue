<template>
    <div class="resource-group-manage">
        <div class="title-wrap">
            <span class="title">监考中心</span>
            <div class="btns">
                <a-button type="primary" @click="mergeMonitor">合并监考</a-button>
            </div>
        </div>
        <div class="filter-wrapper">
            <div style="flex: 1;">
                <div class="filter-wrapper-row">
                    <Switch :columns="stateColums" v-model="params.status"></Switch>
                    <a-input-search v-model:value.trim="searchValue" placeholder="请输入考试名称" allow-clear @blur="handleSearch" @search="handleSearch" />
                </div>
                <div class="filter-wrapper-row more-filter" v-if="filterMoreVisible">
                    <div class="filter-item">
                        <span class="filter-label">试卷类型</span>
                        <a-select v-model:value="params.uniexam" :options="PaperTypeList" placeholder="全部" allowClear />
                    </div>
                    <!-- <div class="filter-item">
                        <span class="filter-label">创建人</span>
                        <a-select placeholder="全部" allowClear v-model:value="params.create_by">
                            <a-select-option :value="1">本人</a-select-option>
                            <a-select-option :value="0">其他</a-select-option>
                        </a-select>
                    </div> -->
                    <div class="filter-item">
                        <span class="filter-label">考试时间</span>
                        <a-range-picker class="time-range-wrap" v-model:value="params.start_time_range"
                            :placeholder="['最早开始时间', '最晚开始时间']" valueFormat="YYYY-MM-DD">
                            <template #suffixIcon>
                                <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                            </template>
                        </a-range-picker>
                    </div>
                </div>
            </div>
            <div class="filter-btns">
                <div class="filter-btn filter-more" @click="filterMoreVisible = !filterMoreVisible">
                    <span v-if="filterMoreActiveNumber" class="filter-number">{{ filterMoreActiveNumber }}</span>
                    <img v-else src="@/assets/icons/svg/filter2.svg" alt="">
                    <span style="margin-left: 4px;">更多条件</span>
                </div>
                <div class="filter-btn" style="margin-top: 16px;" @click="handleReset" v-if="filterMoreVisible">
                    <img width="16" height="16" style="margin-right: 4px;" src="@/assets/images/paper/clear.png" alt="">
                    <div class="label">清空条件</div>
                </div>
            </div>
        </div>
        <ListWrapper ref="listWrapperRef" :card-min-width="386" :params="params" :getListFn="getListFn">
            <template #item="{ item, index }">
                <div class="list-item" :class="{ active: selectedIds.includes(item.id) }">
                    <div class="item-main">
                        <div class="title">
                            <a-tooltip>
                                <template #title>{{ item.name }}</template>
                                <SearchHighLight class="item-name" :text="item.name" :search-text="params.name"></SearchHighLight>
                            </a-tooltip>
                            <span class="status" :style="PaperStatusList.find(el => el.value === item.status)">{{
                                PaperStatusList.find(el => el.value === item.status)?.label || item.status }}</span>
                            <span class="circle-box" :class="{ active: selectedIds.includes(item.id) }"
                                @click="handleCardSelect(item)">
                                <check-circle-filled v-if="selectedIds.includes(item.id)"
                                    style="color: #5478ee;font-size: 16px;" />
                                <div v-else class="circle"></div>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="label"><img src="@/assets/icons/svg/calendar2.svg" alt=""></span>
                            <span class="value">
                                {{ item.startTime?.slice(0, 16) + ' 至 ' + item.endTime?.slice(0, 16) }}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="label"><img src="@/assets/icons/svg/clock2.svg" alt=""></span>
                            <span class="value">{{ item.duration }}分钟</span>
                        </div>
                        <div class="info-item">
                            <span class="label"><img src="@/assets/icons/svg/person.svg" alt=""></span>
                            <span class="value">{{ item.candidatesCount ?? 0 }}人</span>
                        </div>
                        <span class="start-btn" @click="startMonitor(item.id)">开始监考 <right-outlined style="font-size: 12px;"/></span>
                    </div>
                    <div class="uniexam-tag" v-if="item.uniexam == '0'">非统考</div>
                </div>
            </template>
        </ListWrapper>
    </div>
</template>

<script lang="ts" setup>
import ListWrapper from '@/components/ListWrapper.vue';
import { queryTestPaper } from '@/api/admin/paperManage'
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleFilled, RightOutlined } from '@ant-design/icons-vue'
import { useState } from '@/hooks';
import { useRouter } from 'vue-router'; 
import { PaperStatus, PaperStatusList, PaperType, PaperTypeList } from '@/models/paperModel'
import Switch from '@/components/Switch.vue';
import SearchHighLight from '@/components/SearchHighLight.vue';
import _ from 'lodash'

const router = useRouter()

// 更多条件
const filterMoreVisible = ref(false)
const filterMoreActiveNumber = computed(() => {
    let filterMoreKeys = ['uniexam', 'create_by', 'start_time_range']
    return filterMoreKeys.reduce((pre, cur) => {
        if (params.value[cur] !== rawParams[cur]) pre++
        return pre
    }, 0)
})

// 模糊查询
const searchValue = ref('')
function handleSearch() {
    params.value.name = searchValue.value
}

// 考试状态
const stateColums = [
    { label: '全部', value: null },
    { label: '考试中', value: PaperStatus.DURING_EXAMS },
    { label: '未开始', value: PaperStatus.NOT_START }
]

// 接口查询
const [params, resetParams, rawParams] = useState<{
    action: 'query'
    name: string
    status: null | number
    uniexam?: string | null,
    create_by?: null | number,
    start_time_range: null | string[]
}>({
    action: 'query',
    name: '',
    status: null,
    uniexam: undefined,
    create_by: undefined,
    start_time_range: null
})
const getListFn = (params: any) => {
    return queryTestPaper({
        ...params,
        basePager:{
            current: params.page,
            size: params.per_page
        },
        statusList: params.status === null ? [PaperStatus.DURING_EXAMS, PaperStatus.NOT_START] : [params.status],
        uniexam: params.uniexam === undefined ? null : params.uniexam,
        create_by: params.create_by === undefined ? null : [params.create_by],
        startTime: params.start_time_range === null ? null : params.start_time_range[0],
        endTime: params.start_time_range === null ? null : params.start_time_range[1],
    })
}
const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()

// 重置
function handleReset() {
    searchValue.value = ''
    resetParams()
}

// 卡片选中
const selectedIds = ref<string[]>([])
function handleCardSelect(item: any) {
    if (selectedIds.value.includes(item.id)) {
        selectedIds.value = selectedIds.value.filter(el => el !== item.id)
    } else {
        selectedIds.value.push(item.id)
    }
}

// 开始监考
function startMonitor(paperIds: string) {
    router.push({
        path: '/admin/examManage/monitor',
        query: { paperIds, isLiveMonitor: '0' }
    })
}

// 合并监考
function mergeMonitor() {
    if (selectedIds.value.length < 2) {
        message.warning('请至少选择两场考试')
        return
    }
    startMonitor(selectedIds.value.join(','))
}

</script>

<style lang="less" scoped>
.resource-group-manage {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title-wrap {
        height: 64px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }

        .btns {
            height: 32px;

            :deep(.ant-btn) {
                margin-left: 8px;
            }
        }
    }
}

.filter-wrapper {
    padding: 24px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
    background: #ffffff;
    border-radius: 8px;
    margin: 0px 20px 20px;
    display: flex;

    :deep(.filter-wrapper-row) {
        display: flex;
        justify-content: space-between;
        &.more-filter {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-row-gap: 16px;
        }

        +.filter-wrapper-row {
            margin-top: 16px;
        }

        .ant-select,
        .ant-picker,
        .ant-input-search {
            width: 240px;
        }
    }

    .filter-item {
        display: flex;
        align-items: center;
        .filter-label {
            margin-right: 16px;
            color: #626262;
        }
        &:nth-child(3n+2) {
            justify-content: center;
        }
        &:nth-child(3n+3) {
            justify-content: flex-end;
        }
    }

    .filter-btns {
        margin-left: 8px;
    }

    .filter-btn {
        width: 108px;
        height: 32px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.filter-more {
            .filter-number {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #FF4D4F;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
            }
        }

    }


}

.list-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid transparent;
    position: relative;
    user-select: none;

    &.active {
        border: 1px solid #5478EE;
    }

    .circle-box {
        visibility: hidden;
        cursor: pointer;

        &.active {
            visibility: visible !important;
        }
    }

    &:hover {
        .circle-box {
            visibility: visible;
        }
    }

    .item-main {
        flex: 1;
        min-width: 0;
        position: relative;

        .title {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .status {
                display: inline-block;
                width: 44px;
                height: 18px;
                margin-left: 16px;
                background: #fcf0f0;
                color: #d71310;
                border-radius: 4px;
                font-size: 12px;
                text-align: center;
                line-height: 18px;
                margin-right: auto;
                flex-shrink: 0;
            }

            .circle {
                width: 16px;
                height: 16px;
                border: 1px solid #d9d9d9;
                background: #fff;
                border-radius: 50%;

                &:hover {
                    border-color: #5478EE;
                }
            }
        }

        .info-item {
            display: flex;
            height: 30px;
            align-items: center;
            font-size: 12px;

            .label {
                color: #919191;
                flex-shrink: 0;
                margin-right: 16px;
                display: flex;
                align-items: center;
                img {
                    width: 14px;
                }
            }

            .value {
                flex: 1;
                min-width: 0;
            }
        }

        .start-btn {
            position: absolute;
            right: 0;
            bottom: 0;
            color: #5478EE;
            cursor: pointer;
            &:hover {
                color: #8CA7FF;
            }
            &:active {
                color: #3655BB;
            }
        }
    }

    .uniexam-tag {
        position: absolute;
        right: -1px;
        top: -1px;
        width: 60px;
        height: 20px;
        background: linear-gradient(90deg, #e8eeff, #cdd9ff);
        border-radius: 0px 8px 0px 8px;
        color: #5478EE;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }

    .dot {
        font-weight: bold;
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 18px;
        transform: rotate(90deg);
        cursor: pointer;
    }

    .item-name {
        font-weight: 600;
        font-size: 18px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>