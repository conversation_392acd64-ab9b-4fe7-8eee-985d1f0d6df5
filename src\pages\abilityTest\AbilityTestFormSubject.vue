<template>
    <div class="main">
        <Tree ref="treeRef" @updateChecked="onUpdateChecked"></Tree>
        <div class="content">
            <div class="content-title">已选题库范围</div>
            <div class="content-wrapper">
                <ul v-if="list.length">
                    <li class="card-item" v-for="(item, index) in list">
                        <div class="head">
                            <template  v-if="item.isLeaf">
                                <svg-icon name="file-official" v-if="item.is_official_cert"></svg-icon>
                                <svg-icon name="file" v-else></svg-icon>
                            </template>
                            <template v-else>
                                <svg-icon name="folder-official" v-if="item.is_official_cert"></svg-icon>
                                <svg-icon name="folder" v-else></svg-icon>
                            </template>
                            <span class="card-title">
                                <span class="card-title-cha" :title="item.name">
                                {{ item.name }}
                                </span>
                                <svg-icon name="auth" v-if="item.is_official_cert"></svg-icon>
                            </span>
                            <CloseOutlined @click="handleRemove(index)" />
                        </div>
                        <div class="desc">
                            <a-tooltip :title="item.pathName" placement="bottomLeft">
                                {{ item.pathName }}
                            </a-tooltip>
                        </div>
                        <div class="num">共{{ item.num }}道题</div>
                    </li>
                </ul>
                <div v-else class="common-no-data"></div>
            </div>
            <div class="total">
                <svg-icon name="exam-m" style="margin-right: 6px;font-size: 20px;"></svg-icon>
                合计：共<span style="color: #DE504E;">{{ total }}</span>道题
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import Tree from './AbilityTestFormSubjectTree.vue'
import { CloseOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  paperInfo?: any
}>()

type ListType = {
    id: string
    name: string
    type_ques_count: Record<number, number>
    pathName: string
    children: ListType[]
    num: number
    isLeaf: boolean
    is_official_cert: boolean
}

const list = ref<ListType[]>([])
const total = computed(() => list.value.reduce((p, c) => p + c.num, 0))

watch(() => props.paperInfo, (val) => {
    treeRef.value?.setCheckedKeys(val.select_category)
})

function onUpdateChecked(arr: ListType[]) {
    arr.forEach(item => {
        item.num = Object.values(item.type_ques_count).reduce((p, c) => p + c, 0)
    })
    list.value = arr
}

const treeRef = ref<InstanceType<typeof Tree>>()
function handleRemove(index: number) {
    list.value.splice(index, 1)
    treeRef.value?.setCheckedKeys(list.value.map(i => i.id))
}

function validate() {
    return !!list.value?.length
}

defineExpose({
    validate,
    list
})

</script>

<style lang="less" scoped>
.main {
    display: flex;
    height: 100%;

    .content {
        margin-left: 20px;
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;

        .content-title {
            height: 48px;
            line-height: 48px;
            font-weight: bold;
            font-size: 14px;
        }

        .content-wrapper {
            flex: 1;
            min-height: 0;
            overflow: auto;

            >ul {
                padding-right: 14px;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;

                .card-item {
                    border: 1px solid #dddddd;
                    padding: 20px;
                    border-radius: 8px;
                    height: max-content;
                    transition: all ease .2s;

                    &:hover {
                        border: 1px solid transparent;
                        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.20);

                        .head .anticon-close {
                            opacity: 1;
                        }
                    }

                    .head {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .svg-icon {
                            font-size: 20px;
                        }

                        .card-title {
                            font-size: 16px;
                            font-weight: bold;
                            margin-right: auto;
                            margin-left: 8px;
                            display: flex;
                            align-items: center;
                            flex: 1;
                            min-width: 0;
                            overflow: hidden;
                            .card-title-cha {
                                margin-right: 8px;
                                white-space: nowrap;
                                flex: 1;
                                min-width: 0;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }

                        .anticon-close {
                            opacity: 0;
                            transition: all ease .2s;
                            cursor: pointer;
                        }
                    }

                    .desc {
                        font-size: 12px;
                        color: #626262;
                        line-height: 18px;
                        margin-top: 8px;
                        text-wrap: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }

                    .num {
                        font-size: 12px;
                        line-height: 18px;
                        color: rgba(0, 0, 0, 0.85);
                        margin-top: 4px;
                    }
                }
            }

        }

        .total {
            font-size: 14px;
            margin-top: 16px;
            height: 40px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            background: #f1f4fe;
            border: 1px solid #5377ee;
            border-radius: 8px;
            font-weight: bold;
        }
    }
}
</style>