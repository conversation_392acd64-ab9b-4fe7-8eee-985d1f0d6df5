<script lang="tsx">
import type { PropType } from 'vue'
import { computed, defineComponent, ref, Transition } from 'vue'
import { cn } from '@/lib/utils'
import { logicAnd } from '@vueuse/math'
import MaleAvatar from '@/assets/images/resume/男avatar.png'
import FemaleAvatar from '@/assets/images/resume/女avatar.png'
import UnknownAvatar from '@/assets/images/resume/未知avatar.png'
interface CandidateProps {
  resumeId: string
  candidateName: string
  avatar?: string
  resumeStatus?: string
  gender?: string
}
export const ItemComponent = defineComponent({
  name: 'CandidateItem',
  props: {
    candidate: {
      type: Object as PropType<CandidateProps>,
      required: true,
    },
    currentResumeId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    type StatusType = '合适' | '待定' | '未筛选' | '不合适'
    const getStatusStyle = (status: string) => {
      const styles: Record<StatusType, string> = {
        '合适': 'text-[#67c23a] bg-[#f0f9eb]',
        '待定': 'text-[#e6a23c] bg-[#fdf6ec]',
        '未筛选': 'text-[#909399] bg-[#f4f4f5]',
        '不合适': 'text-[#f56c6c] bg-[#fef0f0]',
      }
      return styles[status as StatusType] || ''
    }

    return () => (
      <div 
        class={cn(
          'h-12 flex items-center px-2 mb-1 rounded transition-colors duration-200 hover:bg-[#F1F4FE] cursor-pointer',
          { 'bg-[#F1F4FE]': `${props.currentResumeId}` === `${props.candidate.resumeId}` },
        )}
      >
        <div class="relative mr-2">
          {/* 判断头像的缺省图，根据性别显示 */}
          {(() => {
            // 获取头像显示逻辑
            const getAvatarSrc = () => {
              if (props.candidate?.avatar) {
                return props.candidate.avatar
              }
              
              if (props.candidate?.avatar === null) {
                switch (props.candidate?.gender) {
                  case '男':
                    return MaleAvatar
                  case '女': 
                    return FemaleAvatar
                  default:
                    return UnknownAvatar
                }
              }

              return UnknownAvatar
            }

            return (
              <img 
                src={getAvatarSrc()} 
                alt="头像" 
                class="w-6 h-6 rounded-full object-cover" 
              />
            )
          })()}
          
          {/* <img src={props.candidate?.avatar || ''} alt="头像" class="w-6 h-6 rounded-full object-cover" /> */}
          <span class={[
            'absolute -right-0.5 -bottom-0.5 w-3 h-3 rounded-full flex items-center justify-center ',
            props.candidate.gender === '男' 
              ?  'bg-[#e6f7ff]' 
              : props.candidate.gender === '女' ? 'bg-[#fff0f6]' 
              : 'bg-transparent',
          ]}
          >
            {props.candidate.gender === '男'
              ? (
                  <div class="flex justify-center items-center w-full h-full bg-[#5478EE] text-white rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 512 512">
                      <circle cx="216" cy="296" r="152" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" />
                      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M448 160V64h-96m-28 124L448 64" />
                    </svg>
                  </div>
                )
              : 
              props.candidate.gender === '女' ?
              (
                  <div class="flex justify-center items-center w-full h-full bg-[#FAAD14] text-white rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 512 512">
                      <circle cx="256" cy="184" r="152" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" />
                      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 336v144m58-64H198" />
                    </svg>
                  </div>
                )
                : (<></>)
              }
          </span>
        </div>
        <div class="flex-1 overflow-hidden">
          <span class="text-sm text-[#333] whitespace-nowrap overflow-hidden text-ellipsis">{props.candidate.candidateName}</span>
        </div>
        <Transition
          name="slide-up-resume-status"
          mode="out-in"
        >
          <div 
            key={props.candidate.resumeStatus}
            class={[
              'px-1.5 py-0.5 rounded text-xs whitespace-nowrap',
              getStatusStyle(props.candidate.resumeStatus || '')
            ]}
          >
            {props.candidate.resumeStatus}
          </div>
        </Transition>
      </div>
    )
  },
})
</script>

<script setup lang="tsx">
import { currentResumeId, resumeListDataInFilter } from './shared'
import gsap from 'gsap'
import { MotionPathPlugin } from 'gsap/all'
// import ParabolicSVG from './ParabolicSvg.vue'

// 模拟候选人数据
withDefaults(
  defineProps<{
    jobTitle?: string
  }>(),
  {
    jobTitle: '高级 JAVA 工程师',
  },
)

// 筛选上方显示的候选人（合适、待定、未筛选）
const topCandidates = ref(
  resumeListDataInFilter.value.filter(candidate => 
    ['合适', '待定', '未筛选'].includes(candidate.resumeStatus),
  ).sort((a, b) => a.score - b.score),
)
// 筛选下方显示的候选人（不合适）
const bottomCandidates = ref(
  resumeListDataInFilter.value.filter(candidate => 
    candidate.resumeStatus === '不合适',
  ).sort((a, b) => a.score - b.score),
)

const firstLoad = ref(true)
watch(() => resumeListDataInFilter.value, (newVal) => {
  topCandidates.value = newVal.filter(candidate =>
    ['合适', '待定', '未筛选'].includes(candidate.resumeStatus),
  ).sort((a, b) => a.score - b.score)

  bottomCandidates.value = newVal.filter(candidate =>
    candidate.resumeStatus === '不合适',
  // ).sort((a, b) => a.score - b.score)
  ).sort((a, b) => new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime())

  /**
   * 如果当前的简历还有「未筛选」的，那么自动跳转，否则还是使用当前的简历
   */
  const index = topCandidates.value.findIndex(candidate => candidate.resumeStatus === '未筛选')
  if (index !== -1) {
    handleSelectCurrentResume(topCandidates.value[index])
  }

  if (firstLoad.value) {
    handleSelectCurrentResume( 
      topCandidates.value.length !== 0 
      ? topCandidates.value[0] 
      : bottomCandidates.value[0]
    )
  }

  firstLoad.value = false
}, {
  deep: true,
  immediate: true,
})

// 控制下方模块是否展开
const isBottomExpanded = ref(false)

// 切换下方模块展开状态
function toggleBottomExpand() {
  isBottomExpanded.value = !isBottomExpanded.value
}

// 计算上方模块高度
const topSectionHeight = computed(() => {
  if (!bottomCandidates.value.length) 
    return '100%'
  
  if (isBottomExpanded.value) {
    return '33%' // 展开时上方占 1/3
  }
  else {
    // 未展开时，下方最多显示2个候选人
    const bottomHeight = Math.min(bottomCandidates.value.length, 2) * 48
    return `calc(100vh - ${bottomHeight}px - 40px)` // 减去下方高度和上下内边距
  }
})

// 计算下方模块高度
const bottomSectionHeight = computed(() => {
  if (!bottomCandidates.value.length) 
    return '0'
  
  if (isBottomExpanded.value) {
    return '67%' // 展开时下方占 2/3
  }
  else {
    // 未展开时，下方最多显示2个候选人
    return `${Math.min(bottomCandidates.value.length, 2) * 48 + 40}px` // 候选人高度 + 上下内边距
  }
})

// 判断是否需要显示展开按钮
const showExpandButton = computed(() => {
  return bottomCandidates.value.length > 0
})

/**
 * 设置当前简历
 */
function handleSelectCurrentResume(
  item: typeof bottomCandidates.value[0],
  _e?: MouseEvent,
) {
  if (item?.resumeId) {
    currentResumeId.value = item.resumeId
  }
  // if (e) {
  //   handleClickToAnimate(e, item.resumeId)
  // }
}

onMounted(() => {
  gsap.registerPlugin(MotionPathPlugin)
})
// const parabolicCoords = ref([20, 0])
// const targetIdRef = ref<string | null>(null)
// function handleClickToAnimate(
//     e: MouseEvent ,
//     targetId: string,
//   ) {
//     parabolicCoords.value = [e.clientX, e.clientY]
//     targetIdRef.value = targetId
//   }
// function startGSAPAnimation(targetId: string | null) {
//   if (!targetId) return
//     // 此处执行GSAP动画
//     // eslint-disable-next-line unicorn/prefer-query-selector
//     const movingDiv = document.getElementById(targetId)
//     if (movingDiv) {
//       const cloneDom = movingDiv.cloneNode(true) as HTMLElement
//       cloneDom.style.position = 'absolute'
//       cloneDom.style.top = '0'
//       cloneDom.style.left = '0'
//       cloneDom.style.zIndex = 'calc(infinity)'
//       cloneDom.style.willChange = 'transform'
//       cloneDom.setAttribute('id', 'cloneNode')
//       document.querySelector('#app')?.append(cloneDom)
//       const tl = gsap.timeline({ repeat: 0 })
//       tl.to(cloneDom, {
//         duration: 0.4,
//         motionPath: {
//           path: '#parabolicPath',
//           align: '#parabolicPath',
//           alignOrigin: [0.5, 0.5],
//         },
//         // ease: 'cubic-bezier(0,1.57,.28,1.07)',
//         ease: 'linear',
//         onComplete() {
//           // eslint-disable-next-line unicorn/prefer-dom-node-remove
//           document.querySelector('#app')?.removeChild(cloneDom)
//         },
//       })
//     }
// }

/**
 * 如果鼠标 focus 在上方候选人列表，则可以按上下键切换候选人 --start
 * 因为这里 div 并不是 input 之类的 focus active element 元素
 * 所以不能通过 useActiveElement 来判断
 * 
 * 只能通过这种移动位置进出元素来判断
 */
const topCandidatesRef = ref<HTMLElement>()
const topCandidatesItemRefs = ref<InstanceType<typeof ItemComponent>[]>([])
function handleArrowUp() {
  console.log('⬆️')
    const index = topCandidates.value.findIndex(candidate => candidate.resumeId === currentResumeId.value)
    if (index !== -1) {
      if (index === 0) {
        handleSelectCurrentResume(topCandidates.value[index])
        scrollToIndex(index)
      }
      else {
        handleSelectCurrentResume(topCandidates.value[index - 1])
        scrollToIndex(index - 1)
      }
    }
}

function handleArrowDown() {
  console.log('⬇️ ')
    const index = topCandidates.value.findIndex(candidate => candidate.resumeId === currentResumeId.value)
    if (index !== -1) {
      if (index === topCandidates.value.length - 1) {
        handleSelectCurrentResume(topCandidates.value[index])
        scrollToIndex(index)
      }
      else {
        handleSelectCurrentResume(topCandidates.value[index + 1])
        scrollToIndex(index + 1)
      }
    }
}

const { arrowUp, arrowDown } = useMagicKeys()
watchEffect(() => {
  if (arrowUp.value) {
    handleArrowUp()
  }
  else if (arrowDown.value ) {
    handleArrowDown()
  }
})
function scrollToIndex(index: number) {
  const el = topCandidatesItemRefs.value[index]
  if (el.$el) {
    el.$el.scrollIntoView({
      behavior: 'smooth',
      block: 'center' // 滚动到元素的中心。 nearest 在视口中就不滚动了
    })
  }
}

/**
 * 如果鼠标 focus 在上方候选人列表，则可以按上下键切换候选人 --end
 */
</script>

<template>
  <div class="w-full h-full overflow-hidden flex flex-col bg-white box-border shadow-md">
    <!-- <ParabolicSVG
      :coords="parabolicCoords"
      @startGSAPAnimation="startGSAPAnimation(targetIdRef)"
    /> -->

    <!-- 上方候选人列表 -->
    <div 
      ref="topCandidatesRef"  
      class="pl-2 py-5 overflow-y-scroll transition-height duration-300 ease-in-out border-b border-[#ebeef5]" 
      :style="{ height: topSectionHeight }"
    >
      <transition-group name="list" tag="div">
        <ItemComponent 
          v-for="candidate in topCandidates" 
          :key="candidate.resumeId" 
          :id="candidate.resumeId"
          :candidate="candidate"
          :current-resume-id="currentResumeId"
          ref="topCandidatesItemRefs"
          @click="(e) => handleSelectCurrentResume(candidate, e)"
        />
      </transition-group>
    </div>

    
    <!-- 下方不合适候选人列表 -->
    <div v-if="bottomCandidates.length > 0" class="flex flex-col pb-2 transition-height duration-300 ease-in-out relative" :style="{ height: bottomSectionHeight }">
      <div v-if="showExpandButton" class="flex-shrink-0 border-y border-x-0 border-solid border-[#E8E8E8] h-8 flex items-center justify-center bg-white cursor-pointer text-xs text-[#333] -translate-y-1/2 z-10" @click="toggleBottomExpand">
        <div v-if="!isBottomExpanded" class="i-lucide-chevrons-up text-lg text-[#686868]" />
        <div v-else class="i-lucide-chevrons-down text-lg text-[#686868]" />
      </div>
      <div class="overflow-y-scroll flex-1 pl-2">
        <transition-group name="list" tag="div">
          <ItemComponent 
            v-for="candidate in bottomCandidates" 
            :key="candidate.resumeId" 
            :current-resume-id="currentResumeId" 
            :candidate="candidate"
            @click="handleSelectCurrentResume(candidate)"
          />
        </transition-group>
      </div>
    </div>
  </div>
</template>

<style scoped>

.list-move,
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-active {
  position: absolute;
}
</style>

<style>
.slide-up-resume-status-enter-active,
.slide-up-resume-status-leave-active {
  transform-style: preserve-3d;
  transition: all 0.25s ease-out;
}

.slide-up-resume-status-enter-from {
  opacity: 0;
  /* transform: translateY(6px); */
  transform: translateY(6px) rotateX(-90deg);

}

.slide-up-resume-status-leave-to {
  opacity: 0;
  /* transform: translateY(-6px); */
  transform: translateY(-6px) rotateX(180deg);
}
</style>
