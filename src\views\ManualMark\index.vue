<template>
  <div class="resume-manage-container">
    <h3 class="resume-manage-title">人工阅卷</h3>
    <div class="resume-manage">
      <div class="resume-manage-header">
        <div class="header-left">
          <a-input-search
            class="search-wrapper"
            v-model:value.trim="searchContent"
            placeholder="请输入关键词"
            @keyup.enter="getMarkData"
            @search="getMarkData"
          />
        </div>
      </div>
      <resume-table
        :data="data"
        :pagination="paginationConfig"
        :refreshFn="getMarkData"
        @handleTableChange="handleTableChange"
      >
      </resume-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, ref, watch } from 'vue'
import ResumeTable from '@/views/ManualMark/components/ResumeTable.vue'
import { markingtask } from '@/api/admin/paperManage'
import { useRoute } from 'vue-router'
import store from '@/store'

// 简历阅卷数据
const data = reactive({
  columns: [
    {
      title: '考试名称',
      dataIndex: 'name',
      key: 'name',
      align: 'left',
      width: 300,
      ellipsis: true,
      resizable: true
    },
    {
      title: '创建人',
      dataIndex: 'author',
      key: 'create_by',
      align: 'left',
      width: 120,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '本人', value: 1 },
          { text: '其他', value: 0 },
      ],
    },
    {
      title: '考试类型',
      dataIndex: 'uniexam',
      key: 'uniexam',
      customRender: ({text}: any) => ['非统考', '统考'][text],
      align: 'left',
      width: 160,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '非统考', value: '0' },
          { text: '统考', value: '1' },
      ],
    },
    {
      title: '总分值',
      dataIndex: 'score',
      key: 'score',
      align: 'right',
      width: 160,
      ellipsis: true,
      resizable: true
    },
    {
      title: '考试时间',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'left',
      width: 220,
      ellipsis: true,
      resizable: true,
      sorter: true
    },
    {
      title: '考试状态',
      dataIndex: 'exam_status',
      key: 'exam_status',
      align: 'left',
      width: 160,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '未开始', value: -1 },
          { text: '考试中', value: 0 },
          { text: '已结束', value: 1 },
      ],
    },
    {
      title: '阅卷状态',
      dataIndex: 'status',
      key: 'status',
      align: 'left',
      width: 160,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '待阅卷', value: false },
          { text: '已阅完', value: true },
      ],
    },
    {
      title: '发布状态',
      dataIndex: 'published',
      key: 'published',
      align: 'left',
      width: 160,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '未发布', value: false },
          { text: '已发布', value: true },
      ],
    },
    {
      title: '批改量（题次）',
      dataIndex: 'modifynum',
      key: 'modifynum',
      align: 'right',
      width: 180,
      ellipsis: true,
      resizable: true
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      key: 'action',
      width: 160,
    }
  ],
  loading: false,
  total: 0,
  taskList: []
})

// 快速搜索
const searchContent = ref('')
const otherParams = ref<{
  order_field?: string
  order_type?: 'ascend' | 'descend'
}>({})

// 分页配置项
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: number) => '总条数：' + total,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

// table 页码,排序改变
const handleTableChange = ({ pagination, filters, sort }: any) => {
  // 处理分页
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  // 处理排序
  otherParams.value.order_type = sort.order
  otherParams.value.order_field = sort.order ? sort.field : undefined
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getMarkData()
}
async function getMarkData() {
  data.loading = true
  try {
    let res = await markingtask({
      action: 'query',
      page: paginationConfig.value.current,
      per_page: paginationConfig.value.pageSize,
      teacher: store.getters.userid,
      condition: searchContent.value,
      ...otherParams.value
    }) as any
    data.taskList = res.data
    paginationConfig.value.total = res.total
  } finally {
    data.loading = false
  }
}

const route = useRoute()
watch(
  () => route.params.page,
  (val) => {
    if (val) {
      paginationConfig.value.current = Number(val)
    }
  },
  {
    immediate: true
  }
)

onMounted(() => {
  getMarkData()
})
</script>
<style lang="less" scoped>
.resume-manage-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  overflow: auto;
  padding: 0 20px 0 20px;
}
.resume-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}
.resume-manage {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 10px 24px;
  background-color: #fff;
  .resume-manage-header {
    margin: 8px 0;
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;
    align-items: center;
    .search-wrapper {
      --antd-wave-shadow-color: #fff !important;
      width: 240px;
      line-height: 32px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;

      :deep(.ant-input) {
        font-size: 13px;
        box-shadow: none;
        border: none;
        border-radius: 8px;
        line-height: 26px;
      }
      :deep(.ant-input-group-addon) {
        border-radius: 8px;
      }
      :deep(.ant-input-search-button) {
        border-radius: 8px !important;
        box-shadow: none !important;
        border: none !important;
      }
    }
    .header-right {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
  }
  .table-bar {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .ant-checkbox-wrapper {
      font-size: 15px;
    }
    .show-columns {
      font-size: 15px;
    }
  }

  .resumes {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
  }
  .resume-card {
    width: 360px;
    margin-bottom: 20px;
    margin-right: 20px;
    overflow: hidden;
  }
  .resume-content {
    display: flex;
    .avatar-img {
      width: 1rem;
      height: 1rem;
    }
    .resume-info {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
      span {
        line-height: 1.5;
      }
    }
  }
  .dynamic-column {
    height: 60px;
    line-height: 62px;
    padding-left: 15px;
  }
}
</style>
