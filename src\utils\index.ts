import { PaperFbodyItemModelWithQuestionChildren } from "@/models/paperModel"
import officialIcon from '@/assets/icons/svg/auth.svg'

export const formatAccuracy = (accuracy: number) => {
  if (accuracy === null) {
    return '-'
  } else {
    return Math.floor(accuracy * 100) + '%'
  }
}

export function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0,
      v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}


export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any;
  }

  if (obj instanceof Array) {
    const newArray = obj.map(item => deepClone(item));
    return newArray as any;
  }

  if (obj instanceof Object) {
    const newObj: Record<string, any> = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        newObj[key] = deepClone(obj[key]);
      }
    }
    return newObj as T;
  }

  if (obj as any instanceof Function) {
    return (obj as any).bind({}) as any;
  }

  throw new Error("Unsupported object type");
}

export function numberToChinese(number: number) {
  const chineseNumberMap = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  const chineseUnitMap = ['', '十', '百'];

  if (number < 1 || number >= 100) {
    return '请输入大于0小于100的数字';
  }

  if (number < 10) {
    return chineseNumberMap[number];
  } else {
    const digits = String(number).split('').map(Number);
    const digitCount = digits.length;

    let chineseNumber = '';
    for (let i = 0; i < digitCount; i++) {
      const digit = digits[i];
      if (digit !== 0) {
        chineseNumber += chineseNumberMap[digit] + chineseUnitMap[digitCount - i - 1];
      } else {
        // Avoid consecutive zeros
        if (chineseNumber[chineseNumber.length - 1] !== chineseNumberMap[0]) {
          chineseNumber += chineseNumberMap[digit];
        }
      }
    }

    // Remove trailing unit "十" if necessary
    if (chineseNumber[chineseNumber.length - 1] === chineseUnitMap[1]) {
      chineseNumber = chineseNumber.slice(0, -1);
    }

    // Remove leading "一" if necessary
    if (chineseNumber.startsWith(chineseNumberMap[1]) && chineseNumber.length > 1) {
      chineseNumber = chineseNumber.slice(1);
    }

    // Remove trailing "零" if necessary
    if (chineseNumber.endsWith(chineseNumberMap[0])) {
      chineseNumber = chineseNumber.slice(0, -1);
    }
    return chineseNumber;
  }
}

type TreeItemModel = {
  id: string
  children?: TreeItemModel[]
}
export function findItemInTree(id: string, tree: TreeItemModel[]): TreeItemModel | null {
  let item: any = null
  for (let i = 0; item = tree[i]; i++) {
    if (item.id === id) {
      return item
    } else if (item.children?.length) {
      item = findItemInTree(id, item.children)
      if (item) return item
    }
  }
  return item
}

// 数字转化千分位 
export const formatWithMod = (number: number) => {
  let n = number
  let r = ''
  let temp
  let mod
  do {
    // 求模的值， 用于获取高三位，这里可能有小数
    mod = n % 1000
    // 值是不是大于1，是继续的条件
    n = n / 1000
    // 高三位
    temp = ~~mod
    // 1.填充: n > 1 循环未结束， 就要填充为比如 1 => 001
    // 不然temp = ~~mod的时候, 1 001， 就会变成 "11"
    // 2.拼接“,”
    r = (n >= 1 ? `${temp}`.padStart(3, '0') : temp) + (!!r ? ',' + r : '')
  } while (n >= 1)
  const strNumber = number + ''
  let index = strNumber.indexOf('.')
  // 拼接小数部分
  if (index >= 0) {
    r += strNumber.substring(index)
  }
  return r
}

// 判断一个字符串是否含有图片或者公式标签
export function hasImageOrFormula(str: string) {
  const reg = /<img|<span class="ql-formula" /g
  return reg.test(str)
}

const findLastTextNode = (element: any): any => {
  let lastTextNode = null

  for (let i = element.childNodes.length - 1; i >= 0; i--) {
    const child = element.childNodes[i]

    if (child.nodeType === 3 && child.nodeValue.trim() !== '') {
      // 文本节点且包含非空白字符
      lastTextNode = child
      break
    } else if (child.nodeType === 1) {
      // 元素节点，递归检查子节点
      lastTextNode = findLastTextNode(child)
      if (lastTextNode) {
        break
      }
    }
  }

  return lastTextNode
}
export const htmlAddSimulate = (htmlContent: string) => {
  // 创建一个虚拟的DOM元素
  const tempElement = document.createElement('div')
  tempElement.innerHTML = htmlContent

  // 找到最后一个包含显示文本的节点
  const lastTextNode = findLastTextNode(tempElement)

  // 在文本节点的末尾添加括号
  if (lastTextNode) {
    lastTextNode.nodeValue += '( )';
  }

  return tempElement.innerHTML

}

/** 根据fbody计算考试总时长（调用此函数的前提是单题限时类型） */
export function getTotalSeconds(fbody: PaperFbodyItemModelWithQuestionChildren[]) {
  let totalSeconds = 0
  fbody?.forEach((item) => {
    if (item.options) {
      // 如果是选做题，则取options个时间
      totalSeconds += item.options * item.children[0].duration
    } else {
      totalSeconds += item.children.reduce((prev: number, cur: any) => prev + (cur.duration ?? 0), 0)
    }
  })
  return totalSeconds
}

/** 题干展示 */
export function getQuestionContentByRecord(record: any) {
  // 是否需要展示官方认证的icon（既是官方认证也校对正确）
  const isIconShow = record.official_cert && (record.proofreading === 1)

  if (!isIconShow) return record.complicatedediting ? record.complexcontent : record.body

  const iconStr = `<img src=${officialIcon} style="width: 14px!important;height: 14px!important;margin-right: 8px;position: relative;top: -2px;">`
  return record.complicatedediting ? record.complexcontent.replace('<p>', `<p>${iconStr}`) : `<p>${iconStr}${record.body}</p>`
}

// 给某个题目的题干内容插入括号
export function insetParenthesis(item: any) {
  const reg = /[(（]\s*[)）]/
  if (![0, 1, 2, 6].includes(item.type)) return
  if (item.complexcontent && !reg.test(item.complexcontent)) {
    // 富文本中找到最后一个内容，在其后面加上( )
    item.complexcontent = htmlAddSimulate(item.complexcontent)
  }
  if (item.body && !reg.test(item.body)) {
    item.body = `${item.body}( )`
  }
}

// 设置题号
export const setFbodyQuestionNumber = (fbody: any) => {
  let currentNumber = 1 // 当前题号
  fbody.forEach((item: any) => {
    item.children?.forEach((q: any) => {
      q.number = currentNumber
      currentNumber++
    })
  })
}

// 设置问卷题号
export function setSurveyQuestionNumber(arr) {
  let realIndex = 0;
  let previousSpecialTypeCount = 0;

  arr.forEach((item, index) => {
    // 如果当前题目是特殊类型（分割线），则不增加真实索引，只增加特殊类型计数
    if (item.type > 100) {
      previousSpecialTypeCount++;
      Reflect.deleteProperty(item, 'number')
    } else {
      // 真实索引为当前索引减去之前的特殊类型题目数
      realIndex = index + 1 - previousSpecialTypeCount;
      item.number = realIndex;
    }
  })
}


export function convertToABC(number: number) {
  if (number >= 0 && number < 26) {
    // 将数字转换为对应的大写字母
    return String.fromCharCode('A'.charCodeAt(0) + number);
  } else {
    return 'Invalid Input';
  }
}


export function convertABCTo123(char: string) {
  return char.charCodeAt(0) - 'A'.charCodeAt(0);
}

export function findDeletedBlanks(oldText: string, newText: string) {
  let oldStr = oldText
    .replace(/^_(?=[^_])/gi, '@')
    .replace(/(?<=[^_])_$/gi, '@')
    .replaceAll(/(?<=[^_])_(?=[^_])/gi, '@')
    .replaceAll(/_+/gi, '_')
  let newStr = newText
    .replace(/^_(?=[^_])/gi, '@')
    .replace(/(?<=[^_])_$/gi, '@')
    .replaceAll(/(?<=[^_])_(?=[^_])/gi, '@')
    .replaceAll(/_+/gi, '_')
  const deletedTextLength = oldStr.length - newStr.length

  const maxLength = Math.max(oldStr.length, newStr.length)
  if (oldStr.length < maxLength) {
    oldStr = oldStr + '@'.repeat(maxLength - oldStr.length)
  }
  if (newStr.length < maxLength) {
    newStr = newStr + '@'.repeat(maxLength - newStr.length)
  }

  const deletedBlankIndexArray = []
  for (let i = 0; i < newStr.length; i++) {
    // console.log(i, newStr[i])
    if (newStr[i] !== oldStr[i]) {
      // 删除的开始位置为i
      // 删除的长度
      // console.log('i', i)
      const endIndex = i + Math.abs(deletedTextLength)
      // 当前遍历到的空格的编号
      let currentBlankIndex = 0
      for (let j = 0; j < oldStr.length; j++) {
        if (oldStr[j] === '_') {
          // console.log('j', j)
          if (j >= i && j <= endIndex) {
            deletedBlankIndexArray.push(currentBlankIndex)
          } else if (j > endIndex) {
            break
          }

          currentBlankIndex++
        }
      }

      break
    }
  }

  return deletedBlankIndexArray.reverse()
}


/**
 * 判断指定id是否存在于森林中
 * @param {string} id - 检查的id
 * @param {Array<{ id: string; children?: { id: string }[] }>} trees - 森林结构
 * @return {boolean} - id是否存在于森林中
 */
export const isIdInTrees = (id: string, trees: { id: string; children?: { id: string }[] }[]): boolean => {
  for (const item of trees) {
    if (item.id === id) return true;
    if (item.children?.length) {
      const foundInChildren = isIdInTrees(id, item.children);
      if (foundInChildren) return true;
    }
  }
  return false;
}

/** 
 * 根据分数获取星级
 *  一星——35-47
 *  二星——48-60
 *  三星——61-73
 *  四星——74~86
 *  五星——87~100
 */
export const getStarByScore = (score: number) => {
  if (score < 35) {
    return 0;
  } else if (score < 48) {
    return 1;
  } else if (score < 61) {
    return 2;
  } else if (score < 74) {
    return 3;
  } else if (score < 87) {
    return 4;
  } else {
    return 5;
  }
}