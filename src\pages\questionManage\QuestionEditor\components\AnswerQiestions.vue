<script setup lang="ts">
import ScoreInput from '@/views/QuestionManage/components/ScoreInput.vue'
import QuestionStem from './QuestionStem.vue'

const props = defineProps<{
  formState: any
  optionEditorsRef: any
  handleOptionBlur: () => void
  delOption: (event: Event, index: number) => void
  addOption: () => void
  showGenerateOptionModal: () => void
  scorebasisEditorRef?: any
  scorepts: any
  getPointsScore?: any
  delPoints?: any
  addPoints?: any
}>()
</script>

<template>
  <QuestionStem />
  <a-form-item v-if="formState.type === 3" label="评分依据" name="scorebasis">
    <!-- eslint-disable vue/no-mutating-props -->
    <a-textarea
      v-if="!formState.complicatedediting"
      v-model:value="formState.scorebasis"
      :rows="4"
      placeholder="点击编辑"
    />
    <VueQuillEditor
      v-else
      :ref="scorebasisEditorRef"
      v-model:content="formState.scorebasis"
    />
    <p class="standard-tip">
      (仅阅卷老师可见)
    </p>
  </a-form-item>
  <a-form-item
    v-if="formState.type === 3"
    label="得分点"
    name="answer"
    class="points-wrapper"
  >
    <template v-for="(item, index) in scorepts.value" :key="index">
      <div class="item">
        <div class="index">
          {{ index + 1 }}
        </div>
        <div class="tags-select">
          <JTagInput
            v-model="item.keyword"
            :ignorecase="formState.ignorecase"
            placeholder="输入完成后按回车添加多个关键词"
          />
        </div>
        <div v-if="formState.sepscore" class="score-wrapper">
          <span class="score">分值</span>
          <ScoreInput
            v-model="item.score"
            class="score-input"
            @get-score="getPointsScore($event, item)"
          />
        </div>
        <div class="del-icon-wrap">
          <svg-icon
            class="del-icon"
            name="circle-del"
            width="16px"
            height="16px"
            @click="delPoints(index)"
          />
        </div>
      </div>
    </template>
    <div class="addpoints-btn">
      <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
    </div>
  </a-form-item>
  <div v-if="formState.type === 3" class="fill-blank-config">
    <div>
      <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
      <span>忽略大小写</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>