import { createStore } from 'vuex'
import storage from '@/utils/storage'
import { userLogin, studentLogin, smscodelogin } from '@/api/login'
import { getUserInfo } from '@/api/user'
import permission from './permission'
import { ststoken } from '@/api/exam'
import { stat } from 'fs'

// 定义一个state的接口
export interface State {
  collapsed: boolean
  accessToken: string
  refreshToken: string
  roles: Array<string>
  username: string | null
  email: string | null
  userid: string | null
  ststoken: Object
  userInfo: Object
  appConfig: {
    isEditQuestionResetProofreadConfirm: boolean
    isEditInterviewQuestionResetProofreadConfirm: boolean
  }
  paperInfo: Object
  permiss: Object
  mode: string
}

const store = createStore<State>({
  state() {
    return {
      collapsed: false, // 菜单折叠
      roles: [],
      userInfo: {},
      username: null,
      email: null,
      userid: null,
      accessToken: '' || storage.getItem('accessToken'),
      refreshToken: '' || storage.getItem('refreshToken'),
      permiss: {
        quesmag: {},
        papermag: {},
        stumag: {},
        analysis: {}
      },
      mode: storage.getItem('mode') || 'sun', // 阅卷模式 (日光/暗黑) dark
      ststoken: {},
      paperInfo: {},
      appConfig: {
        isEditQuestionResetProofreadConfirm: false,
        isEditInterviewQuestionResetProofreadConfirm: false
      }
    }
  },
  modules: {
    permission
  },
  getters: {
    collapsed: (state) => state.collapsed,
    accessToken: (state) => state.accessToken,
    refreshToken: (state) => state.refreshToken,
    userInfo: (state) => state.userInfo,
    appConfig: (state) => {
      console.log('storage.getItem',storage.getItem('appConfig') )
      return storage.getItem('appConfig') || state.appConfig
    },
    roles: (state) => state.roles,
    username: (state) => state.username,
    email: (state) => state.email,
    userid: (state) => state.userid,
    ststoken: (state) => state.ststoken,
    paperInfo: (state) => state.paperInfo,
    permiss: (state) => state.permiss,
    mode: (state) => state.mode
  },
  mutations: {
    // 设置mode
    MODIFY_MODE(state, mode) {
      state.mode = mode
      storage.setItem('mode', mode)
      document.body.className = mode
    },
    MODIFY_COLLAPSE(state) {
      state.collapsed = !state.collapsed
    },
    // 存储accessToken
    SET_ACCESS_TOKEN(state, accessToken) {
      state.accessToken = accessToken
      storage.setItem('accessToken', accessToken)
    },
    // 存储refreshToken
    SET_REFRESH_TOKEN(state, refreshToken) {
      state.refreshToken = refreshToken
      storage.setItem('refreshToken', refreshToken)
    },
    // 清除Token
    CLEAR_TOKEN(state) {
      state.accessToken = ''
      state.refreshToken = ''
      state.roles = []
      storage.clearAll()
    },
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    SET_APP_CONFIG(state, appConfig) {
      state.appConfig = appConfig
      storage.setItem('appConfig', appConfig)
    },
    SET_APP_CONFIG_WITH_OBJ(state, config: Record<string, any>) {
      let currentConfig = storage.getItem('appConfig') || state.appConfig
      let newConfig = Object.assign({}, currentConfig, config)
      console.log('new Config',newConfig)
      storage.setItem('appConfig', newConfig)
    },
    // 设置ststoken key
    SET_STSTOKEN(state, ststoken) {
      state.ststoken = ststoken
    },
    SET_PAPER_INFO(state, paperInfo) {
      state.paperInfo = Object.assign(state.paperInfo, paperInfo)
    },
    CLEAR_PAPER_INFO(state) {
      state.paperInfo = {}
    }
  },
  actions: {
    // 菜单menu展开折叠
    MODIFY_COLLAPSE(context) {
      context.commit('MODIFY_COLLAPSE')
    },
    // 登录
    USER_LOGIN(context, userInfo) {
      // context.state.roles = []
      return new Promise((resolve, reject) => {
        // 学生、教师登录
        if (!!userInfo.email) {
          userInfo.passwordHash = userInfo.password
          studentLogin(userInfo)
            .then((data: any) => {
              const { access_token, refresh_token, role } = data
              context.commit('SET_ACCESS_TOKEN', access_token)
              context.commit('SET_REFRESH_TOKEN', refresh_token)
              context.commit('SET_APP_CONFIG_WITH_OBJ', { 
                isEditQuestionResetProofreadConfirm: false, 
                isEditInterviewQuestionResetProofreadConfirm: false 
              })
              resolve(data)
            })
            .catch((error: Error) => {
              reject(error)
            })
        } else {
          // 管理员登录
          userLogin(userInfo)
            .then((data: any) => {
              const { access_token, refresh_token } = data
              context.commit('SET_ACCESS_TOKEN', access_token)
              context.commit('SET_REFRESH_TOKEN', refresh_token)
              resolve(data)
            })
            .catch((error: Error) => {
              reject(error)
            })
        }
      })
    },
    USER_POHNE_LOGIN(context, userInfo) {
      return new Promise((resolve, reject) => {
        smscodelogin(userInfo)
          .then((data: any) => {
            const { access_token, refresh_token, role } = data
            context.commit('SET_ACCESS_TOKEN', access_token)
            context.commit('SET_REFRESH_TOKEN', refresh_token)
            context.commit('SET_APP_CONFIG_WITH_OBJ', { 
              isEditQuestionResetProofreadConfirm: false,
              isEditInterviewQuestionResetProofreadConfirm: false
            })
            resolve(data)
          })
          .catch((error: Error) => {
            reject(error)
          })
      })
    },
    // 退出登录
    USER_LOGIN_OUT({ commit }) {
      return new Promise((resolve) => {
        commit('CLEAR_TOKEN')
        resolve('登出成功！')
      })
    },

    // 获取用户信息，包含权限
    GET_USER_INFO({ commit, state }) {
      return new Promise((resolve, reject) => {
        console.log('获取用户信息', state.userInfo)
        if(state.userInfo && (state.userInfo as any)!.id) return resolve(state.userInfo)
        getUserInfo({ action: 'query' })
          .then((data: any) => {
            commit('SET_USER_INFO', data)
            // 存储roles信息
            // state.roles = [...data.roles]
            // state.permiss = data.permission
            // data.permission = []
            data.roles = []
            state.roles = []
            state.username = data.username
            state.email = data.email
            state.userid = data.id
            resolve(data)
          })
          .catch((error) => {
            commit('CLEAR_TOKEN')
            reject(error)
          })
      })
    },
    // 设置ststoken
    async CHANGE_STS_TOKEN({ commit }, id) {
      const res: any = await ststoken({ paper: id })
      const token = {
        accessKeyId: res.AccessKeyId,
        accessKeySecret: res.AccessKeySecret,
        stsToken: res.SecurityToken
      }
      commit('SET_STSTOKEN', token)
    }
  }
})

export default store