import request from '@/utils/http'

// 回收站题目
export function questrashitem(data?: object) {
  return request({
    url: '/questionBankWrite/trash/page',
    data
  })
}

export function recoverquestrashitem(data?: object) {
  return request({
    url: '/questionBankWrite/trash/recover',
    data
  })
}

export function delquestrashitem(data?: object) {
  return request({
    url: '/questionBankWrite/trash/delete',
    data
  })
}

// 回收站试卷
export function recoverpapertrashitem(data?: object) {
  return request({
    url: '/papertemplate/trash/recover',
    data
  })
}

export function delpapertrashitem(data?: object) {
  return request({
    url: '/papertemplate/trash/delete',
    data
  })
}

export function papertrashitem(data?: object) {
  return request({
    url: '/papertemplate/trash/page',
    data
  })
}
