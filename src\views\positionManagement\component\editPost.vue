<script setup lang="ts">
import { computed, ref } from 'vue'

interface FormData {
  positionName: string
  recruiter: string
  jobType: string
  salaryType: string
  workLocation: string
  salaryRange: { min: string, max: string }
  workExperience: string
  educationRequirement: string
  positionDescription: string
  requiredSkills: string[]
  additionalSkills: string
}

const formData = ref<FormData>({
  positionName: 'Java前端工程师',
  recruiter: '张三三',
  jobType: '社招',
  salaryType: '',
  workLocation: '',
  salaryRange: { min: '', max: '' },
  workExperience: '',
  educationRequirement: '',
  positionDescription: '岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述岗位描述',
  requiredSkills: ['计算机', 'Java'],
  additionalSkills: '',
})

const recruitmentSteps = [
  { name: '人工面试', icon: '👤', bgColor: 'bg-blue-100' },
  { name: 'AI面试', icon: '🤖', bgColor: 'bg-blue-100' },
  { name: '笔试', icon: '📝', bgColor: 'bg-blue-100' },
  { name: '混合考试', icon: '🔄', bgColor: 'bg-blue-100' },
  { name: '心理测评', icon: '🧠', bgColor: 'bg-blue-100' },
]

const characterCount = computed(() => formData.value.positionDescription.length)
const maxCharacters = 5000

const newSkill = ref('')

function handleAIAnalysis() {
  console.log('AI技能解析')
}

function handleSubmit() {
  console.log('确认', formData.value)
}

function handleCancel() {
  console.log('取消')
}

function addSkill() {
  if (newSkill.value && !formData.value.requiredSkills.includes(newSkill.value)) {
    formData.value.requiredSkills.push(newSkill.value)
    newSkill.value = ''
  }
}

function removeSkill(index: number) {
  formData.value.requiredSkills.splice(index, 1)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow p-8">
      <!-- 岗位信息 -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-6">
          岗位信息
        </h2>
        
        <div class="grid grid-cols-3 gap-x-8 gap-y-6">
          <!-- 第一行 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span> 岗位名称
            </label>
            <el-input 
              v-model="formData.positionName"
              placeholder="Java前端工程师"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span> 负责人
            </label>
            <el-select 
              v-model="formData.recruiter"
              placeholder="张三三"
              class="w-full"
            >
              <el-option label="张三三" value="张三三" />
              <el-option label="李四四" value="李四四" />
              <el-option label="王五五" value="王五五" />
            </el-select>
          </div>

          <div />

          <!-- 第二行 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span> 招聘类型
            </label>
            <el-select 
              v-model="formData.jobType"
              class="w-full"
            >
              <el-option label="社招" value="社招" />
              <el-option label="校招" value="校招" />
            </el-select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">薪资类型</label>
            <el-select 
              v-model="formData.salaryType"
              placeholder="请选择"
              class="w-full"
            >
              <el-option label="月薪" value="月薪" />
              <el-option label="年薪" value="年薪" />
            </el-select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">薪资范围</label>
            <div class="flex items-center space-x-2">
              <el-input 
                v-model="formData.salaryRange.min"
                placeholder="最低薪资" 
                class="flex-1"
              />
              <span class="text-gray-500">至</span>
              <el-input 
                v-model="formData.salaryRange.max"
                placeholder="最高薪资" 
                class="flex-1"
              />
              <span class="text-gray-500">千</span>
            </div>
          </div>

          <!-- 第三行 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">工作地点</label>
            <el-select 
              v-model="formData.workLocation"
              placeholder="请选择"
              class="w-full"
            >
              <el-option label="北京" value="北京" />
              <el-option label="上海" value="上海" />
              <el-option label="深圳" value="深圳" />
            </el-select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">工作经验</label>
            <el-select 
              v-model="formData.workExperience"
              placeholder="请选择"
              class="w-full"
            >
              <el-option label="不限" value="不限" />
              <el-option label="1-3年" value="1-3年" />
              <el-option label="3-5年" value="3-5年" />
              <el-option label="5年以上" value="5年以上" />
            </el-select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">学历要求</label>
            <el-select 
              v-model="formData.educationRequirement"
              placeholder="请选择"
              class="w-full"
            >
              <el-option label="不限" value="不限" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
            </el-select>
          </div>
        </div>

        <!-- 岗位描述 -->
        <div class="mt-8">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            <span class="text-red-500">*</span> 岗位描述
          </label>
          <el-input
            v-model="formData.positionDescription"
            type="textarea"
            :rows="6"
            placeholder="请输入岗位描述"
            class="mb-3"
          />
          <div class="flex justify-between items-center">
            <el-button 
              type="primary" 
              size="small"
              @click="handleAIAnalysis"
            >
              AI 技能解析
            </el-button>
            <span class="text-sm text-gray-500">{{ characterCount }}/{{ maxCharacters }}</span>
          </div>
        </div>
      </div>

      <!-- 技能要求 -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-6">
          技能要求
        </h2>
        
        <!-- 必备技能 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">
            <span class="text-red-500">*</span> 必备技能
          </label>
          <div class="flex flex-wrap gap-2 mb-3">
            <el-tag
              v-for="(skill, index) in formData.requiredSkills"
              :key="index"
              closable
              type="primary"
              @close="removeSkill(index)"
            >
              {{ skill }}
            </el-tag>
          </div>
          <div class="flex gap-2">
            <el-input
              v-model="newSkill"
              placeholder="输入技能名称"
              class="w-48"
              @keyup.enter="addSkill"
            />
            <el-button @click="addSkill">
              添加
            </el-button>
          </div>
        </div>

        <!-- 加分技能 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">加分技能</label>
          <el-input
            v-model="formData.additionalSkills"
            placeholder="请输入加分技能"
          />
        </div>
      </div>

      <!-- 招聘流程 -->
      <div class="mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-6">
          招聘流程
        </h2>
        
        <div class="flex items-center justify-between max-w-4xl">
          <template v-for="(step, index) in recruitmentSteps" :key="index">
            <div class="flex flex-col items-center">
              <div class="w-20 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2 border">
                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <span class="text-white text-lg">{{ step.icon }}</span>
                </div>
              </div>
              <span class="text-sm text-gray-700 text-center">{{ step.name }}</span>
            </div>
            
            <!-- 箭头 -->
            <div 
              v-if="index < recruitmentSteps.length - 1"
              class="flex-1 flex justify-center mx-4"
            >
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </div>
          </template>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-4">
        <el-button size="large" @click="handleCancel">
          取消
        </el-button>
        <el-button 
          type="primary"
          size="large"
          @click="handleSubmit"
        >
          确认
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-select {
  width: 100%;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-input__inner) {
  font-family: inherit;
}
</style>