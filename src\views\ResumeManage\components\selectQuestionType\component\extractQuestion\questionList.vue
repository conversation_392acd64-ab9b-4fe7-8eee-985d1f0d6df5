<script lang="ts" setup>
import type { QuestionModel } from '@/models/questionModel'
import type ProofreadingTask from '@/pages/questionManage/components/ProofreadingTask.vue'
import { getaiproofreadingtaskids } from '@/api/exam/index'
import { QuestionEnum } from '@/models/questionModel'
import { addAiCheckByQPathApi, getBankProgress, queryQuestion, quesionDelete, quesionDeleteBatch } from '@/pages/questionManage/hooks/api'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
import { getQuestionContentByRecord } from '@/utils'
import emitter from '@/utils/bus'
import { useQuestionManage } from '@/views/QuestionManage/_components/composable'
import { InfoCircleFilled, StarFilled } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, createVNode, nextTick, onDeactivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

const props = withDefaults(
  defineProps<{
    subjectId?: string
    subjectPath?: string
    /** 是否是官方科目 */
    isOfficialSubject?: boolean
    defaultType?: number | null
    defaultSelectedRows?: any[]
    defaultExcludedRowKeys?: string[]
    disabledRowKeys?: string[]
    writable?: boolean
    current?: number
    subjectUUID?: any
    operationFlag?: any
    selectItem?: any
  }>(),
  {
    subjectId: '',
    isOfficialSubject: false,
    defaultType: null,
    defaultSelectedRows: () => [],
    defaultExcludedRowKeys: () => [],
    disabledRowKeys: () => [],
    writable: false,
    current: 1,
    subjectUUID: '',
    operationFlag: false,
  },
)
const emits = defineEmits<{
  (e: 'select', record: any, selected: boolean): void
  (e: 'removeQuestion', id: string): void
}>()
const { clearFormState } = useQuestionManage()
const store = useStore()

// 可选/已选/已排除
const listTypeEnum = ['全部', '已选']
const listType = ref('全部')

// 是否包含子科目
const includeSubCatg = ref(true)
// 其他参数
const otherParams = ref<{
  types?: number[] | null
  proofreading?: (0 | 1 | 2)[]
  order_field?: string
  order_type?: 'ascend' | 'descend'
  /** 是否仅显示当前科目的试题（不包含子科目） */
  includeChildrenUUIDs: boolean
  create_by?: number[] | null
}>({
      types: typeof props.defaultType === 'number' ? [props.defaultType] : null,
      includeChildrenUUIDs: true,
    })

const paginationConfig = reactive<any>({
  current: props.current,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total: number) => `总条数：${total}`,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small',
})
// 快速搜索
const searchContent = ref('')

const recordParams = ref<any>({
  entity: {},
  params: {},
})
// 已选列表
const selectedRows = ref<any[]>([])
const selectedRowKeys = computed(() => {
  // 使用 Set 去重，避免重复的 id
  const uniqueIds = [...new Set(selectedRows.value.map(i => i.id))]
  return uniqueIds
})
function getFirstPageList() {
  otherParams.value.includeChildrenUUIDs = includeSubCatg.value
  paginationConfig.current = 1
  const params: any = {
    entity: {
      content: `*${searchContent.value}*`,
    },
    params: {},
  }

  if (listType.value === '已选') {
    params.ids = selectedRowKeys.value
    params.params.id_MultiString = params.ids.join(',')
    // params.questionBankUUIDList = params.ids
  }
  recordParams.value.entity = params.entity
  recordParams.value.params = params.params
  getList(params)
}
const rawDefaultSelectedRows = ref<any>([])
const tableRef = useTemplateRef('table')
// 标签
const activeTagIds = reactive<string[]>([])
const tagsList = ref<any>([])
const selectedTags = computed(() =>
  tagsList.value.filter((item: any) => activeTagIds.includes(item.id)).map((i: any) => i.name),
)
function handleClickTag(id: string) {
  const index = activeTagIds.findIndex(i => i === id)
  if (index === -1) {
    activeTagIds.push(id)
  }
  else {
    activeTagIds.splice(index, 1)
  }
  getList()
}

function handleTableChange(pagination: any, filters: any = {}, sort: any = {}, actionInfo: any) {
  const { action } = actionInfo
  const params = {
    entity: {},
    params: {},
  } as any
  if (action === 'filter' || action === 'sort') {
    paginationConfig.current = 1

    if (filters.proofreading) {
      params.entity.checkStatus = filters.proofreading.join(',')
    }
    else {
      delete recordParams.value.entity.checkStatus
    }

    if (filters.difficulty) {
      // params.params['difficulty'] = filters.difficulty
      params.params.difficulty_MultiString = filters.difficulty.join(',')
    }
    else {
      delete recordParams.value.params.difficulty_MultiString
    }

    console.log('this is column', sort)
    if (sort && sort.columnKey && sort.order) {
      params.params = {
        order: sort.order === 'descend' ? 'desc' : 'asc',
        column: sort.columnKey,
      }
    }
    if (sort && !sort.order) {
      delete recordParams.value.params.order
      delete recordParams.value.params.column
    }
  }
  else if (action === 'paginate') {
    paginationConfig.current = pagination.current
    paginationConfig.pageSize = pagination.pageSize
  }
  
  recordParams.value.entity = Object.assign(recordParams.value.entity, params.entity)
  recordParams.value.params = Object.assign(recordParams.value.params, params.params)

  params.entity = recordParams.value.entity
  params.params = recordParams.value.params
  params.params.order = recordParams.value.params.order ?? 'desc'
  params.params.column = recordParams.value.params.column ?? 'createTime'
  getList(params)
}

// 获取所有标签
const tagBtnState = ref<'隐藏' | '更多' | '收起'>('隐藏')
const tagsHeight = computed(() => (tagBtnState.value === '更多' ? 'auto' : '30px'))
const tagsRotate = computed(() =>
  tagBtnState.value === '更多' ? 'rotate(0deg)' : 'rotate(180deg)',
)

const allTagsContainerRef = ref<HTMLDivElement>()

async function getAllTags() {
  // layoutTags()
  // const res = await alltags({ tag: 'question', categoryId: props.subjectId })
  // tagsList.value = res
  // dom更新后查看高度，如果超过一行则展示“更多”，并设置为单行高度
  // nextTick().then(layoutTags)
}
function handleTagBtnStateChange() {
  tagBtnState.value = tagBtnState.value === '更多' ? '收起' : '更多'
}
async function layoutTags() {
  // return
}

onMounted(() => {
  // getAllTags()
  // 初始化选中行数据
  rawDefaultSelectedRows.value = JSON.parse(JSON.stringify(props.defaultSelectedRows))
  initSelectedRows()
})

// 表格配置
const proofreadingColumn = {
  title: '校对状态',
  dataIndex: 'proofreading',
  key: 'proofreading',
  width: 120,
  ellipsis: true,
  resizable: true,
  filters: [
    { text: '校对正确', value: 1 },
    { text: '校对错误', value: 2 },
    { text: '未校对', value: 0 },
  ],
}
const actionColumn = {
  title: '操作',
  key: 'action',
  width: 380,
  align: 'center',
  fixed: 'right',
}
const rawColumsData = [
  {
    title: '题型',
    dataIndex: 'type',
    key: 'types',
    width: 100,
    ellipsis: false,
    // filters: [
    //   { text: '单选题', value: 0 },
    //   { text: '多选题', value: 1 },
    //   { text: '判断题', value: 2 },
    //   { text: '填空题', value: 5 },
    //   { text: '问答题', value: 3 },
    //   { text: '算法题', value: 4 },
    //   { text: '排序题', value: 6 },
    // ],
    resizable: true,
  },
  {
    title: '题干',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
    resizable: true,
    width: 500,
  },
  proofreadingColumn,
  {
    title: '科目',
    dataIndex: 'bankPath',
    key: 'bankPath',
    width: 160,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '难度',
    dataIndex: 'difficulty',
    key: 'difficulty',
    width: 160,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '一星/入门', value: 1 },
      { text: '二星/基础', value: 2 },
      { text: '三星/中阶', value: 3 },
      { text: '四星/高阶', value: 4 },
      { text: '五星/专家', value: 5 },
    ],
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    width: 100,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '本人', value: 1 },
      { text: '其他', value: 0 },
    ],
  },
  // actionColumn,
]
const tableColumns = ref<any>([
  {
    title: '题型',
    dataIndex: 'type',
    key: 'types',
    width: 100,
    ellipsis: false,
    resizable: true,
  },
  {
    title: '题干',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
    resizable: true,
    width: 500,
  },
  proofreadingColumn,
  {
    title: '科目',
    dataIndex: 'bankPath',
    key: 'bankPath',
    width: 160,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '难度',
    dataIndex: 'difficulty',
    key: 'difficulty',
    width: 160,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '一星/入门', value: 1 },
      { text: '二星/基础', value: 2 },
      { text: '三星/中阶', value: 3 },
      { text: '四星/高阶', value: 4 },
      { text: '五星/专家', value: 5 },
    ],
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    width: 100,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '本人', value: 1 },
      { text: '其他', value: 0 },
    ],
  },
  // actionColumn,
])
// 如果传入默认题型，则不展示题型过滤下拉框
if (props.defaultType !== null) 
  Reflect.deleteProperty(tableColumns.value[0], 'filters')
// 根据是否官方科目和是否可写控制操作栏显隐
watch([() => props.isOfficialSubject, () => props.writable], ([isOfficialSubject, writable]) => {
  const officialCert = store.getters.userInfo.officialCert
  const hideAction = !officialCert && isOfficialSubject
  const index = tableColumns.value.findIndex((item: any) => item.key === 'action')
  if (writable && !hideAction) {
    // if (index === -1) 
    //   tableColumns.value.push(actionColumn)
  }
  else {
    if (index !== -1) 
      tableColumns.value.splice(index, 1)
  }
  // 官方科目题目不展示校对结果列
  const proofreadIndex = tableColumns.value.findIndex((item: any) => item.key === 'proofreading')
  if ((!isOfficialSubject && !officialCert) || (isOfficialSubject && officialCert)) { // 同或运算
    if (proofreadIndex === -1) {
      const authorIndex = tableColumns.value.findIndex((item: any) => item.title === '题干')
      // tableColumns.value.splice(authorIndex + 1, 0, proofreadingColumn) // 添加到在题干后面
    }
  }
  else {
    if (proofreadIndex !== -1) 
      tableColumns.value.splice(proofreadIndex, 1)
  }
}, { immediate: true })

// 表格数据
const list = ref<any[]>([])
const loading = ref(false)
const route = useRoute()

const generate = ref(false)
let inttervalId: any = null
async function getProgress() {
  if (!route.query.id) 
    return
  const res = await getBankProgress({
    id: route.query.id,
  })
  if (res && !res.status) {
    generate.value = true
    await getList()
  }
  else {
    generate.value = false
    clearInterval(inttervalId)
  }
}
inttervalId = setInterval(() => getProgress(), 1000)

onUnmounted(() => {
  clearInterval(inttervalId)
})

async function getList(receiveParams?: any) {
  const rootPath = route.query.path
  try {
    const bankPath = `${props.subjectPath}${includeSubCatg.value && props.selectItem.children && props.selectItem.children.length ? '*' : ''}` 

    // if(includeSubCatg.value){
    //   bankPath = `,${props.selectItem.path},${bankPath},`
    // }
    const params = {  
      entity: {
        bankPath,
      },
      params: {},
      pageNo: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    } as any
    if (receiveParams) {
      params.entity = Object.assign(params.entity, receiveParams.entity)
      params.params = receiveParams.params
    }
    const res = (await queryQuestion(params)) as any
    list.value = res.records
    paginationConfig.total = res.total
  }
  finally {
    loading.value = false
  }
}

// 清空表格选择项
function clearTableSelection() {
  // 获取当前选中项与默认项的差集，即为本次新增的项目
  const currentIds = selectedRows.value.map(row => row.id)
  const defaultIds = rawDefaultSelectedRows.value.map((row: any) => row.id)
  // console.log(defaultIds, selectedRows.value)
  console.log('defaultIds', defaultIds)

  const newSelectItem = selectedRows.value.filter((item) => {
    console.log('this is item', item)
    return !defaultIds.includes(item.id)
  })
  selectedRows.value = selectedRows.value.filter((item) => {
    return defaultIds.includes(item.id)
  })
  // 触发removeQuestion事件撤销所有新增项目
  newSelectItem.forEach((record) => {
    emits('select', record, false)
  })

  // 恢复为默认回显数据
  // initSelectedRows()
}
// 初始化选中行数据
function initSelectedRows() {
  console.log('this is initSelectRows', props.defaultSelectedRows)
  // 清空现有数据
  selectedRows.value = []
  // 添加默认选中的行，使用 Set 去重
  if (props.defaultSelectedRows && props.defaultSelectedRows.length > 0) {
    const uniqueRows = props.defaultSelectedRows.filter((row, index, arr) =>
      arr.findIndex(item => item.id === row.id) === index,
    )
    selectedRows.value = [...uniqueRows]
  }
}

function updateSelectedRowKeys(record: any) {
  const key = record.id
  const index = selectedRowKeys.value.findIndex(i => i === key)
  if (index === -1) {
    selectedRows.value.push(record)
    emits('select', record, true)
  }
  else {
    selectedRows.value.splice(index, 1)
    emits('select', record, false)
    // emits('removeQuestion', record.id)
  }
}
function onSelectAll(selected: boolean, selectedRows: any, changeRows: any[]) {
  return changeRows.forEach(updateSelectedRowKeys)
}
function getSelectRows() {
  return selectedRows
}

const getType = (type: number) => QuestionEnum[type]

function formatAccuracy(accuracy: any) {
  if (accuracy === null) {
    return '-'
  }
  else {
    return `${Math.floor(accuracy * 100)}%`
  }
}

// 新建题目
const questionAddFormVisible = ref(false)
function createQuestion() {
  questionAddFormVisible.value = true
}
function onAddFormClose(needRefresh?: boolean) {
  // 如果需要刷新，则重新获取标签和列表数据
  if (needRefresh) {
    getList() // 获取题目列表
  }
  
  clearFormState()
  // 隐藏新增题目表单
  questionAddFormVisible.value = false
}

// 查询题目ids关联的（未开始考试）试卷
function getPapersWithQids(ids: string[]) {
  const h = createVNode
  return new Promise((resolve, reject) => {
    quesionDeleteBatch({ ids, confirm: false })
      .then((res: any) => {
        const showFlag = res.results.flatMap((item: any) => item.warnings)

        if (showFlag.length) {
          // 如果有试卷关联，弹出确认框
          Modal.confirm({
            title: '删除题目风险提醒',
            content: h('div', {}, [
              h('p', {}, '删除当前题目后，会影响以下考试：'),
              ...res.results.map((item: any) =>
                h('div', { style: 'margin-top: 6px' }, item.warnings.map((warn: any) => h('p', { style: 'margin-top: 6px' }, warn.title))),
              ),
              h('p', { style: 'margin-top: 6px' }, '确认删除吗？'),
            ]),
            icon: () => h(InfoCircleFilled),
            onOk() {
              resolve(res) // 确认删除
            },
            onCancel() {
              reject() // 取消删除
            },
          })
        }
        else {
          resolve(res) // 没有关联试卷，直接通过
        }
      })
      .catch((error: Error) => {
        reject(error) // 捕获错误
      })
  })
}

// 删除题目
async function deleteQuestion(record: QuestionModel | any) {
  try {
    // await getPapersWithQids([record.id!])
    await quesionDelete({ ids: [record.id] })
    message.success('删除成功!')
    getList()
  }
  catch (error) {
    
  }
}

// 编辑题目
const qid = ref('')
const questionEditFormVisible = ref(false)
function editQuestion(record: any) {
  qid.value = record.id
  questionEditFormVisible.value = true
}
function onEditFormClose(needRefresh?: boolean) {
  if (needRefresh) {
    getList()
  }
  clearFormState()
  questionEditFormVisible.value = false
}

// 题目校对
const questionProofreadFormVisible = ref(false)
function proofreadQuestion(record: any) {
  qid.value = record.id
  questionProofreadFormVisible.value = true
}

// 视频预览
const previewVideoVisible = ref(false)
const previewVideoTaskId = ref('')
const previewItem = ref(null)
function showPreviewVideo(record: any) {
  previewVideoTaskId.value = record.taskId // 假设 record 中有 taskId 字段
  previewItem.value = record
  previewVideoVisible.value = true
}

function onPreviewVideoClose() {
  previewVideoVisible.value = false
  previewItem.value = null
  previewVideoTaskId.value = ''
}

// 监听 defaultSelectedRows 变化
watch(
  () => props.defaultSelectedRows,
  () => {
    initSelectedRows()
  },
  { deep: true, immediate: false },
)

// 监听切换科目
const debounceTimer = ref<any>(null)
watch(
  () => props.subjectPath,
  () => {
    paginationConfig.current = 1
    if (debounceTimer.value)
      clearTimeout(debounceTimer.value)
    debounceTimer.value = setTimeout(async () => {
      tableColumns.value = [...rawColumsData]
      // 使用初始化函数重新设置选中行，避免重复
      initSelectedRows()
      console.log('切换科目后的选中行', selectedRows.value)
      getList()
    }, 200)
  },
)

// 获取上一题/下一题并高亮展示
const currentQIndex = computed(() => list.value.findIndex(item => item.id === qid.value))
const lastBtnDisabled = computed(() => (paginationConfig.current === 1) && (currentQIndex.value === 0))
const nextBtnDisabled = computed(() => (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) && (currentQIndex.value === list.value.length - 1))
async function getSiblingQuestion(direction: 'last' | 'next') {
  if (!qid.value) 
    return
  const len = list.value.length
  const index = currentQIndex.value
  let newQid = qid.value
  let newIndex = index

  if (direction === 'last') {
    if (index !== 0) {
      newIndex = index - 1
    }
    else {
      // 获取上一页最后一条
      if (paginationConfig.current === 1) {
        throw new Error('没有上一题了')
      }
      else {
        paginationConfig.current -= 1
        await getList()
        newIndex = list.value.length - 1
      }
    }
  }
  else {
    if (index !== len - 1) {
      newIndex = index + 1
    }
    else {
      // 获取下一页第一条
      if (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) {
        throw new Error('没有下一题了')
      }
      else {
        paginationConfig.current += 1
        await getList()
        newIndex = 0
      }
    }
  }
  try {
    newQid = list.value[newIndex].id
  }
  catch (error) {
    throw new Error('题目列表为空')
  }
  nextTick(() => highLightRowByIndex(newIndex))
  return newQid
}

// 根据index将某一行高亮
function highLightRowByIndex(index: number) {
  const rows = document.querySelectorAll('.questions-table .ant-table-tbody tr.ant-table-row')
  rows.forEach((ele, i) => {
    if (i === index) {
      ele.classList.add('active')
    }
    else {
      ele.classList.remove('active')
    }
  })
}

// getList()
emitter.on('updateQuestionList', getList) // 其他菜单（回收站、草稿箱、ai批量出题）可能会对当前科目的题目进行更新

// ——————————————————————————————————————————————ai校对——————————————————————————————————————————————————————
const aiProofreadLoading = ref(false)
const taskVisible = ref(false) // 进度条弹层显隐
const taskId = ref('')
const proofreadingTaskRef = ref<InstanceType<typeof ProofreadingTask>>()
const aiProofreadingConfirmVisible = ref(false) // 批量校对确认框显隐控制

// ai校对按钮的点击事件
async function handleAiProofreadingBtnClick(e: Event) {
  if (aiProofreadLoading.value || proofreadingTaskRef.value?.isActive) {
    // 如果AI校对按钮正在转圈则展示信息
    const taskIds = await getaiproofreadingtaskids({})
    if (taskIds.includes(taskId.value) && taskIds[0] === taskId.value) {
      taskVisible.value = true
    }
    else {
      message.warning('排队处理中')
    }
  }
}

// 创建一个ai校对任务
async function handleAiproofreading() {
  try {
    aiProofreadLoading.value = true
    taskId.value = await addAiCheckByQPathApi({
      path: props.selectItem.path, // bab1072e-cf74-4088-bb7e-81cd8a789c88
    })
    console.log('taskId', taskId.value)
    // if (!taskIds.length) 
    taskVisible.value = true // 只有不需要排队立即执行的任务才展示进度条
  }
  catch (error) {
    console.log(error)
  }
  finally {
    aiProofreadLoading.value = false
    aiProofreadingConfirmVisible.value = false
  }
}

// 查看ai批量校对的结果
const taskFinishedModalVisible = ref(false)
const taskFinishDetail = ref<any>()
function showAiProofreadResult(result: any) {
  taskFinishDetail.value = result
  taskFinishedModalVisible.value = true
}

onDeactivated(() => {
  questionAddFormVisible.value = false
  questionEditFormVisible.value = false
  questionProofreadFormVisible.value = false
})

defineExpose({
  selectedRows,
  layoutTags,
  getSelectRows,
  initSelectedRows,
  clearTableSelection,
})
</script>

<template>
  <div class="question-manage">
    <div class="question-manage-header">
      <div class="header-left">
        <a-input-search
          v-model:value.trim="searchContent"
          placeholder="请输入题干"
          allow-clear
          @search="getFirstPageList"
        />
        <a-radio-group
          v-model:value="listType"
          size="large"
          @change="getFirstPageList"
        >
          <a-radio v-for="item in listTypeEnum" :key="item" :value="item">
            <div style="display: flex; align-items: center; white-space: nowrap;">
              <span>{{ item }}</span>
              <span v-if="item !== '全部'" style="padding-left: 2px; line-height: 1.8">
                (<a-badge
                  :count="item === '已选' ? selectedRowKeys.length : defaultExcludedRowKeys.length"
                  :overflow-count="999"
                  :number-style="{
                    backgroundColor: '#fff',
                    color: '#5478EE',
                    padding: '0',
                    fontSize: '14px',
                    display: 'inline-block',
                  }"
                  :show-zero="true"
                />)
              </span>
            </div>
          </a-radio>
        </a-radio-group>
      </div>
    </div>

    <a-table
      ref="table"
      :key="subjectPath"
      class="questions-table common-table"
      sticky
      :columns="tableColumns"
      :row-key="(record: any) => record.id"
      :data-source="list"
      :row-selection="{
        selectedRowKeys,
        onSelect: updateSelectedRowKeys,
        onSelectAll,
        getCheckboxProps: (record: any) => ({ disabled: disabledRowKeys.includes(record.id) || (isOfficialSubject && !store.getters.userInfo.officialCert) }),
      }"
      :loading="loading"
      :scroll="{ x: 1200, y: 'calc(98% - 20px)' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      @resize-column="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'body'">
          <div style="display: flex;align-items: start;">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>{{
                  record.complicatedediting ? record.body?.slice(0, 100) : record.body
                }}</template>
                <span class="question-content" v-html="getQuestionContentByRecord(record)" />
              </a-tooltip>
            </span>
            <span v-if="record.recover && writable" class="recover-tag">已恢复</span>
            <span v-if="defaultExcludedRowKeys.includes(record.id)" class="exclude-tag">已排除</span>
          </div>
        </template>
        <template v-else-if="column.key === 'types'">
          <span style="font-family: PingFang-SC-Regular;">{{ getType(record.type) || '问答题' }}</span>
        </template>
        <template v-else-if="column.key === 'accuracy'">
          <span style="font-family: PingFang-SC-Regular;">
            {{ formatAccuracy(record.accuracy) }}
          </span>
        </template>
        <template v-else-if="column.key === 'bankPath'">
          <span>
            <!-- 去 ‘/’ 分割最后一级 -->
            {{ record.bankPath.split('/').pop() }}
          </span>
        </template>
        <template v-else-if="column.key === 'difficulty'">
          <StarFilled v-for="item in record.difficulty" :key="item" style="color:#FAAD14" />
        </template>
        <template v-else-if="column.key === 'proofreading'">
          <div style="display: flex; align-items: center;">
            <span :class="[['status_gray', 'status_green', 'status_red'][record.check && (record.check.isCheck || record.check.isAi) ? record.check.isCorrect ? 1 : 2 : 0]]" />
            <img v-if="record.check && record.check?.isAi" style="margin-right: 4px;" src="@/assets/icons/svg/ai.svg" alt="">
            <span style="font-family: PingFang-SC-Regular;">{{ ['未校对', '校对正确', '校对错误'][record.check && (record.check.isCheck || record.check.isAi) ? record.check.isCorrect ? 1 : 2 : 0] }}</span>
          </div>
        </template> 
        <template v-else-if="column.key === 'action'">
          <span>
            <a-button type="text" size="small" :disabled="!record.videoUrl" @click="showPreviewVideo(record)">
              <span :style="{ color: record.videoUrl ? '#5478EE' : '#b3b1b1' }">视频预览</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="proofreadQuestion(record)">
              <span style="color:#5478EE">校对</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="editQuestion(record)">
              <span style="color:#5478EE">编辑</span>
            </a-button>
            
            <a-divider type="vertical" />
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <a-popconfirm
                title="确定删除该题目？"
                ok-text="确定"
                cancel-text="取消"
                placement="left"
                :disabled="!store.getters.userInfo.officialCert && record.official_cert"
                @confirm="() => { deleteQuestion(record) }"
              >
                <a-button type="text" size="small">
                  <span style="color:#5478EE">删除</span>
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded-row">
          <QuestionItemDisplay
            :question-detail="record"
            show-score
            show-type
            show-tag
            show-correct-answer
            show-question-difficulty
            show-question-points-and-basis
            show-question-proofreading-record
            option-letter-type="text"
          />
        </div>
      </template>
    </a-table>
  </div>
</template>

<style lang="less">
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.ques-drawer {
  .ant-drawer-title {
    font-weight: bold;
  }
}

.proofread-modal {
  .ant-modal-header {
    padding: 24px;
    padding-top: 0;
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-close{
    .ant-modal-close-x{
        line-height: 24px;
    }
  }
}
</style>

<style lang="less" scoped>
.question-manage {
  height: 100%;
  overflow: auto;
  padding: 0 10px 0 16px;
  display: flex;
  flex-direction: column;
}
.question-manage-header {
  margin: 0px 0;
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;

    :deep(.ant-checkbox-wrapper) {
      margin-left: 16px;
      span {
        font-size: 14px;
        white-space: nowrap;
      }
    }

    .ant-radio-group {
      margin-left: 24px;
      display: flex;
      align-items: center;

      :deep(span) {
        font-size: 14px;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    .ant-btn {
      border-radius: 8px;
      margin-left: 8px;
      font-size: 14px;
    }
  }
}

.recover-tag {
  font-size: 12px !important;
  color: #2f8c00 !important;
  background-color: #f3f7f1;
  border-radius: 4px;
  padding: 2px 4px;
}

.exclude-tag {
  font-size: 12px;
  color: #d71310 !important;
  background-color: #fcf0f0;
  border-radius: 4px;
  padding: 2px 4px;
}

.all-tags-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding: 0 100px 0 0;
  height: v-bind(tagsHeight);
  overflow: hidden;

  .more-btn {
    cursor: pointer;
    position: absolute;
    top: 3px;
    right: 16px;
    color: #626262;
    font-size: 12px;

    .arrow-icon {
      transform: v-bind(tagsRotate);
    }
  }

  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  }

  .active {
    background: #5478ee;
    color: #fff;
  }
}

.table-bar {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-checkbox-wrapper {
    font-size: 15px;
  }

  .show-columns {
    font-size: 15px;

    span {
      margin-right: 10px;
    }
  }
}

:deep(.ant-table) {
  .ant-table-expanded-row td:nth-child(1) {
    box-shadow: 2px 0px 0px 0px #5478ee inset;
  }
}

.expanded-row {
  padding: 16px;
}
.tooltip-style {
  display: block;
  // width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left !important;
  margin-right: 8px;
}

:deep(.question-content) {
  font-family: PingFang-SC-Regular !important;
  img {
    height: 50px;
    width: auto;
    cursor: initial!important;
    display: inline-block!important;
    float: none!important;
  }
  p {
    display: inline-block;
  }
}
.full-screen-modal{
  .ant-modal-body{
    .create-question-container
    :deep(.close-btn){
      line-height: 38px;
    }
  }
}
.preview-video-modal{
  :deep(.ant-modal-body){
    // background: red;
  }
  :deeo(.ant-modal-content){
    padding: 0;
  }
}
</style>

<style lang="less">
.preview-video-modal{
  .ant-modal-body {
    background: #E9F4FB
  }
  .ant-modal-header{
    background: #E9F4FB;
    margin-bottom: 0;
    padding: 24px;
  }
  .ant-modal-content{
    padding: 0;
  }
  .ant-modal-close-x {
    height: '';
    width: 22px!important;
    line-height: 22px!important;
  }
}
</style>
