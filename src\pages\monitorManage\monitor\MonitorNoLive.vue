<template>
    <div class="monitor-no-live">
        <a-spin wrapperClassName="common-full-spin" :spinning="loading">
            <div v-if="filteredList.length" class="stu-monitor-list">
                <template v-for="item in filteredList" :key="item.id">
                    <div class="item" @click="getStuInfo(item)">
                        <svg-icon v-if="item.status === -2" name="wait_status" width="100%" height="100%" />
                        <svg-icon v-if="item.status === -1" name="err_status" width="100%" height="100%" />
                        <svg-icon v-if="item.status === 0" name="absent_status" width="100%" height="100%" />
                        <svg-icon v-if="item.status === 1" name="ing_status" width="100%" height="100%" />
                        <svg-icon v-if="item.status === 2" name="finish_status" width="100%" height="100%" />
                        <span class="name" :title="item.username">{{ item.username }}</span>
                    </div>
                </template>
            </div>
            <div v-else class="common-no-data" style="background-size: 289px;"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { ref, watch } from 'vue'
import { useIntervalFn } from '@vueuse/core'
import { candstatus } from '@/api/admin/paperManage'
import { useDelayIntervalFn } from '@/hooks/useDelayIntervalFn';

const props = withDefaults(defineProps<{
    /** 更新频率多少秒一次 */
    updateFrequency?: number
    /** 给过滤条件 */
    filter: {
        username: string
        /** 多场考试 , 分隔 */
        paperIds: string
    }
}>(), {
    updateFrequency: 30
})

// 获取列表
const list = ref<any>([])
const loading = ref(false)
const showLoading = ref(true)
async function getStuList() {
    if (!props.filter.paperIds) return list.value = []
    if (showLoading.value) {
        loading.value = true
        showLoading.value = false
    }
    try {
        let res = await candstatus({ templeteIdList: props.filter.paperIds.split(',') }) as any
        if (!props.filter.paperIds) {
            return list.value = []
        } else {
            // list.value = res.cand_status
            list.value = res
        }
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}

// const { pause, resume, isActive } = useIntervalFn(getStuList, 1000 * props.updateFrequency, { immediateCallback: true })
const { pause, resume, isActive } = useDelayIntervalFn(getStuList, 1000 * props.updateFrequency, { immediateCallback: true })


// 过滤条件以及原数组变更都需要刷新列表
const filteredList = ref<any[]>([])
function refreshList() {
    filteredList.value = list.value.filter((i: any) => i.username.includes(props.filter.username) && props.filter.paperIds.includes(i.paper))
}
watch([() => props.filter.username, list], refreshList)
watch(() => props.filter.paperIds, () => {
    showLoading.value = true
    resume()
})

// 查看学生信息
const router = useRouter()
const getStuInfo = ( item: any) => {
    const { stupid, paper, paper_name, computermonitor, phonemonitor } = item
    router.push({
        name: 'stuInfo',
        query: {
            stu: stupid,
            name: paper_name,
            paper,
            computermonitor,
            phonemonitor,
            status: item.status
        }
    })
}


</script>

<style lang="less" scoped>
.monitor-no-live {
    flex: 1;
    min-height: 0;
    overflow: auto;
}

.stu-monitor-list {
    height: calc(100% - 100px);
    overflow: auto;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(5, 1fr);
    grid-column-gap: 0px;
    grid-row-gap: 8px;

    .item {
        position: relative;
        // width: 12.5%;
        height: 86px;
        margin-bottom: 40px;
        cursor: pointer;

        .status {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            line-height: 30px;
            text-align: center;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.85);

            .status-icon {
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 4px;
            }

            .online {
                background: #52c41a;
            }

            .offline {
                background: #d9d9d9;
            }
        }

        .name {
            position: absolute;
            // top: 0;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            // width: 100%;
            max-width: 100px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            line-height: 86px;
            text-align: center;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}

.nodatawrapper {
    width: 100%;
    height: calc(100% - 100px);
    display: flex;
    align-items: center;
    justify-content: center;

    .nodata {
        width: 372px;
        height: 178px;
    }
}

.loading-wrapper {
    top: 58px;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>