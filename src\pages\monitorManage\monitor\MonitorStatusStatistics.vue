<template>
    <div class="stu-monitor-statistic">
        <div>
            应考人数: <span>{{ statusStatistics.all }}</span>人
        </div>
        <div>
            实考人数: <span>{{ statusStatistics.real }}</span>人
        </div>
        <div>
            已交卷: <span>{{ statusStatistics.complete }}</span>人
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { candnumberstatus } from '@/api/admin/paperManage'
import { useRoute } from 'vue-router'
import { useIntervalFn } from '@vueuse/core'
import { useState } from '@/hooks'

const props = withDefaults(defineProps<{
    /** 多场考试 , 分隔 */
    paperIds: string | null
    /** 更新频率多少秒一次 */
    updateFrequency?: number
}>(), {
    updateFrequency: 30
})

const route = useRoute()

// 获取监考人数状态
const [statusStatistics, reset] = useState({
    all: 0,
    real: 0,
    complete: 0
})
async function getStatusStatistics() {
    try {
        if (!props.paperIds) return reset()
        let res = await candnumberstatus({ templeteIdList: props.paperIds.split(',') }) as any
        if (!props.paperIds) return reset()
        Object.keys(statusStatistics.value).forEach(key => {
            statusStatistics.value[key] = res.reduce((pre: number, cur: any): any => {
                return pre + cur[key]
            }, 0)
        })
    } catch (error) {
        console.log(error)
    }
}

const { pause, resume, isActive } = useIntervalFn(getStatusStatistics, 1000 * props.updateFrequency, { immediateCallback: true })

watch(() => props.paperIds, resume)

</script>

<style lang="less" scoped>
.stu-monitor-statistic {
    display: flex;

    div {
        margin-right: 20px;

        span {
            color: #5478ee;
        }
    }
}
</style>