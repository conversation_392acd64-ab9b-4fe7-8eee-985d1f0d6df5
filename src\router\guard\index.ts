import type { Router } from 'vue-router'
import store from '@/store/index'
import { close, start } from '@/utils/nprogress'
import { Modal } from 'ant-design-vue'

const whiteList = ['/login', '/register', '/admin-login', '/modify', '/exam/monitor', '/loseEfficacy']

/**
 * 路由守卫函数
 * @param router - 路由实例
 */
export function createRouterGuard(router: Router) {
  router.beforeEach(async (to, _from, next) => {
    // 销毁所有弹框(Modal.info、Modal.success、Modal.error、Modal.warning、Modal.confirm)
    Modal.destroyAll()

    start() // 开始进度条 // start progress bar

    // 处理路径重定向 - 如果访问的是需要admin前缀的路径，自动添加前缀
    const adminPaths = [
      '/writtenQuestionManage',
      '/subjectManage',
      '/examManage',
      '/interview',
      '/positionManagement',
    ]

    const needsAdminPrefix = adminPaths.some(path => to.path.startsWith(path))
    if (needsAdminPrefix && !to.path.startsWith('/admin/')) {
      const newPath = `/admin${to.path}`
      return next({ path: newPath, query: to.query, replace: true })
    }
    if (to.query.token) {
      store.commit('SET_ACCESS_TOKEN', to.query.token)
      store.commit('SET_APP_CONFIG_WITH_OBJ', { isEditQuestionResetProofreadConfirm: false, isEditInterviewQuestionResetProofreadConfirm: false })
      try {
        const { roles } = await store.dispatch('GET_USER_INFO')
        // 通过角色生成可访问路由
        const filterRoutes = await store.dispatch('GENERATE_ROUTES', roles)
        // 动态添加可访问路由表
        filterRoutes.forEach((item: any) => {
          router.addRoute(item)
        })
        Reflect.deleteProperty(to, 'query')
        next({ ...to, replace: true })
      }
      catch {
        store.commit('CLEAR_TOKEN')
        next('/login')
      }
    }
    else if (store.getters.accessToken) {
      try {
        if (to.path === '/login') {
          const { roles } = await store.dispatch('GET_USER_INFO')
          const filterRoutes = await store.dispatch('GENERATE_ROUTES', roles)
          // 动态添加可访问路由表
          filterRoutes.forEach((item: any) => {
            router.addRoute(item)
          })
          next('/admin')
        }
        else {
          // 判断用户资料是否获取
          const hasRoles = store.state.roles && store.state.roles.length > 0
          if (!hasRoles) {
            // 获取用户信息
            const { roles } = await store.dispatch('GET_USER_INFO')
            // 通过角色生成可访问路由
            const filterRoutes = await store.dispatch('GENERATE_ROUTES', roles)
            // 动态添加可访问路由表
            filterRoutes.forEach((item: any) => {
              router.addRoute(item)
            })
            // 添加完动态路由之后，需要重新导航到目标路由
            return next({ ...to, replace: true })
          }
          return next()
        }
      }
      catch (error) {
        console.error('路由守卫错误:', error)
        // 如果获取用户信息失败，清除token并跳转到登录页
        store.commit('CLEAR_TOKEN')
        next('/login')
      }
    }
    else {
      // 没有 token 的情况下，可以进入白名单
      const flag = whiteList.some(record => to.path.includes(record))
      if (flag) {
        next()
      }
      else {
        next('/login')
      }
    }
  })
  
  router.afterEach(() => {
    const bodySrcollTop = document.body.scrollTop
    if (bodySrcollTop !== 0) {
      document.body.scrollTop = 0
      return
    }
    const docSrcollTop = document.documentElement.scrollTop
    if (docSrcollTop !== 0) {
      document.documentElement.scrollTop = 0
    }
    close() // finish progress bar
  })
}
