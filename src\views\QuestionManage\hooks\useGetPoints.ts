export default (formState: any, scorepts: any, scoreInputTotalRef: any) => {
  // 添加得分点
  const addPoints = () => {
    scorepts.value.push({ keyword: [], score: 0 })
  }

  // 删除问答题得分点
  const delPoints = (index: number) => {
    scorepts.value.splice(index, 1)
    if (formState.value.sepscore) {
      let totalScore = 0
      scorepts.value.forEach((item: any) => {
        totalScore += Number(item.score)
      })
      scoreInputTotalRef.value.score = totalScore
    }
  }
  return {
    addPoints,
    delPoints
  }
}
