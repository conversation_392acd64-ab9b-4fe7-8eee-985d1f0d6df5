<script setup lang='ts'>
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  examDetail: {
    type: Object,
    default: () => {
      return {}
    },
  },
})
const route = useRoute()
const router = useRouter()
const statusText = ref('')
function previewResume() {
  router.push({ name: 'resumeDetail', query: { id: route.query.resumeId } })
}
const colorMap = {
  0: '#FAAD14',
  1: '#FAAD14',
  2: '#FAAD14',
  3: '#ff4d4f',
  4: '#b1b1b1',
}
const statusTextMap = {
  0: '待安排',
  1: '待面试',
  2: '待面试',
  3: '面试中',
  4: '已结束',
}

const linearGradientMap = {
  0: 'linear-gradient(180deg,rgba(250,173,20,0.09), rgba(250,173,20,0.00) 98%);',
  1: 'linear-gradient(180deg,rgba(250,173,20,0.09), rgba(250,173,20,0.00) 98%);',
  2: 'linear-gradient(180deg,rgba(250,173,20,0.09), rgba(250,173,20,0.00) 98%);',
  3: 'linear-gradient(180deg,rgba(255,77,79,0.09), rgba(255,77,79,0.00) 98%);',
  4: 'linear-gradient(180deg,rgba(177,177,177,0.09), rgba(177,177,177,0.00) 98%);',
}

const messageMap = {
  0: '面试已安排，等待候选人参加',
  1: '面试已安排，等待候选人参加',
  2: '面试已安排，等待候选人参加',
  3: '候选人正在面试，结束后可查看回放',
  4: '候选人已完成面试',
}

const state = computed(() => {
  // 安排面试：createTime
  // 发送通知：emailSentTime
  // 参加面试：joinTime
  // 面试回放：replayTime
  const flag = props.examDetail.replayUrl ? 4 : route.query.interviewStatus === '已结束' ? 4 : props.examDetail.joinTime ? 3 : route.query.interviewStatus === '面试中' ? 3 : props.examDetail.emailSentTime ? 2 : props.examDetail.createTime ? 1 : 0
  return flag
})

const bgColor = computed(() => {
  return linearGradientMap[state.value]
})
</script>

<template>
  <div class="p-[20px]">
    <div class="shadow w-full h-[158px] bg-[#fff] rounded-[8px] overflow-hidden">
      <div class="h-[59px] bg-linear px-[24px] py-[19px]" :style="`border-top: 4px solid ${colorMap[state]}; background: ${bgColor};`">
        <div class="flex flex-nowrap items-center background"> 
          <div class="font-bold" :class="`text-[${colorMap[state]}]`">
            {{ route.query.interviewStatus || statusTextMap[state] }}
          </div>
          <a-divider type="vertical" />
          <div>
            {{ messageMap[state] }}
          </div>
        </div>
        <a-divider />
      </div>
      <div class="px-[24px] py-[19px] flex justify-between items-center">
        <div>
          <div class="flex flex- flex-nowrap items-end mb-[12px]">
            <div class="text-[24px] font-bold mr-[16px] ">
              {{ route.query.candidateName }}
            </div>
            <a-tag color="orange">
              {{ route.query.gender }}
            </a-tag>
            <a-tag color="orange">
              {{ route.query.age }}岁
            </a-tag>
          </div>
          <div>
            {{ route.query.phone }}
            <a-divider type="vertical" />
            {{ route.query.email }}
          </div>
        </div>
        <div>
          <a-button type="primary" @click.stop="previewResume">
            查看简历
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
