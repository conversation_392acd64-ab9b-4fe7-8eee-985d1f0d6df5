server {
    listen 80;
    server_name interview-dev.exam.isrc.ac.cn; # 替换为您的域名或IP地址

    # 对应 /api/q/ 的代理
    location /api/q/ {
        proxy_pass http://**********:8080/jeecg-boot/q/; # Vite rewrite 规则已集成到这里
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 对应 /api/position/ 的代理
    location /api/position/ {
        proxy_pass http://**********:8080/jeecg-boot/position/; # Vite rewrite 规则已集成到这里
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 对应 /api/resume/ 的代理
    location /api/resume/ {
        proxy_pass http://**********:8080/jeecg-boot/resume/; # Vite rewrite 规则已集成到这里
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 对应 /api/exam/ 的代理
    location /api/exam/ {
        proxy_pass http://**********:8080/jeecg-boot/exam/; # Vite rewrite 规则已集成到这里
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 对应 /api 的代理
    # 注意：这个规则 `/api` 会匹配所有以 `/api` 开头的路径，包括上面的 `/api/q/` 等。
    # 在 Nginx 中，更具体的 location 匹配会优先。因此，如果 `/api/q/` 等规则放在 `/api` 之前，它们会先被匹配。
    # 这里的 rewrite 规则是移除 `/api` 前缀。
    location /api {
        # target: 'https://teacher-dev.exam.isrc.ac.cn/api/v1/portal',
        # 注意：Vite 的 rewrite(path => path.replace(/^/api/, '')) 意味着 /api/some/path 会被转发到 target/some/path
        # Nginx 的 proxy_pass 如果URI后面没有斜杠，会将匹配的location部分替换掉。
        # 例如，location /api {} proxy_pass https://teacher-dev.exam.isrc.ac.cn/api/v1/portal;
        # 请求 /api/users 会被转发到 https://teacher-dev.exam.isrc.ac.cn/api/v1/portal/users
        # 这与 Vite 的 rewrite 效果一致。
        proxy_pass https://teacher-dev.exam.isrc.ac.cn/api/v1/portal;
        proxy_set_header Host teacher-dev.exam.isrc.ac.cn; # 设置目标服务器的Host头
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # 如果目标服务器需要，可能还需要设置 Origin 或 Referer 头
        # proxy_set_header Origin $http_origin;
        # proxy_set_header Referer $http_referer;
    }

    # 如果您的前端应用是单页应用，通常还需要一个 location 来处理前端路由
    location / {
        root /usr/share/nginx/html; # 前端文件存放的目录
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 其他可能的配置，例如错误页面等
    # error_page 500 502 503 504 /50x.html;
    # location = /50x.html {
    #     root /usr/share/nginx/html;
    # }
}