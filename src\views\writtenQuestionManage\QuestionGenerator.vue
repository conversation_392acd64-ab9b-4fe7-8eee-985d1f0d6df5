<template>
    <div class="question-generator">
        <div class="common-page-title">
            <span>AI批量出题</span>
        </div>
        <div class="main">
            <div class="main-left" >
                <a-alert message="您输入的材料会给到大模型用作训练，请谨慎使用以避免重要数据泄漏。" type="info" show-icon
                    style="background-color: #F1F4FE;border-radius: 4px;border: none;" />
                <div class="text-area-head">
                    <span>输入材料：</span>
                    <a-button size="small" :disabled="generateLoading" style="border-radius: 4px;font-size: 12px;" @click="describe = ''">
                    <img src="@\assets\icons\svg\clear.svg" style="height: 10px; padding-right: 5px; margin-bottom: 2px;"/>
                        清空文本</a-button>
                </div>
                <a-textarea :bordered="false" v-model:value="describe" :disabled="generateLoading" :autoSize="false" show-count
                    placeholder="请输入学科/领域/知识点等关键词描述或者一段文字材料；描述越详细，生成的试题越能符合要求。" :maxlength="10000" />
                <div class="template-wrap">
                    <div class="title">生成示例：</div>
                    <div class="template-list">
                        <div class="template-item" v-for="item in templateTextList" :key="item" @click="!generateLoading && (describe = item)">
                        <div class="text-wrap">{{ item }}</div>
                        </div>
                    </div>
                </div>
                <ul class="type-wrapper">
                    <li class="type-wrapper-item" v-for="item in typeList">
                        <div class="type-wrapper-item__label">
                            <svg-icon :name="item.icon" style="position: relative; top: 2px; font-size: 16px;" />
                            {{ item.label }}
                        </div>
                        <div class="type-wrapper-item__value">
                            <a-input-number v-model:value="item.value" :disabled="generateLoading" :min="0" :max="5" />
                        </div>
                    </li>
                </ul>
                <div class="tips">
                    <p>1.每次最多出10道题，单个题型不能超过5题。</p>
                    <p>2.受限于AI模型的能力，请自行确认AI出题结果是否存在谬误。偶有题目数据不完整的情况，您可重新生成题目。</p>
                </div>
                <div style="margin-top: 16px;">
                    <a-button class="common-ai-button" :loading="generateLoading" style="border-radius: 8px;" @click="handleGenerate">
                        <template #icon><img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;" /></template>
                        {{ generateLoading ? '正在生成' : '开始生成' }}
                    </a-button>
                </div>
            </div>
            <div class="main-right">
                <div class="top-bar">
                    <div>
                        <span class="label">所属题库</span>
                        <a-tree-select show-search :tree-data="categoryList" placeholder="请选择题库" v-model:value="categoryId"
                            v-model:treeExpandedKeys="expandedKeys"
                            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" allow-clear
                            :field-names="{ key: 'id', value: 'id', label: 'name' }" treeNodeFilterProp="name"
                            tree-default-expand-all :virtual="false" :getPopupContainer="(triggerNode: HTMLElement) => {
                                return triggerNode.parentNode
                            }">
                        </a-tree-select>
                    </div>
                    <a-button :class="{'ai-btn': !batchImportDisabled }" :disabled="batchImportDisabled" :loading="exportLoading" @click="handleBatchImport">导入题库</a-button>
                </div>
                <div class="no-data-wrapper" v-if="!list.length">
                    <img src="@/assets/images/nodata2.png" alt="">
                    <a-carousel style="margin-top: 16px; user-select: none;" autoplay effect="fade" :dots="false">
                        <div v-for="item in tips">
                            <h3 style="text-align: center;color: rgba(0,0,0,0.45);">{{ item }}</h3>
                        </div>
                    </a-carousel>
                </div>
                <div v-else class="question-wrapper">
                    <div class="question-item" v-for="(item, index) in list">
                        <Vue3Lottie v-if="item.loading" class="generating-animation" :animationData="generating" height="auto" :auto-play="true" />
                        <QuestionForm v-else-if="item.editing" :form-state="item" @confirm="val => handleEditConfirm(val, index)" @cancel="handleEditCancel(index)"></QuestionForm>
                        <QuestionItemDisplay v-else :questionDetail="item" :show-type="true" :show-score="true" :show-correct-answer="true"
                            :show-question-points-and-basis="true" :deleted="item.deleted">
                            <template #right-top-icon>
                                <div class="icon-btns">
                                    <img class="icon-btn" src="@/assets/icons/svg/edit.svg" alt="" @click="item.editing = true">
                                    <img class="icon-btn" v-if="!item.deleted" src="@/assets/images/svg/delete_black.svg" alt="" @click="item.deleted = true">
                                    <span class="icon-btn" v-else style="color: #5478EE;" @click="item.deleted = false">恢复</span>
                                </div>
                            </template>
                        </QuestionItemDisplay>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { subjcatgs } from '@/api/admin/questionManage';
import { QuestionEnum } from '@/models/questionModel';
import { aiGenerateQuestion } from '@/api/exam/index'
import { reactive, ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import QuestionItemDisplay from './QuestionItemDisplay/index.vue'
import { addQuestion } from '@/api/admin/questionManage'
import { Vue3Lottie } from 'vue3-lottie';
import generating from '@/assets/animation/generating.json'
import SvgIcon from '@/components/SvgIcon.vue';
import { findItemInTree, insetParenthesis } from '@/utils';
import { useRoute } from 'vue-router'
import QuestionForm from './QuestionForm/index.vue'
import { checkQuestionForm } from '@/utils/validate';
import _ from 'lodash';
import emitter from '@/utils/bus';

const route = useRoute()

const expandedKeys = ref<string[]>([])
const categoryId = ref<string>()
const categoryList = ref<any[]>([])
async function getCategoryList() {
    try {
        let res = await subjcatgs({ action: 'query' })
        res.forEach((item) =>{
            if(item.name == '部门题库'){
                categoryList.value = item.children
            }
        }) 
        // expandedKeys.value = categoryList.value.map(i => i.id)

        // 判断传入的题库id是否存在（比如官方题库和部门题库根节点就不存在），如果存在就回显
        const currentCategoryId = route.query.categoryId as string
        const item = findItemInTree(currentCategoryId, categoryList.value)
        if (item) categoryId.value = currentCategoryId

    } catch (error) {
        console.log(error)
    }
}
getCategoryList()


const typeList = reactive([
    { label: '单选', type: QuestionEnum['单选题'], value: 0, icon: 'icon_tixing_danxuan' },
    { label: '多选', type: QuestionEnum['多选题'], value: 0, icon: 'icon_tixing_duoxuan' },
    { label: '判断', type: QuestionEnum['判断题'], value: 0, icon: 'icon_tixing_panduan' },
    { label: '填空', type: QuestionEnum['填空题'], value: 0, icon: 'icon_tixing_tiankong' },
    { label: '问答', type: QuestionEnum['问答题'], value: 0, icon: 'icon_tixing_wenda' },
])

const templateTextList = [ // 示例内容
  '请生成与操作系统中进程管理有关的试题',
  '请根据以下文字材料生成试题：进程（Process）是计算机中运行中程序的实例。每个进程都有自己的内存空间、系统资源和执行状态。进程是程序的执行过程，它包括代码、数据和正在执行的程序状态。在操作系统中，每个进程都被视为独立的实体，它们之间相互隔离，无法直接访问对方的内存空间。线程（Thread）是进程中的执行单元，一个进程可以包含多个线程。线程共享相同的内存空间和系统资源，因此线程之间可以更方便地共享数据和通信。由于线程可以并发执行，因此它们能够更加高效地利用多核处理器和提升系统性能。然而，线程之间的共享也增加了并发编程的复杂性，需要额外的注意来避免数据竞争和同步问题。',
]

const tips = [
    '提供的材料会给到大模型训练，请谨慎使用',
    'AI返回结果仅支持文字，无法返回图片和公式等',
    '暂不支持生成算法题和排序题',
    '为保证生成质量和效率，一次最多返回10道试题',
    '受限于AI模型能力，请自行确认结果正误',
    '若生成结果不达预期，可多次生成',
]

const describe = ref('')

const list = ref<any[]>([])
const generateLoading = computed(() => list.value.some(i => i.loading))
async function handleGenerate() {
    if (!describe.value) return message.warning('很抱歉，目前缺少题目描述信息，无法为您生成试题')
    const total = typeList.reduce((acc, cur) => acc + cur.value, 0)
    if (!total) return message.warning('题型数量不可为0')
    if (total > 10) return message.warning('题目数量总和不能超过10')
    // 加载中
    list.value = new Array(total).fill({ loading: true })

    typeList.filter(i => i.value).forEach(async i => {
        try {
            let res = await aiGenerateQuestion({
                describe: describe.value,
                limit: i.value,
                type: i.type
            })
            res.forEach((item: any) => {
                item.deleted = false
                item.loading = false
            })

            // 从某位选取相应数量loading状态的骨架卡片替换成生成好的题目卡片
            list.value.splice(-i.value, i.value, ...res)

            // 排序（生成好的题目在前，且按照生成顺序，loading状态在最后）
            list.value.sort((a, b) => {
                let defaultOrder = typeList.map(i => i.type)
                if (!a.loading && b.loading) {
                    return -1
                } else if (a.loading && !b.loading) {
                    return 1
                } else if (!a.loading && !b.loading) {
                    // 如果都加载好了就按照原来题目顺序排序
                    return defaultOrder.indexOf(a.type) - defaultOrder.indexOf(b.type)
                } else {
                    return 0
                }
            })
        } catch (error) {
            // 如果失败则从末尾删除响应数量的loading骨架卡片
            list.value.splice(-i.value, i.value)
            message.error(`生成${i.value}道${QuestionEnum[i.type]}失败`)
        }
    })
}

// 清空面板
function reset() {
    list.value = []
    typeList.forEach(i => i.value = 0)
}


// 处理批量导入
const exportLoading = ref(false)
const batchImportDisabled = computed(() => {
    if (list.value.length === 0) return true
    // 所有的都被删了或者有一个没加载完
    return list.value.every(i => i.deleted) || list.value.some(i => i.loading)
})
async function handleBatchImport() {
    if (!categoryId.value) return message.warning('请选择要导入的题库')
    if (list.value.some(i => i.editing)) return message.warning('导入失败，请先保存题目')
    let params = list.value.filter(i => !i.deleted)

    params = _.cloneDeep(params) // 深拷贝，防止修改原数据导致视图报错

    if (!params.length) return message.warning('当前无题目，导入失败')

    // 对所有题目进行校验
    try {
        let results = await Promise.allSettled(params.map(form => {
            let cloneForm = JSON.parse(JSON.stringify(form))
            // 如果是填空或者问答题，需要将答案转成数组
            if ([QuestionEnum['填空题'], QuestionEnum['问答题']].includes(form.type)) {
                if (!Array.isArray(cloneForm.answer))
                    cloneForm.answer = JSON.parse(cloneForm.answer)
            }
            return checkQuestionForm(cloneForm)
        }))
        results.forEach((item, index) => {
            if (item.status === 'rejected') {
                throw new Error(`第${index + 1}题：${item.reason}`)
            }
        })
    } catch (error: any) {
        console.log(error)
        return message.error(error.message)
    }

    try {
        exportLoading.value = true
        params.forEach(item => {
            insetParenthesis(item) // 相关题型题干插入括号
            item.categoryId = categoryId.value // 关联题库
            if ([QuestionEnum['填空题'], QuestionEnum['问答题']].includes(item.type)) {
                if (Array.isArray(item.answer)) {
                    item.answer.forEach?.((item: any) => {
                        // 删除id
                        Reflect.deleteProperty(item, 'id')
                    })
                    item.answer = JSON.stringify(item.answer)
                }
            }
            if (item.type === QuestionEnum['多选题']) {
                if (Array.isArray(item.answer)) {
                    item.answer = item.answer.sort().join('')
                }
            }
            if(item.options){
                item.options = JSON.stringify(item.options)
            }
            Reflect.deleteProperty(item, 'deleted')
            Reflect.deleteProperty(item, 'loading')
            Reflect.deleteProperty(item, 'editing')
        })

        await addQuestion(params)
        reset()
        message.success('导入成功')
        // 更新题目列表
        emitter.emit('updateQuestionList')
    } catch (error) {
        console.log(error)
    } finally {
        exportLoading.value = false
    }
}

// 编辑题目
function handleEditConfirm(formState: any, index: number) {
    list.value[index] = formState
    list.value[index].editing = false
}

function handleEditCancel(index: number) {
    list.value[index].editing = false
}

</script>

<style lang="less" scoped>
.question-generator {
    padding: 0 20px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: auto;

    .top-bar {
        background-color: #fff;
        height: 80px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        margin-bottom: 20px;
        margin-right: 16px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
        .label {
            font-size: 12px;
            color: #626262;
        }

        :deep(.ant-select) {
            width: 200px;
            margin-left: 16px;

            .ant-select-selector {
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-radius: 8px;
            }
        }

        .ai-btn {
            width: 88px;
            height: 32px;
            background: linear-gradient(321deg, #5dedf7 0%, #5491ff 24%, #835aff 47%, #8b54ff 60%, #faccf1 100%);
            border: none;
            border-radius: 8px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 100;
            cursor: pointer;
            user-select: none;
        }
    }

    .main {
        flex: 1;
        min-height: 0;
        // margin-top: 20px;
        display: flex;
        min-width: 980px;

        .main-left {
            flex: 1;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px 24px 24px 24px;
            display: flex;
            flex-direction: column;
            box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);

            .text-area-head {
                height: 40px;
                border-bottom: 1px solid #e8e8e8;
                border-radius: 8px 8px 0 0;
                background-color: #f5f5f5;
                margin-top: 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 8px;
            }

            :deep(.ant-input-textarea) {
                flex: 1;
                min-height: 0;
                .ant-input-affix-wrapper-textarea-with-clear-btn {
                    height: 100%;
                }
                .ant-input {
                    border-radius: 0 0 8px 8px;
                    background-color: #f5f5f5;
                    height: calc(100% - 24px);
                    resize: none;
                }
            }

            .type-wrapper {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-column-gap: 8px;
                grid-row-gap: 8px;

                .type-wrapper-item {
                    display: flex;
                    align-items: center;

                    .type-wrapper-item__label {
                        width: 60px;
                        color: rgba(0, 0, 0, 0.85);

                        img {
                            vertical-align: sub;
                        }
                    }

                    .type-wrapper-item__value {
                        width: 80px;

                        .ant-input-number {
                            border-radius: 8px;
                        }
                    }
                }
            }

            .tips {
                margin-top: 16px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 12px;
            }
        }

        .main-right {
            margin-left: 20px;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;

            .no-data-wrapper {
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .ant-carousel {
                    width: 100%;
                    position: relative;
                    top: -50px;
                }
            }
            
            .question-wrapper {
                flex: 1;
                min-height: 0;
                overflow: scroll;
                padding-right: 10px;
            }

            :deep(.question-item) {
                background-color: #fff;
                box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
                border-radius: 8px;
                transition: all ease 0.5s;

                &:hover {
                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
                }

                &:not(:first-child) {
                    margin-top: 20px;
                }

                .test-question-display {
                    padding: 20px;

                    &:hover {
                        .icon-btns {
                            display: block;
                            opacity: 1;
                        }
                    }

                    .icon-btns {
                        transition: all ease .2s;
                        opacity: 0;
                        .icon-btn {
                            cursor: pointer;
                            margin-left: 16px;
                        }
                    }
                }

                .generating-animation {
                    position: relative;
                    &::after {
                        position: absolute;
                        left: calc(50% - 30px);
                        bottom: 10%;
                        content: '正在生成';
                        color: #2F8C00;
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
.template-wrap {
  margin-bottom: 32px;
  .title {
    font-size: 14px;
    color: rgba(0,0,0,0.85);
    line-height: 20px;
  }
  .template-item {
    margin-top: 8px;
    padding: 8px;
    border-radius: 8px;
    // border: 1px dashed #ccc;
    background: #F8F9FA;
    cursor: pointer;
    &:hover {
      background: #F1F4FE;
    }
    .text-wrap {
      max-height: 36px;
      font-size: 12px;
      color: rgba(1,1,1,0.85);
      line-height: 18px;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      white-space: normal !important;
      -webkit-line-clamp: 1;//超出2行显示省略号，可修改数字
      -webkit-box-orient: vertical;
    }
  }
}

</style>