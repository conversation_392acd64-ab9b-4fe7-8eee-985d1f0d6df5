<!-- 考试成绩分析table -->
<template>
  <a-table
    class="common-table"
    sticky
    :columns="columns"
    :row-key="(record: any) => record.id"
    :data-source="list"
    @change="handleTableChange"
    :scroll="{ x: 800 }"
    :loading="loading"
    :pagination="{
      current: page,
      pageSize: pageSize,
      showSizeChanger: true,
      showTotal: (total: number) => '总条数：' + total,
      total: listTotal,
      pageSizeOptions: ['10', '20', '50', '100'],
      size: 'small'
    }"
    @resizeColumn="(w: any, col: any) => col.width = w">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        <span :class="['name-link', record.status !== PaperStatus.OVER ? 'disabled' : '']" @click="analyze(record)">{{ record.name }}</span>
      </template>
      <template v-if="column.key === 'join_rate'">
        {{ !_.isNil(record.actualCount) ? record.actualCount : '--' }} / {{ !_.isNil(record.candidatesCount) ? record.candidatesCount : '--' }}
      </template>
      <template v-if="column.key === 'pass_rate'">
        {{ !_.isNil(record.passCount) ? record.passCount : '--' }} / {{ !_.isNil(record.candidatesCount) ? record.candidatesCount : '--' }}
      </template>
      <template v-if="column.key === 'score_rate'">
        {{ !_.isNil(record.avgScore) ? record.avgScore : '--' }} / {{ !_.isNil(record.score) ? record.score : '--' }}
      </template>
    </template>
  </a-table>
</template>
  
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import _ from 'lodash'
import { PaperStatus } from '@/models/paperModel'
import { getPagerList } from '@/api/admin/statisticAnalysis'

const columns = ref([
  {
    title: '考试名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    minWidth: 200,
    maxWidth: 400,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '创建人',
    dataIndex: 'teacherName',
    key: 'teacherName',
    width: 100,
    minWidth: 100,
    maxWidth: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: '考试开始日期',
    width: 140,
    minWidth: 140,
    maxWidth: 200,
    dataIndex: 'startTime',
    key: 'startTime',
    resizable: true,
    sorter: true,
  },
  {
    title: '实考 / 应考',
    width: 120,
    key: 'join_rate',
    align: 'right',
  },
  {
    title: '通过人数 / 总人数',
    width: 150,
    key: 'pass_rate',
    align: 'right',
  },
  {
    title: '平均分 / 总分',
    width: 120,
    key: 'score_rate',
    align: 'right',
  },
])

const page = ref(1)
const pageSize = ref(10)
const order_type = ref('')
const order_field = ref('')
const list = ref<any[]>([])
const listTotal = ref(0)
const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const params = _.pickBy({
      basePager:{
        current: page.value,
        size: pageSize.value,
      },
      // startTimeOrder: order_type.value == 'ascend' ? 'asc' : order_type.value == 'descend' ? 'desc' : '',
      order_type: order_type.value,
      order_field: order_field.value,
    }, value => !!value)
    // const { data = [], total = 0 } = await getPagerList(params) as any
    const data = await getPagerList(params) as any
    list.value = Array.isArray(data.records) ? data.records : []
    listTotal.value = data.total
  } catch (error) {
    list.value = []
    listTotal.value = 0
  } finally {
    loading.value = false
  }
}
getList()

// table分页修改、排序修改、筛选修改
function handleTableChange(pagination: any, filters: any = {}, sorter: any = {}) {
  console.log(sorter)
  // 处理分页
  page.value = pagination.current
  pageSize.value = pagination.pageSize
  // 处理排序
  order_type.value = sorter.order
  order_field.value = sorter.order ? sorter.field : ''
  // // 处理筛选
  // Object.assign(otherParams.value, filters)
  getList()
}

// 跳转具体考试的分析页面
const router = useRouter()
function analyze(record: any) {
  if (record.status !== PaperStatus.OVER) {
    message.warning('考试分析尚未统计，请稍后查看')
    return
  }
  router.push({
    name: 'testPaperAnalysisDetail',
    params: {
      id: record.id,
    },
    query: {
      name: record.name,
    }
  })
}

</script>
  
<style lang="less" scoped>
.name-link {
  width: 100%;
  display: block;
  color: rgba(84,120,238,1) !important;
  text-align: left !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  &.disabled {
    color: rgba(0, 0, 0, 0.65) !important;
    cursor: not-allowed;
  }
}
</style>
  