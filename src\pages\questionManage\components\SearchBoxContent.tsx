import fileIcon from '@/assets/icons/svg/file_icon.svg'
import { CaretRightOutlined, RightOutlined } from '@ant-design/icons-vue'
import { defineComponent } from 'vue'

const ContentAll = defineComponent({
  setup() {
    return () => (
      <div class="pure-jsx-component">
        {/*  题库 */}
        <div class="flex items-center">
          题库 
          <RightOutlined class="ml-[10px]" />
        </div>
        <div class="w-full py-[8px] flex flex-wrap justify-between">
          <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
            <img src={fileIcon} class="mr-[10px]" />
            <div>前端面试题库</div>
          </div>
        </div>

        {/*  科目 */}
        <div class="flex items-center">
          科目
          <RightOutlined class="ml-[10px]" />
        </div>
        <div class="py-[8px]">
          <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
            <div class="w-full mb-[8px]">
              操作系统
            </div>
            <div class="flex flex-nowrap justify-between w-full ">
              <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
                前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
              </div>
              <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-end text-[16px]">
                前端
                {'>'}
                输入输出
              </div>
            </div>
          </div>
        </div>
        {/*  题目 */}
        <div class="flex items-center">
          题目
          <RightOutlined class="ml-[10px]" />
        </div>
        <div class="py-[8px]">
          <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
            <div class="w-full mb-[8px]">
              操作系统
            </div>
            <div class="flex flex-nowrap justify-between w-full ">
              <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
                前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },
})
const ContentQuestionPackage = defineComponent({
  setup() {
    return () => (
      <div class="w-full py-[8px] flex flex-wrap justify-between">
        <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
          <img src={fileIcon} class="mr-[10px]" />
          <div>前端面试题库</div>
        </div>

        <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
          <img src={fileIcon} class="mr-[10px]" />
          <div>前端面试题库</div>
        </div>
        <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
          <img src={fileIcon} class="mr-[10px]" />
          <div>前端面试题库</div>
        </div>
      </div>
    )
  },
})

const ContentCollapseItemForSubjectAndQuestion = defineComponent({
  props: {
    type: {
      type: String,
      default: 'subject',
    },
  },
  setup(props) {
    return () => (
      <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
        <div class="w-full mb-[8px]  cursor-pointer">
          <CaretRightOutlined class="mr-[8px]" />
          操作系统
        </div>
        <div class="flex flex-nowrap justify-between w-full ">
          <div class={`w-[${props.type === 'subject' ? 50 : 100}%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]`}>
            前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
          </div>
          {
            props.type === 'subject'
              ? (
                  <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-end text-[16px]">
                    前端
                    {'>'}
                    输入输出
                  </div>
                )
              : <></>
          }

        </div>
      </div>
    )
  },
})
const ContentSubject = defineComponent({
  setup() {
    return () => (
      <div class="py-[8px]">
        <ContentCollapseItemForSubjectAndQuestion type="subject" />
      </div>
    )
  },
})

const ContentQuestion = defineComponent({
  setup() {
    return () => (
      <div class="py-[8px]">
        <ContentCollapseItemForSubjectAndQuestion type="question" />
      </div>
    )
  },
})

const contentMap = {
  all: ContentAll,
  package: ContentQuestionPackage,
  subject: ContentSubject,
  question: ContentQuestion,
}

export default defineComponent({
  setup(props, { attrs }) {
    const type = attrs.type as keyof typeof contentMap
    const Content = contentMap[type] as ReturnType<typeof defineComponent>
    return () => (
      <div class="pure-jsx-component">
        {Content ? <Content /> : <></>}
      </div>
    )
  },
})