export enum InterviewQuestionEnum {
    "单选题",
    "多选题",
    "判断题",
    "问答题",
    "算法题",
    "填空题",
    "排序题",
    "复合题",
    "评分题",
}

/** 题目深度 */
export enum DepthEnum {
    "基础知识" = 1,
    "底层原理",
    "灵活应用",
    "深入思考",
}
/** 题目偏度 */
export enum ExpertiseEnum {
    "经常用到" = 1,
    "很少用到",
}

interface BTestCase {
    input: Array<{ [key: string]: string }>;
    output: Array<{ type: string; value: string }>;
}

interface PTestCase {
    input: Array<{ [key: string]: string }>;
    output: Array<{ type: string; value: string }>;
}

interface DisableKws {
    C: Array<string>;
    "C++": Array<string>;
    Java: Array<string>;
    Python: Array<string>;
    JavaScript: Array<string>;
    Go: Array<string>;
}

interface Option {
    content: string;
    value: string;
    [k: string]: any
}


export interface InterviewQuestionFormState {
    type: InterviewQuestionEnum;
    category?: string;
    body: string;
    complicatedediting: boolean;
    complexcontent: string;
    // answer?: Array<{ keyword: Array<string>; score: number }>;
    /** 深度 */
    depth: DepthEnum;
    /** 偏度 */
    expertise: ExpertiseEnum;
    /** 难度（由深度和偏度计算得出） */
    difficulty: number;
    /** 建议作答时间 */
    suggested_time: number;
    AI_reading: number;
    /** 自定义AI阅读文本 */
    AI_reading_content: string;

    // 选择
    options?: Array<Option>;

    // 算法
    func_name?: string;
    rtype?: { is_list: boolean };
    parameters?: Array<{ name: string; type: string; is_list: boolean }>;
    btestcase?: Array<BTestCase>;
    ptestcase?: Array<PTestCase>;
    disablekws?: DisableKws;

    // 问答
    /** 评分依据 */
    scorebasis: string;
    /** 参考答案 */
    reference_answer: string;
    sepscore?: boolean; // 部分得分

    // 排序
    ordered?: boolean;


    ignorecase?: boolean;
}

export interface InterviewQuestionDetail extends InterviewQuestionFormState {
    id: string
    category_name: string
    AI_reading_video_url?: string
    proofreading_info?: {
        create_at: string
        id: string
        proofreading: number
        ques: string
        teacher: string
        teacher_name: string
        update_at: string
        wrong_reason: string
        is_ai: boolean
    }
    /** 0 未校对 1 校对正确 2 校对错误 */
    proofreading?: 0 | 1 | 2
    [k: string]: any
}

export interface SelfUsefulSentenceDetail {
    id: string
    create_at: string
    update_at: string
    phrases: string
    teacher: string
}

export enum ConversationRole { AI, User }
export interface Conversation {
    type: ConversationRole
    text: string
    qlist?: InterviewQuestionDetail[]
}

export enum ConversationAction {
    "选择题库",
    "关键词搜索",
    "追问题目",
    "相关题目",
    "换一批",
}

export enum ConversationAiStatus {
    "初始化",
    "正在回答",
    "回答完毕",
}

export interface BaseConversationDetail {
    uuid: string;
    text: string;
    qlist?: (InterviewQuestionDetail & { expand?: boolean })[];
    /** 该条记录是否已保存 */
    saved: boolean
}

// 通过条件类型来定义不同情况下的 ConversationDetail 类型
export type ConversationDetail =
    | (BaseConversationDetail & {
        type: ConversationRole.AI
        aiStatus: ConversationAiStatus
        apiName?: string
        apiParams?: any
    })
    | (BaseConversationDetail & { type: ConversationRole.User; aiStatus?: never });