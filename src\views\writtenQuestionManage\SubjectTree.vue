<template>
  <div class="subjects">
    <div v-show="!isCollapse" class="collapse-menu-wrapper" @click="collapseMenu">
      <svg-icon name="expand-menu" class="menu-icon" />
    </div>
    <div v-show="isCollapse" class="category-container" :class="{ isCollapse: !isCollapse }" :style="{ width: boxWidth + 'px' }">
      <div class="header">
        <h2>题库列表</h2>
        <div class="search-ipt-wrapper" :class="{active: searchOpen}">
          <a-input ref="searchIptRef" @change="change" class="search-ipt" v-model:value.trim="searchValue" :bordered="false" placeholder="请输入题库名称" allowClear>
          </a-input>
          <SearchOutlined @click="handleSearch" />
        </div>
        <svg-icon name="collapse-menu" class="menu-icon" @click="collapseMenu" />
      </div>
      <div class="body">
        <a-spin v-if="fetchLoading" class="loading-style"></a-spin>
        <img v-else-if="!fetchLoading && !treeData.length" style="width: 200px;margin-top: 100px;" src="@/assets/images/nodata.png" alt="">
        <a-tree
          v-else
          :draggable="editable && !isMoving"
          show-icon
          show-line
          :blockNode="true"
          :tree-data="treeData"
          :autoExpandParent="autoExpandParent"
          :field-names="{ title: 'name', key: 'id' }"
          v-model:expandedKeys="expandedKeys"
          :selectedKeys="selectedKeys"
          @drop="onDrop"
          @select="onSelect"
          @expand="onExpand"
        >
          <template #icon="item">
            <div>
  
              <img
                src="@/assets/images/admin/folder.png"
                style="width: 16px; margin-right: 4px; margin-bottom: -2px"
                v-if="item.children && item.children.length"
              />
              <img
                src="@/assets/images/admin/file.png"
                style="width: 16px; margin-right: 4px; margin-bottom: -2px"
                v-else
              />
            </div>
          </template>
          <template #title="item">
            <div class="custom-title">
              <SearchHighLight class="title" :text="item.name" :search-text="searchValue"></SearchHighLight>
              <div v-if="showTopicCount && item.topicCount" class="count">({{ item.topicCount }})</div>
              <div class="operbtns" v-if="editable && (store.getters.userInfo.officialCert || !item.officialCert)">
                <span class="icon" v-if="item.idlink.split('|').length <= 5" title="新建子题库" @click.stop="addNewSubDep(item)">
                  <svg-icon name="plus" />
                </span>
                <span class="icon" v-if="item.name !== '官方题库' && item.name !== '部门题库'" title="编辑题库" @click.stop="editCurrentDep(item)">
                  <svg-icon name="edit"
                /></span>
                <a-popconfirm
                  v-if="item.idlink.split('|').length > 2 || (item.idlink.split('|').length === 2 && (treeData.find(el => el.subjectCategoryUUID === item.idlink.split('|')[0])?.children?.length || 0) > 1)"
                  :title="'确认要删除该题库以及其包含的所有子题库和题目吗？'"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="delCurrentDep(item)"
                  @click.stop
                >
                  <span class="icon" title="删除题库">
                    <img src="@/assets/icons/svg/delete.svg" class="del-icon" />
                  </span>
                </a-popconfirm>
              </div>
            </div>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <Icon icon="DownOutlined" :class="switcherCls" />
          </template>
        </a-tree>
        
      </div>
      <div class="footer" v-if="footer">
        <a-checkbox v-model:checked="showTopicCount">显示当前试卷题量数</a-checkbox>
        <a-tooltip placement="right" overlayClassName="light">
            <template #title>
                <span>勾选后将展示题库中已选题目的数量</span>
            </template>
            <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </div>
      <div id="resizer" @mousedown="startResize"
           style="width:4px;height:100%;position:absolute;right:0;top:0;cursor:e-resize;border-right:2px solid #5478EE;transition:all ease 0.2s;opacity: 0;">
      </div>
    </div>
    <a-modal
      class="common-modal"
      v-model:visible="visible"
      :title="isEdit ? '编辑题库' : '新增题库'"
      width="420px"
      :maskClosable="false"
      @cancel="closeModal"
    >
      <template #footer>
        <a-button type="primary" :loading="loading" @click="onSubmit">保存</a-button>
        <a-button @click="closeModal">取消</a-button>
      </template>
      <a-form
        class="dep-form"
        ref="formRef"
        :label-col="labelCol"
        :model="formState"
        :hideRequiredMark="true"
        :rules="rules"
        :colon="false"
      >
        <a-form-item label="题库名称" name="name">
          <a-input v-model:value.trim="formState.name" :maxLength="10" placeholder="题库名称，最多10个字符" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted, onActivated, onDeactivated } from 'vue'
import { altersubcatgstru, editCategory, addCategory, deleteCategory } from '@/api/admin/questionManage'
import { subjcatgs } from './api/index'
import { useStore } from 'vuex'
import { SearchOutlined } from '@ant-design/icons-vue'
import { QuestionModel } from '@/models/questionModel'
import SearchHighLight from '@/components/SearchHighLight.vue'
import { findItemInTree } from '@/utils'
import _ from 'lodash'
import { useRoute } from 'vue-router'

const route = useRoute()

type TreeNode = {
  key?: string
  id: string
  idlink: string
  name: string
  officialCert: boolean
  children: TreeNode[]
  topicCount?: number
}

const store = useStore()

const props = withDefaults(
  defineProps<{
    editable?: boolean
    footer?: boolean
    allSelectRows?: any[]
  }>(),
  {
    editable: false,
    footer: false,
    allSelectRows: () => ([])
  }
)

const emits = defineEmits<{
  (e: 'select', ...args: any[]): void
  (e: 'delete', parentId: string): void
  (e: 'visibleChange'): void
}>()

const treeData = ref<TreeNode[]>([])
const expandedKeys = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const labelCol = ref({ span: 5 })
const formRef = ref()
const visible = ref(false)

const formState = ref({
  name: '',
  pname: '题库列表',
  id: null
})

const isCollapse = ref(true)
const collapseMenu = () => {
  isCollapse.value = !isCollapse.value
  emits('visibleChange')
}

const rules = {
  name: [{ required: true, message: '题库名称不能为空', trigger: 'blur' }]
}

const resetForm = (form = <any>{}) => {
  formState.value.id = form.id || null
  formState.value.name = form.name || ''
  formState.value.pname = form.pname || ''
}

const isEdit = ref(false)
const officialCert = ref<boolean>(false)
const addNewSubDep = (item: any) => {
  isEdit.value = false
  resetForm()
  visible.value = true
  formState.value.pname = item.name
  formState.value.id = item.id
  formState.value.subjectCategoryUUID = item.subjectCategoryUUID
  officialCert.value = item.officialCert
}

const editCurrentDep = (item: any) => {
  item.isEdit = true
  isEdit.value = true
  resetForm(item)
  visible.value = true
  officialCert.value = item.officialCert
}

// 删除题库
async function delCurrentDep(item: any) {
  let parentId = getParentId(item.id, treeData.value) as string
  await deleteCategory({ id: item.id })
  getSubjectData()
  selectedKeys.value = [parentId]
  emits('delete', parentId)
}

const onSelect = (keys: string[], { node }: any) => {
  if (keys.length === 0) return
  selectedKeys.value = keys
  emits('select', node)
}

const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
}

// 移动题库
async function onDrop(info: any) {
  console.log('this is info', info)
  await altersubcatgstru({
    id: info.dragNode.dataRef.id,
    parent: info.node.dataRef.id
  })
  await getSubjectData()
  expandedKeys.value = [info.node.dataRef.id, ...expandedKeys.value]
  selectedKeys.value = [info.dragNode.dataRef.id]
  emits('select', {
    id: info.dragNode.dataRef.id,
    subjectCategoryUUID: info.dragNode.dataRef.subjectCategoryUUID,
    officialCert: info.dragNode.dataRef.officialCert,
  })
}

const closeModal = () => {
  visible.value = false
  formRef.value.resetFields()
}

const loading = ref(false)
async function onSubmit() {
  await formRef.value.validate()
  loading.value = true
  const addParams = {
    parent: formState.value.subjectCategoryUUID,
    name: formState.value.name,
    pname: formState.value.pname
  }
  const modifyParams = {
    id: formState.value.id,
    name: formState.value.name
  }
  // const execFunc =  isEdit.value ? editCategory : addCategory
  // execFunc(isEdit.value ? modifyParams : addParams)
  isEdit.value ? modifyDept(modifyParams) : addDept(addParams)
}

async function addDept(params: any) {
  try {
    autoExpandParent.value = true
    let id = await addCategory({ ...params }) as unknown as string

    closeModal()
    await getSubjectData()
    if (id) {
      expandedKeys.value = [getParentId(id, treeData.value)]
      const subjectCategoryUUID = getSubjectCategoryUUIDById(id, treeData.value)
      selectedKeys.value = [id]
      emits('select', { id, subjectCategoryUUID, officialCert: officialCert.value })
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

async function modifyDept(params: any) {
  try {
    await editCategory({ ...params })
    closeModal()
    await getSubjectData()
    selectedKeys.value = [params.id]
    const subjectCategoryUUID = getSubjectCategoryUUIDById(params.id, treeData.value)
    emits('select', { id: params.id, subjectCategoryUUID, officialCert: officialCert.value })
  } finally {
    loading.value = false
  }
}

const originTreeData = ref<TreeNode[]>([])
const fetchLoading = ref(false)
async function getSubjectData() {
  fetchLoading.value = true
  try {
    let res: any = await subjcatgs({ action: 'query',id: route.query.id })
    res = Array.isArray(res) ? res : []
    // 初始化题库节点的选题数量
    if (!props.editable) {
      res.forEach((node: any) => setNodeTopicCount(node, catgsQuesNumMap.value))
    }
    treeData.value = res
    originTreeData.value = res
    searchValue.value = ''
  } finally {
    fetchLoading.value = false
  }
}

// 科目搜索
const searchIptRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const autoExpandParent = ref(false)
function handleSearch() {
  searchOpen.value = !searchOpen.value
  searchIptRef.value.focus()
}
function change(row) {
      if (row.type == 'click') {
        searchOpen.value = false;
      }
}
function getParentId (id: string | number, tree: TreeNode[]): string | number | undefined {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some(item => item.id === id)) {
        parentKey = node.id;
      } else if (getParentId(id, node.children)) {
        parentKey = getParentId(id, node.children);
      }
    }
  }
  return parentKey;
}
function getSubjectCategoryUUID (id: string | number, tree: TreeNode[],res?:any): string | number | undefined {
  if (res) return res
  res = null;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if(node.subjectCategoryUUID === id){
      res = node.id;
      return res
    }
    else if(node.children && node.children.length){
      res =  getSubjectCategoryUUID(id, node.children,res)
    }
  }
  return res;
}

function getSubjectCategoryUUIDById (id: string | number, tree: TreeNode[],res?:any): string | number | undefined {
  if (res) return res
  res = null;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if(node.id === id){
      res = node.subjectCategoryUUID;
      return res
    }
    else if(node.children && node.children.length){
      res =  getSubjectCategoryUUIDById(id, node.children,res)
    }
  }
  return res;
}


watch(searchValue, (val) => {
  treeData.value = filterTree(originTreeData.value, val)

  autoExpandParent.value = true
  if (val) return
  // 如果搜索词为空字符串，则只展开选中科目
  let id = getParentId(selectedKeys.value[0], originTreeData.value)
  expandedKeys.value = [id]

})

function filterTree(roots: TreeNode[], filterText: string): TreeNode[] {
  return roots.map((root) => filterSingleNode(root, filterText)).filter(Boolean) as TreeNode[];
}

function filterSingleNode(node: TreeNode, filterText: string): TreeNode | null {
  if (node.name && node.name.includes(filterText)) {
    // 如果当前节点符合条件，返回当前节点
    if (filterText) {
      let id = getParentId(node.id, originTreeData.value)
      expandedKeys.value.push(id)
    }
    return {
      ...node,
      children: filterTree(node.children, filterText),
    };
  } else {
    // 如果当前节点不符合条件，递归过滤子节点
    const filteredChildren = filterTree(node.children, filterText);
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
      };
    } else {
      return null; // 如果当前节点及其子节点都不符合条件，返回null
    }
  }
}

// 题库树统计每个题库所选题目的数量
const showTopicCount = ref(true)
const catgsQuesNumMap = ref<Record<string, string[]>>({})
function initCatgsQuesNumMap() {
  if (!props.allSelectRows?.length) return
  let list = props.allSelectRows.map(item => item.children).flat()
  list.forEach(addItemToCatgsQuesNumMap)
}
initCatgsQuesNumMap()
function addItemToCatgsQuesNumMap(item: QuestionModel) {
  if (Reflect.has(catgsQuesNumMap.value, item.categoryUUID)) {
    catgsQuesNumMap.value[item.categoryUUID].push(item.id!)
  } else {
    Reflect.set(catgsQuesNumMap.value, item.categoryUUID, [item.id])
  }
}
function removeItemFromCatgsQuesNumMap(item: QuestionModel) {
  try {
    let index = catgsQuesNumMap.value[item.categoryUUID].findIndex(id => id === item.id)
    if (catgsQuesNumMap.value[item.categoryUUID].length === 0 && index === 0) {
      Reflect.deleteProperty(catgsQuesNumMap.value, item.categoryUUID)
    } else {
      catgsQuesNumMap.value[item.categoryUUID].splice(index, 1)
    }
  } catch (error) {
    console.log(error)
  }
}
/**
 * 深度优先计算每个节点的选题数量
 * @param {TreeNode} node - 当前节点
 * @param {Record<string, string[]>} map - 节点id与选题数组的映射
 */
function setNodeTopicCount(node: TreeNode, map: Record<string, string[]>) {
  let topicCount = catgsQuesNumMap.value[node.subjectCategoryUUID]?.length ?? 0
  if (node.children?.length) {
    topicCount += node.children.reduce((pre: number, cur: TreeNode) => { 
      setNodeTopicCount(cur, map)
      return pre + cur.topicCount!
    }, 0)
  }
  node.topicCount = topicCount
}

watch(catgsQuesNumMap, map => {
  if (!props.editable) {
    originTreeData.value.forEach(node => setNodeTopicCount(node, map))
    treeData.value = filterTree(originTreeData.value, searchValue.value)
  }
}, { deep: true })


// 宽度拖拽
const lastBoxWidth = ref(240)
const BOX_MAX_WIDTH = 400
const BOX_MIN_WIDTH = 240
const boxWidth = ref(240)
const startX = ref(0);
const isMoving = ref(false)
const startResize = (e: MouseEvent) => {
  startX.value = e.clientX;
  isMoving.value = true
  window.addEventListener('mousemove', resize);
  window.addEventListener('mouseup', stopResize);
};

const resize = (e: MouseEvent) => {
  if (!isMoving.value) return
  const newWidth = lastBoxWidth.value + e.clientX - startX.value;
  boxWidth.value = Math.max(Math.min(newWidth, BOX_MAX_WIDTH), BOX_MIN_WIDTH);
};

const stopResize = () => {
  isMoving.value = false
  lastBoxWidth.value = boxWidth.value
  window.removeEventListener('mousemove', resize);
  window.removeEventListener('mouseup', stopResize);
};

async function init() {
  await getSubjectData()
  if (!selectedKeys.value.length) {
    selectedKeys.value = [treeData.value[0].id]
    emits('select', treeData.value[0])
  } else {
    let id = selectedKeys.value[0]
    let item = findItemInTree(id, treeData.value)
    emits('select', item)
  }

  if (!expandedKeys.value.length)
    expandedKeys.value = [treeData.value[0].id]
}
const debounceInit = _.debounce(init, 1000)

onMounted(() => {
  debounceInit()
})

onActivated(() => {
  debounceInit()
})

onDeactivated(() => {
  visible.value = false
})

onUnmounted(() => {
  stopResize()
})

defineExpose({
  addItemToCatgsQuesNumMap,
  removeItemFromCatgsQuesNumMap
})

</script>

<style lang="less" scoped>
.subjects {
  height: 100%;
  user-select: none;

  .collapse-menu-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 48px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    z-index: 999;
    cursor: pointer;
  }

  .menu-icon {
    cursor: pointer;
  }

  .category-container {
    height: 100%;
    flex-shrink: 0 !important;
    overflow: auto;
    background: #fff;
    display: flex;
    flex-direction: column;
    position: relative;
    border-right: 1px solid #e8e8e8;
    #resizer:hover {
      opacity: 1!important;
    }

    &.isCollapse {
      width: 0;
    }

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 48px;
      padding: 0 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      position: relative;

      h2 {
        font-family: PingFang SC;
        font-size: 14px;
        line-height: inherit;
        color: #121633;
        font-weight: bold;
      }

      .plus-icon {
        cursor: pointer;
      }

      .addsubject {
        display: none;
      }

      .search-ipt-wrapper {
        position: absolute;
        right: 40px;
        background-color: #fff;
        padding: 4px;
        border-radius: 8px;
        display: flex;
        
        &.active {
          border: 1px solid #5478ee;
          .search-ipt {
            padding: 0 2px;
            width: 150px;
          }
        }
        .anticon {
          cursor: pointer;
          padding: 4px;
        }
        .anticon-search {
          z-index: 999;
          border-radius: 4px;
          background-color: #fff;
          &:hover {
            background-color: #e8e8e8;
          }
        }
        .search-ipt {
          width: 0;
          height: 22px;
          padding: 0;
          border: none;
          box-shadow: none;
          :deep(.ant-input) {
            font-size: 13px;
          }
        }
      }
    }
  }

  .loading-style {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .body {
    padding-left: 16px;
    padding-top: 8px;
    overflow: auto;
    // height: calc(100% - 50px);
    flex: 1;
    min-height: 0;
  }
  .footer {
    height: 48px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding-left: 16px;
    :deep(.ant-checkbox + span) {
      font-size: 13px;
    }
  }

  :deep(.ant-tree-node-content-wrapper) {
    flex: auto;
    min-width: 0;
    padding: 8px 0 !important;
  }
  :deep(.ant-tree-title) {
      flex: auto;
      min-width: 0;
    }
  .custom-title {
    max-width: 100%;
    height: 24px;
    padding-right: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      min-width: 0;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .count {
      margin-left: 4px;
    }

    .operbtns {
      justify-content: space-around;
      display: none;
      .icon {
        margin-left: 4px;
        &:first-of-type {
          margin-left: 0;
        }
      }
  
      .del-icon {
        width: 14px;
        position: relative;
        bottom: 2px;
      }
    }
    &:hover .operbtns {
      display: flex;
      text-align: right;
    }
  }
}
</style>
