<template>
  <div class="create-question-container">
    <close-outlined class="close-btn" @click="handleCancel" />
    <div class="create-question-content">
      <a-form
        class="form"
        ref="createQuestionFormRef"
        :model="formState"
        :hideRequiredMark="true"
        :rules="rules"
        :colon="false"
        labelAlign="left"
      >
        <a-row :gutter="44">
          <a-col :span="8">
            <a-form-item label="题目类型" name="type">
              <a-select
                v-model:value="formState.type"
                :disabled="props.draftId"
                placeholder="请选择"
                @select="handleTypeChange"
              >
                <a-select-option :value="questionType.SINGLE_CHOICE">单选题</a-select-option>
                <a-select-option :value="questionType.MULTIPLE_CHOICE">多选题</a-select-option>
                <a-select-option :value="questionType.TRUE_OR_FALSE">判断题</a-select-option>
                <a-select-option :value="questionType.FILL_BLOCK">填空题</a-select-option>
                <a-select-option :value="questionType.SHORT_ANSWER">问答题</a-select-option>
                <a-select-option :value="questionType.AlGORITHM_CODING">算法题</a-select-option>
                <a-select-option :value="questionType.SORT_ORDER">排序题</a-select-option>
                <!-- <a-select-option :value="questionType.COMPOSITE">复合题</a-select-option> -->
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所属题库" name="category">
              <a-tree-select
                show-search
                :tree-data="categoryList"
                placeholder="添加题库"
                v-model:value="formState.category"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                allow-clear
                :field-names="replaceFields"
                treeNodeFilterProp="name"
                :virtual="false"
                :getPopupContainer="
                (triggerNode: any) => {
                  return triggerNode.parentNode
                }"
              >
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="4" style="min-width: 180px">
            <a-form-item label="&nbsp;&nbsp;&nbsp;&nbsp;分值" name="score">
              <score-input
                v-if="formState.type !== 7"
                ref="scoreInputTotalRef"
                :disabled="
                  ((formState.type === 5 || formState.type === 3) && formState.sepscore) ||
                  formState.type === 7
                "
                v-model="formState.score"
                :type="formState.type"
                @getScore="getScore"
              />
              <score-input2
                v-else
                ref="scoreInputTotalRef"
                :disabled="
                  ((formState.type === 5 || formState.type === 3) && formState.sepscore) ||
                  formState.type === 7
                "
                v-model="formState.score"
                :type="formState.type"
                @getScore="getScore"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item>
              <template #label>
                <span>复杂编辑</span>
                <a-tooltip placement="right" overlayClassName="light">
                  <template #title>
                    <span>开启此功能后，可以在文本中插入图片和公式，并对文本进行特殊样式编辑</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2" style="width: 54%;height: 54%;"></svg-icon>
                </a-tooltip>
              </template>
              <j-switch
                v-model:checked="formState.complicatedediting"
                @change="handleComplexSwitchChange(formState, $event)"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 编程 -->
        <a-tabs v-if="formState.type === 4" type="card" v-model:activeKey="activeTab">
          <a-tab-pane key="1" tab="普通录入"></a-tab-pane>
          <a-tab-pane key="2" tab="高级录入"></a-tab-pane>
        </a-tabs>
        <div v-show="activeTab === '1'">
          <a-form-item
            v-if="!formState.complicatedediting"
            label="题干内容"
            v-bind="validateInfos.body"
          >
            <a-textarea
              ref="bodyInputRef"
              v-model:value="formState.body"
              :rows="4"
              placeholder="点击编辑"
              @blur="handleBodyBlur"
            />
            <div v-if="formState.type === 5" class="body-tip">
              <svg-icon name="tip" class="tip-icon" />
              <span>点击</span>
              <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
              <span>连续输入两个下划线"__"可增加空位</span>
            </div>
          </a-form-item>
          <a-form-item v-else label="题干内容" v-bind="validateInfos.complexcontent">
            <VueQuillEditor
              ref="bodyEditorRef"
              v-model:text="formState.body"
              v-model:content="formState.complexcontent"
              @blur="handleComplexContentBlur"
            ></VueQuillEditor>
            <div v-if="formState.type === 5" class="body-tip">
              <svg-icon name="tip" class="tip-icon" />
              <span>点击</span>
              <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
              <span>连续输入两个下划线"__"可增加空位</span>
            </div>
          </a-form-item>
        </div>
        <!-- 问答题 -->
        <a-form-item v-if="formState.type === 3" label="评分依据" name="scorebasis">
          <a-textarea
            v-if="!formState.complicatedediting"
            v-model:value="formState.scorebasis"
            :rows="4"
            placeholder="点击编辑"
          />
          <VueQuillEditor
            ref="scorebasisEditorRef"
            v-else
            v-model:content="formState.scorebasis"
          ></VueQuillEditor>
          <p class="standard-tip">(仅阅卷老师可见)</p>
        </a-form-item>
        <a-form-item
          v-if="formState.type === 3"
          label="得分点"
          name="answer"
          class="points-wrapper"
        >
          <template v-for="(item, index) in scorepts" :key="index">
            <div class="item">
              <div class="index">{{ index + 1 }}</div>
              <div class="tags-select">
                <JTagInput
                  :ignorecase="formState.ignorecase"
                  v-model="item.keyword"
                  placeholder="输入完成后按回车添加多个关键词"
                ></JTagInput>
              </div>
              <div v-if="formState.sepscore" class="score-wrapper">
                <span class="score">分值</span>
                <score-input
                  class="score-input"
                  v-model="item.score"
                  @getScore="getPointsScore($event, item)"
                />
              </div>
              <div class="del-icon-wrap">
                <svg-icon
                  class="del-icon"
                  name="circle-del"
                  width="16px"
                  height="16px"
                  @click="delPoints(index)"
                />
              </div>
            </div>
          </template>
          <div class="addpoints-btn">
            <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
          </div>
        </a-form-item>

        <template v-if="formState.type === 4">
          <div v-show="activeTab === '1'">
            <a-form-item label="方法名称" name="func_name">
              <div class="func-name-wrapper">
                <a-input
                  class="form-input"
                  v-model:value.lazy.trim="formState.func_name"
                  placeholder="方法名称"
                  autocomplete="off"
                />
                <span class="func-extra-info"
                  >(以字母、下划线或美元符号开头，可包含字母、数字、下划线或美元符号，且大小写敏感，避免使用关键字，推荐采用驼峰命名法)</span
                >
              </div>
            </a-form-item>
            <a-form-item label="返回值类型" name="rtype">
              <div class="rtype-wrapper">
                <a-select
                  v-model:value="formState.rtype.type"
                  placeholder="返回值类型"
                  class="form-input"
                >
                  <template v-for="option in varType" :key="option.value">
                    <a-select-option :value="option.value">{{ option.label }}</a-select-option>
                  </template>
                </a-select>
                <!-- <a-input class="form-input" v-model:value="formState.rtype.type" /> -->
                <a-checkbox v-model:checked="formState.rtype.is_list" class="form-checkbox" />
                <span class="rtype-extra-info">&nbsp;此变量是数组或列表</span>
              </div>
            </a-form-item>
            <a-form-item label="参数列表" name="parameters">
              <param-list
                :parameters="formState.parameters"
                @addParam="handleAddParameter"
                @delParam="handleDelParameter"
              />
            </a-form-item>
            <a-form-item label="题干测试用例" name="btestcase">
              <test-instance
                :exampleIO="formState.btestcase"
                @addInstance="handleAddInstance"
                @delInstance="handleDelInstance"
              />
            </a-form-item>
            <a-form-item label="批卷测试用例" name="ptestcase">
              <test-instance
                :exampleIO="formState.ptestcase"
                @addInstance="addTestCase"
                @delInstance="delTestCase"
              />
            </a-form-item>
            <a-form-item label="题目验证" name="validateCase">
              <validate-algorithm
                ref="validateAlgorithmRef"
                :func-main="funcMainObj"
                :func-main-params="funcMainParams"
                :func-template="funcTemplateObj"
                :formState="formState"
                @update-template="updateTemplate"
                @update-validate-code="updateValidateCode"
              />
            </a-form-item>

            <a-form-item label="难度" name="difficulty">
              <a-radio-group v-model:value="formState.difficulty">
                <a-radio :value="0">简单</a-radio>
                <a-radio :value="1">中等</a-radio>
                <a-radio :value="2">困难</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>
          <div v-if="activeTab === '2'">
            <JsonEditorVue
              v-if="showJsonEditor"
              ref="jsonEditorRef"
              mode="tree"
              class="jse-theme-dark"
              v-model="formState"
            />
          </div>
        </template>

        <!-- 添加复合题 -->
        <a-collapse :accordion="false" v-if="formState.type == 7 && subQuestionList.length">
          <template v-for="(item, index) in subQuestionList" :key="index">
            <a-collapse-panel :header="`【${item.label}】 ${item.formState.body}`">
              <template #extra>
                <span class="complex-switch-box" @click.stop>
                  <span class="label">复杂编辑</span>
                  <j-switch
                    v-model:checked="item.formState.complicatedediting"
                    @change="handleComplexSwitchChange(item.formState, $event, index)"
                  />
                </span>
                <img src="@/assets/images/delete.png" alt="删除" @click="handleDelete(index)" />
                <img
                  style="margin-left: 16px"
                  v-if="index !== 0"
                  src="@/assets/icons/svg/compoundUp.svg"
                  alt="上移"
                  @click="handleUp($event, index)"
                />
                <img
                  style="margin-left: 16px"
                  v-if="index !== subQuestionList.length - 1"
                  src="@/assets/icons/svg/compoundDown.svg"
                  alt="下移"
                  @click="handleDown(index)"
                />
              </template>
              <template v-if="item.type == 0">
                <single-choice ref="subQuestionsRef" v-model="item.formState"></single-choice>
              </template>
              <template v-else-if="item.type == 1">
                <multiple-choice ref="subQuestionsRef" v-model="item.formState"></multiple-choice>
              </template>
              <template v-else-if="item.type == 2">
                <judging-questions
                  ref="subQuestionsRef"
                  v-model="item.formState"
                ></judging-questions>
              </template>
              <template v-else-if="item.type == 3">
                <QuestionAndAnswer
                  ref="subQuestionsRef"
                  v-model="item.formState"
                ></QuestionAndAnswer>
              </template>
              <template v-else-if="item.type == 5">
                <blank-filling ref="subQuestionsRef" v-model="item.formState"></blank-filling>
              </template>
              <template v-else-if="item.type == 6">
                <OrderQuestion ref="subQuestionsRef" v-model="item.formState"></OrderQuestion>
              </template>
            </a-collapse-panel>
          </template>
        </a-collapse>
        <!-- 单选题 -->
        <a-form-item v-if="formState.type == 0" name="options" class="question-options">
          <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
            <template v-for="(item, index) in formState.options" :key="index">
              <div class="item-option">
                <a-radio :value="item.value">
                  <span class="option-radio" 
                  :style = "{'margin-right':formState.complicatedediting?'8px':'18px'}">
                  {{ item.value }}</span>
                </a-radio>
                <a-textarea
                  v-if="!formState.complicatedediting"
                  class="option-content"
                  v-model:value.trim="item.content"
                  :auto-size="{ minRows: 1 }"
                  @blur='handleOptionBlur'
                  placeholder="点击，编辑选项；选中即正确答案"
                />
                <div v-else class="editor-wrapper">
                  <VueQuillEditor
                    ref="optionEditorsRef"
                    v-model:content="item.content"
                    @blur='handleOptionBlur'
                  ></VueQuillEditor>
                </div>
                <svg-icon
                  class="del-icon"
                  name="circle-del"
                  width="16px"
                  height="16px"
                  @click.prevent="delOptionWrap($event, index)"
                />
              </div>
            </template>
          </a-radio-group>
          <div style="display: flex; align-items: center; margin-top: 16px;">
            <div class="add-option-btn" @click="addOption">
              <svg-icon name="plus" class="mr-[8px]"/>
              <span>添加选项</span>
            </div>
            <a-button class="common-ai-button" style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;" @click="showGenerateOptionModal">
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;" />
              生成干扰项
            </a-button>
            <a-tooltip placement="right" overlayClassName="light">
              <template #title>
                <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </div>
        </a-form-item>
        <!-- 多选题 -->
        <a-form-item v-else-if="formState.type == 1" name="options" class="question-options">
          <a-checkbox-group v-model:value="formState.answer" style="max-width:100%;">
            <template v-for="(item, index) in formState.options" :key="index">
              <div class="item-option">
                <a-checkbox :value="item.value">
                  <span class="option-radio">{{ item.value }}</span>
                </a-checkbox>
                <a-textarea
                  v-if="!formState.complicatedediting"
                  class="option-content"
                  :auto-size="{ minRows: 1 }"
                  v-model:value.trim="item.content"
                  @blur='handleOptionBlur'
                  placeholder="点击，编辑选项；选中即正确答案"
                />
                <div v-else class="editor-wrapper">
                  <VueQuillEditor
                    ref="optionEditorsRef"
                    v-model:content="item.content"
                    @blur='handleOptionBlur'
                  ></VueQuillEditor>
                </div>
                <svg-icon
                  class="del-icon"
                  name="circle-del"
                  width="16px"
                  height="16px"
                  @click.prevent="delOptionWrap($event, index)"
                />
              </div>
            </template>
          </a-checkbox-group>
          <div style="display: flex; align-items: center; margin-top: 16px;">
            <div class="add-option-btn" @click="addOption">
              <svg-icon name="plus" />
              <span>添加选项</span>
            </div>
            <a-button class="common-ai-button" style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;" @click="showGenerateOptionModal">
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;" />
              生成干扰项
            </a-button>
            <a-tooltip placement="right" overlayClassName="light">
              <template #title>
                <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </div>
        </a-form-item>
        <!-- 判断题 -->
        <a-form-item v-else-if="formState.type == 2" class="question-options">
          <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
            <template v-for="(item, index) in formState.options" :key="index">
              <div class="item-option">
                <a-radio :value="item.value">
                  <span class="option-radio">{{ item.value }}</span>
                  <a-input class="option-content" readonly v-model:value="item.content" />
                </a-radio>
              </div>
            </template>
          </a-radio-group>
        </a-form-item>
        <!-- 填空 -->
        <a-form-item v-else-if="formState.type == 5" class="question-options">
          <template v-for="(item, index) in fillBlank" :key="item.id">
            <div class="fill-blank-item">
              <span class="label">填空{{ index + 1 }}答案</span>
              <JTagInput
                :ignorecase="formState.ignorecase"
                class="tag-ipt"
                v-model="item.keyword"
                placeholder="点击，编辑选项；点击回车设置多个关键词"
              ></JTagInput>
              <div v-if="formState.sepscore" class="score-wrapper">
                <span class="score">分值</span>
                <score-input
                  class="score-input"
                  v-model="item.score"
                  ref="scoreInputRef"
                  @getScore="getFillBlankScore($event, item)"
                />
              </div>
            </div>
          </template>
        </a-form-item>
        <!-- 排序题 -->
        <a-form-item v-if="formState.type === 6" name="options" class="question-options">
          <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
            <span class="optionContent">选项内容</span>
            <template v-for="(item, index) in formState.options" :key="index">
              <div class="sortingQuestionOptions item-option">
                <a-radio :value="item.value">
                  <span class="option-radio">{{ item.value }}</span>
                </a-radio>
                <a-textarea
                  v-if="!formState.complicatedediting"
                  class="option-content order-option-content"
                  v-model:value="item.content"
                  :auto-size="{ minRows: 1 }"
                  @blur='handleOptionBlur'
                  placeholder="点击，编辑选项"
                />
                <div v-else class="editor-wrapper">
                  <VueQuillEditor
                    ref="optionEditorsRef"
                    v-model:content="item.content"
                    @blur='handleOptionBlur'
                  ></VueQuillEditor>
                </div>
                <svg-icon
                  class="del-icon"
                  name="circle-del"
                  width="16px"
                  height="16px"
                  @click.prevent="delOptionWrap($event, index)"
                />
              </div>
            </template>
            <span class="rightOrder">（按正确顺序设置）</span>
          </a-radio-group>
          <div class="add-option-btn sort-question" @click="addOption">
            <svg-icon name="plus" />
            <span>添加选项</span>
          </div>
        </a-form-item>
        <!-- 复合题 -->
        <a-form-item
          v-else-if="formState.type == 7"
          class="question-options compound"
          name="subQuestionList"
        >
          <a-radio-group v-model:value="formState.answer">
            <a-dropdown trigger="click" class="compoundQuestions">
              <template #overlay>
                <a-menu class="compoundItem">
                  <template v-for="item in questionsObj" :key="item.key">
                    <a-menu-item @click="addCompoundQuestions(item)">{{
                      item.questionCate
                    }}</a-menu-item>
                  </template>
                </a-menu>
              </template>
              <!-- <img src="@/assets/images/add.png" alt=""> -->
              <a-button class="compoundQuestionsBtn flex items-center" type='text'>
                <img src="@/assets/icons/svg/addItem.svg" alt="请选择子试题" class="addItem mr-[3px] mt-[2px]" />
                请选择子试题
              </a-button>
            </a-dropdown>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-show="activeTab === '1'" name="tags" style="width: 220px">
          <a-select
            class="tag-select"
            mode="tags"
            :options="tagsOptions"
            placeholder="添加标签"
            dropdownClassName="tag-select-wrapper"
            :getPopupContainer="
              (triggerNode: any) => {
                return triggerNode.parentNode
            }"
            v-model:value="formState.tags"
          >
          </a-select>
        </a-form-item>
        <template v-if="formState.type === 4">
          <div v-show="activeTab === '1'" class="fill-blank-config">
            
            <div style="margin-bottom: 6px">
              <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
              <span>本题适用于“部分测试用例通过可得分”</span>
              <a-tooltip placement="right">
                <template #title>
                  <span
                    >若勾选“部分测试用例通过可得分”，则算法题得分按通过测试用例占总数的百分比计算；未勾选时，需全部通过测试用例才能得分。</span
                  >
                </template>
                <svg-icon class="common-info-icon" name="info2"></svg-icon>
              </a-tooltip>
            </div>
            <div style="margin-bottom: 4px">
              <a-checkbox v-model:checked="disabledKeywords" class="check-box" />
              <span>存在禁用关键词</span>
              <a-tooltip placement="right">
                <template #title>
                  <span>禁止学生答题时使用一些系统内置的方法或关键字</span>
                </template>
                <svg-icon class="common-info-icon" name="info2"></svg-icon>
              </a-tooltip>
              <div v-if="disabledKeywords" @click="addKeyword" class="set-keyword-btn">批量设置</div>
            </div>
            <a-form-item
              v-if="disabledKeywords"
              label="禁用关键词"
              class="disablekws-wrapper">
              <template v-for="item in Object.keys(formState.disablekws)" :key="item">
                <div class="item">
                  <span class="label">{{ item }}</span>
                  <a-select
                    class="tags-select"
                    v-model:value="formState.disablekws[item]"
                    mode="tags"
                    placeholder="输入完成后按回车添加多个关键词"
                    :getPopupContainer="
                    (triggerNode: any) => {
                      return triggerNode.parentNode
                    }">
                    <a-select-opt-group
                      v-for="group in languageFunctions.find(el => el.language === item)?.groups"
                      :key="group.key"
                      :label="group.label">
                      <a-select-option
                        v-for="func in group.functions"
                        :key="`${group.key}#${func.funcname}`"
                        :value="func.funcname">
                        {{ func.funcname }}
                      </a-select-option>
                    </a-select-opt-group>
                  </a-select>
                </div>
              </template>
              <div class="err-tip" v-if="!Object.values(formState.disablekws).flat().length">请输入禁用关键词</div>
              <div
                v-if="
                  isFnNameEqualKeyword(
                    Object.values(formState.disablekws).flat(),
                    formState.func_name
                  )
                "
                class="err-tip"
              >
                方法名称含有禁用关键词，请修改方法名
              </div>
            </a-form-item>
          </div>
        </template>
        <div v-if="formState.type === 3" class="fill-blank-config">
          <div>
            <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
            <span>忽略大小写</span>
          </div>
        </div>
        <div v-if="formState.type === 1" class="fill-blank-config">
          <div>
            <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
            <span>本题适用于“部分回答正确时可得分”</span>
            <a-tooltip placement="right">
              <template #title>
                <span
                  >当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span
                >
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </div>
        </div>
        <div v-if="formState.type === 5" class="fill-blank-config">
          <div>
            <a-checkbox v-model:checked="formState.ordered" class="check-box" />
            <span>判分时区分答案先后顺序</span>
          </div>
          <div>
            <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
            <span>忽略大小写</span>
          </div>
        </div>
        <div v-if="formState.type === 6" class="fill-blank-config">
          <div>
            <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
            <span>本题适用于“部分回答正确时可得分”</span>
            <a-tooltip placement="right">
              <template #title>
                <span
                  >当考试设置为“部分回答正确时可得分”，并且本题勾选了此选项，则学生的答案中包含连续正确的顺序不少于2个时，可按照比例得分；否则，在没有完全回答正确的情况下不得分。</span
                >
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </div>
        </div>
        <div v-if="![QuestionEnum['算法题']].includes(formState.type)" style="display: flex;align-items: center;margin-top: 16px;">
          <a-button :loading="getAiAnswerLoading" class="common-ai-button" style="font-size: 14px; margin-left: 2px;border-radius: 8px;" @click="handleGetAiAnswer">
            <template #icon><img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;" /></template>
            答题
          </a-button>
          <a-tooltip placement="right" overlayClassName="light">
            <template #title>
              <span>自动生成当前题目的答案解析</span>
            </template>
            <svg-icon class="common-info-icon" name="info2"></svg-icon>
          </a-tooltip>
        </div>
        <template v-if="aiAnswer || aiExplain || (aiResult && aiResult.length)">
          <template v-for="({answer: aiAnswer, explain:aiExplain}) in aiResult" :key="aiAnswer">
            <div class="ai-answer-panel">
            <div>
              <div class="label">AI 答案</div>
              <div class="value">{{ aiAnswer || '-' }}</div>
            </div>
            <div style="margin-top: 8px;">
              <div class="label">答题解析</div>
              <div class="value">
                <FoldText
                    :text="aiExplain" 
                    :fold-line-clamp="3" 
                    background-color="#f5f5f5"
                    :feedback-params="{
                        apiname: 'getaianswer',
                        input: getaianswerInput,
                        output: {
                            answer: aiAnswer,
                            explain: aiExplain
                        }
                    }"
                ></FoldText>
              </div>
            </div>
          </div>
          </template>

          <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
            <template #icon><InfoCircleFilled /></template>
            <template #message>
              温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
            </template>
          </a-alert>
        </template>
      </a-form>
      <div class="footer">
        <a-button class="save-btn" :loading="addLoading" :disabled='disalbedBth' @click="handleSaveAdd">
          {{ props.draftId ? '发布' : '保存' }}
        </a-button>
        <a-button class="submit-btn" @click="handleSaveDraft" :loading="loading">
          保存草稿箱
        </a-button>
        <a-button @click="handleCancel">取消</a-button>
      </div>
      <div
        class="ai-aide-wrap"
        v-if="[0, 1, 2, 3, 5].includes(formState.type)"
        ref="aiAideRef"
        :style="aiCreateCircleStyle">
        <img
          src="@/assets/images/ai-aide.png"
          class="ai-aide"
          alt="">
        <div class="tip">Hi, 点我试试</div>
      </div>
    </div>
    <!-- 设置禁用关键词 -->
    <SetKeyword
      :visible="setKeywordVisible"
      :checkedFunctions="formState.disablekws"
      @close="setKeywordVisible = false"
      @ok="handleSetKeywordOk" />
    <a-modal title="生成干扰项" v-model:visible="generateOptionModalVisible" @ok="handleGenerateOptionsChoose" @cancel="optionGeneratorRef?.setList([])" :destroyOnClose="true" :keyboard="false" :maskClosable="false">
      <OptionGenerator ref="optionGeneratorRef" :generateParams="generateParams"></OptionGenerator>
    </a-modal>
    <!-- ai助手 -->
    <AiAideModal :visible="aiAideVisible" :type="formState.type" @close="aiAideVisible = false" @created="handleAiCreated" />
  </div>
</template>

<script lang="ts" setup>
import { QuestionEnum, questionType } from '@/models/questionModel'
// import { tagsMange } from '@/api/admin/tagsManage'
import { onMounted, ref, watch, nextTick, watchEffect, onBeforeUnmount, computed, createVNode } from 'vue'
import ScoreInput from './components/ScoreInput.vue'
import ScoreInput2 from './components/ScoreInput2.vue'
import ParamList from './components/ParamList.vue'
import TestInstance from './components/TestInstance.vue'
import { alltags, subjcatgs, ojtemplate, draftquesbyid } from '@/api/admin/questionManage'
import { getAiAnswer } from '@/api/exam/index'
import {
  numberAlpht,
  lettersAlpht,
  judge,
  options,
  sourceForm,
  replaceFields,
  questionsObj
} from './data'
import {
  checkType,
  checkCategory,
  checkOptions,
  checkBody,
  checkRtype,
  checkDisablekws,
  checkParameters,
  checkBtestcase,
  checkPtestcase,
  checkVariateName,
  CHECK_VARIATE_NAME_REG
} from './rules'
import { message, Modal } from 'ant-design-vue'
import { onBeforeRouteLeave } from 'vue-router'
import SingleChoice from './components/SingleChoice.vue'
import MultipleChoice from './components/MultipleChoice.vue'
import JudgingQuestions from './components/JudgingQuestions.vue'
import BlankFilling from './components/BlankFilling.vue'
import JsonEditorVue from 'json-editor-vue'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import 'vue-json-pretty/lib/styles.css'
import QuestionAndAnswer from './components/QuestionAndAnswer.vue'
import OrderQuestion from './components/OrderQuestion.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import { useStore } from 'vuex'
import useWatchParameters from './hooks/useWatchParameters'
import useWatchType from './hooks/useWatchType'
import useCompose from './hooks/useCompose'
import useAlgorithm from './hooks/useAlgorithm'
import useSubmitQuestion from './hooks/useSubmitQuestion'
import useOption from './hooks/useOption'
import useGetPoints from './hooks/useGetPoints'
import useWatchBody from './hooks/useWatchBody'
import useGetScore from './hooks/useGetScore'
import JSwitch from '@/components/JSwitch.vue'
import { Form } from 'ant-design-vue'
import ValidateAlgorithm from './components/ValidateAlgorithm.vue'
import JTagInput from '@/components/JTagInput.vue'
import { hasImageOrFormula, uuid } from '@/utils'
import { CloseOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import SetKeyword from '@/pages/questionManage/components/SetKeyword.vue'
import { LanguageFunc } from '@/models/questionModel'
import languageFunctions from '@/config/languageFunctions'
import OptionGenerator from '@/pages/questionManage/QuestionForm/components/OptionGenerator.vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'

import { useDraggable, useEventListener, useWindowSize } from '@vueuse/core'
import AiAideModal from './components/AiAideModal.vue'
import FoldText from '@/components/FoldText.vue'
import { checkScore } from '@/utils/validate'
import emitter from '@/utils/bus'

const props = defineProps<{
  draftId?: any
  subjectId?: string // 所属题库、
  subjectUUID?: string // 所属题库、
}>()

const emits = defineEmits<{
  (e: 'close', needRefresh: boolean): void
}>()

const disalbedBth = ref(false)
provide('ImageAlignActionHook',{
  before:() =>{
    disalbedBth.value = true
  },
  after:() => {
    disalbedBth.value = false
  }
})

// 对浏览器自带的返回按钮的处理
onBeforeRouteLeave(async (to, from, next) => {
  if (!isSubmit.value) {
    await showConfirmExitModal()
    next()
  } else {
    next()
  }
})

useEventListener('beforeunload', (event) => {
  if (!isSubmit.value) {
    event.preventDefault()
  }
})

// 询问用户是否确认退出编辑
function showConfirmExitModal() {
  return new Promise((resolve, reject) => {
    if (!formState.value.body.trim()) {
      Modal.confirm({
        title: '确定退出此页面？',
        icon: () => createVNode(InfoCircleFilled),
        content: '系统可能不会保存您所做的更改',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          resolve('')
        }
      })
    } else {
      const modal = Modal.confirm({
        title: '是否需要保存草稿箱？',
        content: '直接退出系统不会保存当前内容',
        okText: '保存草稿箱',
        cancelText: '直接退出',
        closable: true,
        closeIcon: '取消',
        class: 'draft-cancel',
        async onOk() {
          await handleSaveDraft()
          resolve('')
        },
        onCancel(e) {
          if (!e.triggerCancel) {
            modal.destroy()
            resolve('')
          }
        }
      })
    }
  })
}

// 关闭事件
async function handleCancel() {
  if (!isSubmit.value) {
    await showConfirmExitModal()
    emits('close', true) // 保存草稿弹框后，如果是草稿箱列表需要刷新（处理了true），题目列表不需要刷新（父组件没处理）
  } else {
    emits('close', false)
  }
}

const useForm = Form.useForm

const subQuestionList = ref<any>([])

const varType = [
  { label: 'string', value: 'string' },
  { label: 'int', value: 'int' }
]

const categoryList = ref<any>([])
const disabledKeywords = ref(false)

const defaultOptions = [
  {
    content: '',
    value: 'A'
  },
  {
    content: '',
    value: 'B'
  },
  {
    content: '',
    value: 'C'
  },
  {
    content: '',
    value: 'D'
  }
]
const scorepts = ref<any>([{ keyword: [], score: 0 }])
const formState = ref<{
  disablekws: LanguageFunc
  [propName: string]: any
}>({
  children: [],
  type: 0,
  category: undefined,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  func_name: '',
  scorebasis: '',
  rtype: { type: undefined, is_list: false },
  difficulty: '',
  parameters: [{ name: '', type: '', is_list: false }],
  btestcase: [{ input: [], output: [] }],
  ptestcase: [{ input: [], output: [] }],
  sepscore: true,
  ordered: true,
  ignorecase: true,
  complicatedediting: false,
  disablekws: {
    C: [],
    'C++': [],
    Java: [],
    Python: [],
    JavaScript: [],
    Go: []
  },

  options: [
    {
      content: '',
      value: 'A'
    },
    {
      content: '',
      value: 'B'
    },
    {
      content: '',
      value: 'C'
    },
    {
      content: '',
      value: 'D'
    }
  ],
  tags: [],
  validatecode: null
})
const createQuestionFormRef = ref()
const scoreInputRef = ref()
const isSubmit = ref(false) // 表单是否发生变化， 否是有, 有变化返回上一页就会给提示

const { checkSubQuestionList, addCompoundQuestions, handleDelete, handleUp, handleDown } =
  useCompose(subQuestionList)

// 校验规则
const rules:any = {
  type: [{ required: true, validator: checkType, trigger: 'change' }],
  category: [{ required: true, validator: checkCategory, trigger: 'blur' }],
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }],
  complexcontent: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }],
  subQuestionList: [{ required: true, validator: checkSubQuestionList, trigger: 'blur' }],
  func_name: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  rtype: [{ required: true, validator: checkRtype, trigger: 'blur' }],
  difficulty: [{ required: true, message: '请选择难度', trigger: 'change', type: 'number' }],
  disablekws: [{ required: true, validator: checkDisablekws, trigger: 'blur' }],
  parameters: [{ required: true, validator: checkParameters, trigger: 'blur' }],
  btestcase: [{ required: true, validator: checkBtestcase, trigger: 'blur' }],
  ptestcase: [{ required: true, validator: checkPtestcase, trigger: 'blur' }]
}

const { validate, validateInfos } = useForm(formState, rules)

// 判断方法名是否是禁用关键词中的一个
function isFnNameEqualKeyword(keywords: string[], fnName: string) {
  return keywords.includes?.(fnName)
}

const store = useStore()
async function getSubjectData() {
  try {
    let res = await subjcatgs({ action: 'query' })
    categoryList.value = JSON.parse(JSON.stringify(res))[0].children
    // res.forEach((item) =>{
    //     if(item.name = '部门题库'){
    //         categoryList.value = item.children
    //     }
    // }) 
    if (!store.getters.userInfo.is_official_cert) {
      categoryList.value = categoryList.value.filter((item: any) => !item.is_official_cert)
    }
    // 编辑草稿
    if (props.draftId) return
    
    await nextTick()
    const flag = props.subjectId ? findSubjectId(props.subjectId, categoryList.value) : false
    formState.value.category = flag ? props.subjectUUID : undefined
  } catch (error) {
    console.log(error)
  }
}

// 监听题库切换
// watch(
//   () => formState.value.category,
//   (val) => {
//     const flag = findSubjectId(val, categoryList.value)
//     formState.value.category = flag ? val : undefined
//   }
// )

const findSubjectId = (id: string, subjectTree: any) => {
  let result = false
  for (let i = 0; i < subjectTree.length; i++) {
    const item = subjectTree[i]
    if (item.id === id) {
      return true // 如果找到了匹配的id，立即返回true
    } else if (item.children && item.children.length) {
      // 如果有子节点，则递归查找
      result = findSubjectId(id, item.children)
      if (result) {
        return true // 如果子节点中找到了匹配的id，立即返回true
      }
    }
  }
  return result
}

const scoreInputTotalRef = ref()
const validateAlgorithmRef = ref()
const fillBlank = ref<any>([])
const fillBlankCount = ref(0)
let oldFillBlank = ref('')

const { getScore, getFillBlankScore, getPointsScore } = useGetScore(
  formState,
  fillBlank,
  scorepts,
  scoreInputTotalRef
)
const {
  handleAddParameter,
  handleDelParameter,
  handleAddInstance,
  handleDelInstance,
  addTestCase,
  delTestCase
} = useAlgorithm(formState)

// 自动保存至草稿箱
// const { uuid, realtime } = useSaveDraft(formState)

const clearValidateAlgorithm = () => {
  funcTemplateObj.value = {
    c: '',
    cpp: '',
    java: '',
    js: '',
    python: '',
    go: ''
  }
  Object.keys(validateAlgorithmRef.value.langMap).forEach((key) => {
    validateAlgorithmRef.value.langMap[key].showTip = false
  })
}
const activeTab = ref('1')
const { loading, addLoading, saveAdd, saveDraft } = useSubmitQuestion(
  formState,
  subQuestionList,
  createQuestionFormRef,
  isSubmit,
  scorepts,
  fillBlank,
  fillBlankCount,
  oldFillBlank,
  scoreInputTotalRef,
  clearValidateAlgorithm,
  disabledKeywords,
  activeTab,
  props,
  emits
)

// 保存并新增
const handleSaveAdd = async () => {
  addLoading.value = true
  const res =
    disabledKeywords.value &&
    (!Object.values(formState.value.disablekws).flat().length || isFnNameEqualKeyword(Object.values(formState.value.disablekws).flat(), formState.value.func_name))
  if (res) {
    // 高级录入
    if (activeTab.value === '2') {
      message.error('题目表单校验失败,请检查输入项内容')
    }
    addLoading.value = false
    return
  }
  try {
    // 如果算法题不存在禁用关键词，则将禁用关键词清空
    if (!disabledKeywords.value && formState.value.type == QuestionEnum['算法题']) {
      Object.keys(formState.value.disablekws).forEach(key => formState.value.disablekws[key] = [])
    }
    let cb = formState.value.complicatedediting ? handleComplexContentBlur : handleBodyBlur
    await saveAdd(cb, validateAlgorithmRef, clearAiAnswer)

    if (props.draftId) {
      // 如果是草稿箱的发布需要通知更新试题列表（虽然次数不一定是在提交成功后才会执行，但是之前的代码太乱，暂时先这样，多执行一次也不坏）
      emitter.emit('updateQuestionList')
    }
  } catch (error) {
    console.log(error)
  }
}

const handleSaveDraft = async () => {
  if (!formState.value.body.trim()) {
    message.error('题干内容不能为空')
    return
  }
  try {
    if (!disabledKeywords.value && formState.value.type == QuestionEnum['算法题']) {
      Object.keys(formState.value.disablekws).forEach(key => formState.value.disablekws[key] = [])
    }
    await saveDraft(validateAlgorithmRef, clearAiAnswer)
  } catch (error) {
    console.log(error)
  }
}

const { addOption, delOption } = useOption(formState)
const { addPoints, delPoints } = useGetPoints(formState, scorepts, scoreInputTotalRef)
const delOptionWrap = (a,b) => {
  delOption(a,b)
  createQuestionFormRef.value.validateFields(['options'])
}
useWatchBody(formState, fillBlank, fillBlankCount, oldFillBlank)

watch(
  () => formState.value.sepscore,
  (val) => {
    let totalScore = 0
    if (val) {
      if (formState.value.type === 5) {
        fillBlank.value.forEach((item: any) => {
          totalScore += Number(item.score)
          formState.value.score = totalScore
        })
      }
    }
  }
)

useWatchParameters(formState)

watch(
  () => subQuestionList,
  (val) => {
    formState.value.score = 0
    val.value.forEach((item: any) => {
      formState.value.score += Number(item.formState.score || 0)
    })
  },
  {
    deep: true
  }
)

const funcMainObj = ref({})
const funcMainParams = ref({})
const funcTemplateObj = ref({})
provide('funcTemplateObj',funcTemplateObj)
// 监听方法名称、返回值类型、参数列表的变化，获取oj模板
watch([() => formState.value.func_name, () => formState.value.rtype, () => formState.value.parameters], async ([func_name, rtype, parameters]) => {
    // 有效的方法名称
    const validFuncname = CHECK_VARIATE_NAME_REG.test(formState.value.func_name) && !isFnNameEqualKeyword(Object.values(formState.value.disablekws).flat(), formState.value.func_name)
    // 有效的返回值类型
    const validRtype = !!formState.value.rtype.type
    // 有效的参数列表
    let validParameters
    try {
      await checkParameters({}, formState.value.parameters)
      validParameters = true
    } catch (error) {
      validParameters = false
    }
    if (validFuncname && validRtype && validParameters) {
      getOjTemplate()
    }
  }, {
    deep: true,
    immediate: true
  })

/** 获取oj模板 */
async function getOjTemplate() {
  funcMainParams.value = {
    funcName: formState.value.func_name,
    rtype: JSON.stringify(formState.value.rtype),
    parameters: JSON.stringify(formState.value.parameters)
  }
  const result: any = await ojtemplate(funcMainParams.value)
  funcMainObj.value = result.func_main
  funcTemplateObj.value = pasteValidatecode || result.func_templete
  pasteValidatecode = null
}

watch(
  () => formState.value.validatecode,
  (val) => {
    if (activeTab.value === '2' && !pasteValidatecode) {
      nextTick(() => {
        funcTemplateObj.value = val
      })
    }
  },
  {
    deep: true
  }
)

const updateTemplate = async (lang: string, item: any) => {
  const result: any = await ojtemplate({
    func_name: formState.value.func_name,
    rtype: formState.value.rtype,
    parameters: formState.value.parameters
  })
  funcTemplateObj.value[item] = JSON.parse(JSON.stringify(result.func_templete[item]))
  validateAlgorithmRef.value.langMap[lang].counter++
}

const updateValidateCode = (validatecode: any) => {
  formState.value.validatecode = validatecode
}

const jsonEditorRef = ref(null)
const showJsonEditor = ref(true)
watch(
  formState,
  () => {
    if (loadedFinish.value) {
      isSubmit.value = false
    }
    if (jsonEditorRef.value) {
      showJsonEditor.value = false
      nextTick(() => {
        showJsonEditor.value = true
      })
    }
  },
  { deep: true }
)

useWatchType(formState, scoreInputTotalRef, options, judge)

const handleBodyBlur = () => {
  return validate('body', { trigger: 'blur' })
}
const handleComplexContentBlur = () => {
  return validate('complexcontent', { trigger: 'blur' })
}
const handleOptionBlur = () =>{
  createQuestionFormRef.value.validateFields(['options'])
}
const tagsList = ref<any>([])
const tagsOptions = ref<any>([])
const getAllTags = () => {
  alltags({ tag: 'question', /** categoryId: props.subjectId **/ }).then((res: any) => {
    tagsList.value = res
    tagsOptions.value = res.map((item: any) => ({ value: item.name, label: item.name }))
  })
}

const subQuestionsRef = ref()
const handleComplexSwitchChange = (formState: any, value: boolean, index?: number) => {
  if (!value) {
    try {
      handleComplexToNormal(formState)
      if (subQuestionsRef.value?.length && index !== undefined) {
        subQuestionsRef.value[index].handleComplexToNormal?.(formState)
      }
    } catch (error) {
      console.log(error)
      message.error('出错了，请刷新重试')
    }
  } else {
    formState.complexcontent = formState.body.replaceAll(/</gi, '&lt;').replaceAll(/>/gi, '&gt;').split('\n').reduce((acc: string, cur: string) => acc += `<p>${cur}</p>`)
  }
}

const scorebasisEditorRef = ref()
const optionEditorsRef = ref()
const handleComplexToNormal = (formState: any) => {
  if (optionEditorsRef.value && optionEditorsRef.value.length) {
    formState.options.forEach((option: any, index: number) => {
      option.content = optionEditorsRef.value[index].getText()
    })
  }
  if (scorebasisEditorRef.value) {
    formState.scorebasis = scorebasisEditorRef.value.getText()
  }
}
function convertFieldJsonContent(data, toJson = false) {
    const defaultKeys = ['options', 'disablekws', 'funcMain', 'funcTemplate', 'parameters', 'ptestcase', 'rtype', 'btestcase', 'validatecode']
    defaultKeys.forEach(key => {
      if (toJson) {
        if (data[key]) {
          const _temp = data[key]
          data[key] = JSON.stringify(_temp)
          data[`${key}Json`] = _temp
        }
      } else {
        if (data[`${key}Json`]) {
          data[`${key}`] = data[`${key}Json`]
        }
      }
    })
    data.func_name = data.funcName 
    data.func_templete = data.funcTemplete
}
function convertBoolean(data) {
  Object.keys(data).forEach(key => {
    if (typeof data[key] === 'boolean') {
      data[key] = data[key] ? 1 : 0
    }
  })
}
// 查看草稿内容
const loadedFinish = ref(false) // 是否回填完草稿箱详情数据，回填之后formstate变化isSubmit才会变化
function getDraftDetail() {
  if (props.draftId) {
    draftquesbyid({ id: props.draftId }).then((res: any) => {

      convertFieldJsonContent(res.questionBankWrite)
      convertBoolean(res.questionBankWrite)

      // res.questionBankWrite.categoryUUID = res.questionBankWrite.category
      res.questionBankWrite.tags = res.questionBankWrite.tagList ? res.questionBankWrite.tagList.filter(item => item != "[]") : []
      pasteValidatecode = res.questionBankWrite.validatecode

      formState.value = res.questionBankWrite
      const score = res.questionBankWrite.score
      if (formState.value.type == QuestionEnum['算法题']) {
        disabledKeywords.value = Boolean(Object.values(formState.value.disablekws).flat().length)
      }
      setTimeout(() => {
        scoreInputTotalRef.value.score = score
        formState.value.options = res.questionBankWrite.options
        formState.value.answer = res.questionBankWrite.answer
        if (formState.value.type === 5) {
          let answer = JSON.parse(res.questionBankWrite.answer)
          answer.forEach((item: any) => {
            item.id = uuid()
          })
          fillBlank.value = answer
        }
        formState.value.type === 3 && (scorepts.value = JSON.parse(res.questionBankWrite.answer))
        // funcTemplateObj.value = formState.value.validatecode
        nextTick(() => {
          // loadedFinish.value = true
          const flag = findSubjectId(res.category, categoryList.value)
          formState.value.category = flag ? res.category : undefined
          if (formState.value.type === 4) {
            delete formState.value.options
          }
          nextTick(() => {
            loadedFinish.value = true
          })
          formState.value.category = formState.value.categoryUUID
        })
      }, 700)
      // nextTick(() => {})
    })
  } else {
    loadedFinish.value = true
  }
}

let pasteValidatecode: any = null
const pasteHandler = (event: Event) => {
  // @ts-ignore
  const clipboardData = event.clipboardData || window.clipboardData
  const pastedText = clipboardData.getData('text')
  if (pastedText.indexOf('validatecode') > -1) {
    const data = JSON.parse(pastedText)
    if (data.validatecode) {
      pasteValidatecode = data.validatecode
    }
  }
}

// 请求题库树后再请求草稿详情
getSubjectData().then(getDraftDetail)


onMounted(() => {
  getAllTags()
  isSubmit.value = true
  document.addEventListener('paste', pasteHandler)
})

onBeforeUnmount(() => {
  document.removeEventListener('paste', pasteHandler)
})

// 题型切换 
function handleTypeChange(type: any):any {
  clearAiAnswer()
  if ([3, 5].includes(type)) {
    formState.value.sepscore = true
  }
  setTimeout(calcTotalScore, 1000);
}

// 计算题目总分
function calcTotalScore() {
  if (formState.value.type === 3) {
    formState.value.score = scorepts.value.reduce((acc: number, cur: any) => acc + cur.score, 0)
  } else if (formState.value.type === 5) {
    formState.value.score = fillBlank.value.reduce((acc: number, cur: any) => acc + cur.score, 0)
  }
}

// 插入空位
const bodyInputRef = ref()
const bodyEditorRef = ref<InstanceType<typeof VueQuillEditor>>()
function insetBlank() {
  let TEXT = '__ '
  if (formState.value.complicatedediting) {
    bodyEditorRef.value?.insetText(TEXT)
  } else {
    let textareaEle = bodyInputRef.value.$el
    const startPos = textareaEle.selectionStart
    const endPos = textareaEle.selectionEnd
    let rawText = formState.value.body
    formState.value.body = rawText.substring(0, startPos) + TEXT + rawText.substring(endPos)
  }
}

// 添加关键词
const setKeywordVisible = ref<boolean>(false)
const addKeyword = () => setKeywordVisible.value = true
const handleSetKeywordOk = (data: LanguageFunc) => {
  formState.value.disablekws = data
  setKeywordVisible.value = false
}

// —————————————————————————————————————————————————— AI 生成干扰项 ———————————————————————————————————————————————————————
const generateOptionModalVisible = ref(false)
const generateParams = ref()
const optionGeneratorRef = ref<InstanceType<typeof OptionGenerator>>()
function showGenerateOptionModal() {
  let type = formState.value.type
  let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  let options = formState.value.options.map((option: any, index: number) => ({ content: option.content, isTrue: formState.value.answer?.includes(option.value) })).filter((i: any) => !!i.content)
  if (![QuestionEnum['单选题'], QuestionEnum['多选题']].includes(type)) {
    return message.error('只有单选题和多选题才能生成干扰项')
  }
  if (!body) {
    return message.error('题干内容不能为空')
  }
  if (options.filter((i: any) => i.isTrue).length === 0) {
    return message.error('使用该功能需要先输入正确的选项，如果您已经输入了正确的选项，请勾选它。')
  }
  if (options.some((i: any) => hasImageOrFormula(i.content))) {
    return message.error('很抱歉，由于AI能力限制，暂不支持基于公式或图片的干扰项生成。')
  }
  // 剔除所有选项的所有标签，将纯文本提交
  options = options.map((option: any, index: number) => {
    let content = formState.value.complicatedediting ? optionEditorsRef.value[index].getText() : option.content
    return { content, ...option }
  })
  generateParams.value = { type, body, answer:formState.value.answer, optionJson: options }
  generateOptionModalVisible.value = true
}

function handleGenerateOptionsChoose() {
  let arr = optionGeneratorRef.value?.list.filter(i => i.checked)
  if (!arr?.length) return message.error('请至少选择一个干扰项')
  let options = formState.value.options
  let arrIndex = 0, optionIndex = 0;
  while (optionIndex < options.length && arrIndex < arr.length) {
    if (options[optionIndex].content.trim() === '') {
      options[optionIndex].content = arr[arrIndex].content
      arrIndex++
      continue
    }
    optionIndex++
  }
  let restOptionNum = 11 - options.length  // 最多可添加11个
  // 如果arr剩下的比option剩下还多，就提示
  if (restOptionNum < arr.length - arrIndex) {
    message.warning('已达最多选项个数')
  }
  
  // 如果有多余干扰项，则直接添加到选项末尾
  if (arrIndex < arr.length && restOptionNum > 0) {
    options.push(...arr.slice(arrIndex, arrIndex + restOptionNum).map((i, k) => ({ content: i.content, value: lettersAlpht[options.length + k] })))
  }
  generateOptionModalVisible.value = false
}


// —————————————————————————————————————————————————— AI 答题 ———————————————————————————————————————————————————————
const aiAnswer = ref('')
const aiExplain = ref('')
const aiResult = ref<any>(null)
const getAiAnswerLoading = ref(false)
const getaianswerInput = computed(() => {
    let type = formState.value.type
    let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
    let options = formState.value.options
    return { body, type, options }
})
function clearAiAnswer() {
  aiAnswer.value = ''
  aiExplain.value = ''
  aiResult.value = null
}
async function handleGetAiAnswer() {
  clearAiAnswer()
  let type = formState.value.type
  let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  if (!body) return message.error('题干内容不能为空')
  if (!formState.value.options?.filter((i: any) => i.content.trim())?.length && [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['排序题']].includes(type)) return message.error('选项内容不能都为空')
  getAiAnswerLoading.value = true
  try {
    let res:any = await getAiAnswer({ body, type, options: formState.value.options })
    aiResult.value = res.results
    // aiAnswer.value = res.answer
    // aiExplain.value = res.explain
  } catch (error) {
    console.log(error)
  } finally {
    getAiAnswerLoading.value = false
  }
}

// ai创建题目悬浮球，可拖拽
const aiAideVisible = ref(false)
const aiAideRef = ref<HTMLElement | null>(null)
let dragStartTimestamp = 0
const { x, y, style: aiCreateCircleStyle } = useDraggable(aiAideRef, {
  initialValue: { x: window.innerWidth - 200, y: 240 },
  onMove(position, event) {
      x.value = Math.max(0, Math.min(position.x, windowWidth.value - 80))
      y.value = Math.max(0, Math.min(position.y, windowHeight.value - 80))
  },
  preventDefault: true,
  onStart() {
    dragStartTimestamp = Date.now()
  },
  onEnd() {
    let dragDuration = Date.now() - dragStartTimestamp
    if (dragDuration <= 200) {
      // 鼠标松开和按下间隔200ms以内认为是点击，打开弹窗
      aiAideVisible.value = true
    }
  }
})

const { width: windowWidth, height: windowHeight } = useWindowSize()

watch([windowWidth, windowHeight], ([w, h]) => {
  // 0 ~ w - 80
  // 0 ~ h - 80
  x.value = Math.min(x.value, w - 80)
  y.value = Math.min(y.value, h - 80)
})

const handleAiCreated = (value: any) => { // ai生成题目，填充信息
  aiAideVisible.value = false
  formState.value = {
    ...formState.value,
    body: value?.body || '',
    complexcontent: formState.value.complicatedediting ? '<p>'+ value?.body +'</p>' : '',
    answer: value?.answer || '',
    options: Array.isArray(value?.options) ? value.options : defaultOptions,
    scorebasis: value?.scorebasis || '',
  }
  if ([3, 5].includes(formState.value.type)) {
    let answer = []
    try {
      answer = typeof value?.answer == 'string' ? JSON.parse(value?.answer) : value?.answer
      answer = Array.isArray(answer) ? answer : []
    } catch (error) {
      // do nothing
    }
    let totalScore = 0
    answer.forEach((item: any) => {
      item.id = uuid()
      totalScore += item.score
    })
    formState.value.score = totalScore
    if (formState.value.type === 5) {
      fillBlankCount.value = answer.length
      fillBlank.value = answer
    }
    if (formState.value.type === 3) {
      scorepts.value = answer
    }
  }
}


</script>

<style lang="less" scoped>
.ai-answer-panel {
  background-color: #f5f5f5;
  width: 752px;
  border-radius: 8px;
  padding: 16px 48px;
  margin-top: 16px;
  > div {
    display: flex;
    .label {
      width: 80px;
      font-size: 12px;
      color: #626262;
      margin-right: 16px;
    }
    .value {
      flex: 1;
      min-width: 0;
      font-size: 12px;
    }
  }
}
:deep(.tagify) {
  border: none;
  font-size: 12px;
  align-items: center;
  .tagify__input {
    &::before {
      font-size: 12px;
    }
  }
  .tagify__tag-text {
    font-size: 12px;
  }
}
:deep(.ant-tree-select-dropdown) {
  .ant-select-tree-node-content-wrapper {
    display: flex;
    align-items: center;
    flex-shrink: 0 !important;
  }
}
.err-tip {
  font-size: 14px;
  color: #f5222d;
}
.addItem {
  margin-top: -1px;
}
:deep(.ant-collapse-item) {
  background-color: #f2f5fc;
}
:deep(.ant-collapse-header) {
  font-size: 12px;
}
:deep(.ant-dropdown-menu-item-only-child) {
  font-size: 12px;
}
:deep(.ant-dropdown-menu-item-only-child):hover {
  background-color: #f1f4fe;
}
:deep(.sortingQuestionOptions .ant-radio) {
  display: none;
}
.ant-form label {
  font-size: 12px;
}
.create-question-container {
  position: relative;
  .close-btn {
    position: absolute;
    top: -60px;
    font-size: 18px;
    right: 10px;
    cursor: pointer;
    color: #929294;
    transition: all ease 0.2s;
    &:hover {
      color: #696969;
    }
  }
  height: 100%;
  padding: 0 20px;
  .create-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .create-question-content {
    display: flex;
    flex-direction: column;
    position: relative;
    min-width: 968px;
    overflow: auto;
    height: 100%;
    background-color: #fff;
    .form-input {
      width: 220px;
      height: 32px;
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.tag-select .ant-select-selector) {
      width: 220px;
      border-radius: 8px;
      font-size: 12px;
    }

    :deep(.ant-select-tree-title) {
      font-size: 12px;
    }
    :deep(.ant-form-item-label > label) {
      font-size: 12px;
      height: 26px;
      color: rgba(0, 0, 0, 0.65);
    }
    :deep(.ant-select-selector) {
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(textarea.ant-input) {
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.ant-tabs-tab) {
      font-size: 12px;
      background-color: #f5f5f5;
    }
    :deep(.ant-tabs-tab-active){
      color: #5478EE;
      background: #fff;
    }

    .form {
      margin-bottom: auto;
      flex: 1;
      min-height: 0;
      overflow: hidden scroll;
      padding-right: 20px;
      :deep(.ant-col) {
        width: 80px;
      }
      .disablekws-wrapper {
        display: flex;
        align-items: flex-start;
        .item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          line-height: 26px;
          &:last-of-type {
            margin-bottom: 0;
          }
          .label {
            width: 70px;
            line-height: 32px;
          }
        }
        .tags-select {
          display: block;
          width: 520px;
          :deep(.ant-select-selection-item-content) {
            font-size: 12px;
          }
        }
      }
      .standard-tip {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
      .points-wrapper {
        .item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-size: 12px;
          .index {
            flex: none;
            width: 20px;
            height: 28px;
            text-align: left;
            line-height: 28px;
          }
          .tags-select {
            width: 80%;
            margin: 0 16px 0 6px;
          }
          :deep(.ant-select-selection-item) {
            font-size: 12px;
          }
        }
        .score-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          margin-right: 16px;
          .score {
            flex-grow: 0;
            flex-shrink: 0;
            margin-right: 16px;
          }
          .score-input {
            flex-grow: 0;
            flex-shrink: 0;
          }
        }
        .del-icon-wrap {
          flex: none;
          width: 16px;
          height: 32px;
          display: flex;
          align-items: center;
          .del-icon {
            cursor: pointer;
          }
        }
        .addpoints-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 82px;
          height: 24px;
          background: #ffffff;
          border: 1px solid rgba(0, 0, 0, 0.15);
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          margin-top: 10px;
        }
      }
      .complex-switch-box {
        padding: 0 5px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
        margin-right: 20px;
        .label {
          margin-right: 10px;
        }
      }
    }
    .func-name-wrapper,
    .rtype-wrapper {
      display: flex;
      align-items: center;
      .func-extra-info {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-left: 12px;
      }
      .rtype-extra-info {
        font-size: 12px;
      }
      .form-checkbox {
        position: relative;
        bottom: -1px;
        margin: 0 10px 0 12px;
      }
    }

    .fill-blank-config {
      div {
        display: flex;
        align-items: center;
        line-height: 28px;
      }
      .check-box {
        margin-right: 8px;
      }
      .set-keyword-btn {
        width: 70px;
        height: 24px;
        margin-left: 16px;
        border: 1px solid rgba(0,0,0,0.15);
        border-radius: 4px;
        justify-content: center;
        font-size: 12px;
        color: rgba(0,0,0,0.65);
        text-align: center;
        line-height: 23px;
        cursor: pointer;
      }
      span {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
      }
      .tip-icon {
        margin-left: 8px;
      }
    }
    .body-tip {
      display: flex;
      align-items: center;
      background: #f1f4fe;
      border-radius: 4px;
      font-size: 12px;
      line-height: 24px;
      margin-top: 8px;
      .tip-icon {
        margin: 0 8px;
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    align-items: center;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
    .ant-btn {
      padding: 5px 16px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
    }
    .save-btn {
      background: #5478ee;
      color: #fff;
      margin-right: 8px;
      border-color: transparent;
    }
    .submit-btn {
      border: 1px solid rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.65);
      margin-right: 8px;
    }
  }
  .ai-aide-wrap {
    position: fixed;

    &:hover .tip {
      opacity: 1;
      animation: sway .3s ease-in-out;
    }

    .tip {
      transition: all ease .2s;
      opacity: 0;
      position: absolute;
      top: -30px;
      left: -32px;
      width: 88px;
      height: 27px;
      background: #ffffff;
      box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.20);
      border-radius: 8px;
      font-size: 12px;
      color: rgba(0,0,0,0.65);
      text-align: center;
      line-height: 27px;
      &::after {
        content: '';
        display: block;
        position: absolute;
        bottom: -6px;
        right: 9px;
        width:0;
        height:0;
        border-right:8px solid transparent;
        border-left:8px solid transparent;
        border-top:8px solid #fff;
      }
    }
    .ai-aide {
      width: 80px;
      height: 80px;
      cursor: pointer;
    }
  }
}
.compound {
  margin-top: 24px;
}

.question-options {
  .sortingQuestionOptions {
    margin-left: 72px;
    margin-top: -5px;
  }
  .rightOrder {
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.5);
    margin-left: 70px;
  }
  .optionContent {
    position: absolute;
    width: 48px;
    height: 18px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #626262;
    line-height: 18px;
  }
  .compoundQuestions {
    width: 120px;
    height: 32px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #5478ee;
    line-height: 18px;
    border-radius: 8px;
  }
  .item-option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    position: relative;
    max-width: 100%;
    .option-radio {
      // margin-right: 18px;
      display: inline-block;
      width: 20px;
    }
    .option-content {
      font-size: 12px;
      border: none;
      box-shadow: none;
      width: 220px;
    }
    .order-option-content {
      width: 220px;
    }
    .editor-wrapper {
      padding: 3px 11px;
      min-width: 0;
    }
    .del-icon {
      visibility: hidden;
      cursor: pointer;
      flex-shrink: 0;
    }
  }
  .fill-blank-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .label {
      width: 70px;
      font-size: 12px;
    }
    .score-wrapper {
      width: 160px;
      // min-width: 160px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .score {
        flex-grow: 0;
        flex-shrink: 0;
      }
      .score-input {
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
    :deep(.ant-select-show-search) {
      // width: 600px !important;
    }
    :deep(.ant-select-selector) {
      border: none;
      box-shadow: none;
    }
  }

  :deep(.item-option > span:nth-child(2)) {
    display: flex;
    align-items: center;
  }
  :deep(.item-option > span:nth-child(2) > .ant-input) {
    font-size: 12px;
    border: 0px;
  }
  .item-option:hover .del-icon {
    position: relative;
    visibility: visible;
  }
  .add-option-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 82px;
    height: 24px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
  }
  
  .get-option-btn {
    width: 82px;
    height: 24px;
  }
  .sort-question {
    margin-left: 77px;
  }
}
// textarea图标间距
:deep(.ql-toolbar.ql-snow .ql-formats) {
    margin-right: 0px;
}
.ant-alert-info {
  border: none;
}
</style>

<style lang="less">
.tag-select-wrapper {
  .rc-virtual-list-holder-inner {
    padding-left: 4px;
    display: flex;
    // flex-direction: row !important;
    flex-wrap: wrap;
  }
  .ant-select-item {
    margin-right: 4px;
    margin-bottom: 4px;
  }
  .ant-select-item-option-content {
    margin-right: 4px;
  }
  .ant-select-item-option-state {
    display: none;
  }
}
.ant-select-item-option-content {
  font-size: 12px;
}
.jse-menu {
  display: none !important;
}
.jse-main {
  height: 560px !important;
  margin-bottom: 16px;
}

.draft-cancel {
  .ant-modal-confirm-btns {
    display: flex;
    flex-direction: row-reverse;
    .ant-btn {
      margin-right: 8px;
      &:first-child {
        margin-right: 70px;
      }
    }
  }
  .ant-modal-close {
    bottom: 32px;
    right: 32px;
    top: auto;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.65);
    &:hover,
    &:focus {
      color: #7fa0fa;
      border-color: #7fa0fa;
    }
  }
  .ant-modal-close-x {
    width: auto!important;
    padding: 0 15px;
    height: 32px;
    line-height: 32px!important;
    font-size: 14px;
    font-weight: 500;
  }
}
</style>
