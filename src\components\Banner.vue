<template>
  <div class="portal-banner" :style="{ backgroundImage: 'url(' + $props.backgroundImgUrl + ')' }">
    <div class="banner-text">
      <span v-html="$props.bannerText"></span>
      <div v-if="hasTextLine" class="banner-text-line"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  halfHeight: {
    type: Boolean,
    default: false
  },
  backgroundImgUrl: {
    type: String,
    default: './assets/images/banner.png'
  },
  bannerText: {
    type: String,
    default: ''
  },
  hasTextLine: {
    type: <PERSON>ole<PERSON>,
    default: false
  }
})
</script>
<style lang="less" scoped>
.portal-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 80px;
  height: 300px;
  // width: 100%;
  // background: #fff url('@/assets/images/banner.png') no-repeat;
  // background-color: #dda87f;
  background-repeat: no-repeat;
  // background-attachment: fixed;
  // background-size: 100% 100%;
  background-size: cover;
  background-position: center;
  .banner-text {
    width: 1200px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    line-height: 1.9;
    font-weight: 500;
    .banner-text-line {
      width: 180px;
      height: 15px;
      background: rgba(255, 255, 255, 0.35);
      margin: -22px auto 8px;
    }
  }
  @media (max-width: 1200px) {
    .banner-text {
      width: 88%;
    }
  }
  @media (max-width: 992px) {
    .banner-text {
      width: 88%;
      font-size: 16px;
    }
  }
  @media (max-width: 576px) {
    .banner-text {
      width: 88%;
      font-size: 14px;
    }
  }
}
</style>
