<template>
    <div ref="chartRef" :style="{ height: height + 'px' }"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { ECharts, init } from 'echarts'
import { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'

const props = defineProps<{
    list?: PaperFbodyItemModelWithQuestionChildren[]
}>()

function getData(list: PaperFbodyItemModelWithQuestionChildren[]) {
    try {
        let data: {
            /** 题库id */
            id: string
            /** 题库名称 */
            name: string
            value: number
            children: any[]
        }[] = []
  
        list?.forEach((item) => {
            item.children.forEach(q => {
                let findItem = data.find(i => i.id === q.questionBankUUID)
                if (findItem) {
                    findItem.children.push(q)
                    findItem.value++
                } else {
                    data.push({
                        id: q.questionBankUUID,
                        name: q.category<PERSON><PERSON>,
                        children: [q],
                        value: 1
                    })
                }
            })
        })
        // 将数据根据QUESTIONS的顺序进行排序
        data.sort((a, b) => a.children.length - b.children.length)
        const groupData = Object.groupBy(data, ({name}) => name)
        return groupData
    } catch (error) {
        console.log(error)
        return []
    }
}

// 图表绘制
const chartRef = ref()
let chart: ECharts | null = null
const height = ref(0)
async function draw() {
    if (!chartRef.value) return
    let list = JSON.parse(JSON.stringify(props.list))
    let data = getData(list)
    height.value = Object.keys(data).length * 30
    await nextTick()
    chart = init(chartRef.value)
    chart?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}道`,

        },
        color: '#5478EE',
        grid: {
            left: 80,
            top: 0,
            bottom: 0
        },
        xAxis: {
            type: 'value',
            show: false
        },
        yAxis: {
            type: 'category',
            axisTick: { show: false },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            },
            axisLabel: {
                width: 60,
                overflow: 'break',
                formatter: function(value) {
                    const width = 36; // 设置宽度
                    const linesToShow = 2; // 显示行数
                    const fontSize = 12; // 字体大小
                    const lineHeight = 16; // 行高
                    
                    // 计算每行能显示的字符数
                    const charsPerLine = Math.floor(width / fontSize) * 2;
                    
                    if(value.length > charsPerLine * linesToShow) {
                        return value.substring(0, charsPerLine * linesToShow - 4) + '...'
                    }
                    return value;
                },
            },
            data:  Object.keys(data).sort((a,b) => data[a].length - data[b].length)
        },
        series: [
            {
                type: 'bar',
                barMinHeight: 10,
                barMaxWidth: 20,
                label: {
                    show: true,
                    position: 'insideLeft'
                },
                data: Object.values(data).map((item) => item.length).sort((a, b) => a - b)
            }
        ]
    })
    chart?.resize()
}

watch(() => props.list, async (val) => {
    draw()
}, { deep: true })

onMounted(() => {
    try {
        setTimeout(() => {
            // 等待dom过渡效果完成
            draw()
        }, 500);
    } catch (error) {
        console.log(error)
    }
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
</style>