<template>
  <a-layout-header class="portal-admin-header">
    <div class="header-left" @click="backToWelcome">
      <img src="@/assets/images/admin/admin-logo.svg" class="admin-login" alt=""  />
      <span class="title">图灵智面教师端</span>
    </div>
    <div class="header-right">
      <a-dropdown :trigger="['click']" class="mode-change" v-if="modeShow">
        <span
          ><svg-icon v-if="mode === 'sun'" name="sun" width="18px" height="18px" /><svg-icon
            v-else
            name="moon"
            width="18px"
            height="18px"
        /></span>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="handleChangeMode('sun')">
              <span class="mode-item"
                ><svg-icon name="sun" width="18px" height="18px" /><span>默认模式</span></span
              >
            </a-menu-item>
            <a-menu-item @click="handleChangeMode('moon')">
              <span class="mode-item"
                ><svg-icon name="moon" width="14px" height="14px" /><span>护眼模式</span></span
              >
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-dropdown :trigger="['click']">
        <a>
          <span class="login-user">{{ $store.getters.userInfo.dept_name }} / {{ username }}</span>
          <img src="@/assets/images/admin/angle-down.svg" alt="" />
        </a>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="changeDepartmentVisible = true">
              <span>切换部门</span>
            </a-menu-item>
            <a-menu-item @click="changePwdDialogVisible = true">
              <span>修改密码</span>
            </a-menu-item>
            <a-menu-item @click="loginOut">
              <span>登出</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </a-layout-header>
  <CustomDialog :txtConfig="{ title: '修改密码', confirm: '保存' }" :dialogVisible="changePwdDialogVisible" :maskClosable="false" @updateDialog="handlePwdChange" @closeDialog="changePwdDialogVisible = false" @cancelDialog="changePwdDialogVisible = false" width="600px">
    <ChangePwd v-if="changePwdDialogVisible" ref="changePwdRef" @success="changePwdDialogVisible = false"></ChangePwd>
  </CustomDialog>
  <a-modal title="切换部门" v-model:visible="changeDepartmentVisible" width="324px" :maskClosable="false" :keyboard="false" @ok="handleChangeDepartment">
    <ChangeDepartment ref="changeDepartmentRef" v-if="changeDepartmentVisible"></ChangeDepartment>
  </a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import CustomDialog from '@/components/CustomDialog.vue'
import ChangePwd from '@/pages/system/ChangePwd.vue'
import ChangeDepartment from '@/pages/system/ChangeDepartment.vue'

const store = useStore()
const router = useRouter()

// 切换部门
const changeDepartmentRef = ref<InstanceType<typeof ChangeDepartment>>()
const changeDepartmentVisible = ref(false)
async function handleChangeDepartment() {
  await changeDepartmentRef.value?.confirm()
  message.success('切换成功')
  changeDepartmentVisible.value = false
}

const handleChangeMode = (mode: string) => {
  if (store.getters.mode === 'sun') {
    store.commit('MODIFY_MODE', mode)
  } else {
    store.commit('MODIFY_MODE', mode)
  }
}
const loginOut = () => {
  store.dispatch('USER_LOGIN_OUT').then((data) => {
    message.success(data)
    if (store.getters.username === 'admin') {
      router.replace('/admin-login')
    } else {
      router.replace('/login')
    }
    sessionStorage.removeItem('loginFail')
    setTimeout(() => {
      location.reload()
    }, 0)
  })
}

const backToWelcome = () => {
  router.push('/admin/welcome')
}

const headerBg = computed(() => {
  return store.getters.mode === 'sun' ? '#fff' : '#24262E'
})
const mainTextColor = computed(() => {
  return store.getters.mode === 'sun' ? '#121633' : 'rgba(255,255,255,0.85)'
})

const route = useRoute()
const modeShow = ref(false)
watch(
  route,
  (val) => {
    if (val.path.indexOf('correctPaper') > -1) {
      modeShow.value = true
    } else {
      store.commit('MODIFY_MODE', 'sun')
      modeShow.value = false
    }
  },
  { immediate: true }
)

const mode = computed(() => store.getters.mode)
const username = computed(() => store.getters.username)

// 修改密码弹框
const changePwdDialogVisible = ref(false)
const changePwdRef = ref<InstanceType<typeof ChangePwd>>()
function handlePwdChange() {
  changePwdRef.value?.submit()
}

</script>
<style lang="less" scoped>
.portal-admin-header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 3;
  height: 48px;
  // margin-bottom: 20px;
  padding: 0 30px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  background-color: v-bind(headerBg);
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
}
.ant-modal-footer {
  .ant-btn {
    width: 97px;
    height: 36px;
    border-color: #3158bd;
    color: #3158bd;
  }
  .ant-btn-primary {
    color: #fff;
    background-color: #3158bd;
    border-color: #3158bd;
  }
}

.header-left {
  display: flex;
  align-items: center;
  .title {
    font-size: 20px;
    font-weight: 600;
    margin-left: 18px;
    color: v-bind(mainTextColor);
  }
}
.header-right {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // :global(.ant-dropdown-menu) {
  //   background-color: v-bind(headerBg) !important;
  // }
  .login-user {
    font-family: SFProDisplay-Regular;
    font-size: 16px;
    color: #6d787f;
    padding: 0 20px 0 10px;
  }
  .header-bell {
    font-size: 22px;
    margin-right: 10px;
  }

  .mode-change {
    position: relative;
    bottom: -3px;

    cursor: pointer;
    :global(.mode-item) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
    }
  }
}
</style>
