<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: String,
    default: '',
  },
  max: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['getScore', 'update:modelValue'])

const score = ref(1)

function subScore() {
  if (score.value <= 1 || props.disabled) 
    return
  score.value -= 1
}
function addScore() {
  if (score.value >= props.max || props.disabled) 
    return
  score.value += 1
}
function formatter(value: any) {
  if (!value) 
    return null
  if (value % 1 === 0) {
    return value.toString()
  }
  else {
    return Math.round(value).toString()
  }
}
defineExpose({
  score,
})
function blurInput() {
  if (!score.value) {
    score.value = 1
  }
  emit('getScore', score.value)
}
watch(score, (val) => {
  emit('getScore', score.value)
})

watch(
  () => props.modelValue,
  (val) => {
    if (val === null) 
      return null
    score.value = Number(val)
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="score-input-container">
    <div class="substract-btn" :class="{ disabled: score <= 0 || disabled }" @click="subScore">
      <svg-icon name="substract" />
    </div>
    <a-input-number
      v-model:value="score"
      class="score-input"
      :controls="false"
      :disabled="disabled"
      :min="0"
      :max="max"
      :step="1"
      :formatter="formatter"
      @blur="blurInput"
      @input="$emit('update:modelValue', score)"
    />
    <div class="add-btn" :class="{ disabled: score >= max || disabled }" @click="addScore">
      <svg-icon name="plus2" />
    </div>
  </div>
</template>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

/* 火狐 */
input {
  -moz-appearance: textfield;
}
.score-input-container {
  background: #fff;
  overflow: hidden;
  user-select: none;
  display: flex;
  align-items: center;
  width: 102px;
  height: 32px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  .substract-btn,
  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 100%;
    cursor: pointer;
  }
  .substract-btn {
    border-right: 1px solid #d9d9d9;
  }
  .disabled {
    background: #f5f5f5;
  }
  .add-btn {
    border-left: 1px solid #d9d9d9;
  }
  .score-input {
    flex: 1;
    height: 30px;
    border: none;
    box-shadow: none;
    :deep(.ant-input-number-input) {
      font-size: 12px;
    }
    text-align: center;
  }
}
</style>
