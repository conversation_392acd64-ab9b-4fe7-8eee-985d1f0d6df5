<script lang="ts" setup>
import ResumeDetail from '../ResumeDetail/index.vue'
import ResumeSidebar from './ResumeSidebar.vue'
import { currentResumeId, resumeListDataInFilter } from './shared'

/**
 * ResumeSelectModal 是父组件
 * resumeListDataInFilter 、 currentResumeId 是共享的状态
 * 
 * resumeListDataInFilter 是侧边栏的数据，为什么设置为共享的，
 *    是因为在右侧简历 ProfileHeader.vue 那里会修改状态，导致侧边栏的数据也会变化
 * currentResumeId 也是因为单个简历编辑后，当前简历需要变化
 * 
 * ResumeSidebar 会牵涉共享的数据，都在 ../shared/index.ts 里面
 * ResumeDetail 总的来说不牵扯共享数据，但是在右侧简历 ProfileHeader.vue 那里会修改状态，
 *  涉及 resumeListDataInFilter ，然后 ResumeSidebar.vue 中监听后，页面改变，然后 currentResumeId 改变
 *  然后到这里，往下传递
 *  
 */
const props = withDefaults(
  defineProps<{
    jobTitle?: string
    resumeList?: any[]
  }>(),
  {
    jobTitle: '',
    resumeList: () => [],
  },
)
const emit = defineEmits(['open', 'close'])
const dialogVisible = defineModel<boolean>('visible', { required: true })

// 从这里往后所有的简历List

watchEffect(() => {
  resumeListDataInFilter.value = props.resumeList
})

function handleClose(done: () => void) {
  done()
}

function handleClickModal() {
  dialogVisible.value = false
  emit('close')
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible" 
    :show-title="false"
    width="700px" 
    :before-close="handleClose" 
    :destroy-on-close="true"
    fullscreen
    :show-close="false" 
    class="resume-edit-dialog !pt-0 !pb-0 !px-0"
    :style="{
      // 'min-width': '200px',
      // '--el-dialog-padding-primary': '0px',
      // 'border': '1px solid #11575D',
      // 'border-radius': '8px',
      // 'overflow': 'hidden',
    }" 
    @open="emit('open')"
  >
    <div class="w-full h-screen overflow-hidden text-black">
      <div class="flex h-[72px] flex-shrink-0 justify-between items-center px-8 border-b border-solid border-[#e3e3e3]">
        <div class="flex items-center gap-2">
          <span class="text-xl font-bold">
            {{ props.jobTitle }}
          </span>
          <span class="bg-[#F5F5F5] w-5 h-5 rounded-full flex justify-center items-center">
            {{ resumeListDataInFilter.length }}
          </span>
        </div>
        <el-button link icon="el-icon-close" @click="handleClickModal">
          <span class="i-lucide-x w-5 h-5 text-gray-500" />
        </el-button>
      </div>

      <div class="h-[calc(100%-72px)] flex">
        <!-- sidebar -->
        <div class="flex-shrink-0 w-[200px] h-full">
          <ResumeSidebar />
        </div>
        <!-- 简历 -->
        <div class="flex-1 h-full overflow-auto p-5 bg-[#eff6fa]">
          <ResumeDetail 
            :key="currentResumeId"
            :props-current-resume-id="currentResumeId"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style>
.el-dialog .el-dialog__header {
  padding-bottom: 0px !important;
}
</style>