<script setup lang="ts">
import type { WeaknessItemProps } from './shared'

withDefaults(defineProps<{
  weaknessData: WeaknessItemProps
}>(), {
  weaknessData: () => ([
    {
      title: '工作经历连续性存疑',
      description: '2018年毕业后立即加入阿里巴巴工作，但简历中未来及实习经历，需要进一步询问入职途径。',
    },
    {
      title: '技术描述与项目经验不匹配',
      description: '简历中提到精通 React，但工作经历中未展现相关经验，需要进一步考察技术深度。',
    },
    {
      title: '管理经验真实性待验证',
      description: '2018年毕业后立即加入阿里巴巴工作，但简历中未来及实习经历，需要进一步询问入职途径。',
    },
  ]),
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        质疑点分析
      </h3>
    </div>
    <!-- 最后一行如果只有一个，那么就占满一整行 -->
    <div class="grid grid-cols-2 gap-4  ">
      <div 
        v-for="(item, index) in weaknessData" 
        :key="index" 
        class="bg-red-50 p-4 rounded-lg"
        :class="{
          'col-span-2': weaknessData.length % 2 !== 0 && index === weaknessData.length - 1,
        }"
      >
        <div class="flex items-start">
          <div class="w-4 h-[20px] flex justify-center items-center">
            <svg width="16px" height="16px" viewBox="0 0 16 16">
              <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g transform="translate(-260.000000, -951.000000)">
                  <g transform="translate(244.000000, 932.000000)">
                    <g transform="translate(16.000000, 19.000000)">
                      <rect x="0" y="0" width="16" height="16" />
                      <rect fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="16" height="16" />
                      <path id="形状" d="M8,1 C4.134375,1 1,4.134375 1,8 C1,11.865625 4.134375,15 8,15 C11.865625,15 15,11.865625 15,8 C15,4.134375 11.865625,1 8,1 Z M7.5,4.625 C7.5,4.55625 7.55625,4.5 7.625,4.5 L8.375,4.5 C8.44375,4.5 8.5,4.55625 8.5,4.625 L8.5,8.875 C8.5,8.94375 8.44375,9 8.375,9 L7.625,9 C7.55625,9 7.5,8.94375 7.5,8.875 L7.5,4.625 Z M8,11.5 C7.5859375,11.5 7.25,11.1640625 7.25,10.75 C7.25,10.3359375 7.5859375,10 8,10 C8.4140625,10 8.75,10.3359375 8.75,10.75 C8.75,11.1640625 8.4140625,11.5 8,11.5 Z" fill="#FF4D4F" />
                    </g>
                  </g>
                </g>
              </g>
            </svg>
          </div>
          <div class="ml-2">
            <div class="text-sm font-medium" v-html="item.title" />
            <div class="mt-1 text-xs text-gray-600" v-html="item.description" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>