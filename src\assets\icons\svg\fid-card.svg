<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="125px" viewBox="0 0 200 125" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 17</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="200" height="125"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="200" height="125" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-3" x="0" y="0" width="122" height="76" rx="4"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="122" height="76" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="考前页面" transform="translate(-523.000000, -1184.000000)">
            <g id="身份证" transform="translate(359.000000, 953.000000)">
                <g id="编组-17" transform="translate(164.000000, 231.000000)">
                    <g id="编组-14">
                        <use id="矩形" stroke="#ADB0B8" mask="url(#mask-2)" stroke-width="2" fill="#EEF0F5" stroke-dasharray="4" xlink:href="#path-1"></use>
                        <g id="证" transform="translate(39.000000, 16.000000)">
                            <use id="矩形" stroke="#ADB0B8" mask="url(#mask-4)" stroke-width="2" fill="#FFFFFF" stroke-dasharray="1" xlink:href="#path-3"></use>
                            <polygon id="形状" fill="#E7E9EF" fill-rule="nonzero" points="17 49 100 49 100 51 17 51"></polygon>
                            <g id="编组-3" transform="translate(17.000000, 13.000000)" fill-rule="nonzero">
                                <polygon id="路径" fill="#E7E9EF" points="0 6 7 6 7 8 0 8"></polygon>
                                <polygon id="路径备份-9" fill="#E7E9EF" points="0 18 7 18 7 20 0 20"></polygon>
                                <polygon id="路径备份-2" fill="#ADB0B8" points="12 6 43 6 43 8 12 8"></polygon>
                                <polygon id="路径备份-10" fill="#ADB0B8" points="12 18 61 18 61 20 12 20"></polygon>
                                <polygon id="路径备份" fill="#E7E9EF" points="0 12 7 12 7 14 0 14"></polygon>
                                <polygon id="路径备份-3" fill="#ADB0B8" points="12 12 36.4042553 12 36.4042553 14 12 14"></polygon>
                                <polygon id="路径" fill="#E7E9EF" points="0 0 7 0 7 2 0 2"></polygon>
                                <polygon id="路径备份-4" fill="#ADB0B8" points="12 0 36.4042553 0 36.4042553 2 12 2"></polygon>
                            </g>
                            <path d="M94.0014365,13 C91.7080246,13.0063329 89.701079,14.6275424 89.0976403,16.9612838 C88.4942017,19.2950251 89.4429087,21.7664522 91.4157229,23 C88.7957611,23.9059516 86.8025986,26.1708279 86.1408673,28.9939394 C85.8553887,29.9253112 86.0090413,30.9444953 86.5543412,31.7365293 C87.0996412,32.5285633 87.9709344,32.9980864 88.8989617,33 L99.1010382,33 C100.029066,32.9980864 100.900359,32.5285633 101.445659,31.7365293 C101.990959,30.9444953 102.144611,29.9253112 101.859133,28.9939394 C101.195461,26.1729647 99.2044793,23.9093334 96.58715,23 C98.5599643,21.7664522 99.5086713,19.2950251 98.9052327,16.9612838 C98.301794,14.6275424 96.2948484,13.0063329 94.0014365,13 Z" id="形状" fill="#ADB0B8" fill-rule="nonzero"></path>
                        </g>
                        <g id="编组-9复制-3" transform="translate(143.000000, 73.000000)">
                            <circle id="椭圆形" fill="#5E7CE0" cx="12" cy="12" r="12"></circle>
                            <g id="add-3" transform="translate(4.000000, 4.000000)" fill="#FFFFFF">
                                <path d="M7.2,7.2 L7.2,0.8 C7.2,0.32 7.52,0 8,0 C8.48,0 8.8,0.32 8.8,0.8 L8.8,7.2 L15.2,7.2 C15.68,7.2 16,7.52 16,8 C16,8.48 15.68,8.8 15.2,8.8 L8.8,8.8 L8.8,15.2 C8.8,15.68 8.48,16 8,16 C7.52,16 7.2,15.68 7.2,15.2 L7.2,8.8 L0.8,8.8 C0.32,8.8 0,8.48 0,8 C0,7.52 0.32,7.2 0.8,7.2 L7.2,7.2 Z" id="路径"></path>
                            </g>
                        </g>
                        <path d="" id="形状结合" stroke="#ADB0B8"></path>
                    </g>
                    <text id="上传身份证人像面" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="18" fill="#252B3A">
                        <tspan x="52" y="112">上传身份证人像面</tspan>
                    </text>
                </g>
            </g>
        </g>
    </g>
</svg>