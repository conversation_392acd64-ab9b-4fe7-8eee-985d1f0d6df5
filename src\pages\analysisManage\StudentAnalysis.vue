<template>
  <div class="analysis">
    <div class="common-page-title">
      <span>考生分析</span>
    </div>
    <div class="analysis-content">
      <a-row :gutter="20">
        <a-col style="height: 300px;" :span="8">
          <div class="panel">
            <div class="panel-title">性别分布</div>
            <SexChart class="panel-content"></SexChart>
          </div>
        </a-col>
        <a-col style="height: 300px;" :span="8">
          <div class="panel">
            <div class="panel-title">年龄分布</div>
            <AgeChart class="panel-content"></AgeChart>
          </div>
        </a-col>
        <a-col style="height: 300px;" :span="8">
          <div class="panel">
            <div class="panel-title">学历分布</div>
            <DegreeChart class="panel-content"></DegreeChart>
          </div>
        </a-col>
      </a-row>
      <a-row style="flex: 1;min-height: 0;margin-top: 20px;">
        <a-col style="height: 100%;" :span="24" flex="auto">
          <div class="panel">
            <div class="panel-title">考生列表</div>
            <StudentTable class="panel-content"></StudentTable>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import SexChart from './components/SexChart.vue'
import AgeChart from './components/AgeChart.vue'
import DegreeChart from './components/DegreeChart.vue';
import StudentTable from './components/StudentTable.vue';


</script>

<style lang="less" scoped>
.analysis {
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: calc(1440px - 240px);

  .analysis-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;

    .panel {
      background-color: #fff;
      height: 100%;
      padding: 20px;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-title {
        font-size: 20px;
        margin-bottom: 10px;
      }

      .panel-content {
        flex: 1;
        min-height: 0;
        overflow: auto;
      }
    }
  }
}
</style>