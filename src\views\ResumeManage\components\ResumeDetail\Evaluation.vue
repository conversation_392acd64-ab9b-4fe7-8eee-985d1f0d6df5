<script setup lang="ts">
import type { EvaluationDataProps } from './shared'
import * as echarts from 'echarts'
import { GaugeChart } from 'echarts/charts'
import {
  LegendComponent,
  TooltipComponent,
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'

// 模拟评价数据
const props = withDefaults(defineProps<{
  evaluationData: EvaluationDataProps
}>(), {
  evaluationData: () => ({
    matchScore: 75,
    overallEvaluation: '<span class="sf-1">拥有3年Java开发经验，</span>精通Java语言和Spring框架，具备扎实的算法和数据结构基础，参与过多个核心项目的开发，展现出良好的架构设计和编码能力。缺乏另一种开发语言的精通和数据库优化的具体经验。建议进入面试环节，重点考察其多语言能力和数据库优化经验。',
  }),
})

use([
  Gauge<PERSON>hart,
  CanvasRenderer,
  TooltipComponent,
  LegendComponent,
])

const option = computed(() => ({
  graphic: [{
    type: 'text',
    left: 'center',
    top: '35%',
    style: {
      // 匹配度在[0,70%)为不符，在[70%,75%)为欠佳，在[75%,80%)为尚可，在[80%,85%)为胜任，在[85%,100%)为卓越
      text: 
        props.evaluationData?.matchScore < 70
          ? '不符' 
          : props.evaluationData?.matchScore < 75 && props.evaluationData?.matchScore >= 70
            ? '欠佳' 
            : props.evaluationData?.matchScore < 80 && props.evaluationData?.matchScore >= 75
              ? '尚可' 
              : props.evaluationData?.matchScore < 85 && props.evaluationData?.matchScore >= 80
                ? '胜任'
                : props.evaluationData?.matchScore >= 85
                  ? '卓越'
                  : '', // 第一行大字
      fontSize: 24,
      fontWeight: 'bold',
      fill: props.evaluationData?.matchScore < 70
        ? '#FF4D4F' // '不符' 
        : props.evaluationData?.matchScore < 75 && props.evaluationData?.matchScore >= 70
          ? '#5478EE' // '欠佳' 
          : props.evaluationData?.matchScore < 80 && props.evaluationData?.matchScore >= 75
            ? '#5478EE' // '尚可' 
            : props.evaluationData?.matchScore < 85 && props.evaluationData?.matchScore >= 80
              ? '#5478EE' // '胜任'
              : props.evaluationData?.matchScore >= 85
                ? '#06B190' // '卓越'
                : '#000000', // 第一行大字,
    },
  }, {
    type: 'text',
    left: 'center',
    top: '55%',
    style: {
      text: '岗位匹配度', // 第二行小字
      fontSize: 12,
      fill: '#000000',
    },
  }],
  series: [
    {
      min: 0,
      max: 100,
      type: 'gauge',
      radius: '100%',
      progress: {
        show: true,
        width: 14,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#3D95FF' }, // 起始颜色
            { offset: 0.5, color: '#5AD8A6' }, // 结束颜色
            { offset: 1, color: '#6AD2E6' }, // 结束颜色
          ]),
        },
      },
      axisLine: {
        lineStyle: {
          width: 14,
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        length: 15,
        show: false,
        lineStyle: {
          width: 2,
          color: '#999',
        },
      },
      axisLabel: {
        show: true,
        distance: -34,
        color: '#666',
        fontSize: 12,
        formatter(value: any) {
          if (value === 0)
            return value // 第一个刻度（起点）
          if (value === 100)
            return value // 最后一个刻度
          return ''
        },
      },
      anchor: {
        show: false,
      },
      title: {
        show: false,
      },
      pointer: {
        show: false,
      },
      detail: {
        show: false,
        valueAnimation: true,
        fontSize: 16,
      },
      data: [
        {
          value: props.evaluationData?.matchScore,
        },
      ],
    },
  ],
}))
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-medium">
        综合评价
      </h3>
    </div>
    <div class="flex items-center justify-center py-0">
      <VChart class="w-[134px] h-[134px]" :option="option" autoresize />
    </div>
    <div class="mt-4 ">
      <div class="text-[12px] leading-6" v-html="evaluationData.overallEvaluation" />
    </div>
  </div>
</template>

<style>
.sf-1 {
  color: #d0813a;
  font-weight: 500;
}
</style>