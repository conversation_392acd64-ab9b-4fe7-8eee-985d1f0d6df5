<script setup lang="ts">
import PackageCreate from './components/QuestionPackageCreate.vue'
import PackageEmpty from './components/QuestionPackageEmpty.vue'
import PackageList from './components/QuestionPackageList.vue'
import { getPackageList } from './hooks/api'

function init() {
  getPkgList()
}

const pkgList = ref([])
async function getPkgList() {
  const res = await getPackageList({
    paegNo: 1,
    pageSize: 100,
  })
  const { records } = res
  pkgList.value = records
}

const dialogVisible = ref(false)
function openCreateDialog() {
  dialogVisible.value = true
}
provide('pkg', {
  getPkgList,
  openCreateDialog,
})
onActivated(() => {
  init()
})
</script>

<template>
  <div class="manual-mark-page-wrap min-w-[800px]">
    <div class="common-page-title-wrapper flex flex-wrap m-0 relative ">
      <span class="title w-full">笔试题库</span>
      <!-- <div class="filter-wrapper mt-[20px]"> -->
      <div class="filter-wrapper mt-[20px] h-0" style="padding: 0;">
        <!-- <a-button type="primary" class="absolute right-0 top-[-50px]" @click="openCreateDialog">
          创建题库 
        </a-button> -->
        <!-- <SearchBox /> -->
      </div>
      <PackageList :pkg-list="pkgList" />
      <PackageEmpty v-show="!pkgList.length" />
      <PackageCreate v-model:dialog-visible="dialogVisible" />
    </div>
  </div>
</template>

<style lang="less" scoped>
// :deep(.ant-input-wrapper:focus-within) {
//   width: 800px;
// }

.empty-box {
  margin-top: 20%;
  margin-left: 50%;
  transform: translateX(-50%);
}

.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-wrapper {
  position: relative;
  width: 100%;
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }
  }
}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
</style>