<template>
    <div ref="chartRef" style="min-height: 100%;" :style="{ height: height + 'px' }"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ECharts, init } from 'echarts'

type NodeType = {
    cal_data: number
    catg_name: string
    children: NodeType[]
}

const props = defineProps<{
    list?: NodeType[]
}>()

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
const height = ref(0)
async function draw() {
    if (!props.list?.length) return
    height.value = props.list.length * 50
    await nextTick()
    chart.value = init(chartRef.value)
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `得分率${(c * 100).toFixed(2)}%`
        },
        color: '#5478ee',
        grid: {
            left: 90,
            top: 30,
            bottom: 40
        },
        xAxis: {
            type: 'value',
            position: 'top',
            axisLabel: {
                formatter: function (value: any) {
                    return (value * 100).toFixed(2) + '%';
                },
            }
        },
        yAxis: {
            type: 'category',
            axisTick: { show: false },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0, 0, 0, 0.45)'
                }
            },
            axisLabel: {
                width: 60,
                overflow: 'break'
            },
            data: props.list.map(item => item.catg_name)
        },
        series: [
            {
                type: 'bar',
                barMaxWidth: 20,
                label: {
                    show: true,
                    position: 'right',
                    formatter: ({ value }: any) => `${(value * 100).toFixed(2)}%`
                },
                // 渐变
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                        {
                            offset: 0, color: '#78c5ff'
                        },
                        {
                            offset: 1, color: 'rgba(84,120,238,0.85)'
                        }
                    ],
                    global: false
                },
                data: props.list.map(item => ({
                    value: item.cal_data,
                    name: item.catg_name
                }))
            }
        ]
    })
    window.addEventListener('resize', resizeChart)
}

function resizeChart() {
    chart.value?.resize()
}

watch(() => props.list, draw)

onMounted(() => {
    draw()
})

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
</style>