<template>
  <a-layout-content class="portal-admin-content">
    <router-view class="router-view" #="{ Component, route }">
      <transition name="slide-left" mode="out-in">
        <div style="height: 100%;overflow-x: auto;">
          <keep-alive>
            <component v-if="route.meta.keepAlive" :is="Component" :key="route.name" />
          </keep-alive>
          <component v-if="!route.meta.keepAlive" :is="Component" />
        </div>
      </transition>
    </router-view>
  </a-layout-content>
</template>

<style lang="less">
.ant-table-thead>tr>th .anticon-filter,
.ant-table-thead>tr>th .ant-table-filter-icon {
  right: auto !important;
}

.portal-admin-content {
  background: linear-gradient(230deg,#e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(-20px);
  opacity: 0;
}

.slide-left-enter-to {
  transform: translateX(0px);
}

.slide-left-leave-from {
  transform: translateX(0);
}

.slide-left-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s;
}
</style>
