import request from '@/utils/http'

// 简历查询
export function queryResumeListApi(data?: object) {
  return request({
    url: '/resume/queryResumeList',
    data
  })
}

/**
 * 1. 根据用户id获取用户简历
 * @param data 
 * @returns 
 */
export function getResumeByIdApi(data?: object) {
  return request({
    url: '/resume/getResumeDetail',
    data
  })
}

/**
 * 2. 设置用户简历是否合适的状态
 * @param data 
 * @returns 
 */
export function updateResumeInfoApi(data?: {
  resumeId: string
  candidateName?: string
  gender?: string
  birthday?: string
  phone?: string
  email?: string
  highestEducation?: string
  workYears?: string
  graduateSchool?: string
  currentPosition?: string
  resumeStatus?: string
  hometown?: string
}) {
  return request({
    url: '/resume/updateResumeInfo',
    data
  })
}

/**
 * 3. 下载简历
 * @param data 
 * @returns 
 */
export function downloadResumeFileApi(data?: any) {
  return request({
      url: `/resume/downloadResume/${data.resumeId}`,
      data,
      method: 'get',
      responseType: 'blob'
  })
}

export function getStuInfo(data?: object) {
  return request({
    url: '/pendstu',
    data
  })
}

// 简历删除
export function modResume(data?: object) {
  return request({
    url: '/student/update',
    data
  })
}

// 简历删除
export function deleteResume(data?: object) {
  return request({
    url: '/student/delete',
    data
  })
}

// 批改试卷
export function checkPaper(data?: object) {
  return request({
    url: '/checkpaper',
    data
  })
}

// 批改试卷
export function filterResume(data?: object) {
  return request({
    url: '/sfilter',
    data
  })
}

// 学生简历
export function stuallinfo(data?: object) {
  return request({
    url: '/student/get',
    data
  })
}

// 批量导出学生简历
export function exportstu(data?: object) {
  return request({
    url: '/excel_export_stu',
    data
  })
}

// 获取简历列表页的报考部门筛选项
export function getstudept(data?: object) {
  return request({
    url: '/getstudept',
    data
  })
}

// 获取关联考试列表页的报考部门筛选项
export function getpaperstudept(data?: object) {
  return request({
    url: '/getpaperstudept',
    data
  })
}



/**
 * 3. 下载简历
 * @param data 
 * @returns 
 */
export function downloadResumeByIdApi(data?: object) {
  return request({
    url: '/downloadResumeByIdApi',
    data
  })
}
