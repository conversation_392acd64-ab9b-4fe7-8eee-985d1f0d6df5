<script setup lang="ts">
import type { QuestionModel } from '@/models/questionModel'
import { uuid } from '@/utils'
import { findImageAndPreview, transferStr } from '@/utils/common'
import ScoreInput from '@/views/QuestionManage/components/ScoreInput.vue'
import { StarFilled } from '@ant-design/icons-vue'
import cloneDeep from 'lodash/cloneDeep'

interface MANUALQUESITEM {
  type: string
  id: string
  configSchema: {
    content: string
  }
  isEdit: boolean
  recordDuration: any
  correctAnswer: {
    reference: string
  }
}
const props = withDefaults(
  defineProps<{
    showType?: boolean // 是否显示题型
    showScore?: boolean // 是否显示分值
    scoreKey?: 'score' | 'new_score'
    showTag?: boolean // 是否展示标签
    showCorrectAnswer?: boolean // 是否展示正确答案
    showQuestionDifficulty?: boolean // 是否显示算法题难度
    showQuestionPointsAndBasis?: boolean // 是否展示问答题得分点
    showQuestionProofreadingRecord?: boolean
    questionDetail: QuestionModel
    item?: any
    print?: boolean // 是否打印预览
    optionLetterType?: 'button' | 'text'
    showDisabledKws?: boolean // 是否展示禁用关键词
    deleted?: boolean // 是否处于删除状态
    showStudentScore?: boolean // 是否展示学生得分
    scoreEditable?: boolean // 是否允许改分
    studentId?: string // 学生id
    paperId?: string // 试卷id
  }>(),
  {
    showType: false,
    showScore: false,
    showTag: false,
    showCorrectAnswer: false,
    showStudentScore: false,
    scoreEditable: false,
    showQuestionDifficulty: false,
    showQuestionPointsAndBasis: false,
    showQuestionProofreadingRecord: false,
    scoreKey: 'score',
    print: false,
    optionLetterType: 'button',
    showDisabledKws: false,
    deleted: false,
  },
)

const emits = defineEmits<{
  /** 保存事件 */
  (e: 'save', data: Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>): void
  /** 取消事件 */
  (e: 'cancel'): void
  /** 表单数据变化事件 */
  (e: 'change', data: Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>): void
}>()
function convertJsonField(questionDetail: any) {
  questionDetail.btestcase = questionDetail.btestcaseJson
  questionDetail.options = questionDetail.optionsJson || questionDetail.options
  questionDetail.disablekws = questionDetail.disablekwsJson
  questionDetail.ptestcase = questionDetail.ptestcaseJson
  questionDetail.rtype = questionDetail.rtypeJson
  questionDetail.parameters = questionDetail.parametersJson
}
convertJsonField(props.questionDetail)

watch(() => props.questionDetail, (newVal) => {
  convertJsonField(newVal)
})
// 获取题干文本
function getContent() {
  let ans = ''
  ans = transferStr(props.questionDetail.configSchema.content)
  return `${ans}`
}
const formData = ref<Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>>({
  configSchema: {
    content: '',
  },
  recordDuration: 5,
  correctAnswer: {
    reference: '',
  },
})

watch(formData, (val) => {
  Object.keys(val).forEach((key) => {
  // eslint-disable-next-line vue/no-mutating-props
    props.questionDetail[key as keyof typeof formData.value] = val[key as keyof typeof formData.value]
  })
}, {
  deep: true,
})

const formRef = useTemplateRef<any>('form')

const revertFormDataFunc = ref<any>(null)
function assignRevert() {
  const rawData = cloneDeep(toRaw(props.questionDetail)) 
  revertFormDataFunc.value = () => {
    Object.keys(rawData).forEach((key) => {
      if (key === 'isEdit') 
        return
      formData.value[key as keyof typeof formData.value] = rawData[key as keyof typeof rawData]
    })
  }
}

assignRevert()

function swtichEdit() {
  // eslint-disable-next-line vue/no-mutating-props
  props.questionDetail.isEdit = false
}

function validate() {
  return formRef.value?.validate()
}

async function save() {
  try {
    // eslint-disable-next-line vue/no-mutating-props
    props.questionDetail.id = uuid()
    await validate()
    formData.value.recordDuration = formData.value.recordDuration * 60
    emits('save', { ...formData.value })
    swtichEdit()
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

function cancel() {
  formData.value.recordDuration = formData.value.recordDuration * 60
  swtichEdit()

  // 重置表单到初始状态
  revertFormDataFunc.value()
}

function resetForm() {
  formData.value.configSchema.content = ''
  formData.value.recordDuration = 5
  formData.value.correctAnswer.reference = ''
}

watch(() => props.questionDetail.isEdit, (isEdit) => {
  if (isEdit) {
    assignRevert()
    formData.value.configSchema.content = props.questionDetail.configSchema.content || ''
    formData.value.correctAnswer.reference = props.questionDetail.correctAnswer?.reference || ''
    formData.value.recordDuration = (props.questionDetail.recordDuration / 60) || 5
  }
})
const anchorRef = useTemplateRef('anchor')
defineExpose({
  validate,
  save,
  cancel,
  resetForm,
  formData,
  item: props.item,
  $el: anchorRef,
})
</script>

<template>
  <div v-if="!questionDetail.isEdit" ref="anchor" class="test-question-display relative pl-[30px]">
    <div class="absolute left-0">
      <img src="@/assets/icons/svg/question_icon.svg" class="mr-[12px]">
    </div>

    <div class="test-question-stem" :class="{ deleted }">
      <div class="flex flex-nowrap items-start w-[90%]">
        <div class="body" :class="{ deleted }" v-html="getContent()" />

        <p class="proofread-record mt-[0] ml-[8px]">
          <a-tag v-if="!questionDetail.check" :bordered="false">
            未校对
          </a-tag>
          <a-tag v-if="questionDetail.check && questionDetail.check.isCorrect === 0" color="error" :bordered="false">
            校对错误
          </a-tag>
        </p>
      </div>
    </div>
    <template v-if="!deleted">
      <div class="test-question-content">
        <template v-if="true">
          <div v-if="showQuestionPointsAndBasis">
            <div class="test-question-scorebasis flex flex-nowrap justify-between w-[85%]">
              <div class="flex flex-nowrap">
                <span class="label">题库</span>
                <div
                  class="scorebais-content"
                  v-html="transferStr(questionDetail.bankPath.split('/').pop())"
                />
              </div>
              <div class="flex flex-nowrap">
                <span class="label">难度</span>
                <div
                  class="scorebais-content"
                >
                  <StarFilled v-for="item in questionDetail.difficulty" :key="item" style="color:#faad14" />
                </div>
              </div>
              <div class="flex flex-nowrap">
                <span class="label">建议时长</span>
                <div
                  class="scorebais-content"
                  @click="$event => findImageAndPreview($event)"
                  v-html="`${questionDetail.recordDuration / 60}分钟`"
                />
              </div>
            </div>
            <div class="test-question-scorebasis">
              <span class="label">参考答案</span>
              <div
                class="scorebais-content"
                @click="$event => findImageAndPreview($event)"
                v-html="transferStr(questionDetail.correctAnswer.reference)"
              />
            </div>
          </div>
        </template>
      </div>
      <div v-if="showTag && questionDetail?.tagname?.length" class="test-question-tag">
        <template v-for="(tag, index) in questionDetail.tagname" :key="index">
          <span class="item">
            {{ tag }}
          </span>
        </template>
      </div>
    </template>
  </div>
  <div v-if="questionDetail.isEdit" class="pt-[16px] pr-[24px] pb-[0] w-full">
    <el-form ref="form" :model="questionDetail" label-width="80">
      <el-form-item label="题干内容" prop="configSchema.content" :rules="[{ required: true, message: '请输入题干内容', trigger: 'blur' }]">
        <el-input
          v-model="formData.configSchema.content"
          maxlength="1000"
          type="textarea"
          resize="none"
          :rows="3"
          placeholder="请输入题干内容"
        />
      </el-form-item>
      <el-form-item label="建议时长" prop="recordDuration">
        <ScoreInput
          v-model="formData.recordDuration"
          @get-score="(val) => {
            formData.recordDuration = val
          }"
        />
      </el-form-item>
      <el-form-item label="参考答案" prop="correctAnswer.reference">
        <el-input
          v-model="formData.correctAnswer.reference"
          maxlength="100"
          type="textarea"
          resize="none"
          :rows="3"
          placeholder="请输入参考答案"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="save">
          保存
        </el-button>
        <el-button size="small" @click="cancel">
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="less" scoped>
.test-question-display {
  padding: 6px;
  padding-left: 30px;
  border-radius: 8px;
  width:100%;
  cursor: pointer;
//   background: #ffffff;
  .btn-wrap {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      background: #f5f5f5;
    }
  }
}

.transparent {
  border-color: transparent !important;
  background-color: transparent !important;
}
.test-question-stem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  white-space: pre-wrap;
  &.deleted {
    margin-bottom: 0;
  }

  &.tool-bar {
    display: flex;
    justify-content: space-between;
  }
  .type {
    align-self: flex-start;
    flex-shrink: 0;
    width: 52px;
    text-align: center;
    line-height: 20px;
    background: #f1f4fe;
    border: 1px solid #5478ee;
    border-radius: 4px;
    font-size: 12px;
    color: #5478ee;
    margin-right: 8px;
  }
  :deep(.body) {
    font-size: 16px;
    line-height: 22px;
    // word-break: break-all;
    text-align: justify;
//     flex: 1;
    min-width: 0;
    // overflow: auto;
    overflow-x: auto;
    overflow-y: hidden;
    &.deleted {
      text-decoration: line-through;
    }
    p {
      font-size: 16px;
      word-break: break-word;
      img {
        max-width: 100%;
        cursor: zoom-in!important;
      }
    }
  }
  .score {
    align-self: flex-start;
    flex-shrink: 0;
    width: 41px;
    text-align: center;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 8px;
    margin-top: 1px;
  }
}
.out-answer {
  color: rgba(0, 0, 0, 0.45);
  padding-left: 50px;
  margin-bottom: 10px;
  .correct-text {
    color: #52c41a;
  }
  .error-text {
    color: #de504e;
  }
}
.test-question-content {
  .test-question-options {
    .item-option {
      display: flex;
      align-items: center;
      .tag {
        color: #000;
        font-weight: bolder;
      }
      + .item-option {
        margin-top: 16px;
      }
      :deep(.option-content) {
        overflow-y: hidden  ;
        p {
          word-wrap: break-word;
          img {
            max-width: 100%;
            cursor: zoom-in!important;
          }
        }
      }
    }
    .option-btn {
      margin-right: 16px;
    }
    .correct-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .error-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .correct-answer-tag {
      font-size: 12px;
      color: #2f8c00;
      background: #f3f7f1;
      border-radius: 4px;
      padding: 0 2px;
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
  .test-question-scorebasis {
    display: flex;
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
      flex-shrink: 0;
    }

    :deep(.scorebais-content) {
      p {
        word-wrap: break-word;
        img {
          max-width: 100%;
          cursor: zoom-in!important;
        }
      }
    }
  }
  .test-question-points {
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #626262;
    margin-bottom: 8px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-items: baseline;
    .label {
      color: #626262;
    }
    
    .points-content {
      margin-left: 10px;
      flex: 1;
      min-width: 0;
      .item {
        display: flex;
        .scorePointIndex {
          flex: none;
          width: 18px;
          height: 28px;
          font-size: 14px;
          color: #121633;
          line-height: 28px;
        }
        .scoPointDetail {
          margin-bottom: 8px;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .scorePoint {
            margin-left: 8px;
            margin-bottom: 8px;
            padding: 1px 4px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            line-height: 24px;
          }
        }
        .score {
          height: 28px;
          margin-left: 8px;
          padding: 0 6px;
          background: #f0f0f0;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
          line-height: 28px;
          white-space: nowrap;
        }
      }
    }
    > div {
      margin-left: 52px;
    }
  }
  .test-question-testcase {
    .title {
      padding: 0 0 8px;
      font-size: 14px;
      font-weight: 600;
    }
    .content {
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 16px;

      .item {
        font-size: 14px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
      .item-text {
        font-weight: 600;
        padding-right: 6px;
      }
    }
  }
  .test-question-difficulty {
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
    }
  }
  .test-question-sequence {
    margin-bottom: 16px;
    .item {
      display: flex;
      min-height: 34px;
      padding-left: 8px;
      align-items: center;
      border: 1px solid #dad8d8;
      color: #121633;
      border-radius: 2px;
    }
  }
  .sort-option-wrapper {
    margin-bottom: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    .item {
      flex: 1;
      margin-left: 10px;
    }
  }
}
.test-question-tag {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.disableKws {
  margin-top: 10px;
  .label {
    color: #626262;
    margin-right: 32px;
  }
  .kw-list {
    margin-top: 8px;
    padding-left: 16px;
    .kw-item {
      display: flex;
      .language {
        flex: none;
        width: 80px;
        height: 24px;
        margin-right: 8px;
        color: #626262;
        line-height: 24px;
      }
      .kw-wrap {
        line-height: 24px;
        display: flex;
        flex-wrap: wrap;
        color: #626262;
        .kw {
          margin-right: 4px;
          margin-bottom: 4px;
          padding: 0 4px;
          background: #f5f5f5;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
  .value {
    padding-left: 16px;
  }
}
.test-question-answer {
  padding: 16px 24px 16px 48px;
  border-radius: 8px;
  margin-top: 20px;
  .label {
    color: #626262;
    font-size: 12px;
    margin-right: 32px;
    white-space: nowrap;
  }
  .correct-answer,
  .stu-answer,
  .get-score {
    margin-bottom: 8px;
    font-size: 12px;
    display: flex;
    &:last-child {
      margin-bottom: 0;
    }
    .content {
      font-size: 12px;
      word-break: break-word;
    }
  }
  .score-record {
    font-size: 12px;
    color: rgba(0,0,0,0.45);
  }
  .answer-imgs {
    &:has(.item) {
      margin-top: 16px;
    }
    display: flex;
    :deep(.item) {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 8px;
      border-radius: 5px;
      overflow: hidden;
      .ant-image {
        height: 100%;
        width: 100%;
      }
      .ant-image-img {
        height: 100%;
        border-radius: 5px;
        object-fit: cover;
      }
    }
  }
  
  &.error {
    border: 1px solid #ff4d4f;
    background: url('@/assets/images/exam/error.png') no-repeat 95%, #fcf0f0;
  }
  &.part {
    border: 1px solid #faad14;
    background: url('@/assets/images/exam/part.png') no-repeat 95%, #faf4ee;
  }
  &.correct {
    background: #f6ffed;
    border: 1px solid #52c41a;
  }
}

.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  position: relative;
  left: -3px;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
  }
}
</style>

<style lang="less">
.popconfirm-editscore {
  .ant-popover-message-title {
    padding-left: 0;
  }
}
</style>
