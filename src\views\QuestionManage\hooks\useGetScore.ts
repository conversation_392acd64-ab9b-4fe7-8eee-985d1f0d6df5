export default (formState: any, fillBlank: any, scorepts: any, scoreInputTotalRef: any) => {
  // 获取分值
  const getScore = (score: any) => {
    formState.value.recordDuration = score
  }
  // 获取填空题分值
  const getFillBlankScore = (score: any, item: any) => {
    console.log(item)
    item.score = score
    let totalScore = 0
    fillBlank.value.forEach((item: any) => {
      totalScore += Number(item.score)
    })
    // scoreInputTotalRef.value.score = totalScore
    formState.value.score = totalScore
  }
  const getPointsScore = (score: any, item: any) => {
    item.score = score
    let totalScore = 0
    scorepts.value.forEach((item: any) => {
      totalScore += Number(item.score)
    })
    formState.value.score = totalScore
  }

  return {
    getScore,
    getFillBlankScore,
    getPointsScore
  }
}
