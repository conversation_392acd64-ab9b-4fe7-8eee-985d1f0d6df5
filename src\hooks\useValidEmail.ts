/**
 * 检查邮箱格式是否正确
 * @param {string} email - 待检查的邮箱地址
 * @returns {boolean} - 邮箱格式是否正确
 * @param email 
 */
export function isValidEmail(email: string) {
  // 判断是否仅包含一个 @ 符号
  if (!email.includes('@') || email.indexOf('@') !== email.lastIndexOf('@')) {
    return false
  }
  // 分割邮箱地址为用户名和域名部分
  const [username, domain] = email.split('@')
  // 判断用户名部分是否只包含有效字符
  const usernameRegex = /^[\w.+%" -]+$/
  if (!usernameRegex.test(username)) {
    return false
  }
  // 判断域名部分是否是有效的域名格式
  const domainParts = domain.split('.')
  if (domainParts.length < 2) {
    return false
  }
  for (const part of domainParts) {
    if (!/^[a-z0-9-]+$/i.test(part)) {
      return false
    }
  }

  // 1. 以点 . 开头 ".<EMAIL>"
  if (/^\./.test(username)) 
    return false
     
  // 2. 以点 . 结尾 "<EMAIL>"
  if (/\.$/.test(username)) 
    return false

  // 3. 包含空格 "<NAME_EMAIL>"
  if (/\s/.test(username)) 
    return false

  // 4. 包含两个连续的点 .. "<EMAIL>"
  if (/\.\./.test(username)) 
    return false

  // 5. 域名部分以短横线 - 开头 "<EMAIL>"
  if (domain.startsWith('-')) 
    return false

  return true
}
/**
 * 表单的检查邮箱格式是否正确
 */
export function checkEmailInForm() {
  return async (_rule: any, value: string) => {
    if (value === '') {
      return Promise.reject('请输入邮箱')
    }
    else if (value.length > 64) {
      return Promise.reject('邮箱地址超过最大长度')
    }
    else if (!isValidEmail(value)) {
      return Promise.reject('邮箱格式不正确')
    }
    else {
      return Promise.resolve()
    }
  }
}