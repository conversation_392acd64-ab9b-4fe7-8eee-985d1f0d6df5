<template>
  <a-layout-sider class="aside" v-model:collapsed="collapsed" :trigger="null" collapsible>
    <h1 class="menu-title" :class="{ collapsed }">
      <span :class="collapsed ? 'hide-text' : 'show-text'" style="display: flex; align-items: center;">
        <img src="@/assets/images/admin/admin-logo.svg" style="margin-right: 6px;cursor: pointer;" width="38" height="28" class="admin-login" alt="" @click="router.push('/admin/welcome')"  />
        图灵智面
      </span>
      <svg-icon
        :name="collapsed ? 'expand-menu' : 'collapse-menu'"
        @click="handleChangeCollapse"
        class="collapse-icon"
      />
    </h1>
    <a-menu
      mode="inline"
      v-model:openKeys="openKeys"
      v-model:selectedKeys="selectedKeys"
      @click="getMenuContent"
      @mouseover="handleMouseOver"
      style="overflow: hidden;"
    >
      <template v-for="item in menuList" :key="item.name">
        <template v-if="!item.children || item.children.length === 0">
          <a-menu-item :key="item.name" :title="item.title">
            <div class="menu-title-wrapper">
              <svg-icon :name="item.icon" class="menu-icon" :class="{ collapsed }" width="16px" height="16px" color="#000"/>
              <span :class="collapsed ? 'hide-text' : 'show-text'">{{ item.title }}</span>
            </div>
          </a-menu-item>
        </template>
        <template v-else>
          <a-sub-menu :key="item.name" :popupClassName="displayMenuPop ? 'hide-menu-pop' : ''">
            <template #title>
              <div class="menu-title-wrapper">
                <svg-icon :name="item.icon" class="menu-icon" :class="{ collapsed }" width="16px" height="16px" />
                <span :class="collapsed ? 'hide-text' : 'show-text'">{{ item.title }}</span>
              </div>
            </template>
            <template v-for="subItem in item.children" :key="subItem.name">
              <a-menu-item>
                <div class="menu-title-wrapper">
                  <svg-icon
                    :name="subItem.icon"
                    class="menu-icon"
                    :class="{ collapsed }"
                    width="16px"
                    height="16px"
                  />
                  <span :class="collapsed ? 'hide-text' : 'show-text'">{{ subItem.title }}</span>
                </div>
              </a-menu-item>
            </template>
          </a-sub-menu>
        </template>
      </template>
    </a-menu>
    <img v-if="!collapsed && store.getters.userInfo?.tryout" src="@/assets/images/pricing.png" alt="" style="cursor: pointer" @click="showPricing" />
    <div class="aside-bottom">
      <a-dropdown :trigger="['click']">
        <div class="user-box">
          <!--  v-if="store.getters.roles.includes('teacher')" -->
          <span style="display: flex; align-items: center;">
            <span class="dept-name" :class="collapsed ? 'hide-text' : 'show-text'">{{ deptName || '暂无部门' }}</span>
            <svg-icon name="angle-down2" style="margin-left: 8px;width: 18px;height: 18px;flex-shrink: 0;" />
          </span>
          <span :class="collapsed ? 'hide-text' : 'show-text'" style="display: flex; align-items: center;">
            {{ username }}
            <svg-icon v-if="store.getters.roles.includes('padmin')" name="angle-down2" style="margin-left: 8px;width: 18px;height: 18px;" />
          </span>
        </div>
        <template #overlay>
          <a-menu>
            <!-- v-if="store.getters.roles.includes('teacher')" -->
            <a-menu-item @click="changeDepartmentVisible = true">
              <span>切换部门</span>
            </a-menu-item>
            <a-menu-item @click="changePwdDialogVisible = true">
              <span>修改密码</span>
            </a-menu-item>
            <a-menu-item @click="loginOut">
              <span>登出</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </a-layout-sider>
  <a-modal title="修改密码" v-model:visible="changePwdDialogVisible" width="600px" :maskClosable="false" :keyboard="false" @ok="handlePwdChange">
    <ChangePwd v-if="changePwdDialogVisible" ref="changePwdRef" @success="changePwdDialogVisible = false"></ChangePwd>
  </a-modal>
  <a-modal title="切换部门" v-model:visible="changeDepartmentVisible" width="450px" :maskClosable="false" :keyboard="false" @ok="handleChangeDepartment">
    <ChangeDepartment ref="changeDepartmentRef" v-if="changeDepartmentVisible"></ChangeDepartment>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineComponent, ref, computed, watch, onBeforeUnmount, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { teacherMenuList } from './menuList'
import { message } from 'ant-design-vue'
import ChangePwd from '@/pages/system/ChangePwd.vue'
import ChangeDepartment from '@/pages/system/ChangeDepartment.vue'
import { upgradevip } from '@/api/user'

const store = useStore()
const router = useRouter()
const route = useRoute()

const collapsed = ref(false)
const handleChangeCollapse = () => {
  collapsed.value = !collapsed.value
  store.commit('MODIFY_COLLAPSE', collapsed.value)
}

function getMenuContent({ keyPath }: any) {
  router.push({ name: `${keyPath[keyPath.length - 1]}` })
}
const openKeys = ref<string[]>([])
const selectedKeys = <any>ref([])

const mainTextColor = computed(() => {
  return store.getters.mode === 'sun' ? '#121633' : 'rgba(255,255,255,0.65)'
})
const subTextColor = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(0,0,0,0.65)' : 'rgba(255,255,255,0.65)'
})

const displayMenuPop = ref(false)
const handleMouseOver = () => {
  displayMenuPop.value = false
}

const menuList = computed(() => {
  // return store.getters.userInfo.roles[0] === 'padmin' ? systemMenuList : teacherMenuList
  return teacherMenuList

})

watch(
  () => route.path,
  (val,oldVal) => {
    const newSelectedKey = val.split('/').at(-1)
    const newOpenKey = val.split('/').at(-2)

    // 检查新的 selectedKey 是否能在 menuList 中找到匹配项
    const canMatchSelected = menuList.value.some(item => {
      if (item.name === newSelectedKey) return true
      return item.children?.some(child => child.name === newSelectedKey)
    })

    // 检查新的 openKey 是否能在 menuList 中找到匹配项
    const canMatchOpen = menuList.value.some(item => item.name === newOpenKey)

    // 只有在能匹配到时才更新 keys
    if (canMatchSelected) {
      selectedKeys.value = [newSelectedKey]
    }
    if (canMatchOpen) {
      openKeys.value = [newOpenKey as any]
    }

    displayMenuPop.value = true
    store.commit('MODIFY_MODE', 'sun')
  },
  { immediate: true }
)



// 用户信息
const username = computed(() => store.getters.username)
const deptName = computed(() => store.getters.userInfo.deptName)
// 切换部门
const changeDepartmentRef = ref<InstanceType<typeof ChangeDepartment>>()
const changeDepartmentVisible = ref(false)
async function handleChangeDepartment() {
  await changeDepartmentRef.value?.confirm()
  changeDepartmentVisible.value = false
}

const handleChangeMode = (mode: string) => {
  if (store.getters.mode === 'sun') {
    store.commit('MODIFY_MODE', mode)
  } else {
    store.commit('MODIFY_MODE', mode)
  }
}
const loginOut = () => {
  store.dispatch('USER_LOGIN_OUT').then((data) => {
    message.success(data)
    if (store.getters.username === 'admin') {
      router.replace('/admin-login')
    } else {
      router.replace('/login')
    }
    sessionStorage.removeItem('loginFail')
    setTimeout(() => {
      location.reload()
    }, 0)
  })
}

// 修改密码弹框
const changePwdDialogVisible = ref(false)
const changePwdRef = ref<InstanceType<typeof ChangePwd>>()
function handlePwdChange() {
  changePwdRef.value?.submit()
}

// 开会员
async function showPricing() {
  try {
    await upgradevip({ id: store.getters.userInfo.id })
    message.success('已收到您的需求，一个工作日内相关人员会联系您，请耐心等候')
  } catch (error) {
    console.log(error)
  }
}

</script>


<style lang="less" scoped>
body.moon .aside {
  background: #181A20;
  .menu-title {
    border-bottom: 1px solid #2d2d2e;
  }
  .aside-bottom {
    border-top: 1px solid #2d2d2e;
    color: rgba(255,255,255,0.85);
  }

  .collapse-icon {
    color: rgba(255,255,255,0.85);
  }
  :deep(.ant-menu) {
    .ant-menu-item {
      border-radius: 8px;
    }

    .ant-menu-item-active {
      background-color: rgba(84, 120, 238, 0.2);
      color: #5478ee;
    }
  }
}


.aside {
  background: url(@/assets/images/aside-bg.webp) no-repeat center center;
  background-size: cover;
  :deep(.ant-layout-sider-children) {
    display: flex;
    flex-direction: column;
    padding: 0 12px!important;
    .aside-bottom {
      height: 60px;
      margin: 12px 12px 0 12px;
      border-top: 1px solid #dfe1e6;
      .user-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        cursor: pointer;
        .dept-name {
          max-width: 100%; 
          overflow: hidden; 
          text-overflow: ellipsis;
          &.hide-text {
            width: 0;
          }
        }
      }
    }
  }

  .ant-menu-vertical {
    border: none;
  }
  :deep(.ant-menu) {
    .ant-menu-item-active {
      background-color: #dde5ff;
      border-radius: 8px;
    }
    
    .ant-menu-submenu-title:not(.ant-menu-inline-collapsed .ant-menu-submenu-title) {
      padding-left: 12px!important;
    }
    
    .ant-menu-item:not(.ant-menu-submenu .ant-menu-item).ant-menu-item:not(.ant-menu-inline-collapsed .ant-menu-item) {
      padding-left: 12px!important;
    }

    .ant-menu-submenu .ant-menu-item {
      padding-left: 36px!important;
    }

    // 收起状态
    &.ant-menu-inline-collapsed {
      .ant-menu-submenu-title, .ant-menu-item:not(.ant-menu-submenu .ant-menu-item) {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.ant-layout-sider {
  .menu-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 600;
    color: v-bind(mainTextColor);
    margin: 0 12px 12px 12px;
    padding: 20px 0;
    border-bottom: 1px solid #dfe1e6;

    &.collapsed {
      justify-content: center;
      .hide-text + .collapse-icon {
        margin-left: 0;
      }
    }

    .collapse-icon {
      flex-shrink: 0;
      height: 21px!important;
      cursor: pointer;
    }
    .hide-text + .collapse-icon {
      margin-left: 5px;
    }
    .hide-text {
      display: none;
    }
  }
  .ant-menu {
    flex: 1;
    min-height: 0;
    overflow: auto;
    background: none;
    color: v-bind(mainTextColor) !important;
  }
  .menu-title-wrapper {
    display: flex;
    align-items: center;
  }
  :deep(.menu-icon) {
    flex-shrink: 0;
    margin-right: 8px;
    &.collapsed {
      margin-right: 0;
    }
  }
  .hide-text {
    width: 0;
    overflow: hidden;
    white-space: nowrap;
    transition: all ease 0.2s;
  }
  .show-text {
    white-space: nowrap;
  }
  :deep(.ant-menu-submenu-arrow) {
    color: v-bind(subTextColor) !important;
  }
  :deep(.ant-menu-sub.ant-menu-inline) {
    background: none;
    color: v-bind(subTextColor);
  }
  :deep(.ant-menu-inline) {
    border-right: 0;
  }
  :deep(.ant-menu-item-selected) {
    color: #5478ee;
    background-color: #fff!important;
    border-radius: 8px;
    display: flex;
    align-items: center;
  }
  :deep(.ant-menu-item::after) {
    border: none;
  }

  .polygon-icon {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: -20px;
    top: 50%;
    .control-icon {
      position: absolute;
      pointer-events: none;
    }
  }
}
.admin-menu {
  height: 100%;
  .ant-menu-item {
    padding: 0;
    .ant-menu-title-content {
      text-align: center;
      font-family: PingFangSC-Regular;
      font-size: 18px;
      color: #666666;
    }
  }
}
</style>
<style lang="less">
.ant-menu-submenu-popup {
  ul {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .ant-menu-item {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .menu-title-wrapper {
    display: flex;
    align-items: center;
    .menu-icon {
      margin-right: 8px;
    }
  }
}
.admin-menu {
  height: 100%;
  padding-top: 10px !important;

  li.ant-menu-item {
    margin-top: 8px;
    padding: 0 !important;
    height: 48px !important;
    &::after {
      border-right-color: #3158bd;
    }
    .ant-menu-title-content {
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;
      // justify-content: center;
      align-items: center;
      text-align: center;
      padding-left: 30px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-menu-item.ant-menu-item-selected {
    background: #3158bd !important;
    .menu-icon {
      color: #fff !important;
    }
    .ant-menu-title-content {
      color: #fff;
      font-weight: 600;
    }
  }
}
.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  width: 100% !important;
  height: 48px !important;
  line-height: 48px !important;
}
.hide-menu-pop {
  display: none;
}
.ant-menu-vertical > .ant-menu-item {
  height: 48px;
  line-height: 48px;
  margin: 0!important;
}
.ant-menu-submenu-popup .ant-menu-item-selected {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    height: 100%;
    width: 2px;
    left: 0;
    background-color: #5478EE;
  }
}

.ant-menu-submenu-vertical .ant-menu-submenu-title {
  height: 48px!important;
  line-height: 48px!important;
  display: flex;
  align-items: center;
}
</style>
