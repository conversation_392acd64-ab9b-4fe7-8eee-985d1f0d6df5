<template>
    <div class="paper-question-select">
        <div class="top-bar">
            <a-button type="primary" @click="showNormalQustionModal"><plus-outlined />常规试题</a-button>
            <a-popover placement="bottom" v-model:visible="popoverVisible" trigger="click">
                <template #content>
                    <span style="cursor: pointer;" @click="showOptionalQustionModal(QuestionEnum['问答题'])">问答题</span>
                    <a-divider style="margin: 10px 0;" />
                    <span style="cursor: pointer;" @click="showOptionalQustionModal(QuestionEnum['算法题'])">算法题</span>
                </template>
                <a-button> <plus-outlined />选做试题</a-button>
            </a-popover>
            <a-badge :count="excludeTotal" :overflow-count="999">
                <a-button @click="paperModalVisible = true"> <filter-outlined />排除试题</a-button>
            </a-badge>
        </div>
        <div class="content">
            <PaperQuestionForm v-show="list.length" v-model:list="list" :name="paperInfo?.name" :isIndividualTiming="paperInfo?.individualTiming" ref="paperFormRef" :excludedIds="excludedList"
                @edit="handleEdit">
            </PaperQuestionForm>
            <div v-show="!list.length" class="empty-container">
                <img width="330" src="@/assets/images/nodata.png" alt="">
                <div class="description">
                    <p>当前试卷暂无试题</p>
                    <p>请点击<span class="theme-text-color">【+常规试题】</span>为该场考试创建试卷</p>
                </div>
                <a-button type="primary" @click="showNormalQustionModal"><plus-outlined />常规试题</a-button>
            </div>
        </div>
        <a-modal :title="modalTitle" v-model:visible="questionModalVisible" width="80%" centered wrapClassName="select-modal"
            :footer="null" :maskClosable="false" @cancel="paperFormRef?.resetCurrentId()">
            <QuestionSelect v-if="questionModalVisible" :defaultSelectedRows="defaultSelectedRows"
                :defaultExcludedRowKeys="excludedList" :defaultType="defaultType" :initialOptions="initialOptions"
                :disabledRowKeys="disabledRowKeys" :allSelectRows="list" @confirm="handleQustionSelectConfirm"
                @close="paperFormRef?.resetCurrentId(), questionModalVisible = false"></QuestionSelect>
        </a-modal>
        <a-modal title="排除试题" v-model:visible="paperModalVisible" width="60%" centered wrapClassName="select-modal" :footer="null"
            :maskClosable="false">
            <PaperSelect v-if="paperModalVisible" :defaultSelectedMap="excludedMap" @confirm="handlePaperSelectConfirm"
                @close="paperModalVisible = false"></PaperSelect>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, h, nextTick } from 'vue'
import { PlusOutlined, FilterOutlined } from '@ant-design/icons-vue';
import QuestionSelect from '../questionManage/QuestionSelect.vue';
import PaperQuestionForm from './PaperQuestionForm.vue';
import PaperSelect from './PaperSelect.vue';
import { QuestionEnum } from '@/models/questionModel';
import { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'
import { uuid } from '@/utils';
import { useRoute } from 'vue-router';
import { Modal } from 'ant-design-vue';
import emitter from '@/utils/bus';
import _ from 'lodash';

const props = defineProps<{
  paperInfo?: any
}>()

const emits = defineEmits<{
  (e: 'change', value: any): void
}>()

const route = useRoute()

const list = ref<PaperFbodyItemModelWithQuestionChildren[]>([])
const paperFormRef = ref<InstanceType<typeof PaperQuestionForm>>()


// 详情
async function getPaperDetail() {
    if (!props.paperInfo) return
    try {
        let res: any = _.cloneDeep(props.paperInfo)
        // 如果是克隆/再来一卷则剔除掉已删除的题目
        let delQuestionIds = res.del_ques
        if ((route.path.includes('copy') || route.path.includes('oneMore')) && delQuestionIds?.length) {
            res.fbody.forEach((item: any) => {
                item.children = item.children.filter((q: any) => !delQuestionIds.includes(q.id))
            })
            res.fbody = res.fbody.filter((item: any) => item.children.length)
        }
        // 数据预处理
        res.fbody.forEach((item: any) => {
            item.children.forEach((i: any) => {
                i.category_name = i.catg_name
                i.new_score = i.score
            })
            // 判断是否是统一分值
            let tempUnifyScore = 1
            if (item.type === QuestionEnum['填空题']) {
                tempUnifyScore = item.children[0].score / JSON.parse(item.children[0].answer).length
                item.isUnify = item.children.every((k: any) => JSON.parse(k.answer).every((t: any) => t.score === tempUnifyScore))
            } else {
                tempUnifyScore = item.children[0].score
                item.isUnify = item.children.every((i: any) => i.score === tempUnifyScore)
            }
            // 给大题加上唯一标识，用于编辑时区分哪个答题
            item.id = uuid()
            if (item.isUnify) item.unifyScore = tempUnifyScore
        })
        list.value = res.fbody
        await nextTick() // 等待paperFormRef初始化完成(因为它通过list.length控制显隐的)
        paperFormRef.value?.init()
        excludedMap.value = res.notpaperids
        emits('change', { fbody: res.fbody })
    } catch (error) {
        console.log(error)
    }
}

// 添加试题
const questionModalVisible = ref(false)
const modalTitle = ref('')
const defaultSelectedRows = ref<any[]>([]) // 用于回显的试题列表
const defaultType = ref<number | null>(null) // 用于预筛选题型
const disabledRowKeys = ref<string[]>([]) // 用于禁用的选择项，除自身以外的ids
const initialOptions = ref(0) // 选做题
const popoverVisible = ref(false)
function showOptionalQustionModal(type: number) {
    initialOptions.value = 1 // 默认选做一道
    modalTitle.value = `添加${QuestionEnum[type]}选做题`
    disabledRowKeys.value = list.value.map(item => item.children.map(i => i.id)).flat() // 禁选所有已选题目
    defaultSelectedRows.value = []
    showQuestionModal(type)
}
function showNormalQustionModal() {
    defaultType.value = null
    initialOptions.value = 0
    modalTitle.value = '添加常规试题'
    disabledRowKeys.value = list.value.filter(item => item.options).map(item => item.children.map(i => i.id)).flat() // 禁选已选选做题中的题目
    defaultSelectedRows.value = list.value.filter(item => !item.options).map(item => item.children).flat()
    showQuestionModal()
}
function showQuestionModal(type?: number) {
    defaultType.value = type ?? null
    popoverVisible.value = false
    questionModalVisible.value = true
}
function handleQustionSelectConfirm(value: { addList?: string[], delList?: string[] }, options?: number) {
    if (!paperFormRef.value) return
    paperFormRef.value.updateList(value, options)
    questionModalVisible.value = false
    paperFormRef.value?.resetCurrentId()
}

// 排除试题
const excludedMap = ref<Record<string, string[]>>({})
const excludedList = computed(() => {
    return [...new Set(Object.values(excludedMap.value).flat())]
})
const excludeTotal = computed(() => {
    let ans = 0
    list.value.forEach(item => {
        item.children.forEach(i => {
            if (excludedList.value.includes(i.id)) ans++
        })
    })
    return ans
})
const paperModalVisible = ref(false)
function handlePaperSelectConfirm(value: Record<string, string[]>) {
    excludedMap.value = value
    paperModalVisible.value = false
}

// 编辑某一题型
function handleEdit(item: PaperFbodyItemModelWithQuestionChildren) {
    modalTitle.value = '编辑' + item.title
    initialOptions.value = item.options || 0
    defaultSelectedRows.value = item.children
    disabledRowKeys.value = list.value.filter(i => i.id !== item.id).map(item => item.children.map(i => i.questionBankUUID)).flat() // 禁选所有除自身以外的已选题目
    showQuestionModal(item.type)
}

// 是否可以下一步
const canNext = computed(() => {
    let arr = paperFormRef.value?._list
    return arr?.length && arr.every(item => item.children.every(q => q.new_score && q.duration)) // 无论是否单题限时，每一道题目都应该有自己的duration时长且不为0。
})

// 记录本次未校对/校对错误的题目id
let uncheckIds: string[] = []
let checkErrorIds: string[] = []

function validateForm() {
    return new Promise((resolve, reject) => {
        list.value = paperFormRef.value?._list || []
        if (!list.value.length) {
            reject(new Error('请选择试题'))
        } else if (!canNext.value) {
            reject(new Error('请检查是否有题目的分值或时间未设置'))
        } else {
            // 判断是否有未校对/校对错误的题目
            let newUncheckIds = getCountByProofreading(0)
            let newCheckErrorIds = getCountByProofreading(2)
            if (newUncheckIds.some(i => !uncheckIds.includes(i)) || newCheckErrorIds.some(i => !checkErrorIds.includes(i))) {
                uncheckIds = newUncheckIds
                checkErrorIds = newCheckErrorIds
                Modal.confirm({
                    title: '提醒',
                    content: () => {
                        let text: any = [h('p', '请注意！您选择的试题中包含：')]
                        if (checkErrorIds.length) {
                            text.push(
                                h('p', [
                                    h('span', { style: { color: '#5478EE' } }, checkErrorIds.length),
                                    '道校对错误'
                                ])
                            )
                        }
                        if (uncheckIds.length) {
                            text.push(
                                h('p', [
                                    h('span', { style: { color: '#5478EE' } }, uncheckIds.length),
                                    '道未校对'
                                ])
                            )
                        }
                        return h('div', text)
                    },
                    onCancel() {
                        uncheckIds = []
                        checkErrorIds = []
                        // 这里如果reject会导致进度条上方展示红色错误
                        // 如果resolve会导致校验成功下一步
                        // 所以这里直接return， 不走.then 也不走.catch
                        return
                    },
                    cancelText: '返回修改',
                    onOk() {
                        resolve(list.value)
                    },
                    okText: '继续'
                })
            } else {
                resolve(list.value)
            }
            emits('change', {
                fbody: list.value,
                score: paperFormRef.value?.totalScore || 0,
                notpaperids: excludedMap.value
            })
        }
    })
}


/** 
 * 根据校对状态统计选题中题目数量
 * @param proofreading 0未校对 1已校对 2校对错误
 * @returns 题目id数组
 */
function getCountByProofreading(proofreading: 0 | 1 | 2): string[] {
    return list.value.flatMap(item => item.children.filter(i => i.proofreading === proofreading).map(i => i.id))
}


// 单题是否限时，对当前界面的影响 （这里只想处理用户手动点击的情况，所以使用事件总线通信，而不能用watch，否则编辑回显会出错）
emitter.on('paper-isindividualtime-swich', onIndividualChange)
function onIndividualChange(val: string) {
  if (val === '1') {
    list.value.forEach(item => {
        if (item.options) {
            // 这里修改后不用再改父组件的paperinfo的fbody，因为它们是同一个引用
            item.options = 1
            item.title = paperFormRef.value?.generateTitle(item.type, item.children.length, item.options)
        }
    })
    // 首次编辑试卷时, list.value 和 paperFormRef.value?._list 还不是同一个引用，所以这里需要手动同步一下
    paperFormRef.value?.init()
  }
}

defineExpose({
    canNext,
    validateForm,
    getPaperDetail
})

</script>
<style lang="less" scoped>
.paper-question-select {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .ant-btn {
        border-radius: 8px;
        margin-left: 8px;
        font-size: 14px;
    }

    .top-bar {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 10px;
    }

    .content {
        flex: 1;
        min-height: 0;
        margin-top: 16px;
        display: flex;
        flex-direction: column;

        >div {
            width: calc(100% - 10px);
            height: 100%;
        }

        .empty-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .description {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                margin-bottom: 15px;

                p {
                    line-height: 20px;
                    text-align: center;
                }
            }

            .ant-btn {
                height: 40px;
            }
        }
    }
}
</style>