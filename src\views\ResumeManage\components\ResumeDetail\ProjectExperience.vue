<script setup lang="ts">
import type { ProjectItemProps } from './shared'
import BaseExperienceCard from './BaseExperienceCard.vue'
// 模拟工作经历数据
withDefaults(defineProps<{
  projectExperienceData: ProjectItemProps
}>(), {
  projectExperienceData: () => ([]),
})
</script>

<template>
  <BaseExperienceCard v-show="projectExperienceData.length > 0" title="项目经历">
    <div v-for="(item, index) in projectExperienceData" :key="index" class="mb-6 last:mb-0">
      <div class="flex items-start mb-2 ">
        <div class="flex-1">
          <div class="flex justify-between">
            <div>
              <span class="font-medium text-base">{{ item.name }}</span>
              <span class="text-gray-500 mx-2" />
              <span class="text-gray-600 text-xs">{{ item.role }}</span>
              <!-- <span class="text-gray-500 mx-2">|</span>
              <span class="text-gray-600 text-xs">{{ item.technologies }}</span> -->
            </div>
            
            <div v-if="item.startDate === '未知' && item.endDate === '未知'" class="text-gray-500">
              未明确说明
            </div>
            <div v-else class="text-gray-500">
              {{ item.startDate }} 至 {{ item.endDate }}
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600 whitespace-pre-line leading-[18px]">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </BaseExperienceCard>
</template>
