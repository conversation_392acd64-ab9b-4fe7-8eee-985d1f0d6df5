;(function () {
  function resize() {
    const baseFontSize = 100 // 1rem = 100px
    const designWidth = 1920 //设计稿宽度
    const width = window.innerWidth // 屏幕宽度
    const cur = (width / designWidth) * baseFontSize
    let currentFontSize

    if (cur > 100) {
      currentFontSize = 100
    } else if (cur > 60) {
      currentFontSize = cur
    } else {
      currentFontSize = 60
    }

    // document.querySelector('html')!.style.fontSize = currentFontSize + 'px'
    document.querySelector('html')!.style.fontSize = 16 + 'px'
  }
  window.onresize = function () {
    resize()
  }

  document.addEventListener('DOMContentLoaded', resize)
})()
