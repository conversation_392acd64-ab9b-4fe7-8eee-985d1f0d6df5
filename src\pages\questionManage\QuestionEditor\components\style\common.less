.tag-select-wrapper {
      .rc-virtual-list-holder-inner {
            padding-left: 4px;
            display: flex;
            // flex-direction: row !important;
            flex-wrap: wrap;
      }

      .ant-select-item {
            margin-right: 4px;
            margin-bottom: 4px;
      }

      .ant-select-item-option-content {
            margin-right: 4px;
      }

      .ant-select-item-option-state {
            display: none;
      }
}

.ant-select-item-option-content {
      font-size: 12px;
}

.jse-menu {
      display: none !important;
}

.jse-main {
      height: 560px !important;
      margin-bottom: 16px;
}

.draft-cancel {
      .ant-modal-confirm-btns {
            display: flex;
            flex-direction: row-reverse;

            .ant-btn {
                  margin-right: 8px;

                  &:first-child {
                        margin-right: 70px;
                  }
            }
      }

      .ant-modal-close {
            bottom: 32px;
            right: 32px;
            top: auto;
            height: 32px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            color: rgba(0, 0, 0, 0.65);

            &:hover,
            &:focus {
                  color: #7fa0fa;
                  border-color: #7fa0fa;
            }
      }

      .ant-modal-close-x {
            width: auto !important;
            padding: 0 15px;
            height: 32px;
            line-height: 32px !important;
            font-size: 14px;
            font-weight: 500;
      }
}