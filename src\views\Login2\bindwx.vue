<template>
  <div class="bindwx-main">
    <bg></bg>
    <div class="content">
      <div class="content-main">
        <!-- 绑定微信 -->
        <a-form
          v-if="router.currentRoute.value.fullPath.indexOf('/register/bindwx') > -1"
          :model="formState"
          :rules="rules"
          ref="formRef"
        >
          <h1>绑定微信</h1>
          <div class="alertmsg">
            <svg-icon class="svgClass" style="width: 16px; height: 16px" name="tip" />
            <p>该账号已存在，请输入账号和密码绑定微信</p>
          </div>
          <a-form-item ref="email" name="email">
            <a-input class="account" v-model:value="formState.email" placeholder="输入邮箱" />
          </a-form-item>
          <a-form-item ref="password" name="password">
            <a-input
              class="pwd"
              v-model:value="formState.password"
              placeholder="输入密码"
              type="password"
            />
          </a-form-item>
          <a-button class="confirm" type="primary" @click="confirm">确认</a-button>
          <p @click="toLogin">暂不绑定</p>
        </a-form>
        <!-- 个人信息完善 -->
        <a-form
          v-else-if="router.currentRoute.value.fullPath.indexOf('/register/perinfo') > -1"
          :model="formState"
          :rules="rules"
        >
          <h1>个人信息完善</h1>
          <div class="alertmsg">
            <svg-icon class="svgClass" style="width: 16px; height: 16px" name="tip" />
            <p>该微信号尚未与考试系统绑定，请完善个人信息</p>
          </div>
          <a-form-item ref="idcard" name="idcard">
            <a-input class="account" v-model:value="formState.idcard" placeholder="身份证号" />
          </a-form-item>
          <a-form-item ref="email" name="email">
            <a-input class="pwd" v-model:value="formState.email" placeholder="邮箱" />
          </a-form-item>
          <a-button class="confirm" type="primary" @click="submit">确认</a-button>
          <p @click="toLogin">暂不完善</p>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import bg from './components/bg.vue'
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { registerCheck, regandbind } from '@/api/login'
import store from '@/store'
import { message } from 'ant-design-vue'

const formRef = ref()
const router = useRouter()

const formState = reactive({
  idcard: '',
  email: '',
  password: ''
})

// 返回登录页
function toLogin() {
  router.push('/login')
}

// 确认
const route = useRoute()
const submit = () => {
  regandbind({ idcard: formState.idcard, email: formState.email, openid: route.query.openid }).then(
    (res: any) => {
      if (res.access_token) {
        store.commit('SET_ACCESS_TOKEN', res.access_token)
        store.commit('SET_REFRESH_TOKEN', res.refresh_token)
        router.push('/exam')
      } else if (res === 'registered') {
        router.push('/register/bindwx')
      }
    }
  )
}

// 账号已存在， 绑定微信后 登录
const confirm = () => {
  formRef.value.validate().then(() => {
    store.dispatch('USER_LOGIN', formState).then((data) => {
      message.success('登录成功！')
      if (data.role === 'student') {
        router.push('/exam')
      } else {
        router.push('/admin')
      }
    })
  })
}

// 验证邮箱
const checkEmail = async (rule: RuleObject, value: string) => {
  const email =
    /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9.]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
  if (value === '') {
    return Promise.reject('请输入邮箱')
  } else if (!email.test(value)) {
    return Promise.reject('邮箱格式不正确')
  }
}

const checkRegisterInfo = (param: any) => {
  return registerCheck(param)
}

// 验证身份证号码
const checkCardNo = async (rule: RuleObject, value: string) => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (value === '') {
    return Promise.reject('请输入身份证号')
  } else if (!reg.test(value)) {
    return Promise.reject('身份证输入不合法')
  } else {
    checkRegisterInfo({ type: 'id_card_num', value: value })
      .then(() => {
        return Promise.resolve()
      })
      .catch(() => {
        return Promise.reject('该身份证号已注册')
      })
  }
}

const rules = {
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  email: [{ required: true, validator: checkEmail, trigger: 'blur' }],
  idcard: [{ required: true, validator: checkCardNo, trigger: 'blur' }]
}
</script>

<style lang="less" scoped>
.bindwx-main {
  position: relative;
  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    width: 572px;
    height: 494px;
    background: #ffffff;
    border-radius: 16px;
    .content-main {
      width: 372px;
      position: absolute;
      left: 100px;
      top: 64px;
      h1 {
        font-size: 24px;
        font-family: PingFang SC, PingFang SC-6;
        font-weight: 500;
        text-align: left;
        line-height: 32px;
        color: rgb(0, 0, 0, 0);
        margin-bottom: 32px;
      }
      .alertmsg {
        position: relative;
        width: 372px;
        height: 32px;
        background: rgba(84, 120, 238, 0.15);
        border-radius: 4px;
        margin-bottom: 16px;
        .svgClass {
          position: absolute;
          top: 8px;
          left: 16px;
        }
        p {
          position: absolute;
          left: 40px;
          top: 5px;
          height: 22px;
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-5;
          font-weight: 400;
          text-align: left;
          color: #000000;
          line-height: 22px;
        }
      }
      .account {
        width: 372px;
        height: 52px;
        background: #f5f5f6;
        border: 0.5px solid rgba(160, 160, 160, 0.5);
        border-radius: 8px;
        // margin-bottom: 32px;
      }
      .pwd {
        width: 372px;
        height: 52px;
        background: #ffffff;
        border: 0.5px solid rgba(37, 43, 58, 0.5);
        border-radius: 8px;
        // margin-bottom: 32px;
      }
      .confirm {
        width: 372px;
        height: 48px;
        border-radius: 8px;
        margin-bottom: 16px;
      }
      p {
        width: 372px;
        height: 22px;
        font-size: 14px;
        font-family: PingFang SC, PingFang SC-5;
        font-weight: 400;
        text-align: center;
        color: #8c8c8c;
        line-height: 22px;
        cursor: pointer;
      }
    }
  }
}
</style>
