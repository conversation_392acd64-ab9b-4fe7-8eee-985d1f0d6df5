<template>
  <div class="question-manage">
    <div class="question-manage-header">
      <div class="header-left">
        <a-input-search
          v-model:value.trim="searchContent"
          placeholder="请输入题干"
          allow-clear
          @search="getFirstPageList"
        />
        <div style="display: flex;align-items: center;">
          <a-checkbox v-model:checked="includeSubCatg" @change="getFirstPageList">包含子题库试题</a-checkbox>
          <a-tooltip placement="right" overlayClassName="light">
            <template #title>
              <span>勾选后，题目列表包含所选题库及其子题库的题目</span>
            </template>
            <svg-icon class="common-info-icon" name="info2"></svg-icon>
          </a-tooltip>
        </div>
        <a-radio-group
          v-if="!writable"
          v-model:value="listType"
          size="large"
          @change="getFirstPageList"
        >
          <a-radio v-for="item in listTypeEnum" :value="item">
            <div style="display: flex; align-items: center; white-space: nowrap;">
              <span>{{ item }}</span>
              <span v-if="item !== '可选'" style="padding-left: 2px; line-height: 1.8">
                (<a-badge
                  :count="item === '已选' ? selectedRowKeys.length : defaultExcludedRowKeys.length"
                  :overflow-count="999"
                  :number-style="{
                    backgroundColor: '#fff',
                    color: '#5478EE',
                    padding: '0',
                    fontSize: '14px'
                  }"
                  :showZero="true"
                />)
              </span>
            </div>
          </a-radio>
        </a-radio-group>
      </div>
      <div
        v-if="
          writable &&
          store.getters.permiss.quesmag.write &&
          (store.getters.userInfo.is_official_cert || !isOfficialSubject)
        "
        class="header-right"
      >
        <a-button type="primary" @click="showForm('')">新增题目</a-button>
        <a-button @click="handleBatchDelete">批量删除</a-button>
      </div>
    </div>
    <a-table
      class="questions-table common-table"
      sticky
      :columns="tableColumns"
      :row-key="(record: any) => record.id"
      :data-source="list"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onSelect: updateSelectedRowKeys,
        onSelectAll: onSelectAll,
        getCheckboxProps: (record: any) => ({ disabled: disabledRowKeys.includes(record.id) || (isOfficialSubject && !store.getters.userInfo.is_official_cert) })
      }"
      :loading="loading"
      :scroll="{ x: 1200, y: 'calc(98% - 20px)' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'body'">
          <div style="display: flex;align-items: start;">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>{{
                  record.complicatedediting ? record.body?.slice(0, 100) : record.body
                }}</template>
                <span class="question-content" v-html="getQuestionContentByRecord(record)"></span>
              </a-tooltip>
            </span>
            <span class="recover-tag" v-if="record.recover && writable">已恢复</span>
            <span class="exclude-tag" v-if="defaultExcludedRowKeys.includes(record.id)"
              >已排除</span
            >
          </div>
        </template>
        <template v-else-if="column.key === 'types'">
          <span>{{ getType(record.type) }}</span>
        </template>
        <template v-else-if="column.key === 'difficulty'">
          <svg-icon v-for="i in getStarByScore(record.difficulty)" name="active_star" width="16px" height="16px" style="margin-top: 4px;"></svg-icon>
          <!-- 防止展示0 -->
          <span></span>
        </template>
        <template v-else-if="column.key === 'proofreading'">
          <div style="display: flex; align-items: center;">
            <span :class="[['status_gray', 'status_green', 'status_red'][record.proofreading]]"></span>
            <img v-if="record.proofreading_info?.is_ai" style="margin-right: 4px;" src="@/assets/icons/svg/ai.svg" alt="">
            <span>{{ ['未校对', '校对正确', '校对错误'][record.proofreading]}}</span>
          </div>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-button type="link" @click="previewVideo(record)">视频预览</a-button>
            <a-divider type="vertical" />
            <a-button type="link" @click="proofreadQuestion(record)">校对</a-button>
            <a-divider type="vertical" />
            <a-button type="link" :disabled="!store.getters.userInfo.is_official_cert && record.official_cert" @click="showForm(record.id)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm
                title="确定删除该题目？"
                ok-text="确定"
                cancel-text="取消"
                placement="left"
                @confirm="deleteQuestion(record)"
                :disabled="!store.getters.userInfo.is_official_cert && record.official_cert"
              >
                <a-button type="link" :disabled="!store.getters.userInfo.is_official_cert && record.official_cert">删除</a-button>
            </a-popconfirm>
          </span>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded-row">
          <QuestionItemDisplay
            :question-detail="record"
            show-type
            show-correct-answer
            show-question-difficulty
            show-question-points-and-basis
            show-question-proofreading-record
            :option-letter-type="'text'"
          />
        </div>
      </template>
    </a-table>
  </div>
  <PagePreview title="视频预览" v-model:visible="questionVideoVisible" :modes="['mobile']">
    <div style="height: 32px;margin: 32px 0 24px;display: flex;justify-content: space-between;align-items: center;padding: 0 18px;">
      <left-outlined />
      <span style="color: #333333;font-weight: bold;font-size: 16px;">图灵智面</span>
      <div style="width: 14px;"></div>
    </div>
    <video style="width: 100%;" controls type="video/webm" :src="videoUrl"></video>
    <p style="margin-top: 23px;padding: 0 16px">{{ currentItem?.body }}</p>
  </PagePreview>
  <a-modal title="新增题目" v-model:visible="formVisible" wrapClassName="full-screen-modal" :maskClosable="false" :closable="false" :keyboard="false" :footer="null" destroyOnClose>
    <QuestionForm :subjectId="subjectId" :qid="qid" @cancel="onFormClose"></QuestionForm>
  </a-modal>
  <a-modal title="题目校对" v-model:visible="questionProofreadFormVisible" wrapClassName="full-screen-modal proofread-modal" :maskClosable="false" :keyboard="false" :footer="null" @cancel="getList">
    <InterviewQuestionProofreadForm v-if="questionProofreadFormVisible" v-model:qid="qid" @close="(questionProofreadFormVisible = false), getList()" :getSiblingQuestionFn="getSiblingQuestion" :lastBtnDisabled="lastBtnDisabled" :nextBtnDisabled="nextBtnDisabled"></InterviewQuestionProofreadForm>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, createVNode, nextTick, computed, h, onActivated, onDeactivated } from 'vue'
import { beforedelques } from '@/api/admin/questionManage'
import { message, Modal } from 'ant-design-vue'
import QuestionItemDisplay from '@/pages/interview-question-manage/InterviewQuestionItemDisplay/index.vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined, InfoCircleOutlined, LeftOutlined } from '@ant-design/icons-vue'
import QuestionForm from './InterviewQuestionForm/index.vue'
import InterviewQuestionProofreadForm from '@/pages/interview-question-manage/InterviewQuestionProofread.vue'
import { getQuestionContentByRecord, getStarByScore } from '@/utils'
import emitter from '@/utils/bus';
import { delaiinterviewquestion, previewvideo, queryaiinterviewquestion } from '@/api/interview'
import { InterviewQuestionDetail, InterviewQuestionEnum } from '@/types/interviewQuestion'
import PagePreview from '@/components/PagePreview.vue'

const props = withDefaults(
  defineProps<{
    subjectId?: string
    /** 是否是官方题库 */
    isOfficialSubject?: boolean
    defaultType?: number | null
    defaultSelectedRows?: any[]
    defaultExcludedRowKeys?: string[]
    disabledRowKeys?: string[]
    writable?: boolean
    current?: number
  }>(),
  {
    subjectId: '',
    isOfficialSubject: false,
    defaultType: null,
    defaultSelectedRows: () => [],
    defaultExcludedRowKeys: () => [],
    disabledRowKeys: () => [],
    writable: false,
    current: 1
  }
)

const emits = defineEmits<{
  (e: 'select', record: any, selected: boolean): void
}>()

const store = useStore()

// 可选/已选/已排除
const listTypeEnum = ['可选', '已选', '已排除']
const listType = ref('可选')

// 是否包含子题库
const includeSubCatg = ref(true)

// 快速搜索
const searchContent = ref('')
function getFirstPageList() {
  otherParams.value.current_catg = !includeSubCatg.value
  paginationConfig.current = 1
  getList()
}

// 其他参数
const otherParams = ref<{
  types?: number[] | null
  proofreading?: (0 | 1 | 2)[]
  order_field?: string
  order_type?: 'ascend' | 'descend'
  /** 是否仅显示当前题库的试题（不包含子题库） */
  current_catg: boolean
  create_by?: number[] | null
}>({
  types: typeof props.defaultType === 'number' ? [props.defaultType] : null,
  current_catg: false
})

function handleTableChange(pagination: any, filters: any = {}, sort: any = {}) {
  // 处理排序
  otherParams.value.order_type = sort.order
  otherParams.value.order_field = sort.order ? sort.field : undefined
  // 处理分页
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getList()
}

// 表格配置
let proofreadingColumn = {
    title: '校对状态',
    dataIndex: 'proofreading',
    key: 'proofreading',
    width: 120,
    ellipsis: true,
    resizable: true,
    filters: [
        { text: '校对正确', value: 1 },
        { text: '校对错误', value: 2 },
        { text: '未校对', value: 0 },
    ]
}
let actionColumn = {
  title: '操作',
  key: 'action',
  width: 260,
  align: 'center',
  fixed: 'right',
}
const tableColumns = ref([
    {
        title: '题型',
        dataIndex: 'type',
        key: 'types',
        width: 100,
        ellipsis: false,
        filters: [
            { text: '单选题', value: 0 },
            { text: '多选题', value: 1 },
            { text: '判断题', value: 2 },
            { text: '填空题', value: 5 },
            { text: '问答题', value: 3 },
            { text: '算法题', value: 4 },
            { text: '排序题', value: 6 }
        ],
        resizable: true,
    },
    {
        title: '题干',
        dataIndex: 'body',
        key: 'body',
        ellipsis: true,
        resizable: true,
        width: 500,
    },
    proofreadingColumn,
    {
        title: '题库',
        dataIndex: 'category_name',
        key: 'category_name',
        width: 160,
        ellipsis: true,
        resizable: true
    },
    {
        title: '难度',
        dataIndex: 'difficulty',
        key: 'difficulty',
        width: 180,
        ellipsis: true,
        resizable: true,
        sorter: true,
        filters: [
            { text: '一星/入门', value: 1 },
            { text: '二星/基础', value: 2 },
            { text: '三星/中阶', value: 3 },
            { text: '四星/高阶', value: 4 },
            { text: '五星/专家', value: 5 },
        ],
    },
    {
        title: '更新时间',
        dataIndex: 'update_at',
        key: 'update_at',
        width: 180,
        ellipsis: true,
        resizable: true,
        sorter: true
    },
    {
        title: '创建人',
        dataIndex: 'author',
        key: 'create_by',
        width: 100,
        ellipsis: true,
        resizable: true,
        filters: [
            { text: '本人', value: 1 },
            { text: '其他', value: 0 },
        ],
    },
    actionColumn
])
// 如果传入默认题型，则不展示题型过滤下拉框
if (props.defaultType !== null) Reflect.deleteProperty(tableColumns.value[0], 'filters')
// 根据是否官方题库和是否可写控制操作栏显隐
watch([() => props.isOfficialSubject, () => props.writable], ([isOfficialSubject, writable]) => {
  const is_official_cert = store.getters.userInfo.is_official_cert
  const hideAction = !is_official_cert && isOfficialSubject
  let index = tableColumns.value.findIndex(item => item.key === 'action')
  if (writable && !hideAction) {
    if(index === -1) tableColumns.value.push(actionColumn)
  } else {
    if(index !== -1) tableColumns.value.splice(index, 1)
  }
  // 官方题库题目不展示校对结果列
  let proofreadIndex = tableColumns.value.findIndex(item => item.key === 'proofreading')
  if (!isOfficialSubject && !is_official_cert || isOfficialSubject && is_official_cert) { // 同或运算
    if (proofreadIndex === -1) {
      let authorIndex = tableColumns.value.findIndex(item => item.title === '题干')
      tableColumns.value.splice(authorIndex + 1, 0, proofreadingColumn) // 添加到在题干后面
    }
  } else {
    if (proofreadIndex !== -1) tableColumns.value.splice(proofreadIndex, 1)
  }
}, { immediate: true })

const paginationConfig = reactive({
  current: props.current,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total: number) => '总条数：' + total,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

// 获取已排除列表长度
const excludedListLength = ref(0)
async function getExcludedListLength() {
  excludedListLength.value = props.defaultExcludedRowKeys.length
  if (props.defaultExcludedRowKeys.length) {
    let params: any = {
      action: 'query',
      page: paginationConfig.current,
      per_page: paginationConfig.pageSize,
      body: searchContent.value,
      category: props.subjectId || null,
      ids: props.defaultExcludedRowKeys,
      ...otherParams.value
    }
    let res = (await queryaiinterviewquestion(params)) as any
    excludedListLength.value = res.total
  }
}
getExcludedListLength()

// 表格数据
const list = ref<any[]>([])
const loading = ref(false)
async function getList() {
  if (!props.subjectId) return
  loading.value = true
  let params: any = {
    action: 'query',
    page: paginationConfig.current,
    per_page: paginationConfig.pageSize,
    body: searchContent.value,
    category: props.subjectId || null,
    notids: props.defaultExcludedRowKeys,
    ...otherParams.value
  }
  if (['可选', '已选'].includes(listType.value)) {
    // 如果是可选或者已选，需要将虽已排除但是已选的也展示出来
    params.notids = params.notids.filter((id: string) => !selectedRowKeys.value.includes(id))
  }
  if (listType.value === '已选') {
    params.ids = selectedRowKeys.value
  } else if (listType.value === '已排除') {
    params.ids = props.defaultExcludedRowKeys
    params.notids = []
  }
  try {
    let res = (await queryaiinterviewquestion(params)) as any
    list.value = res.data
    paginationConfig.total = res.total
  } finally {
    loading.value = false
  }
}

// 已选列表
const selectedRows = ref<any[]>([...props.defaultSelectedRows])
const selectedRowKeys = computed(() => selectedRows.value.map((i) => i.id))

function updateSelectedRowKeys(record: any) {
  let key = record.id
  let index = selectedRowKeys.value.findIndex((i) => i === key)
  if (index === -1) {
    selectedRows.value.push(record)
    emits('select', record, true)
  } else {
    selectedRows.value.splice(index, 1)
    emits('select', record, false)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any[]) =>
  changeRows.forEach(updateSelectedRowKeys)

const getType = (type: number) => InterviewQuestionEnum[type]

// 新建/编辑题目
const qid = ref('')
const formVisible = ref(false)
function showForm(id: string) {
  qid.value = id
  formVisible.value = true
}
function onFormClose(needRefresh?: boolean) {
  if (needRefresh) {
    getList()
  }
  formVisible.value = false
}

// 查询题目ids关联的（未开始考试）试卷
function getPapersWithQids(ids: string[]) {
  let h = createVNode
  return new Promise((resolve, reject) => {
    beforedelques({ ids }).then((res: any) => {
      if (res.length) {
        Modal.confirm({
          title: '删除题目风险提醒',
          content: h('div', {}, [
            h('p', {}, '删除当前题目后，会影响以下考试：'),
            ...res.map((item: any) => h('p', { style: 'margin-top: 6px' }, `《${item.name}》——${item.create_by}`)),
            h('p', { style: 'margin-top: 6px' }, '确认删除吗？')
          ]),
          icon: () => h(InfoCircleOutlined),
          onOk() {
            resolve(res)
          },
          onCancel() {
            reject()
          }
        })
      } else {
        resolve(res)
      }
    })
  })
}

// 删除题目
async function deleteQuestion(record) {
  try {
    await getPapersWithQids([record.id!])
    await delaiinterviewquestion({ id: [record.id] })
    message.success('删除成功!')
    getList()
  } catch (error) {
    
  }
}

// 批量删除题目
const handleBatchDelete = () => {
  if (!selectedRows.value.length) return message.error('请勾选要删除的题目')
  Modal.confirm({
    title: () => `确定删除勾选的${selectedRows.value.length}个题目?`,
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await getPapersWithQids(selectedRowKeys.value)
        await delaiinterviewquestion({
          id: selectedRowKeys.value
        })
        message.success('批量删除成功!')
        selectedRows.value = []
        getList()
      } catch (error) {
        
      }
    },
    onCancel() {}
  })
}

// 题目视频预览
const currentItem = ref<InterviewQuestionDetail>()
const questionVideoVisible = ref(false)
const videoUrl = ref('')
async function previewVideo(record: InterviewQuestionDetail) {
  currentItem.value = record
  let res = await previewvideo({ question_id: record.id })
  videoUrl.value = res
  questionVideoVisible.value = true
}

// 题目校对
const questionProofreadFormVisible = ref(false)
function proofreadQuestion(record: any) {
  qid.value = record.id
  questionProofreadFormVisible.value = true
}

// 监听切换题库
const debounceTimer = ref<any>(null)
watch(
  () => props.subjectId,
  () => {
    paginationConfig.current = 1
    if (debounceTimer.value) clearTimeout(debounceTimer.value)
    debounceTimer.value = setTimeout(async () => {
      getList()
    }, 200)
  }
)

// 获取上一题/下一题并高亮展示
const currentQIndex = computed(() => list.value.findIndex(item => item.id === qid.value))
const lastBtnDisabled = computed(() => (paginationConfig.current === 1) && (currentQIndex.value === 0))
const nextBtnDisabled = computed(() => (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) && (currentQIndex.value === list.value.length - 1))
async function getSiblingQuestion(direction: 'last' | 'next') {
  if (!qid.value) return
  let len = list.value.length
  let index = currentQIndex.value
  let newQid = qid.value,
      newIndex = index
  if (direction === 'last') {
    if (index !== 0) {
      newIndex = index - 1
    } else {
      // 获取上一页最后一条
      if (paginationConfig.current === 1) {
        throw new Error('没有上一题了')
      } else {
        paginationConfig.current -= 1
        await getList()
        newIndex = list.value.length - 1
      }
    }
  } else {
    if (index !== len - 1) {
      newIndex = index + 1
    } else {
      // 获取下一页第一条
      if (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) {
        throw Error('没有下一题了')
      } else {
        paginationConfig.current += 1
        await getList()
        newIndex = 0
      }
    }
  }
  try {
    newQid = list.value[newIndex].id
  } catch (error) {
    throw Error('题目列表为空')
  }
  nextTick(() => highLightRowByIndex(newIndex))
  return newQid
}

// 根据index将某一行高亮
function highLightRowByIndex(index: number) {
  let rows = document.querySelectorAll('.questions-table .ant-table-tbody tr.ant-table-row')
  rows.forEach((ele, i) => {
    if (i === index) {
      ele.classList.add('active')
    } else {
      ele.classList.remove('active')
    }
  })
}

getList()
emitter.on('updateInterviewQuestionList', getList) // 其他菜单（回收站、草稿箱、ai批量出题）可能会对当前题库的题目进行更新

onDeactivated(() => {
  formVisible.value = false
  questionVideoVisible.value = false
  questionProofreadFormVisible.value = false
})

defineExpose({
  selectedRows,
})
</script>

<style lang="less">

.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}

.ques-drawer {
  .ant-drawer-title {
    font-weight: bold;
  }
}

.proofread-modal {
  .ant-modal-header {
    padding: 32px;
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-modal-body {
    padding: 0;
  }
}
</style>
<style lang="less" scoped>
.question-manage {
  height: 100%;
  overflow: auto;
  padding: 0 10px 0 16px;
  display: flex;
  flex-direction: column;
}
.question-manage-header {
  margin: 0px 0;
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;

    :deep(.ant-checkbox-wrapper) {
      margin-left: 16px;
      span {
        font-size: 14px;
        white-space: nowrap;
      }
    }

    .ant-radio-group {
      margin-left: 24px;
      display: flex;
      align-items: center;

      :deep(span) {
        font-size: 14px;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    .ant-btn {
      border-radius: 8px;
      margin-left: 8px;
      font-size: 14px;
      line-height: 24px;
    }
  }
}

.recover-tag {
  font-size: 12px !important;
  color: #2f8c00 !important;
  background-color: #f3f7f1;
  border-radius: 4px;
  padding: 2px 4px;
}

.exclude-tag {
  font-size: 12px;
  color: #d71310 !important;
  background-color: #fcf0f0;
  border-radius: 4px;
  padding: 2px 4px;
}

.table-bar {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-checkbox-wrapper {
    font-size: 15px;
  }

  .show-columns {
    font-size: 15px;

    span {
      margin-right: 10px;
    }
  }
}

:deep(.ant-table) {
  .ant-table-expanded-row td:nth-child(1) {
    box-shadow: 2px 0px 0px 0px #5478ee inset;
  }
}

.expanded-row {
  padding: 16px;
}
.tooltip-style {
  display: block;
  // width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left !important;
  margin-right: 8px;
}

:deep(.question-content) {
  img {
    height: 50px;
    width: auto;
    cursor: initial!important;
    display: inline-block!important;
    float: none!important;
  }
  p {
    display: inline-block;
  }
}
</style>
