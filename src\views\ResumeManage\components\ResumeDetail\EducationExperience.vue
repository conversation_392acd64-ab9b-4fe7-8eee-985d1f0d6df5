<script setup lang="ts">
import type { EducationItemProps } from './shared'
import BaseExperienceCard from './BaseExperienceCard.vue'
// 模拟工作经历数据
defineProps<{
  educationExperienceData: EducationItemProps
}>()
</script>

<template>
  <BaseExperienceCard v-show="educationExperienceData?.length > 0" title="教育经历">
    <div v-for="(item, index) in educationExperienceData" :key="index" class="mb-6 last:mb-0">
      <div class="flex items-start mb-2 ">
        <div class="flex-1">
          <div class="flex justify-start">
            <img :src=" item.emblemUrl " alt="头像" class="w-12 h-12 mr-6 flex-shrink-0 rounded-full object-cover">
            <div class="flex-1 flex flex-col justify-start items-center gap-2">
              <div class="w-full flex justify-start items-end gap-4">
                <span class="font-medium text-base">{{ item.school }}</span>
                <div class="text-gray-500">
                  {{ item.startDate }} 至 {{ item.endDate }}
                </div>
              </div>

              <div class="w-full flex justify-start items-center">
                <span class="text-gray-600 text-xs">{{ item.major }}</span>
                <span class="text-gray-500 mx-2">|</span>
                <span class="text-gray-600 text-xs">{{ item.degree }}</span>
                <!-- <span class="text-gray-500 mx-2">|</span>
                <span class="text-gray-600 text-xs">{{ item.description }}</span> -->
              </div>
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600 whitespace-pre-line leading-[18px]">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </BaseExperienceCard>
</template>
