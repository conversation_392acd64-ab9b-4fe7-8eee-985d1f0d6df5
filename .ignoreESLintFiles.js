export default [
  'ImageResourceReport.py',
  'README.md',
  'recorder.js',
  'src/.env.dev',
  'src/.env.prod',
  'src/App.vue',
  'src/Icon/ICON.ts',
  'src/api/admin/abilityTest.ts',
  'src/api/admin/accountManage.ts',
  'src/api/admin/invitecodeManage.ts',
  'src/api/admin/newsManage.ts',
  'src/api/admin/paperManage.ts',
  'src/api/admin/questionManage.ts',
  'src/api/admin/recycleManage.ts',
  'src/api/admin/resumeManage.ts',
  'src/api/admin/statisticAnalysis.ts',
  'src/api/admin/survey.ts',
  'src/api/admin/systemManage.ts',
  'src/api/admin/tagsManage.ts',
  'src/api/exam/index.ts',
  'src/api/interview.ts',
  'src/api/login.ts',
  'src/api/portal.ts',
  'src/api/test.ts',
  'src/api/user.ts',
  'src/assets/animation/ai_loading.json',
  'src/assets/animation/generating.json',
  'src/assets/animation/proofread_no.json',
  'src/assets/animation/proofread_ok.json',
  'src/assets/gif/1.gif',
  'src/assets/gif/10.gif',
  'src/assets/gif/2.gif',
  'src/assets/gif/3.gif',
  'src/assets/gif/4.gif',
  'src/assets/gif/5.gif',
  'src/assets/gif/6.gif',
  'src/assets/gif/7.gif',
  'src/assets/gif/8.gif',
  'src/assets/gif/9.gif',
  'src/assets/icons/svg/ability-test-m.svg',
  'src/assets/icons/svg/absent_status.svg',
  'src/assets/icons/svg/account.svg',
  'src/assets/icons/svg/active_star.svg',
  'src/assets/icons/svg/addItem.svg',
  'src/assets/icons/svg/ai.svg',
  'src/assets/icons/svg/angle-down2.svg',
  'src/assets/icons/svg/arrow-down.svg',
  'src/assets/icons/svg/attachment.svg',
  'src/assets/icons/svg/auth.svg',
  'src/assets/icons/svg/avatar_default.svg',
  'src/assets/icons/svg/back.svg',
  'src/assets/icons/svg/bad.svg',
  'src/assets/icons/svg/barn.svg',
  'src/assets/icons/svg/bid-card.svg',
  'src/assets/icons/svg/blur.svg',
  'src/assets/icons/svg/calculator.svg',
  'src/assets/icons/svg/calendar.svg',
  'src/assets/icons/svg/calendar2.svg',
  'src/assets/icons/svg/camera.svg',
  'src/assets/icons/svg/circle-del.svg',
  'src/assets/icons/svg/circle.svg',
  'src/assets/icons/svg/clear.svg',
  'src/assets/icons/svg/clock.svg',
  'src/assets/icons/svg/clock2.svg',
  'src/assets/icons/svg/close.svg',
  'src/assets/icons/svg/codedel.svg',
  'src/assets/icons/svg/collapse-menu.svg',
  'src/assets/icons/svg/compoundDown.svg',
  'src/assets/icons/svg/compoundUp.svg',
  'src/assets/icons/svg/computer-active.svg',
  'src/assets/icons/svg/computer.svg',
  'src/assets/icons/svg/computercan.svg',
  'src/assets/icons/svg/comscreen.svg',
  'src/assets/icons/svg/contact.svg',
  'src/assets/icons/svg/copy.svg',
  'src/assets/icons/svg/correct-face.svg',
  'src/assets/icons/svg/correct-finished.svg',
  'src/assets/icons/svg/correnttip.svg',
  'src/assets/icons/svg/count.svg',
  'src/assets/icons/svg/dangertip.svg',
  'src/assets/icons/svg/deactive_star.svg',
  'src/assets/icons/svg/del.svg',
  'src/assets/icons/svg/delete-close.svg',
  'src/assets/icons/svg/delete-open.svg',
  'src/assets/icons/svg/delete.svg',
  'src/assets/icons/svg/deliever.svg',
  'src/assets/icons/svg/department-m.svg',
  'src/assets/icons/svg/disabled_delte.svg',
  'src/assets/icons/svg/disabled_edit.svg',
  'src/assets/icons/svg/done.svg',
  'src/assets/icons/svg/down.svg',
  'src/assets/icons/svg/draft.svg',
  'src/assets/icons/svg/drag.svg',
  'src/assets/icons/svg/draw-paper-active.svg',
  'src/assets/icons/svg/draw-paper.svg',
  'src/assets/icons/svg/duration.svg',
  'src/assets/icons/svg/edit.svg',
  'src/assets/icons/svg/editable.svg',
  'src/assets/icons/svg/empty-circle.svg',
  'src/assets/icons/svg/empty.svg',
  'src/assets/icons/svg/emptylist.svg',
  'src/assets/icons/svg/end-comp-source.svg',
  'src/assets/icons/svg/end-comp.svg',
  'src/assets/icons/svg/end-m.svg',
  'src/assets/icons/svg/err-comp-source.svg',
  'src/assets/icons/svg/err-comp.svg',
  'src/assets/icons/svg/err-m.svg',
  'src/assets/icons/svg/err_status.svg',
  'src/assets/icons/svg/error-fill.svg',
  'src/assets/icons/svg/error.svg',
  'src/assets/icons/svg/errortip.svg',
  'src/assets/icons/svg/exam-anlysis.svg',
  'src/assets/icons/svg/exam-m.svg',
  'src/assets/icons/svg/exam.svg',
  'src/assets/icons/svg/expand-menu.svg',
  'src/assets/icons/svg/expand_img.svg',
  'src/assets/icons/svg/eye.svg',
  'src/assets/icons/svg/face-check.svg',
  'src/assets/icons/svg/failsubmit.svg',
  'src/assets/icons/svg/fid-card.svg',
  'src/assets/icons/svg/file-official.svg',
  'src/assets/icons/svg/file.svg',
  'src/assets/icons/svg/file0.svg',
  'src/assets/icons/svg/filter.svg',
  'src/assets/icons/svg/filter2.svg',
  'src/assets/icons/svg/finish_status.svg',
  'src/assets/icons/svg/finished-paper.svg',
  'src/assets/icons/svg/folder-official.svg',
  'src/assets/icons/svg/folder.svg',
  'src/assets/icons/svg/folder0.svg',
  'src/assets/icons/svg/font.svg',
  'src/assets/icons/svg/forward.svg',
  'src/assets/icons/svg/fullscreen.svg',
  'src/assets/icons/svg/fullsreen.svg',
  'src/assets/icons/svg/good.svg',
  'src/assets/icons/svg/gray-flag.svg',
  'src/assets/icons/svg/gray-tip.svg',
  'src/assets/icons/svg/icon-user.svg',
  'src/assets/icons/svg/icon-website.svg',
  'src/assets/icons/svg/icon_ai_m.svg',
  'src/assets/icons/svg/icon_end.svg',
  'src/assets/icons/svg/icon_err.svg',
  'src/assets/icons/svg/icon_ing.svg',
  'src/assets/icons/svg/icon_monitor_pc.svg',
  'src/assets/icons/svg/icon_monitor_phone.svg',
  'src/assets/icons/svg/icon_offline.svg',
  'src/assets/icons/svg/icon_onemore.svg',
  'src/assets/icons/svg/icon_right_arrow.svg',
  'src/assets/icons/svg/icon_tixing_danxuan.svg',
  'src/assets/icons/svg/icon_tixing_duoxuan.svg',
  'src/assets/icons/svg/icon_tixing_panduan.svg',
  'src/assets/icons/svg/icon_tixing_tiankong.svg',
  'src/assets/icons/svg/icon_tixing_wenda.svg',
  'src/assets/icons/svg/icon_wait.svg',
  'src/assets/icons/svg/incomplete.svg',
  'src/assets/icons/svg/info.svg',
  'src/assets/icons/svg/info2.svg',
  'src/assets/icons/svg/ing-comp-source.svg',
  'src/assets/icons/svg/ing-comp.svg',
  'src/assets/icons/svg/ing-m.svg',
  'src/assets/icons/svg/ing_status.svg',
  'src/assets/icons/svg/instruction.svg',
  'src/assets/icons/svg/interview_ques.svg',
  'src/assets/icons/svg/invisible.svg',
  'src/assets/icons/svg/invitation.svg',
  'src/assets/icons/svg/invitecode.svg',
  'src/assets/icons/svg/link-active.svg',
  'src/assets/icons/svg/link.svg',
  'src/assets/icons/svg/loved.svg',
  'src/assets/icons/svg/m-correct.svg',
  'src/assets/icons/svg/m-monitor.svg',
  'src/assets/icons/svg/m-resource.svg',
  'src/assets/icons/svg/manual.svg',
  'src/assets/icons/svg/menu-control.svg',
  'src/assets/icons/svg/message.svg',
  'src/assets/icons/svg/moon.svg',
  'src/assets/icons/svg/multiple.svg',
  'src/assets/icons/svg/newLogo.svg',
  'src/assets/icons/svg/nextArrow.svg',
  'src/assets/icons/svg/no-signal.svg',
  'src/assets/icons/svg/normal.svg',
  'src/assets/icons/svg/notice.svg',
  'src/assets/icons/svg/num_icon.svg',
  'src/assets/icons/svg/ol-paper-active.svg',
  'src/assets/icons/svg/ol-paper.svg',
  'src/assets/icons/svg/outline_status.svg',
  'src/assets/icons/svg/over.svg',
  'src/assets/icons/svg/paper-card.svg',
  'src/assets/icons/svg/paper-gray.svg',
  'src/assets/icons/svg/paper-list.svg',
  'src/assets/icons/svg/paper-m.svg',
  'src/assets/icons/svg/paper-white.svg',
  'src/assets/icons/svg/pdf.svg',
  'src/assets/icons/svg/person.svg',
  'src/assets/icons/svg/phone-active.svg',
  'src/assets/icons/svg/phone.svg',
  'src/assets/icons/svg/phone2.svg',
  'src/assets/icons/svg/phonecan.svg',
  'src/assets/icons/svg/pin.svg',
  'src/assets/icons/svg/play.svg',
  'src/assets/icons/svg/plus.svg',
  'src/assets/icons/svg/plus2.svg',
  'src/assets/icons/svg/plus3.svg',
  'src/assets/icons/svg/polygon.svg',
  'src/assets/icons/svg/prevArrow.svg',
  'src/assets/icons/svg/preview.svg',
  'src/assets/icons/svg/progressing.svg',
  'src/assets/icons/svg/pwd.svg',
  'src/assets/icons/svg/qingzhu.svg',
  'src/assets/icons/svg/ques-anlysis.svg',
  'src/assets/icons/svg/ques-i.svg',
  'src/assets/icons/svg/ques-m.svg',
  'src/assets/icons/svg/question.svg',
  'src/assets/icons/svg/questions-gray.svg',
  'src/assets/icons/svg/questions-white.svg',
  'src/assets/icons/svg/recycle.svg',
  'src/assets/icons/svg/red-flag.svg',
  'src/assets/icons/svg/red-hat.svg',
  'src/assets/icons/svg/refresh.svg',
  'src/assets/icons/svg/reload.svg',
  'src/assets/icons/svg/reminder.svg',
  'src/assets/icons/svg/resourcegroup-m.svg',
  'src/assets/icons/svg/resume-gray.svg',
  'src/assets/icons/svg/resume-m.svg',
  'src/assets/icons/svg/resume-white.svg',
  'src/assets/icons/svg/resume.svg',
  'src/assets/icons/svg/sandglass.svg',
  'src/assets/icons/svg/scoredown.svg',
  'src/assets/icons/svg/scoreup.svg',
  'src/assets/icons/svg/search.svg',
  'src/assets/icons/svg/shared.svg',
  'src/assets/icons/svg/shelter.svg',
  'src/assets/icons/svg/signal.svg',
  'src/assets/icons/svg/smscode.svg',
  'src/assets/icons/svg/start.svg',
  'src/assets/icons/svg/static-m.svg',
  'src/assets/icons/svg/sticky.svg',
  'src/assets/icons/svg/stu-anlysis.svg',
  'src/assets/icons/svg/stu-m.svg',
  'src/assets/icons/svg/substract.svg',
  'src/assets/icons/svg/success-fill.svg',
  'src/assets/icons/svg/successful.svg',
  'src/assets/icons/svg/successfulsubmit.svg',
  'src/assets/icons/svg/sun.svg',
  'src/assets/icons/svg/survey-m.svg',
  'src/assets/icons/svg/switch.svg',
  'src/assets/icons/svg/table_icon.svg',
  'src/assets/icons/svg/tag.svg',
  'src/assets/icons/svg/teacher-m.svg',
  'src/assets/icons/svg/test-gray.svg',
  'src/assets/icons/svg/test-white.svg',
  'src/assets/icons/svg/test.svg',
  'src/assets/icons/svg/testKnown.svg',
  'src/assets/icons/svg/testVideo.svg',
  'src/assets/icons/svg/testlist.svg',
  'src/assets/icons/svg/thumbnail.svg',
  'src/assets/icons/svg/timedown.svg',
  'src/assets/icons/svg/timeup.svg',
  'src/assets/icons/svg/tip.svg',
  'src/assets/icons/svg/toearly.svg',
  'src/assets/icons/svg/totalQueNum.svg',
  'src/assets/icons/svg/ul-paper-active.svg',
  'src/assets/icons/svg/ul-paper.svg',
  'src/assets/icons/svg/unfold.svg',
  'src/assets/icons/svg/unilogo.svg',
  'src/assets/icons/svg/up.svg',
  'src/assets/icons/svg/uparrow.svg',
  'src/assets/icons/svg/upload.svg',
  'src/assets/icons/svg/uploadimg.svg',
  'src/assets/icons/svg/user.svg',
  'src/assets/icons/svg/userinfo.svg',
  'src/assets/icons/svg/video.svg',
  'src/assets/icons/svg/view.svg',
  'src/assets/icons/svg/visible.svg',
  'src/assets/icons/svg/void.svg',
  'src/assets/icons/svg/wait-comp-source.svg',
  'src/assets/icons/svg/wait-comp.svg',
  'src/assets/icons/svg/wait-m.svg',
  'src/assets/icons/svg/wait.svg',
  'src/assets/icons/svg/wait_status.svg',
  'src/assets/icons/svg/warn.svg',
  'src/assets/icons/svg/warning.svg',
  'src/assets/icons/svg/warntip.svg',
  'src/assets/icons/svg/wechat-login.svg',
  'src/assets/icons/svg/zuobi-comp.svg',
  'src/assets/icons/svg/zuobi-m.svg',
  'src/assets/icons/svg/分组.svg',
  'src/assets/icons/svg/账号.svg',
  'src/assets/images/1_close.png',
  'src/assets/images/1_open.png',
  'src/assets/images/2_close.png',
  'src/assets/images/2_open.png',
  'src/assets/images/account.png',
  'src/assets/images/add.png',
  'src/assets/images/admin/admin-logo.svg',
  'src/assets/images/admin/angle-down.svg',
  'src/assets/images/admin/dept.png',
  'src/assets/images/admin/edit-blue.svg',
  'src/assets/images/admin/edit-gray.svg',
  'src/assets/images/admin/file.png',
  'src/assets/images/admin/folder.png',
  'src/assets/images/admin/icon-del-blue.svg',
  'src/assets/images/admin/icon-del-gray.svg',
  'src/assets/images/admin/icon-link-blue.svg',
  'src/assets/images/admin/icon-link-gray.svg',
  'src/assets/images/admin/icon-more.svg',
  'src/assets/images/admin/icon-preview-blue.svg',
  'src/assets/images/admin/icon-preview-gray.svg',
  'src/assets/images/admin/icon-user.svg',
  'src/assets/images/admin/idcard_example.png',
  'src/assets/images/admin/pdept.png',
  'src/assets/images/ai-aide.png',
  'src/assets/images/aside-bg.webp',
  'src/assets/images/back_to_top.png',
  'src/assets/images/bg-login.jpg',
  'src/assets/images/bg-login2.jpg',
  'src/assets/images/blank.png',
  'src/assets/images/blue-close.png',
  'src/assets/images/blue-del.png',
  'src/assets/images/blue-edit.png',
  'src/assets/images/blue-eye.png',
  'src/assets/images/blue-open.png',
  'src/assets/images/blue-save.png',
  'src/assets/images/default-avatar.png',
  'src/assets/images/delete.png',
  'src/assets/images/exam/delete.png',
  'src/assets/images/exam/error.png',
  'src/assets/images/exam/exam-bg.png',
  'src/assets/images/exam/fail-paper.png',
  'src/assets/images/exam/icon_arrowleft.svg',
  'src/assets/images/exam/icon_arrowright.svg',
  'src/assets/images/exam/icon_calendar.png',
  'src/assets/images/exam/icon_duration.png',
  'src/assets/images/exam/icon_list.png',
  'src/assets/images/exam/icon_notice.png',
  'src/assets/images/exam/icon_reminder.png',
  'src/assets/images/exam/icon_score.png',
  'src/assets/images/exam/icon_start.png',
  'src/assets/images/exam/icon_time.png',
  'src/assets/images/exam/login-logo2.png',
  'src/assets/images/exam/page-logo.png',
  'src/assets/images/exam/part.png',
  'src/assets/images/exam/success-paper.png',
  'src/assets/images/exam/uploadimg.svg',
  'src/assets/images/exam/wait-exam.png',
  'src/assets/images/face-frame.png',
  'src/assets/images/faceVerification.png',
  'src/assets/images/failcommit.png',
  'src/assets/images/fold_arrow.png',
  'src/assets/images/interview-helper-avatar.png',
  'src/assets/images/interview-helper.png',
  'src/assets/images/jubao.png',
  'src/assets/images/locked.png',
  'src/assets/images/login-logo.png',
  'src/assets/images/logo.png',
  'src/assets/images/logo.svg',
  'src/assets/images/logo2.png',
  'src/assets/images/loseEfficacy.png',
  'src/assets/images/news_1.png',
  'src/assets/images/news_2.jpg',
  'src/assets/images/news_3.jpg',
  'src/assets/images/no_monitor.png',
  'src/assets/images/nodata.png',
  'src/assets/images/nodata2.png',
  'src/assets/images/notUploadIDcard.png',
  'src/assets/images/paper/add.png',
  'src/assets/images/paper/arrow-right-disabled.svg',
  'src/assets/images/paper/arrow-right.svg',
  'src/assets/images/paper/back.svg',
  'src/assets/images/paper/clear.png',
  'src/assets/images/paper/close.svg',
  'src/assets/images/paper/copy.png',
  'src/assets/images/paper/copy.svg',
  'src/assets/images/paper/delete-disabled.svg',
  'src/assets/images/paper/delete.png',
  'src/assets/images/paper/delete.svg',
  'src/assets/images/paper/down.png',
  'src/assets/images/paper/edit-disabled.svg',
  'src/assets/images/paper/edit.png',
  'src/assets/images/paper/edit.svg',
  'src/assets/images/paper/filter.png',
  'src/assets/images/paper/manual-active.png',
  'src/assets/images/paper/manual-end.png',
  'src/assets/images/paper/manual-start.png',
  'src/assets/images/paper/mock.png',
  'src/assets/images/paper/mock.svg',
  'src/assets/images/paper/origin.svg',
  'src/assets/images/paper/staff.svg',
  'src/assets/images/paper/time.png',
  'src/assets/images/paper1.jpeg',
  'src/assets/images/paper2.jpg',
  'src/assets/images/paper3.png',
  'src/assets/images/pc-frame.png',
  'src/assets/images/pricing.png',
  'src/assets/images/send.png',
  'src/assets/images/send1.png',
  'src/assets/images/shiyedanwei.png',
  'src/assets/images/successcommit.png',
  'src/assets/images/survey-complete.png',
  'src/assets/images/survey-stop.png',
  'src/assets/images/svg/camera.svg',
  'src/assets/images/svg/delete_black.svg',
  'src/assets/images/svg/female.svg',
  'src/assets/images/svg/icon_delete.svg',
  'src/assets/images/svg/icon_duration.svg',
  'src/assets/images/svg/icon_end_time.svg',
  'src/assets/images/svg/icon_eyes.svg',
  'src/assets/images/svg/icon_pwd.svg',
  'src/assets/images/svg/icon_right_arrow.svg',
  'src/assets/images/svg/icon_start_time.svg',
  'src/assets/images/svg/icon_upload_avatar.svg',
  'src/assets/images/svg/icon_user.svg',
  'src/assets/images/svg/male.svg',
  'src/assets/images/svg/no-grade.svg',
  'src/assets/images/svg/no-test.svg',
  'src/assets/images/svg/no_monitor.svg',
  'src/assets/images/svg/proofread.svg',
  'src/assets/images/svg/proofread_no.svg',
  'src/assets/images/svg/proofread_ok.svg',
  'src/assets/images/svg/proofread_ok2.svg',
  'src/assets/images/svg/welcome-1.svg',
  'src/assets/images/svg/welcome-10.svg',
  'src/assets/images/svg/welcome-2.svg',
  'src/assets/images/svg/welcome-3.svg',
  'src/assets/images/svg/welcome-4.svg',
  'src/assets/images/svg/welcome-5.svg',
  'src/assets/images/svg/welcome-6.svg',
  'src/assets/images/svg/welcome-7.svg',
  'src/assets/images/svg/welcome-8.svg',
  'src/assets/images/svg/welcome-9.svg',
  'src/assets/images/the-end.png',
  'src/assets/images/time.png',
  'src/assets/images/toearly.png',
  'src/assets/images/triangle.png',
  'src/assets/images/turing/turing-Alans.jpg',
  'src/assets/images/turing/turing-univeral.jpg',
  'src/assets/images/turing/turing_code.jpg',
  'src/assets/images/unfold_arrow.png',
  'src/assets/images/wx.png',
  'src/assets/loading.json',
  'src/assets/validate.json',
  'src/assets/welcome.png',
  'src/components/AiAnswer.vue',
  'src/components/AiBot.vue',
  'src/components/Banner.vue',
  'src/components/Calculator.vue',
  'src/components/Calculator2.vue',
  'src/components/CodeBox.vue',
  'src/components/CodeEditor.vue',
  'src/components/CustomDialog.vue',
  'src/components/CustomDrawer.vue',
  'src/components/Editor/extend.ts',
  'src/components/Editor/index.vue',
  'src/components/FoldText.vue',
  'src/components/HelloWorld.vue',
  'src/components/Identify.vue',
  'src/components/JSwitch.vue',
  'src/components/JSwitchPlus.vue',

  'src/components/ListWrapper.vue',
  'src/components/Loading/Loading.vue',
  'src/components/Loading/createLoading.ts',
  'src/components/Loading/index.ts',
  'src/components/Loading/typing.ts',
  'src/components/Loading/useLoading.ts',
  'src/components/PagePreview.vue',
  'src/components/PopoverBox.vue',
  'src/components/RadioBtn/index.vue',
  'src/components/SearchCardWrapper.vue',
  'src/components/SearchHighLight.vue',
  'src/components/SpliteBox/index.vue',
  'src/components/SvgIcon.vue',
  'src/components/Switch.vue',
  'src/components/TagEllipsis.vue',
  'src/components/TimeCount.vue',

  'src/components/VueQuillEditor/modules/paste.js',
  'src/config/constants.ts',
  'src/config/imgPreloader.js',
  'src/config/imgPreloaderList.ts',
  'src/config/index.ts',
  'src/config/languageFunctions.ts',
  'src/config/tableColumns.ts',
  'src/config/theme.js',
  'src/env.d.ts',
  'src/hooks/index.ts',
  'src/hooks/useDelayIntervalFn.ts',
  'src/lang/en.ts',
  'src/lang/index.ts',
  'src/lang/zh-CN.ts',
  'src/layouts/Admin/components/AHeader.vue',
  'src/layouts/Admin/components/AMain.vue',
  'src/layouts/Admin/components/Aside.vue',
  'src/layouts/Admin/components/menuList.ts',
  'src/layouts/Admin/index.vue',
  'src/models/paperModel.ts',
  'src/models/questionModel.ts',
  'src/models/userInfoModel.ts',
  'src/pages/abilityTest/AbilityTestAllocate.vue',
  'src/pages/abilityTest/AbilityTestCard.vue',
  'src/pages/abilityTest/AbilityTestForm.vue',
  'src/pages/abilityTest/AbilityTestFormConfig.vue',
  'src/pages/abilityTest/AbilityTestFormSubject.vue',
  'src/pages/abilityTest/AbilityTestFormSubjectTree.vue',
  'src/pages/abilityTest/AbilityTestGradeTable.vue',
  'src/pages/abilityTest/AbilityTestManage.vue',
  'src/pages/analysisManage/CatalogAnalysis.vue',
  'src/pages/analysisManage/PaperAnalysis.vue',
  'src/pages/analysisManage/PaperDetailAnalysis.vue',
  'src/pages/analysisManage/StudentAnalysis.vue',
  'src/pages/analysisManage/StudentSingleAnalysis.vue',
  'src/pages/analysisManage/components/AgeChart.vue',
  'src/pages/analysisManage/components/CatalogFrequencyAndQuestionAmountChart.vue',
  'src/pages/analysisManage/components/CatalogQuestionAmountProportionChart.vue',
  'src/pages/analysisManage/components/CatalogQuestionTypeChart.vue',
  'src/pages/analysisManage/components/DegreeChart.vue',
  'src/pages/analysisManage/components/PaperAnalysis.vue',
  'src/pages/analysisManage/components/PaperTable.vue',
  'src/pages/analysisManage/components/PassGauge.vue',
  'src/pages/analysisManage/components/QuesDetail.vue',
  'src/pages/analysisManage/components/QuesTable.vue',
  'src/pages/analysisManage/components/ScoreBar.vue',
  'src/pages/analysisManage/components/SexChart.vue',
  'src/pages/analysisManage/components/StudentCapacityChart.vue',
  'src/pages/analysisManage/components/StudentTable.vue',
  'src/pages/interview-helper-manage/components/ListItemCard.vue',
  'src/pages/interview-helper-manage/index.vue',
  'src/pages/interview-helper-manage/interview-helper-form/FormSubjectTree.vue',
  'src/pages/interview-helper-manage/interview-helper-form/index.vue',
  'src/pages/interview-helper-runner/index.vue',
  'src/pages/interview-helper-runner/runner-aside.vue',
  'src/pages/interview-helper-runner/runner-head.vue',
  'src/pages/interview-question-manage/InterviewQuestionConvertor.vue',
  'src/pages/interview-question-manage/InterviewQuestionDraft.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/AlgorithmForm.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/CompositeForm.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/FillblankForm.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/QaFrom.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/SelectForm.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/SortForm.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/components/ComplexInput.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/components/OptionGenerator.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/components/QuestionEditor.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/components/UsefulSentence.vue',
  'src/pages/interview-question-manage/InterviewQuestionForm/index.vue',
  'src/pages/interview-question-manage/InterviewQuestionGenerator.vue',
  'src/pages/interview-question-manage/InterviewQuestionItemDisplay/OptionsBox.vue',
  'src/pages/interview-question-manage/InterviewQuestionItemDisplay/index.vue',
  'src/pages/questionManage/components/QuestionManageContentBody.vue',
  'src/pages/interview-question-manage/InterviewQuestionManage.vue',
  'src/pages/interview-question-manage/InterviewQuestionProofread.vue',
  'src/pages/interview-question-manage/InterviewQuestionSelect.vue',
  'src/pages/interview-question-manage/InterviewSubjectTree.vue',
  'src/pages/interview-question-manage/components/ProofreadingTask.vue',
  'src/pages/interview-question-manage/components/ProofreadingTaskFinished.vue',
  'src/pages/interview-question-manage/components/SetKeyword.vue',
  'src/pages/interview-room/index.vue',
  'src/pages/manualMark/ManualMark.vue',
  'src/pages/manualMark/components/HistoryCard.vue',
  'src/pages/manualMark/components/PaperCard.vue',
  'src/pages/monitorManage/MonitorManage.vue',
  'src/pages/monitorManage/SingleStudentMonitor.vue',
  'src/pages/monitorManage/components/MessageSendModal.vue',
  'src/pages/monitorManage/monitor/Monitor.vue',
  'src/pages/monitorManage/monitor/MonitorLive.vue',
  'src/pages/monitorManage/monitor/MonitorNoLive.vue',
  'src/pages/monitorManage/monitor/MonitorStatusStatistics.vue',
  'src/pages/monitorManage/monitorRecords/MonitorRecords.vue',
  'src/pages/monitorManage/monitorRecords/components/PicturePlayer.vue',
  'src/pages/paperManage/PaperAllocate.vue',
  'src/pages/paperManage/PaperConfig.vue',

  'src/pages/paperManage/PaperGradePublish.vue',
  'src/pages/paperManage/PaperQuestionForm.vue',
  'src/pages/paperManage/PaperQuestionSelect.vue',
  'src/pages/paperManage/PaperQuestionSelectPreview.vue',
  'src/pages/paperManage/PaperSelect.vue',
  'src/pages/paperManage/components/CatalogQuestionAmount.vue',
  'src/pages/paperManage/components/QuestionTypeChart.vue',
  'src/pages/paperManage/components/SharePaper.vue',
  'src/pages/paperManage/components/paperCard.vue',
  'src/pages/paperManage/index.vue',
  'src/pages/questionManage/QuestionConvertor.vue',
  'src/pages/questionManage/QuestionForm/AlgorithmForm.vue',
  'src/pages/questionManage/QuestionForm/CompositeForm.vue',
  'src/pages/questionManage/QuestionForm/FillblankForm.vue',
  'src/pages/questionManage/QuestionForm/QaFrom.vue',
  'src/pages/questionManage/QuestionForm/SelectForm.vue',
  'src/pages/questionManage/QuestionForm/SortForm.vue',
  'src/pages/questionManage/QuestionForm/components/ComplexInput.vue',
  'src/pages/questionManage/QuestionForm/components/OptionGenerator.vue',
  'src/pages/questionManage/QuestionForm/components/QuestionEditor.vue',
  'src/pages/questionManage/QuestionForm/components/ScoreInput.vue',
  'src/pages/questionManage/QuestionForm/index.vue',
  'src/pages/questionManage/QuestionGenerator.vue',
  'src/pages/questionManage/QuestionItemDisplay/OptionsBox.vue',
  'src/pages/questionManage/QuestionItemDisplay/index.vue',
  'src/pages/questionManage/QuestionList.vue',
  'src/pages/questionManage/QuestionManage.vue',
  'src/pages/questionManage/QuestionProofread.vue',
  'src/pages/questionManage/QuestionSelect.vue',
  'src/pages/questionManage/SubjectTree.vue',
  'src/pages/questionManage/components/ProofreadingTask.vue',
  'src/pages/questionManage/components/ProofreadingTaskFinished.vue',
  'src/pages/questionManage/components/SetKeyword.vue',
  'src/pages/recycle/components/InterviewQuestionRecycle.vue',
  'src/pages/recycle/components/PaperRecycle.vue',
  'src/pages/recycle/components/QuestionRecycle.vue',
  'src/pages/recycle/index.vue',
  'src/pages/resourceGroupManage/ResourceGroupManage.vue',
  'src/pages/survey/SurveyAnalysis.vue',
  'src/pages/survey/SurveyAnalysisChart.vue',
  'src/pages/survey/SurveyAnalysisTable.vue',
  'src/pages/survey/SurveyAnswersPanel.vue',
  'src/pages/survey/SurveyCard.vue',
  'src/pages/survey/SurveyForm.vue',
  'src/pages/survey/SurveyManage.vue',
  'src/pages/survey/SurveyPreview.vue',
  'src/pages/survey/SurveyQrCode.vue',
  'src/pages/survey/components/PieChart.vue',
  'src/pages/survey/utils.ts',
  'src/pages/system/ChangeDepartment.vue',
  'src/pages/system/ChangePwd.vue',
  'src/pages/system/ChangePwdFromEmail.vue',
  'src/pages/system/ForgetPwd.vue',
  'src/pages/system/components/PwdStrenthPanel.vue',
  'src/pages/system/loseEfficacy.vue',
  'src/pages/todo/Index.vue',
  'src/pages/welcome/index.vue',
  'src/plugins/font-awesome.ts',
  'src/plugins/print.ts',
  'src/plugins/svgBuilder.ts',
  'src/router/index.ts',
  'src/router/permission.js',
  'src/shime-vue.d.ts',
  'src/store/index.ts',
  'src/store/permission.ts',
  'src/styles/animation.less',
  'src/styles/common.less',
  'src/styles/index.less',
  'src/styles/mixin.less',
  'src/styles/rewrite.less',
  'src/styles/transition.less',
  'src/styles/variable.less',
  'src/theme/dark.less',
  'src/theme/default.less',
  'src/types/index.ts',
  'src/types/interviewQuestion.ts',
  'src/types/route-meta.d.ts',
  'src/types/survey.d.ts',
  'src/utils/antdUtil.ts',
  'src/utils/bus.ts',
  'src/utils/common.ts',
  'src/utils/globalClickHandler.ts',
  'src/utils/http.ts',
  'src/utils/index.ts',
  'src/utils/nprogress.ts',
  'src/utils/rem.ts',
  'src/utils/scene.ts',
  'src/utils/storage.ts',
  'src/utils/url.ts',
  'src/utils/validate.ts',
  'src/views/AccountManage/components/AccountForm.vue',
  'src/views/AccountManage/components/AccountTable.vue',
  'src/views/AccountManage/components/Dialog.vue',
  'src/views/AccountManage/components/TeacherForm.vue',
  'src/views/AccountManage/index.vue',
  'src/views/ErrorPage/index.vue',
  'src/views/Login/adminLogin.vue',
  'src/views/Login/components/InviteLoginForm.vue',
  'src/views/ResumeManage/components/ResumeMultipleSelect/ResumeSidebar.vue',
  'src/views/Login/validate.js',
  'src/views/Login2/bindwx.vue',
  'src/views/Login2/components/bg.vue',
  'src/views/Login2/components/loginLeft.vue',
  'src/views/Login2/index.vue',
  'src/views/Login2/scansucc.vue',
  'src/views/ManualMark/components/ResumeTable.vue',
  'src/views/ManualMark/index.vue',
  'src/views/Monitor/index.vue',
  'src/views/NewsManage/components/NewsDrawer.vue',
  'src/views/NewsManage/index.vue',
  'src/views/PaperManage/components/ContextMenu.vue',
  'src/views/PaperManage/components/CreateInvitationCode.vue',
  'src/views/PaperManage/components/ExtendPaperTime.vue',
  'src/views/PaperManage/components/InvitationCode.vue',
  'src/views/PaperManage/components/JoinMarkPaper.vue',
  'src/views/PaperManage/components/PaperAnalysis.vue',
  'src/views/PaperManage/components/PaperAnalysisRadar.vue',
  'src/views/PaperManage/components/PaperBasicInfo.vue',
  'src/views/PaperManage/components/PaperExamConfig.vue',
  'src/views/PaperManage/components/PaperExamPublish.vue',
  'src/views/PaperManage/components/PaperOperateModal.vue',
  'src/views/PaperManage/components/PaperTable.vue',
  'src/views/PaperManage/components/PaperTypeModal.vue',
  'src/views/PaperManage/components/PreviewPaper.vue',
  'src/views/PaperManage/components/RadioBtn.vue',
  'src/views/PaperManage/components/ScoreDetail.vue',
  'src/views/PaperManage/components/SearchFilter.vue',
  'src/views/PaperManage/components/TagModal.vue',
  'src/views/PaperManage/components/UnifyScore.vue',
  'src/views/PaperManage/components/utils.ts',
  'src/views/PaperManage/correctPaperPlus.vue',
  'src/views/PaperManage/examManagement.vue',
  'src/views/PaperManage/paperEmailSend.vue',
  'src/views/PaperManage/stuInfo.vue',
  'src/views/QuestionManage/components/AiAideModal.vue',
  'src/views/QuestionManage/components/AiInputInfo.vue',
  'src/views/QuestionManage/components/AlgorithmFullScreen.vue',
  'src/views/QuestionManage/components/BlankFilling.vue',
  'src/views/QuestionManage/components/CodeModal.vue',
  'src/views/QuestionManage/components/ContextMenu.vue',
  'src/views/QuestionManage/components/JudgingQuestions.vue',
  'src/views/QuestionManage/components/MultipleChoice.vue',
  'src/views/QuestionManage/components/OrderQuestion.vue',
  'src/views/QuestionManage/components/ParamList.vue',
  'src/views/QuestionManage/components/QuestionAndAnswer.vue',
  'src/views/QuestionManage/components/SaveDraft.vue',
  'src/views/QuestionManage/components/ScoreInput.vue',
  'src/views/QuestionManage/components/ScoreInput2.vue',
  'src/views/QuestionManage/components/SearchFilter.vue',
  'src/views/QuestionManage/components/SingleChoice.vue',
  'src/views/QuestionManage/components/TagModal.vue',
  'src/views/QuestionManage/components/TestInstance.vue',
  'src/views/QuestionManage/components/ValidateAlgorithm.vue',
  'src/views/QuestionManage/createQuestion.vue',
  'src/views/QuestionManage/data.ts',
  'src/views/QuestionManage/draft.vue',
  'src/views/QuestionManage/editQuestion.vue',
  'src/views/QuestionManage/hooks/useAlgorithm.ts',
  'src/views/QuestionManage/hooks/useCompose.ts',
  'src/views/QuestionManage/hooks/useGetPoints.ts',
  'src/views/QuestionManage/hooks/useGetScore.ts',
  'src/views/QuestionManage/hooks/useOption.ts',
  'src/views/QuestionManage/hooks/useSaveDraft.ts',
  'src/views/QuestionManage/hooks/useSubmitQuestion.ts',
  'src/views/QuestionManage/hooks/useWatchBody.ts',
  'src/views/QuestionManage/hooks/useWatchForm.ts',
  'src/views/QuestionManage/hooks/useWatchParameters.ts',
  'src/views/QuestionManage/hooks/useWatchType.ts',
  'src/views/QuestionManage/keywords.ts',
  'src/views/QuestionManage/rules.ts',
  'src/views/ResumeManage/components/CheckPaper.vue',
  'src/views/ResumeManage/components/ContextMenu.vue',
  'src/views/ResumeManage/components/ResumeTable.vue',
  'src/views/ResumeManage/components/SearchFilter.vue',
  'src/views/ResumeManage/components/TagModal.vue',
  'src/views/ResumeManage/index.vue',
  // 'src/views/ResumeManage/resumeDetail.vue',
  'src/views/RolesManage/index.vue',
  'src/views/StatisticAnalysis/components/AddGraph.vue',
  'src/views/StatisticAnalysis/components/BarChart.vue',
  'src/views/StatisticAnalysis/components/BarStackChart.vue',
  'src/views/StatisticAnalysis/components/BubbleChart.vue',
  'src/views/StatisticAnalysis/components/Grid.vue',
  'src/views/StatisticAnalysis/components/GridLayoutChart.vue',
  'src/views/StatisticAnalysis/components/LineChart.vue',
  'src/views/StatisticAnalysis/components/NumberChart.vue',
  'src/views/StatisticAnalysis/components/PaperTable.vue',
  'src/views/StatisticAnalysis/components/PieChart.vue',
  'src/views/StatisticAnalysis/components/Sunburst.vue',
  'src/views/StatisticAnalysis/components/TableAnalysis.vue',
  'src/views/StatisticAnalysis/paperAnalysis.vue',
  'src/views/SystemSetting/components/modifyDeptModal.vue',
  'src/views/SystemSetting/index.vue',
  'src/views/TagsManage/components/PaperTable.vue',
  'src/views/TagsManage/components/TagForm.vue',
  'src/views/TagsManage/components/TagTree.vue',
  'src/views/TagsManage/index.vue',
  'signal-exit/LICENSE.txt',
  'signal-exit/README.md',
  'signal-exit/index.js',
  'signal-exit/package.json',
  'signal-exit/signals.js',
  'public/MP_verify_veD5vxaEFnYHkHPk.txt',
  'public/chrome.png',
  'public/constant.js',
  'public/edge.png',
  'public/favicon.ico',
  'public/loading.json',
  'public/not-support.html',
  'public/overlate.html',
  'public/resume.html',
  'public/unilogo.svg',
  'public/update.png',
  './script/deploy.cjs',
  'script/claude/**/**.js',
  '**/_*/**',
  'src/views/writtenQuestionManage/**/**',
  'src/views/positionManagement/component/postManageList.vue',
  'src/api/web/sys/**',
  'src/views/ResumeManage/components/selectQuestionType/component/extractQuestion/subjectTree.vue',
]
