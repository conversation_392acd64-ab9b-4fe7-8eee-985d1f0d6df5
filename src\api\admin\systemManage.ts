import request from '@/utils/http'

// 获取部门列表
export function getDepList(data?: object) {
  return request({
    url: '/admin/departments',
    data
  })
}

// 修改部门层级
export function modifyDeptLevel(data?: object) {
  return request({
    url: '/admin/alterstru',
    data
  })
}

// 获取所有教师
export function allteachers(data?: object) {
  return request({
    url: '/admin/allteachers',
    data
  })
}

// 更改部门
export function alterteachersdept(data?: object) {
  return request({
    url: '/admin/alterteachersdept',
    data
  })
}

// 资源组
export function resourcegroup(data?: object) {
  return request({
    url: '/admin/resourcegroup',
    data
  })
}
