<script setup lang="ts">
import SearchBoxContent from './SearchBoxContent'

const searchValue = ref('')
const focusSearch = ref(false)
const searchBoxRef = useTemplateRef('searchBox')
function focusSearchBox() {
  focusSearch.value = true
}
function blurSearchBox() {
  focusSearch.value = false
}
function handleClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement
  if (searchBoxRef.value && !searchBoxRef.value.contains(target)) {
    blurSearchBox()
  }
  else {
    focusSearchBox()
  }
}
document.addEventListener('click', handleClickOutside)
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
const activeKey = ref('1')
</script>

<template>
  <div ref="searchBox" class="filter-wrapper-row h-[40px] relative">
    <div :class="`absolute top-0 left-0 ${focusSearch ? 'w-[850px] h-[850px]' : ''}`">
      <a-input-search
        v-model:value.trim="searchValue"
        :style="{ width: focusSearch ? '800px' : '' }"
        class="search-input"
        placeholder="请输入问卷/教师名称"
        allow-clear
        @focus="focusSearchBox"
      />

      <div :style="{ height: focusSearch ? '800px' : '0px' }" class="search-box h-[0] w-[850px] bg-[#fff] absolute top-[40px] left-[-24px] z-[99] rounded-[8px]">
        <div :style="{ height: focusSearch ? '800px' : '0' }" class="transition-all duration-[0.3s] ease-[ease] w-[850px] h-[800px] bg-[#fff] rounded-[8px] px-[32px] pt-[16px]">
          <a-tabs v-show="focusSearch" v-model:active-key="activeKey">
            <a-tab-pane key="1">
              <template #tab>
                <div>
                  全部
                </div>
              </template>
              <SearchBoxContent type="all" />
            </a-tab-pane>
            
            <a-tab-pane key="2">
              <template #tab>
                <div>题库</div>
              </template>
              <SearchBoxContent type="package" />
            </a-tab-pane>

            <a-tab-pane key="3">
              <template #tab>
                <div>科目</div>
              </template>
              <SearchBoxContent type="subject" />
            </a-tab-pane>

            <a-tab-pane key="4">
              <template #tab>
                <div>题目</div>
              </template>
              <SearchBoxContent type="question" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-input-wrapper) {
  transition: all 0.3s ease;
}

.search-box{
  transition: all 0.3s ease;

}
.search-input{
  transition: all 0.3s ease;
  width: 300px;

}
.empty-box {
  margin-top: 20%;
  margin-left: 50%;
  transform: translateX(-50%);
}

.filter-wrapper {
  position: relative;
  width: 100%;
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }
  }
}
</style>