<template>
  <div class="validate-algorithm">
    <template v-for="item in lang" :key="item">
      <div class="item">
        <div class="item-head">
          <span class="lang">编程语言: {{ item }}</span>
          <div class="validate-result">
            <a-button class="validate-btn" type="primary" @click="runCode(item)" :loading="langMap[item].loading" :disabled="(!Object.keys(funcMainParams).length) || (!langMap[item].src)">
              验证
            </a-button>
            <span class="result" v-if="langMap[item].showTip && langMap[item].pass === 1"
              ><svg-icon name="done" class="icon-result" />验证通过</span
            >
            <span class="result" v-if="langMap[item].showTip && langMap[item].pass === -1"
              ><svg-icon name="errortip" class="icon-result" />验证失败</span
            >
          </div>

          <div class="operate-btns">
            <a-tooltip>
              <template #title>
                <span style="font-size: 12px">刷新</span>
              </template>
              <a-popconfirm
                class="pop-confirm"
                placement="topRight"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleResetClick(item)"
              >
                <template #title>
                  <p>是否确定重置编译器?</p>
                </template>
                <svg-icon class="item-icon" name="reload" />
              </a-popconfirm>
            </a-tooltip>
            <a-tooltip>
              <template #title>
                <span style="font-size: 12px">全屏</span>
              </template>
              <svg-icon class="item-icon" name="fullscreen" @click="handleFullScreenClick(item)" />
            </a-tooltip>
          </div>
        </div>
        <div>
          <code-editor
            v-model="langMap[item]['src']"
            :lang="langMap[item]['lang']"
            :template="funcTemplate"
            :answer="langMap[item]['latestAnswer']"
            :counter="langMap[item]['counter']"
          />
        </div>
      </div>
    </template>
  </div>

  <custom-dialog
    class="validate-result-modal"
    :width="800"
    :dialogVisible="validateResultVisible"
    :txtConfig="txtConfig"
    :keyboard="false"
    :maskClosable="false"
    :disabled="disabledConfirm"
    @updateDialog="handleConfirm"
    @closeDialog="handleCancel"
    @cancel-dialog="handleCancel"
  >
    <div class="steps-wrap" v-if="showValidateSteps">
      <a-steps
        :current="currentStep"
        :status="currentStatus"
        labelPlacement="vertical"
        size="small"
      >
        <template v-for="(item, index) in lang" :key="item">
          <a-step :title="item">
            <template #icon>
              <span class="wait" v-if="langMap[item].status === 'wait'">{{ index + 1 }}</span>
              <Icon v-else :icon="getItemStatusIcon(item)" />
            </template>
          </a-step>
        </template>
      </a-steps>
    </div>
    <div class="validate-tips-head">
      <span v-if="!isPassValidate()" class="lang">编程语言: {{ currentLang }}</span>
      <span class="err-info" v-if="!isPassValidate()">
        <svg-icon name="errortip" class="icon-err"></svg-icon>
        <span v-if="currentValidateResult.is_error">代码报错</span>
        <span v-else>未通过全部测试用例</span>
      </span>
      <div v-if="currentStep === 6" class="validate-pass">
        <span class="tip"><svg-icon name="done" class="icon-result" />验证通过</span>
      </div>
    </div>
    <div class="validate-tips-content">
      <div v-if="currentValidateResult.is_error">
        <div class="err-result">
          {{ currentValidateResult.results && currentValidateResult.results[0] }}
        </div>
      </div>
      <div
        class="content"
        v-else-if="currentValidateResult.results.filter((item: any) => !item.is_pass).length"
      >
        <div class="item">
          <span class="item-text">输入: </span>{{ formatTestcase(getFailValidateResult()) }}
        </div>
        <div class="item">
          <span class="item-text">期望输出: </span>{{ getFailValidateResult().output[0].value }}
        </div>
        <div class="item">
          <span class="item-text">实际输出: </span>{{ getFailValidateResult().result }}
        </div>
      </div>
    </div>
  </custom-dialog>

  <algorithm-full-screen
    v-if="isFullscreen"
    :isFullscreen="isFullscreen"
    :questionData="formState"
    :lang="currentFullScreenLang"
    :funcMain="funcMain"
    :func-main-params="funcMainParams"
    :funcTemplate="funcTemplate"
    v-model="langMap"
    @close="handleCloseFullScreen"
    @update-full-screen="updateFullScreen"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import CodeEditor from '@/components/CodeEditor.vue'
import CustomDialog from '@/components/CustomDialog.vue'
import AlgorithmFullScreen from './AlgorithmFullScreen.vue'
import { ojquesjudge } from '@/api/admin/questionManage'
import { useRoute } from 'vue-router'

const props = withDefaults(defineProps<{
  funcMain?: any
  funcMainParams: {
    func_name?: string
    rtype?: any
    parameters?: any[]
  }
  funcTemplate?: any
  formState?: any
}>(), {
  funcMain: null,
  funcTemplate: null,
  formState: null,
})

const emits = defineEmits(['updateTemplate', 'updateValidateCode'])

const lang = ['C', 'C++', 'Java', 'JavaScript', 'Python', 'Go']
const langMap = ref({
  C: { lang: 'c', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
  'C++': { lang: 'cpp', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
  Java: { lang: 'java', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
  JavaScript: { lang: 'js', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
  Python: { lang: 'python', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
  Go: { lang: 'go', loading: false, status: 'wait', src: '', showTip: false, counter: 0 },
})

const currentStep = ref(0)
const currentStatus = ref('wait')

const getItemStatusIcon = (item: string) => {
  switch (langMap.value[item].status) {
    case 'process':
      return 'LoadingOutlined'
    case 'finish':
      return 'CheckCircleOutlined'
    case 'error':
      return 'CloseCircleOutlined'
  }
}

// 运行代码验证
const currentLang = ref() // 当前验证语言
const currentValidateResult = ref() // 当前验证结果
const disabledConfirm = ref(false)
const runCode = (item: any, auto = false) => {
  langMap.value[item].showTip = false
  langMap.value[item].loading = true
  currentLang.value = item
  langMap.value[item].status = 'process'
  return ojquesjudge({
    src: langMap.value[item].src,
    codeType: langMap.value[item].lang,
    ...props.funcMainParams,
    btestcase: JSON.stringify(props.formState.btestcase),
    ptestcase: JSON.stringify(props.formState.ptestcase)
  })
    .then((res) => {
      if (!closeStatus.value || !auto) {
        currentValidateResult.value = res
        if (auto) {
          validateResultVisible.value = true
          currentStep.value !== 6 && (disabledConfirm.value = true)
        } else {
          showValidateSteps.value = false
          disabledConfirm.value = false
        }
        langMap.value[item].loading = false
        // 没有通过验证给提示框
        if (!isPassValidate()) {
          validateResultVisible.value = true
          langMap.value[item].status = 'error'
          currentStatus.value = 'error'
        } else {
          langMap.value[item].status = 'finish'
          currentStatus.value = 'finish'
          langMap.value[item].showTip = true
        }
      }
    })
    .catch(() => {
      langMap.value[item].loading = false
    })
    .finally(() => {})
  // setTimeout(() => {
  //   langMap.value[item].loading = false
  //   validateResultVisible.value = true
  // }, 1000)
}

const showValidateSteps = ref(false)
const submitQuestion = ref()
const autoValidateCode = async (cb?: () => any) => {
  closeStatus.value = false
  currentStatus.value = 'wait'
  if (cb) {
    submitQuestion.value = cb
  }
  showValidateSteps.value = true
  while (!closeStatus.value && currentStep.value < 6 && currentStatus.value !== 'error') {
    try {
      await runCode(lang[currentStep.value], true)
      if (currentStatus.value !== 'error') {
        currentStep.value++
      } else {
        disabledConfirm.value = false
      }
    } catch (error) {
      disabledConfirm.value = false
    }
    if (currentStep.value === 6) {
      disabledConfirm.value = false
    }
  }
}

// 判断是否通过校验
const isPassValidate = () => {
  if (
    currentValidateResult.value.is_error ||
    currentValidateResult.value.results.some((result: any) => !result.is_pass)
  ) {
    langMap.value[currentLang.value].pass = -1
    return false
  }
  langMap.value[currentLang.value].pass = 1
  return true
}

const getFailValidateResult = () => {
  const result = currentValidateResult.value.results.filter((item: any) => !item.is_pass)
  return result[0]
}

const formatTestcase = (item: any) => {
  let str = ''
  item.input.forEach((param: any) => {
    str += Object.keys(param)[0] + ' = ' + Object.values(param)[0] + ', '
  })

  return str.slice(0, -2)
}

// 重置编辑器
const route = useRoute()
const handleResetClick = (item: any) => {
  if (route.query.id) {
    emits('updateTemplate', item, langMap.value[item]['lang'])
  } else {
    langMap.value[item].counter++
  }
}

// 全屏
const isFullscreen = ref(false)

const currentFullScreenLang = ref()
const handleFullScreenClick = (lang: string) => {
  currentFullScreenLang.value = lang
  isFullscreen.value = true
}
// 关闭全屏
const handleCloseFullScreen = () => {
  isFullscreen.value = false
}
const updateFullScreen = (lang: string) => {
  handleResetClick(lang)
}

// 验证结果modal
const validateResultVisible = ref(false)
const txtConfig = ref({ title: '验证结果', confirm: '确认' })

const handleConfirm = () => {
  validateResultVisible.value = false
  langMap.value[currentLang.value].showTip = true
  if (submitQuestion.value && currentStep.value > 5) {
    const validatecode = {}

    Object.keys(langMap.value).forEach((key) => {
      validatecode[langMap.value[key]['lang']] = langMap.value[key].src
    })
    submitQuestion.value(validatecode)
    currentStep.value++
  }
}

const closeStatus = ref(false)
const handleCancel = async () => {
  blockWatchTrigger.value = true
  validateResultVisible.value = false
  if (langMap.value[currentLang.value]) {
    langMap.value[currentLang.value].showTip = true
  }
  currentStep.value = 0
  closeStatus.value = true

  // 清空之前状态
  Object.keys(langMap.value).forEach((key) => {
    langMap.value[key].status = 'wait'
    langMap.value[key].loading = false
    langMap.value[key].showTip = false
  })
  await nextTick()
  queueMicrotask(() => {
    blockWatchTrigger.value = false
  })
}

defineExpose({
  currentStep,
  langMap,
  autoValidateCode
})

// watch(currentStep, () => {
//   autoValidateCode()
// })
const blockWatchTrigger = ref(false)
const template = ref({})
watch(
  () => props.funcTemplate,
  (val) => {
    template.value = val
    handleCancel()
  }
)

watch(
  langMap,
  (val) => {
    if(blockWatchTrigger.value) return
    const validatecode = {}
    Object.values(val).forEach((item) => {
      validatecode[item.lang] = item.src
    })
    emits('updateValidateCode', validatecode)
  },
  { deep: true }
)
</script>

<style lang="less" scoped>
.validate-algorithm {
  .item {
    margin-bottom: 20px;
    .item-head {
      height: 26px;
      display: flex;
      align-items: center;
      font-size: 12px;
      margin-bottom: 5px;
      .lang {
        width: 127px;
      }
      .validate-result {
        display: flex;
        align-items: center;
        position: relative;
        margin-right: auto;
        .validate-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px 8px;
          height: 24px;
          border-radius: 4px;
          border-color: transparent;
          font-size: 12px;
          margin-right: 4px;
        }
        .loading {
          position: absolute;
          transform: translate(-42%, -50%);
          // left: 0;
          // top: 0;
        }
        .result {
          font-size: 12px;
          display: flex;
          align-items: center;
          .icon-result {
            font-size: 16px;
            margin-right: 4px;
          }
        }
      }
      :deep(.item-icon) {
        margin-left: 16px;
        cursor: pointer;
        height: 16px !important;
        width: 16px !important;
      }
    }
    :deep(.monaco-editor div) {
      font: 14px ConSolas !important;
    }
  }
}

.validate-result-modal {
  .validate-tips-head,
  .err-info {
    display: flex;
    align-items: center;
    font-size: 12px;
  }
  .validate-tips-head {
    margin-bottom: 16px;
    .validate-pass {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 28px 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 8px;
      .tip {
        display: flex;
        align-items: center;
        .icon-result {
          margin-right: 4px;
        }
      }
    }
  }
  .err-info {
    margin-left: 24px;
    color: #ff4d4f;
    .icon-err {
      margin-right: 4px;
    }
  }
  .validate-tips-content {
    // height: 192px;
    overflow: auto;

    .content {
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 8px;

      .item {
        font-size: 14px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
      .item-text {
        font-weight: 600;
        padding-right: 6px;
      }
    }
    .err-result {
      border-radius: 8px;
      background: #f5f5f5;
      padding: 16px;
      color: #ff4d4f;
    }
  }
  .steps-wrap {
    margin-bottom: 32px;
    .wait {
      display: inline-block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid rgba(0, 0, 0, 0.25);
      font-size: 14px;
    }
    :deep(.ant-steps-item-title) {
      font-size: 12px;
    }
    :deep(.ant-steps-item-tail) {
      top: -5px;
    }
  }
}
</style>
