<!-- 考试分析 -->
<template>
  <div class="analysis">
    <div class="common-page-title">
      <span>考试分析</span>
    </div>
    <div class="analysis-content">
      <a-row :gutter="20">
        <a-col :span="8">
          <div class="panel">
            <div class="spin-wrap" v-if="waitingLoading">
              <a-spin />
            </div>
            <div class="panel-title">待考试数量</div>
            <div class="panel-content">
              <a-tooltip placement="bottom" :visible="true" color="#fff" overlayClassName="custom-tooltip" :get-popup-container="getPopupContainer">
                <template #title>
                  <div class="tip">您关注的考试中有 <span :style="{ color: colors.waiting }">{{ waitingPager.teacher }}</span> 场待考试</div>
                  <div class="tip">本部门共有 <span :style="{ color: colors.waiting }">{{ waitingPager.dept }}</span> 场待考试</div>
                </template>
                <div class="value" ref="totalRef">
                  <div class="teacher" :style="{ color: colors.waiting }">{{ waitingPager.teacher }}</div>
                  <div class="dept" :style="{ color: colors.waiting }">/{{ waitingPager.dept }}</div>
                </div>
              </a-tooltip>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="panel">
            <div class="spin-wrap" v-if="doneLoading">
              <a-spin />
            </div>
            <div class="panel-title">已考试数量</div>
            <div class="panel-content">
              <a-tooltip placement="bottom" :visible="true" color="#fff" overlayClassName="custom-tooltip" :get-popup-container="getPopupContainer">
                <template #title>
                  <div class="tip">您关注的考试中有 <span :style="{ color: colors.done }">{{ donePager.teacher }}</span> 场已考试</div>
                  <div class="tip">本部门共有 <span :style="{ color: colors.done }">{{ donePager.dept }}</span> 场已考试</div>
                </template>
                <div class="value" ref="totalRef">
                  <div class="teacher" :style="{ color: colors.done }">{{ donePager.teacher }}</div>
                  <div class="dept" :style="{ color: colors.done }">/{{ donePager.dept }}</div>
                </div>
              </a-tooltip>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="panel">
            <div class="spin-wrap" v-if="totalLoading">
              <a-spin />
            </div>
            <div class="panel-title"> 总考试数量</div>
            <div class="panel-content">
              <a-tooltip placement="bottom" :visible="true" color="#fff" overlayClassName="custom-tooltip" :get-popup-container="getPopupContainer">
                <template #title>
                  <div class="tip">您共计关注 <span :style="{ color: colors.total }">{{ totalPager.teacher }}</span> 场考试</div>
                  <div class="tip">本部门共计 <span :style="{ color: colors.total }">{{ totalPager.dept }}</span> 场考试</div>
                </template>
                <div class="value" ref="totalRef">
                  <div class="teacher" :style="{ color: colors.total }">{{ totalPager.teacher }}</div>
                  <div class="dept" :style="{ color: colors.total }">/{{ totalPager.dept }}</div>
                </div>
              </a-tooltip>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row style="flex: 1; min-height: 0; margin-top: 20px">
        <a-col style="height: 100%" :span="24" flex="auto">
          <div class="panel table-panel">
            <div class="panel-title">
              考试成绩分析
              <a-tooltip placement="right" overlayClassName="light" title="统计教师本人可见的总考试场次" :get-popup-container="(trigger: HTMLElement) => trigger.parentElement">
                <svg-icon class="common-info-icon" name="info2" style="font-size: 14px;"></svg-icon>
              </a-tooltip>
            </div>
            <Papertable class="panel-content table-content" />
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import _ from 'lodash'
import { formatWithMod } from '@/utils/index'
import {
  getWaitingPaper,
  getDonePager,
  getTotalPaper,
} from '@/api/admin/statisticAnalysis'

import Papertable from './components/PaperTable.vue'

const getPopupContainer = (trigger: HTMLElement) => trigger.parentElement

const colors = {
  waiting: '#D71310',
  done: '#2F8C00',
  total: '#5478EE',
}

// 未考试数量
const waitingPager = reactive<{
  teacher: string | '--',
  dept: number | '--'
}>({
  teacher: '--',
  dept: '--'
})
const waitingLoading = ref(false)
const getExamsTodoFn = async () => {
  waitingLoading.value = true
  try {
    const res: any  = await getWaitingPaper()
     waitingPager.teacher = Number(res.teacher_count) >= 0 ? res.teacher_count : '--'
    waitingPager.dept = Number(res.dept_count) >= 0 ? res.dept_count : '--'
  } finally {
    waitingLoading.value = false
  }
}

// 已考试数量
const donePager = reactive<{
  teacher: string | '--',
  dept: number | '--'
}>({
  teacher: '--',
  dept: '--'
})
const doneLoading = ref(false)
const getExamsDoneFn = async () => {
  doneLoading.value = true
  try {
    const res: any  = await getDonePager()
    donePager.teacher = Number(res.teacher_count) >= 0 ? res.teacher_count : '--'
    donePager.dept = Number(res.dept_count) >= 0 ? res.dept_count : '--'
  } finally {
    doneLoading.value = false
  }
}

// 考试总数量
const totalPager = reactive<{
  teacher: string | '--',
  dept: number | '--'
}>({
  teacher: '--',
  dept: '--'
})
const totalLoading = ref(false)
const getExamsTotalFn = async () => {
  totalLoading.value = true
  try {
    const res: any  = await getTotalPaper()
    totalPager.teacher = Number(res.teacher_count) >= 0 ? res.teacher_count : '--'
    totalPager.dept = Number(res.dept_count) >= 0 ? res.dept_count : '--'
  } finally {
    totalLoading.value = false
  }
}

getExamsTodoFn()
getExamsDoneFn()
getExamsTotalFn()
</script>

<style lang="less" scoped>
.analysis {
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .analysis-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;

    .panel {
      background-color: #fff;
      height: 292px;
      padding: 24px 20px 0 24px;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      position: relative;
      .spin-wrap {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .panel-title {
        height: 28px;
        font-size: 20px;
        color: rgba(0,0,0,0.85);
        line-height: 28px;
        display: flex;
        align-items: center;
        .svg-icon {
          cursor: pointer;
          margin-left: 4px;
        }
      }

      .panel-content {
        flex: 1;
        min-height: 0;
        overflow: auto;
        &.table-content {
          margin-top: 24px;
        }
        :deep(.custom-tooltip) {
          display: none;
          .tip {
            color: rgba(0,0,0,0.85);
            font-size: 12px;
          }
        }
      }
      .value {
        flex: auto;
        height: 100px;
        margin-top: 45px;
        display: flex;
        justify-content: center;
        align-items: baseline;
        .teacher {
          height: 100px;
          font-size: 72px;
          font-weight: 600;
          text-align: center;
          color: #2f8c00;
          line-height: 100px;
        }
      }
      .dept {
        height: 22px;
        font-size: 24px;
        text-align: center;
        line-height: 22px;
      }
      &:hover .panel-content :deep(.custom-tooltip) {
        display: block;
      }
    }
    .table-panel {
      height: 100%;
    }
  }
}
</style>
