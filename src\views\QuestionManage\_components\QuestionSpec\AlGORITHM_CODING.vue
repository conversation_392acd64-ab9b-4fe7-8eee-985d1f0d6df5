<script setup lang="ts">
import { useQuestionManage, useQuestionOption, useFillBlank, useAlgo } from '../composable';
import useAlgorithm from '../../hooks/useAlgorithm'
import {  ojtemplate } from '@/api/admin/questionManage'
import ParamList from '../../components/ParamList.vue'
import TestInstance from '../../components/TestInstance.vue'
import ValidateAlgorithm from '../../components/ValidateAlgorithm.vue'

const props = defineProps<{
  showJsonEditor: boolean
}>()
const { validateAlgorithmRef, activeTab, varType , funcMainObj ,funcMainParams ,funcTemplateObj } = useAlgo()
const { formState, rules , scorepts ,scoreInputTotalRef } = useQuestionManage()
const {
  handleAddParameter,
  handleDelParameter,
  handleAddInstance,
  handleDelInstance,
  addTestCase,
  delTestCase,
} = useAlgorithm(formState)

function updateValidateCode(validatecode: any) {
  formState.value.validatecode = validatecode
}

async function updateTemplate(lang: string, item: any) {
  const result: any = await ojtemplate({
    func_name: formState.value.func_name,
    rtype: formState.value.rtype,
    parameters: formState.value.parameters,
  })
  // @ts-ignore
  funcTemplateObj.value[item] = JSON.parse(JSON.stringify(result.func_templete[item]))
  validateAlgorithmRef.value.langMap[lang].counter++
}
</script>

<template>
        <template v-if="formState.type === 4">
          <div v-show="activeTab === '1'">
            <a-form-item label="方法名称" name="func_name">
              <div class="func-name-wrapper">
                <a-input
                  v-model:value.lazy.trim="formState.func_name"
                  class="form-input"
                  placeholder="方法名称"
                  autocomplete="off"
                />
                <span class="func-extra-info">(以字母、下划线或美元符号开头，可包含字母、数字、下划线或美元符号，且大小写敏感，避免使用关键字，推荐采用驼峰命名法)</span>
              </div>
            </a-form-item>
            <a-form-item label="返回值类型" name="rtype">
              <div class="rtype-wrapper">
                <a-select
                  v-model:value="formState.rtype.type"
                  placeholder="返回值类型"
                  class="form-input"
                >
                  <template v-for="option in varType" :key="option.value">
                    <a-select-option :value="option.value">
                      {{ option.label }}
                    </a-select-option>
                  </template>
                </a-select>
                <!-- <a-input class="form-input" v-model:value="formState.rtype.type" /> -->
                <a-checkbox v-model:checked="formState.rtype.is_list" class="form-checkbox" />
                <span class="rtype-extra-info">&nbsp;此变量是数组或列表</span>
              </div>
            </a-form-item>
            <a-form-item label="参数列表" name="parameters">
              <ParamList
                :parameters="formState.parameters"
                @add-param="handleAddParameter"
                @del-param="handleDelParameter"
              />
            </a-form-item>
            <a-form-item label="题干测试用例" name="btestcase">
              <TestInstance
                :example-i-o="formState.btestcase"
                @add-instance="handleAddInstance"
                @del-instance="handleDelInstance"
              />
            </a-form-item>
            <a-form-item label="批卷测试用例" name="ptestcase">
              <TestInstance
                :example-i-o="formState.ptestcase"
                @add-instance="addTestCase"
                @del-instance="delTestCase"
              />
            </a-form-item>
            <a-form-item label="题目验证" name="validateCase">
              <ValidateAlgorithm
                ref="validateAlgorithmRef"
                :func-main="funcMainObj"
                :func-main-params="funcMainParams"
                :func-template="funcTemplateObj"
                :form-state="formState"
                @update-template="updateTemplate"
                @update-validate-code="updateValidateCode"
              />
            </a-form-item>

            <a-form-item label="难度" name="difficulty">
              <a-radio-group v-model:value="formState.difficulty">
                <a-radio :value="0">
                  简单
                </a-radio>
                <a-radio :value="1">
                  中等
                </a-radio>
                <a-radio :value="2">
                  困难
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </div>
          <div v-if="activeTab === '2'">
            <JsonEditorVue
              v-if="showJsonEditor"
              ref="jsonEditorRef"
              v-model="formState"
              mode="tree"
              class="jse-theme-dark"
            />
          </div>
        </template>
</template>

<style lang="less" scoped>
@import url('../QuestionBody/scoped.less');
</style>

<style lang="less">
@import url('../QuestionBody/wrap.less');
</style>