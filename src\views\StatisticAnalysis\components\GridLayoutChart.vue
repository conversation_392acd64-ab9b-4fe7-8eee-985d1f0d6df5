<template>
  <grid-layout v-model:layout="layoutData" :col-num="12" :row-height="30" :margin="[8, 10]">
    <template #default="{ gridItemProps }">
      <grid-item
        v-for="item in layout"
        :key="item.i"
        v-bind="gridItemProps"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :isDraggable="isDraggable"
        @resized="handleResizeItem"
        @moved="handleMoveItem"
      >
        <div class="assistor">
          <div class="close-icon">
            <i class="el-icon-close"></i>
          </div>

          <component
            :is="item.component"
            :data="item.data"
            :name="item.name"
            :width="item.newWidth"
            :height="item.newHeight"
            @changeDraggable="(flag: boolean) => isDraggable = flag"
          ></component>
        </div>
      </grid-item>
    </template>
  </grid-layout>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue'
const props = defineProps({
  layout: {
    type: Array,
    default: () => []
  }
})

const isDraggable = ref(true)

const emits = defineEmits(['updateLayout'])

const layoutData = ref<any>([])
const handleResizeItem = (i: number, w: number, h: number, newHeight: number, newWidth: number) => {
  layoutData.value.forEach((item: any) => {
    if (item.i === i) {
      item.newHeight = newHeight
      item.newWidth = newWidth
    }
  })
  emits('updateLayout', layoutData.value)
}
const handleMoveItem = (i: number, w: number, h: number, newHeight: number, newWidth: number) => {
  layoutData.value.forEach((item: any) => {
    if (item.i === i) {
      item.newHeight = newHeight
      item.newWidth = newWidth
    }
  })
  emits('updateLayout', layoutData.value)
}

watch(
  () => props.layout,
  (val) => {
    layoutData.value = val
  },
  {
    immediate: true
  }
)
</script>
<style lang="less" scoped>
.vue-grid-layout {
  position: relative;
  background: #fff;
  .vue-grid-item {
    .assistor {
      height: 100%;
      background: #fff;
      overflow: auto;
      // padding: 10px;
      // border: 1px solid rgb(224, 219, 219);
    }
    .close-icon {
      float: right;
      position: absolute;
      text-align: right;
      right: 0px;
      top: 0px;
      z-index: 200;
    }
  }
}
</style>
