<template>
  <div class="check-paper">
    <a-modal
      width="100%"
      v-model:visible="visible"
      :title="data.title"
      :keyboard="false"
      okText="保存"
      cancelText="取消"
      wrapClassName="check-full-modal"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <!-- 单选 -->
      <a-card class="check-desc-card" v-if="data.singleSelectQuestions.length">
        <p>单项选择题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.singleSelectQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span>{{ `【${item.score}分】` }}{{ index + 1 }}.{{ item.body }}</span>
            <label class="get-score"
              >得分
              <a-input
                size="small"
                style="width: 60px"
                :disabled="paperScore[item.id] >= 0"
                v-model:value="paperScore[item.id]"
            /></label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 36px;
              z-index: 9999;
              color: rgba(0, 255, 0, 0.5);
            "
            :style="[
              paperScore[item.id]
                ? { color: 'rgba(0, 255, 0, 0.5)' }
                : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-radio-group v-model:value="paperAnswer[item.id]">
            <a-radio
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              :style="radioStyle"
            >
              <span
                :style="[item.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : '']"
                >{{ option.content }}</span
              >
            </a-radio>
          </a-radio-group>
        </div>
      </a-card>

      <!-- 多选 -->
      <a-card class="check-desc-card" v-if="data.multiSelectQuestions.length">
        <p>选择题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.multiSelectQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span>{{ `【${item.score}分】` }}{{ index + 1 }}.{{ item.body }}</span>
            <label class="get-score"
              >得分
              <a-input
                v-model:value="paperScore[item.id]"
                size="small"
                style="width: 60px"
                :disabled="paperScore[item.id] >= 0"
              />
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 36px;
              z-index: 9999;
            "
            :style="[
              paperScore[item.id]
                ? { color: 'rgba(0, 255, 0, 0.5)' }
                : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-checkbox-group v-model:value="paperAnswer[item.id]">
            <a-checkbox
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              class="checkbox-style"
            >
              <span
                :style="[
                  item.answer.indexOf(option.value) > -1 ? { color: 'rgba(0, 255, 0, 0.8)' } : ''
                ]"
                >{{ option.content }}</span
              >
            </a-checkbox>
          </a-checkbox-group>
        </div>
      </a-card>

      <!-- 判断 -->
      <a-card class="check-desc-card" v-if="data.trueOrFalseQuestions.length">
        <p>判断题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.trueOrFalseQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span>{{ `【${item.score}分】` }}{{ index + 1 }}.{{ item.body }}</span>
            <label class="get-score"
              >得分
              <a-input
                v-model:value="paperScore[item.id]"
                size="small"
                style="width: 60px"
                :disabled="paperScore[item.id] >= 0"
            /></label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 36px;
              z-index: 9999;
            "
            :style="[
              paperScore[item.id]
                ? { color: 'rgba(0, 255, 0, 0.5)' }
                : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-radio-group v-model:value="paperAnswer[item.id]">
            <a-radio
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              :style="radioStyle"
            >
              <span
                :style="[item.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : '']"
                >{{ option.content }}</span
              >
            </a-radio>
          </a-radio-group>
        </div>
      </a-card>

      <!-- 问答 -->
      <a-card class="check-desc-card" v-if="data.descQuestions.length">
        <p>问答题</p>
        <div class="desc-question" v-for="(item, index) in data.descQuestions" :key="item.id">
          <div class="desc-question-title">
            <span>{{ `【${item.score}分】` }}{{ index + 1 }}.{{ item.body }}</span>
            <label class="get-score"
              >得分 <a-input v-model:value="paperScore[item.id]" size="small" style="width: 60px"
            /></label>
          </div>
          <a-textarea
            disabled
            :auto-size="{ minRows: 6 }"
            v-model:value="paperAnswer[item.id]"
            placeholder="答题区"
          ></a-textarea>
        </div>
      </a-card>

      <!-- 算法编程 -->
      <a-card class="check-desc-card" v-if="data.codeQuestions.length">
        <p>算法题</p>
        <div class="desc-question" v-for="(item, index) in data.codeQuestions" :key="item.id">
          <div class="desc-question-title">
            <span>{{ `【${item.score}分】` }}{{ index + 1 }}.{{ item.body }}</span>
            <label class="get-score"
              >得分 <a-input v-model:value="paperScore[item.id]" size="small" style="width: 60px"
            /></label>
          </div>
          <a-textarea
            disabled
            :auto-size="{ minRows: 10 }"
            v-model:value="paperAnswer[item.id]"
            placeholder="答题区"
          ></a-textarea>
        </div>
      </a-card>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { getPaperDetail } from '@/api/admin/paperManage'
import { QuestionModel } from '@/models/questionModel'
import { message } from 'ant-design-vue'
import { ref, reactive, onMounted, watchEffect } from 'vue'

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  paperId: String,
  answer: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['submit', 'close'])

const data = reactive({
  title: '',
  count: 0,
  totalScore: 0,
  singleSelectQuestions: <any>[],
  multiSelectQuestions: <any>[],
  trueOrFalseQuestions: <any>[],
  descQuestions: <any>[],
  codeQuestions: <any>[]
})

const paperAnswer = reactive({})
const paperScore = reactive({})

// 是否显示
let visible = ref(false)

const radioStyle = reactive({
  display: 'block',
  paddingLeft: '10px',
  height: '30px',
  lineHeight: '30px'
})

watchEffect(() => {
  visible.value = props.isVisible
  console.log(props.answer)
  Object.assign(paperAnswer, props.answer)
})

const handleSave = () => {
  const scoreList = Object.values(paperScore)
  if (scoreList.length < data.count) {
    message.warning('请给出所有试题的得分！')
    return
  }
  scoreList.forEach((item) => {
    data.totalScore += parseInt(item as string)
  })
  emits('submit', { score: data.totalScore, paper: props.paperId })
}

const handleCancel = () => {
  emits('close')
}
onMounted(() => {
  getPaperDetail({ id: props.paperId }).then((res: any) => {
    data.title = res.name
    data.count = res.body.length

    data.singleSelectQuestions = res.body.filter((item: QuestionModel) => item.type === 0)
    data.singleSelectQuestions.forEach((item: any) => {
      const id = item.id
      const answer = item.answer
      const score = item.score
      if (paperAnswer[id] && paperAnswer[id][0] === answer) {
        paperScore[id] = score
      } else {
        paperScore[id] = 0
      }
    })

    data.multiSelectQuestions = res.body.filter((item: QuestionModel) => item.type === 1)
    data.multiSelectQuestions.forEach((item: any) => {
      const id = item.id
      const answer = item.answer
      const score = item.score
      if (paperAnswer[id] && paperAnswer[id].sort().join('') === answer) {
        paperScore[id] = score
      } else {
        paperScore[id] = 0
      }
    })

    data.trueOrFalseQuestions = res.body.filter((item: QuestionModel) => item.type === 2)
    data.trueOrFalseQuestions.forEach((item: any) => {
      const id = item.id
      const answer = item.answer
      const score = item.score
      if (paperAnswer[id] && paperAnswer[id][0] === answer) {
        paperScore[id] = score
      } else {
        paperScore[id] = 0
      }
    })
    data.descQuestions = res.body.filter((item: QuestionModel) => item.type === 3)
    data.codeQuestions = res.body.filter((item: QuestionModel) => item.type === 4)
  })
})
</script>

<style lang="less">
.ant-card.check-desc-card {
  width: 60%;
  margin: 0 auto;
  p {
    font-size: 24px;
    font-weight: bold;
    line-height: 40px;
  }
  .desc-question {
    padding: 20px;
    position: relative;
    .desc-question-title {
      line-height: 2;
      margin-bottom: 10px;
      display: flex;
      span {
        flex-grow: 1;
        margin-right: 10%;
        font-size: 16px;
      }
      .get-score {
        width: 200px;
        font-weight: bold;
        text-align: right;
      }
    }
  }
}
.check-full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-header {
    position: fixed;
    width: 100%;
    z-index: 99;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    overflow: auto;
  }
  .ant-modal-body {
    flex: 1;
    padding-top: 80px;
  }
}

.checkbox-style {
  display: block;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  &:first-child {
    padding-left: 18px;
  }
}
</style>
