<template>
  <div class="account-table">
    <a-table
      :columns="data.columns"
      :rowKey="(record:any) => record.id"
      :data-source="data.accountList"
      :row-selection="{
        selectedRowKeys: selectedTeacher,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        getCheckboxProps: getCheckboxProps
      }"
      :loading="data.loading"
      :customRow="customRow"
      :scroll="{ x: 1200 }"
      :pagination="props.pagination"
      @change="handleTableChange"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'oproles'">
          <a-popover placement="bottom" trigger="click">
            <template #content>
              <div style="width: 180px">
                <template v-for="role in record.oproles" :key="role.id">
                  <a-tag style="margin-top: 6px">{{ role.name }}</a-tag>
                </template>
              </div>
            </template>
            <div class="custom-ellisis">
              <template v-for="role in record.oproles" :key="role.id">
                <a-tag>{{ role.name }}</a-tag>
              </template>
            </div>
          </a-popover>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-tooltip placement="top">
              <template #title>
                <span>编辑</span>
              </template>
              <a-button type="link" @click="editTeacher(record)"
                ><img src="@/assets/images/blue-edit.png" style="width: 16px"
              /></a-button>
            </a-tooltip>
          </span>
          <span>
            <a-tooltip placement="top">
              <template #title>
                <span>查看权限</span>
              </template>
              <a-button type="link" @click="getPermission(record.id)"
                ><img src="@/assets/images/blue-eye.png" style="width: 16px"
              /></a-button>
            </a-tooltip>
          </span>
          <span v-if="record.status">
            <a-tooltip placement="bottom">
              <template #title>
                <span>禁用</span>
              </template>

              <a-popconfirm
                title="确认禁用该教师账号?"
                placement="topRight"
                ok-text="确定"
                cancel-text="取消"
                @confirm="forbidTeacher(record)"
              >
                <a-button type="link"
                  ><img src="@/assets/images/blue-close.png" style="width: 16px"
                /></a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
          <span v-else>
            <a-tooltip placement="bottom">
              <template #title>
                <span>启用</span>
              </template>

              <a-popconfirm
                title="确认启用该教师账号?"
                placement="topRight"
                ok-text="确定"
                cancel-text="取消"
                @confirm="activeTeacher(record)"
              >
                <a-button type="link"
                  ><img src="@/assets/images/blue-open.png" style="width: 16px"
                /></a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
          <span>
            <a-tooltip placement="bottom">
              <template #title>
                <span>删除</span>
              </template>

              <a-popconfirm
                title="确认删除该教师账号?"
                placement="topRight"
                ok-text="确定"
                cancel-text="取消"
                @confirm="delTeacher(record)"
              >
                <a-button type="link"
                  ><img src="@/assets/images/blue-del.png" style="width: 16px"
                /></a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'

import zhCN from 'ant-design-vue/es/locale/zh_CN'
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  pagination: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits([
  'updateTableData',
  'delTeacher',
  'forbidTeacher',
  'activeTeacher',
  'editTeacher',
  'getPermission',
  'getSelectedTeacher'
])

const selectedTeacher = <any>ref([])
const onSelect = (record: any, selected: boolean) => {
  if (!selected) {
    // 取消勾选,删除对应的数组项
    selectedTeacher.value.map((item: any, index: number) => {
      if (item === record.id) {
        selectedTeacher.value.splice(index, 1)
      }
    })
  }
  if (selected) {
    // 点击勾选,添加到selectedTeacher数组
    selectedTeacher.value.push(record.id)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedTeacher.value.push(item.id)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedTeacher.value.map((x: any, indey: number) => {
        if (item.id === x) {
          selectedTeacher.value.splice(indey, 1)
        }
      })
    })
  }
}

const getCheckboxProps = (record: any) => {
  return {
    selectedRowKeys: selectedTeacher.value.includes(record.id)
  }
}

const customRow = (record: any, index: number) => {
  return {
    onMouseenter: () => {
      record.active = true
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = '#3158BD'
    },
    onMouseleave: () => {
      record.active = false
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = ''
    }
  }
}

const handleTableChange = (pagination: any, filters: any = {}) => {
  emits('updateTableData', { pagination, filters })
}

const editTeacher = (record: any) => {
  emits('editTeacher', record)
}
const activeTeacher = (record: any) => {
  emits('activeTeacher', record)
}
const forbidTeacher = (record: any) => {
  emits('forbidTeacher', record)
}
const delTeacher = (record: any) => {
  emits('delTeacher', record.id)
}
const getPermission = (id: string) => {
  emits('getPermission', id)
}

watch(
  selectedTeacher,
  (val) => {
    emits('getSelectedTeacher', val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="less">
.account-table {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-table-selection-column {
    padding-left: 2px !important;
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    &:first-child {
      padding-left: 2px !important;
    }
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: bold;
      color: #121633;
    }
    // .ant-table-header-column .ant-table-column-sorters:hover::before {
    //   background: none;
    // }
  }

  .ant-table-tbody > tr > td {
    padding: 10px;
    font-family: PingFang HK;
    font-size: 14px;
    color: #121633;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
  .ant-btn {
    font-size: 14px;
  }
  .custom-ellisis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
}
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.ant-btn-sm {
  font-size: 12px !important;
}
.arrow-icon {
  position: relative;
  transform: rotate(180deg);
  bottom: -2px;
  left: 2px;
}
.paper-dropdown {
  .ant-dropdown-menu-title-content {
    font-size: 12px !important;
  }
}
.unstart-status,
.ing-status,
.ended-status {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}
.unstart-status {
  background: #52c41a;
}
.ing-status {
  background: #ff4d4f;
}
.ended-status {
  background: #d9d9d9;
}
</style>
