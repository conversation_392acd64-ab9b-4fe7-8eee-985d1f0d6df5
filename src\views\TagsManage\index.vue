<template>
  <div class="tag-manage">
    <div class="tags-tree">
      <h3>标签</h3>
      <div class="tags-search">
        <a-input-search
          class="search-wrapper"
          v-model:value.trim="searchTagContent"
          placeholder="标签搜索"
          @search="getSearchTagData"
        />
      </div>
      <div class="tags-tree-select">
        <tag-tree
            :treeData="treeData"
            :searchValue="searchTagContent"
            @getTagInfo="getTagInfo"
            @refreshTagList="refreshTagList"
          ></tag-tree>
      </div>
    </div>

    <div class="tags-paper-list">
      <div class="tags-paper-header">
        <div class="header-left">
          <a-input-search
            class="search-wrapper"
            v-model:value.trim="searchPaperContent"
            placeholder="试卷搜索"
            @search="getSearchPaperData"
          />
        </div>
         <div class="header-right">
        <span>
          <svg-icon name="info" class="info-icon" />
          <span>此标签下的项目, 总数: <span class="paper-count">{{data.total}}</span></span>
        </span>
      </div>
      </div>
      
      <div class="table-bar">
        <div class="btn-group">
          <a-checkbox v-model:checked="allSelected">全选</a-checkbox>
          <a-button class="del-btn" type="danger" @click="deleteMultiTags">从标签中移除</a-button>
        </div>
        <div class="show-columns">
          <span>分栏显示</span
          ><popover-box
            :columns="columnOptions"
            :selectedColumns="selectedColumns"
            @getDynamicColumns="handleChangeColumn"
          />
        </div>
      </div>

      <paper-table
      :data="data"
      :pagination="paginationConfig"
      @getPaperList="getPaperList"
      @editPaper="handleEditPaper"
      @handleTableChange="handleTableChange"
      @updateSelectedPaper="updateSelectedPaper"
    />
    </div>

    <tag-form :tagData="tagData" :tagModalVisible="tagModalVisible" @refreshTagList="refreshTagList" @closeTagModal="closeTagModal"></tag-form>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted,reactive, createVNode,computed } from 'vue'
import SpliteBox from '@/components/SpliteBox/index.vue'
import TagTree from './components/TagTree.vue'
import TagForm from './components/TagForm.vue'
import PaperTable from './components/PaperTable.vue'
import PopoverBox from '@/components/PopoverBox.vue'
import { tagsMange,paperList,removeTag} from '@/api/admin/tagsManage'

import { Paper } from '@/models/paperModel'
import { message,Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import storage from '@/utils/storage'

// 试卷配置数据
const data = reactive({
  columns: [
    {
      title: '试卷名称',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      ellipsis: true,
      display: true
    },
    {
      title: '总分',
      dataIndex: 'score',
      width: 100,
      key: 'score',
      ellipsis: true,
      display: true
    },
    {
      title: '考试时长',
      dataIndex: 'duration',
      width: 120,
      key: 'duration',
      ellipsis: true,
      display: true
    },
    {
      title: '考试开始时间',
      dataIndex: 'startTime',
      width: 240,
      key: 'startTime',
      sorter: true,
      ellipsis: true,
      display: false
    },
    {
      title: '考试截至时间',
      dataIndex: 'endTime',
      width: 240,
      key: 'endTime',
      sorter: true,
      ellipsis: true,
      display: false
    },
    {
      title: '试卷生成时间',
      dataIndex: 'create_at',
      width: 240,
      key: 'create_at',
      sorter: true,
      ellipsis: true,
      display: false
    },
    {
      title: '操作',
      fixed: 'right',
      key: 'operate',
      width: 60,
      slots: { customRender: 'action' },
      display: true
    }
  ],
  total: 0,
  tag: '',
  deletedList: <any>[],
  paperList: <any>[],
  paper: <Paper>{},
  visible: false,
  paperLoading: false,
  paperId: '',
  orderField: 'startTime',
  orderType: 'descend'
})
const allSelected = ref(false)
const tagModalVisible = ref(false)

const defaultSelectedColumn = ['name', 'score', 'duration', 'operate']
const selectedColumns = computed(() => {
  const personalColumns = storage.getItem('tagColumns')
  if(!personalColumns) {
    return [...defaultSelectedColumn]
  }

  data.columns.forEach((item) => {
    const isExist = personalColumns.includes(item.key)
    if (isExist) {
      item.display = true
    } else {
      item.display = false
    }
  })
  return personalColumns
})
const columnOptions = data.columns.map((item) => {
  return {
    label: item.title,
    value: item.key
  }
})

const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

// 动态展示列
const personalColumns = ref<string[]>([])
const handleChangeColumn = (checkedValue: Array<string>) => {
  personalColumns.value.length = 0
  data.columns.forEach((item) => {
    const isExist = checkedValue.includes(item.key)
    if (isExist) {
      item.display = true
      personalColumns.value.push(item.key)
    } else {
      item.display = false
    }
  })
  storage.setItem('tagColumns', personalColumns.value)
}
const handleTableChange = ({ pagination }: any, filters: any = {}, sort: any = {}) => {
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  if (sort.order) {
    data.orderField = sort.field
    data.orderType = sort.order
  } else {
    data.orderField = 'startTime'
    data.orderType = 'ascend'
  }
  getPaperList()
}

// 标签搜索
const searchTagContent = ref('')
const getSearchTagData = () => {}

// 试卷搜索
const searchPaperContent = ref('')
const getSearchPaperData = () => {
  getPaperList()
}

// 查询标签
const treeData = <any>ref([])
const tagsTotal = <any>ref(0)
const queryTags = async () => {
  const res: any = await tagsMange({ action: 'query' })
  treeData.value.length = 0
  treeData.value.push(...res.tags)
  tagsTotal.value = res.total
  tagData.value = treeData.value[0]
  getPaperList()
}

const updateSelectedPaper = (selectedList:string[]) => {
  data.deletedList.length = 0
  data.deletedList.push(...selectedList)
}

const deleteMultiTags = () => {
  if(!allSelected.value && !data.deletedList.length) {
    message.error('请勾选要删除的试卷')
    return
  }
  if(allSelected.value) {
    data.deletedList.length = 0
    data.deletedList.push('-1')
  }
  const params = {
    papers: [...data.deletedList],
    tag: tagData.value.id
  }

  Modal.confirm({
        title: () => `${allSelected.value ? '确定移除该标签下的所有试卷?': '确定移除所勾选的试卷?'}`,
        icon: () => createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        onOk() {
          removeTag(params).then(() => {
            message.success('批量移除成功!')
            // 刷新
            getPaperList()
          })
        },
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onCancel() {}
      })
}

const getPaperList = () => {
  const params = {
    tag: tagData.value.id,
    condition: searchPaperContent.value,
    page: paginationConfig.value.current,
    per_page:paginationConfig.value.pageSize
  }
  data.paperLoading = true
  paperList(params).then((res:any) => {
    data.paperList = res.data
    data.deletedList.length = 0
    paginationConfig.value.total = res.total
    data.total = res.total
    data.tag = tagData.value.id
  }).finally(() => {
    data.paperLoading = false
  })
}

// 获取标签信息
const tagData = ref({})
const getTagInfo = (info: any) => {
  tagData.value = info
  if(info.refresh) {
    getPaperList()
    return
  }
  tagModalVisible.value = true
}

const closeTagModal = () => {
  tagModalVisible.value = false
}

// 刷新标签信息
const refreshTagList = () => {
  tagModalVisible.value = false
  queryTags()
}

onMounted(() => {
  queryTags()
})
</script>
<style lang="less" scoped>
.tag-manage {
  display: flex;
  justify-content: space-between;
  background: #ECEDF2;
  .search-wrapper {
      width: 278px;
      height: 32px;
      background: #eff0f3;
      border: 1px solid #dcdde1;
      border-radius: 53px;

      color: #b4b5ba;
      box-shadow: none;
      :deep(.ant-input) {
        background: #eff0f3;
        font-size: 13px;
        line-height: 30px;
      }
    }
  .tags-tree {
    width: 326px;
    // height: calc(100vh - 97px);
    margin-right: 20px;
    flex-shrink: 0;
    background: #fff;
    box-shadow: 0px 3px 11px 0px rgba(0, 21, 52, 0.05);
    h3 {
      padding-left: 24px;
      font-weight: bold;
      color: #121633;
      height: 50px;
      line-height: 50px;
    }
    .tags-search {
      padding: 15px 24px;
      border-top: 1px solid #DCDDE1;
      border-bottom: 1px solid #DCDDE1;
    }
    .tags-tree-select {
      padding: 10px 0 30px 20px;
    }
  }
  .tags-paper-list {
    flex: 1;
    overflow: auto;
    background: #fff;
    .tags-paper-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 56px;
      padding: 0 24px;
      border-bottom: 1px solid #dcdde1;
      .header-right {
        font-size: 14px;
        color: #121633;
        font-weight: bold;
        .info-icon {
          position: relative;
          bottom: -2px;
          margin-right: 6px;
        }
        .paper-count {
          color: #3158bd;
        }
      }
    }
  }
  .table-bar {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .ant-checkbox-wrapper {
      font-size: 15px;
    }
    .btn-group {
      .del-btn {
        width: 126px;
        height: 30px;
        font-size: 14px;
        margin-left: 10px;
      }
    }
    .show-columns {
      font-size: 15px;
      span {
        margin-right: 10px;
      }
    }
  }
}
</style>
