<script setup lang="ts">
import StatusList from './statusList'

const props = defineProps<{
  backEndFileList: any[]
}>()
const activeName = ref('uploading')
</script>

<template>
  <div class="mt-[16px]">
    <el-tabs v-model="activeName">
      <el-tab-pane label="正在上传" name="uploading">
        <div class="max-h-[300px] overflow-auto">
          <StatusList v-for="item in props.backEndFileList.filter((item:any) => item.statusDesc !== '已完成')" :key="item.taskId" :item="item" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="已完成" name="finish">
        <div class="max-h-[300px] overflow-auto">
          <StatusList v-for="item in props.backEndFileList.filter((item:any) => item.statusDesc === '已完成')" :key="item.taskId" :item="item" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>

</style>