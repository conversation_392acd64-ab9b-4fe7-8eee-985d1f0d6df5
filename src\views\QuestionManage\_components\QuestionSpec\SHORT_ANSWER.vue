<script setup lang="ts">
import { useQuestionManage, useQuestionOption, useFillBlank } from '../composable';
import useGetPoints from '../../hooks/useGetPoints';
import useGetScore from '../../hooks/useGetScore';
import CommonPhraseButton from '../CommonPhrase/index.vue';
let { oldFillBlank } = useFillBlank();
const { formState, scorepts, scoreInputTotalRef } = useQuestionManage();
watch(() => formState.value.complexFlag, (val) => {
  if (val) {
    formState.value.readFlag = 'ORIGINAL';
  } 
})

</script>

<template>
  <a-form-item v-if="formState.type === 3" label="AI朗读" name="scorebasis">
    <a-radio-group v-model:value="formState.readFlag" class="mb-[12px]">
      <a-radio :value="'STEM'" :disabled="formState.complexFlag">朗读原文题干</a-radio>
      <a-radio :value="'ORIGINAL'">朗读自定义题干</a-radio>
    </a-radio-group>
    <a-textarea
      v-show="formState.readFlag != 'STEM'"
      v-model:value="formState.readContent"
      :rows="4"
      maxLength="100"
      placeholder="点击编辑"
    />
    <!-- 常用语 -->
    <CommonPhraseButton v-model="formState.readContent"  v-show="formState.readFlag != 'STEM'"/>
  <p class="standard-tip"></p>
  </a-form-item>
  <!-- <a-form-item v-if="formState.type === 3" label="评分依据" name="scoreBasis">
    <a-textarea
      v-model:value="formState.scoreBasis"
      :rows="4"
      placeholder="点击编辑"
    />
    <p class="standard-tip"></p>
  </a-form-item> -->
  <a-form-item v-if="formState.type === 3" name="reference" :rules="[{ required: true, message: '请输入参考答案' }]">
    <template #label>
      <div>
        <div class="text-[12px]">参考答案<br /></div>
        <!-- <div class="text-[12px]">（选填）</div> -->
      </div>
    </template>
    <a-textarea
      v-model:value="formState.reference"
      :rows="4"
      maxLength="1000"
      placeholder="点击编辑"
    />
    <p class="standard-tip"></p>
  </a-form-item>
  <!-- <a-form-item
    v-if="formState.type === 3"
    label="得分点"
    name="answer"
    class="points-wrapper"
  >
    <template v-for="(item, index) in scorepts" :key="index">
      <div class="item">
        <div class="index">{{ index + 1 }}</div>
        <div class="tags-select">
          <JTagInput
            v-model="item.keyword"
            :ignorecase="formState.ignorecase"
            placeholder="输入完成后按回车添加多个关键词"
          />
        </div>
        <div v-if="formState.sepscore" class="score-wrapper">
          <span class="score">分值</span>
          <ScoreInput
            v-model="item.score"
            class="score-input"
            @get-score="getPointsScore($event, item)"
          />
        </div>
        <div class="del-icon-wrap">
          <svg-icon
            class="del-icon"
            name="circle-del"
            width="16px"
            height="16px"
            @click="delPoints(index)"
          />
        </div>
      </div>
    </template>
    <div class="addpoints-btn">
      <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
    </div>
  </a-form-item> -->
<!-- 
  <div v-if="formState.type === 3" class="fill-blank-config">
    <div>
      <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
      <span>忽略大小写</span>
    </div>
  </div> -->
</template>

<style lang="less" scoped>
@import url('../QuestionBody/scoped.less');

</style>

<style lang="less">
@import url('../QuestionBody/wrap.less');
.add-normal-container .ant-modal-header {
  padding: 0
}
.add-normal-container .ant-modal-body {
  padding: 0;
}
</style>