<template>
  <div class="score-input-container">
    <div @click="subScore" class="substract-btn" :class="{ disabled: score <= 1 || disabled }">
      <svg-icon name="substract" />
    </div>
    <a-input-number
      class="score-input"
      :disabled="disabled"
      v-model:value="score"
      :min="1"
      :max="100"
      :formatter="(value:any) => Math.floor(value)"
      @input="$emit('update:modelValue', $event.target.value)"
    />
    <div @click="addScore" class="add-btn" :class="{ disabled: score >= 100 || disabled }">
      <svg-icon name="plus" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: String,
    default: ''
  },
  type: {
    type: Number
  }
})

const score = ref(0)

const emit = defineEmits(['getScore'])
const subScore = () => {
  if (score.value <= 1 || props.disabled) return
  score.value--
}
const addScore = () => {
  if (score.value >= 100 || props.disabled) return
  score.value++
}

defineExpose({
  score
})

watch(score, (val) => {
  if (val < 1) {
    score.value = 1
  }
  emit('getScore', score.value)
})

watch(
  () => props.modelValue,
  (val) => {
    score.value = Number(val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

/* 火狐 */
input {
  -moz-appearance: textfield;
}
.score-input-container {
  overflow: hidden;
  user-select: none;
  display: flex;
  align-items: center;
  width: 102px;
  height: 32px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  .substract-btn,
  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 100%;
    cursor: pointer;
  }
  .substract-btn {
    border-right: 1px solid #d9d9d9;
  }
  .disabled {
    background: #f5f5f5;
  }
  .add-btn {
    border-left: 1px solid #d9d9d9;
  }
  .score-input {
    flex: 1;
    height: 30px;
    border: none;
    box-shadow: none;
    text-align: center;
    :deep(.ant-input-number-input) {
      font-size: 12px;
    }
  }
}
</style>
