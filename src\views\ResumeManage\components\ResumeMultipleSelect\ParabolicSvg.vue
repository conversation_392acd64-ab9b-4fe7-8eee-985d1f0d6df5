<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'

const props = defineProps<Props>()

const emits = defineEmits(['startGSAPAnimation'])

interface Props {
  coords: number[]
}

const { width, height } = useWindowSize()

const path = computed(() => {
  return createParabolicPath(
    30,
    height.value - 30,
    props.coords[0] || 0,
    props.coords[1] || 0,
  )
})

function createParabolicPath(x1: number, y1: number, x2: number, y2: number) {
  if (x2 === 0 && y2 === 0) 
    return 'M 0 0 Z'

  const cv = 0.3
  // 计算控制点的坐标
  const cx = x2 - (x2 - x1) * cv
  const cy = y2 - (y1 + y2) * cv

  // 构建 SVG path 字符串
  const pathString = `M ${x2} ${y2} Q ${cx} ${cy} ${x1} ${y1}`
  return pathString
}

watch(() => path.value, () => {
  emits('startGSAPAnimation')
})
</script>

<template>
  <svg
    class="w-full h-full fixed inset-0 z-9 translate-z-0 box-border pointer-events-none"
    :viewBox="`0 0 ${width} ${height}`"
  >
    <path
      id="parabolicPath"
      fill="none"
      :stroke-width="2"
      stroke="transparent"
      :d="path"
    />
  </svg>
</template>

<style scoped>
</style>
