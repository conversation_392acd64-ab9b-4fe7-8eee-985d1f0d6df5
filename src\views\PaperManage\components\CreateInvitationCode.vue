<template>
  <a-modal
    class="paper-type-modal"
    v-model:visible="visible"
    title="创建考试邀请码"
    width="460px"
    style="margin-top: 100px"
    :footer="null"
    :maskClosable="false"
    @cancel="close"
  >
    <a-alert class="tip" style="margin-bottom: 16px;" type="info" show-icon>
        <template #icon><InfoCircleFilled style="font-size: 16px;" /></template>
        <template #message>
          提示：考试邀请码作为考生加入考试的指令，建议您妥善保管并防止泄漏。
        </template>
    </a-alert>
    <div class="type-cards">
      <a-form
        class="form-state"
        ref="formRef"
        :model="formState"
        :rules="rules"
        hideRequiredMark
        labelAlign="left"
        :colon="false"
      >
        <a-form-item label="考试名称">
          <span style="font-size: 12px;">{{ papername }}</span>
        </a-form-item>
        <a-form-item label="创建方式" name="createtype">
          <a-radio-group v-model:value="formState.createtype">
            <a-radio value="sys">系统随机生成</a-radio>
            <a-radio value="user">自定义</a-radio>
          </a-radio-group>
          <div v-if="formState.createtype === 'user'" class="invitecode-input-wrapper">
            <a-input
              class="code-input"
              v-model:value.trim="invitecode"
              placeholder="请输入考试邀请码"
              autocomplete="off"
            />
            <p class="desc">不限制字数，可使用中英文字符、数字、特殊字符（空格除外）</p>
          </div>
        </a-form-item>
        <a-form-item label="有效期" name="datetype">
          <a-radio-group v-model:value="formState.datetype">
            <a-radio value="sys">使用最长有效期</a-radio>
            <a-radio value="user">自定义</a-radio>
          </a-radio-group>
          <div v-if="formState.datetype === 'sys'" style="margin-top: 16px;">
            <span class="datetimerange-text">{{ maximumRangeText }}</span>
          </div>
          <div class="datetime-picker-wrapper" v-if="formState.datetype === 'user'">
            <!-- <a-range-picker
              v-model:value="datetime"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
            /> -->

            <a-range-picker
              show-time
              v-model:value="datetime"
              :disabled-date="disabledStartDate"
              :disabled-time="disabledDateTime"
              @calendarChange="handleCalendarChange"
              format="YYYY-MM-DD HH:mm"
              :placeholder="['开始日期', '结束日期']"
              @change="selectedStartRangeDate"
            />
          </div>
        </a-form-item>
        <a-form-item label="最大容量" name="maxNum" class="max-number-input">
          <a-input-number
            v-model:value="formState.maxNum"
            :precision="0"
            :min="0"
            :disabled="checkedNum"
            style="width: 80px"
            :controls="false"
          />
          <span class="maxnum-limit">
            <a-checkbox v-model:checked="checkedNum" />
            <span class="limit-text">不限制</span>
          </span>
        </a-form-item>
      </a-form>
    </div>
    <div class="btn-group">
      <span @click="createPaperType">创建</span>
      <span @click="close">取消</span>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Moment } from 'moment'
import { createInviteCode } from '@/api/admin/paperManage'
import dayjs, { Dayjs } from 'dayjs'
import store from '@/store'
import { ExclamationCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'

const props = withDefaults(
  defineProps<{
    invitationCodeModalVisible: boolean
    paper: string
    uniexam: string
    limitlateness?: number
    validtime: [string, string]
    duration?: number
    papername: string
    templeteUUID: string
  }>(),
  {
    invitationCodeModalVisible: false
  }
)

const emits = defineEmits(['closeModal', 'getPaperInvitationCode'])

const formState = ref({
  createtype: '',
  datetype: '',
  maxNum: 0
})
const formRef = ref()
const invitecode = ref('')
const datetime = ref<Dayjs[]>([])
const checkedNum = ref(true)

const disabledDate = (current: Moment) => {
  // Can not select days before today and today
  const currentTimeStamp = current && current.valueOf()
  const endTimeStamp = new Date(props.validtime[1]).getTime().valueOf()
  return endTimeStamp <= currentTimeStamp - 24 * 60 * 60 * 1000
}

// 日期限制
const disabledStartDate = (current: Dayjs) => {
  let currentDateStamp = current.startOf('date'),
    startDateStamp = earliestTime.value.startOf('date'),
    endDateStamp = latestTime.value.startOf('date')
  return currentDateStamp > endDateStamp || currentDateStamp < startDateStamp
}
const waitDate = ref() // 待选日期

// 时分限制
const handleCalendarChange = (date: [Dayjs, Dayjs], info: [string, string]) => {
  waitDate.value = date[0] || date[1]
}
const disabledDateTime = (date: Dayjs, type: 'start' | 'end') => {
  console.log(type, date)
  if (!date && waitDate.value) {
    // 如果是由组件自动切换待选日期，则根据待选日期动态限制时间
    date = waitDate.value
  }
  return {
    disabledHours: () => {
      let ans = []
      if (date) {
        // 判断date和最早生效的日期是否相同
        if (date.isSame(earliestTime.value, 'date')) {
          for (let i = 0; i < earliestTime.value.hour(); i++) {
            ans.push(i)
          }
        }
        // 判断date和最晚失效的日期是否相同
        if (date.isSame(latestTime.value, 'date')) {
          for (let i = 23; i > latestTime.value.hour(); i--) {
            ans.push(i)
          }
        }
      }
      return ans
    },
    disabledMinutes: () => {
      let ans = []
      if (date) {
        // 判断date和最早生效的日期是否相同
        if (date.isSame(earliestTime.value, 'date') && date.isSame(earliestTime.value, 'hour')) {
          for (let i = 0; i < earliestTime.value.minute(); i++) {
            ans.push(i)
          }
        }
        // 判断date和最晚失效的日期是否相同
        if (date.isSame(latestTime.value, 'date') && date.isSame(latestTime.value, 'hour')) {
          for (let i = 59; i > latestTime.value.minute(); i--) {
            ans.push(i)
          }
        }
      }
      return ans
    }
  }
}

const selectedStartRangeDate = (value: [Dayjs, Dayjs]) => {
  if (value) {
    value[1] = value[1].startOf('minute')
    datetime.value = value
  } else {
    datetime.value = []
  }
  waitDate.value = undefined
}

const validateCreateType = async (rule: any, value: any) => {
  if (!value) {
    return Promise.reject('请选择创建方式')
  }
  if (value === 'user' && !invitecode.value) {
    return Promise.reject('请输入考试邀请码')
  }
  return Promise.resolve()
}

const validateMaxNum = async (rule: any, value: any) => {
  if (Number(value) <= 0 && !checkedNum.value) {
    return Promise.reject('最大容量不能为0')
  }
  return Promise.resolve()
}

const validateDateType = async (rule: any, value: any) => {
  if (!value) {
    return Promise.reject('请选择有效期')
  }
  if (value === 'user' && !datetime.value.length) {
    return Promise.reject('请选择日期范围')
  }
  if (value === 'user' && datetime.value.length) {
    const endTimeStamp = new Date(props.validtime[1]).getTime().valueOf()
    if (datetime.value[0].valueOf() > endTimeStamp || datetime.value[1].valueOf() > endTimeStamp) {
      return Promise.reject('有效期时间范围不得超过试卷时间范围')
    }
  }
  return Promise.resolve()
}

// 校验规则
const rules = {
  createtype: [{ required: true, validator: validateCreateType, trigger: 'change' }],
  datetype: [{ required: true, validator: validateDateType, trigger: 'change' }],
  maxNum: [{ required: true, validator: validateMaxNum, trigger: 'blur' }]
}

const close = () => {
  invitecode.value = ''
  datetime.value = []
  formRef.value.resetFields()
  emits('closeModal')
}

// 计算邀请码最长有效期
const earliestTime = ref<Dayjs>(dayjs())
const latestTime = ref<Dayjs>(dayjs())
const getDateTimeRange = () => {
  // 如果是统考且无迟到时间限制，则邀请码失效时间为考试结束时间
  if (props.uniexam === '1' && props.limitlateness! <= 0)
    latestTime.value = dayjs(props.validtime[1])
  // 如果是统考且有迟到时间限制，则邀请码失效时间为考试开始时间 + 迟到时间
  else if (props.uniexam === '1' && props.limitlateness! > 0)
    latestTime.value = dayjs(props.validtime[0]).add(props.limitlateness!, 'minute')
  // 如果是非统考，则邀请码失效时间为考试最晚进入时间
  else latestTime.value = dayjs(props.validtime[1])
  // 自定义的有效期默认给最长有效期
  datetime.value = [earliestTime.value, latestTime.value]
}
getDateTimeRange()
const maximumRangeText = computed(
  () =>
    earliestTime.value.format('YYYY-MM-DD HH:mm:ss') +
    ' ~ ' +
    latestTime.value.format('YYYY-MM-DD HH:mm:ss')
)

const loading = ref(false)
const createPaperType = () => {
  formRef.value
    .validate()
    .then(() => {
      loading.value = true
      let etime
      if (formState.value.datetype === 'user') {
        etime = [
          dayjs(datetime.value[0]).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(datetime.value[1]).format('YYYY-MM-DD HH:mm:ss')
        ]
      } else {
        if (props.uniexam == '1') {
          if (props.limitlateness! <= 0) {
            etime = [
              dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
              dayjs(datetime.value[1]).format('YYYY-MM-DD HH:mm:ss')
            ]
          } else {
            const lastestTime =
              new Date(props.validtime[0]).getTime() + props.limitlateness! * 60 * 1000
            console.log(props.validtime[0])
            etime = [
              dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
              dayjs(lastestTime).format('YYYY-MM-DD HH:mm:ss')
            ]
          }
        } else if (props.uniexam == '0') {
          const lastestTime = new Date(props.validtime[1]).getTime() - props.duration! * 60 * 1000

          etime = [
            dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(lastestTime).format('YYYY-MM-DD HH:mm:ss')
          ]
        }
      }
      const params = {
        action: 'add',
        templeteId: props.paper,
        templeteUUID: props.templeteUUID,
        name: invitecode.value,
        etime: etime && Array.isArray(etime) ? etime.toString() : etime,
        maxcap: formState.value.maxNum,
        teacher: store.getters.userid
      }
      return createInviteCode(params)
    })
    .then((res: any) => {
      loading.value = false
      invitecode.value = ''
      datetime.value = []
      formRef.value.resetFields()
      emits('getPaperInvitationCode', res, props.papername)
    })
    .catch(() => {
      loading.value = false
    })
}

const visible = ref(false)
watch(
  () => props.invitationCodeModalVisible,
  (val) => {
    visible.value = val
  },
  {
    immediate: true
  }
)

watch(checkedNum, (val) => {
  if (val) {
    formState.value.maxNum = 0
  }
})
</script>

<style lang="less" scoped>
.paper-type-modal {
  .type-cards {
    overflow:hidden;
    :deep(.ant-form-item-label > label),
    .ant-form {
      label {
      font-size: 12px;
      color: #626262;
        }
      .ant-form-item {
          margin-bottom: 16px;
        }
    }
    :deep(.ant-col) {
      width: 100px;
    }
    .maxnum-limit {
      margin-left: 16px;
      font-size: 12px;
      .limit-text {
        margin-left: 8px;
      }
    }
    .invitecode-input-wrapper {
      .ant-input {
        font-size: 12px;
        border-radius: 8px;
        height: 32px;
      }

      .desc {
        font-size: 12px;
        color: #b4b5ba;
        padding-top: 10px;
      }
    }
    .datetime-picker-wrapper {
      .ant-picker {
        margin-top: 16px;
        width: 100% !important;
        height: 32px;
        border-radius: 8px;
        :deep(.ant-picker-input > input) {
          font-size: 12px;
        }
      }
    }
  }
  .datetimerange-text {
    font-size: 12px;
  }
  :global(.ant-form-item-explain-error) {
    font-size: 12px !important;
  }
  .btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 60px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      cursor: pointer;
      &:first-child {
        color: #fff;
        background: #5478ee;
        margin-right: 8px;
      }
    }
  }
}
</style>
<style lang="less">
.paper-type-modal .ant-modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #121633;
}
.paper-type-modal {
  .ant-modal-header,
  .ant-modal-body {
    padding-left: 32px !important;
    padding-right: 60px !important;
  }
  .ant-input,
  .ant-input-number {
    border-radius: 8px;
    height: 32px;
    font-size: 12px;
  }
  .ant-input-number-input {
    font-size: 12px;
  }
}
.ant-alert {
  align-items: baseline;
}
.ant-alert-icon{
  position: absolute;
  margin-top: 4px;
}
.ant-alert-content {
  padding-left: 22px;
}
</style>
