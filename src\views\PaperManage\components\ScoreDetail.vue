<template>
  <div class="check-paper">
    <a-modal
      width="100%"
      :title="'《' + paperName + '》'"
      v-model:visible="visible"
      :keyboard="false"
      :footer="null"
      wrapClassName="check-full-modal"
      @cancel="handleCancel"
    >
      <p class="stu-username">考生姓名：{{ username }}</p>
      <!-- 单选 -->
      <a-card class="check-desc-card" v-if="data.singleSelectQuestions.length">
        <p>单项选择题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.singleSelectQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分
              <span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 30px;
              z-index: 9999;
              color: #3158bd;
            "
            :style="[
              paperScore[item.id] ? { color: '#3158BD' } : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-radio-group v-model:value="paperAnswerJson[item.id]['text']">
            <a-radio
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              :style="radioStyle"
            >
              <span
                :style="[item.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : '']"
                v-html="item.complicatedediting ? option.content : transferStr(option.content)"
              ></span>
            </a-radio>
          </a-radio-group>
        </div>
      </a-card>

      <!-- 多选 -->
      <a-card class="check-desc-card" v-if="data.multiSelectQuestions.length">
        <p>多项选择题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.multiSelectQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分<span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 30px;
              z-index: 9999;
            "
            :style="[
              paperScore[item.id] ? { color: '#3158BD' } : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-checkbox-group v-model:value="paperAnswerJson[item.id]['text']">
            <div
              v-for="option in item.options"
              :key="option.value"
              :value="option.value"
              class="checkbox-wrapper"
            >
              <a-checkbox disabled class="checkbox-style" :value="option.value">
                <span
                  :style="[
                    item.answer.indexOf(option.value) > -1 ? { color: 'rgba(0, 255, 0, 0.8)' } : ''
                  ]"
                  v-html="item.complicatedediting ? option.content : transferStr(option.content)"
                ></span>
              </a-checkbox>
            </div>
          </a-checkbox-group>
        </div>
      </a-card>

      <!-- 判断 -->
      <a-card class="check-desc-card" v-if="data.trueOrFalseQuestions.length">
        <p>判断题</p>
        <div
          class="desc-question"
          v-for="(item, index) in data.trueOrFalseQuestions"
          :key="item.id"
        >
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分
              <span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 30px;
              z-index: 9999;
            "
            :style="[
              paperScore[item.id] ? { color: '#3158BD' } : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-radio-group v-model:value="paperAnswerJson[item.id]['text']">
            <a-radio
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              :style="radioStyle"
            >
              <span
                :style="[item.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : '']"
                v-html="item.complicatedediting ? option.content : transferStr(option.content)"
              ></span>
            </a-radio>
          </a-radio-group>
        </div>
      </a-card>

      <!-- 排序 -->
      <a-card class="check-desc-card" v-if="data.sortQuestions.length">
        <p>排序题</p>
        <div class="desc-question" v-for="(item, index) in data.sortQuestions" :key="item.id">
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分
              <span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 30px;
              z-index: 9999;
              color: #3158bd;
            "
            :style="[
              paperScore[item.id] ? { color: '#3158BD' } : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <div class="sort-options">
            <template v-for="(option, index) in item.options" :key="option.value">
              <div
                class="item"
                v-html="item.complicatedediting ? option.content : transferStr(option.content)"
                :style="[
                  findMaxContiguousIndex(
                    JSON.parse(item.answer),
                    item.stuanswer.split(',')
                  ).includes(option.value)
                    ? { background: '#52C41A' }
                    : { background: '#f5f5f5' }
                ]"
              ></div>
            </template>
          </div>
          <!-- <a-radio-group v-model:value="paperAnswerJson[item.id]['text']">
            <a-radio
              v-for="option in item.options"
              disabled
              :key="option.value"
              :value="option.value"
              :style="radioStyle"
            >
              <span
                :style="[item.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : '']"
                v-html="item.complicatedediting ? option.content : transferStr(option.content)"
              ></span>
            </a-radio>
          </a-radio-group> -->
        </div>
      </a-card>

      <!-- 填空 -->
      <a-card class="check-desc-card" v-if="data.fillBlankQuestions.length">
        <p>填空题</p>
        <div class="desc-question" v-for="(item, index) in data.fillBlankQuestions" :key="item.id">
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分<span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <span
            style="
              position: absolute;
              right: 40%;
              transform: rotate(-30deg);
              font-size: 30px;
              z-index: 9999;
            "
            :style="[
              paperScore[item.id] ? { color: '#3158BD' } : { color: 'rgba(255, 0, 0, 0.5)' }
            ]"
            >{{ paperScore[item.id] ? '正确' : '错误' }}</span
          >
          <a-input
            readonly
            class="fill-blank-input"
            v-model:value="paperAnswerJson[item.id]['text']"
            placeholder="答题区"
          ></a-input>
        </div>
      </a-card>

      <!-- 问答 -->
      <a-card class="check-desc-card" v-if="data.descQuestions.length">
        <p>问答题</p>
        <div class="desc-question" v-for="(item, index) in data.descQuestions" :key="item.id">
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分<span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <div
            class="desc-input-wrapper"
            v-html="getQATextAnswer(paperAnswerJson[item.id]['text'])"
            placeholder="答题区"
          ></div>
          <div class="answer-imgs">
            <template v-for="img in getAnswerImgs(paperAnswerJson[item.id]['text'])" :key="img">
              <div class="item">
                <a-image :src="getImgUrl(img)" />
              </div>
            </template>
          </div>
        </div>
      </a-card>

      <!-- 算法编程 -->
      <a-card class="check-desc-card" v-if="data.codeQuestions.length">
        <p>算法题</p>
        <div class="desc-question" v-for="(item, index) in data.codeQuestions" :key="item.id">
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ index + 1 }}.
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <label class="get-score"
              >得分<span>{{ paperScore[item.id] }}</span>
            </label>
          </div>
          <a-textarea
            readonly
            :auto-size="{ minRows: 10 }"
            v-model:value="paperAnswerJson[item.id]['text']"
            placeholder="答题区"
          ></a-textarea>
          <!-- <a-textarea
            :rows="4"
            v-model:value="paperAnswerJson[item.id]['result']"
            disabled
            placeholder="代码执行结果"
          /> -->
        </div>
      </a-card>

      <!-- 复合题 -->
      <a-card class="check-desc-card" v-if="data.composeQuestions.length">
        <p>复合题</p>
        <div class="desc-question" v-for="(item, index) in data.composeQuestions" :key="item.id">
          <div class="desc-question-title">
            <span
              >{{ `【${item.score}分】` }}{{ alphaNum[index] }}、
              <span
                v-html="item.complicatedediting ? item.complexcontent : transferStr(item.body)"
              ></span>
            </span>
            <!-- <label class="get-score"
              >得分<span>{{ paperScore[item.id] }}</span>
            </label> -->
          </div>
          <div class="sub-ques-wrapper">
            <template v-for="(subitem, indey) in item.subtopic" :key="subitem.id">
              <!-- 单选 -->
              <template v-if="subitem.type === 0">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <a-radio-group v-model:value="paperAnswerJson[subitem.id]['text']">
                    <a-radio
                      v-for="option in subitem.options"
                      disabled
                      :key="option.value"
                      :value="option.value"
                      :style="radioStyle"
                    >
                      <span
                        :style="[
                          subitem.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : ''
                        ]"
                        v-html="
                          subitem.complicatedediting ? option.content : transferStr(option.content)
                        "
                      ></span>
                    </a-radio>
                  </a-radio-group>
                </div>
              </template>
              <!-- 多选 -->
              <template v-if="subitem.type === 1">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <a-checkbox-group v-model:value="paperAnswerJson[subitem.id]['text']">
                    <div
                      v-for="option in subitem.options"
                      :key="option.value"
                      :value="option.value"
                      class="checkbox-wrapper"
                    >
                      <a-checkbox disabled class="checkbox-style" :value="option.value">
                        <span
                          :style="[
                            subitem.answer.indexOf(option.value) > -1
                              ? { color: 'rgba(0, 255, 0, 0.8)' }
                              : ''
                          ]"
                          v-html="
                            subitem.complicatedediting
                              ? option.content
                              : transferStr(option.content)
                          "
                        ></span>
                      </a-checkbox>
                    </div>
                  </a-checkbox-group>
                </div>
              </template>
              <!-- 判断 -->
              <template v-if="subitem.type === 2">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <a-radio-group v-model:value="paperAnswerJson[subitem.id]['text']">
                    <a-radio
                      v-for="option in subitem.options"
                      disabled
                      :key="option.value"
                      :value="option.value"
                      :style="radioStyle"
                    >
                      <span
                        :style="[
                          subitem.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : ''
                        ]"
                        v-html="
                          subitem.complicatedediting ? option.content : transferStr(option.content)
                        "
                      ></span>
                    </a-radio>
                  </a-radio-group>
                </div>
              </template>
              <!-- 排序 -->
              <template v-if="subitem.type === 6">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <a-radio-group v-model:value="paperAnswerJson[subitem.id]['text']">
                    <a-radio
                      v-for="option in subitem.options"
                      disabled
                      :key="option.value"
                      :value="option.value"
                      :style="radioStyle"
                    >
                      <span
                        :style="[
                          subitem.answer === option.value ? { color: 'rgba(0, 255, 0, 0.8)' } : ''
                        ]"
                        v-html="
                          subitem.complicatedediting ? option.content : transferStr(option.content)
                        "
                      ></span>
                    </a-radio>
                  </a-radio-group>
                </div>
              </template>
              <!-- 填空 -->
              <template v-if="subitem.type === 5">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <a-input
                    readonly
                    class="fill-blank-input"
                    v-model:value="paperAnswerJson[subitem.id]['text']"
                    placeholder="答题区"
                  ></a-input>
                </div>
              </template>
              <!-- 问答 -->
              <template v-if="subitem.type === 3">
                <div class="subitem">
                  <div class="desc-question-title">
                    <span
                      >{{ `【${subitem.score}分】` }}{{ indey + 1 }}.
                      <span
                        v-html="
                          subitem.complicatedediting
                            ? subitem.complexcontent
                            : transferStr(subitem.body)
                        "
                      ></span>
                    </span>
                    <label class="get-score"
                      >得分
                      <span>{{ paperScore[subitem.id] }}</span>
                    </label>
                  </div>
                  <span
                    style="
                      position: absolute;
                      right: 40%;
                      transform: rotate(-30deg);
                      font-size: 30px;
                      z-index: 9999;
                      color: #3158bd;
                    "
                    :style="[
                      paperScore[subitem.id]
                        ? { color: '#3158BD' }
                        : { color: 'rgba(255, 0, 0, 0.5)' }
                    ]"
                    >{{ paperScore[subitem.id] ? '正确' : '错误' }}
                  </span>
                  <div
                    class="desc-input-wrapper"
                    v-html="paperAnswerJson[subitem.id]['text']"
                    placeholder="答题区"
                  ></div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </a-card>

      <a-button class="back-to-top" @click="backTop">回到顶部</a-button>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { getPaperInfo, scoredetail } from '@/api/admin/paperManage'
import { QuestionModel } from '@/models/questionModel'
import { message } from 'ant-design-vue'
import { ref, reactive, onMounted, watchEffect, watch } from 'vue'
import { transferStr } from '@/utils/common'

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  paper: String,
  student: String,
  answer: {
    type: Array,
    default: () => []
  },
  username: String,
  paperName: String
})

const emits = defineEmits(['submit', 'close'])

const alphaNum = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
const data = reactive({
  title: '',
  count: 0,
  totalScore: 0,
  singleSelectQuestions: <any>[],
  multiSelectQuestions: <any>[],
  trueOrFalseQuestions: <any>[],
  sortQuestions: <any>[],
  descQuestions: <any>[],
  codeQuestions: <any>[],
  fillBlankQuestions: <any>[],
  composeQuestions: <any>[]
})

const paperAnswer = reactive([])
const paperAnswerJson = reactive({})
const paperScore = reactive({})

// 是否显示
let visible = ref(false)

const radioStyle = reactive({
  display: 'block',
  paddingLeft: '10px',
  lineHeight: '30px'
})

const highLightKeywords = (textContent: string, keywordstring: string) => {
  const keywords = keywordstring.replaceAll(/[|\n]/gi, ',').slice(0, -1)
  const keywordsArray = keywords.split(',')
  keywordsArray.forEach((item) => {
    if (textContent.includes(item)) {
      textContent = textContent.replaceAll(
        item,
        '<b style="color:orange!important;">' + item + '</b>'
      )
    }
  })
  return textContent
}

// 找最大连续子串
const findMaxContiguousIndex = (arr1, arr2) => {
  if (!arr1 || arr1.length === 0) {
    return 0
  }

  const m = arr1.length
  const n = arr2.length
  const dp = new Array(m + 1).fill(0).map(() => new Array(n + 1).fill(0))
  let max_i = -1
  let max_len = -1
  const result = []

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (arr1[i - 1] === arr2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1
        if (dp[i][j] >= max_len) {
          max_len = dp[i][j]
          max_i = i
          if (arr1.slice(max_i - max_len, max_i).length >= 3) {
            result.push(...arr1.slice(max_i - max_len, max_i))
          }
        }
      } else {
        dp[i][j] = 0
      }
    }
  }
  return max_len === -1 ? [] : result
}

const getQATextAnswer = (answer: string = '') => {
  const index = answer?.indexOf('#@$')
  if (typeof index === 'number' && index !== -1) {
    answer = answer.substring(0, index)
  }
  return answer
}

const getAnswerImgs = (sAnswer: string) => {
  const index = sAnswer.indexOf('#@$')
  if (index === -1) {
    return []
  } else {
    const answerImgsString = sAnswer.split('#@$')[1]
    console.log(answerImgsString.split(','))
    return answerImgsString.split(',')
  }
}

const getImgUrl = (item: string) => {
  return 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/' + item
}

// watchEffect(() => {
//   visible.value = props.isVisible
//   Object.assign(paperAnswer, props.answer)
// })
watch(
  () => props.isVisible,
  () => {
    visible.value = props.isVisible
    Object.assign(paperAnswer, props.answer)
  },
  {
    immediate: true
  }
)

const handleSave = () => {
  const scoreList = Object.values(paperScore)
  console.log(scoreList.length, data.count)
  if (scoreList.length < data.count) {
    message.warning('请给出所有试题的得分！')
    return
  }
  // scoreList.forEach((item) => {
  //   data.totalScore += parseInt(item as string)
  // })
  emits('submit', { score: paperScore, paper: props.paperId })
}

const handleCancel = () => {
  emits('close')
}

let content: any
const backTop = () => {
  content.scrollTop = 0
  document.documentElement.scrollTop = 0
}

onMounted(() => {
  content = document.querySelector('.ant-modal-content')
  const backToTopButton = document.querySelector('.back-to-top')
  content.addEventListener('scroll', () => {
    if (content.scrollTop > 100 || document.documentElement.scrollTop > 100) {
      backToTopButton.style.display = 'block'
    } else {
      backToTopButton.style.display = 'none'
    }
  })
})

watch(
  () => props.student,
  (val) => {
    scoredetail({ paper: props.paper, student: props.student }).then((res: any) => {
      data.count = res.length

      data.singleSelectQuestions = res.filter((item: QuestionModel) => item.type === 0)
      data.singleSelectQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer
      })

      data.multiSelectQuestions = res.filter((item: QuestionModel) => item.type === 1)
      data.multiSelectQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = (item.stuanswer && item.stuanswer.split(',')) || []
        // const id = item.id
        // const currentQuestion: any = paperAnswer.find((item: any) => item.ques === id)
        // paperAnswerJson[id] = {}
        // if (!!currentQuestion) {
        //   paperScore[id] = currentQuestion.score
        //   paperAnswerJson[id]['text'] = currentQuestion.answer
        // } else {
        //   paperScore[id] = 0
        // }
      })

      data.trueOrFalseQuestions = res.filter((item: QuestionModel) => item.type === 2)
      data.trueOrFalseQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer
      })
      data.sortQuestions = res.filter((item: QuestionModel) => item.type === 6)
      data.sortQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer

        if (item.stuanswer) {
          const stuAnswerArray = item.stuanswer.split(',')
          const options = []
          for (let i = 0; i < stuAnswerArray.length; i++) {
            options.push(item.options.find((o: any) => o.value === stuAnswerArray[i]))
          }
          item.options = options
        }
      })

      data.descQuestions = res.filter((item: QuestionModel) => item.type === 3)
      data.descQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer
      })

      data.codeQuestions = res.filter((item: QuestionModel) => item.type === 4)
      data.codeQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer
      })
      data.fillBlankQuestions = res.filter((item: QuestionModel) => item.type === 5)
      data.fillBlankQuestions.forEach((item: any) => {
        paperScore[item.id] = item.stuscore || 0
        paperAnswerJson[item.id] = {}
        paperAnswerJson[item.id]['text'] = item.stuanswer
      })

      // 复合题
      data.composeQuestions = res.filter((item: QuestionModel) => item.type === 7)
      data.composeQuestions.forEach((item) => {
        item.subtopic.forEach((subItem) => {
          paperScore[subItem.id] = subItem.stuscore || 0
          paperAnswerJson[subItem.id] = {}
          if (subItem.type === 1) {
            paperAnswerJson[subItem.id]['text'] = subItem.stuanswer.split(',')
            console.log(paperAnswerJson[subItem.id]['text'])
          } else if (subItem.type === 6) {
            if (subItem.stuanswer) {
              const stuAnswerArray = subItem.stuanswer.split(',')
              const options = []
              for (let i = 0; i < stuAnswerArray.length; i++) {
                options.push(subItem.options.find((o: any) => o.value === stuAnswerArray[i]))
              }
              subItem.options = options
            }
          } else {
            paperAnswerJson[subItem.id]['text'] = subItem.stuanswer
          }
        })
      })
    })
  },
  {
    immediate: true
  }
)
</script>

<style lang="less">
.ant-card.check-desc-card {
  width: 60%;
  margin: 10px auto;
  .ant-card-body {
    > p {
      font-size: 16px;
      color: #121633;
      font-weight: bold;
      line-height: 30px;
    }
  }
  .desc-question {
    padding: 20px;
    position: relative;
    .desc-question-title {
      // line-height: 2;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      > span {
        display: flex;
        align-items: baseline;
        > span:last-child {
          margin-left: 10px;
        }
      }
      span {
        flex: 1;
        // flex-grow: 1;
        // margin-right: 10%;
        font-size: 15px;
        color: #121633;
      }
      .get-score {
        width: 100px;
        font-weight: bold;
        text-align: right;
      }
    }
  }
  .answer-imgs {
    margin-top: 16px;
    display: flex;
    .item {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 8px;
      .ant-image {
        height: 100%;
        width: 100%;
      }
      .ant-image-img {
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
.check-full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-header {
    background-color: #fff;
    position: fixed;
    width: 100%;
    z-index: 10000;

    text-align: center;
    .ant-modal-title {
      font-weight: bold;
      color: #121633;
      height: 50px !important;
      line-height: 50px;
    }
  }
  .stu-username {
    position: fixed;
    z-index: 10001;
    width: 100%;
    text-align: center;
    top: 60px;
    right: 10px;
  }
  .ant-modal-footer {
    position: fixed;
    width: 100%;
    padding: 24px 0;
    background: #fff;
    z-index: 9999;
    bottom: 0;
    text-align: center;
    .ant-btn {
      width: 102px;
      height: 36px;
      &:first-child {
        margin-right: 10px;
        color: #3158bd;
        border-color: #3158bd;
      }
    }
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    overflow: auto;
    .ant-modal-close {
      position: fixed;
      z-index: 10001;
      right: 10px;
    }
  }
  .ant-modal-body {
    flex: 1;
    padding-top: 80px;
  }
}
.checkbox-wrapper {
  display: flex;
  padding-left: 10px;
}
.checkbox-style {
  line-height: 30px;
}
.fill-blank-input {
  border-top: 0;
  border-left: 0;
  border-right: 0;
  &:focus {
    box-shadow: none;
  }
}
.desc-input-wrapper {
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 150px !important;
}
.sub-ques-wrapper {
  .subitem {
    margin-bottom: 24px;
  }
}
.back-to-top {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 40px;
  z-index: 9999;
  font-size: 14px;
  border-radius: 8px;
  cursor: pointer;
}
</style>

<style lang="less" scoped>
.check-desc-card {
  :deep(.ant-radio-wrapper) {
    display: flex !important;
    margin-top: 8px;
  }
  .sort-options {
    margin-bottom: 16px;
    .item {
      display: flex;
      min-height: 34px;
      padding-left: 8px;
      align-items: center;
      border: 1px solid #e8e8e8;
      color: #121633;
      border-radius: 2px;
      margin-bottom: 8px;
    }
  }
}
</style>
