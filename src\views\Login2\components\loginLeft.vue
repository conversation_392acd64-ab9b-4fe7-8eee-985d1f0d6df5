<template>
  <div class="content-left">
    <div class="tab">
      <div class="tab-item" :class="{ active: loginType === 'email' }" @click="changeLoginType('email')">邮箱登录</div>
      <!-- <div class="tab-item" :class="{ active: loginType === 'phone' }" @click="changeLoginType('phone')">手机登录</div> -->
    </div>
    <a-form class="inputbox" :model="formState" :rules="rules" ref="formRef">
      <template v-if="loginType === 'email'">
        <a-form-item name="email">
          <a-input class="inp" v-model:value.trim="formState.email" placeholder="邮箱">
            <template #prefix>
              <svg-icon style="width: 24px; height: 24px" name="account" />
            </template>
          </a-input>
        </a-form-item>
  
        <a-form-item ref="password" name="password">
          <a-input-password
            class="inp pwd"
            v-model:value.trim="formState.password"
            placeholder="密码"
            @keyup.enter="login">
            <template #prefix>
              <svg-icon style="width: 24px; height: 24px" name="pwd" />
            </template>
          </a-input-password>
        </a-form-item>
  
        <a-form-item name="code" v-if="loginFail">
          <div class="image-code-wrapper">
            <a-input
              class="inp"
              placeholder="输入验证码"
              v-model:value.trim="formState.code"
            />
            <div class="identify">
              <img :src="varifyCodeImg" style="height: 36px" />
              <p @click="getVarifyCode">看不清？换一张</p>
            </div>
          </div>
        </a-form-item>
        <div class="edit">
          <a-checkbox v-model:checked="isAutoLogin">30天内自动登录</a-checkbox>
          <span class="forgetpwd" @click="toForgetPwd">忘记密码</span>
        </div>
      </template>
      <template v-else>
        <a-form-item name="phone">
          <a-input class="inp" v-model:value.trim="formState.phone" placeholder="手机号">
            <template #prefix>
              <svg-icon style="width: 24px; height: 24px" name="phone2" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="smscode">
          <a-input class="inp" v-model:value.trim="formState.smscode" placeholder="验证码">
            <template #prefix>
              <svg-icon style="width: 24px; height: 24px" name="smscode" />
            </template>
            <template #suffix>
              <div style="display: flex;align-items: center;">
                <div style="width: 1px; height: 16px; background-color: rgba(0,0,0,0.25);margin-right: 11px;"></div>
                <div style="line-height: 14px;font-size: 16px;width: 80px;">
                  <span v-if="countdown <= 0" style="color: #5478ee;cursor: pointer;" @click="sendSmsCode">发送验证码</span>
                  <span v-else style="width: 100%;display: block;text-align: center;">{{ countdown }}s</span>
                </div>
              </div>
            </template>
          </a-input>
        </a-form-item>
      </template>
      <a-button class="inp logbtn" :loading="loading" type="primary" @click="loginType === 'email' ? login() : phoneLogin()">登录</a-button>
      <div class="agree-rules flex items-center">
        <a-checkbox v-model:checked="isAgreement" class="agree" />
        <p class="pb-[1px] tracking-[.8px]">
          我已阅读并同意<span class="terms text-[#1677ff] cursor-pointer" @click="toService">法律声明</span><span>和</span><span class="terms text-[#1677ff] cursor-pointer" @click="toPrivacy">隐私保护声明</span>
        </p>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { reactive } from 'vue'
import { message } from 'ant-design-vue'
import store from '@/store'
import { checkEmail, checkPhone } from '@/utils/validate'
import { smslogincode, varifyCode,valideCode } from '@/api/login'
import { useIntervalFn, useStorage } from '@vueuse/core'

const formRef = ref()
const router = useRouter()
const route = useRoute()
const isAgreement = ref(true)
const loginType = ref<'email' | 'phone'>('email')
 function toService() {
  const routeUrl = router.resolve({
    path: '/register/service',
  })
  window.open(routeUrl.href, '_blank')
}
function toPrivacy() {
  const routeUrl = router.resolve({
    path: '/register/privacy-policy',
  })
  window.open(routeUrl.href, '_blank')
}
function changeLoginType(type: 'email' | 'phone') {
  loginType.value = type
}

const formState = reactive<{
  email: string
  password: string
  roles: string
  code: string
  validDays?: number

  phone?: string
  /** 手机验证码 */
  smscode?: string
}>({
  email: (route.query.email as unknown as string)?.trim() ?? '',
  password: '',
  roles: 'teacher',
  code: '',
  phone: '',
  smscode: ''
})

const rules = {
  phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
  email: [{ required: true, validator: checkEmail(), trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  smscode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
}

// 验证码
const varifyCodeImg = ref('')
const getVarifyCode = async () => {
  try {
    const data: any = await varifyCode({ action: 'query' })
    console.log(data)
    varifyCodeImg.value = data.captchaUrl
  } catch (error) {
    varifyCodeImg.value = ''
  }
}

const loading = ref(false)
const loginFail = ref<string | null>(null)
onMounted(async () => {
  const cacheLoginFail = sessionStorage.getItem('loginFail')
  loginFail.value = cacheLoginFail
  if (cacheLoginFail) { // 获取图形验证码
    getVarifyCode()
  }
})

// 手机号登录
const countdown = useStorage('tlzk-teacher-countdown', 0)
const { pause, resume } = useIntervalFn(() => {
  if (countdown.value > 0) {
    countdown.value--
  } else {
    pause()
  }
}, 1000)
const sendSmsCodeLoading = ref(false)
async function sendSmsCode() {
  if (sendSmsCodeLoading.value) return 
  try {
    await checkPhone(null, formState.phone)
  } catch (error: any) {
    message.error(error)
    return
  }
  try {
    sendSmsCodeLoading.value = true
    await smslogincode({
      phone: formState.phone!,
    })
    countdown.value = 60
    resume()
    message.success("发送成功");
  } catch (error) {
    
  } finally {
    sendSmsCodeLoading.value = false
  }
}

// 登录
async function login() {
  await formRef.value.validate()
  if(!isAgreement.value) return message.warning('请阅读并同意法律声明和隐私保护声明')
  try {
    loading.value = true
    if (loginFail.value) { // 验证图形验证码
      await valideCode({ captcha: formState.code })
    }
    if (isAutoLogin.value) formState.validDays = 30
    await store.dispatch('USER_LOGIN', formState)
    message.success('登录成功！')
    router.push({
      name: 'questionManage'
    })
  } catch (error) {
    getVarifyCode()
    loginFail.value = 'true'
    sessionStorage.setItem('loginFail', 'true')
  } finally {
    loading.value = false
  }
}

async function phoneLogin() {
  await formRef.value.validate()
  if(!isAgreement.value) return message.warning('请阅读并同意法律声明和隐私保护声明。')
  try {
    loading.value = true
    await store.dispatch('USER_POHNE_LOGIN', {
      phone: formState.phone,
      code: formState.smscode,
    })
    message.success('登录成功！')
    router.push('/admin')
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 忘记密码跳转
function toForgetPwd() {
  router.push({
    name: 'forgetPwd',
    params: {
      email: formState.email
    }
  })
}

// 自动登录
const isAutoLogin = ref(false)

</script>

<style lang="less" scoped>
.content-left {
  padding: 64px 0;
  position: relative;
  .tab {
    height: 32px;
    line-height: 32px;
    margin-bottom: 56px;
    display: flex;
    gap: 48px;
    .tab-item {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.65);
      position: relative;
      cursor: pointer;
      &.active {
        font-weight: 700;
        color: rgba(0, 0, 0, 0.85);
        &::after {
          content: '';
          display: inline-block;
          position: absolute;
          bottom: -8px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #5478ee;
        }
      }
    }
  }
  .inputbox {
    width: 100%;
    .inp {
      width: 372px;
      height: 52px;
      border-radius: 5px;
      font-size: 16px;
      &:-webkit-autofill::first-line {
        font-size: 16px;
      }
      &.pwd {
        margin-top: 20px;
      }
    }
    .err {
      position: absolute;
      top: 240px;
      left: 0;
      p {
        position: absolute;
        left: 19px;
        top: 0;
        width: 120px;
        height: 18px;
        font-size: 12px;
        color: #e65b56;
        line-height: 18px;
      }
    }
    .edit {
      padding-top: 6px;
      text-align: right;
      display: flex;
      justify-content: space-between;
      .ant-checkbox-wrapper {
        font-size: 14px;
      }
      .forgetpwd {
        font-size: 14px;
        cursor: pointer;
      }
    }

    .image-code-wrapper {
      width: 372px;
      height: 52px;
      margin-top: 20px;
      background: #ffffff;
      display: flex;
      p {
        font-family: PingFang-SC-Medium;
        font-size: 12px;
        color: #999999;
        font-weight: 500;
        text-align: center;
        cursor: pointer;
      }
      .form-input {
        height: 52px;
        line-height: 52px;
        font-size: 16px;
        font-family: PingFang SC, PingFang SC-5;
        font-weight: 400;
        text-align: left;
        border: 0.5px solid rgba(37, 43, 58, 0.5);
        border-radius: 8px;
      }
    }

    .logbtn {
      height: 48px;
      margin-top: 24px;
      color: white;
    }
  }
}
.agree-rules {
        margin-top: 32px;
        display: flex;
        p {
          margin-left: 4px;
          font-size: 12px;
          font-family: PingFang SC, PingFang SC-5;
          font-weight: 400;
          text-align: left;
          color: rgb(37, 43, 58);
          line-height: 22px;
          a {
            color: #5478ee;
            font-weight: 400;
          }
        }
}
</style>
