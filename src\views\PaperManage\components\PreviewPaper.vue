<template>
  <div class="preview-paper" ref="previewPaperRef">
    <a-modal
      v-if="visible"
      v-model:visible="visible"
      width="auto"
      height="90vh"
      wrapClassName="full-modal"
      :getContainer="() => $refs.previewPaperRef"
      :footer="null"
      :centered="true"
      @cancel="closePreviewPaper"
    >
      <template #title>
        <span v-if="targetIsVisible">查看试卷</span>
        <span v-else class="left-top-title" :title="data?.name + (data?.username ? '-' + data?.username : '')">
          {{ data?.name + (data?.username ? '-' + data?.username : '') }}
        </span>
        <div v-if="!print && props.studentId" style="float: right;margin-right: 50px;">只看错题 <a-switch v-model:checked="onlyShowWrong"></a-switch></div>
      </template>
      <div class="preview-content">
        <div id="preview-paper">
          <div ref="targetRef" class="header">
            <div class="paper-name">{{ data?.name }}</div>
            <div v-if="totalScore" style="margin-bottom: 8px;">
              总分: {{ totalScore }}分
              <span v-if="print">考试时间: {{ data.duration }}分钟</span>
            </div>
            <div v-if="print" class="stu-info">
              <span>考生姓名:______________</span><span>编号:_______________</span>
            </div>
            <div v-if="data?.username" class="paper-score">
              <span class="user-name" :title="data.username">考生姓名: {{ data.username }}</span>
            </div>
          </div>
          <div class="ai-box" v-if="analyzable">
            <a-button class="common-ai-button" :disabled="studentPaperAnalysisStr" :loading="studentAnalysisLoading" @click="handleAnalysisStudent">
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;" />
              分析
            </a-button>
            <div class="ai-text-panel" v-if="studentPaperAnalysisStr">
              <FoldText 
                :text="studentPaperAnalysisStr" 
                :fold-line-clamp="1" 
                background-color="#f5f5f5"
                :feedback-params="{ 
                  apiname: 'aistudentpaperanalysis',
                  input: { studentId, paperId: data.id },
                  output: studentPaperAnalysisStr
                }"
              ></FoldText>
            </div>
          </div>
          <div class="main">
            <template v-for="(section, index) in questionList" :key="index">
              <div v-if="!onlyShowWrong || section.children.some((i: any) => i.stuscore < i.score)" class="question-section" :class="{ transparent: print }">
                <div class="question-typeinfo">
                  <span class="title"
                    ><span v-if="print">{{ numberToChinese(index + 1) }}、</span
                    >{{ section.title }}</span
                  >
                  <span class="score-info">
                    (<span v-if="section.options">
                      <span style="color: #ff4d4f">任选{{ section.options }}道作答</span>，共{{
                        section.options * section.children[0][scoreKey]
                      }}分</span
                    >
                    <span v-else
                      >共{{ section.children.length }}题，共{{
                        section.children.reduce(
                          (prev: any, current: any) => prev + current[scoreKey],
                          0
                        )
                      }}分</span
                    >)
                  </span>
                </div>
                <template v-for="(question, indey) in section.children" :key="question.id">
                  <div class="item-question" v-if="!onlyShowWrong || question.stuscore < question.score">
                    <QuestionItemDisplay
                      :question-detail="question"
                      v-bind="paperConfig"
                      :score-key="scoreKey"
                      :print="print"
                      :student-id="props.studentId"
                      :paper-id="props.data.id"
                      :scoreEditable="scoreEditable"
                      @edit-score="onScoreEdit"
                      :key="Date.now()"
                    >
                      <template #serialNumber>
                        <span class="question-number">
                          <span
                            v-if="section.options"
                            style="color: #ff4d4f; position: relative; top: 1px; margin-right: 4px"
                            >*</span
                          >
                          {{ getQuestionNumber(index, indey) }}.&nbsp;
                        </span>
                      </template>
                    </QuestionItemDisplay>
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="tool-bar">
        <a-button type="primary" v-if="props.showConfig" @click="handleViewPaperConfig">查看配置</a-button>
        <a-button v-if="print" type="primary" :loading="printLoading" v-print="printObj"
          >打印试卷</a-button
        >
      </div>
      <a-back-top
        :target="getBackToTopContainer"
        :visibilityHeight="120"
        style="position: absolute;right: -72px; bottom: 50px"
      >
        <img src="@/assets/images/back_to_top.png" alt="" />
      </a-back-top>
    </a-modal>
    <PaperConfig :visible="paperConfigVisible" :data="props.data" @close="closePaperConfigDialog" />
    <!-- <CustomDialog
      :dialogVisible="paperConfigVisible"
      :maskClosable="false"
      :showCancelBtn="false"
      :txtConfig="{ title: `《${data?.name}》考试配置`, confirm: '关闭' }"
      width="650px"
      @updateDialog="closePaperConfigDialog"
      @closeDialog="closePaperConfigDialog"
      @cancelDialog="closePaperConfigDialog"
    >
      <div style="height: 500px; overflow: auto">
        <PaperExamConfig v-if="paperConfigVisible" preview />
      </div>
    </CustomDialog> -->
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { scoredetail } from '@/api/admin/paperManage'
import { numberToChinese } from '@/utils/index'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
// import CustomDialog from '@/components/CustomDialog.vue'
// import PaperExamConfig from './PaperExamConfig.vue'
import PaperConfig from '@/pages/paperManage/PaperConfig.vue'
import { useIntersectionObserver } from '@vueuse/core'
import _ from 'lodash'
import { aistudentpaperanalysis } from '@/api/exam'
import { DoubleRightOutlined, DoubleLeftOutlined } from '@ant-design/icons-vue'
import FoldText from '@/components/FoldText.vue'

const props = withDefaults(
  defineProps<{
    isVisible: boolean
    studentId?: string
    data: any
    print?: boolean
    scoreKey?: 'score' | 'new_score'
    showConfig?: boolean // 是否显示查看配置按钮
    studentPaperAnalysisStr?: string
    analyzable?: boolean
    scoreEditable?: boolean
  }>(),
  {
    isVisible: false,
    studentId: '',
    data: () => ({}),
    print: false,
    scoreKey: 'score',
    showConfig: false,
    analyzable: false,
    scoreEditable: false
  }
)

const paperConfig = {
  showScore: true,
  showType: false,
  showTag: false,
  showCorrectAnswer: false,
  showStudentAnswer: false,
  showStudentScore: false,
  showQuestionDifficulty: false,
  showQuestionPointsAndBasis: false
}

// const emits = defineEmits(['closePreviewPaper', 'update:studentPaperAnalysisStr'])
const emits = defineEmits<{
  (e: 'closePreviewPaper', needRefresh: boolean): void
  (e: 'update:studentPaperAnalysisStr', v: string): void
}>()

// 总分
const totalScore = ref(props.data.score ?? 0)
watch(() => props.data.score, (val) => {
  totalScore.value = val
})

// 获取题号
const getQuestionNumber = (index: number, indey: number) => {
  let sum = 0
  for (let i = 0; i < index; i++) {
    sum += questionList.value[i].children.length
  }

  return sum + indey + 1
}

function getBackToTopContainer() {
  return document.querySelector('.full-modal .ant-modal-body .preview-content')
}

// 打印相关
const printLoading = ref(false)
const printObj = {
  // 要打印的区域
  id: 'preview-paper',
  // 打印的标题
  popTitle: props.data.name,
  extraHead: `<title>${props.data.name}</title>`,
  // 打印前回调
  beforeOpenCallback() {
    document.title = props.data.name
    printLoading.value = true
  },
  // 执行打印回调
  openCallback() {
    printLoading.value = false
  },
  closeCallback() {
    document.title = '图灵智面教师系统'
  }
}

let visible = ref()

const closePreviewPaper = () => {
  emits('closePreviewPaper', hasScoreEdit.value)
}

// 是否只看错题
const onlyShowWrong = ref(false)

const questionList = ref<any>([])
watch([() => props.isVisible, onlyShowWrong],
  () => {
    visible.value = props.isVisible
    if (visible.value) {
      questionList.value = props.data.fbody
      if (props.data.id && props.studentId) {
        getStuScoreDetail()
      }
    }
  }
)

// 如果是查看成绩，需要调用接口
const rawScoreDetail = ref()
async function getStuScoreDetail() {
  let fbody = await scoredetail({ templeteId: props.data.id, studentId: props.studentId })
  rawScoreDetail.value = fbody
  questionList.value = _.cloneDeep(fbody)
  console.log('questionList.value', questionList)
  paperConfig.showStudentScore = true
  paperConfig.showCorrectAnswer = true
  // // 重新计算总分
  // totalScore.value = fbody.reduce((prev: any, current: any) => {
  //   return prev + current.children.reduce((prev: any, current: any) => {
  //     return prev + current.stuscore
  //   }, 0)
  // }, 0)
}

// 查看配置
const paperConfigVisible = ref(false)
const handleViewPaperConfig = () => {
  // store.commit('SET_PAPER_INFO', props.paperInfo)
  paperConfigVisible.value = true
}
const closePaperConfigDialog = () => {
  paperConfigVisible.value = false
  // store.commit('CLEAR_PAPER_INFO')
}

// 处理滚动
const targetRef = ref()
const targetIsVisible = ref(false)

const { stop } = useIntersectionObserver(
  targetRef,
  ([{ isIntersecting }], observerElement) => {
    targetIsVisible.value = isIntersecting
  },
)

// ai分析
const studentAnalysisLoading = ref(false)
async function handleAnalysisStudent() {
  try {
    studentAnalysisLoading.value = true
    let res = await aistudentpaperanalysis({ studentId: props.studentId, templeteId: props.data.id })
    emits('update:studentPaperAnalysisStr', res)
  } catch (error) {
    console.log(error)
  } finally {
    studentAnalysisLoading.value = false
  }
}

// 改分
const hasScoreEdit = ref(false)
function onScoreEdit() {
  if (props.data.id && props.studentId) {
    hasScoreEdit.value = true
    getStuScoreDetail()
  }
}

</script>

<style lang="less" scoped>
.preview-paper {
  :deep(.full-modal) {
    overflow: unset;
    .ant-modal {
      .ant-modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .ant-modal-body {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0;

        .tool-bar {
          text-align: center;
          margin-top: 16px;
          .ant-btn {
            border-radius: 8px;
            font-size: 14px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}
.left-top-title {
  display: inline-block;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 25px;
}
.preview-content {
  flex: 1;
  min-height: 0;
  overflow: auto;
  border-radius: 8px;
  width: 974px;
  margin: 0 auto;
  padding-bottom: 8px;
  box-sizing: border-box;
  position: relative;
  #preview-paper {
    height: 100%;
    .common-ai-button {
      font-size: 12px;
      padding: 0 8px;
      margin-bottom: 16px;
      height: 32px;
    }
  }
}
.header {
  text-align: center;
  margin-bottom: 40px;
  .paper-name {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .paper-score {
    font-size: 16px;
    font-weight: bold;
    > span + span {
      margin-left: 24px;
    }
    .user-name {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .stu-info {
    display: flex;
    justify-content: center;
    span {
      margin-right: 16px;
      margin-top: 8px;
    }
  }
}
.ai-box {
  padding: 0 32px 16px 32px;
  margin-bottom: 16px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 1;

  .ai-text-panel {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 16px 24px;
  }
}
.main {
  padding: 0 32px;
  .question-section {
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 24px;
    &.transparent {
      border-color: transparent;
    }
    .question-typeinfo {
      margin-bottom: 24px;
      .title {
        font-size: 20px;
        font-weight: bold;
        padding-right: 8px;
      }
      .score-info {
        font-weight: bold;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .item-question {
    margin-bottom: 24px;
    .question-number {
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      line-height: 22px;
    }
  }
}
</style>
