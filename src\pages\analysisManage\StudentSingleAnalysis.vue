<template>
    <div class="analysis">
        <div class="common-page-title">
            <span @click="router.back()">考生分析 /</span>
            <span class="user-name" :title="basicInfo?.username"> {{ basicInfo?.username }} </span>
        </div>
        <a-spin :spinning="loading">
            <div class="analysis-content">
                <div style="height: 240px;">
                    <div class="panel">
                        <div class="panel-title">考生基本信息</div>
                        <a-row class="panel-content basic-info">
                            <a-col :span="12" style="display: flex;align-items: center;">
                                <div class="avatar">{{ basicInfo?.username.slice(-2) }}</div>
                                <div>
                                    <div class="stu-info">
                                        <span class="stu-name" :title="basicInfo?.username">{{ basicInfo?.username }}</span>
                                       <div class="info-wrapper">
                                            <span class="stu-tag">{{ basicInfo?.sex }}</span>
                                            <span class="stu-tag">{{ basicInfo?.age }}岁</span>
                                            <span style="color: rgba(0, 0, 0, 0.45);font-size: 12px;">{{
                                                basicInfo?.register_time?.slice(0, 7)?.replace('-', '.') ?? '未知时间' }}注册</span>
                                       </div>
                                        
                                    </div>
                                    <div style="color: rgba(0, 0, 0, 0.45);font-size: 16px; font-weight: Medium; margin: 8px 0 16px;">
                                        <span class="stu-school">{{ basicInfo?.school || '未知院校' }}</span>
                                        <a-divider type="vertical"></a-divider>
                                        <span class="stu-school">{{ basicInfo?.heducation }}</span>
                                    </div>
                                    <a-button type="primary" @click="showResume">查看简历</a-button>
                                </div>
                            </a-col>
                            <a-col :span="12">
                                <div class="basic-info-r">
                                    <div class="stu-info-item">
                                        <div class="label">手机号</div>
                                        <div class="value">{{ basicInfo?.phone }}</div>
                                    </div>
                                    <div class="stu-info-item">
                                        <div class="label">电子邮箱</div>
                                        <div class="value">{{ basicInfo?.email }}</div>
                                    </div>
                                    <div class="stu-info-item">
                                        <div class="label">报考单位</div>
                                        <div class="value">{{ basicInfo?.dept_name }}</div>
                                    </div>
                                    <div class="stu-info-item">
                                        <div class="label">备注</div>
                                        <div class="value">{{ basicInfo?.remark }}</div>
                                    </div>

                                </div>
                            </a-col>
                        </a-row>
                    </div>
                </div>
                <div style="flex: 1;min-height: 0;margin-top: 20px;">
                    <div class="panel">
                        <div class="panel-title">考生能力分析</div>
                        <div class="panel-content" style="overflow: auto;">
                            <div v-if="list?.length === 0" class="common-no-data"></div>
                            <StudentCapacityChart v-else :list="list"></StudentCapacityChart>
                        </div>
                        <p v-if="list?.length !== 0" style="font-size: 12px;color: rgba(0,0,0,0.45);text-align: center;padding-top: 10px;">平均得分率</p>
                    </div>
                </div>
            </div>
        </a-spin>
        <ResumeDetail 
            v-if="resumeDetailVisible" 
            :resume-detail-visible="resumeDetailVisible"
            :resume-detail-id="previewResumeId" 
            @closePreviewResume="resumeDetailVisible = false">
        </ResumeDetail>
    </div>
</template>

<script lang="ts" setup>
import router from '@/router'
import { useRoute } from 'vue-router'
import { ref } from 'vue'
import { studentreport } from '@/api/admin/statisticAnalysis'
import StudentCapacityChart from './components/StudentCapacityChart.vue'
import ResumeDetail from '@/views/ResumeManage/resumeDetail.vue'

type NodeType = {
    cal_data: number
    catg_name: string
    children: NodeType[]
}

const route = useRoute()

const basicInfo = ref<{
    age: number
    dept_name: string
    email: string
    heducation: string
    phone: string
    register_time: string
    remark: string | null
    school: string
    sex: string
    username: string
}>()
const list = ref<NodeType[]>()
const loading = ref(false)
async function getStudentreport() {
    loading.value = true
    try {
        let { base_info, meta_data } = await studentreport({ id: route.query.id }) as any
        basicInfo.value = base_info
        list.value = meta_data
    } finally {
        loading.value = false
    }
}
getStudentreport()

// 查看简历
const resumeDetailVisible = ref(false)
const previewResumeId = ref('')
function showResume() {
    previewResumeId.value = route.query.id as string
    resumeDetailVisible.value = true
}

</script>

<style lang="less" scoped>
.user-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: bottom;
}
.analysis {
    padding: 0 20px 20px 20px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .analysis-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0;

        .panel {
            background-color: #fff;
            height: 100%;
            padding: 20px;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            display: flex;
            flex-direction: column;

            .panel-title {
                font-size: 20px;
                margin-bottom: 10px;
            }

            .panel-content {
                flex: 1;
                min-height: 0;
                overflow: auto;
            }
        }
    }
}

.ant-spin-nested-loading {
    flex: 1;
    min-height: 0;

    :deep(.ant-spin-container) {
        height: 100%;
        display: flex;
    }
}

.basic-info {
    display: flex;
    padding: 0 50px;

    .avatar {
        width: 110px;
        height: 110px;
        border-radius: 50%;
        color: #5478ee;
        background: #f1f4fe;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-right: 50px;
    }

    .stu-info {
        display: flex;
        flex-direction: row;
        align-items: center;
         .stu-name {
            margin-right: 16px;
            padding-bottom: 5px;
            font-size: 24px;
            font-weight: bold;
            .user-name()
    }
        .info-wrapper{
            display: inline-block;
            .stu-tag {
                margin-right: 8px;
                color: #D96E00;
                background-color: #FAF4EE;
                font-size: 12px;
                padding: 2px 4px;
                border-radius: 4px;
        }
    }
}
    .stu-info-item {
        margin-top: 16px;
        display: flex;

        .label {
            width: 100px;
            color: #868484;
        }

        .value {
            color: #181818;
        }
    }

    .ant-btn {
        height: 32px;
        border-radius: 8px;
        font-size: 14px;
    }
}
.common-no-data {
    background-size: 320px;
}
</style>