<template>
  <a-form class="login-form" ref="formRef"
    :model="formState"
    :rules="rules"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
    :colon="false"
    hideRequiredMark
    >
    <a-tabs class="invitecode-tabs" v-model:activeKey="activeKey">
      <a-tab-pane key="0" tab="考试邀请码登录" />
    </a-tabs>
    <div class="user-container">
      <a-form-item  label="考试邀请码" name="invitecode">
        <a-input
          v-model:value="formState.invitecode"
          placeholder="请输入考试邀请码"
          autocomplete="off"
          class="login-input"
        >
        </a-input>
      </a-form-item>
      <a-form-item  label="姓名" name="name">
        <a-input
          v-model:value="formState.name"
          placeholder="请输入姓名"
          autocomplete="off"
          class="login-input"
        >
        </a-input>
      </a-form-item>
      <a-form-item  label="身份证号" name="idcard">
        <a-input
          v-model:value="formState.idcard"
          placeholder="请输入身份证号"
          autocomplete="off"
          class="login-input"
        >
        </a-input>
      </a-form-item>
      <a-form-item v-if="isregistered" label="密码" name="password">
        <a-input
          v-model:value="formState.password"
          :type="!passwdShow ? 'password' : ''"
          placeholder="请输入密码"
          autocomplete="off"
          class="login-input"
        >
          <template #prefix> <img src="@/assets/images/svg/icon_pwd.svg" alt="" /> </template>
          <template #suffix>
            <svg-icon v-if="!passwdShow" @click="showPasswd"
              style="margin-right: 10px;color: #999" name="invisible" />
            <eye-outlined v-else @click="showPasswd" style="margin-right: 10px; color: #999" />
          </template>
        </a-input>
      </a-form-item>
      <!-- <a-form-item v-if="!isregister" label="性别" name="sex">
        <a-select v-model:value="formState.sex" placeholder="请选择性别">
          <a-select-option value="male">男</a-select-option>
          <a-select-option value="female">女</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="!isregister"  label="手机号" name="phone">
        <a-input v-model:value="formState.phone" placeholder="请输入手机号" class="login-input">
        </a-input>
      </a-form-item>
      <a-form-item v-if="!isregister" label="邮箱" name="email">
        <a-input v-model:value="formState.email" placeholder="请输入邮箱" class="login-input">
        </a-input>
      </a-form-item>
      <a-form-item v-if="!isregister" label="附加信息" name="info">
        <a-input v-model:value="formState.info" placeholder="请输入附件信息" class="login-input">
        </a-input>
      </a-form-item> -->
    </div>
    <a-form-item>
      <div class="login-tool">
        <div class="invite-login">
          <svg-icon class="invite-icon" name="user" width="14px" height="14px" />
          <span @click="switchAccountLogin">账号密码登录</span>
        </div>
      </div>
    </a-form-item>
    <div class="join-exam">
      <a-button type="primary" @click="onSubmit" :loading="loading"
        ><span>参加考试</span></a-button
      >
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import {codelogin,codepwdlogin} from '@/api/login'
import { message } from 'ant-design-vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import store from '@/store'
// import router from '@/router'
import {checkCardNo} from '../validate'
import { useRouter } from 'vue-router'

const emits = defineEmits(['getRegisterStatus'])
const formRef = ref()
const loading = ref(false)
const labelCol =  { span: 8 }
const wrapperCol =  { span: 16 }
const passwdShow = ref(false)
const formState = reactive({
  invitecode: '',
  name: '',
  idcard: '',
  password: ''
})

const showPasswd = () => {
  passwdShow.value = !passwdShow.value
}

const rules = {
  invitecode: [{ required: true, message: '请输入考试邀请码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  idcard: [{ required: true, validator:checkCardNo, trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  // sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
  // phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  // email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  // info: [{ required: true, message: '请输入附加信息', trigger: 'blur' }]
}

const switchAccountLogin = () => {
  emits('getRegisterStatus', 0)
}

const router = useRouter()
const pwdlogin = ref(false) // 是否密码登录
const isregistered = ref(false)
const onSubmit = () => {
  formRef.value.validate()
  .then(() => {
    loading.value = true
    const params = {
      token: formState.invitecode,
      name: formState.name,
      idcard: formState.idcard
    }
    if(isregistered.value) {
      pwdlogin.value = true
      return codepwdlogin({...params,pwd:formState.password})
    } else {
      return codelogin(params)
    }
  })
  .then((res:any) => {
    loading.value = false
    if(pwdlogin.value) {
      const {access_token, refresh_token} = res
      store.commit('SET_ACCESS_TOKEN', access_token)
      store.commit('SET_REFRESH_TOKEN', refresh_token)
      setTimeout(()=>{
        router.push('/exam')
      },1000)
    } else {
      isregistered.value = res.registered
      if(res.registered) {
        message.info('学员已注册,请输入密码再次登录!')
      } else {
        message.info('尚未注册,请先完成注册,3s后将自动跳转!')
        setTimeout(() => {
          emits('getRegisterStatus', {status: 1,name: formState.invitecode,username: formState.name,idcard:formState.idcard})
        },3000)
      }
    }
  }).catch(() => {
    loading.value = false
  })
}
// watch(
//   () => formState.roles,
//   () => {
//     formRef.value.resetFields()
//   }
// )
</script>
<style lang="less" scoped>
.login-form {
  .user-container {
    padding: 35px 0 0;
  }
  .login-input {
    height: 40px;
    border-radius: 2px;
    margin-bottom: 6px;
    padding-left: 5px;
    background: #fafafa;
  }
  .ant-btn-primary {
    width: 100%;
  }
  .login-tool {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #999;
    .invite-login {
      display: flex;
      align-items: center;
      color: #3158BD;
      .invite-icon {
        position: relative;
        bottom: -1px;
      }
      span {
        font-size: 14px;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .register a {
      text-decoration: underline;
    }
    span {
      padding: 0 5px;
    }
    a {
      font-family: PingFang-SC-Medium;
      font-size: 14px;
      color: #999999;
      font-weight: 500;
    }
    a:hover {
      color: #3158BD;
    }
  }
  .join-exam {
    padding: 0 90px 20px;
    .ant-btn-primary {
      height: 40px;
    }
  }
  .invitecode-tabs {
    padding: 0 63px 0 6px;
  }
  .ant-form-item {
    padding: 0 90px;
  }
  margin-top:90px;
  border-radius: 4px;
  overflow: hidden;
}
</style>
<style lang="less">
.login-form {
  .ant-tabs-nav .ant-tabs-tab {
    padding: 30px 0px 19px;
    margin-right: 30px;
  }
  .ant-form-item-label > label {
    font-size: 15px;
    color: #121633;
  }
  .ant-input-affix-wrapper .ant-input-prefix {
    border-right: 1px solid #ddd;
    margin-right: 0;
    padding: 0 13px;
  }
  .ant-input-affix-wrapper > input.ant-input {
    &:focus {
      background-color: #fff;
    }
    padding-left: 10px;
    background: #fafafa;
  }
  input::-webkit-input-placeholder {
    font-size: 14px;
  }
  .ant-form-item-explain.ant-form-item-explain-error {
    text-align: right;
    color: #ff5040;
    div {
      font-size: 12px;
    }
  }
}
</style>
