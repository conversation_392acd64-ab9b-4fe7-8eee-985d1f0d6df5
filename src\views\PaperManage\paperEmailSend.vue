<template>
  <div class="paper-email-send-container">
    <h3 class="paper-email-send-title">
      <span style="color: rgba(0, 0, 0, 0.45)"><span @click="backtoExam">试卷管理 /</span></span
      ><span>发送邀请邮件《{{$route.query.name}}》</span>
    </h3>
    <div class="paper-email-send">
      <div class="paper-email-send-search">
        <a-input-search
          style="width: 240px;"
          v-model:value.trim="searchContent"
          placeholder="请输入考生姓名"
          allow-clear
          @search="getStuList"
        />

        <!-- <div class="email-send-status">
          <span class="label">发送状态: </span>
          <a-radio-group v-model:value="sendStatus" @change="getStuList">
            <a-radio :value="null">全部</a-radio>
            <a-radio :value="true">已发送</a-radio>
            <a-radio :value="false">未发送</a-radio>
          </a-radio-group>
        </div> -->

        <div class="email-send-btns" v-if="!(route.query.hiddenAction === 'true')">
          <a-button class="send-btn" type="primary" @click="sendEmail">发送邮件</a-button>
          <a-button class="send-btn" @click="editTemplate">编辑模板</a-button>
          <a-button class="send-btn" @click="addStudent">添加考生</a-button>
          <a-button class="add-btn" @click="delMulStuList">批量删除</a-button>
        </div>
      </div>

      <a-table
        :columns="data.columns"
        :rowKey="(record:any) => record.studentId"
        :data-source="data.stuList"
        :loading="data.loading"
        :row-selection="!(route.query.hiddenAction === 'true') ? {
          selectedRowKeys: selectedData,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
          getCheckboxProps: getCheckboxProps
        } : null"
        :scroll="{ x: 1200 }"
        :pagination="data.pagination"
        @change="handleTableChange"
        @resizeColumn="(w: any, col: any) => col.width = w"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'emailStatus'">
            <span>
              <span class="status-icon" :class="{ active: record.emailStatus }"></span>
              {{ record.emailStatus ? '已发送' : '未发送' }}
            </span>
          </template>
          <template v-else-if="column.key === 'updateTime'">
            <span>
              {{ record.emailStatus ? record.updateTime : '-' }}
            </span>
          </template>

          <template v-else-if="column.key === 'action'">
            <span>
              <a-popconfirm
                title="确定删除该考生？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="delStudents(record)"
              >
                <a-button type="link" style="font-size: 14px; color: #5478ee; padding-left: 0"
                  >删除</a-button
                >
              </a-popconfirm>
            </span>
          </template>
        </template>
      </a-table>
    </div>

    <custom-dialog
      :width="420"
      :dialogVisible="emailDialogVisible"
      :txtConfig="emailTxtConfig"
      :keyboard="false"
      :maskClosable="false"
      @updateDialog="updateEmailDialog"
      @closeDialog="closeEmailDialog"
    >
      <a-form
        class="email-form"
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        labelAlign="left"
        :colon="false"
        :hideRequiredMark="true"
      >
        <a-form-item label="邮件首段" name="substract">
          <a-textarea
            v-model:value="emailForm.substract"
            :auto-size="{ minRows: 4 }"
            show-count
            :maxlength="200"
          />
        </a-form-item>
      </a-form>
    </custom-dialog>

    <custom-dialog
      :width="420"
      :loading="addLoading"
      :dialogVisible="stuDialogVisible"
      :txtConfig="stuTxtConfig"
      :keyboard="false"
      :maskClosable="false"
      @updateDialog="updateStuDialog"
      @closeDialog="closeStuDialog"
      @cancelDialog="cancelStuDialog"
    >
      <a-form
        ref="stuFormRef"
        class="student-form"
        :model="studentForm"
        :rules="studentRules"
        labelAlign="left"
        :colon="false"
        hideRequiredMark
      >
        <a-form-item label="添加方式" name="more">
          <a-radio-group v-model:value="studentForm.more">
            <a-radio :value="false">单个添加</a-radio>
            <a-radio :value="true">批量添加</a-radio>
          </a-radio-group>
        </a-form-item>
        <div v-if="!studentForm.more">
          <a-form-item label="考生姓名" name="name">
            <a-input v-model:value.trim="studentForm.name" />
          </a-form-item>
          <a-form-item label="邮箱地址" name="email">
            <a-input v-model:value.trim="studentForm.email" />
          </a-form-item>
        </div>
        <div v-else class="multi-add-stu">
          <p class="label">请输入姓名（邮箱），每行一位</p>
          <a-form-item name="multiStuInfo">
            <a-textarea
              style="width: 100%"
              v-model:value="studentForm.multiStuInfo"
              :auto-size="{ minRows: 5, maxRows: 15 }"
              placeholder="示例：张三（<EMAIL>）"
            />
          </a-form-item>
        </div>
      </a-form>
    </custom-dialog>

    <!-- 预览模板 -->
    <custom-dialog
      :width="720"
      :dialogVisible="previewTemplateVisible"
      :txtConfig="previewTemplateConfig"
      :keyboard="false"
      :maskClosable="false"
      :loading="sendLoading"
      @updateDialog="updatePreviewTemplateDialog"
      @closeDialog="closePreviewTemplateDialog"
      @cancelDialog="cancelPreviewTemplateDialog"
    >
      <iframe ref="iframeRef" width="100%" height="600px" frameborder="0"></iframe>
    </custom-dialog>

    <!-- 编辑模板 -->
    <custom-dialog
      :dialogVisible="templateVisible"
      :txtConfig="templateConfig"
      :keyboard="false"
      :maskClosable="false"
      @updateDialog="updateTemplateDialog"
      @closeDialog="closeTemplateDialog"
      @cancelDialog="cancelTemplateDialog"
      centered
      width="60%"
      wrapClassName="template-modal"
    >
      <a-editor
        ref="editorRef"
        :content="templateContent"
        :isEmailTemplate="true"
        :exclude-menus="[
          'head',
          'todo',
          'quote',
          'code',
          'table',
          'emoticon',
          'video',
          'fontName',
          'strikeThrough',
          'lineHeight',
          'backColor',
          'undo',
          'redo',
          'indent',
          'link',
          'list',
          'justify',
          'fullscreen'
        ]"
        @getHtml="getHtml"
      />
      <template #extra-btn>
        <a-button :loading="resetLoading" @click="resetTemplate">恢复初始模板</a-button>
      </template>
    </custom-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import CustomDialog from '@/components/CustomDialog.vue'
import { examemail,examemailCreate , examemailDelete , sendpaperemail, examemailtemplate,updateExamemailtemplate } from '@/api/admin/paperManage'
import { message } from 'ant-design-vue'
import AEditor from '@/components/Editor/index.vue'
import dayjs from 'dayjs'
import { queryRelatedResume } from '@/api/admin/paperManage'
import { getstudept } from '@/api/admin/resumeManage'
import { useState } from '@/hooks'
const route = useRoute()
// 搜索关键词
const searchContent = ref('')
const otherParams = ref<{
  order_field?: string
  order_type?: 'ascend' | 'descend'
}>({})

// 模板内容
const templateContent = ref('')

// 发送状态
const sendStatus = ref(null)

const defaultColumns = [
  {
    title: '考生姓名',
    dataIndex: 'studentName',
    key: 'studentName',
    width: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: '考生邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 400,
    ellipsis: true,
    resizable: true
  },
  {
    title: '报考部门',
    dataIndex: 'deptName',
    key: 'deptName',
    width: 200,
    ellipsis: true,
    resizable: true
  },
  {
    title: '考生状态',
    dataIndex: 'associate',
    key: 'associate',
    width: 200,
    customRender({ text }: any) {
      return text ? '已发放试卷' : '未注册'
    },
    filters: [
        { text: '已发放试卷', value: 1 },
        { text: '未注册', value: 0 },
    ],
    ellipsis: true,
    resizable: true
  },
  {
    title: '发送状态',
    dataIndex: 'emailStatus',
    key: 'emailStatus',
    width: 200,
    filters: [
        { text: '已发送', value: 1 },
        { text: '未发送', value: 0 },
    ],
    ellipsis: true,
    resizable: true
  },
  {
    title: '发送时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 300,
    ellipsis: true,
    resizable: true,
    sorter: true
  },
  {
    title: '操作',
    fixed: 'right',
    key: 'action',
    width: 120
  }
]
const data = reactive({
  loading: false,
  stuList: [],
  columns: defaultColumns,
  pagination: {
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    total: 0,
    pageSizeOptions: ['10', '20', '50', '100'],
    size: 'small'
  }
})

watch(() => route.query.hiddenAction, (value) => {
  if (value === 'true') data.columns = defaultColumns.filter(el => el.key !== 'action')
  else data.columns = defaultColumns
}, {
  immediate: true,
})

// 获取报考部门列表
// let deptList = ref<any[]>([])
// async function getStudentDept() {
//   deptList.value = await getstudept() as any
// }
// getStudentDept().then(() => {
//   data.columns.find(item => item.title === '报考部门')!.filters = deptList.value
// })

const selectedData = ref<any[]>([])
const selectedDetailData = ref<any[]>([])
const onSelect = (record: any, selected: boolean, selectedRows: any) => {
  if (!selected) {
    // 取消勾选,删除对应的数组项
    selectedData.value.map((item: any, index: number) => {
      if (item === record.studentId) {
        selectedData.value.splice(index, 1)
      }
    })
    const indey = selectedDetailData.value.findIndex((item: any) => item.studentId === record.studentId)
    selectedDetailData.value.splice(indey, 1)
  }
  if (selected) {
    // 点击勾选,添加到selectedPaper数组
    selectedData.value.push(record.studentId)
    const data = selectedRows.find((item: any) => item && item.studentId === record.studentId)
    if (data) {
      selectedDetailData.value.push(data)
    }
  }

  console.log(selectedDetailData.value)
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedData.value.push(item.studentId)
      selectedDetailData.value.push(item)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedData.value.map((x: any, indey: number) => {
        if (item.studentId === x) {
          selectedData.value.splice(indey, 1)
        }
      })
      const indez = selectedDetailData.value.findIndex((q: any) => q.studentId === item.studentId)
      selectedDetailData.value.splice(indez, 1)
    })
  }
}
const getCheckboxProps = (record: any) => {
  return {
    selectedRowKeys: selectedData.value.length ? selectedData.value.includes(record.studentId) : []
  }
}

// 删除考生
const delStudents = (record: any) => {
  const ids = [record.studentId]
  delStuList(ids)
}
const delMulStuList = () => {
  if (!selectedData.value.length) {
    message.error('请勾选需要删除的考生')
    return
  }

  delStuList(selectedData.value)
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  // 处理分页
  data.pagination.current = pagination.current
  data.pagination.pageSize = pagination.pageSize
  // 处理排序
  otherParams.value.order_type = sorter.order
  otherParams.value.order_field = sorter.order ? sorter.field : undefined
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getStuList()
}

const [studentForm, resetStudentForm] = useState({
  more: false,
  name: '',
  email: '',
  multiStuInfo: ''
})
const emailForm = reactive({
  substract:
    '感谢报考中科院软件所，现邀请你参加在线考试，考试过程需全程开启摄像头，请提前准备具有摄像头的电脑，并且使用Edge浏览器或者最新版Chrome浏览器。 '
})

const stuFormRef = ref<any>()
const emailFormRef = ref<any>()

// 验证用户名
const checkUsername = async (rule: any, value: string) => {
  const isReg =
    /^[\u4e00-\u9fa5]|[a-zA-Z]$/.test(value) &&
    /^(?![·\-_\s])(?!.*[·\-_\s]$)/.test(value) &&
    /^[·\-\s\w\u4e00-\u9fa5]*$/.test(value) &&
    !/[0-9]/.test(value) &&
    !/[\u4e00-\u9fa5][a-zA-Z]{1}|[a-zA-Z][\u4e00-\u9fa5]{1}/.test(value) &&
    !/[·_\-\s]{2}/.test(value) &&
    !/[\u4e00-\u9fa5]\s[\u4e00-\u9fa5a]/.test(value) &&
    !/[a-z]\s{2,}[a-z]/i.test(value)
  if (value.trim() === '') {
    return Promise.reject('请输入真实姓名')
  } else if (!isReg) {
    return Promise.reject('真实姓名不合法')
  } else {
    return Promise.resolve()
  }
}

// 验证邮箱
const checkEmail = async (rule: any, value: string) => {
  const email = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (value === '') {
    return Promise.reject('请输入邮箱')
  } else if (value.length > 64) {
    return Promise.reject('邮箱地址超过最大长度')
  } else if (!email.test(value)) {
    return Promise.reject('邮箱格式不正确')
  } else {
    return Promise.resolve()
  }
}

const studentRules = {
  name: [{ required: true, validator: checkUsername, trigger: 'blur' }],
  email: [{ required: true, validator: checkEmail, trigger: 'blur' }],
  multiStuInfo: [{ required: true, message: '考生姓名(邮箱)不能为空', trigger: 'blur' }]
}
const emailRules = {
  substract: [{ required: true, message: '邮件首段内容不能为空', trigger: 'blur' }]
}

// 添加考生 modal
const stuDialogVisible = ref(false)
const emailDialogVisible = ref(false)
const templateVisible = ref(false)
const previewTemplateVisible = ref(false)
const stuTxtConfig = ref({ title: '添加考生', confirm: '添加' })
const emailTxtConfig = ref({ title: '发送邮件', confirm: '发送' })
const templateConfig = ref({ title: '编辑模板', confirm: '保存模板', cancel: '取消编辑' })
const previewTemplateConfig = ref({ title: '邮件模板', confirm: '发送邮件', cancel: '编辑模板' })

const formatStuInfo = (text: string): { name: string; email: string }[] => {
  const objects: { name: string; email: string }[] = [];

  // 正则表达式，用于匹配名字和邮箱的格式
  const regex = /([\u4e00-\u9fa5a-zA-Z\s\.·]+)\s*[（(]([^)）]+)[)）]/g;
  let match;

  // 使用正则表达式来迭代匹配文本中的所有符合格式的行
  while ((match = regex.exec(text))) {
    const name = match[1].trim(); // 去除名字前后的空格
    const email = match[2].trim(); // 去除邮箱前后的空格
    if (email.includes('@')) { // 确保邮箱字段中包含 '@' 符号
      objects.push({ username: name, email });
    }
  }

  return objects;
}

const addLoading = ref(false)
const updateStuDialog = () => {
  stuFormRef.value?.validate().then(() => {
    const stuinfos = studentForm.value.more
      ? formatStuInfo(studentForm.value.multiStuInfo)
      : [{ username: studentForm.value.name, email: studentForm.value.email }]
    addLoading.value = true
    examemailCreate({
      templeteId: route.query.id,
      studentList: stuinfos
    })
      .then((res: any) => {
        if (res) {
          message.success(res)
          // message.info(res)
        } else {
          // message.success('添加考生成功!')
          message.info(res.message)
        }
        addLoading.value = false
        stuDialogVisible.value = false
        resetStudentForm()
        getStuList()
      })
      .catch(() => {
        addLoading.value = false
      })
  })
}
const closeStuDialog = () => {
  resetStudentForm()
  stuDialogVisible.value = false
}
const cancelStuDialog = () => {
  closeStuDialog()
}

const updateEmailDialog = () => {
  emailFormRef.value?.validate().then(() => {
    emailDialogVisible.value = false
    emailFormRef.value?.resetFields()
  })
}
const closeEmailDialog = () => {
  emailFormRef.value?.resetFields()
  emailDialogVisible.value = false
}

const updateTemplateDialog = () => {
  if (!templateContent.value) {
    message.error('模板内容不能为空')
    return
  }
  updateExamemailtemplate({
    action: 'modify',
    templeteId: route.query.id,
    content: templateContent.value
  }).then(() => {
    message.success('邮件模板编辑成功!')
    templateVisible.value = false
    getTemplate().then(() => {
      previewToEdit.value && (previewTemplateVisible.value = true)
      nextTick(() => {
        if (iframeRef.value) {
          iframeRef.value.srcdoc = examTemplate.value
        }
      })
    })
  })
}

const previewToEdit = ref(false)
const closeTemplateDialog = () => {
  templateVisible.value = false
}
const cancelTemplateDialog = () => {
  closeTemplateDialog()
  previewToEdit.value && (previewTemplateVisible.value = true)
}

// 重置模板
const examTemplate = ref('')
const iframeRef = ref()
const resetLoading = ref(false)
const resetTemplate = async () => {
  try {
    resetLoading.value = true
    await getTemplate('initial')
    resetLoading.value = false
    templateContent.value = examTemplate.value
  } catch (error) {
    resetLoading.value = false
  }
}

const sendLoading = ref(false)
const updatePreviewTemplateDialog = () => {
  const examEmailList = selectedDetailData.value.map((item: any) => ({
    email: item.email,
    stuname: item.studentName,
    isnew: item.isnew
  }))

  sendLoading.value = true
  sendpaperemail({ id: route.query.id, examEmailList })
    .then(() => {
      message.success('考试邀请邮件发送成功!')
      previewTemplateVisible.value = false
      previewToEdit.value = false
      sendLoading.value = false
      selectedDetailData.value = []
      getStuList()
    })
    .catch(() => {
      sendLoading.value = false
    })
}
const closePreviewTemplateDialog = () => {
  previewTemplateVisible.value = false
  previewToEdit.value = false
  // editTemplate()
}

const cancelPreviewTemplateDialog = () => {
  previewTemplateVisible.value = false
  previewToEdit.value = true
  editTemplate()
}

const addStudent = () => {
  stuDialogVisible.value = true
}

const sendEmail = () => {
  if (!selectedDetailData.value.length) {
    message.error('请勾选需要发送邀请邮件的考生')
    return
  }
  previewTemplateVisible.value = true
  nextTick(() => {
    iframeRef.value.srcdoc = examTemplate.value
  })

  // emailDialogVisible.value = true
}

// 编辑模板
const editTemplate = () => {
  templateVisible.value = true
  nextTick(() => {
    templateContent.value = examTemplate.value
    const wtextEle = document.querySelector('.w-e-toolbar')
    const wtextcontainerEle = document.querySelector('.w-e-text-container')
    wtextEle.parentElement!.style.height = 'calc(100% - 60px)'
    wtextcontainerEle.style.height = '100%'
  })
}
const getHtml = (val: any) => {
  templateContent.value = val
}

// 获取学生监控列表
const router = useRouter()

const backtoExam = () => {
  router.push({ name: 'paperManage' })
}

// 获取关联考生列表
async function getAssociateAssociateStuList(): Promise<any[]> {
  let { records }: any = await queryRelatedResume({
    action: 'query',
    templeteId: route.query.id,
    page: 1,
    per_page: 9999
  })
  return records.map((i: any) => i.id)
}

// 获取邮件邀请学生列表
async function getStuList() {
  data.loading = true
  try {
    let param = {
      action: 'query',
      templeteId: route.query.id,
      templeteUUID: route.query.UUID,
      condition: searchContent.value,
      // status: sendStatus.value,
      basePager:{
        current: data.pagination.current,
        size: data.pagination.pageSize
      },
      page: data.pagination.current,
      per_page: data.pagination.pageSize,
      ...otherParams.value,
    }
    param.emailStatusList = param.emailStatus
    param.associateList = param.associate
    delete param.emailStatus
    delete param.associate
    let res: any = await examemail(param)
    // let associateIds = await getAssociateStuList()
    // res.records.forEach((item: any) => {
    //   item.associate = associateIds.includes(item.stuid)
    // })
    data.stuList = res.records
    data.loading = false
    data.pagination.total = res.total
    selectedData.value.length = 0
  } catch (error) {
    console.log(error)
  } finally {
    data.loading = false
  }
}

// 删除学生列表
const delStuList = (ids: string[]) => {
  data.loading = true
  const params = {
    action: 'del',
    templeteId: route.query.id,
    templeteUUID:route.query.UUID,
    studentList: ids.map((id) => ({ id }))
  }
  examemailDelete(params)
    .then(() => {
      message.success('删除成功!')
      data.loading = false
      data.pagination.current = 1
      getStuList()
    })
    .catch(() => {
      data.loading = false
    })
}

// 获取邮件模板
const getTemplate = (initial: null | string = null) => {
  return examemailtemplate({ action: 'query', templeteId: route.query.id, initial }).then((res: any) => {
    examTemplate.value = initial != null ? res.initialcontent : res.content
  })
}

onMounted(() => {
  getStuList()
  getTemplate()
})
</script>

<style lang="less" scoped>
.nodatawrapper {
  width: 100%;
  display: flex;

  justify-content: center;
  .nodata {
    width: 372px;
    height: 178px;
  }
}
.paper-email-send-container {
  height: 100%;
  overflow: auto;
  padding: 0 20px 0 20px;
  display: flex;
  flex-direction: column;
}
.paper-email-send-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}
.paper-email-send {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 24px;
}
.paper-email-send-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  .email-send-status {
    font-size: 12px;
    margin-right: auto;
    .label {
      color: #252b3a;
      margin-right: 10px;
    }
    :deep(.ant-radio-wrapper) {
      font-size: 12px !important;
    }
  }
  .email-send-btns {
    .ant-btn {
      border-radius: 8px;
      font-size: 14px;
    }
    .send-btn {
      margin-right: 8px;
    }
  }
}
.student-form,
.email-form {
  :deep(.ant-form-item-label > label) {
    font-size: 12px;
    color: #626262;
  }
  :deep(.ant-col) {
    width: 80px;
  }
  :deep(.ant-radio-wrapper) {
    font-size: 12px !important;
  }
  .ant-input {
    width: 280px;
    height: 32px;
    border-radius: 8px;
    font-size: 12px;
    color: #181818;
  }
  .multi-add-stu {
    margin-bottom: 16px;
    .label {
      font-size: 12px;
      margin-bottom: 8px;
    }
  }
  .ant-input-textarea {
    font-size: 12px;
  }
  :deep(textarea.ant-input) {
    border-radius: 8px;
    font-size: 12px;
    color: #181818;
  }
  :deep(.ant-select-selection-item-content) {
    font-size: 12px;
  }
}
</style>
<style lang="less">
.paper-email-send {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    color: #121633;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .status-icon {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #d9d9d9;
    border-radius: 50%;
  }
  .status-icon.active {
    background: #52c41a;
  }
}
.template-modal {
  .ant-modal-content {
    border-radius: 0px!important;
    .ant-modal-body {
      height: calc(90vh - 200px);
    }
  }
}
</style>
