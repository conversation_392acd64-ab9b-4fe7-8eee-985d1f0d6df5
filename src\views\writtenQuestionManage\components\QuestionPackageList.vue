<script setup lang="ts">
import PackagetItem from './QuestionPackageItem.vue'

defineProps({
  pkgList: {
    type: Array,
    default: () => [],
  },
  type: {
    type: String,
    default: 'package',
  },
})
const emits = defineEmits(['select'])
const activePopupPath = ref('')
function select(item: any) {
  emits('select', item)
}
</script>

<template>
  <div class="grid-container  relative">
    <PackagetItem disabled v-for="item in pkgList" :key="(item as any).path" v-model:active-popup-path="activePopupPath" :type="type" :item="item as any" @select="select" />
  </div>
</template>

<style lang="scss" scoped>
.grid-container {
  min-width: 800px;
  width: 100%;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(386px, 1fr));
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  justify-content: space-between;
  margin-top: 20px;
}
</style>