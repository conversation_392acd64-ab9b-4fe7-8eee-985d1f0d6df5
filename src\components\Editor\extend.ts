// 引入 wangEditor
import E from 'wangeditor' // npm 安装
// const E = window.wangEditor // CDN 引入的方式

// 获取必要的变量，这些在下文中都会用到
const { $, BtnMenu, DropListMenu, PanelMenu, DropList, Panel, Tooltip } = E

const IconUser = new URL('../../../src/assets/icons/svg/icon-user.svg', import.meta.url)
const IconWebsite = new URL('../../../src/assets/icons/svg/icon-website.svg', import.meta.url)

// 第一，菜单 class ，Button 菜单继承 BtnMenu class
export class InsertUser extends BtnMenu {
    constructor(editor: any) {
      // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
        const $elem = E.$(
            `<div class="w-e-menu" data-title="插入人名">
                <img src="${IconUser}">
            </div>`
        )
        super($elem, editor)
    }
    // 菜单点击事件
    clickHandler() {
        // 做任何你想做的事情
        // 可参考【常用 API】文档，来操作编辑器
        this.editor.cmd.do('insertHTML', '&nbsp;<img id="name-position" title="该处会填充考生的真实姓名" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAYCAYAAABtGnqsAAAAAXNSR0IArs4c6QAAAudJREFUWEftmctO6lAUhlfFAhqRSOKVm5DIgAlhwADewBc5D3UeyJA40YETSbg2ykWlAiVKRU7+nWwOLRVoafYZnK6EQKD/ovvb67ZTSdO0a13XfxNRjDyzQ0CRZfmXpKpqy4Nnh5vhWgUAZ47lnpA8gFsGgQfwXwB8eXmher1On5+ftL+/T8lkkiKRiONbGQ6HtLu7S3t7ewYfHx8fNJlM6PDw0NK3aJ3VTdiOQE3T6O7ujmazv6VTkiTK5/MMph3TdZ2+v7+p2WxSMBikk5MTg/z19ZUGgwGlUina2dkhWZbZ76J1q9ZkG2C1WqWnp6cln9FolC3UjtVqNVJVlUUZNoED4j6+vr5oOp1SIBCgcDhM6XSa/SRa5yrAh4cH6vf7Sz6Rwtls1g6/+bXYFETv2dmZQd/r9Rjgq6srS7+ida6k8O3tLat9ZkOUFAoFxwABC3Vw0RB9R0dHKwGK1LkC8P7+nlC8zRYKhSiXyzkGiBp4enpq0KNZoQauikCROlcAAh4gmg3wANGJIRXh19yE0IUBaBVAkTpXAMIJRhhFUeb+4vE4G2WcGjo7YMGQkrDj42P2jtJwcHBg6Vq0zjWAnU6HKpXK3F8mk1kaQTaBadXNeYNC7TPbxcUF+0q0btVabI0xGDe63S6LPowY3FD8Y7EYixpEzKaG+W/R3t7eaDwes5HG7/fPo5Bfk0gk2EfROlcA4qZbrZZhgLZyjEXyhW4KEpvRaDTYPIjTCACi0+Mds6W5O3O/onVbpXC5XDZE3U9wfD4fFYvFjdiNRiMW0Yg8DOLn5+fz6MImoFQg2pHOOKXwWiha50oE3tzcsGPXOsORq1QqrbuMdd3Hx0c2POMF8IvpyaMY/9lutxnMy8tLFo0idVa1eHFxG9fA5+dnen9/Z90SqYb0weL4GRU1C+mHP+QddC1FiwsQXbCfOu9PPkXr+H1sDNAJjP9B4wHccpc9gC4A9B4qOYeoSN5jTcf02GPNP7HBkRmbBj6KAAAAAElFTkSuQmCC" alt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAYCAYAAABtGnqsAAAAAXNSR0IArs4c6QAAAudJREFUWEftmctO6lAUhlfFAhqRSOKVm5DIgAlhwADewBc5D3UeyJA40YETSbg2ykWlAiVKRU7+nWwOLRVoafYZnK6EQKD/ovvb67ZTSdO0a13XfxNRjDyzQ0CRZfmXpKpqy4Nnh5vhWgUAZ47lnpA8gFsGgQfwXwB8eXmher1On5+ftL+/T8lkkiKRiONbGQ6HtLu7S3t7ewYfHx8fNJlM6PDw0NK3aJ3VTdiOQE3T6O7ujmazv6VTkiTK5/MMph3TdZ2+v7+p2WxSMBikk5MTg/z19ZUGgwGlUina2dkhWZbZ76J1q9ZkG2C1WqWnp6cln9FolC3UjtVqNVJVlUUZNoED4j6+vr5oOp1SIBCgcDhM6XSa/SRa5yrAh4cH6vf7Sz6Rwtls1g6/+bXYFETv2dmZQd/r9Rjgq6srS7+ida6k8O3tLat9ZkOUFAoFxwABC3Vw0RB9R0dHKwGK1LkC8P7+nlC8zRYKhSiXyzkGiBp4enpq0KNZoQauikCROlcAAh4gmg3wANGJIRXh19yE0IUBaBVAkTpXAMIJRhhFUeb+4vE4G2WcGjo7YMGQkrDj42P2jtJwcHBg6Vq0zjWAnU6HKpXK3F8mk1kaQTaBadXNeYNC7TPbxcUF+0q0btVabI0xGDe63S6LPowY3FD8Y7EYixpEzKaG+W/R3t7eaDwes5HG7/fPo5Bfk0gk2EfROlcA4qZbrZZhgLZyjEXyhW4KEpvRaDTYPIjTCACi0+Mds6W5O3O/onVbpXC5XDZE3U9wfD4fFYvFjdiNRiMW0Yg8DOLn5+fz6MImoFQg2pHOOKXwWiha50oE3tzcsGPXOsORq1QqrbuMdd3Hx0c2POMF8IvpyaMY/9lutxnMy8tLFo0idVa1eHFxG9fA5+dnen9/Z90SqYb0weL4GRU1C+mHP+QddC1FiwsQXbCfOu9PPkXr+H1sDNAJjP9B4wHccpc9gC4A9B4qOYeoSN5jTcf02GPNP7HBkRmbBj6KAAAAAElFTkSuQmCC" style="max-width: 100%;" />&nbsp;')
    }
    // 菜单是否被激活（如果不需要，这个函数可以空着）
    // 1. 激活是什么？光标放在一段加粗、下划线的文本时，菜单栏里的 B 和 U 被激活，如下图
    // 2. 什么时候执行这个函数？每次编辑器区域的选区变化（如鼠标操作、键盘操作等），都会触发各个菜单的 tryChangeActive 函数，重新计算菜单的激活状态
    tryChangeActive() {
        // 激活菜单
        // 1. 菜单 DOM 节点会增加一个 .w-e-active 的 css class
        // 2. this.this.isActive === true
        // this.active()

        // // 取消激活菜单
        // // 1. 菜单 DOM 节点会删掉 .w-e-active
        // // 2. this.this.isActive === false
        // this.unActive()
    }
}

export class InsertWebSite extends BtnMenu {
    constructor(editor: any) {
        const $elem = E.$(
            `<div class="w-e-menu" data-title="插入网址">
                <img src="${IconWebsite}">
            </div>`
        )
        super($elem, editor)
    }
    // 菜单点击事件
    clickHandler() {
        this.editor.cmd.do('insertHTML', '&nbsp;<img id="website-position" src="data:image/png;base64,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" alt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGgAAAAYCAYAAAAWPrhgAAAAAXNSR0IArs4c6QAABhhJREFUaEPtmndPlFsQxgcVpYhIKBJFQKQoQQRRVJQSiBSJAv/wEe6Hut/BWBIRAwpR0QQBKwrSYUUpShNUipjfJGfzbmPRuwvkZifZKPued2bOPDPPzDnZoMXFxaqVlZV/RSRBArKTImALDg7+J2h2dnYsAM5OwsXBFxsAre9Y9wKOSQCgHZ4EAYD+bwAtLy/L+Pi4fPv2Tbe2f/9+OXz4sOzdu/ePtvrlyxcZGRmxv5OTkyO7du3yqAN72FpfX9fPRmv/yJG/XEwcJiYm5OjRoy4avn//rs+Sk5Mdnr19+1Z4Dzl06JAcOXLEq/VNVxBBwcDo6Kj8+PFDg4UQuJCQEElMTJSsrCwJCgryapQFQ0NDMjg4KKmpqbqe93kX/S9fvnTQcfr0aXn9+rWUl5dLR0eHxMTESEZGhsOanz9/yv379ze0ff36dYfnBKupqWlT/rLoypUr9kScm5uTJ0+eSHV1tcv7JF9nZ6f6axWbzSZra2vCv8SPfXmTTQEEOI8fP9asKCoqkr6+PomLi1Pdk5OTkpaWJo8ePdKsKCws3BRIAIS+CxcuOPj4+fNnBWD37t36HECqqqqku7tbAQXIM2fOCOuioqJk37593vaoQblx44bU19e7gHrv3j2prKz0qqOxsVH9MPb+BiBjhL2srq76DqA3b96owuDgYBkYGNCsBzSErO/v75fjx4/LysqK7NmzR06dOuV1w54AevXqlYKel5enSXHu3Dml0F+/fklra6vSQmhoqOBTSUmJhIeH220NDw/L9PS0i218xZ47gAh8TU2NvgOQDx8+1Eox0tLSIgUFBcI6gPQE0Pz8vAAalOepgvwCkKEBnCOrcZQMJ4gIlURQeM4GeW6lAk9IeQKI9QT/3bt3CoSpVL6Hxtg8Aa+oqLDTrLFBgJaWllxMAm5bW5tHgE6cOKGVubi4KO/fv9dki46OVj0NDQ1SV1cnt2/ftgP04MEDpXlsRURE6LsHDhzQnnrp0qWtBYisZOOGL3H46tWr8uHDB91Aenq63L17187FVEBkZKRLg3SOmjuAaK6AwxBC5ZCtVK5VCAg2yNizZ89qsuDf7Oysx6oFoOfPn8v58+d1zcGDB9VHACehoMyenh5JSEhQ8Omp7Js+BzsUFxc7AETfxT59hiqGWUhSK0DNzc2a0AiMYu2ZPqU4BoOFhQXNKJwnwzIzM2VqakqNx8bGarafPHlS6Y4MJ4gMDBuJO4AYBExV0n88CTRH9tJsL168qL2KnmQVqhkbZgjhGUAx/cXHx+vHAATFYbu3t1fBoGqfPXumeySwfKwVhC7nHoQv21JBWwmQCTDZy0DA8OEsfE9Pcn42NjamyZGdna0gQEFUR21trargb/oJAISFhel3VoDonzdv3tQqS0pK0n5669YtpSzs7ViAnCnO0Jk/KM4K0KdPnyQ/P98FoK6uLqUoZ4CoGJ5RWUySVA8UxP8ZJJ4+faq9xfqeFSCoDGqFLUpLS5UymSChWXrqnTt3NhwStq2CtmNI+Pjxo8tZyIoU9EmWuxPeBVz6xNevX5Wa6ReAzTHAKgaga9euabVBlwDDBwqDHtFHQtCjnKc4pkz6oDkLbgvFsaGtHLNNANvb27Wv0c+MEGgol7HXndCoX7x4oc3+2LFjGnSCTz+hkefm5jrceBiAmOLoN5cvX1a1VB8MwaQINUKrnP0AiBsTfGA9CUAPJlk4XmwbQFt5UDWBpwqYdsrKyuwHX85BXJ84X6GQwTR5sp6MJmjWHmSGG3NzQWUQUAMQh2USwfQmqDElJUUPwkboR+agylDEM4YJJjXGc/yl2ji/UYGAaACnJxrd6PPpFGcc9MdVD1lpgk3QoBE2b8RMXZ7+hurIfjKeYDPyEwx0MLJTUVSBEfoTIzoZT+O39iC3JSmiugg45z6mPXdXWVwXOR8HrPoAh+GEBGEdEydjvs+ueqzGfHVZOjMzo03ZCHTmqwtQ7vOYwug55s7QHQAMFvjg7sLTrMdPgKWXcT76LwJt4heCPkZ9b7KpuzhvSgLP/ReBAED+i61PNAcA8kkY/ack8KMR/8XWF5ptQYGfXfkijn7RoT+7+g0gAQFGbujfkQAAAABJRU5ErkJggg==" style="max-width: 100%;" />&nbsp;')
    }
    tryChangeActive() {}
}