<template>
  <div>
    <div class="paper-score-container">
      <h3 class="paper-manage-title">
        <span style="color: rgba(0, 0, 0, 0.45)"><span @click="router.back()">能力测评 /</span></span
        ><span>查看成绩《{{ route.query.name }}》</span>
      </h3>
      <div class="paper-manage">
        <!-- 试卷header -->
        <div class="paper-manage-header">
          <div class="header-left">
            <a-input-search
              v-model:value.trim="searchContent"
              placeholder="请输入考生姓名"
              allow-clear
              @search="getUserList"
            />
          </div>
          <div class="header-right">
          </div>
        </div>
        <div class="table-wrapper">
          <a-table
            :columns="data.columns"
            :data-source="list"
            :row-key="(record:any) => record.id"
            :loading="data.loading"
            :pagination="paginationConfig"
            :scroll="{ x: 1200 }"
            @change="handleTableChange"
            @resizeColumn="(w: any, col: any) => col.width = w"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <div style="display: flex; align-items: center;">
                  <span style="width: 8px;height: 8px;display: inline-block;border-radius: 50%;margin-right: 4px;" :style="{ backgroundColor: record.is_completed == '已交卷' ? '#52C41A' : record.is_completed == '未交卷' ? '#FAAD14' : '#D9D9D9' }"></span>
                  {{ record.is_completed }}
                </div>
              </template>
              <template v-if="column.key === 'total'">
                <span>
                  <span>{{ record.is_completed == '未交卷' ? '-' : record.total }}</span>
                </span>
              </template>
              <template v-else-if="column.key === 'action'">
                <!-- <a-button type="link" :class="{ disabled: record.is_completed !== '已交卷' || !isConfigMonitor }" @click="queryMonitorPhotos(record)">监控</a-button>
                <a-divider type="vertical" /> -->
                <a-button type="link" @click="queryResume(record)">简历</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <resume-detail
      v-if="resumeDetailVisible"
      :resume-detail-visible="resumeDetailVisible"
      :resume-detail-id="previewResumeId"
      @closePreviewResume="closePreviewResume"
    />
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, ref, computed, watch, onActivated } from 'vue'
import { getPaperDetail, viewgrades } from '@/api/admin/paperManage'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import _ from 'lodash'
import ResumeDetail from '@/views/ResumeManage/resumeDetail.vue'
import { message } from 'ant-design-vue'
import { assessmentdetail, getAssessmentresults } from '@/api/admin/abilityTest'

// 试卷配置数据
const data = reactive({
  columns: [
    {
      title: '考生姓名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      ellipsis: true,
      resizable: true
    },
    { 
      title: '性别',
      dataIndex: 'sex',
      key: 'sex',
      width: 100,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '男', value: '男' },
          { text: '女', value: '女' },
      ],
    },
    { title: '院校', dataIndex: 'school', key: 'school', width: 200, ellipsis: true, resizable: true },
    { title: '电子邮箱', dataIndex: 'email', key: 'email', width: 220, ellipsis: true, resizable: true },
    { title: '实际开始时间', dataIndex: 'startTime', key: 'startTime', width: 200, ellipsis: true, resizable: true },
    { title: '实际结束时间', dataIndex: 'endTime', key: 'endTime', width: 200, ellipsis: true, resizable: true },
    {
      title: '考试状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      ellipsis: true,
      resizable: true,
      sorter: true
    },
    {
      title: '分数',
      dataIndex: 'total',
      key: 'total',
      width: 120,
      align: 'right',
      ellipsis: true,
      resizable: true,
      sorter: true
    },
    {
      title: '是否通过',
      dataIndex: 'ispass',
      width: 160,
      key: 'ispass',
      ellipsis: true,
      resizable: true
    },
    {
      title: '操作',
      width: 100,
      fixed: 'right',
      align: 'center',
      key: 'action'
    }
  ],
  stuList: [],
  loading: false,
})

const list = ref([])

// 快速搜索
const searchContent = ref('')

const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: number) => '总条数：' + total,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

function handleTableChange(pagination: any, filters: any = {}, sort: any = {}) {
  // 处理分页
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  list.value = data.stuList.filter(item => {
    return Object.entries(filters).every(([k, v]: any) => {
      if (v === null) return true
      return v.includes(item[k])
    })
  })
  list.value.sort((a, b) => {
    if (sort.order === 'descend') return b[sort.field] - a[sort.field]
    if (sort.order === 'ascend') return a[sort.field] - b[sort.field]
    return 0
  })
}

const route = useRoute()
const id = route.query.id as string
async function getUserList() {
  data.loading = true
  try {
    let res = await getAssessmentresults({
      assessment: id,
      condition: searchContent.value
    }) as any
    data.stuList = res
    list.value = res
  } finally {
    data.loading = false
  }
}

const router = useRouter()
const queryMonitorPhotos = (item: any) => {
  if (!isConfigMonitor.value) {
    return message.info('本场考试未要求开启摄像头')
  }
  if (item.is_completed !== '已交卷') {
    return message.info('该考生' + item.is_completed)
  }
  router.push({
    path: '/admin/monitor-records',
    query: {
      studentid: item.id,
      paperid: route.query.id,
      stupid: item.stupid,
      papername: route.query.name
    }
  })
}

// 预览简历
const resumeDetailVisible = ref(false)
const previewResumeId = ref()
const queryResume = (record: any) => {
  previewResumeId.value = record.id
  resumeDetailVisible.value = true
}
const closePreviewResume = () => {
  resumeDetailVisible.value = false
}

// 获取考试配置
const isConfigMonitor = ref(false)
const totalScore = ref(0)
async function getPaperConfig() {
  let res = await assessmentdetail({ id: route.query.id as string })
  isConfigMonitor.value = res.computermonitor === 'yes' || res.phonemonitor === 'yes'
  totalScore.value = res.score
}

onMounted(() => {
  getPaperConfig()
  getUserList()
})

</script>

<style lang="less" scoped>
.ai-text-panel {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px 24px;
  min-height: 200px;
}
.paper-score-container {
  min-height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-container) {

  tr td .ant-btn.disabled span {
    color: rgba(0,0,0,0.25)!important;
    cursor: not-allowed;
  }
}
</style>
<style lang="less">
.paper-score-container {
  // height: 100%;
  // overflow: hidden;
  height: calc(100% + 40px);
  padding: 0 20px 20px 20px;
  .paper-manage-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .paper-manage {
    flex: 1;
    // height: calc(100% - 48px);
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;
    background-color: #fff;
    .paper-manage-header {
      display: flex;
      padding-bottom: 16px;
      justify-content: space-between;
      align-items: center;

      .header-right {
        display: flex;
        align-items: center;
        .ant-btn {
          border-radius: 8px;
          font-size: 14px;
          margin-left: 10px;
        }
      }
    }
    .table-bar {
      height: 60px;
      line-height: 60px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ant-checkbox-wrapper {
        font-size: 15px;
      }
      .show-columns {
        font-size: 15px;
        span {
          margin-right: 10px;
        }
      }
    }
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    text-align: left;
    &:first-child {
      padding-left: 8px !important;
    }
    &:last-child {
      padding-left: 22px !important;
    }
    background: #f1f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
  }
}
</style>
