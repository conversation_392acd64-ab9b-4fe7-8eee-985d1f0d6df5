<template>
  <div ref="myRef" :style="{ width, height }" class="bar-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: '教师创建试卷总数'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 20)
})

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  setChartOption()

  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}

const setChartOption = () => {
  Chart.value.setOption({
    title: {
      text: props.name,
      left: 'center',
      textStyle: {
        fontSize: 15,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: category.value
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: count.value,
        type: 'bar'
      }
    ]
  })
}

const category = ref<string[]>([])
const count = ref<number[]>([])
watch(
  () => props.data,
  (val) => {
    category.value = val.map((item: any) => item.name)
    count.value = val.map((item: any) => item.value)
    setTimeout(() => {
      setChartOption()
    }, 200)
  },
  {
    immediate: true
  }
)

watch(
  () => props.width,
  () => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  () => {
    nextTick(() => {
      Chart.value && Chart.value.resize()
    })
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.bar-chart {
  padding: 16px;
  background: #fff;
}
</style>
