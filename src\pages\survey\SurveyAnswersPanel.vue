<template>
    <div class="survey-answers">
        <div class="avatar-swiper">
            <div class="avatar-swiper-content" :style="{ left: left + 'px' }">
                <template v-for="(item, index) in data" :key="item.id">
                    <div class="avatar-swiper-item" :class="{ active: index === activeIndex }">
                        <img :src="item.headimgurl || defaultAvatar" width="36" style="border-radius: 50%;" alt="" @click="activeIndex = index">
                    </div>
                </template>

            </div>
            <div v-if="currentSwiperIndex > 0" class="arrow" style="left: 8px;" @click="clickLeft">
                <LeftOutlined />
            </div>
            <div v-if="currentSwiperIndex < maxSwipperIndex" class="arrow" style="right: 8px;" @click="clickRight">
                <RightOutlined />
            </div>
        </div>
        <div class="scroll-wrapper">
            <div class="basic-info" v-if="activeData">
                <div class="basic-info-item">
                    <div class="label">开始时间</div>
                    <div class="value">{{ activeData.startTime }}</div>
                </div>
                <div class="basic-info-item">
                    <div class="label">结束时间</div>
                    <div class="value">{{ activeData.submitTime }}</div>
                </div>
                <div class="basic-info-item">
                    <div class="label">填写时长</div>
                    <div class="value">{{ getDurationStr(activeData.answerDuration) }}</div>
                </div>
                <div class="basic-info-item">
                    <div class="label">填写设备</div>
                    <div class="value">{{ activeData.answerDevice }}</div>
                </div>
                <div class="basic-info-item">
                    <div class="label">浏览器</div>
                    <div class="value">{{ activeData.answerBrowser }}</div>
                </div>
                <div class="basic-info-item">
                    <div class="label">IP</div>
                    <div class="value">{{ activeData.answerIP }}</div>
                </div>
                <div class="basic-info-item" v-if="activeData.nickname">
                    <div class="label">微信昵称</div>
                    <div class="value">{{ activeData.nickname }}</div>
                </div>
            </div>
            <div class="survey-content" v-if="activeData">
                <a-form-item class="ques-item" v-for="(item, index) in activeData.answerDetail" :data-id="item.id">
                    <template v-if="item.type <= 100">
                        <p class="ques-title">
                            <span v-if="item.required" style="color: #f66f6a;">*</span>
                            {{ item.number }}. {{ item.body }}
                        </p>

                        <!-- 单选题 -->
                        <a-radio-group v-if="item.type === QuestionEnum['单选题']" :value="item.answer">
                            <a-radio v-for="option in item.options" :value="option.value">{{ option.content }}</a-radio>
                            <a-radio value="-1" v-if="item.other">
                                其他
                                <a-input class="other-ipt" v-if="item.answer === '-1'"
                                    :value="item.otherContent ?? ''" />
                            </a-radio>
                        </a-radio-group>

                        <!-- 多选题 -->
                        <div class="ant-checkbox-group" v-if="item.type === QuestionEnum['多选题']" style="width: 100%">
                            <a-row v-for="option in item.options">
                                <a-checkbox :checked="item.answer?.split(',')?.includes(option.value)">{{
                                    option.content }}</a-checkbox>
                            </a-row>
                            <a-row v-if="item.other">
                                <a-checkbox :checked="item.answer?.includes('-1')">
                                    其他
                                    <a-input class="other-ipt" v-if="item.answer?.includes('-1')"
                                        :value="item.otherContent ?? ''" />
                                </a-checkbox>
                            </a-row>
                        </div>

                        <!-- 问答题 -->
                        <a-textarea v-if="item.type === QuestionEnum['问答题']" :value="item.answer ?? ''"
                            placeholder="请输入" :autoSize="true" />

                        <!-- 评分题 -->
                        <div v-if="item.type === QuestionEnum['评分题']" class="mark-ques">
                            <div v-for="(star, starIndex) in item.score">
                                <svg-icon v-if="starIndex + 1 <= item.answer" name="active_star" width="32px"
                                    height="32px"></svg-icon>
                                <svg-icon v-else name="deactive_star" width="32px" height="32px"></svg-icon>
                            </div>
                            <span class="mark-desc">{{ item.min_desc }}</span>
                            <span class="mark-desc" style="right: 0;">{{ item.max_desc }}</span>
                        </div>
                    </template>
                    <template v-else>
                        <a-divider v-if="item.type === QuestionEnum['分割线']" dashed
                            style="border-color: #e8e8e8;color: rgba(0,0,0,0.45);font-size: 14px;">
                            {{ item.dividerTitle }}
                        </a-divider>
                    </template>

                </a-form-item>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import moment from 'moment';
import { QuestionEnum } from '@/models/questionModel'

const props = withDefaults(defineProps<{
    /** 答卷数组 */
    data: any[]
}>(), {
    data: () => ([])
})

/** 默认头像 */
let defaultAvatar = new URL('../../assets/icons/svg/avatar_default.svg', import.meta.url).href

const activeIndex = ref(0)
const activeData = computed(() => props.data[activeIndex.value])

const currentSwiperIndex = ref(0)

watch(currentSwiperIndex, () => {
    // currentSwiperIndex的变化决定头像栏的定位
    left.value = -currentSwiperIndex.value * distance * 4
})
watch(activeIndex, (val) => {
    // 根据激活项水平移动头像栏，确保激活项始终在中间4个
    let i = val - currentSwiperIndex.value * 4
    if (i <= 1) {
        currentSwiperIndex.value = Math.max(currentSwiperIndex.value - 1, 0)
    } else if (i >= 6) {
        currentSwiperIndex.value = Math.min(currentSwiperIndex.value + 1, maxSwipperIndex.value)
    }
})

/** 头像栏的最大页数（一页4个） */
const maxSwipperIndex = computed(() => {
    const total = props.data.length
    return total <= 8 ? 0 : Math.ceil((total - 8) / 4)
})

/** 一个头像和边距的宽度和，用于计算头像栏移动的位置 */
const distance = 63
const left = ref(0)
function clickLeft() {
    if (currentSwiperIndex.value <= 0) return
    currentSwiperIndex.value -= 1
}
function clickRight() {
    if (currentSwiperIndex.value >= maxSwipperIndex.value) return
    currentSwiperIndex.value += 1
}

function positionTo(answerId: string, questionId?: string) {
    activeIndex.value = props.data.findIndex(item => item.serial_number === answerId)
    if (questionId) {
        let ele = document.querySelector(`.ques-item[data-id="${questionId}"]`)
        ele?.scrollIntoView({ behavior: 'smooth' })
    }
}

function getDurationStr(answer_duration) {
    let duration = moment.duration(answer_duration, 'seconds')
    let ans = ''
    if (duration.hours() > 0) {
        ans += duration.hours() + '时'
    }
    if (duration.minutes() > 0) {
        ans += duration.minutes() + '分'
    }
    ans += duration.seconds() + '秒'
    return ans
}

defineExpose({
    positionTo
})

</script>

<style lang="less" scoped>
.survey-answers {
    height: 100%;
    user-select: none;
    display: flex;
    flex-direction: column;

    .avatar-swiper {
        height: 45px;
        margin-bottom: 24px;
        position: relative;
        overflow: hidden;

        .avatar-swiper-content {
            position: absolute;
            left: 0px;
            display: flex;
            gap: 18px;
            transition: all ease .2s;

            .avatar-swiper-item {
                border: 2px solid transparent;
                border-radius: 50%;
                cursor: pointer;
                padding: 2px;

                &.active {
                    border-color: #5478ee;
                }
            }
        }

        .arrow {
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 1);
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all ease .1s;

            .anticon {
                font-size: 6px;
            }

            &:hover {
                background-color: #5478ee;
                color: #fff;
            }
        }
    }

    .scroll-wrapper {
        flex: 1;
        min-height: 0;
        overflow: scroll;
        margin-right: -6px;
        padding-right: 6px;
    }

    .basic-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 16px;
        grid-row-gap: 16px;

        .basic-info-item {
            font-size: 12px;
            display: flex;

            .label {
                width: 72px;
                color: #626262;
            }

            .value {
                color: #181818;
            }
        }

    }

    .survey-content {
        flex: 1;
        min-height: 0;
        overflow: auto;

        .ques-item {
            margin-top: 40px;
            margin-bottom: 40px;
            overflow: hidden;

            .ques-title {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 24px;
                margin-bottom: 16px;
            }
        }
    }
}

.mark-ques {
    display: flex;
    gap: 36px;
    position: relative;
    width: fit-content;
    padding-bottom: 30px;

    .mark-desc {
        position: absolute;
        bottom: 8px;
        padding: 0 4px;
        border-radius: 4px;
        background: #f5f5f5;
        color: #626262;
    }
}

.other-ipt {
    width: 100%;
    margin-left: 0;
    margin-top: 10px;
}

.ant-radio-group,
.ant-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

:deep(.ant-radio-wrapper),
:deep(.ant-checkbox-wrapper) {
    width: 100%;

    >span+span {
        flex: 1;
    }

}

:deep(.ant-radio),
:deep(.ant-checkbox) {
    margin-right: 16px;
}
</style>