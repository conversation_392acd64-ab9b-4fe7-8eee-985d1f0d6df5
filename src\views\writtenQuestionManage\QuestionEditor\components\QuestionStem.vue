<script setup lang="ts">
const {
  formState,
  activeTab,
  validateInfos,
  bodyInputRef,
  bodyEditorRef,
  handleBodyBlur,
  insetBlank,
  handleComplexContentBlur,
} = inject('steamProps') as any
</script>

<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <div v-show="activeTab === '1'">
    <a-form-item
      v-if="!formState.complicatedediting"
      label="题干内容"
      v-bind="validateInfos.body"
      name="body"
    >
      <a-textarea
        :ref="bodyInputRef"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
        @blur="handleBodyBlur"
      />
      <div v-if="formState.type === 5" class="body-tip">
        <svg-icon name="tip" class="tip-icon" />
        <span>点击</span>
        <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
        <span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
    <a-form-item v-else label="题干内容" v-bind="validateInfos.complexcontent" name="body">
      <VueQuillEditor
        :ref="bodyEditorRef"
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
        @blur="handleComplexContentBlur"
      />
      <div v-if="formState.type === 5" class="body-tip">
        <svg-icon name="tip" class="tip-icon" />
        <span>点击</span>
        <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
        <span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
  </div>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>