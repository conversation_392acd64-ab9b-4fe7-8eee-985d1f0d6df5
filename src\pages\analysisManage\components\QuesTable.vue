<!-- 单场考试试题得分table -->
<template>
  <div>
    <a-table
      class="common-table ques-table"
      sticky
      :columns="columns"
      :row-key="(record: any) => record.id"
      :data-source="props.list"
      :scroll="{ x: 400 }"
      :pagination="false"
      @resizeColumn="(w: any, col: any) => col.width = w">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'body'">
          <span class="name-link" @click="analyze(record)">{{ record.body }}</span>
        </template>
        <template v-if="column.key === 'score_rate'">
          {{ !_.isNil(record.score_rate) ? `${record.score_rate}%` : '--' }}
        </template>
      </template>
    </a-table>
    <QuesDetail :quesInfo="quesInfo" :visible="visible" @close="onClose" />
  </div>
</template>
  
<script lang="ts" setup>
import { ref } from 'vue'
import _ from 'lodash'
import { testQuestion } from '@/api/exam'
import QuesDetail from './QuesDetail.vue'

const props = defineProps<{
  list: any[]
}>()

const columns = ref([
  {
    title: '题干',
    dataIndex: 'body',
    key: 'body',
    width: 160,
    minWidth: 100,
    maxWidth: 240,
    ellipsis: true,
    resizable: true
  },
  {
    title: '得分率',
    dataIndex: 'score_rate',
    key: 'score_rate',
    width: 100,
    align: 'right',
    sorter: {
      compare: (a: any, b: any) => a.score_rate - b.score_rate,
      multiple: 2,
    }
  },
  {
    title: '题型',
    width: 80,
    dataIndex: 'type_label',
  },
  {
    title: '题库',
    width: 80,
    dataIndex: 'official_cert_label',
  },
])

const visible = ref(false)
const quesInfo = ref<any>(null)
// 打开题目详情抽屉
const analyze = async (record: any) => {
  try {
    const data = await testQuestion({ id: record.id })
    console.log(data)
    visible.value = true
    quesInfo.value = data
  } finally {
    // do nothing
  }
}
const onClose = () => {
  visible.value = false
}

</script>
  
<style lang="less" scoped>

.ques-table {
  margin-top: 20px;
  .name-link {
    width: 100%;
    display: block;
    color: rgba(84,120,238,1) !important;
    text-align: left !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
</style>
  