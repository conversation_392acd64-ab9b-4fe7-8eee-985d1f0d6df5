<!-- eslint-disable ts/no-use-before-define -->
<script lang="ts" setup>
import Tagify from '@yaireo/tagify'
import { ElMessage } from 'element-plus'
// eslint-disable-next-line
// @ts-ignore
import debounce from 'lodash/debounce'
import { onMounted, ref, watch } from 'vue'
import '@yaireo/tagify/dist/tagify.css'

const props = withDefaults(
  defineProps<{
    modelValue: any[]
    ignorecase?: boolean
    hideTagMarginTop?: boolean
    maxTags?: number
  }>(),
  {
    ignorecase: true,
    hideTagMarginTop: true,
    maxTags: Infinity,
  },
)

const emits = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
}>()

watch(
  () => props.modelValue,
  () => {
    console.log(props.modelValue)
    tagify.value.loadOriginalValues(props.modelValue)
  },
)

const inputRef = ref()
let tagify = ref()

function handleChange(e: any) {
  const str = e.target.value
  if (props.modelValue?.join(',') === str) 
    return
  if (!str) {
    emits('update:modelValue', [])
  }
  else {
    let ans = str.split(',')
    ans = ans.map((str: string) => str.trim())
    emits('update:modelValue', ans)
  }
}

onMounted(() => {
  tagify.value = new Tagify(inputRef.value, {
    originalInputValueFormat: valuesArr => valuesArr.map(item => item.value).join(','),
    duplicates: true,
    pasteAsTags: false,
    maxTags: props.maxTags,
    callbacks: {
      paste(e) {
        let pasteText = e.detail.pastedText
        pasteText = pasteText.replace(/,/g, '，') // 移除所有逗号
        tagify.value.addTags([pasteText.trim()])
      },
      blur() {
        if (!props.ignorecase) 
          return
        const tags = tagify.value.getTagElms()
        const toLowerCaseAArray = tags.map((tag: any) => tag.textContent?.toLowerCase().replace(/,/g, '，'))
        if (new Set(toLowerCaseAArray).size !== toLowerCaseAArray.length) {
          ElMessage.error('请移除大小写不同的重复的关键词')
        }
      },
      add: debounce((e: any) => {
        const tags = tagify.value.getTagElms()
        if (tags.length >= props.maxTags) {
          return ElMessage.warning('超出最大标签数量限制')
        }
        const prevInputTag = tags.slice().slice(0, -1)
        const existTagValue = prevInputTag.map((tag: typeof tags[0]) => {
          if (props.ignorecase) 
            return tag.textContent?.toLowerCase()
          else return tag.textContent
        })
        if (!e.detail.data) 
          return
        const checkValue = props.ignorecase ? e.detail.data.value.toLowerCase() : e.detail.data.value
        if (existTagValue.includes(checkValue)) {
          ElMessage.error('请勿添加重复的关键词')
          tagify.value.removeTags([tags[tags.length - 1]])
        }
      }, 100),
      remove: (e) => {
        emits('update:modelValue', e.detail.tagify.value as unknown as string[])
      },
      invalid: (e) => {
        const { message: msg, tag } = e.detail
        if (msg === 'already exists') {
          ElMessage.error('请勿添加重复的关键词')
          tagify.value.removeTags([tag])
        }
      },
    },
  })
  tagify.value.loadOriginalValues(props.modelValue)
})
</script>

<template>
  <input ref="inputRef" placeholder="按回车键Enter创建标签" @change="handleChange">
</template>

<style lang="less">
.tagify {
  width: 100%;
  border-radius: 4px;
  --tag-bg: #e9e9e9;
  --tag-hover: #e9e9e9;
  --tag-inset-shadow-size: 12rem;
  --tag--max-width: 100%;
  .tagify__tag {
    // margin-top: v-bind(tagMarginTop);
    cursor: text;
  }
}

.tagify__tag:focus div::before,
.tagify__tag:hover:not([readonly]) div::before {
  --tag-bg-inset: -1.5px;
}
.tagify__tag__removeBtn {
  flex: none;
}
</style>