<!-- 单场考试详情分析 -->
<template>
  <div class="analysis">
    <div class="spin-wrap" v-if="loading">
      <a-spin />
    </div>
    <div class="common-page-title" v-if="route.meta.from === 'paperManage'">
      <span @click="gobackPaperManage">试卷管理 / </span>
      <span>考试分析《{{ paperDetail.name || route.query.name }}》</span>
    </div>
    <div class="common-page-title" v-else>
      <span @click="gobackPaper">考试分析 / </span>
      <span>{{ paperDetail.name || route.query.name }}</span>
    </div>
    
    <div class="analysis-content">
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="panel">
            <div class="panel-title">实考人数</div>
            <div class="panel-content">
              <div class="value">{{ paperDetail.actual }}</div>
              <div class="tip">应考人数：{{ paperDetail.total }} 人</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="panel">
            <div class="panel-title">通过人数：</div>
            <div class="panel-content">
              <div class="value">{{ paperDetail.pass_count }}</div>
              <div class="tip">应考人数：{{ paperDetail.total }} 人</div>
            </div>
            <!-- <PassGauge class="panel-content" :value="paperDetail.pass_rate" /> -->
          </div>
        </a-col>
        <a-col :span="6">
          <div class="panel">
            <div class="panel-title">平均分</div>
            <div class="panel-content">
              <div class="value">{{ paperDetail.avg_score }}</div>
              <div class="tip">总分：{{ paperDetail.score }} 分</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="panel">
            <div class="panel-title">平均答题时长</div>
            <div class="panel-content">
              <div class="value">
                <div class="number">{{ paperDetail.avg_duration }}</div>
                <div class="unit">min</div>
              </div>
              <div class="tip">推荐答题时长：{{ paperDetail.rec_duration }} min</div>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row style="flex: 1; min-height: 0; margin-top: 20px" :gutter="20">
        <a-col style="height: 100%" :span="12" flex="auto">
          <div class="panel table-panel">
            <div class="panel-title">得分率排行榜</div>
            <Questable class="panel-content table-content" :list="paperDetail.ques_data" />
          </div>
        </a-col>
        <a-col style="height: 100%" :span="12" flex="auto">
          <div class="panel table-panel">
            <div class="panel-title">分数段统计</div>
            <div class="show-analysis-btn" @click="handleShowAnalysis">查看分析</div>
            <ScoreBar
              class="panel-content"
              :data="paperDetail.score_data"
              :curveData="paperDetail.score_curve_data" />
          </div>
        </a-col>
      </a-row>
    </div>
    <PaperAnalysis
      :visible="analysisVisible"
      :texts="analysisInfo"
      :analysisLoading="analysisLoading"
      @close="analysisVisible = false" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import _ from 'lodash'

import { paperreport, aipaperanalyzenew } from '@/api/admin/statisticAnalysis'
import { QuestionEnum } from '@/models/questionModel'
import Questable from './components/QuesTable.vue'
import PassGauge from './components/PassGauge.vue'
import ScoreBar from './components/ScoreBar.vue'
import PaperAnalysis from './components/PaperAnalysis.vue'

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const route = useRoute()
const gobackPaper = () => router.replace({ name: 'testPaperAnalysis' })
const gobackPaperManage = () => router.replace({ name: 'paperManage' })

const loading = ref(false)
// 考试详情
const paperDetail = reactive<{
  name: string // 考试名称
  actual: number | string // 实考人数
  total: number | string // 总人数
  pass_count: number | string // 通过数
  pass_rate: number // 通过率
  avg_score: number | string // 平均分
  score: number | string // 总分
  avg_duration: number | string // 平均答题时间
  rec_duration: number | string // 推荐答题时长
  ques_data: any[] // 试题得分排行榜
  score_data: any[] // 分数段统计
  score_curve_data: any[] // 正态分布曲线数据
  standard_deviation: number | string // 标准差
}>({
  name: '',
  actual: '--',
  total: '--',
  pass_count: '--',
  pass_rate: 0,
  avg_score: '--',
  score: '--',
  avg_duration: '--',
  rec_duration: '--',
  ques_data: [],
  score_data: [],
  score_curve_data: [],
  standard_deviation: '--',
})
const getPaperDetailFn = async () => {
  try {
    loading.value = true
    const data: any = await paperreport({
      id: props.id,
    })
    paperDetail.name = data?.name || ''
    paperDetail.actual = !_.isNil(data?.actual) ? data.actual : '--'
    paperDetail.total =  !_.isNil(data?.total) ? data.total : '--'
    paperDetail.pass_count = !_.isNil(data?.pass_count) ? data.pass_count : '--'
    paperDetail.pass_rate = data?.pass_count && data?.actual ? Number((data.pass_count / data.actual * 100).toFixed(2)) : 0
    paperDetail.avg_score = !_.isNil(data?.avg_score) ? data.avg_score : '--'
    paperDetail.score = !_.isNil(data?.score) ? data.score : '--'
    paperDetail.avg_duration = !_.isNil(data?.avg_duration) ? data.avg_duration : '--'
    paperDetail.rec_duration = !_.isNil(data?.rec_duration) ? data.rec_duration : '--'
    paperDetail.ques_data = Array.isArray(data?.ques_data) ? data.ques_data.map((el: any) => ({
      ...el,
      type_label: QuestionEnum[el.type] || '--',
      official_cert_label: el.official_cert ? '官方题库' : '部门题库',
    })) : []
    paperDetail.score_data = Array.isArray(data?.score_data) ? data.score_data : []
    console.log("111222",paperDetail.score_data)
    paperDetail.score_curve_data = Array.isArray(data?.score_curve_data) ? data.score_curve_data : []
    paperDetail.score_curve_data = [
      [0, 1.01010101, 2.02020202, 3.03030303, 4.04040404, 5.05050505,
        6.06060606, 7.07070707, 8.08080808, 9.09090909, 10.1010101,
        11.11111111, 12.12121212, 13.13131313, 14.14141414, 15.15151515,
        16.16161616, 17.17171717, 18.18181818, 19.19191919, 20.2020202,
        21.21212121, 22.22222222, 23.23232323, 24.24242424, 25.25252525,
        26.26262626, 27.27272727, 28.28282828, 29.29292929, 30.3030303,
        31.31313131, 32.32323232, 33.33333333, 34.34343434, 35.35353535,
        36.36363636, 37.37373737, 38.38383838, 39.39393939, 40.4040404,
        41.41414141, 42.42424242, 43.43434343, 44.44444444, 45.45454545,
        46.46464646, 47.47474747, 48.48484848, 49.49494949, 50.50505051,
        51.51515152, 52.52525253, 53.53535354, 54.54545455, 55.55555556,
        56.56565657, 57.57575758, 58.58585859, 59.5959596, 60.60606061,
        61.61616162, 62.62626263, 63.63636364, 64.64646465, 65.65656566,
        66.66666667, 67.67676768, 68.68686869, 69.6969697, 70.70707071,
        71.71717172, 72.72727273, 73.73737374, 74.74747475, 75.75757576,
        76.76767677, 77.77777778, 78.78787879, 79.7979798, 80.80808081,
        81.81818182, 82.82828283, 83.83838384, 84.84848485, 85.85858586,
        86.86868687, 87.87878788, 88.88888889, 89.8989899, 90.90909091,
        91.91919192, 92.92929293, 93.93939394, 94.94949495, 95.95959596,
        96.96969697, 97.97979798, 98.98989899, 100],
      [0.30222580351987566, 0.36740581641828113, 0.44486640047610465, 0.5365155040875484, 0.644471979003516, 0.7710719316090513, 0.9188716265634826, 1.0906461747215381, 1.2893832132120813, 1.5182707843013306, 1.780678645666699, 2.080132302085822, 2.420279140847952, 2.804846183106011, 3.237589132449936, 3.722232610324231, 4.262401714003696, 4.861545313287816, 5.52285181148068, 6.249158427132681, 7.042855395949727, 7.905786835862798, 8.839150349512108, 9.843397743114553, 10.918139503882502, 12.062055884729304, 13.272817580345459, 14.547019029537452, 15.880127333690808, 17.266449631819512, 18.6991215138164, 20.17011868418191, 21.670293612119487, 23.189438328624277, 24.71637387002961, 26.239066137996613, 27.744767169906293, 29.220180016334453, 30.651644631689067, 32.02534142965776, 33.32750846667707, 34.544667623041114, 35.663854679037826, 36.67284785553376, 37.560389222808425, 38.31639339042676, 38.932138080208, 39.400431552504585, 39.715752394434986, 39.87435787177565, 39.87435787177565, 39.715752394434986, 39.40043155250458, 38.932138080208, 38.316393390426754, 37.56038922280842, 36.67284785553376, 35.66385467903782, 34.54466762304111, 33.32750846667705, 32.02534142965775, 30.651644631689063, 29.22018001633444, 27.744767169906275, 26.2390661379966, 24.716373870029596, 23.189438328624263, 21.67029361211948, 20.170118684181897, 18.69912151381639, 17.2664496318195, 15.8801273336908, 14.547019029537445, 13.27281758034545, 12.062055884729295, 10.918139503882495, 9.843397743114542, 8.839150349512096, 7.90578683586279, 7.042855395949718, 6.249158427132671, 5.522851811480674, 4.861545313287807, 4.262401714003689, 3.722232610324224, 3.237589132449931, 2.8048461831060045, 2.420279140847948, 2.080132302085817, 1.7806786456666952, 1.5182707843013272, 1.2893832132120786, 1.090646174721535, 0.9188716265634803, 0.7710719316090494, 0.6444719790035144, 0.5365155040875484, 0.44486640047610465, 0.36740581641828113, 0.30222580351987566]
    ]
    paperDetail.standard_deviation = !_.isNil(data?.standard_deviation) ? data.standard_deviation : '--'
  } finally {
    loading.value = false
  }
}
getPaperDetailFn()

const analysisVisible = ref(false)
const analysisInfo = ref<string[]>([])
const analysisLoading = ref(false)
const handleShowAnalysis = async () => {
  try {
    analysisLoading.value = true
    analysisVisible.value = true
    const res: any = await aipaperanalyzenew({
      id: props.id,
    })
    analysisInfo.value = _.isString(res) ? res.split('\n\n') : []
  } catch (error) {
    analysisInfo.value = []
  } finally {
    analysisLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.analysis {
  min-width: 1000px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  .spin-wrap {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4;
  }

  .analysis-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;

    .panel {
      background-color: #fff;
      height: 240px;
      padding: 24px 20px 0 24px;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-title {
        height: 28px;
        font-size: 20px;
        color: rgba(0,0,0,0.85);
        line-height: 28px;
      }
      .show-analysis-btn {
        height: 22px;
        margin-top: 4px;
        font-size: 12px;
        color: #5478EE;
        line-height: 22px;
        cursor: pointer;
      }
      .standard_deviation {
        margin-top: 13px;
        height: 17px;
        font-size: 12px;
        line-height: 17px;
      }

      .panel-content {
        flex: 1;
        min-height: 0;
        overflow: auto;
        position: relative;
      }
      .value {
        height: 100px;
        margin-top: 18px;
        font-size: 72px;
        font-weight: 600;
        text-align: center;
        color: #5478EE;
        line-height: 100px;
        display: flex;
        justify-content: center;
        align-items: baseline;
        .number {
          height: 100px;
          font-size: 72px;
          font-weight: 600;
          text-align: center;
          color: #5478EE;
          line-height: 100px;
        }
        .unit {
          display: block;
          height: 100px;
          font-size: 24px;
          font-weight: 600;
          text-align: center;
          color: #5478ee;
          line-height: 100px;
        }
      }
      .tip {
        height: 22px;
        margin-top: 4px;
        font-size: 14px;
        text-align: center;
        color: rgba(0,0,0,0.45);
        line-height: 22px;
      }
    }
    .table-panel {
      height: 100%;
    }
  }
}
</style>
