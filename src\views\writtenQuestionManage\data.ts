export const numberAlpht = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
export const lettersAlpht = 'ABCDEFGHIJK'
import { reactive } from 'vue'
export const judge = reactive([
  {
    content: '对',
    value: 'A'
  },
  {
    content: '错',
    value: 'B'
  }
])
export const options = [
  {
    content: '',
    value: 'A'
  },
  {
    content: '',
    value: 'B'
  },
  {
    content: '',
    value: 'C'
  },
  {
    content: '',
    value: 'D'
  }
]
export const sourceForm = {
  children: [],
  type: 0,
  official_cert: false,
  category: undefined,
  score: '0',
  body: '',
  answer: '',
  func_name: '',
  scorebasis: '',
  rtype: { type: undefined, is_list: false },
  difficulty: '',
  parameters: [{ name: '', type: '', is_list: false }],
  btestcase: [{ input: [], output: [] }],
  ptestcase: [{ input: [], output: [] }],
  sepscore: true,
  ordered: true,
  ignorecase: true,
  disablekws: {
    C: [],
    'C++': [],
    Java: [],
    Python: [],
    JavaScript: [],
    Go: [],
  },

  options: [
    {
      content: '',
      value: 'A'
    },
    {
      content: '',
      value: 'B'
    },
    {
      content: '',
      value: 'C'
    },
    {
      content: '',
      value: 'D'
    }
  ],
  tags: []
}
export const replaceFields = {
  children: 'children',
  key: 'subjectCategoryUUID',
  value: 'subjectCategoryUUID',
  label: 'name'
}
export const questionsObj = [
  {
    questionKey: 0,
    questionCate: '单选题'
  },
  {
    questionKey: 1,
    questionCate: '多选题'
  },
  {
    questionKey: 2,
    questionCate: '判断题'
  },
  {
    questionKey: 3,
    questionCate: '问答题'
  },
  {
    questionKey: 5,
    questionCate: '填空题'
  },
  {
    questionKey: 6,
    questionCate: '排序题'
  }
]
