<!-- eslint-disable ts/ban-ts-comment -->
<script lang="ts" setup>
import { blobToDataURI, compressAndConvertToWebP } from '@/utils/common'
import { message } from 'ant-design-vue'

import _ from 'lodash'

import BlotFormatter from 'quill-blot-formatter/dist/BlotFormatter'
import ImageUploader from 'quill-image-uploader'
import { onMounted, ref, watch } from 'vue'
import CustomImageSpec from './modules/customImageSpec'
import FormulaModule from './modules/formulaModule'
// import PasteSmart from 'quill-paste-smart'
import PasteSmart from './modules/paste'

const props = withDefaults(
  defineProps<{
    content: string
    text?: string
  }>(),
  {
    content: '',
    text: '',
  },
)
const emits = defineEmits<{
  (event: 'update:content', content: string): void
  (event: 'update:text', content: string): void
}>()
const { before, after } = inject('ImageAlignActionHook', () => ({
  onImageAlignBefore: () => {},
  onImageAlignAfter: () => {},
}), true) as any

const editorRef = ref()

const defaultToolBar = [['bold', 'italic', 'underline'], ['image'], ['formula']]
const lastEnterChar = ''

watch(
  () => props.content,
  (val) => {
    // 如果看上去是空，那么直接置空
    if (val.includes('img')) 
      return
    if (val.includes('formula')) 
      return
    const text = editorRef.value.getText().replace(/\n/g, '')
    if (text.trim()) 
      return
    if (val) {
      emits('update:content', '')
    }
  },
)

const modules = [
  {
    name: 'formulaModule',
    module: FormulaModule,
  },
  {
    name: 'blotFormatter',
    module: BlotFormatter,
    options: {
      customHook: {
        onImageAlignBefore: before,
        onImageAlignAfter: after,
      },
      specs: [CustomImageSpec],
    },
  },
  {
    name: 'imageUploader',
    module: ImageUploader,
    options: {
      upload: (file: File) => {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve, reject) => {
          if (file.size / 1024 > 256) {
            message.error('图片大小不能超过256kb')
            reject()
          }
          else {
            const webpSrc = await compressAndConvertToWebP(file, 0.3) as string
            const normalSrc = await blobToDataURI(file) as string
            resolve(webpSrc.length > normalSrc.length ? normalSrc : webpSrc)
          }
        })
      },
    },
  },
  {
    name: 'clipboard',
    module: PasteSmart,
    options: {
      allowed: {
        tags: ['strong', 'b', 'u', 'i', 'p', 'br', 'em'],
        attributes: ['href', 'rel', 'target', 'class'],
      },
      substituteBlockElements: true,
      hooks: {
        beforeSanitizeElements() {
          // debugger
        },
        uponSanitizeElement(node, data, config) {
          // debugger
          // console.log(node);
        },
        afterSanitizeElements() {
          // debugger
        },
        beforeSanitizeAttributes(node: any) {
          if (node.nodeName && node.nodeName === '#text') {
            // 对 < 这类文本进行转义
            // node.textContent = transferStr(node.textContent)
          }
        },
        uponSanitizeAttribute() {
          // debugger
        },
        afterSanitizeAttributes() {
          // debugger
        },
        beforeSanitizeShadowDOM() {
          // debugger
        },
        uponSanitizeShadowNode() {
          // debugger
        },
        afterSanitizeShadowDOM() {
          // debugger
        },
      },
    },
  },
]

function handleTextChange(e?: any) {
  const html = editorRef.value.getHTML()
  emits('update:content', html)
  const text = editorRef.value.getText().slice(0, -1)
  emits('update:text', text)
}

const deboounceHandleTextChange = _.debounce(() => handleTextChange(), 1000)

function getText() {
  // 用于复杂编辑按钮切换至普通编辑，获取纯文本，这里需要trim一下
  return editorRef.value.getText().slice(0, -1).trim()
}

// 在光标处插入指定文本
function insetText(text: string) {
  if (!text) 
    return
  const quill = editorRef.value.getQuill()
  const range = quill.getSelection(true)
  if (range != null) {
    const index = range.index + range.length
    quill.insertText(index, text, 'user')
  }
}

defineExpose({
  getText,
  insetText,
})

// 首先，创建一个回调函数来处理DOM变化
function handleMutation(mutationList, observer) {
  for (const mutation of mutationList) {
    if (mutation.target.tagName === 'IMG' && mutation.type === 'attributes' && mutation.attributeName === 'data-align') {
      deboounceHandleTextChange()
    }
  }
}

// 然后，创建一个新的MutationObserver实例，并把你的回调函数传递给它
const observer = new MutationObserver(handleMutation)

// 配置观察选项：
const config = { attributes: true, childList: true, subtree: true }

// 开始观察
onMounted(() => {
  const ele: any = document.querySelector('.ql-editor')
  observer.observe(ele, config)
})
</script>

<template>
  <QuillEditor
    ref="editorRef"
    :content="content"
    content-type="html"
    :toolbar="defaultToolBar"
    :modules="modules"
    theme="snow"
    @text-change="handleTextChange"
    @click.prevent
  />
</template>

<style lang="less">
.ql-container {
  width: 100%;
  .ql-editor {
    resize: vertical;
    overflow: auto;
  }

  .ql-tooltip {
    &[data-mode='formula'] {
      left: 0 !important;
      top: -50px !important;
    }
  }
}
</style>
