<template>
  <div>
    <div class="paper-allocate-container">
      <h3 class="paper-allocate-title">
        <span style="color: rgba(0, 0, 0, 0.45)"><span @click="router.back()">能力测评 /</span></span
        ><span>关联考生《{{ route.query.name }}》</span>
      </h3>
      <div class="paper-allocate">
        <!-- 试卷header -->
        <div class="paper-allocate-header">
          <div class="header-left">
            <a-input-search
              v-model:value.trim="searchContent"
              placeholder="请输入关键词"
              allow-clear
              @search="getSearchData"
            />
          </div>
          <div class="header-right" v-if="!(route.query.hiddenAction === 'true')">
            <a-button type="primary" @click="allocateMulStu">批量关联</a-button>
            <a-button @click="removeMulStu">批量移除</a-button>
          </div>
        </div>
        <div class="table-wrapper">
          <a-table
            :columns="data.columns"
            :data-source="data.stuList"
            :row-key="(record:any) => record.id"
            :loading="data.loading"
            :row-selection="!(route.query.hiddenAction === 'true') ? {
              selectedRowKeys: selectedStudent,
              onSelect: onSelect,
              onSelectAll: onSelectAll
            } : null"
            :pagination="paginationConfig"
            @change="handleTableChange"
            :scroll="{ x: 1200 }"
            @resizeColumn="(w: any, col: any) => col.width = w"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <div style="display: flex; align-items: center">
                  <span
                    style="
                      width: 8px;
                      height: 8px;
                      display: inline-block;
                      border-radius: 50%;
                      margin-right: 4px;
                    "
                    :style="{
                      backgroundColor: record.status == 1 ? '#52C41A' : '#D9D9D9'
                    }"
                  ></span>
                  {{ record.status ? '已关联' : '未关联' }}
                </div>
              </template>
              <template v-else-if="column.key === 'action'">
                <span>
                  <span
                    style="color: #5478ee; cursor: pointer; padding-left: 10px"
                    @click="handleAllocate(record)"
                  >
                    {{ record.status ? '移除' : '关联' }}</span
                  >
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getpaperstudept } from '@/api/admin/resumeManage'
import { queryRelatedResume, relatestu, delassociate } from '@/api/admin/paperManage'
import { assessmentrelatestu, associateabilityassessment, delassociateabilityassessment, getassessmentstudept } from '@/api/admin/abilityTest'
import { message } from 'ant-design-vue'

const route = useRoute()

// 试卷配置数据
const defaultColumns = [
  {
    title: '考生姓名',
    dataIndex: 'username',
    key: 'username',
    ellipsis: true,
    width: 370,
    resizable: true,
  },
  {
    dataIndex: 'dept_name',
    title: '报考部门',
    width: 370,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '关联状态',
    dataIndex: 'status',
    key: 'status',
    width: 370,
    ellipsis: true,
    resizable: true,
    filters: [
        { text: '已关联', value: true },
        { text: '未关联', value: false },
    ],
  },
  {
    title: '关联途径',
    dataIndex: 'related_path',
    key: 'related_path',
    customRender: ({ text }: any) => ['手动', '邀请码', '邮件'][text] || '-',
    filters: [
        { text: '手动', value: 0 },
        { text: '邀请码', value: 1 },
        { text: '邮件', value: 2 },
    ],
    width: 370,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '操作',
    width: 250,
    fixed: 'right',
    key: 'action'
  }
]
const data = reactive({
  columns: defaultColumns,
  stuList: [],
  loading: false,
  isVisible: false
})

watch(() => route.query.hiddenAction, (value) => {
  if (value === 'true') data.columns = defaultColumns.filter(el => el.key !== 'action')
  else data.columns = defaultColumns
}, {
  immediate: true,
})

// 获取报考部门列表
let deptList = ref<any[]>([])
async function getStudentDept() {
  deptList.value = await getassessmentstudept({ paper: route.query.id }) as any
}
getStudentDept().then(() => {
  data.columns.find(item => item.title === '报考部门')!.filters = deptList.value
})

// 快速搜索
const searchContent = ref('')
const otherParams = ref<{
  order_field?: string
  order_type?: 'ascend' | 'descend'
}>({})
const getSearchData = () => {
  paginationConfig.value.current = 1
  getStuList()
}

const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: number) => '总条数：' + total,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

const handleTableChange = (pagination: any, filters: any = {}, sorter: any = {}) => {
  // 处理分页
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  // 处理排序
  otherParams.value.order_type = sorter.order
  otherParams.value.order_field = sorter.order ? sorter.field : undefined
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getStuList()
}

async function handleAllocate(record: any) {
  data.loading = true
  // 移除
  try {
    if (record.status) {
      let res = await delassociateabilityassessment({ assessment: route.query.id as string, students: [record.id] })
      if (res?.[0] === record.username) return message.error('该考生已参加考试，无法移除') 
      message.success('移除成功!')
    } else {
      // 关联
      await associateabilityassessment({ assessment: route.query.id as string, students: [record.id] })
      message.success('关联成功!')
    }
    getStuList()
  } catch (error) {
    
  } finally {
    data.loading = false
  }
}

const allocateMulStu = () => {
  if (!selectedStudent.value.length) {
    message.error('请先勾选需要批量关联的学生')
    return
  }
  data.loading = true
  associateabilityassessment({
    assessment: route.query.id as string,
    students: selectedStudent.value
  }).then(() => {
    message.success('批量关联成功!')
    selectedStudent.value.length = 0
    getStuList()
  })
}

async function removeMulStu() {
  if (!selectedStudent.value.length) return message.error('请先勾选需要批量移除的学生')
  data.loading = true
  let res = await delassociateabilityassessment({ assessment: route.query.id as string, students: selectedStudent.value }) as any
  if (!res?.length) message.success('批量移除成功!')
  else if (res?.length === selectedStudent.value.length) message.error('考生已参加考试，无法移除')
  else message.success('移除成功，已参加考试的考生无法移除')
  selectedStudent.value.length = 0
  getStuList()
}

const selectedStudent = <any>ref([])
const onSelect = (record: any, selected: boolean) => {
  if (!selected) {
    // 取消勾选,删除对应的数组项
    selectedStudent.value.map((item: any, index: number) => {
      if (item === record.id) {
        selectedStudent.value.splice(index, 1)
      }
    })
  }
  if (selected) {
    // 点击勾选,添加到selectedStudent数组
    selectedStudent.value.push(record.id)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedStudent.value.push(item.id)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedStudent.value.map((x: any, indey: number) => {
        if (item.id === x) {
          selectedStudent.value.splice(indey, 1)
        }
      })
    })
  }
}

async function getStuList() {
  data.loading = true
  try {
    let res = await assessmentrelatestu({
      condition: searchContent.value,
      paper: route.query.id,
      page: paginationConfig.value.current,
      per_page: paginationConfig.value.pageSize,
      ...otherParams.value
    }) as any
    data.stuList = res.data
    paginationConfig.value.total = res.total
  } finally {
    data.loading = false
  }
}

const router = useRouter()

getStuList()
</script>

<style lang="less" scoped>
.paper-allocate-container {
  min-height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
</style>
<style lang="less">
.paper-allocate-container {
  // height: 100%;
  // overflow: hidden;
  height: calc(100% + 40px);
  padding: 0 20px 20px 20px;
  .paper-allocate-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .paper-allocate {
    flex: 1;
    // height: calc(100% - 48px);
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 10px;
    background-color: #fff;
    .paper-allocate-header {
      margin: 8px 0;
      display: flex;
      padding-bottom: 10px;
      justify-content: space-between;
      align-items: center;

      .header-right {
        display: flex;
        align-items: center;
        .ant-btn {
          border: 1px solid rgba(0, 0, 0, 0.15);
          border-radius: 8px;
          font-size: 14px;
          margin-left: 10px;
        }
      }
    }
    .table-bar {
      height: 60px;
      line-height: 60px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ant-checkbox-wrapper {
        font-size: 15px;
      }
      .show-columns {
        font-size: 15px;
        span {
          margin-right: 10px;
        }
      }
    }
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    text-align: left;
    &:last-child {
      padding-left: 22px !important;
    }
    height: 38px !important;
    background: #f1f4fe !important;
    padding: 8px 8px 8px 10px !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }
  .table-wrapper {
    padding: 0 11px;
  }
}
</style>
