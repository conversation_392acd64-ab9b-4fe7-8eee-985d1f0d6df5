<template>
  <div>
    <div class="paper-score-container">
      <h3 class="paper-manage-title">
        <span style="color: rgba(0, 0, 0, 0.45)"><span @click="backtoExam">试卷管理 /</span></span
        ><span>查看成绩《{{ route.query.name }}》</span>
      </h3>
      <div class="paper-manage">
        <!-- 试卷header -->
        <div class="paper-manage-header">
          <div class="header-left">
            <a-input-search
              v-model:value.trim="searchContent"
              placeholder="请输入考生姓名"
              allow-clear
              @search="getUserList"
            />
          </div>
          <div class="header-right">
            <template v-if="route.query.hiddenPublish !== 'true'">
              <a-button v-if="!published" type="primary" :disabled="!allow_publish" :loading="publishedBtnLoading" @click="showPublishGradeForm = true">发布成绩</a-button>
              <a-popconfirm v-else title="确定撤销发布？" ok-text="确定" cancel-text="取消" @confirm="cancelPublish" :overlayInnerStyle="{ color: 'red' }">
                <a-button :loading="publishedBtnLoading">撤销发布</a-button>
              </a-popconfirm>
            </template>
            <a-button @click="exportGrades">导出成绩</a-button>
            <a-button @click="handleShowAnalysis">试卷分析</a-button>
          </div>
        </div>
        <div class="table-wrapper">
          <a-table
            :columns="data.columns"
            :data-source="list"
            :row-key="(record:any) => record.id"
            :loading="data.loading"
            :pagination="paginationConfig"
            :scroll="{ x: 1200 }"
            @change="handleTableChange"
            @resizeColumn="(w: any, col: any) => col.width = w"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <div style="display: flex; align-items: center;">
                  <span style="width: 8px;height: 8px;display: inline-block;border-radius: 50%;margin-right: 4px;" :style="{ backgroundColor: record.completedName == '已交卷' ? '#52C41A' : record.completedName == '未交卷' ? '#FAAD14' : '#D9D9D9' }"></span>
                  {{ record.completedName }}
                </div>
              </template>
              <template v-if="column.key === 'total'">
                <span>
                  <span>{{ record.completedName == '未交卷' ? '-' : record.total }}</span>
                </span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button type="link" :class="{ disabled: record.completedName !== '已交卷' }" @click="queryScoreDetail(record)">试卷</a-button>
                <a-divider type="vertical" />
                <a-button type="link" @click="queryResume(record)">简历</a-button>
                <a-divider type="vertical" />
                <a-button type="link" :class="{ disabled: record.completedName !== '已交卷' || !isConfigMonitor }" @click="queryMonitorPhotos(record)">监控</a-button>
                <a-divider type="vertical" />
                <a-button type="link" :class="{ disabled: record.completedName !== '已交卷' }" @click="handleAnalysisStudent(record)">分析</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
    <PreviewPaper
      :isVisible="data.isVisible"
      :data="{ name: route.query.name, id: route.query.id, username, score: totalScore }"
      :studentId="curStudentId"
      :analyzable="true"
      :scoreEditable="!published"
      @closePreviewPaper="onPreviewPaperClose"
      v-model:studentPaperAnalysisStr="studentPaperAnalysisStr"
    ></PreviewPaper>

    <resume-detail
      v-if="resumeDetailVisible"
      :resume-detail-visible="resumeDetailVisible"
      :resume-detail-id="previewResumeId"
      @closePreviewResume="closePreviewResume"
    />
    <CustomDialog :dialogVisible="showPublishGradeForm" :maskClosable="false" :txtConfig="{ title: '发布成绩', confirm: '发布' }" :needDoubleConfirmation="true" width="550px" @updateDialog="publishGrade" @closeDialog="showPublishGradeForm = false" @cancelDialog="showPublishGradeForm = false">
      <PaperGradePublish ref="publishRef" v-if="showPublishGradeForm" :examInfo="scoreDetail" :paperId="(route.query.id as string)"></PaperGradePublish>
    </CustomDialog>
    <PaperAnalysis
      :visible="analysisVisible"
      :loading="analysisLoading"
      v-bind="analysisInfo"
      @close="analysisVisible = false" />
    <a-modal title="答卷分析" v-model:visible="isStudentAnalysisModalShow" :footer="null" :destroyOnClose="true">
      <a-spin :spinning="studentAnalysisLoading">
        <div class="ai-text-panel">
          <!-- {{ studentPaperAnalysisStr }} -->
          <FoldText 
            :text="studentPaperAnalysisStr" 
            :fold-line-clamp="999"
            background-color="#f5f5f5"
            :feedback-params="{
              apiname: 'aistudentpaperanalysis',
              input: { studentId: curStudentId, paperId: route.query.id },
              output: studentPaperAnalysisStr
            }"
            v-if="!studentAnalysisLoading"
          ></FoldText>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, ref, computed, watch, onActivated } from 'vue'
import { getPaperDetail, viewgrades } from '@/api/admin/paperManage'
import { aipaperanalyze } from '@/api/admin/statisticAnalysis'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { useStore } from 'vuex'
import _ from 'lodash'
import PreviewPaper from './components/PreviewPaper.vue'
import ResumeDetail from '@/views/ResumeManage/resumeDetail.vue'
import PaperGradePublish from '@/pages/paperManage/PaperGradePublish.vue'
import CustomDialog from '@/components/CustomDialog.vue'
import { aistudentpaperanalysis, ispublished, revokescore } from '@/api/exam'
import { message } from 'ant-design-vue'
import PaperAnalysis from './components/PaperAnalysis.vue'
import FoldText from '@/components/FoldText.vue'
import HttpConfig from '@/config/index'
const store = useStore()

// 试卷配置数据
const data = reactive({
  columns: [
    {
      title: '考生姓名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      ellipsis: true,
      resizable: true
    },
    { 
      title: '性别',
      dataIndex: 'sexName',
      key: 'sexName',
      width: 100,
      ellipsis: true,
      resizable: true,
      filters: [
          { text: '男', value: '男' },
          { text: '女', value: '女' },
      ],
    },
    { title: '院校', dataIndex: 'graduatedSchool', key: 'school', width: 200, ellipsis: true, resizable: true },
    { title: '电子邮箱', dataIndex: 'email', key: 'email', width: 220, ellipsis: true, resizable: true },
    {
      title: '分数',
      dataIndex: 'tscore',
      key: 'tscore',
      width: 120,
      align: 'right',
      ellipsis: true,
      resizable: true,
      sorter: true
    },
    {
      title: '是否通过',
      dataIndex: 'passed',
      width: 160,
      key: 'passed',
      ellipsis: true,
      resizable: true
    },
    {
      title: '考试状态',
      dataIndex: 'completedName',
      key: 'status',
      width: 120,
      ellipsis: true,
      resizable: true,
      sorter: true
    },
    { title: '实际开始时间', dataIndex: 'start', key: 'start', width: 200, ellipsis: true, resizable: true },
    { title: '实际结束时间', dataIndex: 'completetime', key: 'completetime', width: 200, ellipsis: true, resizable: true },
    {
      title: '断网次数',
      dataIndex: 'neterror',
      width: 160,
      key: 'neterror',
      ellipsis: true,
      resizable: true
    },
    {
      title: '切屏次数',
      dataIndex: 'slicecount',
      width: 160,
      key: 'slicecount',
      ellipsis: true,
      resizable: true
    },
    {
      title: '操作',
      width: 260,
      fixed: 'right',
      key: 'action'
    }
  ],
  stuList: [],
  loading: false,
  isVisible: false
})

const list = ref([])

// 快速搜索
const searchContent = ref('')

const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: number) => '总条数：' + total,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

function handleTableChange(pagination: any, filters: any = {}, sort: any = {}) {
  // 处理分页
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  list.value = data.stuList.filter(item => {
    return Object.entries(filters).every(([k, v]: any) => {
      if (v === null) return true
      return v.includes(item[k])
    })
  })
  list.value.sort((a, b) => {
    if (sort.order === 'descend') return b[sort.field] - a[sort.field]
    if (sort.order === 'ascend') return a[sort.field] - b[sort.field]
    return 0
  })
  getUserList(sort)
}

const route = useRoute()
const id = route.query.id as string
async function getUserList(sort?: any) {
  data.loading = true
  try {
    let params = {
      basePager: {
        current: paginationConfig.value.current,
        size: paginationConfig.value.pageSize
      },
      templeteId: id,
      templeteUUID: route.query.UUID,
      condition: searchContent.value,
      completedOrder: 'asc'
    }
    if(sort) {
      params.completedOrder = sort.order === 'descend' ? 'asc' : 'desc'
    }
    let res = await viewgrades(params) as any
    data.stuList = res.records
    paginationConfig.value.total = res.total
    list.value = res.records
  } finally {
    data.loading = false
  }
}

const router = useRouter()
const queryMonitorPhotos = (item: any) => {
  if (!isConfigMonitor.value) {
    return message.info('本场考试未要求开启摄像头')
  }
  if (item.completedName !== '已交卷') {
    return message.info('该考生' + item.completedName)
  }
  router.push({
    path: '/admin/monitor-records',
    query: {
      id:item.id,
      studentid: item.id,
      paperid: route.query.id,
      stupid: item.stupid,
      templeteUUID: item.templeteUUID,
      studentUUID: item.studentUUID,
      papername: route.query.name
    }
  })
}

// 预览简历
const resumeDetailVisible = ref(false)
const previewResumeId = ref()
const queryResume = (record: any) => {
  previewResumeId.value = record.studentId
  resumeDetailVisible.value = true
}
const closePreviewResume = () => {
  resumeDetailVisible.value = false
}

const backtoExam = () => {
  router.push({ name: 'paperManage', params: { page: route.query.page as string } })
}

// 查看成绩
const curStudentId = ref('')
const username = ref('')
const queryScoreDetail = (item: any) => {
  if (item.completedName !== '已交卷') {
    return message.info('该考生' + item.completedName)
  }
  curStudentId.value = item.studentId
  username.value = item.username
  studentPaperAnalysisStr.value = item.student_paper_analysis_str
  data.isVisible = true
}
function onPreviewPaperClose(needRefresh: boolean = false) {
  data.isVisible = false
  if (needRefresh) {
    getUserList()
  }
}

// 获取考试配置
const isConfigMonitor = ref(false)
const totalScore = ref(0)
async function getPaperConfig() {
  let res = await getPaperDetail({ id: route.query.id })
  isConfigMonitor.value = res.computermonitor === 1 || res.phonemonitor === 1
  totalScore.value = res.score
}

// 查看是否已经发布
const published = ref(false)
const allow_publish = ref(false)
const publishedBtnLoading = ref(false)
async function checkIspublished() {
  publishedBtnLoading.value = true
  let res = await ispublished({ templeteId: route.query.id as string }) as any
  published.value = res.published
  allow_publish.value = res.allow_publish
  publishedBtnLoading.value = false
}
watch(published, (value) => {
  // 发布之后才会有是否通过的筛选
  let target = data.columns.find(item => item.title === '是否通过')
  if (!target) return
  if (value) {
    Reflect.set(target, 'filters', [
      { text: '通过', value: '通过' },
      { text: '不通过', value: '不通过' },
    ])
  } else {
    Reflect.deleteProperty(target, 'filters')
  }
}, { immediate: true })

// 发布成绩
const showPublishGradeForm = ref(false)
const publishRef = ref<InstanceType<typeof PaperGradePublish>>()
async function publishGrade() {
  await publishRef.value?.publish()
  showPublishGradeForm.value = false
  checkIspublished()
  getUserList()
}

// 撤销发布
async function cancelPublish() {
  await revokescore({ templeteId: route.query.id as string })
  message.success('撤销成功')
  checkIspublished()
  getUserList()
}

const exportGrades = () => {
  // exportstuscore({ method: 'get', paper: route.query.id })

  axios
    .get(`${HttpConfig.baseApi}/papertemplate/stupaper/export`, {
      params: {
        templeteId: route.query.id
      },
      headers: {
        // Authorization: 'Bearer ' + store.state.accessToken
        Authorization: store.state.accessToken
      },
      responseType: 'blob' //服务器响应的数据类型，可以是 'arraybuffer', 'blob', 'document', 'json', 'text', 'stream'，默认是'json'
    })
    .then((res) => {
      if (!res) return
      let fileName = decodeURIComponent(
        res.headers['content-disposition'].split(';')[1].split('=')[1].replace(/\"/g, '')
      )
      console.log(fileName)
      const blob = new Blob([res.data], { type: 'application/octet-stream' }) // 构造一个blob对象来处理数据，并设置文件类型 excel：application/vnd.ms-excel zip: application/zip
      console.log(res.data)

      if (window.navigator.msSaveOrOpenBlob) {
        //兼容IE10
        navigator.msSaveBlob(blob, fileName)
      } else {
        const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
        const a = document.createElement('a') //创建a标签
        a.style.display = 'none'
        a.href = href // 指定下载链接
        a.download = fileName //指定下载文件名
        document.body.appendChild(a)
        a.click() //触发下载
        URL.revokeObjectURL(a.href) //释放URL对象
        document.body.removeChild(a)
      }
    })
}

const scoreDetail = ref({
  actual_candidates_nums: 0,
  all_scores: [],
  average_score: 0,
  candidates_nums: 0,
  median: 0,
  name: '',
  score: 0,
  published: false
})

// 考试分析
const analysisLoading = ref(false)
const analysisVisible = ref(false)
const analysisInfo = reactive<{ // 分析信息
  joinRate: number,
  passRate: number,
  saturationRate: number,
  diffRate: number,
  texts: string[],
}>({
  joinRate: 0, // 参加率
  passRate: 0, // 通过率
  saturationRate: 0, // 充实度
  diffRate: 0, // 区分度
  texts: [], // ai分析文字
})
const handleShowAnalysis = async () => { // 查看考试分析
  try {
    analysisVisible.value = true
    analysisLoading.value = true
    const res: any = await aipaperanalyze({ id })
    analysisInfo.joinRate = +((res.actual || 0) / (res.total || 0)).toFixed(2)
    analysisInfo.passRate = +((res.pass_count || 0) / (res.actual || 0)).toFixed(2)
    analysisInfo.saturationRate = +((res.avg_duration || 0) / (res.duration || 0)).toFixed(2)
    analysisInfo.diffRate = +(+res.distinction_to_one || 0).toFixed(2)
    analysisInfo.texts = _.isString(res?.aianalyze) ? res.aianalyze.split('\n\n') : []
  } catch(error) {
    analysisInfo.texts = []
  } finally {
    analysisLoading.value = false
  }
}

// 考生答卷分析
const isStudentAnalysisModalShow = ref(false)
const studentPaperAnalysisStr = ref('')
const studentAnalysisLoading = ref(false)
async function handleAnalysisStudent(item: any) {
  if (item.completedName !== '已交卷') {
    return message.info('该考生' + item.completedName)
  }
  isStudentAnalysisModalShow.value = true
  if (item.student_paper_analysis_str) {
    // 如果之前ai分析过就直接展示
    studentPaperAnalysisStr.value = item.student_paper_analysis_str
    return
  }
  try {
    studentPaperAnalysisStr.value = ''
    studentAnalysisLoading.value = true
    let res = await aistudentpaperanalysis({ studentId: item.studentId, templeteId: route.query.id as string })
    item.student_paper_analysis_str = res
    studentPaperAnalysisStr.value = res
    curStudentId.value = item.id
  } catch (error) {
    console.log(error)
    isStudentAnalysisModalShow.value = false
  } finally {
    studentAnalysisLoading.value = false
  }
}



onMounted(() => {
  getPaperConfig()
  checkIspublished()
  getUserList()
})

</script>

<style lang="less" scoped>
.ai-text-panel {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px 24px;
  min-height: 200px;
}
.paper-score-container {
  min-height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-container) {

  tr td .ant-btn.disabled span {
    color: rgba(0,0,0,0.25)!important;
    cursor: not-allowed;
  }
}
</style>
<style lang="less">
.paper-score-container {
  // height: 100%;
  // overflow: hidden;
  height: calc(100% + 40px);
  padding: 0 20px 20px 20px;
  .paper-manage-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .paper-manage {
    flex: 1;
    // height: calc(100% - 48px);
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;
    background-color: #fff;
    .paper-manage-header {
      display: flex;
      padding-bottom: 16px;
      justify-content: space-between;
      align-items: center;

      .header-right {
        display: flex;
        align-items: center;
        .ant-btn {
          border-radius: 8px;
          font-size: 14px;
          margin-left: 10px;
        }
      }
    }
    .table-bar {
      height: 60px;
      line-height: 60px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ant-checkbox-wrapper {
        font-size: 15px;
      }
      .show-columns {
        font-size: 15px;
        span {
          margin-right: 10px;
        }
      }
    }
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    text-align: left;
    &:first-child {
      padding-left: 8px !important;
    }
    &:last-child {
      padding-left: 22px !important;
    }
    background: #f1f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
  }
}
</style>
