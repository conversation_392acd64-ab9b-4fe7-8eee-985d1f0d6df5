@import './variable';
@import './transition';
@import './common.less';
@import './rewrite.less';
@import 'tailwind.css';
@import './element-plus.css';

button {
  all: unset;
}

html {
  font-size: 100px;
}

body,
div,
p,
ul,
ol,
dl,
dt,
dd,
li,
dl,
h1,
h2,
h3,
h4 {
  margin: 0;
  padding: 0;
  font-style: normal;
  font: 14px 'PingFang SC', 'Microsoft YaHei', Arial, Helvetica, sans-serif;
}

ol,
ul,
li {
  list-style: none;
}

img {
  border: 0;
  vertical-align: middle;
}

body {
  color: #121633;
  background: #fff;
  text-align: left;
}

a {
  color: #000000;
  text-decoration: none;
}

// 哀悼模式下页面全灰白
.memorial {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#grayscale");
  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
}

.ant-modal-confirm .ant-modal-content {
  border-radius: 8px;
  box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.1);

  .ant-modal-confirm-btns {
    .ant-btn {
      border-radius: 8px;
      font-size: 14px;
    }
  }
}

#nprogress .bar {
  background: @primary-color !important; //自定义颜色
}

.ant-table-content {
  &::-webkit-scrollbar {
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #e5e6e7;
  }

  &::-webkit-scrollbar-track {
    // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    background: transparent;
  }
}

// treeSelect公共样式
.ant-tree-select-dropdown {
  .ant-select-tree-node-content-wrapper {
    display: flex;
    align-items: center;
    flex-shrink: 0 !important;
  }
}

.ant-picker-ranges {
  .ant-tag-blue {
    background-color: transparent !important;
    border: none;
    color: black !important;
    font-size: 14px;
  }
}

.ant-table {
  .ant-table-row.active {
    .ant-table-cell {
      background-color: #daf1ff !important;

      .oper-btn,
      .ant-dropdown-trigger {
        color: #5478ee;
      }
    }
  }

  td.ant-table-cell {
    height: 45px;

    .ant-dropdown-trigger {
      color: #5478ee;
    }
  }

  .ant-table-cell {
    height: 32px;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 10px;
  }

  .ant-table-tbody>tr:hover:not(.ant-table-expanded-row)>td:not(:has(.empty-table)),
  .ant-table-tbody>tr.ant-table-row-selected td {
    background: #f0f4fe;
  }

  .ant-table-tbody>tr>td:has(.empty-table) {
    border: none;
  }

  .ant-table-sticky-scroll {
    display: none !important;
  }

  .ant-btn-link {
    height: 18px;
    font-weight: 400;
    font-size: 14px;
    padding-left: 4px;
    padding-right: 4px;

    &:not([disabled]) span {
      color: #5478ee !important;
    }
  }
}

.ant-table-pagination.ant-pagination {
  margin: 8px 0;

  &.mini {
    .ant-pagination-item-active {
      background: #5478ee;

      a {
        color: #fff;
      }
    }
  }
}

.ant-modal {
  .ant-modal-content {
    border-radius: 8px;

    .ant-modal-close-x {
      // width: 70px;
      width: 22px;
      height: 22px;
      line-height: 22px;
    }
  }

  .ant-modal-header {
    // padding: 32px 32px 0 32px;
    border-bottom-color: transparent;
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      font-size: 20px;
      font-weight: bold;
    }
  }

  .ant-modal-body {
    padding: 32px;

    .ant-modal-confirm-body {
      .anticon-info-circle {
        color: #6fa8f9;
      }
    }

    >.ant-spin-nested-loading {
      height: 100%;

      .ant-spin-container {
        height: 100%;
      }
    }
  }

  .ant-modal-footer {
    display: flex;
    justify-content: center;
    border-top: none;

    .ant-btn {
      font-size: 14px;
      border-radius: 8px;
      margin-right: 8px;
      margin-left: 0 !important;
    }
  }

}

.full-screen-modal {
  .ant-modal {
    width: 100%!important;
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    border-radius: 0 !important;
  }

  .ant-modal-header {
    border-radius: 0 !important;
  }

  .ant-modal-body {
    flex: 1;
    min-height: 0;
    background-color: #fff;
  }

  .ant-modal-footer {
    background-color: #fff;
    border-radius: 0;
  }
}

.ant-dropdown {
  .ant-dropdown-menu-item {
    &:hover {
      background-color: #f1f4fe;
      color: #5478EE;
    }

    &:active {
      background-color: #5478EE;
      color: #fff;
    }
  }
}

// 确定按钮居中
.ant-picker-ranges {
  .ant-picker-ok{
    margin: 6px auto !important;
  }
  
}
