// antd组件样式重写
.ant-tree-switcher {
  line-height: 32px;
}

.ant-tree-switcher.ant-tree-switcher-noop {
  display: none !important;
}

.ant-tree-node-content-wrapper {
  padding: 4px 0 4px !important;
}

.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open,
.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-close {
  display: flex !important;
  align-items: center;
}

.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal {
  display: flex !important;
  align-items: center;
}

.ant-tree-title {
  width: 100%;
  white-space: nowrap;
}

.common-modal {
  overflow: hidden;
  border-radius: 8px;

  .ant-modal-content {
    border-radius: 8px;
    box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.1);
  }

  .ant-modal-header {
    .ant-modal-title {
      font-size: 16px;
      padding-left: 8px;
      font-weight: bold;
    }
  }

  .ant-input {
    border-radius: 8px;
    font-size: 14px;
  }

  .ant-form-item-label>label {
    font-size: 14px;
    color: #626262;
  }

  .ant-modal-footer {
    display: flex;
    justify-content: center;

    .ant-btn {
      border-radius: 8px;
      font-size: 14px;
    }
  }
}

// --------------------------------------------tooltip--------------------------------------------
.ant-tooltip {
  .ant-tooltip-inner {
    border-radius: 4px;
    font-size: 12px;
  }
  &.light {
    .ant-tooltip-arrow-content {
      background-color: #fff;
    }

    .ant-tooltip-inner {
      background: #fff;
      color: #121633;
    }
  }
}

.ant-popover {
  .ant-popover-message>.anticon {
    top: 6px;
  }
}

.ant-drawer {
  .ant-drawer-header {
    border-bottom: none;
  }
}


// --------------------------------------------time-piker--------------------------------------------

.ant-picker-input > input {
  height: 22px;
  font-size: 13px;
  line-height: 22px;
  border-radius: 8px;
}

// --------------------------------------------input--------------------------------------------
.ant-input-search {
  .ant-input {
    font-size: 13px;
  }
}

// --------------------------------------------button--------------------------------------------
.ant-btn {
  &.disabled {
    span {
      color: rgba(0,0,0,0.25)!important;
      cursor: not-allowed;
    }
  }
}