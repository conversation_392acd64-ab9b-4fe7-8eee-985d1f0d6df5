<template>
  <a-drawer
    :title="$props.newsData!.id ? '编辑新闻' : '新建新闻'"
    :width="900"
    :visible="visible"
    :body-style="{ paddingBottom: '80px' }"
    @close="cancelSubmit"
  >
    <a-form ref="newsFormRef" :model="form" :rules="rules" layout="vertical">
      <a-row>
        <a-col :span="24">
          <a-form-item label="标题" name="title">
            <a-input v-model:value="form.title" placeholder="请输入新闻标题" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="摘要" name="abstract">
            <a-textarea v-model:value="form.abstract" placeholder="请输入新闻摘要" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="18">
          <a-form-item label="图片" name="image">
            <a-upload-dragger
              name="file"
              listType="picture-card"
              :showUploadList="false"
              :beforeUpload="beforeUpload"
              :customRequest="selfUpload"
            >
              <img v-if="form.image != ''" :src="form.image" style="width: 100%" alt="" />
              <div v-else>
                <p class="ant-upload-drag-icon">
                  <Icon icon="PictureOutlined" />
                </p>
                <p class="ant-upload-text">点击或者拖拽上传</p>
              </div>
            </a-upload-dragger>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="内容" name="content">
            <a-editor :content="form.content" @getHtml="getHtml" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '20px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1
      }"
    >
      <a-button style="margin-right: 8px" @click="cancelSubmit">取消</a-button>
      <a-button type="primary" @click="submitNewsContent">保存</a-button>
    </div>
  </a-drawer>
</template>
<script lang="ts">
import { defineComponent, reactive, ref, watch, nextTick } from 'vue'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import AEditor from '@/components/Editor/index.vue'
import { message } from 'ant-design-vue'
import { addNews } from '@/api/admin/newsManage'
export default defineComponent({
  components: {
    AEditor
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    newsData: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['cancelDrawer'],
  setup(props, { emit }) {
    const form = reactive({
      title: '',
      abstract: '',
      image: '',
      content: ''
    })
    const newsFormRef = ref()
    const newsId = ref('')
    const model = reactive({ cover: '' })
    const rules = {
      title: [{ required: true, message: '请输入新闻标题' }],
      abstract: [{ required: true, message: '请输入新闻摘要' }],
      image: [{ required: true, message: '请上传图片', trigger: 'change' }]
    }

    const visible = ref<boolean>(false)

    const beforeUpload = (file: any) => {
      const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/jpg' ||
        file.type === 'image/png' ||
        file.type === 'image/bmp'
      if (!isJPG) {
        message.error('请上传图片文件')
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('图片文件必须小于2MB!')
      }
      return isJPG && isLt2M
    }
    const selfUpload = ({ file }: any) => {
      new Promise((resolve) => {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(file)
        fileReader.onload = () => {
          resolve(fileReader.result)
          form.image = fileReader.result as string
        }
      })
    }
    const getHtml = (val: any) => {
      form.content = val
    }
    const handleChange = () => {}
    const AddNews = (data: any) => {
      addNews(data).then(() => {
        console.log('3242', props.newsData)
        message.success(!!newsId.value ? '编辑成功!' : '新增成功！')
      })
    }
    // 提交
    const submitNewsContent = () => {
      return new Promise((resolve, reject) => {
        newsFormRef.value
          .validate()
          .then(() => {
            const params = {
              action: !!newsId.value ? 'modify' : 'add',
              news: { ...form }
            }
            AddNews(params)
            visible.value = false
            emit('cancelDrawer', visible.value)
          })
          .catch((error: ValidateErrorEntity) => {
            reject(error)
          })
      })
    }
    // 取消
    const cancelSubmit = () => {
      visible.value = false
      newsId.value = ''
      emit('cancelDrawer', visible.value)
    }
    // 监听
    watch(
      () => props.isVisible,
      () => {
        visible.value = !!props.isVisible
        if (visible.value) {
          newsId.value = props.newsData.id
          nextTick(() => {
            // DOM 渲染后
            newsFormRef.value.resetFields()
            Object.assign(form, props.newsData)
          })
        }
      }
    )
    return {
      newsFormRef,
      newsId,
      form,
      model,
      rules,
      visible,
      handleChange,
      beforeUpload,
      selfUpload,
      getHtml,
      submitNewsContent,
      cancelSubmit,
      AddNews
    }
  }
})
</script>
