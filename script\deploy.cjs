#!/usr/bin/env node

const { execSync } = require('node:child_process')
const fs = require('node:fs')
const path = require('node:path')
const process = require('node:process')
const JSZip = require('jszip')
const { Client } = require('ssh2')
// import { execSync } from 'node:child_process'
// import fs from 'node:fs'
// import path, { dirname as __dirname } from 'node:path'
// import JSZip from 'jszip'
// import { Client } from 'ssh2'
// --- 配置 ---
const BUILD_COMMAND = 'pnpm build'
const DIST_FOLDER = 'dist'
const ZIP_FILE_NAME = 'dist.zip'
const REMOTE_HOST = '***********'
const REMOTE_USER = 'root'
const REMOTE_PASSWORD = '?qdsxP%Rb8De' // !! WARNING: Hardcoding password is insecure !!
// const REMOTE_COMMANDS = 'cd /opt/ && ls' // Example remote commands. Adjust 'cp' source path as needed after upload.

// It seems the original request 'cp /opt && ls' might be a typo or incomplete.
// 'cp /opt' would usually copy the '/opt' directory itself (which is likely not intended).
// I've put a more realistic example: assuming you would upload the zip to a location
// like /tmp/dist.zip on the server, and then want to copy it to /opt and list /opt.
// The ssh2 exec method runs a single command string. To run multiple sequential commands,
// you typically chain them with '&&' or ';'.
// For this example, I'll execute 'cp /opt && ls' literally as requested first,
// but add a note about its potential issue. Then provide a more realistic alternative in the comments.
const REQUESTED_REMOTE_COMMANDS_LITERAL = [
  'cd /opt', // Change directory on the remote host
  // 'rm -rf ./dist ./dist.zip', // Remove existing dist and dist.zip
  'unzip ./dist.zip -d ./dist', // Unzip the uploaded zip to /opt/dist (using -qo for quiet overwrite)
  // Copy from remote host /opt/dist to container /tmp/dist
  // Execute commands INSIDE the container non-interactively
  // Use bash -c to run multiple commands or complex logic inside the container
  // 'docker exec exam-ai-interviews bash -c "cd /usr/share/nginx && ls"' // Combine cd, rm, mv, ls inside the container
]
const DOCKER_COMMANDS = [
  'docker cp /opt/dist sleepy_jones:/tmp/dist',
  '[ $? -le 1 ]',
  'docker exec sleepy_jones bash -c "rm -rf /usr/share/nginx/html && mv /tmp/dist /usr/share/nginx/html"', // Combine cd, rm, mv, ls inside the container
  'rm -rf /opt/dist /opt/dist.zip',
]
// Use this one if you want the literal command:
// const REMOTE_COMMANDS_TO_EXECUTE = REQUESTED_REMOTE_COMMANDS_LITERAL;
// Use this one for a more likely scenario (assuming you upload the zip first):
// Note: This script does *not* include the upload step. It only connects and runs commands.
// Uploading the zip would require using SFTP via ssh2 or another method.
// If you want to upload the zip first, you'd need to add SFTP logic.
// For now, we'll stick to the commands requested after connection.
const REMOTE_COMMANDS_TO_EXECUTE = REQUESTED_REMOTE_COMMANDS_LITERAL

// --- 辅助函数：递归读取目录并添加到 Zip ---
async function addDirectoryToZip(zip, dirPath, baseDir) {
  const entries = fs.readdirSync(dirPath)

  for (const entryName of entries) {
    const entryPath = path.join(dirPath, entryName)
    const relativePath = path.relative(baseDir, entryPath)
    const stats = fs.statSync(entryPath)

    if (stats.isFile()) {
      zip.file(relativePath, fs.readFileSync(entryPath))
    }
    else if (stats.isDirectory()) {
      // Create folder entry in zip (optional, files within will create it)
      // zip.folder(relativePath); // Explicitly creating folder entry might be needed depending on JSZip version/usage
      // Recurse into subdirectory
      await addDirectoryToZip(zip, entryPath, baseDir)
    }
    // Ignore other types (symlinks etc.) for simplicity
  }
}

// --- 主执行流程 ---
async function deploy() {
  console.log('--- 开始部署流程 ---')

  // Step 1: 执行 pnpm build
  console.log(`\n1. 正在执行构建命令: ${BUILD_COMMAND}`)
  try {
    execSync(BUILD_COMMAND, { stdio: 'inherit' }) // stdio: 'inherit' 显示子进程的输出
    console.log('   构建成功！')
  }
  catch (error) {
    console.error('   构建失败:', error.message)
    process.exit(1) // 构建失败则退出
  }

  // Step 2: 检查 dist 文件夹是否存在
  const distPath = path.join(__dirname, '../', DIST_FOLDER)
  if (!fs.existsSync(distPath) || !fs.statSync(distPath).isDirectory()) {
    console.error(`\n错误: 构建完成后未找到 "${DIST_FOLDER}" 文件夹，请检查您的构建脚本。`)
    process.exit(1)
  }
  console.log(`\n2. 找到 "${DIST_FOLDER}" 文件夹.`)

  // Step 3: 压缩 dist 文件夹
  console.log(`\n3. 正在将 "${DIST_FOLDER}" 压缩为 "${ZIP_FILE_NAME}"...`)
  const zip = new JSZip()
  try {
    await addDirectoryToZip(zip, distPath, distPath) // Add contents of dist, using distPath as base so zip contents are relative to dist

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' })
    const zipFilePath = path.join(__dirname, ZIP_FILE_NAME)
    fs.writeFileSync(zipFilePath, zipBuffer)
    console.log(`   "${ZIP_FILE_NAME}" 创建成功: ${zipFilePath}`)
  }
  catch (error) {
    console.error('   压缩失败:', error.message)
    process.exit(1)
  }

  // Step 4 & 5: 通过 SSH 连接并执行远程命令
  console.log(`\n4. 正在通过 SSH 连接到 ${REMOTE_USER}@${REMOTE_HOST}...`)

  const conn = new Client()

  conn.on('ready', async () => {
    console.log('   SSH 连接成功！')
    await new Promise((resolveOuter, reject) => {
      conn.sftp(async (err, sftp) => {
        if (err) {
          console.error('   SFTP 连接失败:', err.message)
          conn.end()
          process.exit(1)
          return
        }
        console.log('   SFTP 连接成功！')
        await new Promise((resolve, reject) => {
          sftp.fastPut('./script/dist.zip', '/opt/dist.zip', (err) => {
            if (err) {
              console.error('   SFTP fastPut 错误:', err)
              reject(err)
            }
            else {
              console.log('   SFTP fastPut 完成.')
              resolve()
              resolveOuter()
            }
          })
        })
      })
    })

    console.log(`\n5. 正在远程执行命令: "${REMOTE_COMMANDS_TO_EXECUTE}"...`)

    // Note: 'cp /opt' might not do what you expect if /opt is a directory.
    // A more common scenario might be uploading the zip first, then copying it.
    // This script only executes the command string provided after connection.

    await new Promise((resolve, reject) => {
      conn.exec(REMOTE_COMMANDS_TO_EXECUTE.concat(DOCKER_COMMANDS).join(';'), (err, stream) => {
        if (err) {
          console.error('   远程命令执行失败:', err.message)
          conn.end()
          process.exit(1)
          return
        }

        console.log('--- 远程命令输出 ---')
        stream.on('data', (data) => {
          process.stdout.write(data) // 直接输出到控制台
        })
        stream.on('stderr', (data) => {
          process.stderr.write(data) // 输出错误信息到 stderr
        })
        stream.on('close', (code, signal) => {
          console.log('\n--- 远程命令执行完毕 ---')
          if (code !== 0) {
            console.error(`   远程命令退出码: ${code} (非零表示失败)`)
            // conn.end()
          }
          else {
            console.log('   远程命令成功执行。')
            // Step 6: 保持会话 (这里的exec执行完就会关闭stream，然后我们end connection)
            // 如果需要保持一个交互式会话，需要使用 conn.shell()，但这超出了执行固定脚本的需求。
            // 对于执行完命令就结束的流程，直接关闭连接是正确的。
            console.log('\n   正在关闭 SSH 连接...')
            // conn.end()
            console.log('   SSH 连接已关闭。')
            console.log('\n--- 部署流程完成 ---')
            // process.exit(0)
          }
          resolve()
        })
      })
    })

    // conn.exec(DOCKER_COMMANDS.join(';'), (err, stream) => {
    //   console.log(' Docker 命令开始执行')
    //   if (err) {
    //     console.error('   Docker 命令执行失败:', err.message)
    //     conn.end()
    //     process.exit(1)
    //     return
    //   }

    //   console.log('--- Docker 命令输出 ---')
    //   stream.on('data', (data) => {
    //     process.stdout.write(data) // 直接输出到控制台
    //   })
    //   stream.on('stderr', (data) => {
    //     process.stderr.write(data) // 输出错误信息到 stderr
    //   })
    //   stream.on('close', (code, signal) => {
    //     console.log('\n--- Docker 命令执行完毕 ---')
    //     if (code !== 0) {
    //       console.error(`   Docker 命令退出码: ${code} (非零表示失败)`)
    //       conn.end()
    //       process.exit(1)
    //     }
    //     else {
    //       console.log('   Docker 命令成功执行。')
    //       // Step 6: 保持会话 (这里的exec执行完就会关闭stream，然后我们end connection)
    //       // 如果需要保持一个交互式会话，需要使用 conn.shell()，但这超出了执行固定脚本的需求。
    //       // 对于执行完命令就结束的流程，直接关闭连接是正确的。
    //       console.log('\n   正在关闭 SSH 连接...')
    //       // conn.end()
    //       console.log('   SSH 连接已关闭。')
    //       console.log('\n--- 部署流程完成 ---')
    //       // process.exit(0)
    //     }
    //   })
    // })

    execSync(`del ${path.join(__dirname, ZIP_FILE_NAME)}`, { stdio: 'inherit' })
    process.exit(0)
  }).on('error', (err) => {
    console.error('   SSH 连接失败:', err.message)
    process.exit(1)
  }).connect({
    host: REMOTE_HOST,
    port: 22, // 默认 SSH 端口
    username: REMOTE_USER,
    password: REMOTE_PASSWORD,
    // Keep the connection alive (optional, default is often fine)
    // keepaliveInterval: 10000, // Send keepalive packet every 10s
    // keepaliveCountMax: 5, // Max keepalive failures before disconnecting
  })

  // Note: The `ssh2` connection is event-driven. The `connect` call
  // starts the process, but the `ready` event handler runs when connected.
  // The script will wait for these events.
}

// 执行主函数
deploy().catch((err) => {
  console.error('\n--- 发生未知错误 ---')
  console.error(err)
  process.exit(1)
})