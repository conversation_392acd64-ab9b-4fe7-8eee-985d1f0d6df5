<template>
    <div class="main" :class="{ expand }">
        <div class="title">
            <span>题量统计</span>
            <svg-icon :name="expand ? 'expand-menu' : 'collapse-menu'" class="menu-icon" @click="expand = !expand" />
        </div>
        <div class="main-content">
            <VueDraggable v-model="data" :animation="150" :onUpdate="handleUpdate" @start="showHover = false" @end="showHover = true">
                <div class="type-item" :class="{ 'user-hover': showHover }" v-for="(item, index) in data" :key="item.id" @click="hanldeTypeItemClick(index)">
                    <span v-if="item.options">{{ item.title.replace('题', '') }}</span>
                    <span v-else>
                        {{ item.title.replace('题', '') }}<span style="color: #5377ee;">{{ item.children.length }}</span>道
                    </span>
                    <div class="drag-icon">
                        <div v-for="i in 4" style="line-height: 4px">..</div>
                    </div>
                </div>
            </VueDraggable>
            <div style="height: 200px;" v-if="expand">
                <QuestionTypeChart :list="data"></QuestionTypeChart>
            </div>
            <div v-if="expand">
                <CatalogQuestionAmount :list="data"></CatalogQuestionAmount>
            </div>
        </div>
        <div class="abstract" :class="{ expand }">
            <p class="abstract-item" :class="{ expand }"><span v-if="expand">合计：</span><span class="red-text">{{ totalNum }}</span>题共<span class="red-text">{{ totalScore }}</span>分</p>
            <template v-if="isIndividualTiming == '1'">
                <a-divider type="vertical" v-if="expand" style="background-color: #d9d9d9;"  />
                <p class="abstract-item" :class="{ expand }">
                    <span v-if="expand">考试时长：</span>
                    <span class="red-text">{{ Math.floor(totalSeconds / 60) }}</span>分<template v-if="!(totalSeconds % 60)">钟</template>
                    <template v-if="totalSeconds % 60">
                        <span class="red-text">{{ totalSeconds % 60 }}</span>秒
                    </template>
                </p>
            </template>
        </div>
        <a-button type="primary" @click="handlePreview">预览试卷</a-button>
        <PreviewPaper :is-visible="previewVisible" :data="paperInfo" @closePreviewPaper="previewVisible = false"
            score-key="new_score"></PreviewPaper>
    </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import store from '@/store'
import PreviewPaper from '@/views/PaperManage/components/PreviewPaper.vue'
import { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'
import { VueDraggable } from 'vue-draggable-plus'
import QuestionTypeChart from './components/QuestionTypeChart.vue'
import CatalogQuestionAmount from './components/CatalogQuestionAmount.vue'
import { useVModel } from '@vueuse/core'
import { getTotalSeconds } from '@/utils'

const props = defineProps<{
    name: string
    list: PaperFbodyItemModelWithQuestionChildren[]
    totalScore: number
    isIndividualTiming?: '0' | '1'
}>()

const emits = defineEmits<{
    (e: 'update:list', list: PaperFbodyItemModelWithQuestionChildren[]): void
    (e: 'sortend', newIndex: number): void
    (e: 'position', index: number): void
}>()

const data = useVModel(props, 'list', emits)

const expand = ref(false)

const paperInfo = ref<{
    name: string
    score: number
    fbody: PaperFbodyItemModelWithQuestionChildren[]
}>({
    name: '',
    score: 0,
    fbody: []
})

// 处理题型点击
function hanldeTypeItemClick(index: number) {
    emits('position', index)
}



/** 总题量 */
const totalNum = ref(0)
/** 总时间（秒） */
const totalSeconds = ref(0)
watch(() => props.list, (newVal) => {
    totalNum.value = newVal.map((item) => item.children).flat().length
    totalSeconds.value = getTotalSeconds(newVal)
}, { immediate: true, deep: true })

const hasSorted = ref(false)
function handleUpdate({ newIndex }: any) {
    hasSorted.value = true
    emits('sortend', newIndex)
}

const showHover = ref(true)

// 试卷预览
const previewVisible = ref(false)
function handlePreview() {
    paperInfo.value = {
        name: props.name,
        score: props.totalScore,
        fbody: props.list
    }
    previewVisible.value = true
}

defineExpose({ hasSorted })

</script>

<style lang="less" scoped>
.main {
    padding: 24px 10px 24px 16px;
    background-color: #fff;
    height: 100%;
    margin-left: 8px;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    transition: all 0.3s ease-in-out;
    width: 120px;
    display: flex;
    flex-direction: column;

    &.expand {
        width: 350px;

        .type-item {
            padding-left: 16px !important;
        }
    }

    .title {
        font-weight: bold;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 6px;

        .menu-icon {
            margin-left: 6px;
            cursor: pointer;
        }
    }

    .main-content {
        flex: 1;
        min-height: 0;
        overflow: scroll;
        padding-right: 6px;
        margin-top: 24px;

        .type-item {
            line-height: 34px;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 2px;
            padding: 0 8px;
            transition: padding 0.3s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: nowrap;
            cursor: move;
            position: relative;
            // 超出隐藏
            white-space: nowrap;
            overflow: hidden;

            &:not(:first-child) {
                margin-top: 8px;
            }

            &.user-hover:hover {
                border: 1px solid #5478ee;
                background: #f1f4fe;
                .drag-icon {
                    opacity: 1;
                }
            }

            .drag-icon {
                position: absolute;
                top: 50%;
                right: 0px;
                height: 100%;
                transform: translateY(calc(-50%));
                padding-top: 4px;
                padding-right: 4px;
                background-color: #fff;
                // 白色阴影
                box-shadow: -2px 0px 2px 0px #fff;
                opacity: 0;
                transition: all ease 0.2s;
            }
        }
    }
}

.abstract {
    &.expand {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
        overflow: hidden;
    }
    .abstract-item {
        text-align: center;
        transition: all 0.3s ease-in-out;
        text-wrap: nowrap;
        padding-right: 6px;
        font-weight: bold;
        line-height: 24px;
    
        &.expand {
            text-align: right;
        }
        .red-text {
            color: #DE504E;
        }
    }
}

.ant-btn {
    width: 100%;
    border-radius: 8px;
    font-size: 14px;
    margin-top: 16px;
}
</style>