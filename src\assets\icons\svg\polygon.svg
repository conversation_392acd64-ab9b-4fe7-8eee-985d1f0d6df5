<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="94px" viewBox="0 0 28 94" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>矩形</title>
    <defs>
        <path d="M200,365 L209.111079,375.629592 C210.975294,377.80451 212,380.574554 212,383.439089 L212,426.560911 C212,429.425446 210.975294,432.19549 209.111079,434.370408 L200,445 L200,445 L200,365 Z" id="path-1"></path>
        <filter x="-108.3%" y="-13.8%" width="316.7%" height="132.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12461757 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="控件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="矩形" transform="translate(-192.000000, -360.000000)">
            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
        </g>
    </g>
</svg>