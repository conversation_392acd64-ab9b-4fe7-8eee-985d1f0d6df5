<template>
  <section class="full-loading">
    <Vue3Lottie :animationData="AstronautJSON" :height="120" :width="120" />
    <div class="loading-text">
      <div class="text">{{ loadingText }}</div>
      <div class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { Vue3Lottie } from 'vue3-lottie'
import AstronautJSON from '@/assets/loading.json'

defineProps({
  loadingText: {
    type: String,
    default: '加载中'
  }
})
</script>
<style lang="less" scoped>
.full-loading {
  display: flex;
  flex-direction: column;
  position: fixed;
  z-index: 100000000;
  top: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(240 242 245 / 50%);

  &.absolute {
    position: absolute;
    z-index: 300;
    top: 0;
    left: 0;
  }

  .loading-text {
    display: flex;
  }

  .text {
    color: #5478ee;
  }

  .spinner {
    margin-left: 8px;
  }

  .spinner > div {
    width: 6px;
    height: 6px;
    margin-right: 2px;
    background-color: #5478ee;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
    animation: bouncedelay 1.4s infinite ease-in-out;
    /* Prevent first frame from flickering when animation starts */
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }

  .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }

  .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
  }

  @-webkit-keyframes bouncedelay {
    0%,
    80%,
    100% {
      -webkit-transform: scale(0);
    }
    40% {
      -webkit-transform: scale(1);
    }
  }

  @keyframes bouncedelay {
    0%,
    80%,
    100% {
      transform: scale(0);
      -webkit-transform: scale(0);
    }
    40% {
      transform: scale(1);
      -webkit-transform: scale(1);
    }
  }
}
</style>
