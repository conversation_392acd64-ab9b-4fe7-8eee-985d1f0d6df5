<script setup lang="ts">
import JTagInput from '@/components/JTagInput.vue'
import ScoreInput from '@/views/QuestionManage/components/ScoreInput.vue'
import QuestionStem from './QuestionStem.vue'

const props = defineProps<{
  formState: any
  optionEditorsRef: any
  handleOptionBlur: () => void
  delOption: (event: Event, index: number) => void
  addOption: () => void
  showGenerateOptionModal: () => void
  fillBlank?: any
  getFillBlankScore?: any
  scoreInputRef?: any
}>()
</script>

<template>
  <QuestionStem />
  <a-form-item v-if="formState.type === 5" class="question-options">
    <!--  eslint-disable -->
    <template v-for="(item, index) in fillBlank.value" :key="item.id">
      <div class="fill-blank-item">
        <span class="label">填空{{ index + 1 }}答案</span>
        <JTagInput
          v-model="item.keyword"
          :ignorecase="formState.ignorecase"
          class="tag-ipt"
          placeholder="点击，编辑选项；点击回车设置多个关键词"
        />
        <div v-if="formState.sepscore" class="score-wrapper">
          <span class="score">分值</span>
          <ScoreInput
            :ref="scoreInputRef"
            v-model="item.score"
            class="score-input"
            @get-score="getFillBlankScore($event, item)"
          />
        </div>
      </div>
    </template>
  </a-form-item>
  <div v-if="formState.type === 5" class="fill-blank-config">
    <div>
      <a-checkbox v-model:checked="formState.ordered" class="check-box" />
      <span>判分时区分答案先后顺序</span>
    </div>
    <div>
      <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
      <span>忽略大小写</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>