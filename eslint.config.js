import antfu from '@antfu/eslint-config'
import eslintHandleProgressPlugin from 'eslint-plugin-file-progress'
import ignoreESLintFiles from './.ignoreESLintFiles.js'

export default antfu({
  formatters: false,
  plugins: { eslintHandleProgressPlugin },
  rules: {
    'style/eol-last': 'off',
    'style/no-trailing-spaces': 'off',
    'format/prettier': 'off',
    'no-restricted-globals': 'off',
    'node/prefer-global/buffer': 'off',
    // 忽略无限禁用（原来项目遗留的代码中大量出现，暂时忽略）
    'eslint-comments/no-unlimited-disable': 'off',
    // 忽略 promise reject 错误（因为在 src/utils）
    'prefer-promise-reject-errors': 'off',
    // 忽略未使用的导入（原来项目遗留的代码中大量出现，暂时忽略）
    // 'unused-imports/no-unused-vars': 'off',
    // 忽略 console（原来项目遗留的代码中大量出现，暂时忽略）
    'no-console': 1,
    // 忽略 eqeqeq（原来项目遗留的代码中大量出现，暂时忽略）
    'eqeqeq': 'off',
    'eslintHandleProgressPlugin/activate': 1,
    'ts/no-unused-expressions': 'off',
    'vue/no-export-in-script-setup': 'off',
    'unused-imports/no-unused-vars': 'off',
    'vue/no-multiple-template-root': 'off',
    'vue/one-component-per-file': 'off',
    'vue/no-setup-props-destructure': 'off',
    'vue/component-api-style': ['error', ['script-setup', 'composition']],
  },
  ignores: [
    ...ignoreESLintFiles,
  ],
  settings: {
    progress: {
      hide: false, // use this to hide the progress message, can be useful in CI
      hideFileName: false, // use this to hide the file name, would simply show "Linting..."
      successMessage: 'Lint Successfully',
    },
  },
})
