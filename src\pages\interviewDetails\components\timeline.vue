<script setup lang="ts">
import { useRoute } from 'vue-router'

const props = defineProps({
  examDetail: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const route = useRoute()
const state = computed(() => {
  // 安排面试：createTime
  // 发送通知：emailSentTime
  // 参加面试：joinTime
  // 面试回放：replayTime
  // const flag = props.examDetail.replayUrl ? 4 : props.examDetail.joinTime ? 3 : props.examDetail.emailSentTime ? 2 : props.examDetail.createTime ? 1 : 0
  const flag = props.examDetail.exam.replayUrl ? 4 : props.examDetail.exam.joinTime ? 3 : route.query.interviewStatus === '面试中' ? 3 : props.examDetail.exam.emailSentTime ? 2 : props.examDetail.exam.createTime ? 1 : 0
  return flag
})

const stateMap = {
  finish: 'circle_finish',
  pending: 'circle_blur',
  not: 'circle_empty',
}
// stateMap[status(1)]
function status(node: number) {
  const examInfo = props.examDetail.exam
  const { emailSentTime, createTime, joinTime, replayUrl, evaluation } = examInfo
  /**
   * 安排面试：1
   * 创建面试：2
   * 发送邮件：3
   * AI 面试：4
   * 参加面试：5
   * 面试完成：6
   * 面试结果：7
   */
  if (node === 1) {
    if (emailSentTime && createTime) 
      return 'finish'
    if (emailSentTime || createTime)
      return 'pending'
    else
      return 'not'
  }
  else if (node === 2) {
    if (createTime) 
      return 'finish'
    return 'not'
  }
  else if (node === 3) {
    if (emailSentTime) 
      return 'finish'
    return 'not'
  }
  else if (node === 4) {
    if (route.query.interviewStatus === '已结束') 
      return 'finish'
    if ((emailSentTime || route.query.interviewStatus !== '已结束') && state.value > 1)
      return 'pending'
    else
      return 'not'
  }
  else if (node === 5) {
    if (joinTime) 
      return 'finish'
    else
      return 'not'
  }
  else if (node === 6) {
    if (route.query.interviewStatus === '已结束') 
      return 'finish'
    else
      return 'not'
  }
  else if (node === 7) {
    if (evaluation) 
      return 'finish'
    else
      return 'not'
  }
  else {
    return 'not'
  }
}
</script>

<template>
  <a-timeline>
    <a-timeline-item>
      <template #dot>
        <SvgIcon :name="stateMap[status(1)]" width="18px" height="23px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          安排面试
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.createTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item v-show="status(1) !== 'finish'">
      <template #dot>
        <SvgIcon :name="stateMap[status(2)]" width="14px" height="13px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          创建面试
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.createTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item v-show="status(1) !== 'finish'">
      <template #dot>
        <SvgIcon :name="stateMap[status(3)]" width="14px" height="13px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          发送邮件
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.createTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item>
      <template #dot>
        <SvgIcon :name="stateMap[status(4)]" width="18px" height="23px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          AI面试
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.emailSentTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item v-show="status(4) !== 'finish'">
      <template #dot>
        <SvgIcon :name="stateMap[status(5)]" width="14px" height="13px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          参加面试
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.emailSentTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item v-show="status(4) !== 'finish'">
      <template #dot>
        <SvgIcon :name="stateMap[status(6)]" width="14px" height="13px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          面试完成
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.emailSentTime }}
        </div>
      </div>
    </a-timeline-item>
    <a-timeline-item>
      <template #dot>
        <SvgIcon :name="stateMap[status(7)]" width="18px" height="23px" />
      </template>
      <div class="flex flex-wrap">
        <div class="w-full">
          面试结果
        </div>
        <div class="text-nowrap mt-[5px]">
          {{ examDetail.exam.replayTime }}  
        </div>        
      </div>
    </a-timeline-item>        
  </a-timeline>
</template>

<style lang="scss" scoped>

</style>