<template>
  <div ref="editor"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, reactive, watch } from 'vue'
import WangEditor from 'wangeditor'
import { InsertUser, InsertWebSite } from './extend'
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  isEmailTemplate: {
    type: Boolean,
    default: false
  },
  excludeMenus: {
    type: Array,
    default: () => ['head', 'todo', 'quote', 'code', 'table', 'emoticon', 'video']
  }
})
const emits = defineEmits(['getHtml'])
// DOM
const editor = ref()
// 实例
let instance = <any>reactive({})

const updateContent = () => {
  console.log(props.content)
  if (props.content !== instance.txt.html()) {
    instance.txt.html(props.content) //根据父组件传来的值设置html值
  }
}

//监听
watch(
  () => props.content,
  (val) => {
    console.log('props.content===', val)
    // 设置内容
    // instance.txt.html(props.content)
    if (val !== instance.txt.html()) {
      instance.txt.html(val) //根据父组件传来的值设置html值
    }
    // instance.selection.moveCursor(instance.$textElem.elems[0], false)
  }
)

function handleExtendMenu(instance: any) {
  if (props.isEmailTemplate) {
    // 注册插入人名菜单
    instance.menus.extend('InsertUserKey', InsertUser)
    instance.config.menus.push('InsertUserKey')
    // 注册插入网址菜单
    instance.menus.extend('InsertWebSiteKey', InsertWebSite)
    instance.config.menus.push('InsertWebSiteKey')
  }
}

onMounted(() => {
  instance = new WangEditor(editor.value)
  handleExtendMenu(instance)
  Object.assign(instance.config, {
    onchange() {
      emits('getHtml', instance.txt.html())
    }
  })
  instance.config.pasteFilterStyle = false
  instance.config.pasteIgnoreImg = false
  instance.config.uploadImgShowBase64 = true
  instance.config.showLinkImg = false
  instance.config.showFullScreen = false
  // 配置菜单栏，设置不需要的菜单
  instance.config.excludeMenus = props.excludeMenus
  console.log(instance.config.menus)
  instance.create()
})

onBeforeUnmount(() => {
  instance.destroy()
  instance = null
})

defineExpose({
  updateContent
})
</script>

<style lang="less">
.w-e-text {
  padding: 10px;
}
.w-e-text p {
  font-size: 14px !important;
  line-height: normal;
  margin: 0;
}
</style>
