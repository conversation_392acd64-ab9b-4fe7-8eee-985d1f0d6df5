<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>图灵智面</title>
    <style>
      html {
        /* 值为1 为黑白 */
        filter: grayscale(0);
      }
      .loading {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 9999;
        left: 0;
        top: 0;
        background-color: rgb(240 242 245 / 50%);

        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .loading .spin {
        width: 120px;
        height: 120px;
      }
      .loading .text-wrap {
        display: flex;
      }
      .loading .text {
        color: #5478ee;
        font-size: 14px;
      }
      .spinner {
        margin-left: 8px;
      }

      .spinner > div {
        width: 6px;
        height: 6px;
        margin-right: 2px;
        background-color: #5478ee;

        border-radius: 100%;
        display: inline-block;
        -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
        animation: bouncedelay 1.4s infinite ease-in-out;
        /* Prevent first frame from flickering when animation starts */
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }

      .spinner .bounce1 {
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
      }

      .spinner .bounce2 {
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
      }

      @-webkit-keyframes bouncedelay {
        0%,
        80%,
        100% {
          -webkit-transform: scale(0);
        }
        40% {
          -webkit-transform: scale(1);
        }
      }

      @keyframes bouncedelay {
        0%,
        80%,
        100% {
          transform: scale(0);
          -webkit-transform: scale(0);
        }
        40% {
          transform: scale(1);
          -webkit-transform: scale(1);
        }
      }
    </style>
    <!-- <script src="./constant.js"></script> -->
    <script async src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"></script>
    <!-- <script src="./recorder.js"></script> -->
  </head>
  <body>
    <div class="loading">
      <div class="spin"></div>
      <div class="text-wrap">
        <div class="text">加载中</div>
        <div class="spinner">
          <div class="bounce1"></div>
          <div class="bounce2"></div>
          <div class="bounce3"></div>
        </div>
      </div>
    </div>
    <div id="app" translate="no">
      <style>
        /* html .app-loading {
          background-color: #2c344a;
        }

        html .app-loading .app-loading-title {
          color: rgb(255 255 255 / 85%);
        } */

        /* .app-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background-color: #f4f7f9;
        }

        .app-loading .app-loading-wrap {
          display: flex;
          position: absolute;
          top: 50%;
          left: 50%;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transform: translate3d(-50%, -50%, 0);
        }

        .app-loading .dots {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 98px;
        }

        .app-loading .app-loading-title {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 30px;
          color: rgb(0 0 0 / 85%);
          font-size: 18px;
        }

        .app-loading .app-loading-logo {
          display: block;
          width: 90px;
          margin: 0 auto;
          margin-bottom: 20px;
        }

        .dot {
          display: inline-block;
          position: relative;
          box-sizing: border-box;
          width: 48px;
          height: 48px;
          margin-top: 30px;
          transform: rotate(45deg);
          animation: ant-rotate 1.2s infinite linear;
          font-size: 32px;
        }

        .dot i {
          display: block;
          position: absolute;
          width: 20px;
          height: 20px;
          transform: scale(0.75);
          transform-origin: 50% 50%;
          animation: ant-spin-move 1s infinite linear alternate;
          border-radius: 100%;
          opacity: 0.3;
          background-color: #0065cc;
        }

        .dot i:nth-child(1) {
          top: 0;
          left: 0;
        }

        .dot i:nth-child(2) {
          top: 0;
          right: 0;
          animation-delay: 0.4s;
        }

        .dot i:nth-child(3) {
          right: 0;
          bottom: 0;
          animation-delay: 0.8s;
        }

        .dot i:nth-child(4) {
          bottom: 0;
          left: 0;
          animation-delay: 1.2s;
        }

        @keyframes ant-rotate {
          to {
            transform: rotate(405deg);
          }
        }

        @keyframes ant-rotate {
          to {
            transform: rotate(405deg);
          }
        }

        @keyframes ant-spin-move {
          to {
            opacity: 1;
          }
        }

        @keyframes ant-spin-move {
          to {
            opacity: 1;
          }
        } */
      </style>
      <!-- <div class="app-loading">
        <div class="app-loading-wrap">
          <div class="app-loading-dots">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
          <div class="app-loading-title">努力加载中...</div>
        </div> -->
      <!-- </div> -->
    </div>
    <script>
      const loading = document.querySelector('.spin')
      try {
        bodymovin.loadAnimation({
          container: loading, // 包含动画的dom元素
          renderer: 'svg', //渲染出来的是什么格式
          loop: true, //循环播放
          autoplay: true, //自动播放
          path: './loading.json' // 动画json的路径
        })
      } catch (error) {
        console.log(error)
      }
    </script>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // if (MEMORIAL_MODE) {
      //   document.querySelector('html').className = 'memorial'
      // }

      ;(function () {
        let isBrowserSupported = false
        let ua = navigator.userAgent || navigator.vendor || window.opera
        if (/iPhone|iPod|iPad|Android|BlackBerry|Windows Mobile/i.test(ua)) {
          return
        }

        // 检查Chrome版本
        const chromeMatch = ua.match(/Chrome\/(\d+)/)
        if (chromeMatch && parseInt(chromeMatch[1], 10) >= 112) {
          isBrowserSupported = true
        }

        // 检查Edge版本
        const edgeMatch = ua.match(/Edg\/(\d+)/)
        if (edgeMatch && parseInt(edgeMatch[1], 10) >= 94) {
          isBrowserSupported = true
        }

        // 检查Safari版本
        const safariMatch = ua.match(/Version\/([0-9\.]+) Safari/)
        if (safariMatch && parseFloat(safariMatch[1]) >= 605) {
          isBrowserSupported = true
        }

        // 检查ECMAScript 2022支持
        try {
          // 选择了ECMAScript 2022的一些新特性进行检测，例如BigInt
          eval('0n')
        } catch (err) {
          isBrowserSupported = false
        }

        // 如果浏览器版本不满足要求，跳转到Chrome下载页面
        if (!isBrowserSupported) {
          window.location.href =
            '/not-support.html?redirecturi=' + encodeURIComponent(window.location.href)
        }
      })()
    </script>
  </body>
</html>
