<template>
  <div>
    <div class="questions-analysis">
      <div class="questions-analysis-header">
        <span class="title">组织架构</span>
      </div>
      <div class="questions-analysis-content">
        <div class="category-wrapper">
          <h3 class="title">部门列表</h3>
          <div class="body">
            <a-tree
              draggable
              show-line
              show-icon
              :blockNode="true"
              :tree-data="treeData"
              :field-names="replaceFields"
              v-model:expandedKeys="expandedKeys"
              @dragenter="onDragEnter"
              @drop="onDrop"
            >
              <template #icon="item">
                <div @click="getDeptTeachers(item)">
                  <img
                    src="@/assets/images/admin/pdept.png"
                    style="width: 16px; margin-right: 4px; margin-bottom: -2px"
                    v-if="item.children && item.children.length"
                  />
                  <img
                    src="@/assets/images/admin/dept.png"
                    style="width: 16px; margin-right: 4px; margin-bottom: -2px"
                    v-else
                  />
                </div>
              </template>
              <template #title="item">
                <div class="custom-title" @click="getDeptTeachers(item)">
                  <span :title="item.name">{{ item.name }}</span>
                  <div class="operbtns">
                    <span title="编辑部门" @click.stop="editCurrentDep(item)"
                      ><Icon icon="EditOutlined"
                    /></span>
                    <span title="新建子部门" @click.stop="addNewSubDep(item.name, item.id)"
                      ><Icon icon="PlusOutlined" />
                    </span>
                    <a-popconfirm
                      v-if="item.parent"
                      title="确认要删除该部门及其子部门？"
                      ok-text="确定"
                      cancel-text="取消"
                      @click="preventDefaultEvent($event)"
                      @confirm="deleteDep($event, item.id)"
                    >
                      <span title="删除部门">
                        <img src="@/assets/icons/svg/delete.svg" class="del-icon" />
                      </span>
                    </a-popconfirm>
                  </div>
                </div>
              </template>
              <template #switcherIcon="{ switcherCls }">
                <Icon icon="DownOutlined" :class="switcherCls" />
              </template>
            </a-tree>

            <a-modal
              class="add-dept-modal"
              v-model:visible="visible"
              :title="isEdit ? '编辑部门' : '新增部门'"
              width="520px"
              :keyboard="false"
              :maskClosable="false"
              @cancel="closeModal"
            >
              <template #footer>
                <a-button type="primary" :loading="loading" @click="onSubmit">{{
                  isEdit ? '保存' : '新增'
                }}</a-button>
                <a-button @click="closeModal">取消</a-button>
              </template>
              <a-form
                v-if="visible"
                class="dep-form"
                ref="formRef"
                :label-col="labelCol"
                :model="formState"
                :colon="false"
                :hideRequiredMark="true"
                :rules="rules"
              >
                <a-form-item label="部门名称" name="name">
                  <a-input v-model:value.trim="formState.name" placeholder="部门名称" />
                </a-form-item>
                <a-form-item label="上级部门">
                  <a-input v-model:value="formState.pname" readonly class="parent-dept" />
                </a-form-item>
                <a-form-item label="允许报考" name="allow_application">
                  <a-radio-group v-model:value="formState.allow_application">
                    <a-radio :value="true">是</a-radio>
                    <a-radio :value="false">否</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="资源组" name="resource_group">
                  <a-select
                    v-model:value="formState.resource_group"
                    show-search
                    placeholder="选择资源组"
                    :options="resourcesOptions"
                    :filter-option="filterOption"
                  ></a-select>
                </a-form-item>
              </a-form>
            </a-modal>
          </div>
        </div>
        <div class="right-area">
          <div class="question-manage-header">
            <div class="header-left">
              <a-input-search
                class="search-wrapper"
                v-model:value.trim="searchContent"
                placeholder="请输入关键词"
                @search="getSearchData"
                @keyup.enter="getSearchData"
              />
            </div>
            <div class="header-right">
              <span class="add-btn" @click="changeTeacherDept">更改部门</span>
            </div>
          </div>
          <div v-if="letters.length">
            <div class="teacher-alpht-wrapper">
              <template v-for="(item, index) in letters" :key="item">
                <span
                  class="item"
                  :class="{ active: currentLetterIndex === index }"
                  @click="handleClickItem(item, index)"
                  >{{ item }}</span
                >
              </template>
            </div>
            <div class="teacher-alpht-card">
              <template v-for="(item, index) in teacherCards" :key="item.letter">
                <h3 :id="'letter' + item.letter" v-if="item.teachers.length">{{ item.letter }}</h3>
                <div class="item">
                  <template v-for="teacher in item.teachers" :key="teacher.id">
                    <div class="teacher-card" :class="{ active: teacher.checked }">
                      <!-- <div class="letter">{{ item.letter }}</div> -->
                      <div class="letter">
                        <!-- <svg-icon :name="item.letter + '_' + (index % 3)" width="48px;" height="48px" /> -->
                        <!-- <img :src="getImgUrl(item.letter)" /> -->
                        <!-- <svg-icon name="A1" width="48px" height="48px" /> -->
                        <img
                          :src="
                            'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/exam/teacher/letters/logo_' +
                            item.letter +
                            ((index % 3) + 1) +
                            '.png'
                          "
                        />
                      </div>
                      <div class="info">
                        <div class="username">{{ teacher.name }}</div>
                        <div class="dept">{{ teacher.dept }}</div>
                        <div class="dept">{{ teacher.phone }}</div>
                        <div class="email">{{ teacher.email }}</div>
                      </div>
                      <div class="checkbox">
                        <a-checkbox v-model:checked="teacher.checked" />
                      </div>
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </div>
          <div v-else class="no-data">
            <img class="nodata" src="@/assets/images/nodata.png" />
          </div>
        </div>
      </div>
    </div>

    <modify-dept-modal
      :modifyDeptVisible="modifyDeptVisible"
      :teachers="selectedTeachers"
      @close="closeDeptModal"
      @refresh-teachers="getAllTeachers"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import ModifyDeptModal from './components/modifyDeptModal.vue'
import { getDepList, modifyDeptLevel, allteachers, resourcegroup } from '@/api/admin/systemManage'
import { message } from 'ant-design-vue'

const treeData = ref<any[]>([])
const expandedKeys = ref(['0', '0-0', '0-0-1', '0-0-0-1'])
const labelCol = ref({ span: 5 })
const formRef = ref()
const visible = ref(false)
const modifyDeptVisible = ref(false)

// 搜索
const searchContent = ref('')
const filterArray = ref<any>([])
const getSearchData = () => {
  filterArray.value.length = 0
  for (let i = 0; i < sourceTeachersData.value.length; i++) {
    const teacherObj: any = {}
    teacherObj.letter = sourceTeachersData.value[i].letter
    teacherObj.teachers = sourceTeachersData.value[i].teachers.filter(
      (item: any) => item.name.indexOf(searchContent.value) > -1
    )
    filterArray.value.push(teacherObj)
  }
  teacherCards.value = filterArray.value
}

// 字母表
const letters = ref([])
const currentLetterIndex = ref(0)
const handleClickItem = (item: any, index: number) => {
  currentLetterIndex.value = index
  const elem = document.getElementById('letter' + item)
  elem && elem.scrollIntoView()
}

const teacherCards = ref<any>([])

const formState = ref({
  name: '',
  pname: '',
  allow_application: false,
  id: null,
  resource_group: null
})

// 资源组
const resourcesOptions = ref([])
const filterOption = (input: string, option: any) => {
  return option.value.indexOf(input) >= 0
}

const replaceFields = {
  title: 'name'
}

const rules = {
  name: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
  allow_application: [{ required: true, message: '请选择是否允许报考', trigger: 'blur' }],
  resource_group: [{ required: true, message: '请选择资源组', trigger: 'blur' }]
}

const resetForm = (form = <any>{}) => {
  formState.value.id = form.id || null
  formState.value.name = form.name || ''
  formState.value.pname = form.pname || ''
  formState.value.allow_application = form.allow_application || false
  formState.value.resource_group = form.resource_group || ''
}

// const getImgUrl = (letter: string) => {
//   return encodeURI('https://oss-public.exam.isrc.ac.cn/exam/teacher/letters/logo_A1.png')
// }

const isEdit = ref(false)
const addNewSubDep = (name: string, id: any) => {
  isEdit.value = false
  resetForm()
  visible.value = true
  formState.value.pname = name
  formState.value.id = id
}

const editCurrentDep = (item: any) => {
  item.isEdit = true
  isEdit.value = true
  resetForm(item)
  visible.value = true
}

const preventDefaultEvent = (e: Event) => {
  e.stopPropagation()
}

const deleteDep = (e: Event, id: string) => {
  getDepList({ action: 'del', id }).then(() => {
    loading.value = false
    message.success('部门删除成功!')
    getDepData()
  })
}

const onDragEnter = (info: any) => {
  console.log(info)
}
const onDrop = (info: any) => {
  const params = {
    id: info.dragNode.dataRef.id,
    parent: info.node.dataRef.id
  }
  modifyDeptLevel(params).then(() => {
    getDepData()
  })
}

const closeModal = () => {
  visible.value = false
}

const closeDeptModal = () => {
  modifyDeptVisible.value = false
}

const loading = ref(false)
const onSubmit = () => {
  formRef.value.validate().then(() => {
    loading.value = true
    const addParams = {
      parent: formState.value.id,
      name: formState.value.name,
      pname: formState.value.pname,
      resource_group: formState.value.resource_group,
      allow_application: formState.value.allow_application
    }
    const modifyParams = {
      id: formState.value.id,
      name: formState.value.name,
      resource_group: formState.value.resource_group,
      allow_application: formState.value.allow_application
    }

    isEdit.value ? modifyDept(modifyParams) : addDept(addParams)
    closeModal()
  })
}

async function getDepData() {
  treeData.value = await getDepList({ action: 'query' }) as any
}

const addDept = (params: any) => {
  getDepList({ action: 'add', ...params }).then(() => {
    loading.value = false
    message.success('部门新增成功!')
    getDepData()
  })
}

const modifyDept = (params: any) => {
  getDepList({ action: 'modify', ...params }).then(() => {
    loading.value = false
    message.success('部门编辑成功!')
    getDepData()
  })
}

const selectedTeachers = ref<any>([])
const changeTeacherDept = () => {
  selectedTeachers.value.length = 0
  teacherCards.value.forEach((item: any) => {
    item.teachers.forEach((subItem: any) => {
      if (subItem.checked) {
        selectedTeachers.value.push(subItem)
      }
    })
  })
  if (!selectedTeachers.value.length) {
    message.info('请先选择需要更改部门的教师')
    return
  }
  modifyDeptVisible.value = true
}

// 获取所有教师信息
const sourceTeachersData = ref<any[]>([])
const getAllTeachers = () => {
  allteachers({ dept: selectedDept.value }).then((res: any) => {
    letters.value = res.xingshi
    teacherCards.value = res.teacherinfo
    sourceTeachersData.value = JSON.parse(JSON.stringify(res.teacherinfo))
  })
}

const selectedDept = ref('')
const getDeptTeachers = (item: any) => {
  selectedDept.value = item.id
  getAllTeachers()
}

// 获取资源组数据
const getResourceData = () => {
  resourcegroup({ action: 'query', condition: '' }).then((res: any) => {
    resourcesOptions.value = res.data.map((item: any) => ({
      value: item.name,
      label: item.name === 'default' ? '默认资源组' : item.name
    }))
  })
}
onMounted(async () => {
  await getDepData()
  selectedDept.value = treeData.value?.[0]?.id
  getAllTeachers()
  getResourceData()
})
</script>

<style lang="less" scoped>
.questions-analysis {
  padding: 16px;
  .questions-analysis-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 14px;
      font-weight: bold;
    }
  }
  .questions-analysis-content {
    position: relative;
    display: flex;
    height: calc(100vh - 120px);
    overflow: auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    .category-wrapper {
      width: 260px;
      flex-shrink: 0;
      height: 100%;
      border-right: 1px solid #e8e8e8;
      .title {
        padding-left: 16px;
        line-height: 48px;
        border-bottom: 1px solid #e8e8e8;
        font-size: 14px;
        font-weight: bold;
      }
      .body {
        padding-left: 16px;
        padding-top: 8px;
        // .custom-title {
        //   width: 100%;
        //   display: flex;
        //   justify-content: space-between;
        //   align-items: center;
        // }
        // .operbtns {
        //   width: 60px;
        //   display: flex;
        //   justify-content: space-around;
        // }
        .dep-form.ant-form {
          padding: 0 20px;
        }
        .custom-title {
          height: 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &:hover .operbtns {
            display: flex;
          }
          &:hover span {
            max-width: 80px;
          }
        }
        .operbtns {
          text-align: right;
          width: 60px;
          justify-content: space-around;
          display: none;
          .del-icon {
            width: 14px;
            position: relative;
            bottom: 2px;
          }
        }
      }
    }
  }
}
.right-area {
  width: calc(100% - 260px);
  padding: 16px;
  .no-data {
    height: calc(100% - 44px);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.question-manage-header {
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  .search-wrapper {
    --antd-wave-shadow-color: #fff !important;
    width: 240px;
    line-height: 32px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;

    :deep(.ant-input) {
      font-size: 13px;
      box-shadow: none;
      border: none;
      border-radius: 8px;
      line-height: 26px;
    }
    :deep(.ant-input-group-addon) {
      border-radius: 8px;
    }
    :deep(.ant-input-search-button) {
      border-radius: 8px !important;
      box-shadow: none !important;
      border: none !important;
    }
  }
  .header-right {
    display: flex;
    align-items: center;
    font-size: 14px;
    span {
      width: 88px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      border-radius: 8px;
      font-size: 14px;
      margin-left: 8px;
      cursor: pointer;
    }
    .add-btn {
      background: #5478ee;
      color: #fff;
    }
    .import-btn,
    .del-btn {
      border: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
}
.teacher-alpht-wrapper {
  display: flex;
  margin-bottom: 20px;
  flex-shrink: 0;
  .item {
    width: 24px;
    line-height: 24px;
    text-align: center;

    border-radius: 4px;
    margin-right: 10px;
    font-size: 12px;
    cursor: pointer;
    &.active {
      background: #f1f4fe;
    }
  }
}
.teacher-alpht-card {
  height: calc(100vh - 240px);
  scroll-behavior: smooth;
  overflow: auto;
  &::-webkit-scrollbar-track-piece {
    background: #f5f5f5;
  }

  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a6a7a9;
    border-radius: 20px;
  }
  h3 {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .item {
    display: flex;
    flex-wrap: wrap;

    .teacher-card {
      display: flex;
      width: 280px;
      margin-right: 3%;
      margin-bottom: 16px;
      padding: 20px;
      border: 1px solid #dddddd;
      border-radius: 8px;
      transition: all 0.3s;
      &.active {
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
        transform: translateY(-2px);
      }
      .letter {
        width: 48px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        // background: #d2f6f9;
        border-radius: 24px;
        margin-right: 20px;
      }
      .info {
        margin-right: auto;
        .username {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 8px;
        }
        .dept {
          font-size: 12px;
          color: #626262;
          margin-bottom: 4px;
        }
        .email {
          font-size: 12px;
          color: #626262;
        }
      }
    }
  }
}
</style>

<style lang="less">
.ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
  flex: 1;
  min-width: 0;
}
.add-dept-modal {
  width: 420px !important;
  .ant-modal-content {
    border-radius: 8px !important;
  }
  .ant-modal-header {
    border-radius: 8px !important;
  }
  .ant-modal-title {
    font-size: 15px;
    color: #121633;
    font-weight: bold;
  }
  .ant-form-item-label > label {
    font-size: 12px;
    color: #626262;
  }
  .ant-form label {
    font-size: 12px;
  }
  .ant-col {
    width: 80px;
  }
  .ant-input {
    height: 32px;
    border-radius: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
  }
  .ant-select-selector {
    font-size: 12px;
    border-radius: 8px !important;
  }
  .parent-dept {
    box-shadow: none;
    border-color: #d6d6d6;
  }
  .ant-modal-footer {
    border-radius: 8px;
    text-align: center;
    padding-bottom: 30px;
    .ant-btn {
      margin-left: 20px;
      font-size: 14px;
    }
  }
}
.ant-tree-switcher.ant-tree-switcher-noop {
  display: none !important;
}
.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open,
.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-close {
  display: flex !important;
  align-items: center;
}
.ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal {
  display: flex !important;
  align-items: center;
}
.ant-tree-title {
  width: 100%;
}
</style>
