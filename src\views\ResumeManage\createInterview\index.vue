<script setup lang="ts">
import { createTemplateApi, getPackageList } from '@/pages/questionManage/hooks/api'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import SelectQuestionType from '../components/selectQuestionType/index.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)

const formRef = useTemplateRef<any>('form')
const selectQuestionTypeRef = useTemplateRef('selectQuestionType')
const formInline = reactive<any>({
  name: `${route.query.candidateName}-${route.query.position}`,
  time: [],
  disableAdditional: false,
})
const disabledCreate = ref(true)
const operationFlag = ref(false)
function setDisabledCreate(val: boolean) {
  disabledCreate.value = val
  operationFlag.value = true
}

function disabledDate(time: any) {
  return dayjs(time).isBefore(dayjs().startOf('day'))
}
const rules = reactive({
  name: [
    { required: true, message: '请输入面试名称', trigger: 'blur' },
  ],
  time: [
    { required: true, message: '请选择面试时间', trigger: 'blur' },
  ],
})
init()

function init() {
  getPkgList()
}

const pkgList = ref([])
const renderList = ref([])
const selectType = ref(0)

watch(selectType, (val) => {
  if (val === 1) {
    renderList.value = pkgList.value.filter((item: any) => item.selected)
  }
  else {
    renderList.value = pkgList.value.filter((item: any) => item)
  }
})
watch(formInline, (val) => {
  operationFlag.value = true
}, { deep: true })

const selection = ref([])
async function getPkgList() {
  const res = await getPackageList({
    pageNo: 1,
    pageSize: 100,
  })
  const { records } = res
  records.forEach((item: any) => {
    item.selected = false
  })
  pkgList.value = records
  renderList.value = pkgList.value.filter((item: any) => item)
}
function select(item: any) {
  item.selected = !item.selected
}

function create() {
  formRef.value.validate(async (valid: any) => {
    if (!valid)
      return

    await selectQuestionTypeRef.value!.validateContentRequired()

    const contentResults = selectQuestionTypeRef.value!.getRequestParamsPipe()

    const checkQuestionCheck = (next?: any) => {
      let noCheckNum = 0 
      let checkFailNum = 0
      const quesionList = contentResults.selectedQuestionList
      quesionList.forEach((question: any) => {
        if (!question.chenck) 
          noCheckNum++
        else if (question.chenck.isCorrect === 2) 
          checkFailNum++
      })
      if (noCheckNum || checkFailNum) {
        const noCheckText = noCheckNum ? `${noCheckNum}道未校对题目，` : ''
        const checkFailText = checkFailNum ? `${checkFailNum}道校对错误题目，` : ''
        ElMessageBox.confirm(
          `请注意！您选择的指定题目中包含：${noCheckText}${checkFailText}是否继续创建？`,
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )
          .then(() => {
            next && next()
          })
          .catch(() => {
            // nop
          })
      }
      else {
        next && next()
      }
    }
    const checkQuestionNum = (next?: any) => {
      const totalNum = selectQuestionTypeRef.value!.contentList().reduce((total: number, item: any) => {
        if (item.type === 'pkg') {
          // 如果是题库，体量为 extractNum
          return total + (Number(item.extractNum) || 0)
        }
        else if (item.type === 'question' || item.type === 'manual') {
          // 如果是单个题目或手动输入题目，体量为 1
          return total + 1
        }
        return total
      }, 0)

      const tipText = totalNum < 10 ? '题目总量未满10道，可能会影响AI面试效果，是否确认创建AI面试' : totalNum > 20 ? '题目总量超过20道，可能会影响AI面试效果，是否确认创建AI面试' : ''
      if (totalNum < 10 || totalNum > 20) {
        ElMessageBox.confirm(
          tipText,
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )
          .then(() => {
            next && next()
          })
          .catch(() => {
            // nop
          })
      }
      else {
        next && next()
      }
    }
    const submitExec = async () => {
      try {
        loading.value = true
        const startTime = formInline.time[0]
        const endTime = formInline.time[1]
        const res = await createTemplateApi({
          // packagePathList,
          title: formInline.name,
          startTime: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
          endTime: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
          resumeId: route.query.resumeId,
          positionId: route.query.positionId,
          ...contentResults,
        })
        if (res && res.includes('创建成功')) {
          message.success('创建成功')
          router.back()
        }
      }
      catch (e) {}
      finally {
        loading.value = false
      }
    }

    checkQuestionCheck(() => checkQuestionNum(submitExec))
  })
}

function cancel() {
  if (operationFlag.value) {
    ElMessageBox.confirm(
      '确认取消创建吗?',
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(() => {
        router.back()
      })
      .catch(() => {
        // nop
      })
  }
  else {
    router.back()
  }
}

function clear(e: any) {
  e.stopPropagation()
  selection.value = []
  pkgList.value.forEach((item: any) => {
    item.selected = false
  })
}
const pkgName = ref('')
async function getSearchData() {
  const res = await getPackageList({
    content: pkgName.value,
    pageNo: 1,
    pageSize: 100,
  })
  const { records } = res
  records.forEach((item: any) => {
    item.selected = false
  })
  pkgList.value = records
  renderList.value = pkgList.value.filter((item: any) => item)
}
</script>

<template>
  <div v-loading="loading" class="manual-mark-page-wrap min-w-[1440px]">
    <div class="common-page-title-wrapper flex flex-wrap m-0 relative dw">
      <div class="title w-full text-[rgba(0,0,0,0.45)]">
        <span @click="cancel">人才库</span>

        <span class="title w-full">
          / 创建面试
        </span>
      </div>

      <div class="filter-wrapper mt-[20px] flex flex-wrap" @click.stop>
        <div class="w-full text-[16px] font-bold">
          基本信息
        </div>
        <div class="mt-[16px]" @click.stop>
          <el-form ref="form" :inline="true" :model="formInline" class="demo-form-inline" :rules="rules" hide-required-asterisk>
            <el-form-item label="面试名称" prop="name">
              <el-input v-model="formInline.name" maxlength="50" placeholder="岗位-姓名" clearable style="width: 200px;" />
            </el-form-item>
            <el-form-item label="面试时间" prop="time">
              <a-range-picker
                v-model:value="formInline.time "
                show-time
                type="datetimerange"
                start-placeholder="最早进入时间"
                end-placeholder="最晚进入时间"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-form>
          <!-- <el-form-item prop="disableAdditional">
              <el-checkbox v-model="formInline.disableAdditional" label="禁止追问" size="mini" />
              <el-tooltip
                class="box-item"
                effect="light "
                content="勾选后面试时不会对候选人的回答进行追问"
                placement="right"
              >
                <el-icon class="ml-[8px]">
                  <InfoFilled color="#d9d9d9" />
                </el-icon>
              </el-tooltip>
            </el-form-item> -->
        </div>
      </div>

      <div class="flex flex-nowrap justify-between w-full">
        <div class="filter-wrapper mt-[20px] flex flex-wrap" style="flex:1" @clik.stop>
          <SelectQuestionType ref="selectQuestionType" @set-disabled-create="setDisabledCreate" />

          <el-divider />
          <div> 
            <a-button type="primary" class="mr-[16px]" :disabled="disabledCreate" @click.stop="create">
              创建面试
            </a-button>
            <a-button @click="cancel">
              取消
            </a-button>
          </div>
        </div>
        <div id="interviewQuestionAnalyse" class=" mt-[20px] flex flex-wrap relative" style="width: fit-content;" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
// :deep(.ant-input-wrapper:focus-within) {
//   width: 800px;
// }

.empty-box {
  margin-top: 20%;
  margin-left: 50%;
  transform: translateX(-50%);
}

.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.filter-wrapper {
  position: relative;
  width: 100%;
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }
  }
}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
.dw{
  @media screen and (max-width: 1440px) {
    width: 86%;
  }
    @media screen and (min-width: 1441px) {
    width: 100%;
  }
}
</style>