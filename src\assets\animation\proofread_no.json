{"v": "5.8.1", "fr": 60, "ip": 0, "op": 61, "w": 200, "h": 200, "nm": "认证状态-不通过-40*40", "ddd": 0, "assets": [{"id": "错误试卷.png", "w": 200, "h": 200, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "背景.png", "w": 200, "h": 200, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAHIElEQVR4nO3cP2wb1x3A8d977+5IkZIsVZYc5x/aIejQpUO7u6NRBMiiLoaBTN4KtICBFl1yWYoGCNAC3TwFCNKhWgJk8Fjv7dClQ5GhRd34j2RV/ymSd+/9OphMaEbS6cQTSUnfD2Dcg+79+f3I+1nk05EiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAuHjMpAMYp3v3/hZvLS42JNyYCS5vWOPrkvtEjE2Ms7EYk4TQja2NjIbIqO9GEtVEQteNIz5jnWrwxlinIiL9tgZ/5PNUtv8wFRtM8CrWeWNyFeu8BPXG2I5R1w2+k9naTCfPfcf6qCX2+eHi1lbrwYMfZdVlPd0uZYHc/vmXtdnZ2fmgM3OR6py3Oq/GNYz6eNKxXQZqXGbUt1wwu7kxe9Yc7u3v7+8+/OM7nUnHVrULXyBpqvav7Z1rjUgWfJBFZ92Chqw26bheciLie0cZaPuKxo86f7WMtR0fZNtZ2WrlO9s/rr+9k6YmTCSYilzIArmTbs4fHOTLsXFLuZhr1og9ql/Zy2e6LrfRTTr/oBIi0Z1M/WazGW18li7tnnGqibkQBZKmav/ZXV8OEi9n3nwnMiYREfGSi5NIvOTnsm5/bieRnGa98v1fvSBFIhHJe8dvjy87/6iqzl/VddXp/+qSbXw/Wdm4CL9dprlAzE9/vb2QhPw1Y9yyOHGvXj7ftPtPlwveeOvU9d6kDr+JLerfb/te/+H1ht8ED/cvmq/ofJFLlb9XnxuzPmOjZ3/63cK2iJzqMRi3qSuQu/efNVtR/UYUzErQbu3Vp7ToEil7vsjw+GHl1ssll0giyXvn++3o2HhGze9i5J+YpJNbXW/k7eeffvzawSmDG4upKJDVVBPJNpeDdytWtTnpeDA5wZgD6/y6xEsba6npTjqeiRbIu+mTRpI334g0XwpBjEgsIpm8PMpAu7/tPnx+WFH/svMVzT+sbHxF469u/taq5kY2u9H8V1+kpnXKACs3kQJZTddnpWXeCM4sTGJ9XCzW67Y09Ku1dGV/3GuPtUDupJvz3VZ8M0iYO7rHtP2POWp85z3/1crfit1LGtnTcW4Xj6VA3vvF1kKcuBteO03jIlWfv9xl6bWNi8ayg9Ff67j1Rz0/6vrn7bLkb4NrZV3//PM/LG6XGXcW51ogt9PN+UZHXw/B1URErIs1+MxYFx/5gPTPBZ+dKa7h+Uedr2rkX23+1vpOq2aePDzH3yjn8sCtpv9IfPb6TVU95qXUS4mIdHtHGWifdeuicL7hHwwPKGvE+ci/mvyNMXsufvJ0Lf1B5bte1RaIqln9zf71qKtLwZnCuYse36rPD6u6f9nrjfyry896VZuYzbXfzr4QYyp7yVpZgaym67OhrSs2WO6YxcQEGzJbN+tV7XiNXCD37mm8u/Bi2dukUUVAQBVc6Lbmt69vPHhgRvrsykgF8l66tRDnnUWR+ijTfC3kHWOjmoa8Y0RE+m0b1SZyn85wPMOqjo/8q83fRaptke3P07Pvdp2pQFb/rC76+8H1Tieru6iuPm+faZ7+WBfVVUSk3z5uvlH7Dzvv8UXIfzz512pxO/9h88Xaz0zpO/dLJ7b6y8cznbi+GIk98jMYwDTKJYRa1t5a+/1bh2XGlSgQNau/2pr3rsZ7DVxYzndaax8t7oqcbqfrVAVyK/1L9HbnnWuZiU97n7SIiByISLN3lIH2cbfrnnf/quOtej7yH0/+sWb5f2pf7jxKf1L4SbPCAnn//X/Vw82lZtadjlvjgSrEiah9unnwySffa5/U78SL/u79Z00f1Y/9u0/W2TNxbU6zzp4REem349rcRHZdxh0P+V/8/F3e7p70Ia1jCkTN3fvPG22XnPiSKplR7R4ak8yoioj0293Do/+KXrZ/eVsistg7ykB7sZrph5D/5ci/7rv5px/faB31vuTbgamadz98OlOXiF0qXBltycMXH9w8HL5N5ZUCSVO1/xZJDnam5P3GWyLyuHeUgfbj007wXxF5s3eUgfab1cV4nsh/rPk3r4l+V6Q7+G0r3xRCmtrbm3e4jwpX3sOlzzJJ0yDSLxBVc+vDga9nOsYtEXnUO8pA+9EpzxePKLfeqPGWVTRf8XpFI8qtN2q8ZRXNV7xe0Yhy640a70kefSD+65dbaZraNE2tqJr+cbB93PlR/5WdXweOOvSzs8xX1J/8r3b+MiXf+gMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAmE7/B4ad8MDpfnCiAAAAAElFTkSuQmCC", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "❌-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 21, "s": [-45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [-40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [-45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-50]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [-45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-41]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [-45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [-49]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [-45]}, {"t": 55, "s": [-45]}], "ix": 10}, "p": {"a": 0, "k": [141, 120, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1.296, 1.296, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0.296, 0.296, 0]}, "t": 35, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 45, "s": [100, 100, 100]}, {"t": 55, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [6.5, 40], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 4, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [0, 0.608, 0.608, 0.608, 0.5, 0.746, 0.746, 0.746, 0.998, 0.884, 0.884, 0.884, 0.999, 0.884, 0.884, 0.884, 1, 0.884, 0.884, 0.884]}, {"t": 45, "s": [0, 0.663, 0.192, 0.286, 0.25, 0.727, 0.261, 0.308, 0.5, 0.792, 0.329, 0.329, 0.75, 0.818, 0.461, 0.461, 1, 0.843, 0.592, 0.592]}], "ix": 9}}, "s": {"a": 0, "k": [9, -3], "ix": 5}, "e": {"a": 0, "k": [-10, 8], "ix": 6}, "t": 1, "nm": "gradient fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "❌-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 21, "s": [45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [50]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [40]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [49]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [45]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [41]}, {"i": {"x": [0.27], "y": [0.769]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [45]}, {"t": 55, "s": [45]}], "ix": 10}, "p": {"a": 0, "k": [140, 120, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [3.5, -1.5, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [-100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0.25, 0.25, 0]}, "t": 25, "s": [-100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [-110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 45, "s": [-100, 100, 100]}, {"t": 55, "s": [-100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [6.5, 40], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 4, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [0, 0.608, 0.608, 0.608, 0.5, 0.745, 0.745, 0.745, 0.998, 0.882, 0.882, 0.882, 0.999, 0.882, 0.882, 0.882, 1, 0.882, 0.882, 0.882]}, {"t": 45, "s": [0, 0.663, 0.192, 0.286, 0.25, 0.727, 0.261, 0.308, 0.5, 0.792, 0.329, 0.329, 0.75, 0.818, 0.461, 0.461, 1, 0.843, 0.592, 0.592]}], "ix": 9}}, "s": {"a": 0, "k": [9, -3], "ix": 5}, "e": {"a": 0, "k": [-10, 8], "ix": 6}, "t": 1, "nm": "gradient fill 2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "🌟-红", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [140, 120, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [110, 110, 100]}, {"t": 45, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 8, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [36]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35, "s": [36]}, {"t": 55, "s": [36]}], "ix": 6}, "is": {"a": 0, "k": 50, "ix": 8}, "or": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [44]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35, "s": [44]}, {"t": 55, "s": [44]}], "ix": 7}, "os": {"a": 0, "k": 50, "ix": 9}, "ix": 1, "nm": "多边星形路径 1", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "st", "c": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0.78823530674, 0.78823530674, 0.78823530674, 1]}, {"t": 45, "s": [0.803921580315, 0.40000000596, 0.403921574354, 1]}], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [0, 0.922, 0.922, 0.922, 0.5, 0.922, 0.922, 0.922, 1, 0.922, 0.922, 0.922]}, {"t": 45, "s": [0, 1, 0.925, 0.925, 0.5, 1, 0.843, 0.843, 1, 1, 0.761, 0.761]}], "ix": 9}}, "s": {"a": 0, "k": [-43, -71], "ix": 5}, "e": {"a": 0, "k": [68, 85], "ix": 6}, "t": 1, "nm": "gradient fill 3", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.001, 0.853, 0.971, 0.988, 0.597, 0.813, 0.962, 0.994, 0.999, 0.773, 0.952, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 1, "nm": "gradient fill 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "错误试卷.png", "cl": "png", "refId": "错误试卷.png", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 100, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "背景.png", "cl": "png", "refId": "背景.png", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 100, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "bm": 0}], "markers": [], "metadata": {"filename": "校对认证动效-2.aep"}}