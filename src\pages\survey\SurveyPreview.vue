<template>
    <div class="survey-paper" v-if="surveyInfo">
        <div class="survey-paper-main">
            <div class="survey-title">{{ surveyInfo.name }}</div>
            <a-form ref="formRef" class="survey-content" :model="surveyInfo" @finishFailed="scrollToFirstErrFormItem">
                <p class="survey-tip" v-if="surveyInfo.describe">{{ surveyInfo.describe }}</p>
                <a-form-item class="ques-item" v-for="(item, index) in surveyInfo.content"
                    :name="['content', index, 'answer']">
                    <template v-if="item.type <= 100">
                        <p class="ques-title">
                            <span v-if="item.required" style="color: #f66f6a;">*</span>
                            {{ item.number }}. {{ item.body }}
                            <span style="margin-left: 10px;">({{ QuestionEnum[item.type] }})</span>
                        </p>

                        <!-- 单选题 -->
                        <div class="options-group" v-if="item.type === QuestionEnum['单选题']">
                            <div class="option-item" v-for="(option, index) in item.options">
                                <span class="option-selectbox option-selectbox-radio"></span>
                                {{ option.content }}
                            </div>
                            <div class="option-item" v-if="item.other">
                                <span class="option-selectbox option-selectbox-radio"></span>
                                其他 __________________________________________
                            </div>
                        </div>

                        <!-- 多选题 -->
                        <div class="options-group" v-if="item.type === QuestionEnum['多选题']">
                            <div class="option-item" v-for="(option, index) in item.options">
                                <span class="option-selectbox"></span>
                                {{ option.content }}
                            </div>
                            <div class="option-item" v-if="item.other">
                                <span class="option-selectbox"></span>
                                其他 __________________________________________
                            </div>
                        </div>

                        <!-- 评分题 -->
                        <div v-if="item.type === QuestionEnum['评分题']" class="mark-ques">
                            <div class="mark-ques-stars">
                                <div v-for="star in item.score">
                                    <img src="@/assets/icons/svg/deactive_star.svg" style="width: 32px;" alt="">
                                </div>
                            </div>
                            <span class="mark-desc">{{ item.min_desc }}</span>
                            <span class="mark-desc" style="float: right;">{{ item.max_desc }}</span>
                        </div>
                    </template>
                    <template v-else>
                        <a-divider v-if="item.type === QuestionEnum['分割线']" dashed
                            style="border-color: #e8e8e8;color: rgba(0,0,0,0.45);font-size: 14px;">
                            {{ item.dividerTitle }}
                        </a-divider>
                    </template>

                </a-form-item>
            </a-form>
        </div>
    </div>
    <Teleport to="#page-preview-teleport" v-if="showToolbar">
        <a-button v-if="token" :loading="printLoading" v-print="printConfig">打印</a-button>
        <a-button v-if="token && surveyInfo?.published && surveyInfo?.going" style="margin-left: 8px;" @click="showShareModal">分享</a-button>
    </Teleport>
    <a-modal title="分享问卷" v-model:visible="shareModalVisible" :footer="null" :width="648" centered>
        <SurveyQrCode :name="shareName" :url="shareUrl" :loginMethod="surveyInfo.login_method"></SurveyQrCode>
    </a-modal>
</template>

<script lang="ts" setup>
import { Ref, onMounted, ref } from 'vue'
import { answerquestionnairecontent, submitquestionnaire } from '@/api/admin/survey'
import { QuestionEnum } from '@/models/questionModel';
import { scrollToFirstErrFormItem } from '@/utils/common'
import _ from 'lodash';
import { FormInstance } from 'ant-design-vue/es/form';
import { setSurveyQuestionNumber } from '@/utils';
import SurveyQrCode from './SurveyQrCode.vue'
import { getStudentOrigin } from '@/utils/url';

const props = defineProps<{
    token?: string

    /** 实时预览传入的信息 */
    data?: any
}>()

const formRef = ref<FormInstance>()
const surveyInfo = ref({}) as Ref<any>
async function getDetail() {
    if (props.token) {
        try {
            let res = await answerquestionnairecontent({ urlId: props.token })

            surveyInfo.value = res
        } catch (error) {
            console.log(error)
        }
    } else if (props.data) {
        surveyInfo.value = _.cloneDeep(props.data)
    }
    
    setSurveyQuestionNumber(surveyInfo.value.content)
}
getDetail()

// 打印相关
const printLoading = ref(false)
const printConfig = {
    // 要打印的区域
    id: 'preview-frame',
    // 打印的标题
    popTitle: '',
    extraHead: `<title></title>`,
    // 打印前回调
    beforeOpenCallback() {
        document.title = surveyInfo.value.name
        printLoading.value = true
    },
    // 执行打印回调
    openCallback() {
        printLoading.value = false
    },
    closeCallback() {
        document.title = '图灵智面教师系统'
    }
}

// 分享
const shareModalVisible = ref(false)
const shareUrl = ref('')
const shareName = ref('')
function showShareModal() {
    if (!props.token) return

    shareModalVisible.value = true

    let origin = getStudentOrigin()

    shareUrl.value = `${origin}/#/surveyPaper/${props.token}`
    shareName.value = surveyInfo.value.name
}

const showToolbar = ref(false)
onMounted(() => {
    showToolbar.value = true
})


defineExpose({
    contentEle: () => document.querySelector('.survey-content')
})

</script>

<style lang="less" scoped>
.survey-paper {
    height: 100%;
    background: linear-gradient(230deg, #e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);
    overflow: hidden;

    .survey-paper-main {
        height: 100%;
        overflow: hidden;
        margin: auto;
        max-width: 1000px;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        box-shadow: 0 1px 3px #0000001a;
        border-radius: 8px;
    }

    .survey-title {
        margin: 40px auto 0;
        font-size: 28px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
        line-height: 48px;
        text-align: center;
    }

    .survey-tip {
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        line-height: 24px;
        margin-top: 16px;
        margin-bottom: 24px;
        white-space: break-spaces;
    }

    .survey-content {
        padding: 16px 40px 40px 40px;
        flex: 1;
        min-height: 0;
        overflow: auto;

        .ques-item {
            padding-top: 16px;
            overflow: hidden;

            .ques-title {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 24px;
                margin-bottom: 16px;
            }
        }
    }
}

.mark-ques {
    width: fit-content;
    
    .mark-ques-stars {
        display: flex;
        gap: 32px;
        position: relative;
    }

    .svg-icon {
        cursor: pointer;
    }

    .mark-desc {
        display: inline-block;
        margin-top: 8px;
        padding: 0 4px;
        border-radius: 4px;
        background: #f5f5f5;
        color: #626262;
    }
}

.submit-btn {
    width: 80px;
    height: 40px;

    @media (max-width: 1440px) {
        width: 100%;
    }
}

.options-group {
    display: flex;
    flex-direction: column;
    gap: 18px;

    .option-item {
        display: flex;
        align-items: flex-start;
    }

    .option-selectbox {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 1px solid #d9d9d9;
        margin-right: 18px;
        position: relative;
        top: 2px;
        flex-shrink: 0;

        &.option-selectbox-radio {
            border-radius: 50%;
        }
    }
}
</style>