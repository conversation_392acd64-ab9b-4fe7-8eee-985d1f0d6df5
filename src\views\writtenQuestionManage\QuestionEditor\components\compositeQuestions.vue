<script setup lang="ts">
import QuestionEditor from './Editor.vue'
import QuestionStem from './QuestionStem.vue'

const props = defineProps<{
  formState: any
  subQuestionList?: any
  handleComplexSwitchChange?: any
  handleDelete?: any
  handleUp?: any
  handleDown?: any
  subQuestionsRef?: any
  questionsObj?: any
  addCompoundQuestions?: any
}>()
</script>

<template>
  <QuestionStem />
  <a-collapse v-if="formState.type === 7 && subQuestionList.value.length" :accordion="false">
    <template v-for="(item, index) in subQuestionList.value" :key="index">
      <a-collapse-panel :header="`【${item.label}】 ${item.formState.body}`">
        <template #extra>
          <span class="complex-switch-box" @click.stop>
            <span class="label">复杂编辑</span>
            <JSwitch
              v-model:checked="item.formState.complicatedediting"
              @change="handleComplexSwitchChange(item.formState, $event, index)"
            />
          </span>
          <img src="@/assets/images/delete.png" alt="删除" @click="handleDelete(index)">
          <img
            v-if="index !== 0"
            style="margin-left: 16px"
            src="@/assets/icons/svg/compoundUp.svg"
            alt="上移"
            @click="handleUp($event, index)"
          >
          <img
            v-if="index !== subQuestionList.length - 1"
            style="margin-left: 16px"
            src="@/assets/icons/svg/compoundDown.svg"
            alt="下移"
            @click="handleDown(index)"
          >
        </template>
        <QuestionEditor :ref="subQuestionsRef" v-model="item.formState" composite :component-type="item.type" />
      </a-collapse-panel>
    </template>
  </a-collapse>

  <a-form-item
    v-if="formState.type === 7"
    class="question-options compound"
    name="subQuestionList"
  >
    <!-- eslint-disable vue/no-mutating-props -->
    <a-radio-group v-model:value="formState.answer">
      <a-dropdown trigger="click" class="compoundQuestions">
        <template #overlay>
          <a-menu class="compoundItem">
            <template v-for="item in questionsObj" :key="item.key">
              <a-menu-item @click="addCompoundQuestions(item)">
                {{
                  item.questionCate
                }}
              </a-menu-item>
            </template>
          </a-menu>
        </template>
        <!-- <img src="@/assets/images/add.png" alt=""> -->
        <a-button class="compoundQuestionsBtn flex items-center" type="text">
          <img src="@/assets/icons/svg/addItem.svg" alt="请选择子试题" class="addItem mr-[3px] mt-[2px]">
          请选择子试题
        </a-button>
      </a-dropdown>
    </a-radio-group>
  </a-form-item>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>