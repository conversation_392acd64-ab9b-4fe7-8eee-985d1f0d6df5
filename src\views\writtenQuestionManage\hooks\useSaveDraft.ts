import { watch, ref, onMounted, onBeforeUnmount } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { draftques } from '@/api/admin/questionManage'

export default (formState: any) => {
  const uuid = ref()
  let count = 0
  const countTimer = ref<any>(null)
  const autoSaveTimer = ref<any>(null)
  const realtime = ref('')

  const saveDraft = (formState: any) => {
    const { score, type, category } = formState
    const params = {
      action: 'add',
      id: uuid.value,
      content: formState,
      score,
      type,
      category,
      title: formState.body
    }
    draftques(params).then((data: any) => {
      realtime.value = data.split(' ')[1]
    })
  }

  watch(
    formState,
    (val) => {
      if (val.body) {
        if (count >= 5) {
          saveDraft(val)
          count = 0
          return
        }
        if (autoSaveTimer.value) {
          clearTimeout(autoSaveTimer.value)
        }
        autoSaveTimer.value = setTimeout(() => {
          saveDraft(val)
          count = 0
        }, 2000)
      }
    },
    { deep: true }
  )

  onMounted(() => {
    // 生成uuid
    uuid.value = uuidv4()
    countTimer.value = setInterval(() => {
      count++
    }, 1000)
  })

  onBeforeUnmount(() => {
    clearInterval(countTimer.value)
  })

  return { uuid, realtime }
}
