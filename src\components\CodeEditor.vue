<template>
  <div class="editor" :style="{ height: props.height }" ref="dom"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import JsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import 'monaco-editor/esm/vs/basic-languages/python/python.contribution'
import 'monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution'
import 'monaco-editor/esm/vs/basic-languages/java/java.contribution'
import 'monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution'
import 'monaco-editor/esm/vs/basic-languages/go/go.contribution'

monaco.languages.register({
  id: 'javascript'
})
// @ts-ignore
self.MonacoEnvironment = {
  getWorker(workerId, label) {
    if (label === 'json') {
      return new JsonWorker()
    }
    return new EditorWorker()
  }
}

const props = defineProps({
  modelValue: String,
  answer: {
    type: String,
    default: ''
  },
  lang: {
    type: String,
    default: 'c'
  },
  height: {
    type: String,
    default: '100%'
  },
  counter: {
    type: Number,
    default: 0
  },
  copy: {
    type: Number,
    default: 0
  },
  paste: {
    type: Number,
    default: 0
  },
  template: {
    type: Object,
    default: () => {}
  },
  fontSize: {
    type: String,
    default: '14px'
  }
})

const emit = defineEmits(['update:modelValue', 'changeLang'])

const dom = ref()
const cTemplate = ref('')
const cppTemplate = ref('')
const pyTemplate = ref('')
const javaTemplate = ref('')
const jsTemplate = ref('')
const goTemplate = ref('')

let instance: any

const resetCode = ref(false)
const codeData = <any>ref({})
const coding = (language: string) => {
  const lang = language === 'js' ? 'javascript' : language
  const jsonModel = monaco.editor.createModel(props.modelValue as string, lang)
  instance = monaco.editor.create(dom.value, {
    model: jsonModel,
    fontSize: font.value,
    fontFamily: 'Consolas',
    codeLensFontFamily:"Consolas",
    tabSize: 2,
    automaticLayout: true,
    scrollBeyondLastLine: false,
    theme: 'vs-dark',
    mouseWheelZoom: false,
    scrollbar: {
      alwaysConsumeMouseWheel: false
    },
    minimap: {
      // 关闭代码缩略图
      enabled: false // 是否启用预览图
    },
    cursorWidth: 2
  })
  instance.updateOptions({ contextmenu: false })
  if (language === 'c') {
    // instance.setValue(cTemplate.value)
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['c']
        ? instance.setValue(props.template['c'])
        : instance.setValue('')
    }
  }
  if (language === 'cpp') {
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['cpp']
        ? instance.setValue(props.template['cpp'])
        : instance.setValue('')
    }
  }
  if (language === 'python') {
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['python']
        ? instance.setValue(props.template['python'])
        : instance.setValue('')
    }
  }
  if (language === 'java') {
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['java']
        ? instance.setValue(props.template['java'])
        : instance.setValue('')
    }
  }
  if (language === 'js') {
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['js']
        ? instance.setValue(props.template['js'])
        : instance.setValue('')
    }
  }
  if (language === 'go') {
    if (instance.getValue() && !resetCode.value) {
      instance.setValue(instance.getValue())
    } else {
      props.template && props.template['go']
        ? instance.setValue(props.template['go'])
        : instance.setValue('')
    }
  }

  console.log(instance.getValue())
  emit('update:modelValue', instance.getValue())

  instance.onDidChangeModelContent(() => {
    const value = instance.getValue()
    codeData.value[props.lang] = value
    emit('update:modelValue', value)
  })
}
watch(
  () => props.lang,
  (val) => {
    const language = val === 'js' ? 'javascript' : val
    monaco.editor.setModelLanguage(instance.getModel(), language)
    switch (props.lang) {
      case 'c':
        const ctemplate = props.template['c'] ? props.template['c'] : cTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : ctemplate)
        break
      case 'cpp':
        const cpptemplate = props.template['cpp'] ? props.template['cpp'] : cppTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : cpptemplate)
        break
      case 'python':
        const pytemplate = props.template['python'] ? props.template['python'] : pyTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : pytemplate)
        instance.setValue(instance.getValue())
        break
      case 'java':
        const javatemplate = props.template['java'] ? props.template['java'] : javaTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : javatemplate)
        instance.setValue(instance.getValue())
        break
      case 'js':
        const jstemplate = props.template['js'] ? props.template['js'] : jsTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : jstemplate)
        instance.setValue(instance.getValue())
        break
      case 'go':
        const gotemplate = props.template['go'] ? props.template['go'] : goTemplate.value
        instance.setValue(codeData.value[props.lang] ? codeData.value[props.lang] : gotemplate)
        instance.setValue(instance.getValue())
        break
    }
    // emit('update:modelValue', instance.getValue())

    // if (
    //   // instance.getValue() === cTemplate.value ||
    //   // instance.getValue() === cppTemplate.value ||
    //   // instance.getValue() === ''
    //   instance.getValue() === (props.template['C'] && props.template['C']['text']) ||
    //   instance.getValue() === (props.template['C++'] && props.template['C++']['text']) ||
    //   instance.getValue() === ''
    // ) {
    //   instance.dispose() //使用完成销毁实例
    //   coding(props.lang)
    // } else {
    //   message.warning('编码区代码有修改，请先重置或清空代码！')
    //   //- 设置语言
    //   monaco.editor.setModelLanguage(instance.getModel(), props.lang)
    // }
  }
)

watch(
  () => props.template,
  (val) => {
    if(!val) return
    const language = props.lang === 'js' ? 'javascript' : props.lang
    monaco.editor.setModelLanguage(instance.getModel(), language)
    switch (props.lang) {
      case 'c':
        const ctemplate = val['c'] ? val['c'] : cTemplate.value
        instance.setValue(ctemplate)
        instance.setValue(instance.getValue())
        break
      case 'cpp':
        const cpptemplate = val['cpp'] ? val['cpp'] : cppTemplate.value
        instance.setValue(cpptemplate)
        instance.setValue(instance.getValue())
        break
      case 'python':
        const pytemplate = val['python'] ? val['python'] : pyTemplate.value
        instance.setValue(pytemplate)
        instance.setValue(instance.getValue())
        break
      case 'java':
        const javatemplate = val['java'] ? val['java'] : javaTemplate.value
        instance.setValue(javatemplate)
        instance.setValue(instance.getValue())
        break
      case 'js':
        const jstemplate = val['js'] ? val['js'] : jsTemplate.value
        instance.setValue(jstemplate)
        instance.setValue(instance.getValue())
        break
      case 'go':
        const gotemplate = val['go'] ? val['go'] : goTemplate.value
        instance.setValue(gotemplate)
        instance.setValue(instance.getValue())
        break
    }
  }
)

watch(
  () => props.counter,
  () => {
    console.log(props.template)
    instance.dispose() //使用完成销毁实例
    codeData.value[props.lang] = ''
    resetCode.value = true
    coding(props.lang)
  }
)

watch(
  () => props.answer,
  (val) => {
    if (val) {
      instance.setValue(val)
      codeData.value[props.lang] = val
    }
  }
)

const font = ref(14)
watch(
  () => props.fontSize,
  (val) => {
    if (val === '14px') {
      font.value = 14
    } else if (val === '18px') {
      font.value = 18
    }
    instance.dispose() //使用完成销毁实例
    coding(props.lang)
  }
)

onMounted(() => {
  coding(props.lang)
  emit('update:modelValue', instance.getValue())
})
</script>

<style lang="less" scoped>
.editor {
  width: 100%;
  min-height: 400px;
  transition: height 0.2s ease-in;
}
.lang-select {
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
  z-index: 999;
  background-color: #000;
}
:deep(.monaco-editor div) {
  font: 14px ConSolas !important;
}
</style>
