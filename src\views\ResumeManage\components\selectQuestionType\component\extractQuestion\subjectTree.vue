<script lang="ts" setup>
import type { QuestionModel } from '@/models/questionModel'
import { getCategory, getFistLevelTree } from '@/api/admin/questionManage'
import SearchHighLight from '@/components/SearchHighLight.vue'
import { createSubjectApi, deletePackageApi, updatePackageApi } from '@/pages/questionManage//hooks/api'
import { findItemInTree } from '@/utils'
import { SearchOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import _ from 'lodash'
// @ts-nocheck
import { onActivated, onDeactivated, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

interface TreeNode {
  key?: string
  id: string
  idlink: string
  name: string
  officialCert: boolean
  children: TreeNode[]
  topicCount?: number
  subjectCategoryUUID: string
}

const props = withDefaults(
  defineProps<{
    createable?: boolean
    editable?: boolean
    footer?: boolean
    allSelectRows?: any[]
  }>(),
  {
    createable: true,
    editable: false,
    footer: false,
    allSelectRows: () => [],
  },
)
const emits = defineEmits<{
  (e: 'select', ...args: any[]): void
  (e: 'delete', parentId: string): void
  (e: 'visibleChange'): void
}>()
const store = useStore()
const route = useRoute()
const treeData = ref<any[]>([])
const expandedKeys = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const selectedSubjectItem = ref<any>()
const labelCol = ref({ span: 5 })
const formRef = ref()
const visible = ref(false)
const openVisible = ref(false)
const formState = ref({
  name: '',
  pname: '科目列表',
  id: null,
  subjectCategoryUUID: null,
})

const isCollapse = ref(true)
function collapseMenu() {
  isCollapse.value = !isCollapse.value
  emits('visibleChange')
}

const rules = {
  name: [{ required: true, message: '科目名称不能为空', trigger: 'blur' }],
}

function resetForm(form = {}) {
  formState.value.id = form.id || null
  formState.value.name = form.name || ''
  formState.value.pname = form.pname || ''
}

const isEdit = ref(false)
const officialCert = ref<boolean>(false)
function addNewSubDep(item: any) {
  isEdit.value = false
  resetForm()
  visible.value = true
  if (item) {
    formState.value.pname = item.name
    formState.value.id = item.id
    formState.value.prePath = item.path
  }
  else {
    formState.value.prePath = route.query.path as string
  }
}

const condition = ref<boolean>(true)
const deleteText = ref<string>('')
async function handleVisibleChange(bool: boolean, item: any) {
  if (bool) {
    condition.value = true
    const res = await deletePackageApi({ id: item.id, path: item.path, confirm: false })
    if (!res.length) {
      deleteText.value = '确认要删除该科目以及其包含的所有子科目和题目吗？'
    }
    else {
      deleteText.value = `确认删除当前题库及所含题目吗？以下AI面试：${res.map((item: any) => `《${item.title}》-- 创建人`).join(';')}`
    }
    condition.value = false
  }
}

function editCurrentDep(item: any) {
  item.isEdit = true
  isEdit.value = true
  resetForm(item)
  visible.value = true
  if (item) {
    formState.value.pname = item.name
    formState.value.id = item.id
    formState.value.prePath = item.path.split('/')
    formState.value.prePath.pop()
    formState.value.prePath = formState.value.prePath.join('/')
  }
  else {
    formState.value.prePath = route.query.path as string
  }
}

async function delCurrentDep(item: any) {
  let path = getParentPath(item.path, treeData.value) as string
  await deletePackageApi({ id: item.id, path: item.path, confirm: true })
  item.openVisible = false
  await getSubjectData()
  if (path) {
    selectedKeys.value = [path]
  }
  else {
    selectedKeys.value = [treeData.value[0].path]
  }
  emits('delete', path)
}

function onSelect(keys: string[], { node }: any) {
  if (keys.length === 0) 
    return
  selectedKeys.value = keys
  selectedSubjectItem.value = node
  emits('select', node)
}

function onExpand(keys: string[]) {
  expandedKeys.value = keys
  autoExpandParent.value = false
}

async function onDrop(info: any) {
  const { dragNode, node } = info
  const changePath = `${node.path}/${dragNode.name}`
  try {
    await updatePackageApi({
      id: dragNode.id,
      path: changePath,
    })
    await getSubjectData()

    expandedKeys.value = [info.node.dataRef.path, ...expandedKeys.value]
    selectedKeys.value = [changePath]
    emits('select', info.dragNode)
  }
  catch (e) {
    message.error('当前标签名和拖拽后的同级标签有重名，不允许拖拽')
  }
}

function closeModal() {
  visible.value = false
  formRef.value.resetFields()
}

const loading = ref(false)
async function onSubmit() {
  await formRef.value.validate()
  loading.value = true
  const basePath = route.query.path as string
  const addParams = {
    name: formState.value.name,
    path: `${formState.value.prePath}/${formState.value.name}`,
  }

  const modifyParams = {
    id: formState.value.id,
    name: formState.value.name,
    path: `${formState.value.prePath}/${formState.value.name}`,
  }
  isEdit.value ? modifyDept(modifyParams) : addDept(addParams)
}

async function addDept(params: any) {
  try {
    autoExpandParent.value = true
    const { path } = params;
    (await createSubjectApi({ ...params })) as unknown as string
    closeModal()
    await getSubjectData()
    if (path) {
      expandedKeys.value = [...expandedKeys.value, getParentPath(path, treeData.value)]
      // const subjectCategoryUUID = getSubjectCategoryUUIDById(id, treeData.value);
      selectedKeys.value = [path]
      emits('select', { id, subjectCategoryUUID, officialCert: officialCert.value })
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    loading.value = false
  }
}

async function modifyDept(params: any) {
  try {
    await updatePackageApi({ ...params })
    closeModal()
    await getSubjectData()
    selectedKeys.value = [params.path]
    // const subjectCategoryUUID = getSubjectCategoryUUIDById(params.path, treeData.value);
    emits('select', { id: params.id })
  }
  finally {
    loading.value = false
  }
}

const originTreeData = ref<TreeNode[]>([])
const fetchLoading = ref(false)
function addOpenVisible(res) {
  // 递归遍历res 加上openVisible
  res.forEach((node: any) => {
    node.openVisible = false
    if (node.children && node.children.length > 0) {
      addOpenVisible(node.children)
    }
  })
}

function onLoadData(treeNode: any) {
  return new Promise<void>((resolve) => {
    if (treeNode.dataRef.children) {
      resolve()
      return
    }
    getCategory({ path: treeNode.path as string }).then((res: any) => {
      treeNode.dataRef.children = res
      treeData.value = [...treeData.value]
      resolve()
    })
  })
}
async function getSubjectData() {
  fetchLoading.value = true
  try {
    //     let res: any = await getCategory({ path: route.query.path as string })
    let res: any = await getFistLevelTree({ paegNo: 1, pageSize: 300 })
    res = Array.isArray(res) ? res : res.records ? res.records : []
    res.forEach((item: any) => {
      delete item.children
    })
    if (!props.editable) {
      res.forEach((node: any) => setNodeTopicCount(node, catgsQuesNumMap.value))
    }
    addOpenVisible(res)
    treeData.value = res
    originTreeData.value = res
    searchValue.value = ''
  }
  finally {
    fetchLoading.value = false
  }
}

const searchIptRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const autoExpandParent = ref(false)
function handleSearch() {
  searchOpen.value = !searchOpen.value
  searchIptRef.value.focus()
}
function change(row: any) {
  if (row.type == 'click') {
    searchOpen.value = false
  }
}
function getParentId(id: string | number, tree: TreeNode[]): string | number | undefined {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.id === id)) {
        parentKey = node.id
      }
      else if (getParentId(id, node.children)) {
        parentKey = getParentId(id, node.children)
      }
    }
  }
  return parentKey
}
function getParentPath(path: string | number, tree: TreeNode[]): string | number | undefined {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.path === path)) {
        parentKey = node.path
      }
      else if (getParentPath(path, node.children)) {
        parentKey = getParentPath(path, node.children)
      }
    }
  }
  return parentKey
}
function getSubjectCategoryUUID(
  id: string | number,
  tree: TreeNode[],
  res?: any,
): string | number | undefined {
  if (res) 
    return res
  res = null
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.subjectCategoryUUID === id) {
      res = node.id
      return res
    }
    else if (node.children && node.children.length) {
      res = getSubjectCategoryUUID(id, node.children, res)
    }
  }
  return res
}

function getSubjectCategoryUUIDById(
  id: string | number,
  tree: TreeNode[],
  res?: any,
): string | number | undefined {
  if (res) 
    return res
  res = null
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.id === id) {
      res = node.subjectCategoryUUID
      return res
    }
    else if (node.children && node.children.length) {
      res = getSubjectCategoryUUIDById(id, node.children, res)
    }
  }
  return res
}

watch(searchValue, (val) => {
  treeData.value = filterTree(originTreeData.value, val)

  autoExpandParent.value = true
  if (val) 
    return
  let id = getParentPath(selectedKeys.value[0], originTreeData.value)
  expandedKeys.value = [id]
})

function filterTree(roots: TreeNode[], filterText: string): TreeNode[] {
  if (!roots) 
    return []
  return roots.map(root => filterSingleNode(root, filterText)).filter(Boolean) as TreeNode[]
}

function filterSingleNode(node: TreeNode, filterText: string): TreeNode | null {
  if (node.name.includes(filterText)) {
    if (filterText) {
      let path = getParentPath(node.path, originTreeData.value)
      expandedKeys.value.push(path)
    }
    return {
      ...node,
      children: filterTree(node.children, filterText),
    }
  }
  else {
    const filteredChildren = filterTree(node.children, filterText)
    if (filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
      }
    }
    else {
      return null
    }
  }
}

const showTopicCount = ref(true)
const catgsQuesNumMap = ref<Record<string, string[]>>({})
function initCatgsQuesNumMap() {
  // 清空之前的数据，避免重复累加
  catgsQuesNumMap.value = {}

  if (!props.allSelectRows?.length) 
    return
  let list = props.allSelectRows.map(item => item.children).flat()
  // 过滤掉无效项
  list = list.filter(item => item && item.id && item.bankPath)
  // 使用 Set 去重，避免重复添加相同的题目
  const uniqueItems = []
  const idSet = new Set()

  list.forEach((item) => {
    if (!idSet.has(item.id)) {
      idSet.add(item.id)
      uniqueItems.push(item)
    }
  })

  uniqueItems.forEach(addItemToCatgsQuesNumMap)
}
// 不在这里立即调用，而是在 watch 中监听 props.allSelectRows 变化时调用
function addItemToCatgsQuesNumMap(item: QuestionModel) {
  if (Reflect.has(catgsQuesNumMap.value, item.bankPath)) {
    // 检查是否已经存在相同的 ID，避免重复添加
    if (!catgsQuesNumMap.value[item.bankPath].includes(item.id!)) {
      catgsQuesNumMap.value[item.bankPath].push(item.id!)
    }
  }
  else {
    Reflect.set(catgsQuesNumMap.value, item.bankPath, [item.id])
  }
}
function removeItemFromCatgsQuesNumMap(item: QuestionModel) {
  try {
    if (!catgsQuesNumMap.value[item.bankPath]) 
      return

    let index = catgsQuesNumMap.value[item.bankPath].findIndex(
      id => id === item.id,
    )

    if (index !== -1) {
      catgsQuesNumMap.value[item.bankPath].splice(index, 1)
      // 如果数组为空，删除该路径的记录
      if (catgsQuesNumMap.value[item.bankPath].length === 0) {
        Reflect.deleteProperty(catgsQuesNumMap.value, item.bankPath)
      }
    }
  }
  catch (error) {
    console.log(error)
  }
}
function setNodeTopicCount(node: TreeNode, map: Record<string, string[]>) {
  // 获取当前节点的题目数量
  let topicCount = map[node.path]?.length ?? 0

  // 递归计算子节点的题目数量
  if (node.children?.length) {
    topicCount += node.children.reduce((pre: number, cur: TreeNode) => {
      setNodeTopicCount(cur, map)
      return pre + (cur.topicCount ?? 0)
    }, 0)
  }

  // 设置节点的题目数量
  node.topicCount = topicCount
}

// 监听 allSelectRows 变化，重新初始化题目数量映射
watch(
  () => props.allSelectRows,
  () => {
    initCatgsQuesNumMap()
  },
  { deep: true, immediate: true },
)

watch(
  catgsQuesNumMap,
  (map) => {
    if (!props.editable) {
      originTreeData.value.forEach(node => setNodeTopicCount(node, map))
      treeData.value = filterTree(originTreeData.value, searchValue.value)
    }
  },
  { deep: true },
)

const lastBoxWidth = ref(240)
const BOX_MAX_WIDTH = 400
const BOX_MIN_WIDTH = 240
const boxWidth = ref(240)
const startX = ref(0)
const isMoving = ref(false)
function startResize(e: MouseEvent) {
  startX.value = e.clientX
  isMoving.value = true
  window.addEventListener('mousemove', resize)
  window.addEventListener('mouseup', stopResize)
}

function resize(e: MouseEvent) {
  if (!isMoving.value) 
    return
  const newWidth = lastBoxWidth.value + e.clientX - startX.value
  boxWidth.value = Math.max(Math.min(newWidth, BOX_MAX_WIDTH), BOX_MIN_WIDTH)
}

function stopResize() {
  isMoving.value = false
  lastBoxWidth.value = boxWidth.value
  window.removeEventListener('mousemove', resize)
  window.removeEventListener('mouseup', stopResize)
}

async function init() {
  await getSubjectData()
  if (!selectedKeys.value.length) {
    selectedKeys.value = [treeData.value[0].path]
    emits('select', treeData.value[0])
  }
  else {
    let id = selectedKeys.value[0]
    let item = findItemInTree(id, treeData.value)
    emits('select', item)
  }

  if (!expandedKeys.value.length) 
    expandedKeys.value = [treeData.value[0].id]
}
const debounceInit = _.debounce(init, 1000)

onMounted(() => {
  debounceInit()
})

onActivated(() => {
  debounceInit()
})

onDeactivated(() => {
  visible.value = false
})

onUnmounted(() => {
  stopResize()
})

defineExpose({
  addItemToCatgsQuesNumMap,
  removeItemFromCatgsQuesNumMap,
})
</script>

<template>
  <div class="subjects">
    <div v-show="!isCollapse" class="collapse-menu-wrapper" @click="collapseMenu">
      <svg-icon name="expand-menu" class="menu-icon" />
    </div>
    <div
      v-show="isCollapse"
      class="category-container"
      :class="{ isCollapse: !isCollapse }"
      :style="{ width: `${boxWidth}px` }"
    >
      <div class="header">
        <h2>科目列表</h2>
        <div class="search-ipt-wrapper" :class="{ active: searchOpen }">
          <a-input
            ref="searchIptRef"
            v-model:value.trim="searchValue"
            class="search-ipt"
            :bordered="false"
            placeholder="请输入题库/科目名称"
            allow-clear
            @change="change"
          />
          <SearchOutlined @click="handleSearch" />
        </div>
        <div>        
          <svg-icon v-if="createable" name="plus" class="menu-icon mr-[10px]" @click.stop="addNewSubDep(null)" />
          <svg-icon name="collapse-menu" class="menu-icon" @click="collapseMenu" />
        </div>
      </div>
      <div class="body">
        <a-spin v-if="fetchLoading" class="loading-style" />
        <img
          v-else-if="!fetchLoading && !treeData.length"
          style="width: 200px; margin-top: 100px"
          src="@/assets/images/nodata.png"
          alt=""
        >
        <a-tree
          v-else
          v-model:expanded-keys="expandedKeys"
          :draggable="editable && !isMoving"
          show-icon
          show-line
          :block-node="true"
          :tree-data="treeData "
          :load-data="onLoadData"
          :auto-expand-parent="autoExpandParent"
          :field-names="{ title: 'name', key: 'path' }"
          :selected-keys="selectedKeys"
          @drop="onDrop"
          @select="onSelect as any"
          @expand="onExpand as any"
        >
          <template #icon="item">
            <div>
              <img
                v-if="item.children && item.children.length && !item.children[0].children && expandedKeys.includes(item.path)"
                src="@/assets/images/admin/folder.png"
                style="width: 16px; margin-right: 4px; margin-bottom: -2px"
              >
              <img
                v-else-if="item.children && item.children.length"
                src="@/assets/images/svg/folder1.svg"
                style="width: 16px; margin-right: 4px; margin-bottom: -2px"
              >
              <img
                v-else
                src="@/assets/images/admin/file.png"
                style="width: 16px; margin-right: 4px; margin-bottom: -2px"
              >
            </div>
          </template>
          <template #title="item">
            <div class="custom-title">
              <SearchHighLight
                class="title"
                :text="item.name"
                :search-text="searchValue"
                style="font-weight: normal;"
              />
              <div v-if="showTopicCount && item.topicCount" class="count">
                ({{ item.topicCount }})
              </div>
              <div
                v-if="editable && (store.getters.userInfo.officialCert || !item.officialCert)"
                class="operbtns"
              >
                <span
                  class="icon"
                  title="新建子科目"
                  @click.stop="addNewSubDep(item)"
                >
                  <svg-icon name="plus" />
                </span>
                <span
                  v-if="item.name !== '官方科目' && item.name !== '部门科目'"
                  class="icon"
                  title="编辑科目"
                  @click.stop="editCurrentDep(item)"
                >
                  <svg-icon name="edit" />
                </span>
                <!-- 
                <a-popconfirm
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="delCurrentDep(item)"
                  @cancel="cancelPopconfirm(item)"
                  :open="item.openVisible"
                  @openChange="handleVisibleChange($event, item)"
                > -->
                
                <a-popconfirm
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="delCurrentDep(item)"
                  @open-change="handleVisibleChange($event, item)"
                >
                  <template #title>
                    <div>
                      {{ deleteText }}
                    </div>
                  </template>
                  <span class="icon" title="删除科目">
                    <img src="@/assets/icons/svg/delete.svg" class="del-icon">
                  </span>
                </a-popconfirm>
              </div>
            </div>
          </template>
          <template #switcherIcon="{ switcherCls }">
            <Icon icon="DownOutlined" :class="switcherCls" />
          </template>
        </a-tree>
      </div>
      <div v-if="footer" class="footer">
        <a-checkbox v-model:checked="showTopicCount">
          显示当前试卷题量数
        </a-checkbox>
        <a-tooltip placement="right" overlay-class-name="light">
          <template #title>
            <span>勾选后将展示科目中已选题目的数量</span>
          </template>
          <svg-icon class="common-info-icon" name="info2" />
        </a-tooltip>
      </div>
      <div
        id="resizer"
        style="
          width: 4px;
          height: 100%;
          position: absolute;
          right: 0;
          top: 0;
          cursor: e-resize;
          border-right: 2px solid #5478ee;
          transition: all ease 0.2s;
          opacity: 0;
        "
        @mousedown="startResize"
      />
    </div>
    <a-modal
      v-model:visible="visible"
      class="common-modal"
      :title="isEdit ? '编辑科目' : '新增科目'"
      width="420px"
      :mask-closable="false"
      @cancel="closeModal"
    >
      <template #footer>
        <a-button type="primary" :loading="loading" @click="onSubmit">
          保存
        </a-button>
        <a-button @click="closeModal">
          取消
        </a-button>
      </template>
      <a-form
        ref="formRef"
        class="dep-form"
        :label-col="labelCol"
        :model="formState"
        :hide-required-mark="true"
        :rules="rules as any"
        :colon="false"
      >
        <a-form-item label="科目名称" name="name">
          <a-input
            v-model:value.trim="formState.name"
            :max-length="10"
            placeholder="科目名称，最多10个字符"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-tree) {
    .ant-tree-switcher {
        background: transparent !important;
        line-height: 40px;
        margin-left: -8px;
    }
}
.subjects {
  height: 100%;
  user-select: none;

  .collapse-menu-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 48px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    z-index: 999;
    cursor: pointer;
  }

  .menu-icon {
    cursor: pointer;
  }

  .category-container {
    height: 100%;
    flex-shrink: 0 !important;
    overflow: auto;
    background: #fff;
    display: flex;
    flex-direction: column;
    position: relative;
    border-right: 1px solid #e8e8e8;

    #resizer:hover {
      opacity: 1 !important;
    }

    &.isCollapse {
      width: 0;
    }

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 48px;
      padding: 0 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      position: relative;

      h2 {
        font-family: PingFang SC;
        font-size: 14px;
        line-height: inherit;
        color: #121633;
        font-weight: bold;
      }

      .plus-icon {
        cursor: pointer;
      }

      .addsubject {
        display: none;
      }

      .search-ipt-wrapper {
        position: absolute;
        right: 60px;
        background-color: #fff;
        padding: 4px;
        border-radius: 8px;
        display: flex;

        &.active {
          border: 1px solid #5478ee;

          .search-ipt {
            padding: 0 2px;
            width: 135px;
          }
        }

        .anticon {
          cursor: pointer;
          padding: 4px;
        }

        .anticon-search {
          z-index: 999;
          border-radius: 4px;
          background-color: #fff;

          &:hover {
            background-color: #e8e8e8;
          }
        }

        .search-ipt {
          width: 0;
          height: 22px;
          padding: 0;
          border: none;
          box-shadow: none;

          :deep(.ant-input) {
            font-size: 13px;
          }
        }
      }
    }
  }

  .loading-style {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .body {
    padding-left: 16px;
    padding-top: 8px;
    overflow: auto;
    flex: 1;
    min-height: 0;
  }

  .footer {
    height: 48px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding-left: 16px;

    :deep(.ant-checkbox + span) {
      font-size: 13px;
    }
  }

  :deep(.ant-tree-node-content-wrapper) {
    flex: auto;
    min-width: 0;
    padding: 8px 0 !important;
  }

  :deep(.ant-tree-title) {
    flex: auto;
    min-width: 0;
  }

  .custom-title {
    max-width: 100%;
    height: 24px;
    padding-right: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      min-width: 0;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .count {
      margin-left: 4px;
    }

    .operbtns {
      justify-content: space-around;
      display: none;

      .icon {
        margin-left: 4px;

        &:first-of-type {
          margin-left: 0;
        }
      }

      .del-icon {
        width: 14px;
        position: relative;
        bottom: 2px;
      }
    }

    &:hover .operbtns {
      display: flex;
      text-align: right;
    }
  }
}
</style>
