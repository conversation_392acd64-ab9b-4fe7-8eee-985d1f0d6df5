<template>
  <div class="lose-efficacy-wrap">
    <div class="header" @click="backToWelcome">
      <img src="@/assets/images/admin/admin-logo.svg" class="admin-login" alt=""  />
      <span class="title">图灵智面</span>
    </div>
    <div class="content" v-if="!hasSendedSuccess">
      <div class="title">抱歉</div>
      <div class="tip">密码重置链接已过期（有效期为 <span>10</span> 分钟），请重新输入邮箱</div>
      <a-form :model="formState" ref="formRef" :rules="rules">
        <a-form-item name="email">
          <a-input
            class="form-input"
            v-model:value.trim="formState.email"
            placeholder="请输入邮箱"
          />
        </a-form-item>
      </a-form>
      <a-button class="register" type="primary" :loading="loading" @click="onSubmit">
        发送密码重置邮件
      </a-button>
      <img class="img" :src="loseEfficacyImg" alt="">
    </div>
    <div v-else class="send-email-notes">
      <div class="title">忘记密码</div>
      <div class="notes-content">
        <div class="send-email-info">
          验证邮件已经发送至您的邮箱 <span>{{ formState.email }}</span>
        </div>
        <div class="tip">请接收邮件，并按邮件提示操作</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { checkEmail } from '@/utils/validate'
import { resetpwd } from '@/api/user'
import loseEfficacyImg from '@/assets/images/loseEfficacy.png'

const router = useRouter()

const formRef = ref()
const formState = reactive({
  email: '',
})
const rules = {
  email: [{ required: true, validator: checkEmail(), trigger: 'blur' }],
}

const hasSendedSuccess = ref(false)
const loading = ref(false)
const onSubmit = async () => { // 发送邮件
  loading.value = true
  try {
    await formRef.value.validate()
    await resetpwd({ action: 'query', email: formState.email })
    hasSendedSuccess.value = true
  } finally {
    loading.value = false
  }
}

const backToWelcome = () => {
  router.replace('/admin/welcome')
}
</script>

<style lang="less" scoped>
.lose-efficacy-wrap {
  width: 100vw;
  height: 100vh;
  min-width: 480px;
  min-height: 700px;
  position: relative;
  background: #f4f4f4;
  display: flex;
  justify-content: center;
  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 48px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.13);
    padding-left: 25px;
    display: flex;
    align-items: center;
    .title {
      font-size: 20px;
      font-weight: 600;
      margin-left: 18px;
      color: rgba(0,0,0,0.85);
    }
  }
  .content {
    width: 427px;
    margin-top: 128px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      height: 60px;
      height: 60px;
      font-size: 40px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 600;
      text-align: center;
      color: #000000;
      line-height: 60px;
    }
    .tip {
      margin-top: 24px;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC-Regular;
      text-align: center;
      color: #000000;
      line-height: 22px;
      span {
        font-weight: 600;
      }
    }
    .form-input {
      width: 372px;
      height: 52px;
      margin-top: 24px;
      border: 1px solid #d1d1d1;
      border-radius: 8px;
      font-size: 16px;
      font-family: PingFang SC, PingFang SC-5;
      font-weight: 400;
      text-align: left;
      line-height: 52px;
    }
    .register {
      width: 372px;
      height: 48px;
      border-radius: 8px;
    }
    .img {
      width: 300px;
      height: 244px;
      object-fit: contain;
      margin-top: 48px;
    }
  }

  .send-email-notes {
    position: absolute;
    left: 50%;
    top: 30%;
    translate: -50% -50%;
    width: 50%;
    min-width: 480px;
    height: 220px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;
    .title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 38px;
    }
    .notes-content {
      width: 400px;
      margin: 0 auto;
      .send-email-info {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        span {
          font-size: 14px;
          color: #6182ef;
        }
      }
      .tip {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>
