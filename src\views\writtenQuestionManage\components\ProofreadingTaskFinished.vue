<template>
    <div class="proofreading-task-finished">
        <div class="proofreading-detail">
            <ul v-if="data">
                <li class="flex">
                    <span class="label">校对正确</span>
                    <span class="value">{{ data.correctQues }}道</span>
                </li>
                <li class="flex">
                    <span class="label">校对错误</span>
                    <span class="value">{{ data.errorQues }}道</span>
                </li>
                <li class="flex">
                    <span class="label">校对失败</span>
                    <span class="value">{{ data.failedQues }}道</span>
                </li>
                <a-divider/>
                <li class="flex">
                    <span class="label">校对合计</span>
                    <span class="value">{{ data.totalQues }}道</span>
                </li>
            </ul>
        </div>
        <div style="text-align: center; margin-top: 24px;">
            <a-button type="primary" style="border-radius: 8px;font-size: 14px;" @click="emits('close')">我已知晓</a-button>
        </div>
    </div>
</template>

<script lang="ts" setup>

const props = defineProps<{
    data: {
        catg: string
        completed: boolean
        create_at: string
        create_by: string
        dept: string
        failed_ques: number
        id: string
        success_ques: number
        correct_ques: number
        error_ques: number
        total_ques: number
        update_at: string
    }
}>()

const emits = defineEmits<{
    (e: 'close'): void
}>()

</script>

<style lang="less" scoped>
.proofreading-task-finished {
    display: flex;
    flex-direction: column;
    li {
        margin-bottom: 8px;
        .label {
            color: rgba(0,0,0,0.65);
            display: inline-block;
            width: 100px;
        }
        .value {
            width: 72px;
            display: inline-block;
            text-align: end;
            margin-left: 20px;
            color: rgba(0,0,0,0.85);
        }
    }
    .proofreading-detail {
        display: flex;
        justify-content: center;
        flex-direction: row;
    }
}
</style>