import { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import MotionResolver from 'motion-v/resolver'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
// import themePreprocπessorPlugin from '@zougt/vite-plugin-theme-preprocessor'
import { defineConfig } from 'vite'
// import compressPlugin from 'vite-plugin-compression'
import removeConsole from 'vite-plugin-remove-console'
import modifyVars from './src/config/theme'
import { svgBuilder } from './src/plugins/svgBuilder'

export default defineConfig({
  // define: {
  //   'process.env': {}
  // },
  plugins: [
    vue(),
    vueJsx(),
    removeConsole({
      includes: ['log', 'warn', 'error', 'info'],
    }),
    svgBuilder('./src/assets/icons/svg/'),
    // themePreprocessorPlugin({
    //   less: {
    //     multipleScopeVars: [
    //       { scopeName: 'theme-default', path: resolve('./src/styles/theme/default.less') },
    //       { scopeName: 'theme-dark', path: resolve('./src/styles/theme/dark.less') }
    //     ]
    //   }
    // })
    // requireTransform({
    //   fileRegex: /.js$|.vue$/
    // }),
    // compressPlugin({
    //   ext: '.gz', //gz br
    //   algorithm: 'gzip', //brotliCompress gzip
    //   deleteOriginFile: true
    // })

    AutoImport({
      imports: [
        'vue',
        '@vueuse/core',
        {
          // add any other imports you were relying on
          'vue-router/auto': ['useLink'],
        },
      ],
      dts: 'src/typings/auto-imports.d.ts',
      dirs: ['./src/api'],
      vueTemplate: true,
    }),
    Components({
      dts: 'src/typings/components.d.ts',
      resolvers: [
        AntDesignVueResolver({
          importStyle: false,
        }),
        MotionResolver(),
      ],
    }),
  ],
  base: './',
  build: {
    sourcemap: false,
    chunkSizeWarningLimit: 9000,
    // rollupOptions: {
    //   output: {
    //     manualChunks: undefined
    //   }
    // }
  },
  resolve: {
    alias: {
      '@': resolve('./src'),
      'api': resolve('./src/api'),
    },
  },
  server: {
    port: 8000,
    hmr: true,
    // open: true,
    cors: true,
    // https: true,
    host: '0.0.0.0',
    proxy: {
      '/api/q/': {
        target: 'http://***********:8080/jeecg-boot',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/api\/q\//, 'q/')
        },
      },
      '/api/position/': {
        target: 'http://***********:8080/jeecg-boot',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/api\/position\//, 'position/')
        },
      },
      '/api/resume/': {
        target: 'http://***********:8080/jeecg-boot',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/api\/resume\//, 'resume/')
        },
      },
      '/api/exam/': {
        target: 'http://***********:8080/jeecg-boot',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/api\/exam\//, 'exam/')
        },
      },
      // 笔试题库接口
      '/api/written/': {
        target: 'http://***********:8080/jeecg-boot',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/api\/written\//, 'written/')
        },
      },
      '/api': {
        // target: 'http://192.168.3.32:5000',
        // target: 'https://exam2.isrc.iscas.ac.cn/',
        // target: 'https://exam.test.isrc.ac.cn/',
        // target: 'https://exam.isrc.ac.cn/',
        // target: 'https://teacher-preview.exam.isrc.ac.cn',
        target: 'https://teacher.exam.isrc.ac.cn/api/v1/portal',
        // target: 'https://teacher-dev.exam.isrc.ac.cn',
        // target: 'https://teacher.exam.isrc.ac.cn/',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars,
        javascriptEnabled: true,
      },
    },
  },
})
