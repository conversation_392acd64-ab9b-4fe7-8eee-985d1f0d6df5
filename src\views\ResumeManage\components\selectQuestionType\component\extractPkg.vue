<script setup lang="ts">
import PkgList from '@/pages/questionManage/components/QuestionPackageList.vue'
import { getPackageList } from '@/pages/questionManage/hooks/api'

const props = defineProps<{
  selectPkg: Array<any>
}>()

const emits = defineEmits<{
  (e: 'removePkg', pkgId: string): void
}>()

init()

function init() {
  getPkgList()
}

const pkgList = ref<any[]>([])
const renderList = ref<any[]>([])
const selectType = ref(0)

watch(selectType, (val) => {
  if (val === 1) {
    renderList.value = pkgList.value.filter((item: any) => item.selected)
  }
  else {
    renderList.value = pkgList.value.filter((item: any) => item)
  }
})

// 监听 selectPkg 变化，更新选中状态
watch(() => props.selectPkg, (newSelectPkg) => {
  if (pkgList.value.length > 0) {
    const selectedPkgIds = new Set(newSelectPkg?.map((item: any) => item.id) || [])

    pkgList.value.forEach((item: any) => {
      item.selected = selectedPkgIds.has(item.id)
    })

    // 更新渲染列表
    if (selectType.value === 1) {
      renderList.value = pkgList.value.filter((item: any) => item.selected)
    }
    else {
      renderList.value = pkgList.value.filter((item: any) => item)
    }
  }
}, { deep: true, immediate: true })
const selection = ref([])

// 初始化选中状态
function initSelectedState(records: any[]) {
  const selectedPkgIds = new Set(props.selectPkg?.map((item: any) => item.id) || [])

  return records.map((item: any) => ({
    ...item,
    selected: selectedPkgIds.has(item.id),
  }))
}

async function getPkgList() {
  const res = await getPackageList({
    pageNo: 1,
    pageSize: 100,
  })
  const { records } = res

  // 根据 selectPkg 设置已选中状态
  pkgList.value = initSelectedState(records)
  renderList.value = pkgList.value.filter((item: any) => item)
}

const currentSelections = ref<any>([])
function select(item: any) {
  const wasSelected = item.selected
  item.selected = !item.selected
  // 如果从选中变为未选中，通知父组件删除对应的题库
  if (wasSelected && !item.selected) {
    currentSelections.value = currentSelections.value.filter((i: any) => i.id !== item.id)
    emits('removePkg', item.id)
  }
  else {
    currentSelections.value.push(item)
  }
}

function clear(e: any) {
  e.stopPropagation()
  selection.value = []
  pkgList.value.forEach((item: any) => {
    item.selected = false
  })
  currentSelections.value = []
}
const pkgName = ref('')
async function getSearchData() {
  const selectedIds = currentSelections.value.filter((item: any) => item.selected).map((item: any) => item.id)
  const res = await getPackageList({
    content: pkgName.value,
    pageNo: 1,
    pageSize: 100,
  })
  const { records } = res

  // 根据 selectPkg 设置已选中状态，而不是全部设为 false
  pkgList.value = initSelectedState(records)
  renderList.value = pkgList.value.filter((item: any) => item).map((item: any) => {
    if (selectedIds.includes(item.id)) {
      item.selected = true
    }
    return item
  })
}
function getSelectData() {
  // const selectedData = pkgList.value.filter((item: any) => item.selected)
  return currentSelections.value
}
defineExpose({
  getSelectData,
})
</script>

<template>
  <div class="overflow-y-auto px-[12px] mb-[30px] dh">
    <div class="w-full text-[16px] font-bold flex items-center" @click.stop>
      <a-input-search v-model:value.trim="pkgName" placeholder="搜索题库" allow-clear style="width: 240px;" @search="getSearchData" />
      <el-radio-group v-model="selectType" class="ml-[16px]">
        <el-radio :value="0">
          全部
        </el-radio>
        <el-radio :value="1">
          已选（{{ currentSelections.length }}）
        </el-radio>
      </el-radio-group>
      <el-button type="text" size="small" :disabled="!currentSelections.length" @click="clear">
        清空已选
      </el-button>
    </div>
    <PkgList :pkg-list="renderList" type="selection" @select="select" />
  </div>
</template>

<style scoped lang="scss">
.dh{
  @media screen and (max-width: 1440px) {
    height: 400px;
  }
  @media screen and (min-width: 1441px) {
    height: 800px;
  }
}
</style>