<template>
  <div class="exam-system">
    <bg></bg>
    <div class="content-wrap">
      <loginLeft></loginLeft>
      <!-- <div class="content-right">
        <svg-icon class="svgClass" style="width: 172px; height: 32px" name="wechat-login" />
        <div class="tdcode">
          <img :src="wxcode" alt="微信二维码" style="width: 100%" />
        </div>
        <span class="remind">登录视为您已同意第三方账号用户协议</span>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import bg from './components/bg.vue'
import loginLeft from './components/loginLeft.vue'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { loginwxcode, checklogin } from '@/api/login'
import store from '@/store'

const router = useRouter()

const wxcode = ref('')
const sceneid = ref('')
const startCheckLogin = ref(true)
const getWxcode = () => {
  loginwxcode().then((res: any) => {
    wxcode.value = res.qrcode
    sceneid.value = res.sceneid
    if (sceneid.value) {
      watchLoginStatus()
    }
  })
}

const watchLoginStatus = () => {
  checklogin({ sceneid: sceneid.value }).then((res: any) => {
    if (res) {
      startCheckLogin.value = false
      if (res.code === 1011) {
        router.push({ path: '/register/perinfo', query: { openid: res.openid } })
      } else if (res.access_token) {
        store.commit('SET_ACCESS_TOKEN', res.access_token)
        store.commit('SET_REFRESH_TOKEN', res.refresh_token)

        setTimeout(() => {
          router.push('/exam')
        }, 1000)
      }
    } else {
      startCheckLogin.value && setTimeout(watchLoginStatus, 1000)
    }
  })
}

onMounted(() => {
  // getWxcode()
})

onBeforeUnmount(() => {
  // startCheckLogin.value = false
})
</script>

<style lang="less" scoped>
.exam-system {
  position: relative;
  .content-wrap {
    width: 564px;
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-color: white;
    border-radius: 16px;
    display: flex;
    justify-content: center;
    .corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 92px;
      height: 92px;
      background: url('@/assets/images/triangle.png') center/100% 100% no-repeat;
      z-index: 999;
      cursor: pointer;
      div {
        position: absolute;
        top: 7.2px;
        right: 8.37px;
        width: 42px;
        height: 42px;
        font-size: 18px;
        font-family: PingFang SC, PingFang SC-6;
        font-weight: 500;
        color: #ffffff;
        line-height: 42px;
        text-align: center;
        transform: rotateZ(45deg);
      }
    }

    .content-right {
      position: absolute;
      right: 111px;
      top: 152px;
      text-align: center;
      .svgClass {
        margin-bottom: 16px;
      }
      .tdcode {
        margin: 0 auto 20px;
        width: 180px;
        height: 180px;
        // background: url('@/assets/images/bg-login.jpg') center/100% 100% no-repeat;
      }
      .remind {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
