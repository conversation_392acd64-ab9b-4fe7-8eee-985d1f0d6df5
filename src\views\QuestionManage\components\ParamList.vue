<template>
  <div class="param-list">
    <a-row class="">
      <a-col :span="6" class="head"><span>变量名</span></a-col>
      <a-col :span="6" class="head"><span>变量类型</span></a-col>
      <a-col :span="6" class="head"><span>条件</span></a-col>
      <a-col :span="6" class="head"><span>操作</span></a-col>
    </a-row>
    <template v-for="(item, index) in paramList" :key="index">
      <a-row>
        <a-col :span="6" class="content">
          <a-input v-model:value="item.name" autocomplete="off" />
        </a-col>
        <a-col :span="6" class="content">
          <a-select v-model:value="item.type">
            <template v-for="option in varType" :key="value">
              <a-select-option :value="option.value">{{ option.label }}</a-select-option>
            </template>
          </a-select>
        </a-col>
        <a-col :span="6" class="content">
          <a-checkbox v-model:checked="item.is_list" />
          <span>此变量是数组或列表</span>
        </a-col>
        <a-col :span="6" class="content">
          <img src="@/assets/icons/svg/delete.svg" class="del-icon" @click="delParam(index)" />
        </a-col>
      </a-row>
    </template>
    <div class="addparam-btn">
      <span @click="addParam"><svg-icon name="plus" />添加变量</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
const props = defineProps({
  parameters: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['addParam', 'delParam'])

const varType = [
  { label: 'string', value: 'string' },
  { label: 'int', value: 'int' }
]

const paramList = ref<any>([])
const addParam = () => {
  emits('addParam')
}
const delParam = (index: number) => {
  emits('delParam', index)
}

watch(
  () => props.parameters,
  (val) => {
    paramList.value = val
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.param-list {
  .head {
    line-height: 28px;
    background: #f1f4fe;
    box-shadow: -1px 0px 0px 0px #ffffff inset, 0px -1px 0px 0px #e8e8e8 inset;
    font-size: 12px;
    font-weight: 600;
    padding-left: 8px;
  }
  .content {
    display: flex;
    align-items: center;
    padding: 4px;
    line-height: 32px;
    font-size: 12px;
    border-bottom: 1px solid #e8e8e8;
    .ant-input {
      border-radius: 8px;
      font-size: 12px;
    }
    :deep(.ant-checkbox) {
      margin-right: 8px;
    }
    .del-icon {
      cursor: pointer;
    }
  }
  .addparam-btn {
    margin-top: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 82px;
    height: 24px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
  }
}
</style>
