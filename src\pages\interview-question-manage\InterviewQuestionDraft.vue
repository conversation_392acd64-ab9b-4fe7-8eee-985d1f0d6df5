<template>
  <div class="draft-container">
    <h3 class="draft-title">草稿箱</h3>
    <div class="draft-main">
      <div class="draft-header">
        <div class="header-left">
          <a-input-search v-model:value.trim="searchContent" placeholder="请输入题干" allow-clear @search="getSearchData" />
        </div>
        <div class="header-right">
          <span class="del-btn" @click="handleBatchRemove">批量删除</span>
        </div>
      </div>
      <a-table :columns="columns" :row-key="(record: any) => record.id" :data-source="list" :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onSelect: updateSelectedRowKeys,
        onSelectAll,
      }" :loading="loading" :scroll="{ x: 1200 }" :pagination="paginationConfig" @change="handleTableChange"
        @resizeColumn="(w: any, col: any) => col.width = w">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>{{
                  record.complicatedediting ? record.title.slice(0, 100) : record.title
                }}</template>
                <span class="questionContent">{{
                  record.complicatedediting ? record.title.slice(0, 100) : record.title
                }}</span>
              </a-tooltip>
            </span>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>
              {{ InterviewQuestionEnum[record.type] }}
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <span>
              <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                <template #title>
                  <span class="tooltip-text">编辑</span>
                </template>
                <a-button type="text" size="small" @click="editDraft(record)">
                  <svg-icon name="editable" style="margin-bottom: -2px" />
                </a-button>
              </a-tooltip>
              <a-divider type="vertical" />
              <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                <template #title>
                  <span class="tooltip-text">删除</span>
                </template>
                <a-popconfirm title="确定删除该题目草稿？" ok-text="确定" cancel-text="取消" @confirm="handleRemove(record)">
                  <a-button type="text" danger size="small">
                    <img src="@/assets/images/svg/icon_delete.svg" style="width: 14px; margin-bottom: 3px" />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </span>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <div class="expanded-row">
            <QuestionItemDisplay :question-detail="record.content" show-type show-correct-answer
              show-question-difficulty show-question-points-and-basis :option-letter-type="'text'" />
          </div>
        </template>
      </a-table>
    </div>
    <a-modal title="编辑草稿" v-model:visible="formVisible" wrapClassName="full-screen-modal" width="100%"
      :maskClosable="false" :closable="false" :keyboard="false" :footer="null" destroyOnClose>
      <InterviewQuestionForm :type-switch="false" :draftId="draftId" @cancel="onFormClose"></InterviewQuestionForm>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, createVNode } from 'vue'
import QuestionItemDisplay from '@/pages/interview-question-manage/InterviewQuestionItemDisplay/index.vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import InterviewQuestionForm from '@/pages/interview-question-manage/InterviewQuestionForm/index.vue'
import { InterviewQuestionEnum } from '@/types/interviewQuestion'
import { delaiinterviewdraftquestion, queryaiinterviewdraftquestion } from '@/api/interview'

const columns = ref([
  {
    title: '题型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    ellipsis: true,
    filters: [
      { text: '单选题', value: 0 },
      { text: '多选题', value: 1 },
      { text: '判断题', value: 2 },
      { text: '填空题', value: 5 },
      { text: '问答题', value: 3 },
      { text: '算法题', value: 4 },
      { text: '排序题', value: 6 }
    ],
    resizable: true
  },
  {
    title: '题干',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
    width: 850,
    resizable: true
  },
  {
    title: '题库',
    dataIndex: 'catg_name',
    key: 'catg_name',
    width: 160,
    ellipsis: true,
    resizable: true
  },
  {
    title: '上次保存时间',
    dataIndex: 'update_at',
    key: 'update_at',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
])

// 获取草稿箱列表
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

// 快速搜索
const searchContent = ref('')
const otherParams = ref<{
  order_field?: string
  order_type?: 'ascend' | 'descend',
  type?: number[]
}>({})
const getSearchData = () => {
  paginationConfig.current = 1
  getList()
}

// 已选列表
const selectedRowKeys = ref<string[]>([])
function updateSelectedRowKeys(record: any) {
  let key = record.id
  let index = selectedRowKeys.value.findIndex((i) => i === key)
  if (index === -1) {
    selectedRowKeys.value.push(key)
  } else {
    selectedRowKeys.value.splice(index, 1)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any[]) => changeRows.forEach(updateSelectedRowKeys)

// 表格过滤,排序,翻页操作等
const handleTableChange = (pagination: any, filters: any = {}, sort: any = {}) => {
  // 处理分页
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  // 处理排序
  otherParams.value.order_type = sort.order
  otherParams.value.order_field = sort.order ? sort.field : undefined
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getList()
}

/** 查询列表 */
const loading = ref(false)
const list = ref([])
async function getList() {
  loading.value = true
  try {
    let res = await queryaiinterviewdraftquestion({
      page: paginationConfig.current,
      per_page: paginationConfig.pageSize,
      title: searchContent.value,
      ...otherParams.value
    })
    list.value = res.data
    paginationConfig.total = res.total
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()


// 编辑草稿箱
const draftId = ref('')
const formVisible = ref(false)
function editDraft(record: any) {
  draftId.value = record.id
  formVisible.value = true
}
function onFormClose(needRefresh?: boolean) {
  needRefresh && getList()
  formVisible.value = false
}

/** 删除 */
async function handleRemove(record: any) {
  loading.value = true
  try {
    await delaiinterviewdraftquestion({
      ids: [record.id]
    })
    message.success('删除成功!')
    paginationConfig.current = 1
    getList()
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

/** 批量删除 */
function handleBatchRemove() {
  if (!selectedRowKeys.value.length) {
    return message.error('请勾选要删除的题目草稿')
  }
  Modal.confirm({
    title: () => `确定删除勾选的${selectedRowKeys.value.length}个题目草稿?`,
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    onOk() {
      delaiinterviewdraftquestion({ ids: selectedRowKeys.value }).then(() => {
        message.success('批量删除成功!')
        selectedRowKeys.value = []
        // 刷新
        paginationConfig.current = 1
        getList()
      })
    },
    onCancel() { }
  })
}

</script>

<style lang="less" scoped>
.draft-container {
  height: 100%;
  overflow: auto;
  padding: 0 20px 20px 20px;

  // box-sizing: border-box;
  .draft-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }

  .draft-main {
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    min-height: calc(100% - 48px);
    padding: 10px 24px;
    background-color: #fff;

    .draft-header {
      margin: 8px 0;
      display: flex;
      padding-bottom: 10px;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;
      }

      .header-right {
        display: flex;
        align-items: center;
        font-size: 14px;

        span {
          width: 88px;
          height: 32px;
          text-align: center;
          line-height: 32px;
          border-radius: 8px;
          font-size: 14px;
          margin-left: 8px;
          cursor: pointer;
        }

        .del-btn {
          border: 1px solid rgba(0, 0, 0, 0.15);
        }
      }
    }

    .expanded-row {
      padding: 16px;
    }
  }
}
</style>
<style lang="less">
.ant-table-expanded-row td:nth-child(1) {
  box-shadow: 2px 0px 0px 0px #5478ee inset;
}

.ant-table-expanded-row td:nth-child(2) {
  background: #fff;
}

.draft-container {
  .ant-table-thead>tr>th {
    background: #f1f4fe !important;
    padding-left: 10px;
    color: #121633;
    font-weight: bold;
    font-size: 15px;

    .ant-table-column-title {
      color: #121633;
      font-weight: bold;
      font-size: 15px;
    }
  }

  .ant-table-tbody>tr:not(.ant-table-expanded-row)>td {
    padding: 10px;
    font-size: 14px;

    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }

  .ant-table-row-expand-icon:focus,
  .ant-table-row-expand-icon:hover {
    color: #5478ee;
  }

  .ant-table-tbody>tr:hover:not(.ant-table-expanded-row)>td,
  .ant-table-tbody>tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
}

.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
</style>
