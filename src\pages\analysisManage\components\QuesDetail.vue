<!-- 题目详情 -->
<template>
  <a-drawer
    :width="608"
    title="题目详情"
    placement="right"
    :closable="false"
    :visible="props.visible"
    class="ques-detail-wrap"
    destroyOnClose
    @close="onClose">
    <template #extra>
      <close-outlined class="close-icon" style="color: rgba(0,0,0,0.85); fontSize: 14px;" @click="onClose" />
    </template>
    <QuestionItemDispaly
      :question-detail="props.quesInfo"
      show-score show-type show-tag
      show-correct-answer show-question-difficulty show-question-points-and-basis
      option-letter-type="text" />
    <a-divider />
    <p class="sub-title">题目其他信息</p>
    <ul class="other-info">
      <li>
        <span class="label">所属题库</span>
        <div class="value">{{ props.quesInfo.categoryName }}</div>
      </li>
      <li v-if="![QuestionEnum['单选题'], QuestionEnum['判断题']].includes(props.quesInfo.type)">
        <span class="label">题目配置</span>
        <div class="value">
          <p v-if="[QuestionEnum['多选题'], QuestionEnum['排序题']].includes(props.quesInfo.type)">
            <a-checkbox v-model:checked="props.quesInfo.sepscore" :disabled="true" />
            <span>本题适用于“部分回答正确时可得分”</span>
            <a-tooltip placement="right">
              <template #title>
                <span v-if="props.quesInfo.type === QuestionEnum['多选题']">当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span>
                <span v-if="props.quesInfo.type === QuestionEnum['排序题']">当考试设置为“部分回答正确时可得分”，并且本题勾选了此选项，则学生的答案中包含连续正确的顺序不少于2个时，可按照比例得分；否则，在没有完全回答正确的情况下不得分。</span>
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </p>
          <p v-if="[QuestionEnum['填空题']].includes(props.quesInfo.type)">
              <a-checkbox v-model:checked="props.quesInfo.ordered" :disabled="true" />
              <span>判分时区分答案先后顺序</span>
          </p>
          <p v-if="[QuestionEnum['填空题'], QuestionEnum['问答题']].includes(props.quesInfo.type)">
              <a-checkbox v-model:checked="props.quesInfo.ignorecase" :disabled="true" />
              <span>忽略大小写</span>
          </p>
          <p v-if="[QuestionEnum['算法题']].includes(props.quesInfo.type)">
              <a-checkbox v-model:checked="props.quesInfo.sepscore" :disabled="true" />
              <span>本题适用于“部分测试用例通过可得分”</span>
              <a-tooltip placement="right">
                  <template #title>
                      <span>若勾选“部分测试用例通过可得分”，则算法题得分按通过测试用例占总数的百分比计算；未勾选时，需全部通过测试用例才能得分。</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2"></svg-icon>
              </a-tooltip>
          </p>
          <p v-if="[QuestionEnum['算法题']].includes(props.quesInfo.type)">
            <a-checkbox :checked="hasDisablekws" :disabled="true" />
            <span>存在禁用关键词</span>
            <a-tooltip placement="right">
              <template #title>
                <span>禁止学生答题时使用一些系统内置的方法或关键字</span>
              </template>
              <svg-icon class="common-info-icon" name="info2" style="margin-left: 8px;"></svg-icon>
            </a-tooltip>
          </p>
        </div>
      </li>
      <li class="disableKws" v-if="hasDisablekws">
        <span class="label">禁用关键词</span>
        <div class="kw-list">
          <div class="kw-item" v-for="item in Object.keys(props.quesInfo.disablekws)">
            <div class="language">{{ item }}:</div>
            <div class="kw-wrap">
              <div class="kw" v-for="keyword in props.quesInfo.disablekws[item]">{{ keyword }}</div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </a-drawer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'

import { QuestionEnum } from '@/models/questionModel'
import QuestionItemDispaly from '@/pages/questionManage/QuestionItemDisplay/index.vue'

const props = defineProps<{
  visible: boolean,
  quesInfo: any
}>()
const emit = defineEmits(['close'])

const hasDisablekws = computed(() => {
  if (!props.quesInfo?.disablekws) return false
  try {
    return Object.values(props.quesInfo.disablekws).flat().length
  } catch (error) {
    return false
  }
})

const onClose = () => emit('close')

</script>

<style lang="less" scoped>
.ques-detail-wrap {
  .sub-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .other-info {
    font-size: 14px;
    &>li {
      margin-bottom: 16px;
      display: flex;
      .label {
        flex: none;
        width: 100px;
        color: rgba(0, 0, 0, 0.65);
      }
      .value {
        color: rgba(0, 0, 0, 0.85);
        .ant-checkbox-wrapper {
          margin-right: 8px;
        }
        p {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .disableKws {
    margin-top: 10px;
    .label {
      color: #626262;
    }
    .kw-list {
      padding-left: 16px;
      .kw-item {
        display: flex;
        .language {
          flex: none;
          width: 80px;
          height: 24px;
          margin-right: 8px;
          color: #626262;
          line-height: 24px;
        }
        .kw-wrap {
          line-height: 24px;
          display: flex;
          flex-wrap: wrap;
          color: #626262;
          .kw {
            margin-right: 4px;
            margin-bottom: 4px;
            padding: 0 4px;
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
    .value {
      padding-left: 16px;
    }
  }
}
</style>