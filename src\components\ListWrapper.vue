<template>
    <div class="list-wrapper" v-scroll="onScroll">
        <template v-if="list.length">
            <ul class="list" ref="listRef">
                <li v-for="item in list" v-element-visibility="(e: boolean) => onElementVisibility(item, e)">
                    <slot name="item" :item="item"></slot>
                </li>
            </ul>
            <div v-if="list.length < pagination.total && loading" class="load-more">
                加载中<a-spin :spinning="loading"></a-spin>
            </div>
            <!-- <p v-else style="text-align: center;">没有了</p> -->
        </template>
        <div class="basic-box" v-else>
            <a-spin v-if="loading"></a-spin>
            <div class="common-no-data" v-else></div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import { watch, ref, reactive, computed } from 'vue'
import { useThrottleFn, useIntervalFn } from '@vueuse/core'
import type { UseScrollReturn } from '@vueuse/core'
import { vScroll } from '@vueuse/components'
import { uuid } from '@/utils'
import { vElementVisibility } from '@vueuse/components'

interface ReturnType {
    data: any[]
    page: number
    total: number
}

const props = defineProps<{
    cardMinWidth?: number | string
    params: Record<string, any>
    getListFn: (...args: any[]) => Promise<any>
    updateFn?: (...args: any[]) => Promise<any>
}>()


// 设置卡片最小宽度
const computedCardMinWidth = computed(() => {
    const w = props.cardMinWidth
    if (typeof w === 'number') return w + 'px'
    if (typeof w === 'string') return w
    return '400px'
})

const pagination = reactive({
    page: 1,
    per_page: 20,
    total: 0,
})

const list = ref<any[]>([])
const loading = ref(false)

let requestId = ''
/** 
 * @param tempRequestId 如果是通过筛选的请求，需要传入id，来做防抖
 */
async function getList(tempRequestId?: string) {
    loading.value = true
    try {
        let { records:data, page, total } = await props.getListFn({
            ...props.params,
            page: pagination.page,
            per_page: pagination.per_page
        }) as ReturnType
        if (tempRequestId === requestId || !tempRequestId) { // 处理research防抖
            list.value.push(...data)
            pagination.total = total
        }
    } catch (error) {

    } finally {
        loading.value = false
    }
}
getList()

watch(() => props.params, research, { deep: true })

// 距离底部1000px触发加载
const listRef = ref()
const onScroll = useThrottleFn((state: UseScrollReturn) => {

    if (pagination.page * pagination.per_page >= pagination.total) return
    if (!listRef.value) return
    // console.log(`${listRef.value.offsetHeight} - ${state.y.value} = ${listRef.value.offsetHeight - state.y.value}`)

    if (listRef.value.offsetHeight - state.y.value < 1002) {
        pagination.page++
        getList()
    }
}, 100)


/** 重新查询 */
function research() {
    // 记录最新的请求id
    let tempRequestId = uuid()
    requestId = tempRequestId

    list.value = []
    pagination.page = 1
    getList(tempRequestId)
}

/** 批量删除 */
function handleBatchDelete(ids: string[]) {
    list.value = list.value.filter(item => !ids.includes(item.id))
}


// 更新视窗内的数据
const viewIds = ref<string[]>([])
function onElementVisibility(item: any, state: boolean) {
    if (state) {
        if (viewIds.value.includes(item.id)) return
        viewIds.value.push(item.id)
    } else {
        // 删除
        viewIds.value = viewIds.value.filter(id => id !== item.id)
    }
}

async function handleUpdate(ids: string[]) {
    if (!props.updateFn) return
    try {
        const res: any = await props.updateFn({ ids })
        res.forEach((item: any) => {
            const find = list.value.find(v => v.id === item.id)
            Object.assign(find, item)
        })
    } catch (error) {
        console.log(error)
    }
}

const { pause: pauseUpdate, resume: resumeUpdate } = useIntervalFn(() => {
    handleUpdate(viewIds.value)
}, 5000, {
    immediate: false
})

watch(() => props.updateFn, (val) => {
    !!val ? resumeUpdate() : pauseUpdate()
}, { immediate: true })

defineExpose({
    research,
    handleBatchDelete,
    handleUpdate
})

</script>

<style lang="less" scoped>
.list-wrapper {
    flex: 1;
    min-height: 0;
    overflow: scroll;

    .basic-box {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .list {
        padding: 0 14px 20px 20px; // 右侧有6px的滚动条
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(v-bind(computedCardMinWidth), 1fr));
        gap: 20px;

        >li {
            box-shadow: 0 1px 3px #0000001a;
            background-color: #ffffff;
            border-radius: 8px;
            transition: all .3s ease;

            &:hover {
                box-shadow: 0 6px 18px #0003;
            }

            >div {
                background: #ffffff;
                width: 100%;
                border-radius: 8px;
                box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.20);
                    top: -2px;
                }
            }

        }

    }

    .load-more {
        width: 100%;
        display: flex;
        justify-content: center;
    }
}
</style>