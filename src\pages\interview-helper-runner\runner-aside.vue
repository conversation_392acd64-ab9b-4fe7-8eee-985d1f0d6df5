<template>
    <div class="runner-aside" :style="{ width: boxWidth + 'px' }">
        <div class="tree-header">
            <h2>题库列表</h2>
            <div class="search-ipt-wrapper" :class="{ active: searchOpen }">
                <a-input ref="searchIptRef" class="search-ipt" v-model:value.trim="searchValue"
                    @blur="searchOpen = false" :bordered="false" placeholder="请输入关键词" :allowClear="searchOpen">
                </a-input>
                <SearchOutlined @click="handleSearch" />
            </div>
        </div>
        <div class="tree-body">
            <a-spin v-if="fetchLoading" class="loading-style"></a-spin>
            <div v-else-if="!fetchLoading && !treeData.length" class="common-no-data"></div>
            <a-tree v-else v-bind="$attrs" show-line show-icon :blockNode="true" :tree-data="treeData"
                :autoExpandParent="autoExpandParent" :field-names="{ title: 'name', key: 'id' }"
                v-model:expandedKeys="expandedKeys" @select="onSelect" @expand="onExpand">
                <template #switcherIcon="{ switcherCls }">
                    <Icon icon="DownOutlined" :class="switcherCls" />
                </template>
                <template #icon="item">
                    <svg-icon v-if="item.children && item.children.length" style="width: 16px;height: 16px;"
                        name="folder0"></svg-icon>
                    <svg-icon v-else style="width: 16px;height: 16px;" name="file0"></svg-icon>
                </template>
                <template #title="item">
                    <div class="custom-title">
                        <SearchHighLight class="title" :text="item.name" :search-text="searchValue" />
                    </div>
                </template>
            </a-tree>
        </div>
        <div id="resizer" @mousedown="startResize"
            style="width:4px;height:100%;position:absolute;right:0;top:0;cursor:e-resize;border-right:2px solid #5478EE;transition:all ease 0.2s;opacity: 0;">
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import SearchHighLight from '@/components/SearchHighLight.vue'
import { buildsubjcatgstree } from '@/api/interview'

const props = defineProps<{
    id: string
}>()

const emits = defineEmits<{
    (e: 'select', item: TreeNode): void
}>()

type TreeNode = {
    key?: string
    id: string
    name: string
    is_official_cert: boolean
    children: TreeNode[]
    pathName: string
    num: number
    type_ques_count: Record<number, number>
    isLeaf: boolean
    disableCheckbox?: boolean
}

const treeData = ref<TreeNode[]>([])
const expandedKeys = ref<any[]>([''])

const onSelect = (keys: string[], { node }: any) => {
    emits('select', node.dataRef)
}

const onExpand = (keys: string[]) => {
    expandedKeys.value = keys;
    autoExpandParent.value = false;
}

// 获取源数据的回调(在获取之前或者过程中可能需要执行一个依赖与源数据的方法,此方法会在获取完源数据后被调用)
let getSubjectDataCallBackFn: Function | null = null

const originTreeData = ref<TreeNode[]>([])
const fetchLoading = ref(false)
async function getSubjectData() {
    fetchLoading.value = true
    try {
        let res: any = await buildsubjcatgstree({ id: props.id })
        res = Array.isArray(res) ? res : []
        treeData.value = res
        originTreeData.value = res
        getSubjectDataCallBackFn?.()
        getSubjectDataCallBackFn = null
    } finally {
        fetchLoading.value = false
    }
}

// 科目搜索
const searchIptRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const autoExpandParent = ref(false)
function handleSearch() {
    searchOpen.value = true
    searchIptRef.value.focus()
}

function getParentId(id: string | number, tree: TreeNode[]): string | number | undefined {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
            if (node.children.some(item => item.id === id)) {
                parentKey = node.id;
            } else if (getParentId(id, node.children)) {
                parentKey = getParentId(id, node.children);
            }
        }
    }
    return parentKey;
}

watch(searchValue, (val) => {
    treeData.value = filterTree(originTreeData.value, val)
    autoExpandParent.value = true
    if (val) return
})

function filterTree(roots: TreeNode[], filterText: string): TreeNode[] {
    return roots.map((root) => filterSingleNode(root, filterText)).filter(Boolean) as TreeNode[];
}

function filterSingleNode(node: TreeNode, filterText: string): TreeNode | null {
    if (node.name.includes(filterText)) {
        // 如果当前节点符合条件，返回当前节点
        if (filterText) {
            let id = getParentId(node.id, originTreeData.value)
            expandedKeys.value.push(id)
        }
        return {
            ...node,
            children: filterTree(node.children, filterText),
        };
    } else {
        // 如果当前节点不符合条件，递归过滤子节点
        const filteredChildren = filterTree(node.children, filterText);
        if (filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren,
            };
        } else {
            return null; // 如果当前节点及其子节点都不符合条件，返回null
        }
    }
}


// 宽度拖拽
const lastBoxWidth = ref(260)
const BOX_MAX_WIDTH = 400
const boxWidth = ref(260)
const startX = ref(0);
const isMoving = ref(false)
const startResize = (e: MouseEvent) => {
    startX.value = e.clientX;
    console.log('startX:', startX.value)
    isMoving.value = true
    window.addEventListener('mousemove', resize);
    window.addEventListener('mouseup', stopResize);
};

const resize = (e: MouseEvent) => {
    if (!isMoving.value) return
    const newWidth = lastBoxWidth.value + e.clientX - startX.value;
    boxWidth.value = Math.min(newWidth, BOX_MAX_WIDTH);
};

const stopResize = () => {
    isMoving.value = false
    lastBoxWidth.value = boxWidth.value
    window.removeEventListener('mousemove', resize);
    window.removeEventListener('mouseup', stopResize);
};



onMounted(async () => {
    await getSubjectData()
    expandedKeys.value = [treeData.value[0].id]
})

onUnmounted(() => {
    stopResize()
})

</script>

<style lang="less" scoped>
:deep(.ant-tree) {
    background-color: transparent;

    .ant-tree-checkbox {
        margin-top: 16px !important;
    }

    .ant-tree-treenode {
        padding-left: 16px;
        padding-bottom: 0;
        transition: background-color ease .2s;


        &:not(.ant-tree-unselectable .ant-tree-treenode) {
            &:hover {
                background-color: #dde5ff;
                color: #5478ee;
            }

            &:active {
                background-color: #fff;
            }
        }

        &:is(.ant-tree-unselectable .ant-tree-treenode) .ant-tree-node-content-wrapper {
            &:hover {
                cursor: not-allowed !important;
            }
        }

        &.ant-tree-treenode-selected {
            .ant-tree-node-selected {
                background-color: transparent;
            }
        }

        .ant-tree-icon__customize {
            line-height: 30px;
        }
    }

    .ant-tree-node-content-wrapper {
        padding-left: 4px !important;

        &:hover {
            background-color: transparent;
        }
    }

    .ant-tree-switcher {
        background: transparent !important;
        line-height: 48px;
    }

}

.runner-aside {
    height: 100%;
    min-width: 260px;
    flex-shrink: 0 !important;
    display: flex;
    flex-direction: column;
    position: relative;

    user-select: none;
    border: 1px solid #e8e8e8;
    background: url(@/assets/images/aside-bg.webp) no-repeat center center;
    background-size: cover;

    .specific-info {
        width: 200px;
        padding: 12px 8px;
        position: absolute;
        left: calc(100% + 3px);
        border-radius: 4px;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
        font-size: 12px;
        background-color: #fff;
        transition: top ease .1s;

        .specific-info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            >div,
            span {
                flex: 1;
                height: 100%;
                display: flex;
                align-items: center;
                padding: 0 12px;

                &:first-child {
                    margin-right: 2px;
                }
            }

            >ul {
                line-height: 28px
            }
        }
    }

    #resizer:hover {
        opacity: 1 !important;
    }

    .tree-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 48px;
        padding: 0 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        position: relative;

        h2 {
            font-family: PingFang SC;
            font-size: 14px;
            line-height: inherit;
            color: #121633;
            font-weight: bold;
        }

        .plus-icon {
            cursor: pointer;
        }

        .addsubject {
            display: none;
        }

        .search-ipt-wrapper {
            position: absolute;
            right: 16px;
            left: 16px;
            padding: 4px;
            border-radius: 8px;
            display: flex;
            justify-content: flex-end;

            &.active {
                border: 1px solid #5478ee;
                background-color: #E3EAFE;

                .search-ipt {
                    padding: 0 2px;
                    flex: 1;
                }
            }

            .anticon {
                cursor: pointer;
                padding: 4px;
            }

            .anticon-search {
                z-index: 999;
                border-radius: 4px;
            }

            .search-ipt {
                width: 0;
                height: 22px;
                padding: 0;
                border: none;
                box-shadow: none;
                // flex: 1;

                :deep(.ant-input) {
                    font-size: 13px;
                }
            }
        }
    }
}

.loading-style {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.tree-body {
    overflow: auto;
    flex: 1;
    min-height: 0;
}

:deep(.ant-tree-node-content-wrapper) {
    flex: auto;
    min-width: 0;
}

:deep(.ant-tree-title) {
    flex: auto;
    min-width: 0;
}

.custom-title {
    max-width: 100%;
    height: 40px;
    padding-right: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
        min-width: 0;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>