import { QuestionEnum } from "@/models/questionModel"

export function valideSurvey(survey: SurveyType) {
    return new Promise((resolve, reject) => {

        if (survey.name?.trim() === '')
            return reject('问卷名称不能为空')

        if (survey.name.length > 50)
            return reject('问卷名称不能超过50字')

        if (survey.describeContent.length > 200)
            return reject('问卷描述不能超过200字')

        let questionList = survey.content.filter(i => i.type <= 100)
        if (!questionList?.length)
            return reject('问卷问题不能为空')

        for (let i = 0; i < questionList.length; i++) {
            let qItem = questionList[i]

            if (!qItem.body?.trim())
                return reject('题干不能为空')

            if (qItem.type === QuestionEnum['单选题'] || qItem.type === QuestionEnum['多选题']) {
                if (!qItem.options?.length)
                    return reject('至少需要1个选项')

                if (qItem.options?.some((option: any) => !option.content?.trim()))
                    return reject('选项内容都不能为空')
            }

            if (qItem.type === QuestionEnum['评分题'] && (!qItem.min_desc || !qItem.max_desc))
                return reject('评分描述不能为空')
        }

        resolve('校验通过')
    })
}