<script setup lang="ts">
import type { TechSkillsDataProps } from './shared'

const props = defineProps<{
  techSkillsData: TechSkillsDataProps
}>()

const renderTechSkillsData = computed(() => {
  return [
    {
      label: '必备',
      skills: props.techSkillsData.required.skills,
    },
    {
      label: '缺失',
      skills: props.techSkillsData.missing.skills,
    },
    {
      label: '加分',
      skills: props.techSkillsData.bonus.skills,
    },
    {
      label: '额外',
      skills: props.techSkillsData.extra.skills,
    },
  ]
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center mb-6">
      <h3 class="text-xl font-medium">
        技能总览
      </h3>
    </div>
    <div class="space-y-[18px]">
      <div v-for="(item, index) in renderTechSkillsData" :key="index" class="flex items-start gap-2">
        <div class="w-16 h-[20px] flex items-center text-sm">
          <span 
            class="w-2 h-2 rounded-full mr-1"
            :class="{
              'bg-[#72c240]': item.label === '必备',
              'bg-[#FF4D4F]': item.label === '缺失',
              'bg-[#5c77e6]': item.label === '加分',
              'bg-[#6036ab]': item.label === '额外',
            }"
          />
          {{ item.label }}
        </div>
        <div class="flex-1 flex flex-wrap gap-2">
          <span v-show="item.skills.length === 0" class="h-[20px] leading-5 text-xs text-gray-500 mr-2">
            暂无
          </span>
          
          <span
            v-for="skill in item.skills"
            v-show="item.skills.length > 0"
            :key="skill.skillName"
            class="px-2 h-[18px] rounded text-xs flex justify-center items-center"
            :class="{
              'bg-[#c5eba9] text-[#256A02]': item.label === '必备' && skill.proficiency === 2,
              'bg-[#d3e9c4] text-[#2F8C00]': item.label === '必备' && skill.proficiency === 1,
              // 'line-through bg-[#F5F5F5] text-[#626262]': item.label === '必备' && skill.proficiency === 2,

              'bg-[#FCF0F0] text-[#D71310] ': item.label === '缺失' && skill.proficiency === 2,
              'bg-[#FCF0F0] text-[#D71320] ': item.label === '缺失' && skill.proficiency === 1,

              'bg-[#CED9FF] text-[#1138B6]': item.label === '加分' && skill.proficiency === 2,
              'bg-[#F1F4FE] text-[#1138B6]': item.label === '加分' && skill.proficiency === 1,
              // 'line-through bg-[#F5F5F5] text-[#626262] border': item.label === '加分' && skill.proficiency === 2,

              'bg-[#dac8fb] text-purple-700 ': item.label === '额外' && skill.proficiency === 2,
              'bg-[#f3f0fa] text-purple-700 ': item.label === '额外' && skill.proficiency === 1,
              
            }"
          >
            {{ skill.skillName }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>