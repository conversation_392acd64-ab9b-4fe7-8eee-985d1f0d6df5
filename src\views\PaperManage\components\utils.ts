export function adjust_scores_proportional(questions: any[], total_score: number) {
    let n = questions.length;
    if (n === 0) return questions

    questions.forEach(question => {
        question.new_score = 1;
    });

    let total_integer_scores = questions.reduce((sum, question) => sum + question.new_score, 0);
    let remaining = total_score - total_integer_scores;
    while (remaining > 0) {
        let remainder_index = [...Array(n).keys()].sort((i, j) => {
            const a = ((questions[j].score - questions[j].new_score) / questions[j].score)
            const b = ((questions[i].score - questions[i].new_score) / questions[i].score)
            if (a === b) {
                return questions[j].score - questions[i].score
            }
            return a - b
        });
        questions[remainder_index[0]].new_score += 1;
        remaining -= 1;
    }

    return questions;
}