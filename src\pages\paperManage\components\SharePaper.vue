<template>
  <a-modal
    :visible="props.visible"
    :title="contentType === 'first' ? '共享试卷' : undefined"
    :footer="null"
    width="680px"
    destroyOnClose
    :maskClosable="false"
    @cancel="handleClose">
    <template #title >
      <div class="back" @click="goback">
        <img class="back-icon" src="@/assets/images/paper/back.svg" alt="" />
        <div class="label">返回</div>
      </div>
    </template>
    <div class="papername">
      <div class="label">考试名称：</div>
      <div class="name">{{ props.paperName }}</div>
    </div>
    <div class="content first" v-show="contentType === 'first'">
      <div class="search" @click="handleShowDepart">
        <div class="text">搜索教师/部门名称</div>
        <div class="split"></div>
        <img src="@/assets/images/paper/add.png" alt="" class="add-icon">
      </div>
      <div class="shared-list" v-if="sharedList.length">
        <div class="shared-item" v-for="item in sharedList" :key="item.id">
          <img
            v-if="item.type === PaperShareUnit.DEPT"
            class="icon"
            src="@/assets/images/paper/origin.svg"
            alt="">
          <img
            v-else
            class="icon"
            src="@/assets/images/paper/staff.svg"
            alt="">
          <div class="name">{{ item.name }}</div>
          <a-dropdown v-if="props.teacher !== item.id" class="operation" :trigger="['click']">
            <a class="ant-dropdown-link" @click.prevent>
              {{ PaperPowerTypeList.find(el => el.value === item.powerType)?.label || item.powerType }}
              <svg-icon class="down-icon" style="width: 16px;height: 16px;position: relative;top: 1px;" name="arrow-down"></svg-icon>
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item
                  v-for="operation in PaperPowerTypeList"
                  :key="operation.value"
                  style="fontSize: 12px"
                  @click="handleChangeShareType(item.id, item.type, operation.value, item)">
                  {{ operation.label }}
                </a-menu-item>
                <a-popconfirm
                  :title="`是否确定移除${item.name}`"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDeleteShare(item.id, item.type)">
                  <a-menu-item key="delete" style="color: #D71310; font-size: 12px">
                    移除
                  </a-menu-item>
                </a-popconfirm>
              </a-menu>
            </template>
          </a-dropdown>
          <div class="teacher" v-else>创建人</div>
        </div>
      </div>
      <div class="no-data-warp" v-else>
        <div class="common-no-data"></div>
      </div>
    </div>
    <div class="content second" v-show="contentType === 'second'">
      <div class="container">
        <div class="left">
          <a-input
            ref="searchInput"
            class="input-wrap"
            v-model:value.trim="keyword"
            placeholder="搜索教师/部门名称">
            <template #suffix>
              <SearchOutlined />
            </template>
          </a-input>
          <div class="breadcrumb">
            <div :class="['label', currentDept.id ? 'active' : '']" @click="currentDept = { id: '', name: '组织架构' }">组织架构</div>
            <template v-if="currentDept.id">
              <div class="split">/</div>
              <div class="label sub-label" :title="currentDept.name">{{ currentDept.name }}</div>
            </template>
          </div>
          <div class="origin-list-wrap">
            <div class="origin-list">
              <div class="origin-item" v-for="item in showDeptList" :key="item.id">
                <!--   :checked="checkedList.find(el => el.id === item.id)" -->
                <!-- 
                  :indeterminate="!checkedList.find(el => el.id === item.id) && item.children?.length && item.children.find(el => checkedList.find(cl => cl.id === el.id))"
                -->
                <a-checkbox
                  class="checkbox"
                  :checked="isFullyChecked(item)"
                  :indeterminate="isIndeterminate(item)"
                  :disabled="props.teacher === item.id"
                  @change="handleCheck(item)">
                </a-checkbox>
                <img
                  v-if="item.type === PaperShareUnit.DEPT"
                  class="icon"
                  src="@/assets/images/paper/origin.svg"
                  alt="">
                <img
                  v-else
                  class="icon"
                  src="@/assets/images/paper/staff.svg"
                  alt="">
                <div class="name" :title="item.name">{{ item.name }}</div>
                <div
                  class="promission"
                  v-if="item.powerType && props.teacher !== item.teacherUUID"
                  :style="item.powerType === PaperPowerType.READ ? { background: '#F3F7F1', color: '#2F8C00' } : { background: '#F4F0FB', color: '#6733B1' }">
                  {{ PaperPowerTypeList.find(pl => pl.value === item.powerType)?.label }}
                </div>
                <div
                  :class="['children', checkedList.find(el => el.id === item.id) ? 'disabled' : '']"
                  v-if="!keyword && item.children?.length"
                  @click="handleChangeCurrentDept(item)">
                  <div class="label">下级</div>
                  <img v-if="!checkedList.find(el => el.id === item.id)" class="icon" src="@/assets/images/paper/arrow-right.svg" alt="">
                  <img v-else class="icon" src="@/assets/images/paper/arrow-right-disabled.svg" alt="">
                </div>
                <div class="creater" v-if="props.teacher === item.id">创建人</div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="top">
            <div class="total">已选：<span style="color: #5478EE">{{ checkedList.length }}</span>个</div>
            <a-dropdown class="operation" :trigger="['click']">
              <a class="ant-dropdown-link" @click.prevent>
                {{ PaperPowerTypeList.find(el => el.value === operationType)?.label }}
                <svg-icon class="down-icon" style="width: 16px;height: 16px;position: relative;top: 1px;" name="arrow-down"></svg-icon>
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-for="operation in PaperPowerTypeList"
                    :key="operation.value"
                    @click="operationType = operation.value">
                    {{ operation.label }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="checked-list-wrap">
            <div class="checked-list" v-if="checkedList.length">
              <div class="checked-item" v-for="item in checkedList" :key="item.id">
                <img
                  v-if="item.type === PaperShareUnit.DEPT"
                  class="icon"
                  src="@/assets/images/paper/origin.svg"
                  alt="">
                <img
                  v-else
                  class="icon"
                  src="@/assets/images/paper/staff.svg"
                  alt="">
                <div class="name">{{ item.name }}</div>
                <img
                  class="delete"
                  src="@/assets/images/paper/close.svg"
                  alt=""
                  @click="handleCheck(item)">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="btn-list">
        <a-button class="btn" type="primary" :loading="submitLoading" :disabled="!checkedList.length" @click="handleOk">确认</a-button>
        <a-button class="btn" type="default" @click="handleClose">取消</a-button>
      </div>
    </div>
  </a-modal>
  
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import _ from 'lodash'

import { PaperPowerType, PaperPowerUnit, PaperShareUnit, PaperPowerTypeList } from '@/models/paperModel'
import { getDeptandResource, getPaperShare, modifyPaperShare, deletePaperShare, addPaperShare } from '@/api/admin/paperManage'

const props = defineProps<{
  isShared: boolean // 是否已共享
  visible: boolean
  paperId: string
  paperName: string
  teacher: string // 创建人id
  templeteUUID: string
}>()

const emits = defineEmits(['close', 'update:isShared'])

const contentType = ref<'first' | 'second'>('first') // 弹窗内容类型，一级弹窗展示已共享列表，二级弹窗展示添加共享人

const loading = ref(false)
// 已共享列表
const sharedList = ref<PaperPowerUnit[]>([])
  const getAllDescendants = (item: PaperPowerUnit): PaperPowerUnit[] => {
  let descendants: PaperPowerUnit[] = [];
  if (item.children?.length) {
    descendants = [...item.children];
    item.children.forEach(child => {
      descendants = [...descendants, ...getAllDescendants(child)];
    });
  }
  return descendants;
}

const isFullyChecked = (item: PaperPowerUnit): boolean => {
  const descendants = getAllDescendants(item);
  if (descendants.length === 0) {
    return !!checkedList.value.find(el => el.id === item.id);
  }
  
  const allDescendantsChecked = descendants.every(desc => 
    checkedList.value.find(el => el.id === desc.id)
  );
  return !!checkedList.value.find(el => el.id === item.id) || allDescendantsChecked;
}

const isIndeterminate = (item: PaperPowerUnit): boolean => {
  if (checkedList.value.find(el => el.id === item.id)) return false;
  
  const descendants = getAllDescendants(item);
  if (descendants.length === 0) return false;
  
  const someDescendantsChecked = descendants.some(desc => 
    checkedList.value.find(el => el.id === desc.id)
  );
  const allDescendantsChecked = descendants.every(desc => 
    checkedList.value.find(el => el.id === desc.id)
  );
  
  return someDescendantsChecked && !allDescendantsChecked;
}
const getSharedListFn = async () => {
  try {
    loading.value = true
    const res: any = await getPaperShare({ id: props.paperId })
    sharedList.value = Array.isArray(res) ? res.map(el => ({
      id: el.teacher || el.dept,
      name: el.teacher_name || el.dept_name,
      type: el.dept ? PaperShareUnit.DEPT : PaperShareUnit.TEACHER,
      powerType: el.permission.indexOf('write') !== -1 ? PaperPowerType.WRITE : PaperPowerType.READ,
    })) : []
    emits('update:isShared', sharedList.value.length > 1)
  } catch (error) {
    console.log('error', error)
    sharedList.value = []
  } finally {
    loading.value = true
  }
}
// 修改共享权限
const handleChangeShareType = async (id: string, unitType: PaperShareUnit, type: PaperPowerType, item) => {
  try {
    loading.value = true
    await modifyPaperShare({
      templeteId: props.paperId,
      templeteUUID: props.templeteUUID,
      dept: unitType === PaperShareUnit.DEPT ? [{
        deptId: id,
        permission: type === PaperPowerType.READ ? 'paper:read' : 'paper:read,paper:write',
      }] : null,
      teacher: unitType === PaperShareUnit.TEACHER ? [{
        teacherUUID: id,
        permission: type === PaperPowerType.READ  ? 'paper:read' : 'paper:read,paper:write',
      }] : null,
      // teacher: unitType === PaperShareUnit.TEACHER ? id : null,
      // dept: unitType === PaperShareUnit.DEPT ? id : null,
      // permission: type === PaperPowerType.READ ? 'paper:read' : 'paper:read,paper:write',
    })
    message.success('修改权限成功')
    getSharedListFn()
  } finally {
    loading.value = false
  }
}
// 移除共享
const handleDeleteShare = async (id: string, unitType: PaperShareUnit) => {
  try {
    loading.value = true
    await deletePaperShare({
      paper: props.paperId,
      dept: unitType === PaperShareUnit.DEPT ? [id] : [],
      teacher: unitType === PaperShareUnit.TEACHER  ? [id] : [],
    })
    message.success('移除成功')
    getSharedListFn()
  } finally {
    loading.value = false
  }
}

// 当前部门&共享资源组
const deptList = ref<PaperPowerUnit[]>([])
const getDepartListFn = async () => {
  try {
    loading.value = true
    const res: any = await getDeptandResource()
    deptList.value = Array.isArray(res) ? res.map(el => ({
      id: el.dept,
      name: el.dept_name,
      type: PaperShareUnit.DEPT,
      powerType: sharedList.value.find(sl => sl.id === el.dept)?.powerType,
      children: Array.isArray(el.teachers) ? el.teachers.map((tl: any) => ({
        id: tl.id,
        teacherUUID: tl.teacherUUID,
        name: tl.username,
        type: PaperShareUnit.TEACHER,
        powerType: sharedList.value.find(sl => sl.id === tl.teacherUUID)?.powerType,
        children: [],
      })) : []
    })) : []
  } catch (error) {
    deptList.value = []
  } finally {
    loading.value = false
  }
}
const searchInput = ref<HTMLElement | null>(null) // 搜索框
const handleShowDepart = () => { // 一级弹窗点击搜索框，展示二级搜索框，并聚焦二级弹窗搜索框，获取组织架构
  getDepartListFn()
  contentType.value = 'second'
  nextTick(() => {
    searchInput.value?.focus()
  })
}
// 搜索
const keyword = ref('')
// 当前选中部门
const defaultDept = {
  id: '',
  name: '组织架构'
}
const currentDept = ref(defaultDept)
const handleChangeCurrentDept = (dept: PaperPowerUnit) => { // 切换上级部门
  // 已选中部门不允许切换到下级
  if (checkedList.value.find(el => el.id === dept.id)) return
  currentDept.value = dept
}
// 深度遍历查找包含关键词的教师or部门，返回的数据包括找到的数据和上级部门
const filterKeywordDeep = (list: PaperPowerUnit[], value: string): PaperPowerUnit[] => {
  if (!Array.isArray(list)) return []
  const teacherList: PaperPowerUnit[] = []
  const deptList: PaperPowerUnit[] = []
  list.forEach((el: PaperPowerUnit) => {
    if (el.name.indexOf(value) !== -1) {
      if (el.type === PaperShareUnit.DEPT) deptList.push(el)
      else teacherList.push(el)
    }
    if (Array.isArray(el.children) && el.children.length) {
      el.children.forEach((cl: PaperPowerUnit) => {
        if (cl.name.indexOf(value) !== -1) {
          if (cl.type === PaperShareUnit.DEPT) deptList.push(cl)
          else teacherList.push(cl)
          if (!deptList.find(dl => dl.id === el.id)) deptList.push(el)
        }
      })
    }
  })
  return [...teacherList, ...deptList]
}
// 最终展示的组织架构
const showDeptList = computed(() => {
  // 当前展示的组织架构
  const currentDeptList = currentDept.value.id ? (deptList.value.find(el => el.id === currentDept.value.id)?.children || []) : deptList.value
  // 当前展示的组织架构搜索
  const showList = keyword.value ? filterKeywordDeep(currentDeptList, keyword.value) : currentDeptList
  console.log('this is showList',showList)
  return showList 
})

// 选中列表
const checkedList = ref<PaperPowerUnit[]>([])
// 选中or取消选中
const handleCheck = (item: PaperPowerUnit) => {
  const current = checkedList.value.find(el => el.id === item.id)
  if (!current) {
    checkedList.value = [...checkedList.value, { ...item }]
  } else {
    checkedList.value = checkedList.value.filter(el => el.id !== item.id)
  }
}
// 共享权限，默认可查看
const operationType = ref<PaperPowerType>(PaperPowerType.READ)

const submitLoading = ref(false)
const handleOk = async () => {
  try {
    submitLoading.value = true
    const deptList = checkedList.value.filter(el => el.type === PaperShareUnit.DEPT)
    const teacherList = checkedList.value.filter(el => el.type === PaperShareUnit.TEACHER)

    const params = {
      templeteId: props.paperId,
      templeteUUID: props.templeteUUID,
      dept: deptList.map(el => ({
        deptId: el.id,
        permission: operationType.value === PaperPowerType.WRITE ? 'paper:read,paper:write' : 'paper:read',
      })),
      teacher: teacherList.map(el => ({
        teacherUUID: el.teacherUUID,
        permission: operationType.value === PaperPowerType.WRITE ? 'paper:read,paper:write' : 'paper:read',
      })),

      dept_permission: deptList.map(el => ({
        dept: el.id,
        permission: operationType.value === PaperPowerType.WRITE ? 'paper:read,paper:write' : 'paper:read',
      })),
      teacher_permission: teacherList.map(el => ({
        teacherId: el.id,
        permission: operationType.value === PaperPowerType.WRITE ? 'paper:read,paper:write' : 'paper:read',
      })),
    }
    await addPaperShare(params)
    message.success('添加权限成功')
    emits('update:isShared', true)
    emits('close')
  } finally {
    submitLoading.value = false
  }
}
const handleClose = () => emits('close')

const goback = () => { // 返回一级弹窗
  contentType.value = 'first'
  keyword.value = ''
  currentDept.value = defaultDept
}
watch(() => props.visible, value => {
  if (value) {
    getSharedListFn()
  } else {
    contentType.value = 'first'
    keyword.value = ''
    currentDept.value = defaultDept
    checkedList.value = []
    operationType.value = PaperPowerType.READ
    submitLoading.value = false
  }
})
</script>

<style lang="less" scoped>
@import '../.././../styles/mixin.less';

.back {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  .back-icon {
    flex: none;
    width: 16px;
    height: 16px;
    object-fit: contain;
    margin-right: 8px;
  }
  .label {
    height: 22px;
    font-size: 20px;
    text-align: left;
    color: rgba(0,0,0,0.85);
    line-height: 22px;
  }
}
.papername {
  height: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .label, .name {
    font-size: 14px;
    color: rgba(0,0,0,0.85);
    line-height: 20px;
  }
  .label {
    flex: none;
    width: 80px;
  }
  .name {
    flex: auto;
    text-align: left;
    min-width: 50px;
    .ellipsis();
  }
}
.content {
  height: 400px;
  &.first {
    .search {
      height: 32px;
      background: #ffffff;
      border: 1px solid rgba(0,0,0,0.15);
      border-radius: 8px;
      padding: 0 8px 0 12px;
      display: flex;
      align-items: center;
      .text {
        flex: auto;
        font-size: 12px;
        text-align: left;
        color: #8a8e99;
        line-height: 32px;
      }
      .split {
        flex: none;
        width: 1px;
        height: 14px;
        margin-right: 8px;
        background: #d9d9d9;
      }
      .add-icon {
        flex: none;
        width: 16px;
        height: 16px;
        object-fit: contain;
      }
    }
    .shared-list {
      max-height: calc(100% - 44px);
      margin-top: 12px;
      margin: 12px -32px 0;
      padding: 0 32px;
      overflow-x: hidden;
      overflow-y: auto;
      .shared-item {
        height: 32px;
        box-shadow: 0px -1px 0px 0px #e8e8e8 inset;
        padding-left: 8px;
        display: flex;
        align-items: center;
        &:hover {
          background: #f1f4fe;
        }
        .icon {
          flex: none;
          width: 12px;
          height: 12px;
          object-fit: contain;
          margin-right: 8px;
        }
        .name {
          flex: auto;
          font-size: 12px;
          text-align: left;
          color: rgba(0,0,0,0.85);
          line-height: 32px;
        }
        .teacher {
          width: 68px;
          margin-right: 70px;
          padding-left: 5px;
          height: 32px;
          font-size: 12px;
          text-align: left;
          color: #5478ee;
          line-height: 32px;
        }
        .operation {
          margin-right: 70px;
          display: flex;
          align-items: center;
          padding-left: 5px;
        }
      }
    }
    .no-data-warp {
      width: 100%;
      height: 200px;
    }
  }
  &.second {
    display: flex;
    flex-direction: column;
    .container {
      flex: none;
      height: calc(100% - 64px);
      border-bottom: 1px solid #E8E8E8;
      display: flex;
      .left {
        flex: none;
        width: 424px;
        height: 100%;
        padding-right: 15px;
        .input-wrap {
          width: 100%;
          height: 32px;
          line-height: 32px;
          border-radius: 8px;
          &:focus {
            border: 1px solid #5478EE;
          }
          :deep(.ant-input) {
            font-size: 13px;
            box-shadow: none;
            border-radius: 8px;
            line-height: 26px;
          }
        }
        .breadcrumb {
          height: 32px;
          margin-top: 6px;
          display: flex;
          align-items: center;
          .label {
            font-size: 12px;
            font-weight: 600;
            color: rgba(0,0,0,0.85);
            line-height: 32px;
            &.active {
              font-weight: 400;
              color: #5478EE;
              cursor: pointer;
            }
          }
          .sub-label {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .split {
            width: 6px;
            margin: 0 4px;
            font-size: 12px;
            color: rgba(0,0,0,0.45);
            line-height: 32px;
          }
        }
        .origin-list-wrap {
          height: calc(100% - 76px);
          margin-top: 6px;
          margin-right: -15px;
          padding-right: 15px;
          overflow-x: hidden;
          overflow-y: auto;
          .origin-list {
            border-top: 1px solid #e8e8e8;
            .origin-item {
              height: 32px;
              border-bottom: 1px solid #e8e8e8;
              padding: 0 16px;
              display: flex;
              align-items: center;
              position: relative;
              .checkbox {
                flex: none;
                margin-right: 24px;
                &:deep(.ant-checkbox-indeterminate) {
                  .ant-checkbox-inner {
                    background: #5478EE;
                    &::after {
                      width: 10px;
                      height: 2px;
                      background: #fff;
                    }
                  }
                }
              }
              .icon {
                flex: none;
                width: 16px;
                height: 16px;
                object-fit: contain;
                margin-right: 8px;
              }
              .name {
                flex: auto;
                min-width: 0;
                font-size: 12px;
                text-align: left;
                color: rgba(0,0,0,0.85);
                line-height: 32px;
                .ellipsis();
              }
              .promission {
                flex: none;
                width: 44px;
                height: 18px;
                margin-left: 8px;
                border-radius: 4px;
                font-size: 12px;
                text-align: center;
                line-height: 18px;
              }
              .children {
                flex: none;
                width: 40px;
                height: 32px;
                margin-left: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                .label {
                  font-size: 12px;
                  text-align: left;
                  color: #5478ee;
                  text-align: center;
                  line-height: 32px;
                }
                .icon {
                  flex: none;
                  width: 12px;
                  height: 12px;
                  margin-left: 4px;
                  margin-right: 0;
                }
                &.disabled {
                  .label {
                    color: rgba(0,0,0,0.25);
                  }
                  cursor: not-allowed;
                }
              }
              .creater {
                width: 64px;
                height: 32px;
                position: absolute;
                top: 0;
                right: 0;
                font-size: 12px;
                text-align: center;
                color: rgba(0,0,0,0.25);
                line-height: 32px;
              }
            }
          }
        }
      }
      .right {
        flex: none;
        width: 193px;
        padding-left: 15px;
        border-left: 1px solid #e8e8e8;
        .top {
          height: 32px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .total {
            font-size: 12px;
            font-weight: Regular;
            color: rgba(0,0,0,0.85);
            line-height: 32px;
          }
          .operation {
            display: flex;
            align-items: center;
            padding-left: 5px;
            .down-icon {
              margin-left: 8px;
            }
          }
        }
        .checked-list-wrap {
          margin-top: 13px;
          border-top: 1px solid #e8e8e8;
          height: calc(100% - 45px);
          overflow-x: hidden;
          overflow-y: auto;
          .checked-item {
            height: 32px;
            padding: 0 8px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            .icon {
              flex: none;
              width: 12px;
              height: 12px;
              object-fit: contain;
              margin-right: 8px;
            }
            .name {
              flex: auto;
              min-width: 0;
              font-size: 12px;
              color: rgba(0,0,0,0.85);
              line-height: 32px;
              .ellipsis();
            }
            .delete {
              width: 12px;
              height: 12px;
              object-fit: contain;
              cursor: pointer;
            }
          }
        }
      }
    }
    .btn-list {
      flex: none;
      height: 32px;
      margin-top: 32px;
      display: flex;
      justify-content: center;
      .btn {
        margin-left: 8px;
        border-radius: 8px;
        &:first-of-type {
          margin-left: 0;
        }
      }
    }

  }
  .operation {
    flex: none;
    width: 68px;
    height: 100%;
    font-size: 12px;
    text-align: right;
    color: #5478ee;
    line-height: 32px;
    .down-icon {
      width: 16px;
      height: 16px;
      object-fit: contain;
      margin-left: 8px;
    }
  }
}
.operation-item {
  font-size: 12px;
}
</style>
