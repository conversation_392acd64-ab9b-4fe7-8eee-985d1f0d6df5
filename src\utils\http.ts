import axios from 'axios'
import config from '@/config/index'
import store from '@/store'
import { message } from 'ant-design-vue'
import { generateObjectHash } from './common'

const TOKEN_INVALID = 'Token认证失败，请重新登录'
const NETWORK_ERROR = '服务器开小差啦！'
const SYSTEM_ERROR = '系统错误'
const THROTTLE_ERROR = '节流处理中，稍后再试'
const PASSWORD_ERROR = '当前账号密码已修改，请重新登录'

// 创建axios实例对象，添加全局配置
const service = axios.create({
  baseURL: config.baseApi,
  timeout: 180000,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'X-Access-Token': "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************.4UHHTi760AcjY_rwPOeamTX59C5fk_VJ-kVDdn8kCmQ"
  }
})

// 请求拦截
const debounceHashCancel = new Map()
const throttleHashTime = new Map()
const disableTipAPI = ['/q/bank/edit','/q/commonPhrase/add']
service.interceptors.request.use(
  (req: any) => {
    if (store.state.accessToken) {
      // req.headers!.Authorization = 'Bearer ' + store.state.accessToken
      req.headers!.Authorization = store.state.accessToken
    }
    // 根据所有参数生成一个唯一hash
    let hash = ''
    if (req.debounceTime || req.throttleTime) {
      hash = generateObjectHash(req)
    }
    if (req.debounceTime) {
      // 需要防抖（用于查询）
      const cancel = debounceHashCancel.get(hash)
      cancel?.()
      return new Promise(resolve => {
        const timer = setTimeout(() => {
          clearTimeout(timer)
          resolve(req)
        }, req.debounceTime)
        debounceHashCancel.set(hash, () => {
          clearTimeout(timer)
          resolve(new Error('取消请求'))
        })
      })
    }
    if (req.throttleTime) {
      // 需要节流（用于表单提交）
      const newTime = new Date().getTime()
      const oldTime = throttleHashTime.get(hash)
      if (oldTime && (newTime - oldTime) < req.throttleTime) {
        return Promise.reject(new Error(THROTTLE_ERROR))
      } else {
        throttleHashTime.set(hash, newTime)
      }
    }
    return req
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截
let reloadTimer: any = null
const specialUrl = ['/q/bank/progress']
service.interceptors.response.use(
  (res) => {

    if (res.config.responseType === 'blob' && res.status === 200) {
      // 如果是文件流下载的接口，则直接返回
      return res
    }

    const { code, data, msg, display, result } = res.data
    if (code == "200") {
      return result ? result : specialUrl.includes((res.config.url) as any) ? result : data ? data : res.data 
    } else if ([1001, 1002, 1003, 4001, 4002, 4003, 1090].includes(code)) {
      if (!reloadTimer) {
        (code === 1090) && message.error(PASSWORD_ERROR)
        reloadTimer = setTimeout(() => {
          store.commit('CLEAR_TOKEN')
          // router.push('/login')
          location.reload()
        }, 1500)
      }
      return Promise.reject(TOKEN_INVALID)
    } else if (code === 5005) {
      message.error('图形验证码输入有误')
      return Promise.reject(msg)
    } else if (code === 9003 || code === 3001) {
      message.error('参数不合法')
      return Promise.reject(msg)
    } else {
      const tipAble = !disableTipAPI.includes(res.config.url)
      console.log("tipabLE", tipAble)
      tipAble && message.error(res.data.message ? res.data.message : display ? msg : SYSTEM_ERROR)
      return Promise.reject(res.data.message ? res.data.message : display ? msg : SYSTEM_ERROR)
    }
  },
  (err) => {
    const { config, response } = err
    // console.log(response, config, config.retryCount < 3)
    if (
      response &&
      (response.status === 500 || response.status === 502) &&
      config &&
      (config.retryCount || 0) < 3
    ) {
      // 如果请求失败且是服务器错误，且重试次数小于 3，则重试
      config.retryCount = config.retryCount || 0
      config.retryCount++
      return service(config)
    }

    if (err.message.indexOf('timeout') > -1) {
      message.error(NETWORK_ERROR)
      return Promise.reject(err)
    } else if (err.message === THROTTLE_ERROR) {
      return Promise.reject(err)
    } else {
      message.error(NETWORK_ERROR)
      return Promise.reject(err)
    }
  }
)

/**
 *请求核心函数
 */

function request(options: any): any {
  options.method = options.method || 'post'
  if (options.method.toLowerCase() === 'get') {
    options.params = options.data
  }
  if (config.env === 'prod') {
    service.defaults.baseURL = config.baseApi
  } else {
    service.defaults.baseURL = config.mock ? config.mockApi : config.baseApi
  }
  return service(options)
}
export default request
