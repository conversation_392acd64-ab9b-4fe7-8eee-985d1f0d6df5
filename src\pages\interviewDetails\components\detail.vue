<script setup>
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import InterviewReplay from './interviewReplay/index.vue'
import playBackVideo from './playbackVideo/index.vue'

const props = defineProps({
  examDetail: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const route = useRoute()
const collapseInfo = ref(false)
function switchInfo() {
  collapseInfo.value = !collapseInfo.value
}
const color = {
  1: '#06B190',
}
const dialogVisible = ref(false)
function openDialog() {
  dialogVisible.value = true
}
</script>

<template>
  <div class="bg-[#fff] rounded-[8px] shadow px-[24px] py-[19px] mb-[16px]">
    <div class="text-[20px] font-bold mb-[16px]">
      安排面试
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px] w-[80px]">面试名称</span>
      <span>{{ examDetail.exam.title }}</span>
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px] w-[80px]">面试岗位</span>
      <span>{{ route.query.position }}</span>
    </div>   
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px] w-[80px]">面试时间</span>
      <span>{{ examDetail.exam.startTime }} 至 {{ examDetail.exam.endTime }}</span>
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px] w-[80px]">面试题库</span>
      <div>
        <span class="p-[3px] rounded-[4px] bg-[#f1f4fe] text-[12px] text-[#5478ee] mr-[8px]">前端基础知识</span>
      </div>
    </div>
    <el-divider />
    <!-- AI面试 -->
    <div class="text-[20px] font-bold mb-[16px] flex items-center">
      发送邮件
      <!-- <el-icon class="ml-[10px] mt-[5px] cursor-pointer">
        <Refresh />
      </el-icon> -->
    </div>
    <div v-show="collapseInfo" class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">接收邮箱</span>
      <span>{{ examDetail.exam.receiverEmail }}</span>
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">发送时间</span>
      <span>{{ examDetail.exam.emailSentTime }}</span>
    </div>   
    <div v-show="!collapseInfo" class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">邮件内容</span>
      <el-button type="text" class="mt-[-7px]" @click="switchInfo">
        查看
      </el-button>
    </div>
    <div v-show="collapseInfo" class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">邮件内容</span>
    </div>   
    <div v-show="collapseInfo" class="bg-[#f5f5f5] rounded-[8px] p-[24px]">
      <p class="text-[24px] font-semibold mb-[24px] text-gray-800">
        {{ route.query.candidateName }}，你好：
      </p>

      <p class="text-gray-700 mb-[32px] text-[16px]">
        现邀请你参加AI面试。面试期间请保持摄像头和麦克风全程开启，建议提前准备好能正常使用的手机设备，并确保已经安装微信APP。
      </p>

      <p class="text-[24px] font-semibold mb-[24px] text-gray-800">
        面试信息
      </p>

      <div class="space-y-[16px] mb-[16px]" v-html="examDetail.exam.emailContent" />

      <h2 class="text-[24px] font-bold  text-gray-900 border-b pb-[16px]" :class="collapseInfo ? 'mb-[32px]' : 'mb-[10px]'">
        注意事项
      </h2>

      <div v-show="collapseInfo" class="space-y-[32px] text-gray-700 text-sm sm:text-base leading-relaxed">
        <div class="space-y-[3px]">
          <div>1.1 关于面试</div>
          <div>1.你的面试间二维码是专属的私人面试地址，请勿随意转发。</div>
          <div>2.你可以在指定时间内随时进入面试，根据系统提示完成所有题目。</div>
          <div>3.每道题目有固定答题时间，系统会自动计时，时间结束后自动停止录制，请合理分配答题时间。</div>
        </div>

        <div class="space-y-[3px]">
          <div>1.2 关于面试设备</div>
          <div>1.使用微信APP扫一扫功能，打开面试间二维码。</div>
          <div>2.确保您的手机摄像头、麦克风功能正常，手机电量充足。</div>
          <div>3.面试期间请勿切换APP，确保网络连接畅通，网速应在10Mb/S以上。</div>
          <div>4.如遇突发情况，如断网、手机死机、断电等，请直接刷新页面，或重启手机后重新通过面试间二维码进入，如果仍然无法解决，请联系相关负责人。</div>
        </div>

        <div class="space-y-[3px]">
          <div>1.3 关于作弊</div>
          <div>1.请严格遵守面试规定，确保面试间个人信息与自己匹配，不得找人代替。</div>
          <div>2.面试全程将会录像存档，请务必遵守面试纪律。</div>
          <div>3.请不要在面试过程中退出小程序或切换到其他应用，否则可能会被视为作弊嫌疑。避免使用任何与面试无关的软件、搜索引擎或聊天工具，以免影响成绩。</div>
          <div>4.我们将结合技术和人工抽查的方式，监控作弊行为。系统会自动比对提交的视频内容，判断是否存在大面积相似回答。对于高度相似的回答，系统将标记为疑似作弊，后续由面试负责人进行评估决定候选人是否进入下一轮甄选。</div>
          <div>5.一旦作弊行为被证实，候选人将被取消后续筛选资格，并且该不良记录会永久存档于本单位的诚信档案。</div>
        </div>
      </div>
      <el-button type="text" @click="switchInfo">
        {{ collapseInfo ? '收起' : '' }}
      </el-button>
    </div>
  </div>      

  <div v-show="examDetail.exam.joinTime" class="bg-[#fff] rounded-[8px] shadow px-[24px] py-[19px] mb-[16px]">
    <div class="text-[20px] font-bold mb-[16px]">
      AI面试
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">开始时间</span>
      <span>{{ examDetail.exam.joinTime }}</span>
    </div>
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">结束时间</span>
      <span>{{ examDetail.exam.finishTime }}</span>
    </div>   
    <div class="flex flex-nowrap mb-[16px]">
      <span class="text-[rgba(0,0,0,0.45)] mr-[38px]">面试时长</span>
      <span>{{ examDetail.exam.finishTime && `${dayjs(examDetail.exam.finishTime).diff(dayjs(examDetail.exam.joinTime), 'minute')} 分钟` }}</span>
    </div>
  </div>  

  <div v-if="examDetail.exam.replayUrl" class="bg-[#fff] rounded-[8px] shadow px-[24px] py-[19px] mb-[16px]">
    <div class="text-[20px] font-bold mb-[16px] w-full flex justify-between">
      面试结果
      <a-button type="primary" @click="openDialog">
        面试详情
      </a-button>
    </div>
    <div class="flex flex-nowrap justify-between">
      <playBackVideo :dialog-visible="dialogVisible" :url="examDetail.exam.replayUrl" style="width: 49%;" />
      <div class="w-[49%] bg-[#f7f7f7] rounded-[8px] p-[24px] bg-inherit">
        <div class="text-[18px] mb-[24px] flex items-center">
          <img src="@/assets/icons/svg/ai.svg" class="mr-[8px]">
          面试评价：
          <span class="text-[22px] inline-block" :style="{ color: examDetail.exam.score >= 81 ? '#06B190' : examDetail.exam.score >= 61 ? '#06B190' : examDetail.exam.score >= 41 ? 'ff4d4f' : '#d10000' }">{{ examDetail.exam.score >= 81 ? '优秀' : examDetail.exam.score >= 61 ? '良好' : examDetail.exam.score >= 41 ? '合格' : '不合格' }}</span>
        </div>
        <div class="text-[14px]">
          {{ examDetail.exam.evaluation }}
        </div>
      </div>
    </div>
  </div>

  <el-dialog v-model="dialogVisible" fullscreen class="replay-container">
    <InterviewReplay v-if="dialogVisible" :exam-detail="examDetail" />
  </el-dialog>
</template>

<style lang="scss" scoped>
.bg-inherit{
  background: linear-gradient(90deg,#e7edff 37%, #ebe7ff 87%);
}
.bt{
  border-bottom: 1px solid #e8e8e8;
}
.op-left {
  flex-shrink: 0;
  width: 200px;
  min-height: 812px;
  height: calc(100vh - 63px);
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.12); 
}
.content-bg {
  background: linear-gradient(230deg,#e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);
}
</style>

<style>
.replay-container.el-dialog.is-fullscreen{
  padding: 0;
  background-color: #F5F5F5;
  overflow: hidden;
}
.replay-container .el-dialog__headerbtn{
  margin-top: 8px;
}
.replay-container .el-dialog__header {
  padding-bottom: 0;
}
</style>