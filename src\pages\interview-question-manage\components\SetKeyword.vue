<!-- 设置禁用关键词 -->
<template>
  <a-modal
    :visible="visible"
    title="禁用关键词"
    wrapClassName="setkeyword-modal"
    width="700px"
    centered
    destroyOnClose
    :footer="null"
    @cancel="handleClose">
    <div class="container-wrap">
      <a-input-search
        class="input-wrap"
        v-model:value.trim="keyword"
        placeholder="请输入关键词"
        @change="handleKeywordChange" />
      <div class="container">
        <div class="left" ref="groupRef">
          <div
            class="language-wrap"
            v-for="item in languageFunctions"
            :key="item.language"
            :id="item.language">
            <div class="language">{{ item.language }}</div>
            <div class="function-group-list">
              <div
                :class="['function-group-item', activeLanguageGroup === `${item.language}#${group.key}` ? 'active' : '']"
                :id="`${item.language}#${group.key}`"
                v-for="group in item.groups"
                :key="group.key"
                @click="handleShowFunc(item.language, group.key)">
                {{ group.label }}
              </div>
            </div>
          </div>
        </div>
        <div class="right" ref="funcRef">
          <template v-if="showLanguageFunctions.length">
            <div class="language-wrap" v-for="item in showLanguageFunctions" :key="item.language">
              <div class="language">{{ item.language }}</div>
              <div
                class="group-wrap"
                :id="`${item.language}#${group.key}#Func`"
                v-intersection-observer="onIntersectionObserver"
                v-for="group in item.groups"
                :key="group.key">
                <a-checkbox
                  v-bind="getGroupCheckStatus(item.language, group.key)"
                  @change="(e: Event) => handleChangeGroup(e, item.language, group.key)">
                  <div class="label">{{ group.label }}</div>
                </a-checkbox>
                <div class="function-wrap">
                  <div
                    class="function"
                    v-for="func in group.functions"
                    :key="func.funcname">
                    <a-checkbox
                      :checked="(checkedFunctionsData[item.language] || []).includes(func.funcname)"
                      @change="handleChangeFunc(item.language, group.key, func.funcname)">
                      <div class="label">{{ func.funcname }}</div>
                    </a-checkbox>
                    <div class="description">{{ func.description }}</div>
                    <div class="usage">{{ func.usage }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <div class="common-no-data" v-else></div>
        </div>
      </div>
    </div>
    <div class="footer">
      <a-button type="primary" class="btn" @click="handleOk">确认</a-button>
      <a-button type="default" class="btn" @click="handleClose">取消</a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { useScroll, useResizeObserver } from '@vueuse/core'
import { vIntersectionObserver } from '@vueuse/components'
import _ from 'lodash'

import languageFunctions from '@/config/languageFunctions'
import { LanguageFunc } from '@/models/questionModel'

const props = defineProps<{
  visible: boolean
  checkedFunctions: LanguageFunc
}>()
const emits = defineEmits(['close', 'ok'])

const activeLanguageGroup = ref('C#sort')
const checkedFunctionsData = reactive<LanguageFunc>({
  C: [],
  'C++': [],
  Java: [],
  Python: [],
  JavaScript: [],
  Go: [],
})

const funcRef = ref<HTMLElement | null>(null)
const { y: funcY } = useScroll(funcRef)

const keyword = ref('')
const filterGroupAndFunction = () => { // 根据关键词筛选函数组和函数
  const resultKeyword = keyword.value.trim().toLocaleLowerCase() // 转化后的关键词
  const result: any = []
  languageFunctions.forEach(el => {
    if (el.language.toLocaleLowerCase().includes(resultKeyword)) { // 语言中包含关键词
      result.push(el)
    } else { // 语言中不包含关键词，在函数组中继续查找
      const groups: any[] = []
      el.groups.forEach(gl => {
        if (gl.label.toLocaleLowerCase().includes(resultKeyword)) { // 函数组名中包含关键词
          groups.push(gl)
        } else { // 函数组名中不包含关键词，在函数中继续查找
          const funcs: any[] = []
          gl.functions.forEach(fl => {
            if (fl.funcname.toLocaleLowerCase().includes(resultKeyword)) { // 函数名中包含关键词
              funcs.push(fl)
            } else if (fl.description.toLocaleLowerCase().includes(resultKeyword)) { // 函数描述中包含关键词
              funcs.push(fl)
            }
          })
          if (funcs.length) {
            groups.push({
              ...gl,
              functions: funcs,
            })
          }
        }
      })
      if (groups.length) {
        result.push({
          ...el,
          groups,
        })
      }
    }
  })
  return result
}
const showLanguageFunctions = ref<any[]>(languageFunctions) // 通过关键词筛选后展示在右侧的函数列表
const handleKeywordChange = _.debounce(() => { // 关键词改变
  funcY.value = 0
  if (!keyword.value) showLanguageFunctions.value = languageFunctions
  showLanguageFunctions.value = filterGroupAndFunction()
}, 500)

const groupRef = ref<HTMLElement | null>(null)
const { y: groupY } = useScroll(groupRef)
const groupHeight =ref(0) // 左侧函数组高度
useResizeObserver(groupRef, (entries: any) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  groupHeight.value = height
})
const autoScrollGroupIntoview = (language: string, groupKey: string) => { // 左侧函数组滚动到可视区域
  activeLanguageGroup.value = `${language}#${groupKey}`
  const languageEl = document.getElementById(`${language}`)
  const languageElOffsetTop = languageEl?.offsetTop || 0
  const languageElHeight = languageEl?.offsetHeight || 0
  if (!(languageElOffsetTop >= groupY.value && (languageElOffsetTop + languageElHeight) <= (groupY.value + groupHeight.value))) {
    languageEl?.scrollIntoView({ behavior: "smooth" })
  }
}
const handleChangeFunc = (language: string, groupKey: string, funcName: string) => { // 函数选中状态改变
  autoScrollGroupIntoview(language, groupKey)
  if (checkedFunctionsData[language].includes(funcName)) {
    checkedFunctionsData[language] = checkedFunctionsData[language].filter((el: string) => el !== funcName)
  } else {
    checkedFunctionsData[language] = [...checkedFunctionsData[language], funcName]
  }
}
const getGroupCheckStatus = (language: string, groupKey: string) => { // 判断函数集合是否全选、部分选中，未选
  // 当前语言当前组合下的所有函数
  const groupFunctions = showLanguageFunctions.value.find(el => el.language === language)?.groups.find((el: any) => el.key === groupKey)?.functions || []
  // 当前语言选中的所有函数
  const checkedLanguageFuncs: string[] = checkedFunctionsData[language] || []
  // 当前语言当前组合下选中的所有函数
  const groupChecked = checkedLanguageFuncs.filter((el: string) => groupFunctions.find((gl: any) => gl.funcname === el))
  const diffCount = groupFunctions.length - groupChecked.length
 return {
    checked: diffCount === 0,
    indeterminate: diffCount > 0 && diffCount < groupFunctions.length
  }
}
const handleChangeGroup = (e: Event, language: string, groupKey: string) => { // 函数组选中状态改变
  autoScrollGroupIntoview(language, groupKey)
  const groupFuncs = showLanguageFunctions.value.find(el => el.language === language)?.groups.find((el: any) => el.key === groupKey)?.functions || []
  const groupFuncNames = groupFuncs.map((el: any) => el.funcname)
  if (e.target?.checked) {
    checkedFunctionsData[language] = _.uniq(checkedFunctionsData[language].concat(groupFuncNames))
  } else {
    checkedFunctionsData[language] = checkedFunctionsData[language].filter((el: string) => !groupFuncNames.includes(el))
  }
}

const handleShowFunc = (language: string, groupKey: string) => { // 点击左侧函数组，右侧内容滚动到区域内
  activeLanguageGroup.value = `${language}#${groupKey}`
  const rightGroupFuncs = document.getElementById(`${language}#${groupKey}#Func`)
  rightGroupFuncs?.scrollIntoView({ behavior: "smooth" })
}

watch(() => props.visible, value => { // 重置数据
  if (value) {
    checkedFunctionsData.C = _.cloneDeep(props.checkedFunctions.C)
    checkedFunctionsData['C++'] = _.cloneDeep(props.checkedFunctions['C++'])
    checkedFunctionsData.Java = _.cloneDeep(props.checkedFunctions.Java)
    checkedFunctionsData.Python = _.cloneDeep(props.checkedFunctions.Python)
    checkedFunctionsData.JavaScript = _.cloneDeep(props.checkedFunctions.JavaScript)
    activeLanguageGroup.value = 'C#sort'
    keyword.value = ''
    showLanguageFunctions.value = languageFunctions
  }
})

const onIntersectionObserver = ([{ isIntersecting, target }]: any) => {
  const targetId = target.id
  if (isIntersecting) {
    const [language, groupKey] = targetId.split('#')
    const leftGroup = document.getElementById(`${language}#${groupKey}`)
    // console.log('leftGroup', leftGroup, leftGroup?.clientHeight, leftGroup?.offsetHeight)
    // leftGroup?.scrollIntoView({ behavior: "smooth" })
  }
}

const handleClose = () => emits('close')

const handleOk = () => emits('ok', _.cloneDeep(checkedFunctionsData))

</script>

<style lang="less" scoped>
.container-wrap {
  // height: calc(100vh - 171px); // 高度贴顶
  height: 444px;
  .input-wrap {
    --antd-wave-shadow-color: #fff !important;
    width: 100%;
    line-height: 32px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    :deep(.ant-input) {
      font-size: 13px;
      box-shadow: none;
      border: none;
      border-radius: 8px;
      line-height: 26px;
    }
    :deep(.ant-input-group-addon) {
      border-radius: 8px;
    }
    :deep(.ant-input-search-button) {
      border-radius: 8px !important;
      box-shadow: none !important;
      border: none !important;
    }
  }
}
.container {
  height: calc(100% - 44px);
  margin-top: 12px;
  display: flex;
  .left {
    flex: none;
    width: 112px;
    height: 100%;
    margin-right: 4px;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative; // 用于获取子元素的offsetTop，不可删除
    .language-wrap {
      margin-top: 12px;
      &:first-of-type {
        margin-top: 0;
      }
    }
    .function-group-item {
      height: 18px;
      margin-top: 8px;
      font-size: 12px;
      text-align: left;
      color: rgba(0,0,0,0.85);
      line-height: 18px;
      cursor: pointer;
      &.active {
        color: #5478EE;
      }
      &:hover {
        color: #8ca7ff;
      }
    }
  }
  .right {
    flex: auto;
    height: 100%;
    border-left: 1px solid #E8E8E8;
    padding-left: 16px;
    overflow-x: hidden;
    overflow-y: auto;
    .language-wrap {
      margin-top: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid #E8E8E8;
      &:first-of-type {
        margin-top: 0;
      }
      .group-wrap, .function-wrap {
        margin-top: 12px;
        .label {
          height: 18px;
          font-size: 12px;
          font-weight: 600;
          text-align: left;
          color: rgba(0,0,0,0.85);
          line-height: 18px;
        }
      }
      .function-wrap {
        padding-left: 24px;
        .function {
          margin-top: 12px;
          &:first-child {
            margin-top: 0;
          }
        }
        .description, .usage {
          font-size: 12px;
          text-align: left;
          line-height: 18px;
        }
        .description {
          color: rgba(0,0,0,0.85);
          margin-top: 8px;
        }
        .usage {
          color: rgba(0,0,0,0.65);
        }
      }
    }
  }
  .language {
    height: 18px;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
    color: rgba(0,0,0,0.85);
    line-height: 18px;
  }
}
.footer {
  height: 64px;
  padding-top: 32px;
  display: flex;
  justify-content: center;
  .btn {
    margin-left: 8px;
    border-radius: 8px;
    font-size: 14px;
    &:first-of-type {
      margin-left: 0;
    }
  }
}
</style>
