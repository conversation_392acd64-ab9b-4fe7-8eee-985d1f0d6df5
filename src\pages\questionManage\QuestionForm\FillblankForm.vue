<template>
    <div>
        <div class="fill-blank-item" v-for="(item, index) in proxy.answer" :key="item.id">
            <span class="label">填空{{ index + 1 }}答案</span>
            <JTagInput class="tag-ipt" v-model="item.keyword" placeholder="点击，编辑选项；点击回车设置多个关键词"></JTagInput>
            <div class="score-wrapper">
                <span class="score">分值</span>
                <a-input-number v-model:value="item.score" :min="1" :max="100" :precision="0" />
            </div>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { watch } from 'vue'
import { useVModel } from '@vueuse/core';
import JTagInput from '@/components/JTagInput.vue';
import _ from 'lodash';
import { findDeletedBlanks } from '@/utils';

const props = defineProps<{
    formState: any
}>();

const emits = defineEmits<{
    (e: 'update:formState', v: any): void
}>();

const proxy = useVModel(props, 'formState', emits, {
    passive: true,
    deep: true
});

watch(() => proxy.value.body, (val, oldVal) => {
    const res = val.match(/_{2,}/g)
    if (res) {
        if (proxy.value.answer.length > res.length) {
            // 说明删除了空格
            let deletedBlankIndexArray = []
            if (res.length === 0) {
                deletedBlankIndexArray.push(0)
            } else {
                deletedBlankIndexArray = findDeletedBlanks(oldVal, val)
            }
            deletedBlankIndexArray.forEach((item) => {
                proxy.value.answer.splice(item, 1)
            })
            console.log('deletedBlankIndexArray', deletedBlankIndexArray)
        } else if (proxy.value.answer.length < res.length) {
            // 说明增加了空格, 与删除空格的逻辑相反
            let addedBlankIndexArray = []
            if (res.length === 1) {
                addedBlankIndexArray.push(0)
            } else {
                addedBlankIndexArray = findDeletedBlanks(val, oldVal)
            }
            console.log('addedBlankIndexArray', addedBlankIndexArray)
            addedBlankIndexArray.forEach((item) => {
                proxy.value.answer.splice(item, 0, { score: 1, id: _.uniqueId() })
            })
        }
    } else {
        proxy.value.answer = []
    }
})

watch(() => proxy.value.answer, (val) => {
    proxy.value.score = val.reduce((prev: number, curr: any) => prev + curr.score, 0)
}, { deep: true })

</script>
  
<style lang="less" scoped>
.fill-blank-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .label {
        width: 100px;
    }

    .score-wrapper {
        width: 160px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .score {
            flex-grow: 0;
            flex-shrink: 0;
            margin-right: 8px;
        }
    }
}

:deep(.tag-ipt) {
    margin: 0 10px;
    border: none;
    font-size: 12px;
    align-items: center;

    .tagify__input {
        &::before {
            font-size: 12px;
        }
    }

    .tagify__tag-text {
        font-size: 12px;
    }
}
</style>
  