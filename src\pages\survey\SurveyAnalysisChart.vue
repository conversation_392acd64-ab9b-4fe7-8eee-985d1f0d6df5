<template>
    <div class="survey-analysis-chart">
        <div class="filter-bar">
            <div class="filter-item">
                <span class="filter-label">提交时间</span>
                <a-range-picker class="time-range-wrap" v-model:value="params.submit_time_range"
                    :placeholder="['开始时间', '结束时间']" valueFormat="YYYY-MM-DD" @change="getDetail">
                    <template #suffixIcon>
                        <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                    </template>
                </a-range-picker>
            </div>
            <div class="filter-item" v-if="list?.length">
                <span class="filter-label" style="margin-right: 8px;">全部饼图</span>
                <a-switch v-model:checked="allChartVisible"></a-switch>
            </div>
        </div>
        <div class="survey-analysis-content">
            <a-alert class="tip" type="info" show-icon>
                <template #icon>
                    <ExclamationCircleFilled />
                </template>
                <template #message>
                    提示：当前页面仅统计有效问卷的填写数据
                </template>
            </a-alert>
            <div class="q-list" v-if="list?.length">
                <div class="q-item" v-for="(item, index) in list">
                    <div class="q-type">{{ QuestionEnum[item.type] }}</div>
                    <div class="q-required" :style="{ visibility: item.required ? 'visible' : 'hidden' }">*</div>
                    <div class="q-main">
                        <div class="q-title">{{ index + 1 }}. {{ item.body }}</div>
                        <p style="margin-top: 8px;color: rgba(0,0,0,0.45);font-size: 12px;">有效填写量:
                            {{ item.efficient_count }}
                        </p>

                        <ul class="qa-options" v-if="item.type === QuestionEnum['问答题']">
                            <li class="qa-option-item qa-option-item--header">
                                <span>内容</span>
                                <span style="width: 143px;">受访者</span>
                            </li>
                            <div style="max-height: 400px;overflow: auto;" v-if="item.all_answer?.length">
                                <li class="qa-option-item" v-for="answer in item.all_answer">
                                    <span class="qa-option-item-content" :title="answer.content">{{ answer.content
                                        }}</span>
                                    <div class="option-avatars" style="width: 143px;">
                                        <img v-for="num in answer.serial_numbers.slice(0, 3)" :src="getAvatar(num)"
                                            alt="" :title="getNickName(num)"
                                            @click="showDrawer(answer.serial_numbers, num, item.id)">
                                        <span v-if="answer.serial_numbers?.length > 3" class="avatar-more"
                                            @click="showDrawer(answer.serial_numbers)">
                                            +{{ answer.serial_numbers?.length - 3 }}
                                        </span>
                                    </div>
                                </li>
                            </div>
                            <div v-else class="common-no-data" style="height: 160px;"></div>
                        </ul>
                        <ul class="q-options" v-else>
                            <li class="q-option-item-wrapper" v-for="option in item.options"
                                :title="`${option.content} (${item.answer_analysis[option.value] ?? 0})`">
                                <div class="q-option-item">
                                    <div class="q-option-item-bg">
                                        <span class="common-text-overflow" :style="{ maxWidth: '100%' }">{{
                                            option.content }}
                                        </span>
                                        <span>({{
                                            item.answer_analysis[option.value] ?? 0 }})</span>
                                    </div>
                                    <div class="q-option-item-progress" :style="{
                                        width: option.rate + '%'
                                    }">
                                        <span class="q-option-item-progress-cha"
                                            :style="{ width: (100 / option.rate) * 100 + '%' }">
                                            <span class="common-text-overflow" :style="{ maxWidth: '100%' }">{{
                                                option.content }}
                                            </span>
                                            <span>({{ item.answer_analysis[option.value] ?? 0 }})</span>
                                        </span>
                                    </div>
                                </div>
                                <div class="option-rate">{{ option.rate.toFixed(2) }}%</div>
                                <div class="option-avatars">
                                    <img v-for="num in option.serial_numbers.slice(0, 3)" :src="getAvatar(num)" alt=""
                                        :title="getNickName(num)"
                                        @click="showDrawer(option.serial_numbers, num, item.id)">
                                    <span v-if="option.serial_numbers?.length > 3" class="avatar-more"
                                        @click="showDrawer(option.serial_numbers)">
                                        +{{ option.serial_numbers?.length - 3 }}
                                    </span>
                                </div>
                            </li>
                        </ul>
                        <div class="bottom-bar" v-if="item.type !== QuestionEnum['问答题']">
                            <div>
                                <span style="margin-right: 8px;font-size: 12px;">显示饼图</span>
                                <a-switch v-model:checked="item.chartVisible"></a-switch>
                            </div>
                        </div>
                        <div class="pie-content" v-if="item.chartVisible && item.type !== QuestionEnum['问答题']">
                            <PieChart :item="item"></PieChart>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="common-no-data" style="height: calc(100% - 32px)"></div>

            <a-back-top :target="getBackToTopContainer" :visibilityHeight="120"
                style="position: absolute;right: 40px; bottom: 80px;width: auto;height: auto;">
                <img src="@/assets/images/back_to_top.png" alt="" />
            </a-back-top>
        </div>
        <a-drawer v-model:visible="drawerVisible" class="survey-answers-drawer" title="问卷详情" placement="right"
            :destroyOnClose="true" width="550px">
            <SurveyAnswersPanel ref="surveyAnswersPanelRef" :data="surveyAnswers"></SurveyAnswersPanel>
        </a-drawer>
    </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import SurveyAnswersPanel from './SurveyAnswersPanel.vue'
import { questionnairedatastatistics } from '@/api/admin/survey';
import { QuestionEnum } from '@/models/questionModel';
import PieChart from './components/PieChart.vue';
import defaultAvatar from '@/assets/icons/svg/avatar_default.svg'
import _ from 'lodash';

const route = useRoute()

const id = route.query.id as string

const params = reactive({
    submit_time_range: [],
})
let list = ref<any[]>([])
const allSurveyAnswers = ref<any[]>([])
let requestId = ''
async function getDetail() {
    let _requestId = _.uniqueId()
    requestId = _requestId
    try {
        params.startTimeStr = params.submit_time_range[0]
        params.endTimeStr = params.submit_time_range[1]
        let { analyze_data, detail_data } = await questionnairedatastatistics({ id, ...params })
        if (requestId !== _requestId) return // 做处理防抖

        // 选项值类型转化
        detail_data.forEach(item => item.answerDetail.forEach(a => a.options?.forEach(o => o.value = String(o.value))))

        list.value = Object.entries(analyze_data).map(([k, v]: any) => {
            if (v.other) {
                v.options.push({ value: '-1', content: '其他' })
            }
            Reflect.set(v, 'id', k)
            // Reflect.set(v, 'validFillCount', v.all_answer?.length ?? Object.values(v.answer_analysis).reduce((p: any, c: any) => (p + c), 0)) // 有效填写数量
            Reflect.set(v, 'chartVisible', false)

            if (v.type === QuestionEnum['评分题']) {
                // 如果是评分题，则添加options属性
                Reflect.set(v, 'options', new Array(5).fill(1).map((item, index) => ({ content: index + 1 + '分', value: index + 1 })))
            }

            v.options?.forEach(option => {
                // 选项的值转成字符串类型
                option.value = String(option.value)

                // 选择题、评分题需要添加rate属性
                let rate = 0
                if (v.efficient_count > 0) {
                    rate = ((v.answer_analysis[option.value] ?? 0) * 100 / v.efficient_count)
                }
                Reflect.set(option, 'rate', rate)

                // 获取所有答卷中，选了这个选项的卷子
                let arr = detail_data.filter(item => item.answerDetail.find(it => it.id === k)?.answer?.split(',').includes(String(option.value)))
                Reflect.set(option, 'serial_numbers', arr.map(i => i.serial_number))
            })

            // 问答题获取所有答卷中，是这个回答的卷子
            if (v.type === QuestionEnum['问答题']) {
                // 先去重
                v.all_answer = [...new Set(v.all_answer)].filter(i => i)
                // 统计每个答案的数量
                v.all_answer = v.all_answer.map(content => {
                    let arr = detail_data.filter(item => item.answerDetail.find(it => it.id === k)?.answer === content)
                    return {
                        content,
                        serial_numbers: arr.map(i => i.serial_number)
                    }
                })
            }


            return v
        })
        allSurveyAnswers.value = detail_data
    } catch (error) {
        console.log(error)
    }
}
getDetail()

const drawerVisible = ref(false);

const allChartVisible = computed({
    set(val) {
        list.value.forEach(i => i.chartVisible = val)
    },
    get() {
        return list.value.every(i => i.chartVisible)
    }
})

const surveyAnswersPanelRef = ref<InstanceType<typeof SurveyAnswersPanel>>()
const surveyAnswers = ref<any[]>([])
async function showDrawer(ids: string[], answerId?: string, questionId?: string) {
    if (!ids?.length) return
    surveyAnswers.value = allSurveyAnswers.value.filter(item => ids.includes(item.serial_number))

    drawerVisible.value = true;

    if (answerId) {
        // 打开面板后定位
        await nextTick()
        surveyAnswersPanelRef.value?.positionTo(answerId, questionId)
    }
};

function getBackToTopContainer() {
    return document.querySelector('.survey-analysis-content')
}

// 根据serial_number获取头像
function getAvatar(serial_number: string) {
    return allSurveyAnswers.value.find(item => item.serial_number === serial_number)?.headimgurl || defaultAvatar
}

function getNickName(serial_number: string) {
    return allSurveyAnswers.value.find(item => item.serial_number === serial_number)?.nickname || '匿名用户'
}

</script>
<style lang="less" scoped>
.survey-analysis-chart {
    display: flex;
    flex-direction: column;
    min-width: 1200px;

    .filter-bar {
        margin: 20px 20px 0;
        height: 80px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
        padding: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter-item {
            display: flex;
            align-items: center;

            .filter-label {
                margin-right: 24px;
                color: #626262;
            }
        }
    }

    .survey-analysis-content {
        flex: 1;
        min-height: 0;
        overflow: scroll;
        padding: 32px 100px 48px 32px;
        margin: 20px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);

        :deep(.tip) {
            border-radius: 4px;
            background: #f1f4fe;
            border: none;

            .ant-alert-message {
                font-size: 12px !important;
            }
        }

        .q-list {
            margin-top: 10px;

            .q-item {
                display: flex;
                margin-bottom: 48px;

                &:last-child {
                    margin-bottom: 0;
                }

                .q-type {
                    flex-shrink: 0;
                    width: 52px;
                    height: 22px;
                    background: #f1f4fe;
                    border: 1px solid #5478ee;
                    border-radius: 4px;
                    color: #5478ee;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                }

                .q-required {
                    flex-shrink: 0;
                    width: 16px;
                    height: 16px;
                    color: #F66F6A;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .q-main {
                    flex: 1;
                    min-width: 0;

                    .q-title {
                        font-size: 16px;
                        color: rgba(0, 0, 0, 0.85);
                    }

                    .q-options {
                        margin-top: 8px;
                        display: flex;
                        flex-direction: column;
                        gap: 16px;

                        .q-option-item-wrapper {
                            display: flex;
                            align-items: center;

                            .q-option-item {
                                flex: 1;
                                min-width: 0;
                                width: 100%;
                                height: 40px;
                                line-height: 40px;
                                padding-right: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                background: #daf1ff;
                                border-radius: 8px;
                                color: #5478EE;
                                position: relative;

                                .q-option-item-bg {
                                    display: flex;
                                    width: 100%;
                                    font-size: 16px;
                                    text-indent: 12px;
                                }

                                .q-option-item-progress {
                                    background-color: #5478EE;
                                    height: 100%;
                                    line-height: 40px;
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    border-radius: 8px;
                                    color: #fff;
                                    font-size: 16px;
                                    overflow: hidden;
                                    text-wrap: nowrap;
                                    transition: all ease .2s;

                                    .q-option-item-progress-cha {
                                        display: flex;
                                        text-indent: 12px;
                                        padding-right: 12px;
                                    }
                                }

                            }

                            .option-rate {
                                width: 60px;
                                color: rgba(0, 0, 0, 0.85);
                                margin-left: 16px;
                            }

                        }

                    }

                    .bottom-bar {
                        display: flex;
                        justify-content: space-between;
                        padding-top: 16px;
                        font-size: 12px;
                    }

                    .pie-content {
                        background-color: #f8f9fa;
                        height: 320px;
                        margin-top: 16px;
                    }

                    .qa-options {
                        border: 1px solid #d9d9d9;
                        border-radius: 8px;
                        margin-top: 8px;
                        overflow: hidden;

                        .qa-option-item {
                            height: 40px;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding-left: 20px;

                            &.qa-option-item--header {
                                color: rgba(0, 0, 0, 0.85);
                                font-size: 14px;
                                font-weight: bold;
                                background-color: #f2f5fc !important;
                                border-bottom: 1px solid #d9d9d9;
                            }

                            &:nth-child(odd) {
                                background-color: #f5f5f5;
                            }

                            .qa-option-item-content {
                                flex: 1;
                                min-width: 0;
                                text-wrap: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }

                            .option-avatars {
                                flex-shrink: 0;
                            }

                        }
                    }
                }
            }
        }
    }
}

.option-avatars {
    width: 140px;
    right: 0;
    z-index: 999;

    img {
        width: 32px;
        border-radius: 50%;
        cursor: pointer;
        // border: 4px solid transparent;
        mask-image: radial-gradient(ellipse 5px 4px at 56px center, #0000 30px, #000 0);

        &:nth-child(1) {
            transform: translateX(8px);
        }

        &:nth-child(2) {
            transform: translateX(4px);
        }

        &:nth-child(3) {
            mask-image: none;
        }

        &:last-child {
            mask-image: none;
        }
    }

    .avatar-more {
        width: 32px;
        height: 32px;
        background: #daf1ff;
        border-radius: 50%;
        display: inline-block;
        line-height: 32px;
        text-align: center;
        color: #5478ee;
        font-weight: bold;
        font-size: 12px;
        cursor: pointer;
        margin-left: 8px;
    }
}
</style>

<style lang="less">
.survey-answers-drawer {
    .ant-drawer-header {
        padding: 32px 32px 22px 32px;

        .ant-drawer-header-title {
            flex-direction: row-reverse;

            .ant-drawer-title {
                font-size: 20px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: bold;
            }
        }
    }

    .ant-drawer-body {
        padding: 0 32px 32px;
    }
}
</style>