<script setup lang="ts">
import type { CampusExperienceItemProps } from './shared'
import BaseExperienceCard from './BaseExperienceCard.vue'
// 模拟工作经历数据
withDefaults(defineProps<{
  campusExperienceData: CampusExperienceItemProps
}>(), {
  campusExperienceData: () => ([]),
})
</script>

<template>
  <BaseExperienceCard v-show="campusExperienceData.length > 0" title="校园经历">
    <div v-for="(item, index) in campusExperienceData" :key="index" class="mb-6 last:mb-0">
      <div class="flex items-start mb-2 ">
        <div class="flex-1">
          <div class="flex justify-between">
            <div>
              <span class="font-medium text-base">{{ item.school }}</span>
              <span class="text-gray-500 mx-2" />
              <span class="text-gray-600 text-xs">{{ item.position }}</span>
              <span class="text-gray-500 mx-2">|</span>
              <span class="text-gray-600 text-xs">{{ item.startDate }} 至 {{ item.endDate }}</span>
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600 whitespace-pre-line leading-[18px]">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </BaseExperienceCard>
</template>
