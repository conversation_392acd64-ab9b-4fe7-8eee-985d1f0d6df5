<script setup lang="ts">
import { uuid } from '@/utils'
import { CloseOutlined } from '@ant-design/icons-vue'
import { ElDialog, ElMessage } from 'element-plus'
import ExtractQuestion from '../component/extractQuestion/index.vue'
import { requestParamsPipe } from '../uitls'
import Analysis from './alalysis/index.vue'
import ExtractPkg from './extractPkg.vue'
import Initialize from './initialize.vue'
import Content from './selectContent/index.vue'

const emits = defineEmits(['setDisabledCreate'])
interface PKGITEM {
  type: string
  id: string
  name: string
  total: number
  extractNum: number | string | any
}
interface MANUALQUESITEM {
  type: string
  id: string
  isEdit: boolean
  content: string
  recordDuration: any
  correctAnswer: {
    reference: string
  }
}
const activeType = ref(0)
const modalVis = ref(false)
const modelTitle = ref('')
const types = [
  {
    type: 1,
    typeName: '新增抽题范围',
    typeDesc: '选择一个或多个题库作为抽题范围',
  },  
  {
    type: 2,
    typeName: '选择已有题目',
    typeDesc: '从题库中选择一个或多个题目',
  },  
  {
    type: 3,
    typeName: '手动输入题目',
    typeDesc: '在本试卷中录入一道临时题目',
  },  
]
const revertFunc = ref<any>(null)
function closeModal() {
  activeType.value = 0
  modalVis.value = false
}
const pkgRef = useTemplateRef<any>('pkg')
const qusRef = useTemplateRef<any>('qus')
const contentRef = useTemplateRef<any>('content')

const pkgSelection = ref<Array<PKGITEM>>([])
const contentList = ref<PKGITEM | MANUALQUESITEM | any>([])
watch(() => contentList.value, (val) => {
  emits('setDisabledCreate', !val.length)
}, { deep: true })
// 保存打开弹框时的 contentList 状态，用于取消时恢复
const contentListSnapshot = ref<any[]>([])

const isEmpty = computed(() => {
  return !contentList.value.length
})

function cancelModal() {
  // 如果是题库选择弹框，恢复之前的状态
  if (activeType.value === 1) {
    // 恢复 contentList 到打开弹框前的状态
    contentList.value = [...contentListSnapshot.value]
  }
  else if (activeType.value === 2) {
    const exec = revertFunc.value
    exec && exec()
  }

  closeModal()
}

const attachBaseIdRecord = ref<any>(null)
function addPkg() {
  /** 
   * 获取选中的题库数据
   * @type {Array<{id: string, name: string, total: number, extractNum: number}>}
   */
  pkgSelection.value = pkgRef.value.getSelectData()
  pkgSelection.value.forEach((item: any) => {
    item.type = 'pkg'
    item.extractNum = 5
  })
  if (attachBaseIdRecord.value) {
    const index = contentList.value.findIndex((item: any) => item.id === attachBaseIdRecord.value)
    contentList.value.splice(index, 0, ...pkgSelection.value)
    attachBaseIdRecord.value = null
  }
  else {
    const filterAddSelection = pkgSelection.value.filter((item: any) => !contentList.value.find((contentItem: any) => contentItem.id === item.id))
    contentList.value.push(...filterAddSelection)
  }
  closeModal()
}
function removeQuestion(id: any) {
  contentList.value = contentList.value.filter((item: any) => item.id !== id)
}
function addQuestion() {
  const selectQuestions = toRaw(qusRef.value.getSelectQuestions()) 
  // 排除已经存在的题目
  const existingQuestionIds = contentList.value
    .filter((item: any) => item.type === 'question')
    .map((item: any) => item.id)
  const filteredQuestions = selectQuestions.filter((question: any) => 
    !existingQuestionIds.includes(question.id),
  )
  if (!selectQuestions || !selectQuestions.length) {
    return ElMessage({
      message: '请选择题目',
      type: 'warning',
    })
  }
  selectQuestions.forEach((item: any) => {
    item.type = 'question'
    item.isEdit = false
  })
  
  if (attachBaseIdRecord.value) {
    const index = contentList.value.findIndex((item: any) => item.id === attachBaseIdRecord.value)
    contentList.value.splice(index, 0, ...filteredQuestions)
    attachBaseIdRecord.value = null
  }
  else {
    contentList.value.push(...filteredQuestions)
  }
  closeModal()
}
function addManualQuestion() {
  const manualQuestion = {
    type: 'manual',
    id: uuid(),
    configSchema: {
      content: '',
    },
    isEdit: true,
    recordDuration: 5,
    correctAnswer: {
      reference: '',
    },
  }
  
  if (attachBaseIdRecord.value) {
    const index = contentList.value.findIndex((item: any) => item.id === attachBaseIdRecord.value)
    contentList.value.splice(index, 0, manualQuestion)
    attachBaseIdRecord.value = null
  }
  else {
    contentList.value.push(manualQuestion)
  }
  closeModal()
}

function modalConfirm() {
  if (activeType.value === 1) {
    addPkg()
  }
  else if (activeType.value === 2) {
    addQuestion()
  }
}

function deleteItemDistribute(item: any) {
  const { id, type } = item
  if (type === 'pkg') {
    pkgSelection.value = pkgSelection.value.filter((item: any) => item.type !== 'pkg' || item.id !== id)
  }
  else if (type === 'question') {
    // todo
  }
}

// 处理题库取消选中事件
function handleRemovePkg(pkgId: string) {
  // 从 contentList 中删除对应的题库
  contentList.value = contentList.value.filter((item: any) =>
    !(item.type === 'pkg' && item.id === pkgId),
  )
}

function switchType(type: number, attachBaseId?: any) {
  if (attachBaseId) {
    attachBaseIdRecord.value = attachBaseId
  }
  activeType.value = type
  modelTitle.value = types.find(item => item.type === type)!.typeName

  // 保存当前 contentList 状态的快照
  contentListSnapshot.value = JSON.parse(JSON.stringify(contentList.value))

  if (type === 3) {
    addManualQuestion()
  }
  else {
    modalVis.value = true
  }
}
function getRequestParamsPipe() {
  return requestParamsPipe(toRaw(contentList.value))
} 
const selectPkg = computed(() => {
  return contentList.value.filter((item: any) => item.type === 'pkg')
})
const defaultRows = computed(() => {
  return contentList.value
    .filter((item: any) => item.type === 'question')
})

function focusItem(item: any) {
  contentRef.value.focusItem(item)
}
function validateContentRequired() {
  return contentRef.value.validateContentRequired()
}

watch(() => modalVis.value, async (val) => {
  if (val && !revertFunc.value) {
    revertFunc.value = (() => {
      const currentRawValue = JSON.parse(JSON.stringify(contentList.value))
      return () => {
        contentList.value = currentRawValue
      }
    })() 
  }
  if (!val) {
    revertFunc.value = null
    await nextTick()
  }
})

defineExpose({
  contentList,
  getRequestParamsPipe,
  validateContentRequired,
})
</script>

<template>
  <Initialize v-if="isEmpty" v-model="activeType" v-model:model-title="modelTitle" v-model:modal-vis="modalVis" @switch-type="switchType" />
  
  <Content v-if="!isEmpty" ref="content" v-model="contentList" @delete="deleteItemDistribute" @switch-type="switchType" />
    
  <Teleport defer to="#interviewQuestionAnalyse">
    <Analysis v-model="contentList" @focus-item="focusItem" />
  </Teleport>

  <ElDialog v-model="modalVis" width="80vw" :show-close="false" :z-index="11">
    <div class="text-[20px] font-bold text-[#000] mb-[32px] flex justify-between">
      {{ modelTitle }}
      <CloseOutlined style=" color:rgba(0,0,0,0.85);width: 15px;height: 15px;margin-right: 12px;cursor: pointer;" @click="closeModal" />
    </div>
    <ExtractPkg v-if="activeType === 1" ref="pkg" :select-pkg="selectPkg" @remove-pkg="handleRemovePkg" />
    <ExtractQuestion v-if="activeType === 2" ref="qus" :revert-func="revertFunc" :default-selected-rows="defaultRows" @remove-question="removeQuestion" />
    <div class="w-full flex items-center justify-center">
      <el-button type="primary" @click="modalConfirm">
        确定
      </el-button>
      <el-button @click="cancelModal">
        取消
      </el-button>
    </div>
  </ElDialog>
</template>

<style scoped lang="scss">
.dh {
      height: calc(100vh - 400px);
}
.normal_bg{
      background: linear-gradient(180deg,#f1f4ff, #f8faff);
      border: 1px solid transparent;
}
.active_bg{
      background: linear-gradient(180deg,#dde6ff, #f8faff);
      border: 1px solid #5478ee;
      border-radius: 8px;
      box-shadow: 0px 10px 40px 0px rgba(0,0,0,0.10); 
}
</style>