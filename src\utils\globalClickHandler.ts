document.addEventListener('click', function (event) {
    var clickedElement = event.target as Element;
    // 循环遍历点击元素的祖先元素
    while (clickedElement) {
        // 检查当前祖先元素是否具有类名'row'
        if (clickedElement.classList.contains('ant-table-row')) {
            // 获取点击元素的兄弟元素
            let siblings = clickedElement.parentElement?.children ?? []
            // 遍历兄弟元素
            for (let i = 0; i < siblings.length; i++) {
                let sibling = siblings[i];
                // 检查兄弟元素是否具有'active'类名
                if (sibling.classList.contains('active')) {
                    // 移除'active'类名
                    sibling.classList.remove('active');
                }
            }
            // 给点击行添加'active'类名
            clickedElement.classList.add('active')
            break; // 退出循环
        }
        // 否则，继续检查上一级祖先元素
        clickedElement = clickedElement.parentElement!;
    }
});