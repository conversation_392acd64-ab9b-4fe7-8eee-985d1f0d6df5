// 引入默认路由、动态路由
import { constRoutes, asyncRoutes , examMigrateRoutes} from '@/router/index'
import { RouteRecordRaw } from 'vue-router'
import store from './index'

// 筛选后端返回的权限数据，和权限路由中的meta里面的数据匹配，匹配成功返回true，失败为false
function hasPermission(roles: Array<string>, route: any) {
  if (route && route.meta.roles) {
    return roles.some((role) => route.meta.roles.indexOf(role) >= 0)
  } else {
    return true
  }
}

/**
 * // 递归遍历asyncRoutes路由表
 * @param roles 用户拥有的角色
 * @param routes 待过滤的路由表，首次传入的是asyncRoutes
 */
function filterAsyncRoutes(state: any, roles: string[], routes: any[]) {
  const res: any[] = []

  routes.forEach((route) => {
    const temp = { ...route }
    // if (hasPermission(roles, temp)) {
    //   if (temp.children) {
    //     temp.children = filterAsyncRoutes(state, roles, temp.children)
    //   }
    //   res.push(temp)
    // }
    res.push(temp)
  })
  return res
}

const permission = {
  state: () => ({
    routes: [],  // 初始化为空数组
    addRoutes: []
  }),
  mutations: {
    // 将匹配成功的权限路由拼接到公共路由中
    SET_ROUTES(
      state: { addRoutes: any; routes: RouteRecordRaw[] },
      routes: ConcatArray<RouteRecordRaw>
    ) {
      state.addRoutes = routes
      state.routes = constRoutes.concat(routes)
    }
  },
  actions: {
    // 对后台返回来的权限和动态路由权限匹配
    GENERATE_ROUTES({ commit }, roles: string[]) {
      return new Promise((resolve) => {
        const accessRoutes = filterAsyncRoutes(store, roles, asyncRoutes)
        commit('SET_ROUTES', accessRoutes)
        resolve(accessRoutes)
      })
    }
  },
  getters: {
    routes: (state: { routes: any }) => state.routes,
    addRoutes: (state: { addRoutes: any }) => state.addRoutes
  }
}

export default permission
