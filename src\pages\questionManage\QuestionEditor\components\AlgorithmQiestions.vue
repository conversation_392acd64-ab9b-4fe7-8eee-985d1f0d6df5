<script setup lang="ts">
import useAlgorithm from '@/views/QuestionManage/hooks/useAlgorithm'

const props = defineProps<{
  formState: any
  activeTab: any
  validateInfos: any
  insetBlank: () => void
  handleBodyBlur: () => void
  handleComplexContentBlur: () => void
  varType: any
  funcMainObj
  funcMainParams
  funcTemplateObj
  updateTemplate
  updateValidateCode
  showJsonEditor
}>()
const { formState } = props
const {
  handleAddParameter,
  handleDelParameter,
  handleAddInstance,
  handleDelInstance,
  addTestCase,
  delTestCase,
} = useAlgorithm(formState)
</script>

<template>
  <!-- eslint-disable -->
  <a-tabs v-if="formState.type === 4" v-model:active-key="activeTab" type="card">
    <a-tab-pane key="1" tab="普通录入" />
    <a-tab-pane key="2" tab="高级录入" />
  </a-tabs>
  <div v-show="activeTab === '1'">
    <a-form-item
      v-if="!formState.complicatedediting"
      label="题干内容"
      v-bind="validateInfos.body"
    >
      <a-textarea
        ref="bodyInputRef"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
        @blur="handleBodyBlur"
      />
      <div v-if="formState.type === 5" class="body-tip">
        <svg-icon name="tip" class="tip-icon" />
        <span>点击</span>
        <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
        <span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
    <a-form-item v-else label="题干内容" v-bind="validateInfos.complexcontent">
      <VueQuillEditor
        ref="bodyEditorRef"
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
        @blur="handleComplexContentBlur"
      />
      <div v-if="formState.type === 5" class="body-tip">
        <svg-icon name="tip" class="tip-icon" />
        <span>点击</span>
        <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
        <span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
  </div>
    
  <template v-if="formState.type === 4">
    <div v-show="activeTab === '1'">
      <a-form-item label="方法名称" name="func_name">
        <div class="func-name-wrapper">
          <a-input
            v-model:value.lazy.trim="formState.func_name"
            class="form-input"
            placeholder="方法名称"
            autocomplete="off"
          />
          <span class="func-extra-info">(以字母、下划线或美元符号开头，可包含字母、数字、下划线或美元符号，且大小写敏感，避免使用关键字，推荐采用驼峰命名法)</span>
        </div>
      </a-form-item>
      <a-form-item label="返回值类型" name="rtype">
        <div class="rtype-wrapper">
          <a-select
            v-model:value="formState.rtype.type"
            placeholder="返回值类型"
            class="form-input"
          >
            <template v-for="option in varType" :key="option.value">
              <a-select-option :value="option.value">
                {{ option.label }}
              </a-select-option>
            </template>
          </a-select>
          <!-- <a-input class="form-input" v-model:value="formState.rtype.type" /> -->
          <a-checkbox v-model:checked="formState.rtype.is_list" class="form-checkbox" />
          <span class="rtype-extra-info">&nbsp;此变量是数组或列表</span>
        </div>
      </a-form-item>
      <a-form-item label="参数列表" name="parameters">
        <ParamList
          :parameters="formState.parameters"
          @add-param="handleAddParameter"
          @del-param="handleDelParameter"
        />
      </a-form-item>
      <a-form-item label="题干测试用例" name="btestcase">
        <TestInstance
          :example-i-o="formState.btestcase"
          @add-instance="handleAddInstance"
          @del-instance="handleDelInstance"
        />
      </a-form-item>
      <a-form-item label="批卷测试用例" name="ptestcase">
        <TestInstance
          :example-i-o="formState.ptestcase"
          @add-instance="addTestCase"
          @del-instance="delTestCase"
        />
      </a-form-item>
      <a-form-item label="题目验证" name="validateCase">
        <ValidateAlgorithm
          ref="validateAlgorithmRef"
          :func-main="funcMainObj"
          :func-main-params="funcMainParams"
          :func-template="funcTemplateObj"
          :form-state="formState"
          @update-template="updateTemplate"
          @update-validate-code="updateValidateCode"
        />
      </a-form-item>
    
      <a-form-item label="难度" name="difficulty">
        <a-radio-group v-model:value="formState.difficulty">
          <a-radio :value="0">
            简单
          </a-radio>
          <a-radio :value="1">
            中等
          </a-radio>
          <a-radio :value="2">
            困难
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </div>
    <div v-if="activeTab === '2'">
      <JsonEditorVue
        v-if="showJsonEditor"
        ref="jsonEditorRef"
        v-model="formState"
        mode="tree"
        class="jse-theme-dark"
      />
    </div>
  </template>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>