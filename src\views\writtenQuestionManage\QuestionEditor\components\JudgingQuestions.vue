<script setup lang="ts">
import QuestionStem from './QuestionStem.vue'

const props = defineProps<{
  formState: any
  optionEditorsRef: any
  handleOptionBlur: () => void
  delOption: (event: Event, index: number) => void
  addOption: () => void
  showGenerateOptionModal: () => void
}>()
</script>

<template>
  <QuestionStem />
  <a-form-item v-if="formState.type === 2" class="question-options">
    <!--  eslint-disable -->
    <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
      <template v-for="(item, index) in formState.options" :key="index">
        <div class="item-option">
          <a-radio :value="item.value">
            <span class="option-radio">{{ item.value }}</span>
            <a-input v-model:value="item.content" class="option-content" readonly />
          </a-radio>
        </div>
      </template>
    </a-radio-group>
  </a-form-item>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>