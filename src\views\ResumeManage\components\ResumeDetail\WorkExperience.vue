<script setup lang="ts">
import type { ExperienceItemProps } from './shared'
import BaseExperienceCard from './BaseExperienceCard.vue'
// 模拟工作经历数据
withDefaults(defineProps<{
  workExperienceData: ExperienceItemProps
}>(), {
  workExperienceData: () => ([
    {
      company: '中科院计算机部门',
      department: '科研助理',
      position: '计算机部门',
      startDate: '2019.09',
      endDate: '2022.06',
      description: '这是某项目描述...',
    },
    // ... 其他数据
  ]),
})
</script>

<template>
  <BaseExperienceCard v-show="workExperienceData.length > 0" title="工作经历">
    <div v-for="(item, index) in workExperienceData" :key="index" class="mb-6 last:mb-0">
      <div class="flex items-start mb-2 ">
        <div class="flex-1">
          <div class="flex justify-between">
            <div>
              <span class="font-medium text-base">{{ item.company }}</span>
              <span class="text-gray-500 mx-2" />
              <span class="text-gray-600 text-xs">{{ item.department }}</span>
              <span class="text-gray-500 mx-2">|</span>
              <span class="text-gray-600 text-xs">{{ item.position }}</span>
            </div>
            <div class="text-gray-500">
              {{ item.startDate }} 至 {{ item.endDate }}
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600 whitespace-pre-line leading-[18px]">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </BaseExperienceCard>
</template>

<style scoped>
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  height: 0;
  opacity: 0;
}
</style>
