<template>
  <div style="display: flex; flex-direction: column;">
    <div class="paper-form">
      <a-collapse v-model:activeKey="expandKeys">
        <a-collapse-panel v-for="item in _list" :key="item.id">
          <template #header>
            <div class="header-wrapper" @click.stop>
              <div class="header-left">
                <div class="title">{{ item.title }}</div>
                <div style="display: flex;align-items: center;">
                  <a-checkbox
                    v-if="!item.options"
                    :disabled="!!item.options"
                    v-model:checked="item.isUnify"
                    @change="changeUnify(item)"
                    >统一分值</a-checkbox
                  >
                  <a-tooltip placement="right" overlayClassName="light">
                    <template #title>
                        <span>勾选后设置该组试题的统一分值</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2" style="margin-left: 0;"></svg-icon>
                  </a-tooltip>
                </div>
                <div style="color: rgba(0, 0, 0, 0.45); font-weight: bold">
                  <span>（共{{ item.children.length }}题，</span>
                  <span v-if="!!item.options"
                    >任选<strong style="color: #d71310">{{ item.options }}</strong
                    >道作答，</span
                  >
                  <span v-if="item.isUnify"
                    >每{{ item.type === QuestionEnum['填空题'] ? '空' : '题' }}
                    <a-input-number
                      class="number-ipt unify-ipt"
                      v-model:value="item.unifyScore"
                      :bordered="false"
                      :min="1"
                      :max="100"
                      :precision="0"
                      :controls="false"
                      style="text-align: right"
                      @keyup.enter="handleIptEnter"
                    />
                    分，</span
                  >
                  <span
                    >共<strong style="color: #d71310">{{
                      item.options
                        ? item.unifyScore! * item.options
                        : item.children.reduce((acc, cur) => acc + cur.new_score, 0)
                    }}</strong
                    >分）</span
                  >
                </div>
                <a-popconfirm v-if="isIndividualTiming == '1'" ok-text="确定" cancel-text="取消" @confirm="handleLimitTimeConfirm(item)" @visibleChange="handleLimitTimeVisibleChange" overlayClassName="popconfirm-individualtime">
                  <template #icon></template>
                  <template #title>
                    每题限时
                    <a-input-number
                      v-model:value="limitMinutes"
                      :min="0"
                      :precision="0"
                      :controls="false"
                      style="text-align: right; width: 60px;border-radius: 8px;"
                    />
                    分
                    <a-input-number
                      v-model:value="limitSeconds"
                      :min="0"
                      :precision="0"
                      :controls="false"
                      style="text-align: right; width: 60px;border-radius: 8px;"
                    />
                    秒
                  </template>
                  <span style="color: #5478EE;cursor: pointer;font-size: 12px;">统一限时</span>
                </a-popconfirm>  
              </div>
              <div class="header-right">
                更改题目顺序
                <a-tooltip placement="right" overlayClassName="light">
                  <template #title>
                      <span>打开后可以拖动改变该组试题中小题的顺序</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2" style="margin-left: 6px;"></svg-icon>
                </a-tooltip>
                <a-switch v-model:checked="item.sortable" @change="(value: boolean) => item.columns.find((i: any) => i.key === 'drag').width = value ? 50 : 0" />
                <span>
                  <a-button type="link" size="small" @click="editQuestion(item)">选择试题 </a-button>
                  <a-divider type="vertical" />
                  <a-popconfirm
                    title="确定删除该题目类型？"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="deleteQuestionType(item)"
                  >
                    <a-button type="link" size="small">删除</a-button>
                  </a-popconfirm>
                </span>
              </div>
            </div>
          </template>
          <VueDraggable
            v-model="item.children"
            :disabled="!item.sortable"
            :animation="150"
            target=".ant-table-tbody"
            ghostClass="ghost"
            @start="item.expandedRowKeys = []"
            @end="handleTableDragEnd(item)"
          >
            <a-table
              :key="item.id"
              :dataSource="item.children"
              :columns="item.columns"
              :pagination="false"
              :rowClassName="() => (item.sortable ? 'move' : '')"
              rowKey="id"
              @resizeColumn="(w: any, col: any) => col.width = w"
              :rowExpandable="() => !item.sortable"
              :showExpandColumn="!item.sortable"
              v-model:expandedRowKeys="item.expandedRowKeys"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'drag' && item.sortable">
                  <div v-for="i in 4" style="line-height: 4px">..</div>
                </template>
                <template v-if="column.key === 'new_score'">
                  <a-input-number
                    class="number-ipt"
                    :class="{ border: !item.isUnify && !item.sortable, move: item.sortable }"
                    :disabled="item.isUnify || item.sortable"
                    v-model:value="record.new_score"
                    :bordered="false"
                    :min="1"
                    :max="100"
                    :precision="0"
                    :controls="false"
                    style="text-align: right"
                    @keyup.enter="handleIptEnter"
                  />
                </template>
                <template v-if="column.key === 'duration'">
                  <a-input-number
                    class="number-ipt"
                    :class="{ border: !item.sortable, move: item.sortable }"
                    :disabled="item.sortable"
                    v-model:value="record.duration"
                    :bordered="false"
                    :min="1"
                    :max="3600"
                    :precision="0"
                    :controls="false"
                    style="text-align: right"
                    @keyup.enter="handleIptEnter"
                  />
                </template>
                <template v-if="column.key === 'body'">
                  <div style="display: flex;align-items: flex-start;">
                    <span class="tooltip-style">
                      <a-tooltip placement="topLeft">
                        <template #title>
                          {{
                            record.complicatedediting ? record.body.slice(0, 100) : record.body
                          }}</template
                        >
                        <span class="question-content" v-html="getQuestionContentByRecord(record)"></span>
                      </a-tooltip>
                    </span>
                    <span class="exclude-tag" v-if="record.proofreading === 0">未校对</span>
                    <span class="exclude-tag" v-if="record.proofreading === 2">校对错误</span>
                    <span class="exclude-tag" v-if="excludedIds.includes(record.id)">已排除</span>
                    <span class="exclude-tag" v-if="isRepeat(record)">疑似重复</span>
                  </div>
                </template>
                <template v-else-if="column.key === 'proofreading'">
                  <div>
                    <span :class="[['status_gray', 'status_green', 'status_red'][record.proofreading]]"></span>
                    <span>{{ ['未校对', '校对正确', '校对错误'][record.proofreading]  }}</span>
                  </div>
                </template>
                <template v-else-if="column.key === 'categoryName'">
                  <span class="tooltip-style">
                    <a-tooltip placement="topLeft" :title="record.categoryName">
                      <span>{{ record.categoryName }}</span>
                    </a-tooltip>
                  </span>
                </template>
                <template v-else-if="column.key === 'accuracy'">
                  <span>{{ formatAccuracy(record.accuracy) }}</span>
                </template>
                <template v-else-if="column.key === 'action'">
                  <span>
                    <a-popconfirm
                      title="确定删除该题目？"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="deleteQuestion(record, item)"
                    >
                      <a-button type="link" size="small">删除</a-button>
                    </a-popconfirm>
                  </span>
                </template>
              </template>
              <template #expandedRowRender="{ record }">
                <div class="expanded-row">
                  <QuestionItemDisplay
                    :question-detail="record"
                    show-score
                    score-key="new_score"
                    show-type
                    show-tag
                    show-correct-answer
                    show-question-difficulty
                    show-question-points-and-basis
                    :option-letter-type="'text'"
                  >
                  </QuestionItemDisplay>
                </div>
              </template>
            </a-table>
          </VueDraggable>
        </a-collapse-panel>
      </a-collapse>
    </div>
    <Teleport v-if="_list.length" to="#paper-form-teleport">
      <PaperQuestionSelectPreview ref="paperQuestionSelectPreviewRef" :name="name" :isIndividualTiming="isIndividualTiming" :totalScore="totalScore" v-model:list="_list" @sortend="onSortEnd" @position="onSortEnd"></PaperQuestionSelectPreview>
    </Teleport>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { QuestionEnum } from '@/models/questionModel'
import { formatAccuracy } from '@/utils'
import type { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'
import type { QuestionModel } from '@/models/questionModel'
import { uuid } from '@/utils'
import { adjust_scores_proportional } from '@/views/PaperManage/components/utils'
import { VueDraggable } from 'vue-draggable-plus'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
import PaperQuestionSelectPreview from './PaperQuestionSelectPreview.vue'
import { message } from 'ant-design-vue'
import _ from 'lodash'
import { QUESTIONS } from '@/config/constants'
import { getQuestionContentByRecord } from '@/utils'

const props = withDefaults(
  defineProps<{
    name: string
    isIndividualTiming?: '0' | '1'
    excludedIds?: string[]
    list: PaperFbodyItemModelWithQuestionChildren[]
  }>(),
  {
    excludedIds: () => []
  }
)
const emits = defineEmits<{
  (e: 'edit', value: PaperFbodyItemModelWithQuestionChildren): void
  (e: 'update:list', value: any[]): void
}>()

const expandKeys = ref<string[]>([])

const columns = ref([
  {
    key: 'drag',
    width: 0,
  },
  {
    title: '分值',
    dataIndex: 'new_score',
    key: 'new_score',
    width: 100,
    align: 'right',
    resizable: true
  },
  {
    title: '题干',
    dataIndex: 'body',
    key: 'body',
    width: 540,
    align: 'left',
    ellipsis: true,
    resizable: true
  },
  {
    title: '题库',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 120,
    ellipsis: true,
    resizable: true
  },
  {
    title: '正确率',
    dataIndex: 'accuracy',
    key: 'accuracy',
    width: 120,
    align: 'right',
    ellipsis: true,
    resizable: true
  },
  {
    title: '引用次数',
    dataIndex: 'referenceCount',
    key: 'referenceCount',
    align: 'right',
    width: 120,
    ellipsis: true,
    resizable: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
    fixed: 'right',
    ellipsis: true,
  }
])

const _list = ref<PaperFbodyItemModelWithQuestionChildren[]>([])
function init() {
  _list.value = _.cloneDeep(props.list) // 初始化_list
  _list.value.forEach(item => {
    item.expandedRowKeys = []
    item.columns = _.cloneDeep(columns.value)
  })
}

// ————————————————————————————   单题限时   ———————————————————————————————————————————————— 
let isIndividualTimingColumn = {
  title: '限时（秒）',
  dataIndex: 'duration',
  key: 'duration',
  width: 100,
  align: 'right',
  resizable: true
}
watch(() => props.isIndividualTiming, (isIndividualTiming) => {
  try {
    let beforeIndex = columns.value.findIndex((item) => item.key === 'new_score')
    let index = columns.value.findIndex((item) => item.key === 'duration')
    if (isIndividualTiming == '1' && index == -1) {
      columns.value.splice(beforeIndex + 1, 0, isIndividualTimingColumn)
    }
    if (isIndividualTiming == '0' && index != -1) {
      columns.value.splice(index, 1)
    }
    _list.value.forEach(item => item.columns = _.cloneDeep(columns.value))
  } catch (error) {
    console.log('error', error)    
  }
}, { immediate: true })
const limitMinutes = ref(0)
const limitSeconds = ref(0)
function handleLimitTimeConfirm(item: PaperFbodyItemModelWithQuestionChildren) {
  let duration = limitMinutes.value * 60 + limitSeconds.value
  let maxDuration = 60 * 60 // 最长每题限时60分钟
  if (duration) {
    item.children.forEach(i => i.duration = Math.min(duration, maxDuration))
  }
}

function handleLimitTimeVisibleChange(visible: boolean) {
  if (!visible) {
    limitMinutes.value = 0
    limitSeconds.value = 0
  }
}

function generateTitle(type: number, length: number, options: number) {
  let optionsChar = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  return QuestionEnum[type] + optionsChar[length] + '选' + optionsChar[options]
}



/** —————————————————————————————— 题目增删改查 —————————————————————————————————————— */
// 删除题目
function removeQustions(arr: QuestionModel[]) {
  if (!arr?.length) return
  let delIds = arr.map((item) => item.id)
  for (let i = _list.value.length - 1, t: any; (t = _list.value[i]); i--) {
    for (let j = t.children.length - 1, q = null; (q = t.children[j]); j--) {
      if (delIds.includes(q.id)) {
        t.children.splice(j, 1)
      }
    }
    if (!t.children?.length) {
      _list.value.splice(i, 1)
      expandKeys.value = expandKeys.value.filter((item) => item !== t.id)
    }
    if (t.options) {
      t.title = generateTitle(t.type, t.children.length, t.options)
    }
  }
}

// 获取根据当前题型下的已有题目获取新进的题目的单题限时
function getDurationByArr(type: number, arr?: QuestionModel[]) {
  let defaultDuration = QUESTIONS.find(item => item.value === type)?.defaultDuration ?? 0
  if (!arr?.length) return defaultDuration
  if (arr.every((item) => item.duration === arr[0].duration)) {
    return arr[0].duration
  } else {
    return defaultDuration
  }
}

// 添加常规题
function addNormalQuestions(arr: QuestionModel[]) {
  let newArr:PaperFbodyItemModelWithQuestionChildren[] = []
  arr?.forEach((q) => {
    let parent = _list.value.find((item: any) => item.type === q.type && !item.options)

    // 添加单题限时
    q.duration = getDurationByArr(q.type, parent?.children)

    if (parent) {
      parent.children.push(q)
    } else {
      parent = newArr.find((item: any) => item.type === q.type && !item.options)
      if (parent) {
        parent.children.push(q)
      } else {
        let id = uuid()
        newArr.push({
          id,
          title: QuestionEnum[q.type],
          type: q.type,
          children: [q],
          isUnify: false,
          unifyScore: q.new_score,
          sortable: false,
          columns: _.cloneDeep(columns.value)
        })
      }
    }
  })
  if (newArr.length) {
    handleSort(newArr)
    _list.value.push(...newArr)
    expandKeys.value.push(...newArr.map(i => i.id))
  }
}

// 根据指定id添加常规题
function addNormalQuestionsById(arr: QuestionModel[], id: string) {
  let parent = _list.value.find((item) => item.id === id)
  if (!parent) return

  // 添加单题限时
  let duration = getDurationByArr(parent?.type, parent.children)
  arr.forEach((q) => q.duration = duration)

  parent.children.push(...arr)
}

// 添加选做题
function addOptionalQuestions(arr: QuestionModel[], options: number) {
  if (!arr?.length) return
  let type = arr[0].type

  // 添加单题限时
  let duration = QUESTIONS.find(item => item.value === type)?.defaultDuration ?? 0
  arr.forEach((q) => q.duration = duration)

  let title = generateTitle(type, arr.length, options)
  let id = uuid()
  // 选做题的默认分值，取均分
  let unifyScore = Math.round(arr.reduce((prev, cur) => prev + cur.new_score!, 0) / arr.length)
  _list.value.push({
    id,
    title,
    type,
    children: arr,
    options,
    isUnify: true,
    unifyScore,
    sortable: false,
    columns: _.cloneDeep(columns.value)
  })
  expandKeys.value.push(id)
}

// 根据指定id添加选做题
function addOptionalQuestionsById(arr: QuestionModel[], options: number, id: string) {
  let parent = _list.value.find((item) => item.id === id)
  if (!parent) return
  let type = parent.type

  // 添加单题限时
  let duration = getDurationByArr(parent?.type, parent.children)
  arr.forEach((q) => q.duration = duration)

  parent.children.push(...arr)
  parent.title = generateTitle(type, parent.children.length, options)
  parent.options = options
  expandKeys.value.push(id)
}

/** 根据新增或删除的题目列表更新当前组件以及父组件 ★★★★★ */
const currentId = ref('') // 当前正在编辑的大块id
function resetCurrentId() {
  currentId.value = ''
}
function updateList({ addList, delList }: any, options?: number) {
  removeQustions(delList)
  // 添加new_score字段
  addList?.forEach((item: any) => (item.new_score = item.score))
  if (options) {
    if (currentId.value) {
      addOptionalQuestionsById(addList, options, currentId.value)
    } else {
      addOptionalQuestions(addList, options)
    }
  } else {
    if (currentId.value) {
      addNormalQuestionsById(addList, currentId.value)
    } else {
      addNormalQuestions(addList)
    }
  }
  if (!paperQuestionSelectPreviewRef.value?.hasSorted) {
    console.log('进行了最终的排序')
    handleSort(_list.value)
  }
  emits('update:list', JSON.parse(JSON.stringify(_list.value)))
}

// 排序
function handleSort(list: PaperFbodyItemModelWithQuestionChildren[]) {
  // 判断，单选，多选，填空，排序，问答，问答自选题，算法，算法自选题
  let defaultOrder = [2, 0, 1, 5, 6, 3, 4]
  list.sort((a, b) => {
    if (a.type !== b.type) {
      return defaultOrder.indexOf(a.type) - defaultOrder.indexOf(b.type)
    } else {
      if (!a.options && b.options) return -1
      else if (a.options && !b.options) return 1
      else return 0
    }
  })
}

// 编辑某一题型
function editQuestion(item: PaperFbodyItemModelWithQuestionChildren) {
  currentId.value = item.id
  emits('edit', item)
}

/** 删除某一道题 */
function deleteQuestion(record: any, item: PaperFbodyItemModelWithQuestionChildren) {
  if (item.options && item.children.length <= item.options + 1) {
    message.warning('可选题量不能少于或者等于必做题量')
    return
  }
  let delList = [record]
  updateList({ delList })
}

/** 删除某一类型下的所有子题 */
function deleteQuestionType(item: PaperFbodyItemModelWithQuestionChildren) {
  updateList({ delList: item.children })
}

/** ———————————————————————————————— 分值 ———————————————————————————————————— */
function changeUnify(item: PaperFbodyItemModelWithQuestionChildren) {
  if (item.isUnify) {
    let currentTotalScore = item.children.reduce((acc: number, cur: any) => acc + cur.new_score, 0)
    let unifyScore = 1
    if (item.type === QuestionEnum['填空题']) {
      let averageScore = Math.floor(
        currentTotalScore /
          item.children.reduce((acc: number, cur: any) => acc + JSON.parse(cur.answer).length, 0)
      )
      unifyScore = Math.max(1, averageScore)
      item.children.forEach((q: any) => {
        q.new_score = JSON.parse(q.answer).length * unifyScore
      })
    } else {
      unifyScore = Math.floor(currentTotalScore / item.children.length)
      item.children.forEach((q: any) => {
        q.new_score = unifyScore
      })
    }
    item.unifyScore = unifyScore
  }
}

function handleIptEnter(e: any) {
  e.target.blur()
}
// 试卷总分
const totalScore = computed(() => {
  let total = 0
  _list.value.forEach((item) => {
    item.children.forEach((q: any) => {
      if (item.isUnify) {
        if (item.type === QuestionEnum['填空题']) {
          q.new_score = JSON.parse(q.answer).length * (item.unifyScore ?? 1)
        } else {
          q.new_score = item.unifyScore
        }
      }
      if ([QuestionEnum['填空题'], QuestionEnum['问答题']].includes(item.type)) {
        // 分配小空分值
        item.children.forEach(adjustQuestionItemScore)
      }
      if (!item.options) total += q.new_score // 如果不是选做题则总分累加
    })
    if (item.options) total += item.unifyScore! * item.options // 如果是选做题则总分+=每题分值*题数
  })
  return total
})
// 根据某一道填空题/问答题总分分配每个空分数
function adjustQuestionItemScore(item: {
  new_score: number
  score: number
  answer: string // "[{\"score\":0,\"keyword\":[\"123\"]}]"
}) {
  const total_score = item.new_score
  try {
    let answer = JSON.parse(item.answer)
    if (answer.some((i: any) => i.score > 0)) {
      // 如果是按空得分（填空题）/按得分点得分（问答题），则需要分配分数
      adjust_scores_proportional(answer, total_score)
      // new_score赋值给score（改分后保证给后端的数据结构不变）
      answer = answer.map((item: any) => {
        let { new_score, ...rest } = item
        return Object.assign(rest, { score: new_score })
      })
    }
    item.answer = JSON.stringify(answer)
  } catch (error) {}
}

// 右侧题目预览面板
const paperQuestionSelectPreviewRef = ref<InstanceType<typeof PaperQuestionSelectPreview>>()
async function onSortEnd(newIndex: number) {
  await nextTick()
  // 找到第newIndex个.ant-collapse-item元素， 将它的.ant-collapse-header元素添加sortend类名，三秒之后移除这个类名
  let header = document.querySelectorAll('.ant-collapse-item .ant-collapse-header')[newIndex]
  // 滚动到可视区域
  header.scrollIntoView({ behavior: 'smooth', block: 'center' })
  // 将其他的.ant-collapse-header元素的sortend类名移除
  document.querySelectorAll('.ant-collapse-item .ant-collapse-header').forEach((item) => {
    item.classList.remove('sortend')
  })
  header.classList.add('sortend')
  setTimeout(() => {
    header.classList.remove('sortend')
  }, 3000)
}

function handleTableDragEnd(item: any) {
  let index = expandKeys.value.findIndex((i) => i === item.id)
  item.id = uuid()
  expandKeys.value.splice(index, 1, item.id)
}

/** 
 * @description 判断某一题是否疑似重复(第一题不展示,后面的展示)
 * @param record 当前题目
 */
function isRepeat({ id, repetition }: any) {
  if (!repetition) return false
  try {
    for (let i = 0; i < _list.value.length; i++) {
      for (let j = 0; j < _list.value[i].children.length; j++) {
        if (_list.value[i].children[j].id === id) {
          return false
        } else {
          if (_list.value[i].children[j].repetition === repetition) {
            return true
          }
        }
      }
    }
  } catch (error) {
    return false
  }
}

defineExpose({
  init,
  _list,
  updateList,
  resetCurrentId,
  totalScore,
  adjustQuestionItemScore,
  generateTitle
})
</script>
<style lang="less" scoped>

@keyframes shineAnimate {
  from {
    background: #f2f5fc;
  }
  to {
    background: #daf1ff;
  }
}

.ant-btn-link {
  color: #5478ee;
}

.paper-form {
  height: 100%;
  flex: 1;
  min-height: 0;
  overflow-y: scroll;
  padding-right: 4px;
}

:deep(.ant-collapse) {
  border: none;
  border-radius: 8px;
  background-color: #fff;

  .ant-collapse-item {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #d9d9d9;

    .ant-collapse-header {
      background-color: #f2f5fc;
      align-items: center;
      padding: 0;
      height: 48px;
      padding-left: 12px;

      &.sortend {
        animation: shineAnimate 0.8s infinite;
      }

      .header-wrapper {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        cursor: auto;
        padding-right: 12px;
      }

      .header-left {
        margin-right: auto;
        display: flex;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: bold;
          margin-right: 16px;
        }
      }

      .header-right {
        display: flex;
        align-items: center;

        .ant-switch {
          margin-left: 8px;
          margin-right: 8px;
        }
      }
    }
  }
}

.exclude-tag {
  font-size: 12px;
  color: #d71310;
  background-color: #fcf0f0;
  border-radius: 4px;
  padding: 2px 4px;
  margin-left: 2px;
}

:deep(.number-ipt) {
  .ant-input-number-input {
    text-align: right;
    border: 1px dashed transparent;
    border-radius: 8px;
  }

  &.border {
    .ant-input-number-input {
      border: 1px dashed #d9d9d9;
    }
  }

  &.move {
    .ant-input-number-input {
      cursor: move !important;
    }
  }

  &.ant-input-number-disabled {
    background-color: transparent;
    color: #121633;

    .ant-input-number-input {
      cursor: text;
    }
  }

  &.unify-ipt {
    width: 40px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    background: #ffffff;

    .ant-input-number-input {
      border: none;
      text-align: center;
    }
  }
  &:not(.unify-ipt) {
    max-width: 90px;
    width: auto;
  }
}

:deep(.ant-table) {
  .ant-table-row {
    &.move {
      cursor: move;
    }
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
}

.overview-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  border: 1px solid #5377ee;
  background: #f1f4fe;
  height: 40px;
  padding: 0 10px;
  margin-right: 10px;

  > div {
    display: flex;
    align-items: center;
  }
}
.tooltip-style {
  display: block;
  // width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left !important;
  margin-right: 8px;
}

.expanded-row {
  padding: 16px;
}

:deep(.question-content) {
  img {
    height: 50px;
    width: auto;
    cursor: initial!important;
    display: inline-block!important;
    float: none!important;
  }
  p {
    display: inline-block;
  }
}
</style>

<style lang="less">
.paper-form {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }

  .ant-table-selection-column {
    padding-left: 2px !important;
  }

  .ant-table-thead > tr > th {
    font-weight: bold;

    &:first-child {
      padding-left: 2px !important;
    }

    &:nth-of-type(2) {
      text-align: left;
    }

    background: #f0f4fe !important;

    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px !important;
      font-weight: bold;
      color: #121633;
    }
  }

  .ant-table-tbody > tr:not(.ant-table-expanded-row) > td {
    padding: 10px;
    font-family: PingFang HK;
    font-size: 14px;
    color: #121633;

    &:nth-of-type(2) {
      text-align: left;
    }
  }

  .ant-btn {
    font-size: 14px;
  }
  .ant-table-content {
    overflow: auto hidden;
  }
}

.popconfirm-individualtime {
  .ant-popover-message-title {
    padding-left: 0;
  }
}
</style>
