<template>
  <a-modal
    :visible="props.visible"
    destroyOnClose
    :keyboard="false"
    :maskClosable="false"
    :footer="null"
    :closable="false"
    @cancel="() => emits('close')">
    <template #title>
      <div class="title-wrap">
        <div class="text-wrap">
          <img src="@/assets/icons/svg/ai.svg" alt="" class="ai">
          <div class="text">助手</div>
        </div>
        <CloseOutlined class="close" style="fontSize: 16px; color: rgba(0, 0, 0, 0.85);" @click="handleClose" />
      </div>
    </template>
    <AiInputInfo v-model:value="text" :disabled="loading" />
    <div class="template-wrap" v-show="!loading">
      <div class="title">生成示例：</div>
      <div class="template-list">
        <div class="template-item" v-for="item in templateTextList" :key="item" @click="text = item">
          <div class="text-wrap">{{ item }}</div>
        </div>
      </div>
    </div>
    <div class="loading-wrap" v-show="loading">
      <Vue3Lottie :animationData="AstronautJSON" :width="188" :height="135" />
    </div>
    <div class="footer">
      <a-button class="common-ai-button" :loading="loading" style="border-radius: 8px;" @click="handleCreate">
        <template #icon><img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;" /></template>
        {{ loading ? '正在生成' : '开始生成' }}
      </a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { Vue3Lottie } from 'vue3-lottie'

import { aiGenerateQuestion } from '@/api/exam'
import AstronautJSON from '@/assets/animation/ai_loading.json'
import AiInputInfo from './AiInputInfo.vue'
import { aiinterviewgeneratequestion } from '@/api/interview'

const props = withDefaults(defineProps<{
  apiName?: string
  visible: boolean
  type: number
}>(), {
  apiName: 'aiGenerateQuestion'
})

const emits = defineEmits(['close', 'created'])

const loading = ref(false)
const text = ref('') // 输入文字内容

const templateTextList = [ // 示例内容
  '请生成一道与操作系统中进程管理有关的试题',
  '请根据以下文字材料生成一道试题：进程（Process）是计算机中运行中程序的实例。每个进程都有自己的内存空间、系统资源和执行状态。进程是程序的执行过程，它包括代码、数据和正在执行的程序状态。在操作系统中，每个进程都被视为独立的实体，它们之间相互隔离，无法直接访问对方的内存空间。线程（Thread）是进程中的执行单元，一个进程可以包含多个线程。线程共享相同的内存空间和系统资源，因此线程之间可以更方便地共享数据和通信。由于线程可以并发执行，因此它们能够更加高效地利用多核处理器和提升系统性能。然而，线程之间的共享也增加了并发编程的复杂性，需要额外的注意来避免数据竞争和同步问题。',
]

const handleClose = () => { // 关闭弹窗
 if (loading.value) {
  message.info('正在生成ai题目中，请稍等')
  return
 }
 emits('close')
}

const api = {
  aiGenerateQuestion,
  aiinterviewgeneratequestion
}

const handleCreate = async () => { // ai生成
  try {
    if (!text.value) {
      message.warning('请输入关键词描述或者文字材料')
      return
    }
    loading.value = true
    const res: any = await api[props.apiName]({
      describe: text.value,
      type: props.type,
      limit: 1,
    })
    if (Array.isArray(res) && res.length > 0) {
      emits('created', res[0])
    } else {
      message.error('生成题目出错，请重试')
    }
  } finally {
    loading.value = false
  }
}

watch(() => props.visible, (val) => {
  if (!val) {
    loading.value = false
    text.value = ''
  }
})

</script>

<style lang="less" scoped>
.title-wrap {
  height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .text-wrap {
    display: flex;
    align-items: center;
    .ai {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
    .text {
      font-size: 20px;
      font-weight: 600;
      color: rgba(0,0,0,0.85);
      line-height: 22px;
    }
  }
}
.template-wrap {
  margin-top: 20px;
  margin-bottom: 32px;
  .title {
    font-size: 14px;
    color: rgba(0,0,0,0.85);
    line-height: 20px;
  }
  .template-item {
    margin-top: 8px;
    padding: 8px;
    border-radius: 8px;
    // border: 1px dashed #ccc;
    background: #F8F9FA;
    cursor: pointer;
    &:hover {
      background: #F1F4FE;
    }
    .text-wrap {
      max-height: 36px;
      font-size: 12px;
      color: rgba(1,1,1,0.85);
      line-height: 18px;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      white-space: normal !important;
      -webkit-line-clamp: 2;//超出2行显示省略号，可修改数字
      -webkit-box-orient: vertical;
    }
  }
}
.loading-wrap {
  margin-top: 20px;
  margin-bottom: 19px;
}
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  .btn {
    width: 108px;
    height: 32px;
    border: 1px solid;
    border-radius: 8px;
    border-image: linear-gradient(270deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%) 1 1;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    .ai {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .text {
      font-size: 14px;
      color: rgba(0,0,0,0.65);
      line-height: 22px;
    }
  }
}
</style>
