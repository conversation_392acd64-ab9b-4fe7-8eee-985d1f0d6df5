import { RuleObject } from 'ant-design-vue/es/form/interface'
import keywords from '@/views/QuestionManage/keywords'

export const CHECK_VARIATE_NAME_REG = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/

// 校验题目类型
export const checkType = async (rule: RuleObject, value: string) => {
  if (value === undefined) {
    return Promise.reject('请选择题目类型')
  } else {
    return Promise.resolve()
  }
}

// 校验所属题库
export const checkCategory = async (rule: RuleObject, value: string) => {
  if (value === undefined || !value) {
    return Promise.reject('请选择所属题库')
  } else {
    return Promise.resolve()
  }
}

// 校验选项内容
export const checkOptions = async (rule: RuleObject, value: []) => {
  let isDup = false
  const allContents: any = []
  value.forEach((item: any) => {
    if (allContents.includes(item.content)) {
      isDup = true
    } else {
      allContents.push(item.content)
    }
  })

  if (!value.length) {
    return Promise.reject('请添加选项')
  }
  if (!value.every((item: any) => item.content)) {
    return Promise.reject('选项内容都不能为空')
  }
  if (isDup) {
    return Promise.reject('选项内容不能相同')
  } else {
    return Promise.resolve()
  }
}

// 校验标签
// const checkTags = async(rule: RuleObject, value: []) => {
//   if (value.length === 0) {
//     return Promise.reject('请选择标签')
//   }else {
//     return Promise.resolve();
//   }
// }

// 校验题干
export const checkBody = async (rule: RuleObject, value: '') => {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  } else {
    return Promise.resolve()
  }
}

export const checkRtype = async (rule: RuleObject, value: { type: string }) => {
  if (!value.type) {
    return Promise.reject('请选择返回值类型')
  } else {
    return Promise.resolve()
  }
}

// 校验警用关键词
export async function checkDisablekws(rule: RuleObject, value: {}) {
  if (!Object.values(value).flat().length) return Promise.reject('请输入禁用关键词')
  return Promise.resolve()
}


export const checkParameters = async (rule: RuleObject, value: []) => {
  if (value.length === 0) {
    return Promise.reject('请添加参数列表')
  }
  if (value.some((item: any) => !item.name || !item.type)) {
    return Promise.reject('请完善参数列表内容')
  }
  if (value.some((item: any) => !CHECK_VARIATE_NAME_REG.test(item.name))) {
    return Promise.reject('名称不符合命名规范')
  }
  if (value.some((item: any) => judgeIsKeyword(item.name))) {
    return Promise.reject('变量名称不能是关键字')
  }

  if ([...new Set(value.map((item: any) => item.name))].length !== value.length) {
    return Promise.reject('变量名不能重复')
  }
  return Promise.resolve()
}
export const checkBtestcase = async (rule: RuleObject, value: []) => {
  for (let i = 0; i < value.length; i++) {
    const item: any = value[i]
    for (let j = 0; j < item.input.length; j++) {
      if (!Object.keys(item.input[j])[0]) {
        return Promise.reject('请完善题干测试用例')
      }
      if (!item.input[j][Object.keys(item.input[j])[0]]) {
        return Promise.reject('请完善题干测试用例')
      }
    }
    for (let k = 0; k < item.output.length; k++) {
      if (!item.output[k].value) {
        return Promise.reject('请完善题干测试用例')
      }
    }
  }
  return Promise.resolve()
}
export const checkPtestcase = async (rule: RuleObject, value: []) => {
  for (let i = 0; i < value.length; i++) {
    const item: any = value[i]
    for (let j = 0; j < item.input.length; j++) {
      if (!Object.keys(item.input[j])[0]) {
        return Promise.reject('请完善批卷测试用例')
      }
      if (!item.input[j][Object.keys(item.input[j])[0]]) {
        return Promise.reject('请完善批卷测试用例')
      }
    }
    for (let k = 0; k < item.output.length; k++) {
      if (!item.output[k].value) {
        return Promise.reject('请完善批卷测试用例')
      }
    }
  }
  return Promise.resolve()
}

// 校验变量名合法性
export const checkVariateName = async (rule: RuleObject, value: string) => {
  return
  if (!value.length) {
    return Promise.reject('请输入名称')
  }
  if (!CHECK_VARIATE_NAME_REG.test(value)) {
    return Promise.reject('名称不符合命名规范')
  }
  return Promise.resolve()
}

export function judgeIsKeyword(name: string) {
  if (
    keywords.C_Keywords.includes(name) ||
    keywords.CPP_Keywords.includes(name) ||
    keywords.Java_Keywords.includes(name) ||
    keywords.Javascript_Keywords.includes(name) ||
    keywords.Python_Keywords.includes(name) || 
    keywords.Go_Keywords.includes(name)
  ) {
    return true
  }
  return false
}

// const checkScorebasis = async (rule: RuleObject, value: '') => {
//   if (value === '') {
//     return Promise.reject('请输入得分依据')
//   } else {
//     return Promise.resolve()
//   }
// }

// const checkAnswer = async (rule: RuleObject, value: '') => {
//   if (value === '') {
//     return Promise.reject('请输入得分点')
//   } else {
//     return Promise.resolve()
//   }
// }
