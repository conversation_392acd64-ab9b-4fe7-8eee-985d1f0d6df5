<template>
  <div class="draft-container">
    <h3 class="draft-title">草稿箱</h3>
    <div class="draft-main">
      <div class="draft-header">
        <div class="header-left">
          <a-input-search
            v-model:value.trim="searchContent"
            placeholder="请输入题干"
            allow-clear
            @search="getSearchData"
          />
        </div>
        <div class="header-right">
          <span class="del-btn" @click="delMultiDraft">批量删除</span>
        </div>
      </div>
      <a-table
        :columns="data.columns"
        :row-key="(record:any) => record.id"
        :data-source="data.draftList"
        :row-selection="{
          selectedRowKeys: selectedQuestions,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
          getCheckboxProps: getCheckboxProps
        }"
        :loading="data.loading"
        :customRow="customRow"
        :scroll="{ x: 1200 }"
        :pagination="paginationConfig"
        @change="handleTableChange"
        @resizeColumn="(w: any, col: any) => col.width = w"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>{{
                  record.complicatedediting ? record.title.slice(0, 100) : record.title
                }}</template>
                <span class="questionContent">{{
                  record.complicatedediting ? record.title.slice(0, 100) : record.title
                }}</span>
              </a-tooltip>
            </span>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>
              {{ getType(record.type) }}
            </span>
          </template>
          <template v-else-if="column.key === 'updateTime'">
            <span>
              {{ dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <span>
              <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                <template #title>
                  <span class="tooltip-text">编辑</span>
                </template>
                <a-button type="text" size="small" @click="editDraft(record)">
                  <svg-icon name="editable" style="margin-bottom: -2px" />
                </a-button>
              </a-tooltip>
              <a-divider type="vertical" />
              <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                <template #title>
                  <span class="tooltip-text">删除</span>
                </template>
                <a-popconfirm
                  title="确定删除该题目草稿？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteDraft(record)"
                >
                  <a-button type="text" danger size="small">
                    <img src="@/assets/images/svg/icon_delete.svg" style="width: 14px; margin-bottom: 3px" />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </span>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <div class="expanded-row">
            <QuestionItemDisplay
              :question-detail="record.content"
              show-score
              show-type
              show-tag
              show-correct-answer
              show-question-difficulty
              show-question-points-and-basis
              :option-letter-type="'text'"
            />
          </div>
        </template>
      </a-table>
    </div>
    <a-modal title="编辑草稿" v-model:visible="questionAddFormVisible" wrapClassName="full-screen-modal" width="100%" :maskClosable="false" :closable="false" :keyboard="false" :footer="null">
      <QuestionAddForm v-if="questionAddFormVisible" :draftId="draftId" :categoryRootUUID="categoryRootUUID" @close="onFormClose"></QuestionAddForm>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, createVNode } from 'vue'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
import { draftDelete, draftques , deldraftques} from '@/api/admin/questionManage'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import QuestionAddForm from './createQuestion.vue'
import dayjs from 'dayjs'

const tableColumn = ref([
  {
    title: '题型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    ellipsis: true,
    filters: [
      { text: '单选题', value: 0 },
      { text: '多选题', value: 1 },
      { text: '判断题', value: 2 },
      { text: '填空题', value: 5 },
      { text: '问答题', value: 3 },
      { text: '算法题', value: 4 },
      { text: '排序题', value: 6 }
    ],
    resizable: true
  },
  {
    title: '题干',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
    width: 850,
    resizable: true
  },
  {
    title: '题库',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 160,
    ellipsis: true,
    resizable: true
  },
  {
    title: '分值',
    dataIndex: 'score',
    key: 'score',
    width: 120,
    customHeaderCell: () => ({
      style: {
        textAlign: 'left !important' //头部单元格水平居中
      }
    }),
    align: 'right',
    ellipsis: true,
    resizable: true
  },
  {
    title: '上次保存时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
])

const data = reactive({
  columns: tableColumn,
  loading: false,
  draftList: []
})

// 获取草稿箱列表
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

// 快速搜索
const searchContent = ref('')
const getSearchData = () => {
  paginationConfig.value.current = 1
  getDraftList()
}

const getType = (type: number) => {
  switch (type) {
    case 0:
      return '单选题'
    case 1:
      return '多选题'
    case 2:
      return '判断题'
    case 3:
      return '问答题'
    case 4:
      return '算法题'
    case 5:
      return '填空题'
    case 6:
      return '排序题'
    case 7:
      return '复合题'
  }
}

// 选择条目
const selectedQuestions = <any>reactive([])
const onSelect = (record: any, selected: boolean) => {
  if (!selected) {
    // 取消勾选,删除对应的数组项
    selectedQuestions.map((item: any, index: number) => {
      if (item === record.id) {
        selectedQuestions.splice(index, 1)
      }
    })
  }
  if (selected) {
    // 点击勾选，添加id字段到selectedQuestions数组里
    selectedQuestions.push(record.id)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedQuestions.push(item.id)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedQuestions.map((x: any, indey: number) => {
        if (item.id === x) {
          selectedQuestions.splice(indey, 1)
        }
      })
    })
  }
}
const getCheckboxProps = (record: any) => {
  return {
    selectedRowKeys: selectedQuestions.includes(record.id)
  }
}

const customRow = (record: any, index: number) => {
  return {
    onMouseenter: () => {
      record.active = true
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = '#3158BD'
    },
    onMouseleave: () => {
      record.active = false
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = ''
    }
  }
}

// 表格过滤,排序,翻页操作等
const handleTableChange = (pagination: any, filters: any = {}, sort: any = {}) => {
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  types.value = filters.type
  getDraftList()
}

// 编辑草稿箱
const draftId = ref('')
const categoryRootUUID = ref('')
const questionAddFormVisible = ref(false)
const editDraft = (record: any) => {
  draftId.value = record.id
  categoryRootUUID.value = record.categoryRootUUID
  questionAddFormVisible.value = true
}
function onFormClose(needRefresh: boolean) {
  needRefresh && getDraftList()
  categoryRootUUID.value = ""
  questionAddFormVisible.value = false
}

const deleteDraft = (record: any) => {
  data.loading = true
  draftDelete({
    // action: 'del',
    ids: [record.id]
  })
    .then(() => {
      data.loading = false
      message.success('删除成功!')
      paginationConfig.value.current = 1
      getDraftList()
    })
    .catch(() => {
      data.loading = false
    })
}
const delMultiDraft = () => {
  if (!selectedQuestions.length) {
    message.error('请勾选要删除的题目草稿')
    return
  }
  const params = {
    action: 'del',
    ids: selectedQuestions
  }
  Modal.confirm({
    title: () => `确定删除勾选的${selectedQuestions.length}个题目草稿?`,
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    onOk() {
      deldraftques(params).then(() => {
        message.success('批量删除成功!')
        selectedQuestions.length = 0
        // 刷新
        paginationConfig.value.current = 1
        getDraftList()
      })
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {}
  })
}

// 查询题目
const types = ref([])
const getDraftList = () => {
  data.loading = true
  draftques({
    action: 'query',
    page: paginationConfig.value.current,
    per_page: paginationConfig.value.pageSize,
    title: searchContent.value,
    typeList: types.value
  })
    .then((res: any) => {
      data.loading = false
      res.records.forEach(item => {
        item.content = JSON.parse(item.content)
      });
      data.draftList = res.records
      paginationConfig.value.total = res.total
    })
    .catch(() => {
      data.loading = false
    })
}

onMounted(() => {
  getDraftList()
})
</script>

<style lang="less" scoped>
.draft-container {
  height: 100%;
  overflow: auto;
  padding: 0 20px 20px 20px;
  // box-sizing: border-box;
  .draft-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }
  .draft-main {
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    min-height: calc(100% - 48px);
    padding: 10px 24px;
    background-color: #fff;
    .draft-header {
      margin: 8px 0;
      display: flex;
      padding-bottom: 10px;
      justify-content: space-between;
      align-items: center;
      .header-left {
        display: flex;
        align-items: center;
      }

      .header-right {
        display: flex;
        align-items: center;
        font-size: 14px;
        span {
          width: 88px;
          height: 32px;
          text-align: center;
          line-height: 32px;
          border-radius: 8px;
          font-size: 14px;
          margin-left: 8px;
          cursor: pointer;
        }

        .del-btn {
          border: 1px solid rgba(0, 0, 0, 0.15);
        }
      }
    }

    .expanded-row {
      padding: 16px;
    }
  }
}
</style>
<style lang="less">
.ant-table-expanded-row td:nth-child(1) {
  box-shadow: 2px 0px 0px 0px #5478ee inset;
}
.ant-table-expanded-row td:nth-child(2) {
  background: #fff;
}
.draft-container {
  .ant-table-thead > tr > th {
    background: #f1f4fe !important;
    padding-left: 10px;
    color: #121633;
    font-weight: bold;
    font-size: 15px;
    .ant-table-column-title {
      color: #121633;
      font-weight: bold;
      font-size: 15px;
    }
  }
  .ant-table-tbody > tr:not(.ant-table-expanded-row) > td {
    padding: 10px;
    font-size: 14px;
    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }
  .ant-table-row-expand-icon:focus,
  .ant-table-row-expand-icon:hover {
    color: #5478ee;
  }

  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
}
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
</style>
