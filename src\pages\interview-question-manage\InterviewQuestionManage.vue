<template>
  <div class="question-manage-container">
    <h3 class="question-manage-title">题目管理</h3>
    <div class="question-manage-content">
      <SubjectTree :editable="true" @select="handleSelectSubject" @delete="handleSubjectDelete"></SubjectTree>
      <div class="questions-list">
        <div v-if="isOfficialSubject && store.getters.userInfo?.tryout" class="locked-box">
          <div class="locked">
            <img src="@/assets/images/locked.png" alt="" style="width: fit-content;" />
            <span class="desc">升级正式版可解锁</span>
          </div>
        </div>
        <QuestionList v-else ref="listRef" :writable="true" :current="route.params.page ? Number(route.params.page) : 1"
          :subject-id="subjectId" :is-official-subject="isOfficialSubject"></QuestionList>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import SubjectTree from './InterviewSubjectTree.vue'
import QuestionList from './InterviewQuestionList.vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

const store = useStore()

const listRef = ref<InstanceType<typeof QuestionList>>()
const route = useRoute()
// 题库树
const subjectId = ref('')
const isOfficialSubject = ref(store.getters.userInfo.is_official_cert ? true : false)
function handleSelectSubject({ id, is_official_cert }: any) {
  subjectId.value = id
  isOfficialSubject.value = is_official_cert
}
function handleSubjectDelete(parentId: string) {
  subjectId.value = parentId
}
</script>

<style lang="less" scoped>
.question-manage-container {
  height: 100%;
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
  min-width: 1200px;

  .question-manage-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
  }

  .question-manage-content {
    flex: 1;
    min-height: 0;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    overflow: hidden;
    display: flex;

    .questions-list {
      padding-right: 16px;
      background-color: #fff;
      flex: 1;
      min-width: 530px;
      padding-top: 16px;

      .locked-box {
        height: 100%;

        .locked {
          display: flex;
          flex-direction: column;
          height: 100%;
          justify-content: center;
          align-items: center;

          .desc {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
