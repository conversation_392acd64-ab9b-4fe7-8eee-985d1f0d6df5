<template>
    <a-table class="common-table" sticky :columns="columns" :row-key="(record: any) => record.id" :data-source="list"
        @change="handleTableChange" :scroll="{ x: 1200, y: '90%' }" :pagination="paginationConfig" :loading="loading"
        @resizeColumn="(w: any, col: any) => col.width = w">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'username'">
                <a-button type="link" @click="analyze(record)" :title="record.username">{{ record.username }}</a-button>
            </template>
        </template>
    </a-table>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { studentlist } from '@/api/admin/statisticAnalysis'

const columns = ref([
    {
        title: '考生姓名',
        dataIndex: 'username',
        key: 'username',
        width: 400,
        ellipsis: true,
        resizable: true
    },
    {
        title: '性别',
        dataIndex: 'sexName',
        key: 'sexName',
        width: 320,
        ellipsis: true,
        resizable: true
    },
    {
        title: '年龄',
        width: 320,
        dataIndex: 'age',
        key: 'age',
        ellipsis: true,
        resizable: true
    },
    {
        title: '最高学历',
        width: 320,
        dataIndex: 'heducation',
        key: 'heducation',
        ellipsis: true,
        resizable: true
    },
    {
        title: '报考部门',
        width: 320,
        dataIndex: 'deptName',
        key: 'deptName',
        ellipsis: true,
        resizable: true
    }
])

const paginationConfig = reactive({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total: number) => '总条数：' + total,
    total: 0,
    pageSizeOptions: ['10', '20', '50', '100'],
    size: 'small'
})

const list = ref<any[]>([])
const loading = ref(false)
async function getList() {
    loading.value = true
    try {
        let res = await studentlist({ 
            basePager:{
                current: 1,
                size: 9999
            }}) as any

        list.value = res.records
    } catch (error) {

    } finally {
        loading.value = false
    }
}
getList()


function handleTableChange(pagination: any, filters: any = {}, sort: any = {}) {
    // // 处理排序
    // otherParams.value.order_type = sort.order
    // otherParams.value.order_field = sort.order ? sort.field : undefined
    // 处理分页
    paginationConfig.current = pagination.current
    paginationConfig.pageSize = pagination.pageSize
    // // 处理筛选
    // Object.assign(otherParams.value, filters)
    getList()
}

// 跳转某位考生的分析界面
const router = useRouter()
function analyze(record: any) {
    router.push('/admin/statisticAnalysis/studentSingleAnalysis?id=' + record.id)
}

</script>

<style>
.common-table .ant-spin-nested-loading .ant-spin-container .ant-table {
    overflow: hidden;
}
</style>
<style lang="less" scoped></style>
  