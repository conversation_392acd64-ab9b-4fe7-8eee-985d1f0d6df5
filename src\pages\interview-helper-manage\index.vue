<template>
    <div class="page-wrapper">
        <div class="title-wrap">
            <span class="title">面试助手</span>
            <div class="btns">
                <a-button type="primary" @click="handleCreate">新增面试助手</a-button>
            </div>
        </div>
        <div class="filter-wrapper">
            <div style="flex: 1;">
                <div class="filter-wrapper-row">
                    <div class="filter-item">
                        <a-input-search v-model:value.trim="searchValue" placeholder="请输入面试助手名称/创建人" allow-clear
                            class="select-wrap" @blur="handleSearch" @search="handleSearch" />
                    </div>
                    <div class="filter-item" style="margin-left: 68px;">
                        <span class="filter-label">创建时间</span>
                        <a-range-picker v-model:value="params.start_time_range" :placeholder="['最早开始时间', '最晚开始时间']"
                            valueFormat="YYYY-MM-DD">
                            <template #suffixIcon>
                                <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                            </template>
                        </a-range-picker>
                    </div>
                </div>
            </div>
        </div>
        <ListWrapper ref="listWrapperRef" :card-min-width="392" :params="params" :getListFn="getListFn">
            <template #item="{ item }">
                <ListItemCard :data="item" :search-text="params.name" @removed-ok="handleDelete(item.id)" />
            </template>
        </ListWrapper>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import _ from 'lodash'
import ListWrapper from '@/components/ListWrapper.vue'
import ListItemCard from './components/ListItemCard.vue'
import { useState } from '@/hooks'
import { queryaiinterviewassistant } from '@/api/interview'

const router = useRouter()

// 模糊查询
const searchValue = ref('')
function handleSearch() {
    params.value.name = searchValue.value
}

const [params, resetParams, rawParams] = useState<{
    action: 'query'
    name: string
    start_time_range: null | string[]
}>({
    action: 'query',
    name: '', // 关键词
    start_time_range: null, // 开始结束时间
})

const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()

const getListFn = (params: any) => {
    return queryaiinterviewassistant({
        ...params,
    })
}

// 新建考试
function handleCreate() {
    router.push('/admin/interview/interview-helper-form')
}

// 删除
function handleDelete(id: string) {
  listWrapperRef.value?.handleBatchDelete([id])
}

</script>

<style lang="less" scoped>
.filter-wrapper {
    padding: 24px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
    background: #ffffff;
    border-radius: 8px;
    margin: 0px 20px 20px;
    display: flex;

    :deep(.filter-wrapper-row) {
        display: flex;

        +.filter-wrapper-row {
            margin-top: 16px;
        }

        .ant-select,
        .ant-picker,
        .ant-input-search {
            width: 240px;
        }
    }

    .filter-item {
        display: flex;
        align-items: center;

        .filter-label {
            margin-right: 16px;
            color: #626262;
        }

        &:nth-child(3n+2) {
            justify-content: center;
        }

        &:nth-child(3n+3) {
            justify-content: flex-end;
        }
    }
}

.page-wrapper {
    width: 100%;
    height: 100%;
    min-width: 1150px;
    display: flex;
    flex-direction: column;

    .title-wrap {
        height: 64px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }

        .btns {
            height: 32px;

            :deep(.ant-btn) {
                border-radius: 8px;
                margin-left: 8px;
                font-size: 14px;
            }
        }
    }
}
</style>
