<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="-14.8188447%" y1="36.5601032%" x2="90.4150128%" y2="54.4360121%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="97.8048289%" y1="32.2118435%" x2="-3.24448961%" y2="66.8893875%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M18.5,5.5 C18.8676558,5.5 19.2250138,5.54409062 19.5670747,5.62727279 C20.4739957,7.03108903 21,8.70411707 21,10.5 C21,11.8015557 20.7237141,13.0385813 20.2265911,14.155628 C19.6954029,14.3778427 19.1119671,14.5 18.5,14.5 C17.153279,14.5 15.9447306,13.9084131 15.1200502,12.970935 L15.0715142,12.9705944 C15.0286373,12.892342 14.9828571,12.8159067 14.9343184,12.7414335 C14.8660021,12.6568169 14.8025085,12.5678951 14.7422492,12.4766496 C14.1017397,11.6679859 13.1113609,11.15 12,11.15 C10.8913893,11.15 9.9031627,11.6654254 9.26188397,12.4697125 C9.20070541,12.5633898 9.13568365,12.654628 9.06743341,12.7432515 C9.01104501,12.8252981 8.96022924,12.9110475 8.91306644,12.9990476 L8.92866667,12.9707586 L8.8799498,12.970935 C8.05526942,13.9084131 6.84672096,14.5 5.5,14.5 C4.88803287,14.5 4.3045971,14.3778427 3.77274684,14.1565822 C3.27628593,13.0385813 3,11.8015557 3,10.5 C3,8.70411707 3.52600433,7.03108903 4.4323536,5.62657528 C4.77498624,5.54409062 5.13234416,5.5 5.5,5.5 C6.92468359,5.5 8.1947338,6.16206422 9.0192667,7.19530873 L9.02569033,7.19575863 L9.06820173,7.25774649 C9.1347273,7.34418031 9.19818268,7.43309948 9.25840469,7.52434081 C9.89890502,8.3323512 10.8890008,8.85 12,8.85 C13.1109992,8.85 14.101095,8.3323512 14.7422561,7.52508497 C14.8018173,7.43309948 14.8652727,7.34418031 14.9317983,7.25774649 C14.9474716,7.23839961 14.9610029,7.21715618 14.9743097,7.19575863 C15.8052662,6.16206422 17.0753164,5.5 18.5,5.5 Z" id="path-3"></path>
        <filter x="-16.7%" y="-33.3%" width="133.3%" height="166.7%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="1.17961196e-14%" y1="42.4861997%" x2="100%" y2="57.5138003%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="89.5850875%" y1="40.152513%" x2="5.35482699%" y2="60.3148842%" id="linearGradient-6">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M4.43104914,5.62859732 C3.52549965,7.03269465 3,8.70497881 3,10.5 C3,11.8019433 3.2764505,13.039318 3.77385309,14.1566259 C2.14514777,13.4799284 1,11.873696 1,10 C1,7.94076686 2.38316601,6.20458945 4.27112095,5.66984487 Z M23,10 C23,11.873696 21.8548522,13.4799284 20.2262578,14.1569962 C20.7235495,13.039318 21,11.8019433 21,10.5 C21,8.70497881 20.4745003,7.03269465 19.5689509,5.62859732 C21.5382744,6.10759503 23,7.88309536 23,10 Z" id="path-7"></path>
        <linearGradient x1="-23.7713477%" y1="44.6316124%" x2="76.6486727%" y2="57.1152015%" id="linearGradient-8">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="87.556508%" y1="43.2034955%" x2="5.35482699%" y2="54.5056984%" id="linearGradient-9">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M9,16 C10.0231831,16 11.7687037,16.2315622 12.903018,17.1114433 C11.7218689,17.6811757 10.3981671,18 9,18 C7.60123673,18 6.27700238,17.6809037 5.09612051,17.1115348 C6.23110817,16.2315871 7.97676173,16 9,16 Z" id="path-10"></path>
        <filter x="-38.4%" y="-150.0%" width="176.9%" height="400.0%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="-37.1900495%" y1="40.1156146%" x2="65.4048437%" y2="53.8287612%" id="linearGradient-12">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="89.5070248%" y1="33.0310121%" x2="5.35482699%" y2="60.383098%" id="linearGradient-13">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M14,19.3333333 C14,20.4419956 11.7614237,21 9,21 C6.23857625,21 4,20.4280904 4,19.3333333 C4,18.3285139 4.44912491,17.6136007 5.0979946,17.1106581 C6.27831621,17.6812202 7.6019306,18 9,18 C10.3980694,18 11.7216838,17.6812202 12.902122,17.1123819 C13.5508751,17.6136007 14,18.3285139 14,19.3333333 Z" id="path-14"></path>
        <path d="M9,3.5 C12.0375661,3.5 14.5,5.96243388 14.5,9 C14.5,12.0375661 12.0375661,14.5 9,14.5 C5.96243388,14.5 3.5,12.0375661 3.5,9 C3.5,5.96243388 5.96243388,3.5 9,3.5 Z M9,5.5 C7.06700338,5.5 5.5,7.06700338 5.5,9 C5.5,10.9329966 7.06700338,12.5 9,12.5 C10.9329966,12.5 12.5,10.9329966 12.5,9 C12.5,7.06700338 10.9329966,5.5 9,5.5 Z" id="path-15"></path>
        <filter x="-54.5%" y="-45.5%" width="227.3%" height="227.3%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="1" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.152753497 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="8.81880296%" y1="9.30569435%" x2="93.0787331%" y2="93.1157159%" id="linearGradient-17">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-18" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-19"></use>
        </pattern>
        <image id="image-19" width="18" height="18" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEqADAAQAAAABAAAAEgAAAACaqbJVAAACsklEQVQ4ET2UQYsTQRCFX3aKDBlISDAQ2aDgQRBvCv4ID/4E/9tevXvdX+DBqzdhZRcDkYQdmHWG7l2/15PYbE9qprtevXpVtZMv138/9X3WMEhdz+7G37bLusduH6SLxzyZz6V5Iy2aSrOp1GA3daWp7VoKbKk8slKSEh/BVRUcssvijs2oKvGnmjs1xhnE7jHjYwUAT+WcBYYG3gezK199wuYx9YbBFMN+ZlL7ncNoMMI3y7IBGGj9QHTSKyiP/EDJTOxcNqkZ5JxeONdAHzuMeCOzHkZ2jBaNDAILB7WjtSo2ek3NCsdo6kCLVNKb4lA5HyBTymo7a5J1cTGymOO4tOgzQIvYI0jlgAs+9DnUoEvvyAg2plqpG7J2R1dNBeDZSlrNT4xmaEM1nJ71ixlAfFO2wEjSDICWaK5Apbs9jCaVXmwqXbJXXG6aKMKXyloSA5nRedlVPIrY69BmPZ7EJOvD27po46oVgLPT6KLY7YtvcTarxMPM3Iy/fmddf+vdrE/pSbpcVzQkbABD2rIKG6y42SecT31DJ7YA7I9ZN7ukw730+WNTglx97fRyE6SYtUIrC+/KlbZwajs0KKPBKNzTN38O0i3gjvT+NenhRBy9exP6/gPwNpFyIPo4MkVPWiNuDQQLM/ElV2mOoK+2oS3iWlivLUyOMPx5l9TdJrUEcCt49po6K/Y4tt2YxpHm8+GWiM9X7pmqNJuB1tibdabjg0qmwtpN++Cqs2N3AB02ZmWQ9Sq0XMAK26PgFL2cgnuoXfKfgqKYgNkPFGdOH8aBF2tgkNWC3HFYuOlcGQOdquOB9WgsSaUnkKvrDPZomnqa2NGsifvJF8sGpMHBzr7g/hoH1uVHU2ZzSAEBn0hHsonmP0gUEAM4JfdJmM4JzIPpAfX2nYGeSaRURgKwf8F6BqjPznU/AAAAAElFTkSuQmCC"></image>
        <path d="M9,0 C13.9705627,0 18,4.02943725 18,9 C18,13.9705627 13.9705627,18 9,18 C4.02943725,18 0,13.9705627 0,9 C0,4.02943725 4.02943725,0 9,0 Z" id="path-20"></path>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-564.000000, -130.000000)">
            <g id="icon/06毛玻璃/24*24/09合并监考" transform="translate(564.000000, 130.000000)">
                <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="url(#linearGradient-5)" xlink:href="#path-7"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                </g>
                <g id="监控" transform="translate(3.000000, 1.500000)" fill-rule="nonzero">
                    <g id="形状结合" filter="url(#filter-11)">
                        <use fill="url(#linearGradient-8)" xlink:href="#path-10"></use>
                        <use fill-opacity="0.6" fill="url(#linearGradient-9)" xlink:href="#path-10"></use>
                    </g>
                    <g id="形状结合">
                        <use fill="url(#linearGradient-12)" xlink:href="#path-14"></use>
                        <use fill-opacity="0.6" fill="url(#linearGradient-13)" xlink:href="#path-14"></use>
                    </g>
                    <path d="M9,0 C13.9705627,0 18,4.02943725 18,9 C18,13.9705627 13.9705627,18 9,18 C4.02943725,18 0,13.9705627 0,9 C0,4.02943725 4.02943725,0 9,0 Z M9,3.5 C5.96243388,3.5 3.5,5.96243388 3.5,9 C3.5,12.0375661 5.96243388,14.5 9,14.5 C12.0375661,14.5 14.5,12.0375661 14.5,9 C14.5,5.96243388 12.0375661,3.5 9,3.5 Z" id="形状结合" fill-opacity="0.4" fill="#8CA7FF"></path>
                    <g id="形状结合">
                        <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                        <use fill-opacity="0.4" fill="#0F47FF" xlink:href="#path-15"></use>
                    </g>
                    <circle id="椭圆形" fill-opacity="0.3" fill="#D9E2FF" cx="9" cy="9" r="3.5"></circle>
                    <g id="路径" stroke-linejoin="square" stroke-width="0.5">
                        <path stroke="url(#linearGradient-17)" d="M9,0.25 C11.4162458,0.25 13.6037458,1.22937711 15.1871843,2.81281566 C16.7706229,4.39625422 17.75,6.58375422 17.75,9 C17.75,11.4162458 16.7706229,13.6037458 15.1871843,15.1871843 C13.6037458,16.7706229 11.4162458,17.75 9,17.75 C6.58375422,17.75 4.39625422,16.7706229 2.81281566,15.1871843 C1.22937711,13.6037458 0.25,11.4162458 0.25,9 C0.25,6.58375422 1.22937711,4.39625422 2.81281566,2.81281566 C4.39625422,1.22937711 6.58375422,0.25 9,0.25 Z"></path>
                        <path stroke="url(#pattern-18)" d="M9,0.25 C11.4162458,0.25 13.6037458,1.22937711 15.1871843,2.81281566 C16.7706229,4.39625422 17.75,6.58375422 17.75,9 C17.75,11.4162458 16.7706229,13.6037458 15.1871843,15.1871843 C13.6037458,16.7706229 11.4162458,17.75 9,17.75 C6.58375422,17.75 4.39625422,16.7706229 2.81281566,15.1871843 C1.22937711,13.6037458 0.25,11.4162458 0.25,9 C0.25,6.58375422 1.22937711,4.39625422 2.81281566,2.81281566 C4.39625422,1.22937711 6.58375422,0.25 9,0.25 Z"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>