<script setup lang="ts">
const props = defineProps({
  tags: {
    type: Array,
    default: () => [],
  },
})
</script>

<template>
  <div class="ml-[8px] mt-[-2px]">
    <el-tag v-show="tags && tags.includes('position_skill')" type="info" class="mr-[8px]">
      岗位技能
    </el-tag>
    <el-tag v-show="tags && tags.includes('resume_related')" type="success" class="mr-[8px]">
      简历深挖
    </el-tag>
    <el-tag
      v-show="tags && tags.includes('keep_ask')" type="warning"
    >
      追问延伸
    </el-tag>
  </div>
</template>

<style scoped>

</style>