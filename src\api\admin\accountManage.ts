import request from '@/utils/http'

// 获取教师账号列表
export function getAccountList(data?: object) {
  return request({
    url: '/admin/teachers',
    data
  })
}

// 新增账号
export function addAccount(data?: object) {
  return request({
    url: '/admin/teachers',
    data,
    throttleTime: 1000
  })
}

// 修改账号
export function modifyAccount(data?: object) {
  return request({
    url: '/admin/teachers',
    data
  })
}

// 删除账号
export function delAccount(data?: object) {
  return request({
    url: '/admin/teachers',
    data
  })
}

// 重置账号密码
export function resetAccountPassword(data?: object) {
  return request({
    url: '/admin/teachers',
    data
  })
}

// 角色
export function adminRoles(data?: object) {
  return request({
    url: '/admin/roles',
    data
  })
}

// 权限
export function teacherpermissions(data?: object) {
  return request({
    url: '/admin/teacherpermissions',
    data
  })
}

// 查询部门
export function teacherdept(data?: object) {
  return request({
    url: '/admin/teacherdept',
    data
  })
}

// 查询角色
export function teacherroles(data?: object) {
  return request({
    url: 'admin/teacherroles',
    data
  })
}

// 获取子部门
export function subdepartment() {
  return request({
    // url: 'subdepartment'
    url: '/teacher/deptTree', 
    data: {}
  })
}

// 切换部门
export function switchdepartment (data: { dept_id: string }) {
  return request({
    url: '/teacher/switchdepartment',
    // url: '/teacher/update',
    data
  })
}
