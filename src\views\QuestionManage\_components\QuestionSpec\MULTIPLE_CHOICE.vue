<script setup lang="ts">
import { useQuestionManage, useQuestionOption } from '../composable';
const { formState, handleOptionBlur } = useQuestionManage()
const { addOption, delOption, delOptionWrap } = useQuestionOption()
defineProps<{
      showGenerateOptionModal: () => void,
}>()
</script>

<template>
      <a-form-item v-if="formState.type == 1" name="options" class="question-options">
            <a-checkbox-group v-model:value="formState.answer" style="max-width:100%;">
                  <template v-for="(item, index) in formState.options" :key="index">
                        <div class="item-option">
                              <a-checkbox :value="item.value">
                                    <span class="option-radio">{{ item.value }}</span>
                              </a-checkbox>
                              <a-textarea v-if="!formState.complicatedediting" v-model:value.trim="item.content"
                                    class="option-content" :auto-size="{ minRows: 1 }" placeholder="点击，编辑选项；选中即正确答案"
                                    @blur="handleOptionBlur" />
                              <div v-else class="editor-wrapper">
                                    <VueQuillEditor ref="optionEditorsRef" v-model:content="item.content"
                                          @blur="handleOptionBlur" />
                              </div>
                              <svg-icon class="del-icon" name="circle-del" width="16px" height="16px"
                                    @click.prevent="delOptionWrap($event, index)" />
                        </div>
                  </template>
            </a-checkbox-group>
            <div style="display: flex; align-items: center; margin-top: 16px;">
                  <div class="add-option-btn" @click="addOption">
                        <svg-icon name="plus" />
                        <span>添加选项</span>
                  </div>
                  <a-button class="common-ai-button"
                        style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;"
                        @click="showGenerateOptionModal">
                        <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;">
                        生成干扰项
                  </a-button>
                  <a-tooltip placement="right" overlay-class-name="light">
                        <template #title>
                              <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2" />
                  </a-tooltip>
            </div>
      </a-form-item>
      <div v-if="formState.type === 1" class="fill-blank-config">
          <div>
            <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
            <span>本题适用于“部分回答正确时可得分”</span>
            <a-tooltip placement="right">
              <template #title>
                <span>当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span>
              </template>
              <svg-icon class="common-info-icon" name="info2" />
            </a-tooltip>
          </div>
        </div>
</template>

<style lang="less" scoped>
@import url('../QuestionBody/scoped.less');
</style>

<style lang="less">
@import url('../QuestionBody/wrap.less');
</style>