<script setup lang="ts">
import JTagInput from '@/components/JTagInput.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Vue3Lottie } from 'vue3-lottie'
import { useRouter } from 'vue-router'
import animation from '../assets/Animation.json'
import { createPackageApi, getAIRecommend } from '../hooks/api'

const router = useRouter()
const pkg = inject<any>('pkg')
const dialogVisible = defineModel<boolean>('dialogVisible', { required: true })
const dialogLoading = ref(false)

const formRef = useTemplateRef<any>('form')
const formState = ref<{
  name: string
  path: string
  description: string
  subdirectoryList: string[]
}
>({
  name: '',
  path: '',
  description: '',
  subdirectoryList: [],
})
function clearForm() {
  formState.value = {
    name: '',
    path: '',
    description: '',
    subdirectoryList: [],
  }
  clearTempRecommedList()
}
function handleClose(tip = true) {
  if (tip && Object.entries(formState.value).some(([key, value]) => key !== 'subdirectoryList' && value)) {
    ElMessageBox.confirm('确认取消吗？ 取消后当前内容不保存？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      customStyle: {
        marginTop: '-500px',
      },
      type: 'warning',
    }).then(() => {
      clearForm()
      dialogVisible.value = false
    }).catch(() => {
      // 用户取消操作
    })
    return
  }
  if (!dialogLoading.value)
    dialogVisible.value = false
  clearForm()
}

const createLoading = ref(false)
async function createPackage() {
  formRef.value!.validate(async (valid: boolean) => {
    if (!valid) {
      return
    }

    try {
      createLoading.value = true
      await formRef.value!.validate()
      formState.value.path = `/${formState.value.name}`
      const routerItem = {
        name: formState.value.name,
        path: formState.value.path,
        id: '',
      }
      const result = await createPackageApi(formState.value)
      if (result) {
        ElMessage.success('题库创建成功')
      }
      createLoading.value = false
      handleClose(false)
      pkg.getPkgList()
      // 跳转到详情页
      routerItem.id = result
      goDetail(routerItem)
      clearForm()
      clearTempRecommedList()
    }
    catch (e) {
      createLoading.value = false
      handleClose(false)
      clearForm()
      clearTempRecommedList()
    }
  })
}

const tempRecommedList = ref([])
function clearTempRecommedList() {
  tempRecommedList.value = []
}
function addTag(tag: any) {
  // 限制50个标签
  if (formState.value.subdirectoryList.length >= 50) {
    ElMessage.warning('超出最大标签数量限制')
    return
  }
  if (formState.value.subdirectoryList.includes(tag)) {
    formState.value.subdirectoryList = formState.value.subdirectoryList.filter(item => item !== tag)
  }
  else {
    formState.value.subdirectoryList.push(tag)
    formState.value.subdirectoryList = [...formState.value.subdirectoryList]
  }
}

// const loadRecommend = ref(false)
async function recommendByAi() {
  if (!formState.value.name) {
    return formRef.value!.validate()
  }
  dialogLoading.value = true
  const result = await getAIRecommend({
    name: formState.value.name,
    path: `/${formState.value.name}`,
    description: formState.value.description,
  })
  tempRecommedList.value = result
  // 取result前五个元素
  formState.value.subdirectoryList = result.slice(0, 5)
  dialogLoading.value = false
}
function goDetail(createItem: any) {
  router.push({
    name: 'packageDetail',
    query: { 
      name: createItem.name,
      path: createItem.path,
      id: createItem.id,
    },
  }) 
}
const tipText = ['题库正在生成中', '请耐心等候正在根据考察范围为您生成科目树', 'AI生成的内容仅供参考，建议校对后使用']
const tipIndex = ref(0)
const intervalId = ref<any>('')
watch(() => createLoading.value, (val) => {
  if (!val) {
    clearInterval(intervalId as any)
  }
  else if (val) {
    setInterval(() => {
      if (tipIndex.value < 2) {
        tipIndex.value++
      }
      else {
        tipIndex.value = 0
      }
    }, 5000)
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <template #title>
      <div class="font-bloder text-[18px]">
        创建题库
      </div>
    </template>
    <el-form ref="form" label-width="80" class="mt-[20px] relative" :model="formState">
      <el-form-item prop="name" label="名称" :rules="[{ required: true, message: '请输入题库名称', trigger: 'blur' }]">
        <el-input
          v-model="formState.name"
          size="large"
          maxlength="16"
          placeholder="请输入题库名称"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="简介">
        <el-input
          v-model="formState.description"
          maxlength="50"
          size="large"
          placeholder="请输入简介"
          :style="{ width: '100%' }"
        />
      </el-form-item>
      <el-form-item label="考察范围">
        <JTagInput v-model="formState.subdirectoryList" :hide-tag-margin-top="false" :max-tags="50" />
        <a-button style="font-size: 14px; border-radius: 8px;padding: 0 8px; height: 32px;margin-top: 10px;" class="flex items-center" @click="recommendByAi">
          <template #icon>
            <img v-show="!dialogLoading" src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
            <img v-show="dialogLoading" src="../assets/loading.svg" style="margin-right: 4px;width: 14px;" class="animate-spin">
          </template>
          智能推荐 
        </a-button>
        <a-skeleton v-show="dialogLoading" :title="false" :paragraph="{ rows: 3 }" style="margin-top: 10px;" /> 
        <div class="w-full mt-[12px]">
          <el-tag v-for="item in tempRecommedList" :key="item" :type="formState.subdirectoryList.includes(item) ? 'primary' : 'info'" class="mr-[8px] mb-[8px] cursor-pointer" @click="addTag(item)">
            {{ item }}
          </el-tag>
        </div>
      </el-form-item>
      <div class="w-full flex justify-center">
        <a-button class="mr-[16px]" type="primary" @click="createPackage">
          确定
        </a-button>
        <a-button @click="handleClose as any">
          取消
        </a-button>
      </div>
    </el-form>
    <div v-show="createLoading" class="w-full h-full flex-wrap bg-red absolute bg-[#fff] opacity-[0.9] p-[56px] top-0 left-0 z-[99] flex items-center justify-center content-center">
      <Vue3Lottie :animation-data="animation" :auto-play="true" :loop="true" :height="200" :no-margin="true" />
      <div class="text-[14px] font-bold text-[rgba(0,0,0,0.85)]">
        {{ tipText[tipIndex] }}
      </div>
      <!-- <div class="text-start w-full">当前创建进度</div>
      <a-progress :percent="30" />
      <div>AI生成的内容仅供参考，建议校对后使用</div> -->
    </div>
  </el-dialog>     
</template>

<style scoped lang="scss">
.border{
  border: 1px solid #5478ee;
}
</style>