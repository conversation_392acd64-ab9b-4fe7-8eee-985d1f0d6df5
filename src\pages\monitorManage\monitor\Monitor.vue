<template>
    <MonitorLive v-if="multiScreen" :filter="filter" :timeStamp="timeStamp" :updateFrequency="updateFrequency" />
    <div class="stu-monitor-container" v-if="!multiScreen">
        <h3 class="stu-monitor-title">
            <span style="color: rgba(0, 0, 0, 0.45)">
                <span @click="router.back">监考中心/</span>
            </span>
            <span v-if="(route.query.paperIds as string).includes(',')">合并监考</span>
            <span v-else>监考《{{ paperInfos[0]?.name }}》</span>
        </h3>
        <!-- <p>isPending: {{ isPending }}; remainTime: {{ remainTime / 1000 }}秒</p> -->
        <div class="stu-monitor">
            <template v-if="!paperInfos.length || !paperInfos.every(item => item.isEnd)">
                <div class="stu-monitor-search">
                    <div style="display: flex;">
                        <a-input-search v-model:value.trim="searchContent" placeholder="请输入考生姓名" allow-clear @blur="handleSearch" @search="handleSearch" />
                        <div class="switch-monitor" v-if="isShowLiveMonitorSwitch">
                            <span style="display: flex;">实时监控
                                <a-tooltip placement="right" overlayClassName="light">
                                    <template #title>
                                        <span>打开后可实时查看监控画面</span>
                                    </template>
                                    <svg-icon class="common-info-icon" name="info2" style="width: 16px; height: 16px;"></svg-icon>
                                </a-tooltip>
                            </span>
                            <a-switch v-model:checked="isLiveMonitor" checkedValue="1" unCheckedValue="0" />
                        </div>
                    </div>
                    <div id="teleported-monitor-btnbox"></div>
                </div>
                <ul class="paper-filter" v-if="(route.query.paperIds as string).includes(',')">
                    <li v-for="item in paperInfos" @click="clickCheckBox(item)">
                        <a-checkbox @change="handlePaperChange"  v-model:checked="item.checked" :disabled="item.isEnd">{{ item.name }}</a-checkbox>
                    </li>
                    <a-tooltip placement="right" overlayClassName="light">
                        <template #title>
                            <span>显示当前选中的考试场次，可勾选考试名称查看对应考生监控</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2" style="width: 16px; height: 16px;"></svg-icon>
                    </a-tooltip>
                </ul>
                <div class="stu-monitor-bar">
                    <MonitorStatusStatistics :paperIds="filter.paperIds" :updateFrequency="updateFrequency"></MonitorStatusStatistics>
                    <div class="stu-monitor-status">
                        <div>
                            <svg-icon name="icon_wait" width="16px" height="16px" />
                            <span>等待中</span>
                        </div>
                        <div>
                            <svg-icon name="icon_ing" width="16px" height="16px" />
                            <span>考试中</span>
                        </div>
                        <div>
                            <svg-icon name="icon_err" width="16px" height="16px" />
                            <span>行为异常</span>
                        </div>
                        <div>
                            <svg-icon name="icon_end" width="16px" height="16px" />
                            <span>已交卷</span>
                        </div>
                    </div>
                </div>
                <MonitorLive v-if="isLiveMonitor === '1'" :filter="filter" :timeStamp="timeStamp" :updateFrequency="updateFrequency" />
                <MonitorNoLive v-if="isLiveMonitor === '0'" :filter="filter" :updateFrequency="updateFrequency" />
            </template>
            <div class="end-box" v-else>
                <img src="@/assets/images/the-end.png" alt="">
                <span>本场考试已结束</span>
            </div>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { ref, computed, reactive, Ref, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MonitorStatusStatistics from './MonitorStatusStatistics.vue'
import MonitorLive from './MonitorLive.vue'
import MonitorNoLive from './MonitorNoLive.vue'
import { useRouteQuery } from '@vueuse/router'
import { getPaperNameByIds } from '@/api/admin/paperManage'
import { useDebounceFn, useLocalStorage, useTimeoutFn } from '@vueuse/core'
import moment from 'moment'
import { message } from 'ant-design-vue'
import { log } from 'console'

const route = useRoute()
const router = useRouter()
console.log('this is route.query',route.query)
const multiScreen = ref(false)
if(route.query.multi && route.query.multi == '1'){
    multiScreen.value = true
}
const updateFrequency = 3
/** 本次进入监考界面的时间戳，来区分localstorage的key，避免一对多监考数据混乱的情况 */
const timeStamp = moment().format('YYYYMMDDHHmmss')

/** 试卷ids（合并的考试用,分隔） */
// const paperIds = route.query.paperIds as string
const paperIds = useLocalStorage('monitorlive__paperIds__' + timeStamp, route.query.paperIds)

/** 是否实时监控切换 */
const isLiveMonitor = useRouteQuery('isLiveMonitor') as Ref<string | null>
const isShowLiveMonitorSwitch = computed(() => paperInfos.value.some(item => item.computermonitor == '1' || item.phonemonitor == '1'))

// 搜索考生姓名
const searchContent = useLocalStorage('monitorlive__searchContent__' + timeStamp, '')

// 过滤条件
const filter = reactive({
    username: '',
    paperIds: ''
})
function handleSearch() {
    filter.username = searchContent.value
}

function handlePaperChange() {
    paperIds.value = filter.paperIds = paperInfos.value.filter(i => i.checked).map(i => i.id).join(',')
}

function clickCheckBox(item){
    if (item.isEnd){
        message.info('该场考试已结束');
    }
}

// 根据ids获取试卷信息
const paperInfos = ref<{
    id: string
    name: string
    endTime: string
    checked?: boolean
    isEnd?: boolean
    [key: string]: any
}[]>([])
async function getPaperInfos() {
    try {
        let res = await getPaperNameByIds({ ids: route.query.paperIds ? (route.query.paperIds as any).split(',') : []}) as any
        res.forEach((item: any) => {
            item.checked = true
            item.isEnd = false
        })
        if (!res?.length) return message.error('服务异常，请刷新重试')
        paperInfos.value = res
        filter.paperIds = paperInfos.value.map(i => i.id).join(',')
        // paperInfos.value = [
        //     { id: '1', name: '1233', endTime: '2024-01-10 17:05:30' },
        //     { id: '2', name: '1235', endTime: '2024-01-10 17:05:50' },
        //     { id: '3', name: '1236', endTime: '2024-01-10 17:06:10' },
        //     { id: '4', name: '1237', endTime: '2024-01-10 17:06:15' },
        // ]
        setClosestRemainTime()
    } catch (error) {
        console.log(error)
    }
}
getPaperInfos()


// 全部考试结束则展示当前考试已结束
const remainTime = ref()
const { start, isPending, stop } = useTimeoutFn(setClosestRemainTime, () => remainTime.value, {
    immediate: false
})

/**
 * 设置离最近的考试的剩余时间
 */
function setClosestRemainTime() {
    if (!paperInfos.value?.length) return
    paperInfos.value.forEach(item => {
        item.isEnd = moment(item.endTime).isSameOrBefore(moment())
        if (item.isEnd) { 
            item.checked = false
        }
    })
    let notEndPaperList = paperInfos.value.filter(item => !item.isEnd)
    if (!notEndPaperList.length) {
        paperIds.value = filter.paperIds = ''
        return
    }
    let endTimeList = notEndPaperList.map(item => +new Date(item.endTime))
    let closestEndTimeStamp: any = Math.min(...endTimeList)
    remainTime.value = closestEndTimeStamp - Date.now()
    start()
    handlePaperChange()
}

/** 清理一天前的monitorlive__paperIds__和monitorlive__searchContent__ */
function clearOldMonitorLiveItemsWithMoment() {
    // 获取一天前的时间
    const oneDayAgo = moment().subtract(2, 'hours');

    // 遍历localStorage中的所有项
    for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        // 检查键是否符合特定的前缀和时间戳格式
        if (key?.startsWith('monitorlive__paperIds__') || key?.startsWith('monitorlive__searchContent__')) {
            try {
                const timestampPart = key.split('__')[2];
                if (timestampPart && timestampPart.length === 14) {
                    // 使用moment解析时间戳
                    const timestampMoment = moment(timestampPart, "YYYYMMDDHHmmss");
                    
                    // 检查时间戳是否是一天前的
                    if (timestampMoment.isBefore(oneDayAgo)) {
                        localStorage.removeItem(key);
                        console.log('Removed:', key);
                    }
                }
            } catch (error) {
                console.log(error)
            }
        }
    }
}
clearOldMonitorLiveItemsWithMoment();


</script>
  
<style lang="less" scoped>
.paper-filter {
    margin-top: 16px;

    li {
        display: inline-block;
        padding: 5px 8px;
        background: rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        margin-right: 8px;
        user-select: none;
        margin-bottom: 8px;
    }
}

.stu-monitor-container {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 0 20px 0 20px;
}

.stu-monitor-title {
    line-height: 48px;
    font-size: 14px;
    font-weight: 600;
}

.stu-monitor {
    display: flex;
    flex-direction: column;
    min-height: 0;
    min-width: 1200px;
    flex: 1;
    position: relative;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;
    .end-box {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.45);
        user-select: none;
    }
}

.stu-monitor-search {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .status-select {
        width: 200px;
        margin: 0 16px;

        :deep(.ant-select-selector) {
            border-radius: 8px;
            font-size: 12px;
        }
    }

    .switch-monitor {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-left: 16px;

        span {
            white-space: nowrap;
            margin-right: 10px;
        }
    }
}

.stu-monitor-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
}

.stu-monitor-status {
    display: flex;
    align-items: center;

    div {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        margin-right: 16px;

        span {
            margin-left: 8px;
        }
    }
}

.monitor-live-photos {
    height: calc(100% - 140px);
    overflow: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    padding-right: 10px;

    // flex-wrap: wrap;
    .live-item {
        position: relative;
        // width: calc(25% - 16px);
        // margin-right: 16px;
        min-height: 200px;

        // max-height: 258px;
        // margin-bottom: 10px;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .temp-img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            // min-width: 440px;
            // min-height: 258px;
            background: #f3f2f2;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        .live-img-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 28px;
            padding-left: 12px;
            background: #ebecec;
            font-size: 14px;

            .right-icon {
                margin-right: 10px;
                margin-top: 2px;
            }

            .expand-icon {
                margin-left: 10px;
                cursor: pointer;
            }
        }
    }
}

.expand-img-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;

    img {
        width: 600px;
        height: 300px;
    }

    .close-icon {
        position: absolute;
        top: 24px;
        right: 24px;
        cursor: pointer;
    }
}

.pagination-style {
    margin-top: 20px;
    width: 100%;
    text-align: center;
}
</style>
  