<template>
    <div class="pwd-strenth-panel">
        <div class="rule-item" v-for="item in rules">
            <span class="check-box" :class="{ checked: item.checked }"><check-outlined style="font-size: 12px;" /></span>
            <span class="rule-text">{{ item.text }}</span>
        </div>

        <div class="strenth-progress">
            <div class="label">安全程度:</div>
            <div class="dot active"></div>
            <template v-for="(item, index) in rules">
                <div class="line" :class="{ active: index < okNum }"></div>
                <div class="dot" :class="{ active: index < okNum }"></div>
            </template>
            <div class="strenth-desc">{{ strenthDesc }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watchEffect } from 'vue'
import { checkPwdLength, checkNoASCII, checkPwdDiversity } from '@/utils/validate'
import { CheckOutlined } from '@ant-design/icons-vue'


const props = defineProps<{
    password: string
}>()

interface Rule {
    text: string
    validator: (value: string) => boolean
    checked?: boolean
}

const rules: Rule[] = reactive([
    {
        text: '8~26个字符',
        validator: checkPwdLength,
        checked: true
    },
    {
        text: '不能包含特殊字符',
        validator: (value) => !checkNoASCII(value)
    },
    {
        text: '密码只能包含大写字母、小写字母、数字和字符且至少包含四种字符中的三种',
        validator: checkPwdDiversity
    }
])

enum Strenth {
    '弱',
    '一般',
    '强',
    '极强'
}
const strenthDesc = ref(Strenth[0])

const okNum = ref(0)


watchEffect(() => {
    rules.forEach(item => {
        item.checked = item.validator(props.password)
    })

    okNum.value = rules.reduce((prev, curr) => {
        if (curr.checked) {
            return prev + 1
        }
        return prev
    }, 0)

    strenthDesc.value = Strenth[okNum.value]
})


</script>

<style lang="less" scoped>
@activeColor: #50D4AB;
@inactiveColor: #a8a7a7;

.pwd-strenth-panel {
    padding: 10px;
    display: inline-block;
    max-width: 380px;

    .rule-item {
        margin-bottom: 6px;
        display: flex;
        align-items: flex-start;


        .check-box {
            background-color: @inactiveColor;
            color: #fff;
            margin-right: 8px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            top: 2px;

            &.checked {
                background-color: @activeColor;
            }
        }

        .rule-text {
            flex: 1;
            min-width: 0;
        }
    }

    .strenth-progress {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .label {
            margin-right: 4px;
        }

        .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: @inactiveColor;

            &.active {
                background-color: @activeColor;
            }
        }

        .line {
            width: 58px;
            height: 2px;
            background-color: @inactiveColor;

            &.active {
                background-color: @activeColor;
            }
        }

        .strenth-desc {
            margin-left: 12px;
            width: 3em;
        }
    }
}
</style>