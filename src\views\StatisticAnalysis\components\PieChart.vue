<template>
  <div ref="myRef" :style="{ width, height }" class="pie-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: '题型占比'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 20)
})

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  setChartOption()
  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}
const setChartOption = () => {
  Chart.value.setOption({
    title: {
      text: props.name,
      left: 'center',
      textStyle: {
        fontSize: 15,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      textStyle: {
        width: 120,
        overflow: 'break'
      }
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: chartData.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}

const chartData = ref<any>([])
watch(
  () => props.data,
  (val) => {
    chartData.value = val
    setTimeout(() => {
      setChartOption()
    }, 200)
  },
  {
    immediate: true
  }
)

watch(
  () => props.width,
  (val) => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  (val) => {
    Chart.value.resize()
  }
)
</script>

<style lang="less" scoped>
.pie-chart {
  padding: 16px;
  background: #fff;
}
</style>
