<template>
  <a-form class="question-form" :model="formState" :rules="rules" autocomplete="off" :colon="false" labelAlign="left"
    @finish="handleSubmit">
    <a-row>
      <a-col :span="12">
        <a-form-item label="题目类型" name="type">
          <a-select v-model:value="formState.type" placeholder="请选择题目类型" :disabled="!typeSwitch">
            <a-select-option v-for="item in QUESTIONS" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="分值" name="score">
          <a-input-number :disabled="[QuestionEnum['填空题'], QuestionEnum['问答题']].includes(formState.type)" v-model:value="formState.score" :min="1" :max="100" :precision="0" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-form-item label="题干内容" name="body">
      <a-textarea ref="bodyInputRef" v-model:value="formState.body" :rows="4" placeholder="点击编辑" />
      <div v-if="formState.type === QuestionEnum['填空题']" class="body-tip">
        <svg-icon name="tip" class="tip-icon" />
        <span>点击</span>
        <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
        <span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
    <SelectForm v-model:formState="formState"
      v-if="[QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['判断题']].includes(formState.type)"></SelectForm>
    <FillblankForm v-model:formState="formState" v-else-if="formState.type === QuestionEnum['填空题']"></FillblankForm>
    <QaFrom v-model:formState="formState" v-else-if="formState.type === QuestionEnum['问答题']"></QaFrom>
    <a-button type="primary" html-type="submit">保存</a-button>
    <a-button style="margin-left: 8px;" @click="handleCancel">取消</a-button>
  </a-form>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue';
import { useState } from '@/hooks';
import { QUESTIONS } from '@/config/constants';
import SelectForm from './SelectForm.vue';
import FillblankForm from './FillblankForm.vue';
import QaFrom from './QaFrom.vue';
import { QuestionEnum } from '@/models/questionModel';
import type { Rule } from 'ant-design-vue/es/form';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { checkBody, checkOptions, checkQuestionForm, checkScore } from '@/utils/validate';

interface FormState {
  type: number
  category?: string
  score: number
  body: string
  answer: string | any[]
  /** 评分依据 */
  scorebasis?: string
  options: { value: string, content: string, id?: string }[]
}

const props = withDefaults(defineProps<{
  formState?: FormState
  /** 是否支持题型切换 */
  typeSwitch?: boolean
}>(), {
  typeSwitch: false
})

const emits = defineEmits<{
  (e: 'confirm', value: FormState): void
  (e: 'cancel'): void
}>();

const [formState, resetFormSate] = useState<FormState>({
  type: 0,
  category: undefined,
  score: 0,
  body: '',
  answer: '',
  scorebasis: '', // 评分依据
  options: [
    { value: 'A', content: '', id: _.uniqueId() },
    { value: 'B', content: '', id: _.uniqueId() },
    { value: 'C', content: '', id: _.uniqueId() },
    { value: 'D', content: '', id: _.uniqueId() }],
});

function formatFormState(formState: FormState) {
  if (formState.options?.length) {
    // 为每个选项添加唯一id，用于给组件作key
    formState.options = formState.options.map((item: any) => ({ ...item, id: _.uniqueId() }))
  }

  if (formState.type === QuestionEnum['多选题']) {
    if (!Array.isArray(formState.answer)) {
      // 答案要转成数组
      formState.answer = formState.answer.split('')
    }
  } else if ([QuestionEnum['填空题'], QuestionEnum['问答题']].includes(formState.type)) {
    if (!Array.isArray(formState.answer)) {
      // 答案要转成数组
      try {
        formState.answer = JSON.parse(formState.answer) as any[]
      } catch (error) {
        console.error('填空题答案解析失败，已置空')
        formState.answer = []
      }
    }
    formState.answer = formState.answer.map((item: any) => ({ ...item, id: _.uniqueId() }))
  }
}
// 如果父组件传入了formState，则使用父组件的formState
watch(() => props.formState, (newVal) => {
  if (newVal) {
    formatFormState(newVal)
    formState.value = _.cloneDeep(newVal)
  }
}, { immediate: true })

const rules: Record<string, Rule[]> = {
  body: [{ required: true, validator: checkBody(formState.value), trigger: 'blur' }],
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions(formState.value), trigger: 'blur' }],
};


// 插入空位
const bodyInputRef = ref()
function insetBlank() {
  let TEXT = '__ '
  let textareaEle = bodyInputRef.value.$el
  const startPos = textareaEle.selectionStart
  const endPos = textareaEle.selectionEnd
  let rawText = formState.value.body
  formState.value.body = rawText.substring(0, startPos) + TEXT + rawText.substring(endPos)
}


async function handleSubmit() {
  if (props.formState) {
    try {
      await checkQuestionForm(formState.value)
      emits('confirm', formState.value);
    } catch (error: any) {
      message.error(error.message)
    }
  } else {
    console.log('handleSubmit');
  }
}

function handleCancel() {
  emits('cancel');
}


</script>

<style lang="less" scoped>
.question-form {
  padding: 20px;
  :first-child{
    :nth-child(2){
      :deep(.ant-col){
        width: 86px;
      }
    }
  }
  :deep(.ant-form-item-label) {
    width: 100px;
  }

  .ant-select {
    width: 200px;
  }

  .body-tip {
    display: flex;
    align-items: center;
    background: #f1f4fe;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    margin-top: 8px;

    .tip-icon {
      margin: 0 8px;
    }
  }
}
</style>