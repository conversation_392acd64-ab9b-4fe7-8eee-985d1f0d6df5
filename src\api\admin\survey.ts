import request from '@/utils/http'

export function addquestionnaire(data?: object) {
    return request({
        url: '/questionnaire/create',
        data
    })
}
export function modifyquestionnaire(data?: object) {
    return request({
        url: '/questionnaire/update',
        data
    })
}
export function questionnaires(data?: object) {
    return request({
        url: '/questionnaire/page',
        data
    })
}
export function questionnairedetail(data?: { id: string }) {
    return request({
        url: '/questionnaire/get',
        data
    })
}

export function publishquestionnaire(data?: { id: string }) {
    return request({
        url: '/questionnaire/publish',
        data
    })
}

export function pauseorunpause(data?: { id: string, going: number }) {
    return request({
        url: '/questionnaire/update',
        data
    })
}

export function delquestionnaire(data?: { ids: string[] }) {
    return request({
        url: '/questionnaire/delete',
        data
    })
}

export function questionnairebyids(data?: { ids: string[] }) {
    return request({
        url: '/questionnaire/list',
        data
    })
}

export function submitquestionnaire(data?: any) {
    return request({
        url: '/submitquestionnaire',
        data
    })
}

export function answerquestionnairecontent(data?: { token: string }) {
    return request({
        url: '/questionnaire/get',
        data
    })
}
export function questionnairereports(data?: any) {
    return request({
        url: '/questionnaire/detail/report',
        data
    })
}
export function markanswerquestionnaire(data?: any) {
    return request({
        url: '/questionnaire/detail/update',
        data
    })
}

export function questionnairedatastatistics(data?: any) {
    return request({
        url: '/questionnaire/detail/get',
        data
    })
}

export function downloadquestionnairedata(data?: any) {
    return request({
        url: '/questionnaire/detail/export',
        data,
        method: 'get',
        responseType: 'blob'
    })
}