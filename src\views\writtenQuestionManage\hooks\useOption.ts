import { message } from 'ant-design-vue'
import { lettersAlpht } from '../data'
import { QuestionEnum } from '@/models/questionModel'

export default (formState: any) => {
  // 增加选项
  const addOption = () => {
    if (formState.value.options.length >= 11) return message.warn('已达最多选项个数')
    formState.value.options.push({
      content: '',
      value: ''
    })
    formState.value.options.forEach((item: any, index: number) => {
      item.value = lettersAlpht[index]
    })
  }

  // 删除选项
  const delOption = (e: any, index: number) => {
    e.preventDefault()
    e.stopPropagation()
    if (formState.value.type === QuestionEnum['单选题'] && formState.value.options.length <= 2) {
      return message.warning('至少保留2个选项')
    }
    if ([QuestionEnum['多选题'], QuestionEnum['排序题']].includes(formState.value.type) && formState.value.options.length <= 3) {
      return message.warning('至少保留3个选项')
    }
    formState.value.options.splice(index, 1)

    // 重新给每一个选项value赋值
    formState.value.options.forEach((option: any, i: number) => {
      option.value = lettersAlpht[i]
    })

    // 处理已选答案
    if (!formState.value.answer?.length) return; // 如果没有答案，则直接返回
    if (formState.value.type === QuestionEnum['单选题']) {
      if (lettersAlpht[index] === formState.value.answer) {
        // 如果删除的是答案，需要清空答案
        formState.value.answer = ''
      } else {
        // 如果删除的在答案之前，答案需要减1
        const answerIndex = lettersAlpht.indexOf(formState.value.answer)
        if (answerIndex > index) {
          formState.value.answer = lettersAlpht[answerIndex - 1]
        }
      }
    } else if (formState.value.type === QuestionEnum['多选题']) {
      let newAnswer: string[] = []
      formState.value.answer.forEach((item: string) => {
        let answerIndex = lettersAlpht.indexOf(item)
        if (answerIndex > index) {
          newAnswer.push(lettersAlpht[answerIndex - 1])
        } else if (answerIndex < index) {
          newAnswer.push(lettersAlpht[answerIndex])
        }
      })
      formState.value.answer = newAnswer
    }
  }
  return {
    addOption,
    delOption
  }
}
