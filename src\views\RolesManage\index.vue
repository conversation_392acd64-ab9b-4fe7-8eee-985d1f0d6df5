<template>
  <div class="role-manage-container">
    <h3 class="role-manage-title">角色管理</h3>
    <div class="role-manage">
      <div class="role-manage-header">
        <div class="header-left">
          <a-input-search
            class="search-wrapper"
            v-model:value.trim="searchContent"
            placeholder="请输入关键词"
            @search="getSearchData"
            @keyup.enter="getSearchData"
          />
        </div>
        <div class="header-right">
          <span class="add-btn" @click="addRole">新增角色</span>
        </div>
      </div>

      <div class="role-manage-list">
        <template v-if="roleList?.length" v-for="item in roleList" :key="item.id">
          <div class="item">
            <div class="capital">{{ item.name[0] }}</div>
            <div class="content">
              <h3 class="role-name">{{ item.name }}</h3>
              <div>
                <template v-for="tag in item.permission" :key="tag">
                  <span class="tag" :style="{ color: tag.color }">{{ tag.name }}</span>
                </template>
              </div>
            </div>
            <div class="operate-btn">
              <svg-icon
                name="edit"
                width="18px"
                height="18px"
                class="edit-icon"
                @click="editRole(item)"
              />
              <img
                src="@/assets/icons/svg/delete.svg"
                class="delte-icon"
                @click="delRole(item.id)"
              />
            </div>
          </div>
        </template>
        <div v-else style="height:100%;display: flex;align-items:center;justify-content:center;">
          <img src="@/assets/images/nodata.png" />
        </div>
      </div>

      <custom-drawer
        class="custom-drawer"
        :drawer-visible="drawerVisible"
        @close-drawer="closeDrawer"
        :keyboard="false"
        :maskClosable="false"
      >
        <template #header>
          <div class="header">
            <span class="title">{{ id ? '编辑角色' : '新增角色' }}</span>
            <svg-icon
              class="close-icon"
              name="close"
              width="36px"
              height="36px"
              @click="closeDrawer"
            />
          </div>
        </template>
        <template #footer>
          <div class="footer">
            <span class="submit" @click="saveRole">保存</span>
            <span class="cancel" @click="closeDrawer">取消</span>
          </div>
        </template>
        <div class="content" v-if="drawerVisible">
          <a-form
            class="form"
            ref="formStateRef"
            :model="formState"
            :rules="rules"
            labelAlign="left"
            :colon="false"
            :hideRequiredMark="true"
          >
            <a-form-item label="角色名称" name="name">
              <a-input v-model:value.trim="formState.name"></a-input>
            </a-form-item>
          </a-form>

          <div class="permission-list">
            <dl class="item">
              <dt><a-checkbox v-model:checked="quesmag" />题库管理</dt>
              <dd>
                <a-checkbox
                  :disabled="permissionList.quesmag.write"
                  v-model:checked="permissionList.quesmag.read"
                /><span class="name">查看</span><span class="desc">仅支持查看题目信息</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.quesmag.write" /><span class="name"
                  >编辑</span
                ><span class="desc">支持查看、新增、修改和删除题目信息</span>
              </dd>
            </dl>
            <dl class="item">
              <dt><a-checkbox v-model:checked="papermsg" />考试管理</dt>
              <dd>
                <a-checkbox
                  :disabled="
                    permissionList.papermag.write ||
                    permissionList.papermag.proctor ||
                    permissionList.papermag.scoring
                  "
                  v-model:checked="permissionList.papermag.read"
                /><span class="name">查看</span><span class="desc">仅支持查看考试信息</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.papermag.write" /><span class="name"
                  >编辑</span
                ><span class="desc">支持新增考试、关联考生、创建考试邀请码和延长考试功能</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.papermag.proctor" /><span class="name"
                  >监考</span
                ><span class="desc">允许对指定考试进行监考</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.papermag.scoring" /><span class="name"
                  >人工阅卷</span
                ><span class="desc">允许被指派为阅卷人</span>
              </dd>
            </dl>
            <dl class="item">
              <dt><a-checkbox v-model:checked="stumag" />考生管理</dt>
              <dd>
                <a-checkbox
                  :disabled="permissionList.stumag.write"
                  v-model:checked="permissionList.stumag.read"
                /><span class="name">查看</span><span class="desc">仅支持查看考生信息</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.stumag.write" /><span class="name"
                  >编辑</span
                ><span class="desc">支持查看、修改和删除考生信息</span>
              </dd>
            </dl>
            <dl class="item">
              <dt><a-checkbox v-model:checked="analysismsg" />统计分析</dt>
              <dd>
                <a-checkbox v-model:checked="permissionList.analysis.ques" /><span class="name"
                  >题库分析</span
                ><span class="desc">支持查看题库相关图表</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.analysis.paper" /><span class="name"
                  >考试分析</span
                ><span class="desc">支持查看考试相关图表</span>
              </dd>
              <dd>
                <a-checkbox v-model:checked="permissionList.analysis.stu" /><span class="name"
                  >考生分析</span
                ><span class="desc">支持查看考生相关图表</span>
              </dd>
            </dl>
          </div>
        </div>
      </custom-drawer>
    </div>
  </div>
</template>
<script setup lang="ts">
import CustomDrawer from '@/components/CustomDrawer.vue'
import { onMounted, ref, computed, createVNode, watch } from 'vue'
import { adminRoles } from '@/api/admin/accountManage'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
// 快速搜索
const searchContent = ref('')
const getSearchData = () => {
  getAllRoles()
}

const addRole = () => {
  id.value = undefined
  formState.value.name = ''
  permissionList.value = {
    quesmag: {
      read: false,
      write: false
    },
    papermag: {
      proctor: false,
      read: false,
      scoring: false,
      write: false
    },
    stumag: {
      read: false,
      write: false
    },
    analysis: {
      paper: false,
      ques: false,
      stu: false
    }
  }
  drawerVisible.value = true
}

interface IRole {
  id?: string
  name: string
  quesmag: { read: boolean; write: boolean }
  papermag: { proctor: boolean; read: boolean; write: boolean; scoring: boolean }
  stumag: { read: boolean; write: boolean }
  analysis: { paper: boolean; ques: boolean; stu: boolean }
  permission?: any
}
const roleList = ref<IRole[]>([])

const permission = {
  quesmag: {
    read: '查看题库',
    write: '查看/编辑题库'
  },
  papermag: {
    read: '查看试卷',
    write: '查看/编辑试卷',
    proctor: '监考',
    scoring: '人工阅卷'
  },
  stumag: {
    read: '查看考生',
    write: '查看/编辑考生'
  },
  analysis: {
    paper: '考试分析',
    ques: '题库分析',
    stu: '考生分析'
  }
}

// 右侧drawer
const drawerVisible = ref(false)
const closeDrawer = () => {
  drawerVisible.value = false
}

const formState = ref({
  name: ''
})
const rules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
}
const permissionList = ref({
  quesmag: {
    read: false,
    write: false
  },
  papermag: {
    proctor: false,
    read: false,
    scoring: false,
    write: false
  },
  stumag: {
    read: false,
    write: false
  },
  analysis: {
    paper: false,
    ques: false,
    stu: false
  }
})

const quesmag = computed({
  get() {
    return permissionList.value.quesmag.read && permissionList.value.quesmag.write
  },
  set(value) {
    if (value) {
      permissionList.value.quesmag.read = true
      permissionList.value.quesmag.write = true
    } else {
      permissionList.value.quesmag.read = false
      permissionList.value.quesmag.write = false
    }
  }
})
const papermsg = computed({
  get() {
    return (
      permissionList.value.papermag.read &&
      permissionList.value.papermag.write &&
      permissionList.value.papermag.proctor &&
      permissionList.value.papermag.scoring
    )
  },
  set(value) {
    if (value) {
      permissionList.value.papermag.read = true
      permissionList.value.papermag.write = true
      permissionList.value.papermag.proctor = true
      permissionList.value.papermag.scoring = true
    } else {
      permissionList.value.papermag.read = false
      permissionList.value.papermag.write = false
      permissionList.value.papermag.proctor = false
      permissionList.value.papermag.scoring = false
    }
  }
})

const stumag = computed({
  get() {
    return permissionList.value.stumag.read && permissionList.value.stumag.write
  },
  set(value) {
    if (value) {
      permissionList.value.stumag.read = true
      permissionList.value.stumag.write = true
    } else {
      permissionList.value.stumag.read = false
      permissionList.value.stumag.write = false
    }
  }
})

const analysismsg = computed({
  get() {
    return (
      permissionList.value.analysis.paper &&
      permissionList.value.analysis.ques &&
      permissionList.value.analysis.stu
    )
  },
  set(value) {
    if (value) {
      permissionList.value.analysis.paper = true
      permissionList.value.analysis.ques = true
      permissionList.value.analysis.stu = true
    } else {
      permissionList.value.analysis.paper = false
      permissionList.value.analysis.ques = false
      permissionList.value.analysis.stu = false
    }
  }
})

const id = ref()
const formStateRef = ref()
const saveRole = () => {
  formStateRef.value.validate().then(() => {
    adminRoles({
      action: id.value ? 'modify' : 'add',
      id: id.value ? id.value : undefined,
      roles: {
        ...permissionList.value,
        name: formState.value.name
      }
    }).then(() => {
      message.success(id.value ? '编辑角色成功!' : '新增角色成功!')
      drawerVisible.value = false
      getAllRoles()
    })
  })
}

const editRole = (role: any) => {
  formState.value.name = role.name
  id.value = role.id
  permissionList.value = {
    quesmag: role.quesmag,
    papermag: role.papermag,
    stumag: role.stumag,
    analysis: role.analysis
  }
  drawerVisible.value = true
}

const delRole = (id: any) => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确认删除该角色?',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      adminRoles({ action: 'del', id }).then(() => {
        message.success('角色删除成功!')
        getAllRoles()
      })
    }
  })
}

// 获取所有角色列表
const getAllRoles = () => {
  adminRoles({ action: 'query', condition: searchContent.value }).then((res: any) => {
    roleList.value = res
    roleList.value.forEach((item: any) => {
      item.permission = []
      Object.keys(item.quesmag).forEach((key) => {
        if (item.quesmag[key]) {
          item.permission.push({ name: permission['quesmag'][key], color: '#2F8C00' })
          if (key === 'write') {
            const index = item.permission.findIndex((o: any) => o.name === '查看题库')
            item.permission.splice(index, 1)
          }
        }
      })
      Object.keys(item.papermag).forEach((key) => {
        if (item.papermag[key]) {
          item.permission.push({ name: permission['papermag'][key], color: '#6733B1' })
          if (key === 'write') {
            const index = item.permission.findIndex((o: any) => o.name === '查看试卷')
            item.permission.splice(index, 1)
          }
        }
      })
      Object.keys(item.stumag).forEach((key) => {
        if (item.stumag[key]) {
          item.permission.push({ name: permission['stumag'][key], color: '#D96E00' })
          if (key === 'write') {
            const index = item.permission.findIndex((o: any) => o.name === '查看考生')
            item.permission.splice(index, 1)
          }
        }
      })
      Object.keys(item.analysis).forEach((key) => {
        if (item.analysis[key]) {
          item.permission.push({ name: permission['analysis'][key], color: '#626262' })
        }
      })
    })
  })
}

watch(
  permissionList,
  (val) => {
    if (val.quesmag.write) {
      permissionList.value.quesmag.read = true
    }
    if (val.papermag.write || val.papermag.scoring || val.papermag.proctor) {
      permissionList.value.papermag.read = true
    }
    if (val.stumag.write) {
      permissionList.value.stumag.read = true
    }
  },
  {
    deep: true
  }
)

onMounted(() => {
  getAllRoles()
})
</script>

<style lang="less" scoped>
.role-manage-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px 20px 20px;
}
.role-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}
.role-manage {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 10px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  .role-manage-header {
    margin: 8px 15px;
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;
    align-items: center;
    .search-wrapper {
      --antd-wave-shadow-color: #fff !important;
      width: 240px;
      line-height: 32px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;

      :deep(.ant-input) {
        font-size: 13px;
        box-shadow: none;
        border: none;
        border-radius: 8px;
        line-height: 26px;
      }
      :deep(.ant-input-group-addon) {
        border-radius: 8px;
      }
      :deep(.ant-input-search-button) {
        border-radius: 8px !important;
        box-shadow: none !important;
        border: none !important;
      }
    }
    .header-right {
      display: flex;
      align-items: center;
      font-size: 14px;
      span {
        width: 88px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 8px;
        font-size: 14px;
        margin-left: 8px;
        cursor: pointer;
      }
      .add-btn {
        background: #5478ee;
        color: #fff;
      }
      .import-btn,
      .del-btn {
        border: 1px solid rgba(0, 0, 0, 0.15);
      }
    }
  }
  .role-manage-list {
    padding: 0 12px 0 16px;
    flex: 1;
    min-height: 0;
    overflow: auto;
    .item {
      &:hover {
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
      }
      transition: all 0.25s;
      display: flex;
      align-items: center;
      height: 96px;
      padding: 24px;
      border: 1px solid #dddddd;
      border-radius: 8px;
      margin-bottom: 16px;
      .capital {
        width: 48px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        background: #f1f4fe;
        border-radius: 8px;
        font-size: 24px;
        color: #5478ee;
        margin-right: 24px;
      }
      .content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 48px;
        margin-right: auto;
        .role-name {
          font-size: 16px;
          color: #252b3a;
          font-weight: bold;
        }
        .tag {
          margin-right: 8px;
          font-size: 12px;
          padding: 0 4px;
          border-radius: 4px;
          background: #f5f5f5;
        }
      }
      .delte-icon {
        position: relative;
        bottom: 5px;
        margin-left: 12px;
        cursor: pointer;
      }
      .edit-icon {
        cursor: pointer;
      }
    }
  }
}
.custom-drawer {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    .title {
      font-size: 20px;
      padding-left: 8px;
      font-weight: bold;
    }
    .close-icon {
      cursor: pointer;
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    z-index: 2;
    background: #fff;
    padding: 24px 0;
    bottom: 0;
    left: 32px;
    right: 32px;
    border-top: 1px solid #e8e8e8;
    span {
      width: 60px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: rgba(0, 0, 0, 0.65);
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      cursor: pointer;
    }
    .submit {
      background-color: #5478ee;
      color: #fff;
    }
    .cancel {
      margin-left: 8px;
    }
  }
  .content {
    padding: 0 8px;
    .form {
      :deep(.ant-form-item-label > label) {
        font-size: 12px;
        color: #626262;
      }
      :deep(.ant-col) {
        width: 98px;
      }
      .ant-input {
        width: 300px;
        height: 32px;
        border-radius: 8px;
        font-size: 12px;
        color: #181818;
      }
    }
  }
  .permission-list {
    margin-bottom: 100px;
    .item {
      margin-bottom: 24px;
      dt {
        margin-bottom: 16px;
        font-size: 12px;
      }
      dd {
        margin-bottom: 8px;
        padding-left: 24px;
        font-size: 12px;
        .name {
          display: inline-block;
          width: 58px;
          color: #262626;
        }
        .desc {
          color: #aeaeae;
        }
      }
      .ant-checkbox-wrapper {
        margin-right: 8px;
      }
    }
  }
}
</style>
