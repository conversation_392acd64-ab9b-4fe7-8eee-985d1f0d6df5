interface SurveyQuestionType {
    /** 题目序号 */
    number?: number

    id: string
    type: number
    body: string
    required: boolean

    // 单选/多选
    options?: {
        id: string
        content: string
        value: number | string
        other?: boolean
    }[]
    other?: boolean
    
    // 评分
    score?: number
    min_desc?: string
    max_desc?: string
    style?: any

    // 分割线
    dividerTitle?: string
}

interface SurveyType {
    id: string
    name: string
    describeContent: string
    content: SurveyQuestionType[]
}