<template>
    <div>
        <a-checkbox v-model:checked="isUnify">统一分值</a-checkbox>
        <span>（共{{ list.length }}题，<span v-if="isUnify">
                每{{ type === 'fillBlank' ? '空' : '题' }}
                <a-input-number ref="iptRef" v-model:value="unifyScore" :min="1" :precision="0" :controls="false"
                    @blur="setUnifyScore" @pressEnter="handlePressEnter"></a-input-number>
                分，</span>共{{ totalScore }}分）
        </span>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
const props = defineProps<{
    checked: boolean
    data: {
        score: number
        new_score: number
        [k: string]: any
    }[]
    paperScore: number
    type?: string
}>()

const emits = defineEmits(['update:data', 'update:checked', 'update:paperScore'])

// 输入框
const iptRef = ref()
function handlePressEnter() {
    iptRef.value!.blur()
}

const list = ref<any>([])

// 总分值
const totalScore = computed(() => {
    return list.value.reduce((acc: number, cur: any) => acc + cur.new_score, 0)
})

// 是否设置统一分值
const isUnify = computed({
    get() {
        return props.checked
    },
    set(val) {
        emits('update:checked', val)
        if (val) {
            setUnifyScore()
        }
    }
})

// 计算统一分值
const unifyScore = ref(0)
function calcUnifyScore(list: any = []) {
    if (list.length === 0) return
    if (props.type === 'fillBlank') {
        let averageScore = Math.floor(totalScore.value / list.reduce((acc: number, cur: any) => acc + JSON.parse(cur.answer).length, 0))
        unifyScore.value = Math.max(1, averageScore)
    } else {
        unifyScore.value = Math.floor(totalScore.value / list.length)
    }
}

// 设置统一分值
function setUnifyScore() {
    list.value.forEach((item: any) => {
        if (props.type === 'fillBlank') {
            item.new_score = JSON.parse(item.answer).length * unifyScore.value
        } else {
            item.new_score = unifyScore.value
        }
    })
    emits('update:data', list.value)
}

// 子题分值变化实时计算统一分值和总分
watch(
    () => props.data,
    (val) => {
        list.value = val
        calcUnifyScore(list.value)
    },
    {
        deep: true
    }
)

// 初始化
function init() {
    list.value = props.data
    calcUnifyScore(list.value)
}
init()

// 总分变了就修改试卷分值
// 一定要在list初次赋值之后监听，否则totalScore的oldVal会是0
// watch(
//     () => totalScore.value,
//     (val, oldVal) => {
//         let ans = Number(props.paperScore) + (val - oldVal)
//         emits('update:paperScore', ans)
//     }
// )

</script>