<script setup lang="ts">
import type { StrengthProps } from './shared'

defineProps<{
  strengthsData: StrengthProps
}>()
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 h-full">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        核心优势
      </h3>
    </div>
    <div v-if="strengthsData?.length > 0" class="space-y-4">
      <div v-for="(item, index) in strengthsData" :key="index" class="flex items-start">
        <div class="w-5 h-5 flex justify-center items-center flex-shrink-0">
          <svg width="16px" height="16px" viewBox="0 0 16 16">
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g transform="translate(-650.000000, -315.000000)">
                <g transform="translate(626.000000, 232.000000)">
                  <g transform="translate(24.000000, 80.000000)">
                    <g transform="translate(0.000000, 3.000000)">
                      <rect x="0" y="0" width="16" height="16" />
                      <rect x="0" y="0" width="16" height="16" />
                      <rect fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="16" height="16" />
                      <path
                        id="路径"
                        d="M8,1 C4.134375,1 1,4.134375 1,8 C1,11.865625 4.134375,15 8,15 C11.865625,15 15,11.865625 15,8 C15,4.134375 11.865625,1 8,1 Z" fill="#52C41A"
                      />
                      <path
                        id="路径"
                        d="M11.0234375,5.7140625 L7.7328125,10.2765625 C7.534375,10.553125 7.1234375,10.553125 6.925,10.2765625 L4.9765625,7.5765625 C4.9171875,7.49375 4.9765625,7.378125 5.078125,7.378125 L5.8109375,7.378125 C5.9703125,7.378125 6.121875,7.4546875 6.215625,7.5859375 L7.328125,9.1296875 L9.784375,5.7234375 C9.878125,5.59375 10.028125,5.515625 10.1890625,5.515625 L10.921875,5.515625 C11.0234375,5.515625 11.0828125,5.63125 11.0234375,5.7140625 Z" fill="#FFFFFF"
                      />
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>

        <div class="ml-2 text-sm" v-html="item.description" />
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-start h-full">
        <div class="text-gray-500">
          暂无核心优势
        </div>
      </div>
    </div>
  </div>
</template>
