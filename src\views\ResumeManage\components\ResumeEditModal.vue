<script lang="ts" setup>
import { updateResumeInfoApi } from '@/api/admin/resumeManage'
import { checkEmailInForm } from '@/hooks/useValidEmail'
import { chinaCity } from '@/lib/china-city'
import { ElMessage, ElMessageBox } from 'element-plus'

const emit = defineEmits(['confirm', 'cancel'])
const dialogVisible = defineModel<boolean>('visible', { required: true })
const userData = defineModel<any>('userData', { required: true })

const lastNoChangedUserData = ref(userData.value)

// 省市数据
const provinceAndCityOptions = Object.entries(chinaCity).map(([city, districts]) => {
  return {
    value: city,
    label: city,
    children: districts.map(district => ({
      value: district,
      label: district,
    })),
  }
})

// 禁用日期函数
function birthdayDisabledDate(time: Date) {
  return time.getTime() > Date.now()
}

// 表单数据
const formData = reactive({
  name: userData.value?.candidateName,
  gender: userData.value?.gender,
  birthday: userData.value?.birthday,
  phone: userData.value?.phone,
  email: userData.value?.email,
  education: userData.value?.highestEducation,
  school: userData.value?.graduateSchool,
  hometown: userData.value?.hometown,
  experience: userData.value?.workYears,
})

/**
 * 打开对话框时，设置表单数据
 */
function handleOpened() {
  formData.name = userData.value?.candidateName
  formData.gender = userData.value?.gender
  formData.birthday = userData.value?.birthday
  formData.phone = userData.value?.phone
  formData.email = userData.value?.email
  formData.education = userData.value?.highestEducation
  formData.school = userData.value?.graduateSchool
  formData.experience = userData.value?.workYears
  formData.hometown = userData.value?.hometown

  lastNoChangedUserData.value = JSON.parse(JSON.stringify(formData))
}

// 手机号验证函数
function validatePhone(_rule: any, value: string) {
  const phoneRegex = /^1[3,45789]\d{9}$/
  if (value === '') {
    return Promise.reject('请输入手机号')
  }
  else if (!phoneRegex.test(value)) {
    return Promise.reject('手机号格式不正确')
  }
  return Promise.resolve()
}

// 表单规则
const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 20, message: '姓名最多20个字符', trigger: 'blur' },
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  birthday: [{ required: true, message: '请选择出生年月', trigger: 'change' }],
  phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  email: [{ required: true, validator: checkEmailInForm(), trigger: 'blur' }],
  school: [{ max: 50, message: '毕业院校最多50个字符', trigger: 'blur' }],
  experience: [{ max: 10, message: '工作年限最多10个字符', trigger: 'blur' }],
}

// 表单引用
const formRef = ref()

function handleClose(done: () => void) {
  if (JSON.stringify(lastNoChangedUserData.value) !== JSON.stringify(formData)) {
    ElMessageBox.confirm('确定取消吗？取消后当前内容不保存', {
      type: 'warning',
    })
      .then(() => {
        done()
      })
      .catch(() => {
        // 用户点击取消，保持对话框打开
      })
  }
  else {
    done()
  }
}

function handleCancel() {
  if (JSON.stringify(lastNoChangedUserData.value) !== JSON.stringify(formData)) {
    ElMessageBox.confirm('确定取消吗？取消后当前内容不保存', {
      type: 'warning',
    })
      .then(() => {
        dialogVisible.value = false
        emit('cancel')
      })
      .catch(() => {
        // catch error
      })
  }
  else {
    dialogVisible.value = false
    emit('cancel')
  }
}

function handleConfirm() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      updateResumeInfoApi({
        resumeId: userData.value.resumeId,
        candidateName: formData.name,
        gender: formData.gender,
        phone: formData.phone,
        email: formData.email,
        highestEducation: formData.education,
        workYears: formData.experience,
        graduateSchool: formData.school,
        birthday: formData.birthday,
        hometown: formData.hometown,
      })
        .then((res: any) => {
          dialogVisible.value = false
          emit('confirm', res)
          ElMessage.success('修改成功')
        })
        .catch((error: any) => {
          ElMessage.error('修改失败')
          console.error(error)
        })
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible" 
    title="编辑简历"
    width="700px" 
    :before-close="handleClose" 
    destroy-on-close 
    :show-close="false"
    class="resume-edit-dialog !p-8" :style="{
      // 'min-width': '200px',
      // '--el-dialog-padding-primary': '0px',
      // 'border': '1px solid #11575D',
      // 'border-radius': '8px',
      // 'overflow': 'hidden',
    }" 
    @opened="handleOpened"
  >
    <template #header="{ close }">
      <div class="flex justify-between items-center">
        <span class="text-xl font-bold">编辑简历</span>
        <el-button link icon="el-icon-close" @click="close">
          <span class="i-lucide-x w-5 h-5 text-gray-500" />
        </el-button>
      </div>
    </template>

    <el-form
      ref="formRef" :model="formData" :rules="rules" label-position="left" label-width="80px"
      class="resume-form "
    >
      <div class="grid grid-cols-2 gap-12 pt-8">
        <div class="col-span-1">
          <el-form-item label="姓名" prop="name" class="required-field ">
            <el-input v-model="formData.name" placeholder="请输入" maxlength="20" />
          </el-form-item>

          <el-form-item label="性别" prop="gender" class="required-field">
            <el-select v-model="formData.gender" placeholder="请选择" class="w-full">
              <el-option label="男" value="男" />
              <el-option label="女" value="女" />
            </el-select>
          </el-form-item>

          <el-form-item label="出生年月" prop="birthday" class="required-field">
            <el-date-picker
              v-model="formData.birthday"
              format="YYYY-MM-DD" 
              value-format="YYYY-MM-DD"
              type="date" 
              placeholder="请选择" 
              :disabled-date="birthdayDisabledDate"
              class="w-full"
            />
          </el-form-item>

          <el-form-item label="手机号" prop="phone" class="required-field">
            <el-input v-model="formData.phone" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="电子邮箱" prop="email" class="required-field">
            <el-input v-model="formData.email" placeholder="请输入" />
          </el-form-item>
        </div>

        <div class="col-span-1 flex flex-col">
          <el-form-item label="最高学历" prop="education">
            <el-select v-model="formData.education" placeholder="请选择" class="w-full">
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="毕业院校">
            <el-input v-model="formData.school" placeholder="请输入" maxlength="50" />
          </el-form-item>

          <el-form-item label="籍贯">
            <el-cascader
              v-model="formData.hometown"
              expand-trigger="hover"
              :options="provinceAndCityOptions"
              placeholder="请选择省/市"
              class="w-full"
              clearable
            />
          </el-form-item>

          <el-form-item label="工作年限" prop="experience">
            <el-input v-model="formData.experience" placeholder="请输入" maxlength="10" />
          </el-form-item>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="flex justify-center gap-4">
        <el-button type="primary" class="w-24" @click="handleConfirm">
          确认
        </el-button>
        <el-button class="w-24" @click="handleCancel">
          取消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.resume-edit-dialog :deep(.el-dialog__header) {
  margin-right: 0;
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  font-size: 20px !important;
}

.resume-form :deep(.el-form-item__label) {
  font-weight: normal;
  font-size: 12px !important;
}

.required-field :deep(.el-form-item__label)::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-select__selected-item ) {
  font-size: 12px !important;
}
:deep(.el-input__wrapper) {
  /* flex-direction: row-reverse; */
}

/* 上传组件样式 */
.avatar-uploader :deep(.el-upload) {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader :deep(.el-upload:hover) {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 96px;
  height: 96px;
  text-align: center;
  border-radius: 50%;
  border: 4px solid #f8d57e;
  background-color: #4b83f0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>