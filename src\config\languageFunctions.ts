export default [
  {
    "language": "C",
    "groups": [
      {
        key: 'sort',
        label: '排序函数',
        functions: [
          {
            "funcname": "qsort",
            "description": "快速排序—对数组进行排序",
            "usage": "qsort(arr, len, sizeof(int), compare); // arr是待排序整型数组，len是数组中元素数量，compare是比较函数"
          }
        ]
      }
    ]
  },
  {
    "language": "C++",
    "groups": [
      {
        key: 'sort',
        label: '排序函数',
        functions: [
          {
            "funcname": "::sort",
            "description": "将给定范围内的元素进行排序，默认升序。",
            "usage": "std::sort(begin(v), end(v)); // 将向量v排序"
          },
          {
            "funcname": "::stable_sort",
            "description": "与std::sort类似，但保持相等元素的相对顺序。",
            "usage": "std::stable_sort(begin(v), end(v)); // 将向量v稳定排序"
          },
          {
            "funcname": "::partial_sort",
            "description": "将范围内的前n个最小元素以升序排序。",
            "usage": "std::partial_sort(begin(v), begin(v) + n, end(v)); // 对向量v的前n个元素排序"
          },
          {
            "funcname": "::nth_element",
            "description": "重新排列元素，使第n个元素放置在完全排序后它将处于的位置。",
            "usage": "std::nth_element(begin(v), begin(v) + n, end(v)); // 仅将第n个位置的元素排序"
          },
          {
            "funcname": "::partial_sort_copy",
            "description": "将一范围内元素的部分排序复制到另一范围。",
            "usage": "std::partial_sort_copy(begin(src), end(src), begin(dest), end(dest)); // 将src的部分排序复制到dest"
          }
        ]
      },
      {
        key: 'search',
        label: '搜索函数',
        functions: [
          {
            "funcname": "::find",
            "description": "在给定范围内查找与指定值等价的第一个元素。",
            "usage": "auto it = std::find(begin(v), end(v), value); // 在向量v中查找value"
          },
          {
            "funcname": "::find_if",
            "description": "在给定范围内查找使谓词p返回true的第一个元素。",
            "usage": "auto it = std::find_if(begin(v), end(v), predicate); // 根据谓词查找元素"
          },
          {
            "funcname": "::find_if_not",
            "description": "在给定范围内查找不使谓词p返回true的第一个元素。",
            "usage": "auto it = std::find_if_not(begin(v), end(v), predicate); // 查找不满足谓词的元素"
          },
          {
            "funcname": "::search",
            "description": "在第一个范围内查找第二个范围的首次出现。",
            "usage": "auto it = std::search(begin(haystack), end(haystack), begin(needle), end(needle)); // 在haystack中查找needle序列的开始"
          },
          {
            "funcname": "::search_n",
            "description": "在范围内查找值连续出现n次的第一个位置。",
            "usage": "auto it = std::search_n(begin(v), end(v), count, value); // 查找值连续出现n次的位置"
          },
          {
            "funcname": "::binary_search",
            "description": "在已排序的范围内判断等于给定值的元素是否存在。",
            "usage": "bool found = std::binary_search(begin(v), end(v), value); // 检查已排序向量v中是否存在value"
          },
          {
            "funcname": "::lower_bound",
            "description": "在已排序的范围内查找可以插入value且不破坏顺序的第一个位置。",
            "usage": "auto it = std::lower_bound(begin(v), end(v), value); // 查找value在已排序向量v中的下界"
          },
          {
            "funcname": "::upper_bound",
            "description": "在已排序的范围内查找value的上界，即第一个大于value的元素。",
            "usage": "auto it = std::upper_bound(begin(v), end(v), value); // 查找value在已排序向量v中的上界"
          },
          {
            "funcname": "::equal_range",
            "description": "在已排序的范围内找到等于给定值的元素的范围，返回一对迭代器。",
            "usage": "auto p = std::equal_range(begin(v), end(v), value); // 查找值等于value的元素范围"
          }
        ]
      },
      {
        key: 'math',
        label: '数学函数',
        functions: [
          {
            "funcname": "::accumulate",
            "description": "计算给定范围内所有元素的累积总和。",
            "usage": "int sum = std::accumulate(begin(v), end(v), 0); // 计算向量v的元素和"
          },
          {
            "funcname": "::inner_product",
            "description": "计算两个范围的内积。",
            "usage": "int product = std::inner_product(begin(v1), end(v1), begin(v2), 0); // 计算向量v1和v2的内积"
          },
          {
            "funcname": "::adjacent_difference",
            "description": "计算相邻元素的差异并存储到另一范围。",
            "usage": "std::adjacent_difference(begin(v), end(v), begin(result)); // 计算v中相邻元素的差异"
          },
          {
            "funcname": "::iota",
            "description": "在范围内填充序列。",
            "usage": "std::iota(begin(v), end(v), startValue); // 从startValue开始填充向量v"
          },
          {
            "funcname": "::partial_sum",
            "description": "计算给定范围内所有元素的部分累积和。",
            "usage": "std::partial_sum(begin(v), end(v), begin(result)); // 计算向量v的部分和到result"
          },
          {
            "funcname": "::reduce",
            "description": "计算范围内所有元素的累积结果（C++17引入）。",
            "usage": "int sum = std::reduce(begin(v), end(v), 0); // 计算向量v元素的累积和"
          },
          {
            "funcname": "::transform_reduce",
            "description": "将变换和归约组合在一起，计算范围内所有元素的变换后的累积结果（C++17引入）。",
            "usage": "int sum = std::transform_reduce(begin(v), end(v), 0, std::plus<>(), transformOp); // 对向量v变换后的元素求和"
          },
          {
            "funcname": "::exclusive_scan",
            "description": "计算给定范围内所有元素的独占前缀扫描（C++17引入）。",
            "usage": "std::exclusive_scan(begin(v), end(v), begin(result), init); // 对向量v执行独占扫描到result"
          },
          {
            "funcname": "::inclusive_scan",
            "description": "计算给定范围内所有元素的包含前缀扫描（C++17引入）。",
            "usage": "std::inclusive_scan(begin(v), end(v), begin(result)); // 对向量v执行包含扫描到result"
          }
        ]
      },
      {
        key: 'string',
        label: '字符串处理函数',
        functions: [
          {
            "funcname": ".find",
            "description": "搜索字符串中第一次出现的另一个字符串、字符或字符序列。",
            "usage": "size_t pos = str.find(sub); // 在字符串str中查找子字符串sub的位置"
          },
          {
            "funcname": ".rfind",
            "description": "搜索字符串中最后一次出现的另一个字符串、字符或字符序列。",
            "usage": "size_t pos = str.rfind(sub); // 在字符串str中从尾部开始查找子字符串sub的位置"
          },
          {
            "funcname": ".find_first_of",
            "description": "搜索字符串中第一次出现任何提供的字符集合中字符的位置。",
            "usage": "size_t pos = str.find_first_of(chars); // 在字符串str中查找chars中任意字符首次出现的位置"
          },
          {
            "funcname": ".find_last_of",
            "description": "搜索字符串中最后一次出现任何提供的字符集合中字符的位置。",
            "usage": "size_t pos = str.find_last_of(chars); // 在字符串str中查找chars中任意字符最后一次出现的位置"
          },
          {
            "funcname": ".find_first_not_of",
            "description": "搜索字符串中第一次出现不在提供的字符集合中的字符的位置。",
            "usage": "size_t pos = str.find_first_not_of(chars); // 在字符串str中查找第一个不在chars中的字符"
          },
          {
            "funcname": ".find_last_not_of",
            "description": "搜索字符串中最后一次出现不在提供的字符集合中的字符的位置。",
            "usage": "size_t pos = str.find_last_not_of(chars); // 在字符串str中最后一个不在chars中的字符"
          },
          {
            "funcname": ".substr",
            "description": "返回字符串的一个子串。",
            "usage": "std::string sub = str.substr(pos, len); // 从字符串str的pos位置开始，截取长度为len的子串"
          }
        ]
      },
      {
        key: 'list',
        label: '集合操作函数',
        functions: [
          {
            "funcname": "::set_union",
            "description": "构造两个已排序范围的并集。",
            "usage": "std::set_union(begin(v1), end(v1), begin(v2), end(v2), std::back_inserter(result)); // 计算v1和v2的并集到result"
          },
          {
            "funcname": "::set_intersection",
            "description": "构造两个已排序范围的交集。",
            "usage": "std::set_intersection(begin(v1), end(v1), begin(v2), end(v2), std::back_inserter(result)); // 计算v1和v2的交集到result"
          },
          {
            "funcname": "::set_difference",
            "description": "构造两个已排序范围的差集，即存在于第一个范围但不在第二个范围中的元素。",
            "usage": "std::set_difference(begin(v1), end(v1), begin(v2), end(v2), std::back_inserter(result)); // 计算v1和v2的差集到result"
          },
          {
            "funcname": "::set_symmetric_difference",
            "description": "构造两个已排序范围的对称差集，即只存在于一个范围但不同时存在于两个范围中的元素。",
            "usage": "std::set_symmetric_difference(begin(v1), end(v1), begin(v2), end(v2), std::back_inserter(result)); // 计算v1和v2的对称差集到result"
          }
        ]
      },
    ],
  },
  {
    "language": "Java",
    "groups": [
      {
        key: 'sort',
        label: '排序函数',
        functions: [
          {
            "funcname": ".sort",
            "description": "对数组进行排序",
            "usage": "Arrays.sort(array); // 对数组array进行升序排序"
          }
        ]
      },
      {
        key: 'search',
        label: '搜索函数',
        functions: [
          {
            "funcname": ".binarySearch",
            "description": "使用二分搜索算法在指定数组中搜索指定值的索引",
            "usage": "int index = Arrays.binarySearch(array, key); // 在数组array中搜索值为key的元素，并返回其索引"
          }
        ]
      },
      {
        key: 'math',
        label: '数学函数',
        functions: [
          {
            "funcname": ".min",
            "description": "返回两个参数中较小的那个数",
            "usage": "int minResult = Math.min(num1, num2);"
          },
          {
            "funcname": ".max",
            "description": "返回两个参数中较大的那个数",
            "usage": "int maxResult = Math.max(num1, num2);"
          },
          {
            "funcname": ".abs",
            "description": "返回参数的绝对值",
            "usage": "int absoluteValue = Math.abs(number);"
          },
          {
            "funcname": ".sqrt",
            "description": "返回参数的平方根",
            "usage": "double squareRoot = Math.sqrt(number);"
          },
          {
            "funcname": ".cbrt",
            "description": "返回参数的立方根",
            "usage": "double cubeRoot = Math.cbrt(number);"
          },
          {
            "funcname": ".pow",
            "description": "返回第一个参数的第二个参数次方",
            "usage": "double result = Math.pow(base, exponent);"
          },
          {
            "funcname": ".exp",
            "description": "返回自然常数e的参数次方",
            "usage": "double result = Math.exp(number);"
          },
          {
            "funcname": ".log",
            "description": "返回参数的自然对数",
            "usage": "double naturalLog = Math.log(number);"
          },
          {
            "funcname": ".log10",
            "description": "返回参数的以10为底的对数",
            "usage": "double logBase10 = Math.log10(number);"
          },
          {
            "funcname": ".log1p",
            "description": "返回参数加1的自然对数",
            "usage": "double logOfOnePlusNumber = Math.log1p(number);"
          },
          {
            "funcname": ".sin",
            "description": "返回参数的正弦值",
            "usage": "double sineValue = Math.sin(angleInRadians);"
          },
          {
            "funcname": ".cos",
            "description": "返回参数的余弦值",
            "usage": "double cosineValue = Math.cos(angleInRadians);"
          },
          {
            "funcname": ".tan",
            "description": "返回参数的正切值",
            "usage": "double tangentValue = Math.tan(angleInRadians);"
          },
          {
            "funcname": ".asin",
            "description": "返回参数的反正弦值",
            "usage": "double angleInRadians = Math.asin(value);"
          },
          {
            "funcname": ".acos",
            "description": "返回参数的反余弦值",
            "usage": "double angleInRadians = Math.acos(value);"
          },
          {
            "funcname": ".atan",
            "description": "返回参数的反正切值",
            "usage": "double angleInRadians = Math.atan(value);"
          },
          {
            "funcname": ".atan2",
            "description": "根据两个参数的值计算反正切值",
            "usage": "double angleInRadians = Math.atan2(y, x);"
          },
          {
            "funcname": ".toRadians",
            "description": "将角度转换为弧度",
            "usage": "double angleInRadians = Math.toRadians(angleInDegrees);"
          },
          {
            "funcname": ".toDegrees",
            "description": "将弧度转换为角度",
            "usage": "double angleInDegrees = Math.toDegrees(angleInRadians);"
          },
          {
            "funcname": ".round",
            "description": "将参数四舍五入为整数",
            "usage": "long roundedNumber = Math.round(decimalNumber);"
          },
          {
            "funcname": ".rint",
            "description": "将参数转换为最接近的整数",
            "usage": "double roundedNumber = Math.rint(decimalNumber);"
          },
          {
            "funcname": ".ceil",
            "description": "返回不小于参数的最小整数",
            "usage": "double roundedUpNumber = Math.ceil(decimalNumber);"
          },
          {
            "funcname": ".floor",
            "description": "返回不大于参数的最大整数",
            "usage": "double roundedDownNumber = Math.floor(decimalNumber);"
          },
          {
            "funcname": ".scalb",
            "description": "返回参数乘以2的n次幂",
            "usage": "double scaledNumber = Math.scalb(number, n);"
          }
        ]
      },
      {
        key: 'string',
        label: '字符串处理函数',
        functions: [
          {
            "funcname": ".contains",
            "description": "判断字符串中是否包含指定的字符序列",
            "usage": "boolean contains = str.contains(\"searchString\");"
          },
          {
            "funcname": ".startsWith",
            "description": "判断字符串是否以指定的前缀开始",
            "usage": "boolean startsWith = str.startsWith(\"prefix\");"
          },
          {
            "funcname": ".endsWith",
            "description": "判断字符串是否以指定的后缀结束",
            "usage": "boolean endsWith = str.endsWith(\"suffix\");"
          },
          {
            "funcname": ".indexOf",
            "description": "返回指定字符或字符串在字符串中第一次出现的位置",
            "usage": "int index = str.indexOf('a');"
          },
          {
            "funcname": ".lastIndexOf",
            "description": "返回指定字符或字符串在字符串中最后一次出现的位置",
            "usage": "int lastIndex = str.lastIndexOf('a');"
          },
          {
            "funcname": ".toCharArray",
            "description": "将字符串转换为字符数组",
            "usage": "char[] charArray = str.toCharArray();"
          },
          {
            "funcname": ".split",
            "description": "使用指定的分隔符将字符串拆分为子字符串",
            "usage": "String[] parts = str.split(\",\");"
          },
          {
            "funcname": ".substring",
            "description": "返回指定索引范围内的子字符串",
            "usage": "String sub = str.substring(startIndex, endIndex);"
          },
          {
            "funcname": ".concat",
            "description": "将指定字符串连接到此字符串的结尾",
            "usage": "String concatenated = str.concat(\"anotherString\");"
          },
          {
            "funcname": ".replace",
            "description": "使用新字符串替换旧字符串中的所有匹配项",
            "usage": "String replaced = str.replace(\"old\", \"new\");"
          },
          {
            "funcname": ".matches",
            "description": "判断字符串是否匹配指定的正则表达式",
            "usage": "boolean matches = str.matches(\"[a-z]+\\d*\");"
          },
          {
            "funcname": ".join",
            "description": "将字符串数组中的所有元素用指定分隔符连接成一个字符串",
            "usage": "String joined = String.join(\",\", strArray);"
          }
        ]
      },
      {
        key: 'list',
        label: '集合操作函数',
        functions: [
          {
            "funcname": ".addAll",
            "description": "将一个集合中的所有元素添加到另一个集合中",
            "usage": "list1.addAll(list2);"
          },
          {
            "funcname": ".disjoint",
            "description": "判断两个集合是否没有相同的元素",
            "usage": "boolean isDisjoint = Collections.disjoint(list1, list2);"
          },
          {
            "funcname": ".frequency",
            "description": "返回指定元素在集合中出现的次数",
            "usage": "int freq = Collections.frequency(list, element);"
          },
          {
            "funcname": ".nCopies",
            "description": "返回包含指定元素的不可变列表",
            "usage": "List<String> copies = Collections.nCopies(5, \"hello\");"
          },
          {
            "funcname": ".replaceAll",
            "description": "使用新值替换集合中的所有旧值",
            "usage": "Collections.replaceAll(list, oldValue, newValue);"
          },
          {
            "funcname": ".reverseOrder",
            "description": "返回一个比较器，以相反的顺序对对象进行排序",
            "usage": "Collections.sort(list, Collections.reverseOrder());"
          },
          {
            "funcname": ".shuffle",
            "description": "随机排列集合中的元素",
            "usage": "Collections.shuffle(list);"
          },
          {
            "funcname": ".swap",
            "description": "交换集合中指定位置的元素",
            "usage": "Collections.swap(list, index1, index2);"
          },
          {
            "funcname": ".fill",
            "description": "用指定的元素替换集合中的所有元素",
            "usage": "Collections.fill(list, element);"
          },
          {
            "funcname": ".copy",
            "description": "将源列表的所有元素复制到目标列表中",
            "usage": "Collections.copy(destList, srcList);"
          },
          {
            "funcname": ".min",
            "description": "返回集合中的最小元素",
            "usage": "T minElement = Collections.min(list);"
          },
          {
            "funcname": ".max",
            "description": "返回集合中的最大元素",
            "usage": "T maxElement = Collections.max(list);"
          }
        ]
      },
    ]
  },
  {
    "language": "Python",
    "groups": [
      {
        key: 'sort',
        label: '排序函数',
        functions: [
          {
            "funcname": "sorted",
            "description": "对可迭代对象进行排序，返回一个新的列表",
            "usage": "sortedList = sorted(myList)"
          },
          {
            "funcname": ".sort",
            "description": "对列表进行就地排序，不返回新的列表",
            "usage": "myList.sort()"
          }
        ]
      },
      {
        key: 'search',
        label: '搜索函数',
        functions: [
          {
            "funcname": ".index",
            "description": "返回列表中指定值的第一个匹配项的索引",
            "usage": "index = myList.index(value)"
          },
          {
            "funcname": ".bisect",
            "description": "在有序列表中查找插入位置，返回插入点的索引",
            "usage": "insertIndex = bisect.bisect(sortedList, value)"
          },
          {
            "funcname": ".bisect_left",
            "description": "在有序列表中查找插入位置，返回插入点的索引（左侧）",
            "usage": "insertIndex = bisect.bisect_left(sortedList, value)"
          },
          {
            "funcname": ".bisect_right",
            "description": "在有序列表中查找插入位置，返回插入点的索引（右侧）",
            "usage": "insertIndex = bisect.bisect_right(sortedList, value)"
          }
        ]
      },
      {
        key: 'math',
        label: '数学函数',
        functions: [
          {
            "funcname": "min",
            "description": "返回可迭代对象中的最小值",
            "usage": "smallest = min(myList)"
          },
          {
            "funcname": "max",
            "description": "返回可迭代对象中的最大值",
            "usage": "largest = max(myList)"
          },
          {
            "funcname": "sum",
            "description": "返回可迭代对象中所有元素的总和",
            "usage": "total = sum(myList)"
          },
          {
            "funcname": ".sqrt",
            "description": "返回一个数的平方根",
            "usage": "squareRoot = math.sqrt(number)"
          },
          {
            "funcname": ".pow",
            "description": "返回一个数的幂",
            "usage": "result = math.pow(base, exponent)"
          },
          {
            "funcname": ".ceil",
            "description": "返回大于或等于指定数字的最小整数",
            "usage": "roundedUp = math.ceil(number)"
          },
          {
            "funcname": ".floor",
            "description": "返回小于或等于指定数字的最大整数",
            "usage": "roundedDown = math.floor(number)"
          },
          {
            "funcname": ".sin",
            "description": "返回给定角度的正弦值",
            "usage": "sineValue = math.sin(angle)"
          },
          {
            "funcname": ".cos",
            "description": "返回给定角度的余弦值",
            "usage": "cosValue = math.cos(angle)"
          },
          {
            "funcname": ".tan",
            "description": "返回给定角度的正切值",
            "usage": "tanValue = math.tan(angle)"
          },
          {
            "funcname": ".asin",
            "description": "返回一个数的反正弦值",
            "usage": "angle = math.asin(value)"
          },
          {
            "funcname": ".acos",
            "description": "返回一个数的反余弦值",
            "usage": "angle = math.acos(value)"
          },
          {
            "funcname": ".atan",
            "description": "返回一个数的反正切值",
            "usage": "angle = math.atan(value)"
          },
          {
            "funcname": ".hypot",
            "description": "返回两个数的平方和的平方根",
            "usage": "distance = math.hypot(x, y)"
          },
          {
            "funcname": ".radians",
            "description": "将角度转换为弧度",
            "usage": "radians = math.radians(degrees)"
          },
          {
            "funcname": ".degrees",
            "description": "将弧度转换为角度",
            "usage": "degrees = math.degrees(radians)"
          },
          {
            "funcname": ".log",
            "description": "返回一个数的自然对数",
            "usage": "naturalLog = math.log(number)"
          },
          {
            "funcname": ".log10",
            "description": "返回一个数的以10为底的对数",
            "usage": "logBase10 = math.log10(number)"
          },
          {
            "funcname": ".log2",
            "description": "返回一个数的以2为底的对数",
            "usage": "logBase2 = math.log2(number)"
          },
          {
            "funcname": ".exp",
            "description": "返回自然常数e的幂次方",
            "usage": "result = math.exp(x)"
          },
          {
            "funcname": ".expm1",
            "description": "返回e的x次方减1",
            "usage": "result = math.expm1(x)"
          },
          {
            "funcname": ".factorial",
            "description": "返回一个数的阶乘",
            "usage": "result = math.factorial(x)"
          },
          {
            "funcname": ".gcd",
            "description": "返回两个数的最大公约数",
            "usage": "result = math.gcd(x, y)"
          },
          {
            "funcname": ".isclose",
            "description": "确定两个值是否在指定的误差范围内相等",
            "usage": "close = math.isclose(a, b, rel_tol=1e-09, abs_tol=0.0)"
          },
          {
            "funcname": ".isfinite",
            "description": "检查一个数是否是有限的",
            "usage": "finite = math.isfinite(x)"
          },
          {
            "funcname": ".isinf",
            "description": "检查一个数是否是无穷大",
            "usage": "inf = math.isinf(x)"
          },
          {
            "funcname": ".isnan",
            "description": "检查一个数是否是NaN",
            "usage": "nan = math.isnan(x)"
          },
          {
            "funcname": "numpy.mean",
            "description": "计算数组的平均值",
            "usage": "meanValue = numpy.mean(myArray)"
          },
          {
            "funcname": "numpy.median",
            "description": "计算数组的中位数",
            "usage": "medianValue = numpy.median(myArray)"
          },
          {
            "funcname": "numpy.std",
            "description": "计算数组的标准差",
            "usage": "stdDeviation = numpy.std(myArray)"
          },
          {
            "funcname": "numpy.var",
            "description": "计算数组的方差",
            "usage": "variance = numpy.var(myArray)"
          }
        ]
      },
      {
        key: 'string',
        label: '字符串处理函数',
        functions: [
          {
            "funcname": ".find",
            "description": "返回指定字符串在原字符串中第一次出现的位置，如果未找到返回-1",
            "usage": "position = myString.find(substring)"
          },
          {
            "funcname": ".index",
            "description": "和find类似，但是未找到指定字符串时会引发ValueError异常",
            "usage": "position = myString.index(substring)"
          },
          {
            "funcname": ".join",
            "description": "将序列中的元素以指定的字符连接生成一个新的字符串",
            "usage": "newString = separator.join(myList)"
          },
          {
            "funcname": ".split",
            "description": "将字符串根据指定的分隔符分割成子字符串，并返回一个包含子字符串的列表",
            "usage": "substrings = myString.split(separator)"
          },
          {
            "funcname": ".replace",
            "description": "将指定字符串替换成新的字符串",
            "usage": "newString = myString.replace(old, new)"
          },
          {
            "funcname": ".upper",
            "description": "将字符串中的字母转换为大写",
            "usage": "upperString = myString.upper()"
          },
          {
            "funcname": ".lower",
            "description": "将字符串中的字母转换为小写",
            "usage": "lowerString = myString.lower()"
          },
          {
            "funcname": ".capitalize",
            "description": "将字符串的第一个字母转换为大写，其他字母转换为小写",
            "usage": "capitalizedString = myString.capitalize()"
          },
          {
            "funcname": ".title",
            "description": "将字符串中每个单词的首字母大写",
            "usage": "titledString = myString.title()"
          },
          {
            "funcname": ".startswith",
            "description": "检查字符串是否以指定的前缀开头",
            "usage": "startsWithPrefix = myString.startswith(prefix)"
          },
          {
            "funcname": ".endswith",
            "description": "检查字符串是否以指定的后缀结尾",
            "usage": "endsWithSuffix = myString.endswith(suffix)"
          },
          {
            "funcname": ".strip",
            "description": "去掉字符串开头和结尾的空白字符",
            "usage": "strippedString = myString.strip()"
          },
          {
            "funcname": ".rstrip",
            "description": "去掉字符串结尾的空白字符",
            "usage": "rightStrippedString = myString.rstrip()"
          },
          {
            "funcname": ".lstrip",
            "description": "去掉字符串开头的空白字符",
            "usage": "leftStrippedString = myString.lstrip()"
          }
        ]
      },
      {
        key: 'list',
        label: '集合操作函数',
        functions: [
          {
            "funcname": "set.union",
            "description": "返回两个集合的并集",
            "usage": "unionSet = set1.union(set2)"
          },
          {
            "funcname": "set.intersection",
            "description": "返回两个集合的交集",
            "usage": "intersectionSet = set1.intersection(set2)"
          },
          {
            "funcname": "set.difference",
            "description": "返回一个集合中存在，而另一个集合中不存在的元素组成的集合",
            "usage": "differenceSet = set1.difference(set2)"
          },
          {
            "funcname": "set.symmetric_difference",
            "description": "返回两个集合中互相不存在的元素组成的集合",
            "usage": "symmetricDifferenceSet = set1.symmetric_difference(set2)"
          },
          {
            "funcname": "set.issubset",
            "description": "判断一个集合是否为另一个集合的子集",
            "usage": "isSubset = set1.issubset(set2)"
          },
          {
            "funcname": "set.issuperset",
            "description": "判断一个集合是否包含另一个集合",
            "usage": "isSuperset = set1.issuperset(set2)"
          },
          {
            "funcname": "set.isdisjoint",
            "description": "判断两个集合是否没有交集",
            "usage": "isDisjoint = set1.isdisjoint(set2)"
          }
        ]
      },
    ]
  },
  {
    "language": "JavaScript",
    "groups": [
      {
        key: 'sort',
        label: '排序函数',
        functions: [
          {
            "funcname": ".sort",
            "description": "对数组元素进行排序，原数组将被改变",
            "usage": "myArray.sort()"
          }
        ]
      },
      {
        key: 'search',
        label: '搜索函数',
        functions: [
          {
            "funcname": ".indexOf",
            "description": "返回指定元素在数组中第一次出现的位置，如果未找到返回-1",
            "usage": "position = myArray.indexOf(element)"
          },
          {
            "funcname": ".lastIndexOf",
            "description": "返回指定元素在数组中最后一次出现的位置，如果未找到返回-1",
            "usage": "position = myArray.lastIndexOf(element)"
          },
          {
            "funcname": ".find",
            "description": "返回数组中第一个满足测试函数的元素的值，否则返回undefined",
            "usage": "foundElement = myArray.find(testFunction)"
          },
          {
            "funcname": ".findIndex",
            "description": "返回数组中第一个满足测试函数的元素的索引，否则返回-1",
            "usage": "foundIndex = myArray.findIndex(testFunction)"
          },
          {
            "funcname": ".includes",
            "description": "判断数组是否包含指定元素，返回true或false",
            "usage": "isIncluded = myArray.includes(element)"
          }
        ]
      },
      {
        key: 'math',
        label: '数学函数',
        functions: [
          {
            "funcname": ".min",
            "description": "返回参数中的最小值",
            "usage": "minValue = Math.min(5, 3, 8)"
          },
          {
            "funcname": ".max",
            "description": "返回参数中的最大值",
            "usage": "maxValue = Math.max(5, 3, 8)"
          },
          {
            "funcname": ".abs",
            "description": "返回参数的绝对值",
            "usage": "absoluteValue = Math.abs(-7)"
          },
          {
            "funcname": ".sqrt",
            "description": "返回参数的平方根",
            "usage": "squareRoot = Math.sqrt(25)"
          },
          {
            "funcname": ".pow",
            "description": "返回以参数1为底的参数2的幂",
            "usage": "result = Math.pow(2, 3)"
          },
          {
            "funcname": ".ceil",
            "description": "返回大于或等于参数的最小整数",
            "usage": "roundedUp = Math.ceil(3.14)"
          },
          {
            "funcname": ".floor",
            "description": "返回小于或等于参数的最大整数",
            "usage": "roundedDown = Math.floor(3.14)"
          },
          {
            "funcname": ".round",
            "description": "返回四舍五入的整数",
            "usage": "rounded = Math.round(3.14)"
          },
          {
            "funcname": ".sin",
            "description": "返回参数的正弦值",
            "usage": "sineValue = Math.sin(Math.PI / 2)"
          },
          {
            "funcname": ".cos",
            "description": "返回参数的余弦值",
            "usage": "cosineValue = Math.cos(Math.PI)"
          },
          {
            "funcname": ".tan",
            "description": "返回参数的正切值",
            "usage": "tangentValue = Math.tan(Math.PI / 4)"
          },
          {
            "funcname": ".asin",
            "description": "返回参数的反正弦值",
            "usage": "arcSineValue = Math.asin(0.5)"
          },
          {
            "funcname": ".acos",
            "description": "返回参数的反余弦值",
            "usage": "arcCosineValue = Math.acos(0.5)"
          },
          {
            "funcname": ".atan",
            "description": "返回参数的反正切值",
            "usage": "arcTangentValue = Math.atan(1)"
          },
          {
            "funcname": ".atan2",
            "description": "返回参数2除以参数1的反正切值",
            "usage": "atan2Value = Math.atan2(1, 2)"
          },
          {
            "funcname": ".exp",
            "description": "返回自然对数e的参数次幂",
            "usage": "exponentialValue = Math.exp(2)"
          },
          {
            "funcname": ".log",
            "description": "返回参数的自然对数",
            "usage": "naturalLogarithm = Math.log(10)"
          },
          {
            "funcname": ".log10",
            "description": "返回参数的以10为底的对数",
            "usage": "logBase10 = Math.log10(100)"
          },
          {
            "funcname": ".log2",
            "description": "返回参数的以2为底的对数",
            "usage": "logBase2 = Math.log2(8)"
          },
          {
            "funcname": ".random",
            "description": "返回一个大于等于0小于1的随机数",
            "usage": "randomNumber = Math.random()"
          },
          {
            "funcname": ".trunc",
            "description": "去除参数的小数部分，返回整数",
            "usage": "integerPart = Math.trunc(3.14)"
          },
          {
            "funcname": ".sign",
            "description": "返回参数的符号，1表示正数，-1表示负数，0表示0",
            "usage": "signValue = Math.sign(-7)"
          },
          {
            "funcname": ".cbrt",
            "description": "返回参数的立方根",
            "usage": "cubeRoot = Math.cbrt(27)"
          },
          {
            "funcname": ".hypot",
            "description": "返回所有参数的平方和的平方根",
            "usage": "hypotenuse = Math.hypot(3, 4)"
          },
          {
            "funcname": ".imul",
            "description": "返回参数1和参数2的32位整数乘法运算结果",
            "usage": "product = Math.imul(2, 4)"
          },
          {
            "funcname": ".fround",
            "description": "返回参数的最接近的单精度浮点数表示",
            "usage": "roundedValue = Math.fround(3.14)"
          },
          {
            "funcname": ".clz32",
            "description": "返回参数的32位无符号整数形式的前导零个数",
            "usage": "leadingZeros = Math.clz32(1000)"
          },
          {
            "funcname": ".expm1",
            "description": "返回e的参数次幂减1的值",
            "usage": "exponentialMinusOne = Math.expm1(1)"
          }
        ]
      },
      {
        key: 'string',
        label: '字符串处理函数',
        functions: [
          {
            "funcname": ".indexOf",
            "description": "返回指定元素在字符串中第一次出现的位置，如果未找到返回-1",
            "usage": "position = myString.indexOf('abc')"
          },
          {
            "funcname": ".lastIndexOf",
            "description": "返回指定元素在字符串中最后一次出现的位置，如果未找到返回-1",
            "usage": "position = myString.lastIndexOf('abc')"
          },
          {
            "funcname": ".search",
            "description": "返回一个正则表达式在字符串中匹配到的位置",
            "usage": "position = myString.search(/abc/)"
          },
          {
            "funcname": ".slice",
            "description": "提取字符串的某个部分并返回一个新字符串",
            "usage": "newString = myString.slice(1, 4)"
          },
          {
            "funcname": ".split",
            "description": "将字符串分割成子串并返回一个字符串数组",
            "usage": "subStrings = myString.split('-')"
          },
          {
            "funcname": ".substring",
            "description": "返回字符串的一个子串，两个参数指定了子串的开始和结束位置",
            "usage": "newString = myString.substring(1, 4)"
          },
          {
            "funcname": ".substr",
            "description": "返回字符串的一个子串，两个参数分别指定了子串的开始位置和长度",
            "usage": "newString = myString.substr(1, 3)"
          },
          {
            "funcname": ".replace",
            "description": "将字符串中的指定内容替换为新内容",
            "usage": "newString = myString.replace('old', 'new')"
          },
          {
            "funcname": ".toUpperCase",
            "description": "将字符串转换为大写字母",
            "usage": "upperCaseString = myString.toUpperCase()"
          },
          {
            "funcname": ".toLowerCase",
            "description": "将字符串转换为小写字母",
            "usage": "lowerCaseString = myString.toLowerCase()"
          },
          {
            "funcname": ".trim",
            "description": "去除字符串两端的空白字符",
            "usage": "trimmedString = myString.trim()"
          },
          {
            "funcname": ".trimStart",
            "description": "去除字符串开头的空白字符",
            "usage": "trimmedString = myString.trimStart()"
          },
          {
            "funcname": ".trimEnd",
            "description": "去除字符串末尾的空白字符",
            "usage": "trimmedString = myString.trimEnd()"
          },
          {
            "funcname": ".replaceAll",
            "description": "将字符串中的所有指定内容替换为新内容",
            "usage": "newString = myString.replaceAll('old', 'new')"
          },
          {
            "funcname": ".startsWith",
            "description": "判断字符串是否以指定内容开头，返回true或false",
            "usage": "isStartWith = myString.startsWith('prefix')"
          },
          {
            "funcname": ".endsWith",
            "description": "判断字符串是否以指定内容结尾，返回true或false",
            "usage": "isEndWith = myString.endsWith('suffix')"
          }
        ]
      },
      {
        key: 'list',
        label: '集合操作函数',
        functions: [
          {
            "funcname": ".filter",
            "description": "创建一个由所有通过测试的元素组成的新数组",
            "usage": "filteredArray = myArray.filter(element => element > 5)"
          },
          {
            "funcname": ".map",
            "description": "创建一个新数组，结果是原数组中每个元素调用一个提供的函数后的返回值",
            "usage": "mappedArray = myArray.map(element => element * 2)"
          },
          {
            "funcname": ".reduce",
            "description": "对数组中的每个元素执行一个由您提供的reducer函数（升序执行），将其结果汇总为单个返回值",
            "usage": "total = myArray.reduce((accumulator, currentValue) => accumulator + currentValue, 0)"
          },
          {
            "funcname": ".reduceRight",
            "description": "对数组中的每个元素执行一个由您提供的reducer函数（降序执行），将其结果汇总为单个返回值",
            "usage": "total = myArray.reduceRight((accumulator, currentValue) => accumulator + currentValue, 0)"
          },
          {
            "funcname": ".from",
            "description": "通过给定的类数组或可迭代对象创建一个新数组",
            "usage": "newArray = Array.from(mySet)"
          },
          {
            "funcname": ".concat",
            "description": "用于合并两个或多个数组",
            "usage": "newArray = array1.concat(array2)"
          }
        ]
      },
    ]
  },
  {
    "language": "Go",
    "groups": [
      {
        "key": "sort",
        "label": "排序函数",
        "functions": [
          {
            "funcname": "sort.Ints",
            "description": "对整数切片进行排序",
            "usage": "sort.Ints(nums) // nums为整数切片"
          },
          {
            "funcname": "sort.Float64s",
            "description": "对浮点数切片进行排序",
            "usage": "sort.Float64s(nums) // nums为浮点数切片"
          },
          {
            "funcname": "sort.Strings",
            "description": "对字符串切片进行排序",
            "usage": "sort.Strings(strs) // nums为字符串切片"
          },
          {
            "funcname": "sort.Slice",
            "description": "对整数切片进行排序对任意切片进行排序，需要提供一个比较函数",
            "usage": "sort.Slice(people, func(i, j int) bool { return people[i] < people[j]}) // nums为任意切片"
          }
        ]
      },
      {
        "key": "search",
        "label": "搜索函数",
        "functions": [
          {
            "funcname": "sort.SearchInts",
            "description": "对已排序的切片进行二分搜索",
            "usage": "sort.SearchInts(nums, target) // 需要检查返回的位置是否包含目标值",
          },
          {
            "funcname": "sort.SearchStrings",
            "description": "对已排序的字符串切片进行二分搜索",
            "usage": "sort.SearchStrings(strs, target) // 需要检查返回的位置是否包含目标值",
          }
        ]
      },
      {
        "key": "math",
        "label": "数学函数",
        "functions": [
          {
            "funcname": ".Abs",
            "description": "返回一个数的绝对值",
            "usage": "result = math.Abs(-3.14)"
          },
          {
            "funcname": ".Ceil",
            "description": "返回大于或等于一个数的最小整数",
            "usage": "result = math.Ceil(3.14)"
          },
          {
            "funcname": ".Floor",
            "description": "返回小于或等于一个数的最大整数",
            "usage": "result = math.Floor(3.14)"
          },
          {
            "funcname": ".Sqrt",
            "description": "返回一个数的平方根",
            "usage": "result = math.Sqrt(9)"
          },
          {
            "funcname": ".Cbrt",
            "description": "返回一个数的立方根",
            "usage": "result = math.Sqrt(8)"
          },
          {
            "funcname": ".Pow",
            "description": "返回一个数的幂",
            "usage": "result = math.Pow(2, 3)"
          },
          {
            "funcname": ".Sin",
            "description": "返回一个角的正弦值",
            "usage": "math.Sin(math.Pi / 2)"
          },
          {
            "funcname": ".Cos",
            "description": "返回一个角的余弦值",
            "usage": "result = math.Cos(math.Pi / 2)"
          },
          {
            "funcname": ".Cosh",
            "description": "返回一个角的双曲余弦值",
            "usage": "result = math.Cosh(math.Pi / 2)"
          },
          {
            "funcname": ".Tan",
            "description": "返回一个角的正切值",
            "usage": "result = math.Tan(math.Pi / 2)"
          },
          {
            "funcname": ".Asin",
            "description": "返回一个数的反正弦值",
            "usage": "result = math.Asin(1)"
          },
          {
            "funcname": ".Acos",
            "description": "返回一个数的反余弦值",
            "usage": "result = math.Acos(1)"
          },
          {
            "funcname": ".Atan",
            "description": "返回一个数的反正切值",
            "usage": "result = math.Atan(1)"
          },
          {
            "funcname": ".Dim",
            "description": "返回两个数的差的最大值和零的最大值",
            "usage": "result = math.Dim(1, 2)"
          },
          {
            "funcname": ".Exp",
            "description": "返回一个数的指数函数值",
            "usage": "result = math.Exp(1)"
          },
          {
            "funcname": ".Exp2",
            "description": "返回2的一个数的幂",
            "usage": "result = math.Exp2(1)"
          },
          {
            "funcname": ".IsInf",
            "description": "判断一个数是否为正无穷或负无穷",
            "usage": "result = math.IsInf(1)"
          },
          {
            "funcname": ".IsNaN",
            "description": "判断一个数是否为非数值",
            "usage": "result = math.IsNaN(1)"
          },
          {
            "funcname": ".Log",
            "description": "返回一个数的自然对数",
            "usage": "result = math.Log(1)"
          },
          {
            "funcname": ".Log10",
            "description": "返回一个数的以10为底的对数",
            "usage": "result = math.Log10(1)"
          },
          {
            "funcname": ".Min",
            "description": "返回两个数中的最小值",
            "usage": "result = math.Min(1, 2)"
          },
          {
            "funcname": ".Max",
            "description": "返回两个数中的最大值",
            "usage": "result = math.Max(1, 2)"
          },
          {
            "funcname": ".Mod",
            "description": "返回两个数的余数",
            "usage": "result = math.Mod(1, 2)"
          },
          {
            "funcname": ".Modf",
            "description": "返回一个数的整数部分和小数部分",
            "usage": "result = math.Modf(1.2)"
          },
          {
            "funcname": ".Round",
            "description": "返回一个数四舍五入的结果",
            "usage": "result = math.Round(1.2)"
          }
        ]
      },
      {
        "key": "string",
        "label": "字符串处理函数",
        "functions": [
          {
            "funcname": ".Contains",
            "description": "检查字符串是否包含子串",
            "usage": "result = strings.Contains('abc', 'a')"
          },
          {
            "funcname": ".Count",
            "description": "计算子串在字符串中出现的次数",
            "usage": "result = strings.Count('abc', 'a')"
          },
          {
            "funcname": ".HasPrefix",
            "description": "检查字符串是否以指定的前缀开始",
            "usage": "result = strings.HasPrefix('abc', 'a')"
          },
          {
            "funcname": ".HasSuffix",
            "description": "检查字符串是否以指定的后缀结束",
            "usage": "result = strings.HasSuffix('abc', 'a')"
          },
          {
            "funcname": ".Index",
            "description": "返回子串在字符串中首次出现的位置，如果没有找到则返回-1",
            "usage": "result = strings.Index('abc', 'a')"
          },
          {
            "funcname": ".LastIndex",
            "description": "返回子串在字符串中最后出现的位置，如果没有找到则返回-1",
            "usage": "result = strings.LastIndex('abc', 'a')"
          },
          {
            "funcname": ".Replace",
            "description": "替换字符串中的子串",
            "usage": "result = strings.Replace('abc', 'a', 'b', 1)"
          },
          {
            "funcname": ".Split",
            "description": "根据分隔符将字符串分割为切片",
            "usage": "result = strings.Split('abc', 'a')"
          },
          {
            "funcname": ".Join",
            "description": "将字符串切片连接为一个字符串",
            "usage": "result = strings.Join(['a', 'b', 'c'], ',')"
          },
          {
            "funcname": ".ToLower",
            "description": "将字符串转换为小写",
            "usage": "result = strings.ToLower('abc')"
          },
          {
            "funcname": ".ToUpper",
            "description": "将字符串转换为大写",
            "usage": "result = strings.ToUpper('abc')"
          },
          {
            "funcname": ".TrimSpace",
            "description": "去除字符串两端的空白字符",
            "usage": "result = strings.TrimSpace('abc')"
          },
          {
            "funcname": ".Trim",
            "description": "去除字符串两端的空白字符去除字符串两端的指定字符",
            "usage": "result = strings.Trim('abc', 'a')"
          },
          {
            "funcname": ".TrimLeft",
            "description": "去除字符串左端的指定字符",
            "usage": "result = strings.TrimLeft('abc', 'a')"
          },
          {
            "funcname": ".TrimRight",
            "description": "去除字符串右端的指定字符",
            "usage": "result = strings.TrimRight('abc', 'a')"
          }
        ]
      },
      {
        "key": "list",
        "label": "集合操作函数",
        "functions": [
          {
            "funcname": "cap",
            "description": "获取切片的容量",
            "usage": "result = cap(nums)"
          },
          {
            "funcname": "append",
            "description": "向切片添加元素",
            "usage": "result = append(nums, 4, 5)"
          },
          {
            "funcname": "copy",
            "description": "复制切片",
            "usage": "result = copy(nums)"
          }
        ]
      }
    ]
  }
]