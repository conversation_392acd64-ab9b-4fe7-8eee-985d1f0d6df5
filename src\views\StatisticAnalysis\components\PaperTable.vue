<template>
  <div class="paper-table-anaylsis">
    <a-table :columns="columns" :row-key="(record:any) => record.id" :data-source="data">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <span>
            {{ record.type === 'regular' ? '指定试题' : '指定范围' }}
          </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <span @click="analyze(record)" style="color: #5478ee">分析</span>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const columns = [
  {
    title: '考试名称',
    width: 260,
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: '创建人',
    dataIndex: 'teacher',
    key: 'teacher',
    ellipsis: false
  },

  {
    title: '考试时间',
    width: 310,
    dataIndex: 'time',
    key: 'time',
    ellipsis: true
  },
  {
    title: '考试时长',
    dataIndex: 'duration',
    key: 'duration',
    align: 'right',
    ellipsis: false
  },
  {
    title: '题目数量',
    dataIndex: 'quesnum',
    key: 'quesnum',
    align: 'right',
    ellipsis: false
  },
  {
    title: '应考人数',
    dataIndex: 'candidates',
    key: 'candidates',
    align: 'right',
    ellipsis: false
  },
  {
    title: '实考人数',
    dataIndex: 'realstus',
    key: 'realstus',
    align: 'right',
    ellipsis: false
  },
  {
    title: '缺考人数',
    dataIndex: 'absentees',
    key: 'absentees',
    align: 'right',
    ellipsis: false
  },
  {
    title: '平均分',
    dataIndex: 'avgscore',
    key: 'avgscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '最高分',
    dataIndex: 'hscore',
    key: 'hscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '及格线',
    dataIndex: 'pscore',
    key: 'pscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '及格率',
    dataIndex: 'prate',
    key: 'prate',
    align: 'right',
    ellipsis: false
  },
  {
    title: '操作',
    fixed: 'right',
    key: 'action',
    width: 60
  }
]

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  width: {
    type: Number || String,
    default: '100%'
  },
  height: {
    type: Number || String,
    default: '100%'
  }
})

// const loading = ref(false)
// 分页配置项
// :pagination="paginationConfig"
//       @change="handleTableChange"
// const paginationConfig = ref({
//   current: 1,
//   pageSize: 10,
//   showSizeChanger: true,
//   total: 0,
//   pageSizeOptions: ['10', '20', '50', '100']
// })
// // table 页码,排序改变
// const handleTableChange = ({ pagination }: any) => {
//   // paginationConfig.value.current = pagination.current
//   // paginationConfig.value.pageSize = pagination.pageSize
// }

const router = useRouter()
const analyze = (record: any) => {
  router.push('/admin/statisticAnalysis/paperAnalysis?id=' + record.id)
}

const dynamicWidth = ref('100%')
const dynamicHeight = ref('100%')
watch(
  () => props.width,
  (val) => {
    if (typeof val === 'string') {
      dynamicWidth.value = val
    } else {
      dynamicWidth.value = val + 'px'
    }
  },
  {
    immediate: true
  }
)
watch(
  () => props.height,
  (val) => {
    if (typeof val === 'string') {
      dynamicHeight.value = val
    } else {
      dynamicHeight.value = val + 'px'
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less">
.paper-table-anaylsis {
  background: #fff;
  .ant-table {
    padding: 24px;
    width: calc(v-bind(dynamicWidth) - 24px);
    height: calc(v-bind(dynamicHeight) -70px);
  }

  .ant-table-thead > tr > th {
    font-weight: bold;
    &:first-child {
      padding-left: 8px !important;
    }
    background: #f1f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
}
</style>
