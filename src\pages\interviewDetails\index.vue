<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { getExamDetail } from '../questionManage/hooks/api'
import Detail from './components/detail.vue'
import Overview from './components/overview.vue'
import Timeline from './components/timeline.vue'

const route = useRoute()
const examDetail = ref<any>(null)
const router = useRouter()
onMounted(async () => {
  const res = await getExamDetail({
    resumeId: route.query.resumeId as string,
  })
  examDetail.value = res
})
</script>
    
<template>
  <div class="manual-mark-page-wrap">
    <div class="common-page-title-wrapper">
      <div class="title">
        <span @click="router.back()">人才库 / </span>
        <span>AI面试</span>
      </div>
    </div>
    <Overview v-if="examDetail" :exam-detail="examDetail" />
    <div class="grid px-[20px] h-[calc(100% - 200px)] relative overflow-hidden" style="grid-template-columns: 1fr 4fr;grid-gap: 0 16px;">
      <div class="bg-[#fff] rounded-[8px] shadow px-[24px] py-[19px]">
        <Timeline v-if="examDetail" :exam-detail="examDetail" />
      </div>
      <div class="h-[100%] overflow-auto">
        <Detail v-if="examDetail" :exam-detail="examDetail" />
      </div>
    </div>
  </div>
</template>
    
<style lang="less" scoped>
.shadow{
  box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.10);  
}
.bg-linear{
  background: linear-gradient(180deg,rgba(250,173,20,0.09), rgba(250,173,20,0.00) 98%);    
}
.post_card{
  background: #fff;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
}
.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-wrapper {
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  margin: 0px 20px 20px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }

  }

}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
</style>