{"_args": [["signal-exit@3.0.5", "D:\\code\\exam-system"]], "_development": true, "_from": "signal-exit@3.0.5", "_id": "signal-exit@3.0.5", "_inBundle": false, "_integrity": "sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8=", "_location": "/signal-exit", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "signal-exit@3.0.5", "name": "signal-exit", "escapedName": "signal-exit", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/execa", "/restore-cursor"], "_resolved": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632948384106&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "_spec": "3.0.5", "_where": "D:\\code\\exam-system", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "description": "when you want to fire an event no matter how a process exits.", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.0.10"}, "files": ["index.js", "signals.js"], "homepage": "https://github.com/tapjs/signal-exit", "keywords": ["signal", "exit"], "license": "ISC", "main": "index.js", "name": "signal-exit", "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version", "test": "tap --timeout=240 ./test/*.js --cov"}, "version": "3.0.5"}