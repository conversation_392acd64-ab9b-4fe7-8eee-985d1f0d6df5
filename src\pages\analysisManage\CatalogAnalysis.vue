<template>
  <div class="analysis">
    <div class="common-page-title">
      <span>题库分析</span>
    </div>
    <div class="analysis-content">
      <SubjectTree ref="treeRef" @select="handleTreeSelect"></SubjectTree>
      <div v-if="isOfficialSubject && store.getters.userInfo?.tryout" class="locked-box">
        <div class="locked">
          <img src="@/assets/images/locked.png" alt="" style="width: fit-content;" />
          <span class="desc">升级正式版可解锁</span>
        </div>
      </div>
      <div v-else class="main">
        <a-row :gutter="20">
          <a-col :span="12" style="height: 350px;">
            <div class="panel">
              <div class="panel-title">
                题型数量分布
                <a-tooltip
                  placement="top"
                  title="统计当前题库及其全部子题库的数据"
                  overlayClassName="light"
                >
                  <svg-icon
                    class="common-info-icon"
                    name="info2"
                    style="font-size: 14px;"
                  ></svg-icon>
                </a-tooltip>
              </div>
              <CatalogQuestionTypeChart
                :id="catalogId"
                class="panel-content"
              ></CatalogQuestionTypeChart>
            </div>
          </a-col>
          <a-col :span="12" style="height: 350px;">
            <div class="panel">
              <div class="panel-title">
                子题库题量分布
                <a-tooltip
                  placement="top"
                  title="统计当前题库下各一级子题库的数据"
                  overlayClassName="light"
                >
                  <svg-icon
                    class="common-info-icon"
                    name="info2"
                    style="font-size: 14px;"
                  ></svg-icon>
                </a-tooltip>
              </div>
              <CatalogQuestionAmountProportionChart
                :id="catalogId"
                class="panel-content"
              ></CatalogQuestionAmountProportionChart>
            </div>
          </a-col>
        </a-row>
        <a-row
          style="flex: 1; min-height: 0; overflow: hidden; margin-top: 20px;"
        >
          <a-col :span="24" style="height: 100%;">
            <div class="panel">
              <div class="panel-title">
                子题库引用次数与题量分布
                <a-tooltip
                  placement="top"
                  title="统计当前题库下各一级子题库的数据"
                  overlayClassName="light"
                >
                  <svg-icon
                    class="common-info-icon"
                    name="info2"
                    style="font-size: 14px;"
                  ></svg-icon>
                </a-tooltip>
              </div>
              <CatalogFrequencyAndQuestionAmountChart
                style="overflow-y: auto;"
                :key="catalogId"
                :id="catalogId"
                class="panel-content"
              ></CatalogFrequencyAndQuestionAmountChart>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import SubjectTree from "@/pages/questionManage/SubjectTree.vue";
import CatalogQuestionTypeChart from "@/pages/analysisManage/components/CatalogQuestionTypeChart.vue";
import CatalogQuestionAmountProportionChart from "@/pages/analysisManage/components/CatalogQuestionAmountProportionChart.vue";
import CatalogFrequencyAndQuestionAmountChart from "./components/CatalogFrequencyAndQuestionAmountChart.vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import { useStore } from "vuex";

const store = useStore();

const treeRef = ref<InstanceType<typeof SubjectTree>>();
const catalogId = ref("");
const isOfficialSubject = ref(
  store.getters.userInfo.is_official_cert ? true : false
);
function handleTreeSelect({ id, is_official_cert }: any) {
  catalogId.value = id;
  isOfficialSubject.value = is_official_cert;
}
</script>

<style lang="less" scoped>
.analysis {
  padding: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .analysis-content {
    display: flex;
    flex: 1;
    min-height: 0;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    background: #ffffff;
    overflow: hidden;

    .locked-box {
      height: 100%;
      flex: 1;

      .locked {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: center;
        align-items: center;

        .desc {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          line-height: 20px;
        }
      }
    }

    .main {
      flex: 1;
      min-width: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
    }

    .panel {
      background-color: #f5f5f5;
      height: 100%;
      padding: 20px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-title {
        font-size: 20px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        .anticon {
          margin-left: 6px;
        }
      }

      .panel-content {
        flex: 1;
        min-height: 0;
        overflow: hidden;
      }
    }
  }
}
</style>