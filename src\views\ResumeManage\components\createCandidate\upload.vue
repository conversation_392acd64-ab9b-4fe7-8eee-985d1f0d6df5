<script setup lang="ts">
import { getResumeTaskList, uploadFile } from '@/pages/questionManage/hooks/api'
import { debounce } from '@/utils/common'
import { message } from 'ant-design-vue'
import { ElLoading } from 'element-plus'

const props = defineProps({
  postId: {
    type: String,
    default: '',
  },
})
const fileList = defineModel() as any
const backEndFileList = defineModel('backEndFileList') as any
watch(() => fileList.value.length, (val) => {
  if (!props.postId && val > 0) {
    message.warning('请先选择岗位')
    fileList.value = []
    return false
  }
})
const handleFileChange = debounce(async () => {
  if (!props.postId) {
    fileList.value = []
    return false
  }
  const loading = ElLoading.service({
    lock: true,
    text: '上传中',
    background: 'rgba(0, 0, 0, 0.3)',
  })
  const formData = new FormData()
  formData.append('positionId', props.postId)
  const filesToUpload = fileList.value.map((item: any) => item.raw)
  filesToUpload.forEach((file: any) => {
    formData.append('files', file)
  })
  const uploadRes = await uploadFile(formData)
  message.success(uploadRes.message || '文件上传成功')
  const currentTaskIds = uploadRes.results.map((item: any) => item.taskId)
  fileList.value = []
  const res = await getResumeTaskList({
    positionId: props.postId,
    taskIds: currentTaskIds,
    includeDeleted: false,
    pageNo: 1,
    pageSize: 10,
  })
  backEndFileList.value = res.records
  poolingTaskList()
  loading.close()
}, 800)
const intervalId = ref<any>('')
function poolingTaskList() {
  intervalId.value = setInterval(async () => {
    const res = await getResumeTaskList({
      positionId: props.postId,
      pageNo: 1,
      pageSize: 10,
    })
    backEndFileList.value = res.records
  }, 1800)
}
function beforeUpload() {
  if (!props.postId) {
    message.warning('请先选择岗位')
    return false
  }
}

defineExpose({
  clear: () => {
    clearInterval(intervalId.value)
  },
})

onUnmounted(() => {
  clearInterval(intervalId.value)
})
</script>

<template>
  <!-- :disabled="!postId" -->
  <el-upload
    v-model:file-list="fileList"
    drag
    name="file"
    class="upload-drag-container mt-[16px]"
    :before-upload="beforeUpload"
    :custom-request="() => {}"
    :show-file-list="false"
    accept=".pdf"
    :show-upload-list="false"
    :auto-upload="false"
    :multiple="true"
    :on-change="handleFileChange"
  >
    <div class="ant-upload-drag-icon flex flex-nowrap justify-center items-center relative">
      <svg class="mr-[16px]" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <title>Pdf</title>
        <g id="问卷管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="面试管理-人才库-添加候选人01" transform="translate(-200.000000, -201.000000)" fill-rule="nonzero">
            <g id="弹窗备份-2" transform="translate(81.000000, 51.000000)">
              <g id="编组-3" transform="translate(32.000000, 134.000000)">
                <g id="Pdf" transform="translate(87.000000, 16.000000)">
                  <rect id="矩形" fill="#000000" opacity="0" x="0" y="2.56773721e-05" width="32" height="32" />
                  <path id="路径" d="M28.182838,28.9524007 C28.182838,29.7939669 27.5005975,30.4762132 26.6590312,30.4762132 L5.32571875,30.4762132 C4.48414034,30.4762132 3.80190625,29.7939791 3.80190625,28.9524007 L3.80190625,1.52386943 C3.80190625,1.11972381 3.96243837,0.73212811 4.24820955,0.446351074 C4.53398073,0.160574037 4.92157313,0 5.32571875,0 L18.4152187,0 C18.8202481,-0.00232402284 19.209548,0.1566749 19.497125,0.441900677 L27.7409375,8.68580693 C28.0261556,8.97337623 28.1851539,9.36266343 28.182838,9.76768193 L28.182838,28.9524319 L28.182838,28.9524007 Z" fill="#EBECF0" />
                  <path id="路径" d="M28.1828438,28.9524007 L28.1828438,30.4762132 C28.1828438,31.3177672 27.5006165,31.9999912 26.6590625,32.0000257 L5.32571875,32.0000257 C4.48414034,32.0000257 3.80190625,31.3177916 3.80190625,30.4762132 L3.80190625,28.9524007 C3.80190625,29.7939791 4.48414034,30.4762132 5.32571875,30.4762132 L26.6590313,30.4762132 C27.0631715,30.4762132 27.4507591,30.3156691 27.7365294,30.0298988 C28.0222997,29.7441286 28.1828438,29.3565409 28.1828438,28.9524007 L28.1828438,28.9524007 Z" fill="#C1C7D0" />
                  <path id="路径" d="M0.75428125,16.7619007 L31.2304375,16.7619007 L31.2304375,24.3809944 C31.2304375,25.2225728 30.5482034,25.9048069 29.706625,25.9048069 L2.278125,25.9048069 C1.43654659,25.9048069 0.75428125,25.2225728 0.75428125,24.3809944 L0.75428125,16.7619007 L0.75428125,16.7619007 Z" fill="#FF5630" />
                  <path id="形状" d="M3.801875,16.7619319 L3.801875,13.7143382 L0.7543125,16.7619007 L3.8019375,16.7619007 L3.801875,16.7619319 Z M28.1828125,16.7619319 L28.2133125,13.7143382 L31.2456875,16.7619319 L28.1828125,16.7619319 Z" fill="#DE350B" />
                  <path id="形状" d="M8.3733125,18.2857444 L11.02475,18.2857444 C11.5097478,18.263063 11.9830175,18.4391665 12.3352188,18.7733694 C12.6733502,19.1116712 12.8555267,19.5753767 12.8380938,20.0533694 C12.8536842,20.531046 12.6717853,20.9940451 12.3352188,21.3333694 C11.9705132,21.6787027 11.480713,21.8603095 10.9790313,21.8362132 L9.45521875,21.8362132 L9.45521875,24.2286194 L8.3733125,24.2286194 L8.3733125,18.2857444 Z M9.39425,20.8457444 L10.7657188,20.8457444 C11.0491948,20.8680315 11.3323252,20.7985828 11.5733125,20.6476507 C11.7487967,20.4896671 11.8392111,20.2579873 11.817125,20.0229007 C11.817125,19.5047965 11.4768125,19.2457444 10.7961875,19.2457444 L9.39428125,19.2457444 L9.39425,20.8457444 Z M13.58475,18.3771819 L15.992375,18.3771819 C16.6910116,18.3477008 17.3647082,18.6396352 17.8209375,19.1695569 C18.3029123,19.7727764 18.5468933,20.5318272 18.5066563,21.3029007 C18.5402322,22.0823652 18.3093152,22.8502895 17.8514063,23.4819632 C17.4150521,24.0664453 16.7214449,24.4018695 15.9923438,24.3809944 L13.58475,24.3809944 L13.58475,18.3771819 Z M14.6056875,23.3448069 L15.992375,23.3448069 C16.4325584,23.3606925 16.8530389,23.1618137 17.12,22.8114632 C17.4135112,22.3768751 17.5529935,21.8564956 17.5161875,21.3333694 C17.5669611,20.7901734 17.4149768,20.2473979 17.0895,19.8095569 C16.7936617,19.4726553 16.3644661,19.2831376 15.9161875,19.2914632 L14.6056875,19.2914632 L14.6056875,23.3448069 Z M23.6114063,19.3371819 L20.457125,19.3371819 L20.457125,20.7543069 L23.6114063,20.7543069 L23.6114063,21.7295569 L20.457125,21.7295569 L20.457125,24.2895569 L19.4361875,24.2895569 L19.4361875,18.3467132 L23.6114063,18.3467132 L23.6114063,19.3371819 Z" fill="#FFFFFF" />
                  <path id="路径" d="M28.1828125,9.76765068 L28.1828125,9.98099443 L19.9390625,9.98099443 C19.0974841,9.98099443 18.41525,9.29876033 18.41525,8.45718193 L18.41525,3.125e-05 C18.8202793,-0.00229277284 19.2095793,0.15670615 19.4971562,0.441931927 L27.7562187,8.68577568 C28.03587,8.97581524 28.1892738,9.36480232 28.182875,9.76765068 L28.1828125,9.76765068 Z" fill="#C1C7D0" />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
      <div>
        <div class="ant-upload-text text-start text-[rgba(0,0,0,0.25)]">
          将文件拖到此处上传，或者您可以点击上传
        </div>
        <div class="ant-upload-text text-start text-[rgba(0,0,0,0.45)]">
          请先选择应聘岗位，再上传简历
        </div>
      </div>
    </div>
  </el-upload>
</template>

<style scoped lang="less">
.upload-drag-container{
  :deep(.el-upload-dragger){
    padding: 20px;
  }
}
</style>