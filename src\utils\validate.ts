import { RuleObject } from 'ant-design-vue/es/form/interface'
import { registerCheck } from '@/api/login'
import moment from 'moment'
import { QuestionEnum } from '@/models/questionModel'

// 验证用户名
export const checkUsername = async (rule: RuleObject, value: string) => {
    let isReg =
        /^[\u4e00-\u9fa5]|[a-zA-Z]$/.test(value) &&
        /^(?![·\-_\s])(?!.*[·\-_\s]$)/.test(value) &&
        /^[·\-\s\w\u4e00-\u9fa5]*$/.test(value) &&
        !/[0-9]/.test(value) &&
        !/[\u4e00-\u9fa5][a-zA-Z]{1}|[a-zA-Z][\u4e00-\u9fa5]{1}/.test(value) &&
        !/[·_\-\s]{2}/.test(value) &&
        !/[\u4e00-\u9fa5]\s[\u4e00-\u9fa5a]/.test(value) &&
        !/[a-z]\s{2,}[a-z]/i.test(value)
    if (value.trim() === '') {
        return Promise.reject('请输入真实姓名')
    } else if (!isReg) {
        return Promise.reject('真实姓名不合法')
    } else {
        return Promise.resolve()
    }
}

/** 验证手机号 */
export const checkPhone = (rule: any, value?: string) => new Promise((resolve, reject) => {
    if (!value || value?.trim() === '') return reject('请输入手机号')

    let isReg = /^1[34578]\d{9}$/.test(value)
    if (!isReg) return reject('手机号格式不正确')

    resolve('校验通过')
})

function isValidEmail(email: string) {
    // 判断是否仅包含一个 @ 符号
    if (email.indexOf('@') === -1 || email.indexOf('@') !== email.lastIndexOf('@')) {
        return false;
    }
    // 分割邮箱地址为用户名和域名部分
    const [username, domain] = email.split('@');
    // 判断用户名部分是否只包含有效字符
    const usernameRegex = /^[a-zA-Z0-9._-]+$/;
    if (!usernameRegex.test(username)) {
        return false;
    }
    // 判断域名部分是否是有效的域名格式
    const domainParts = domain.split('.');
    if (domainParts.length < 2) {
        return false;
    }
    for (const part of domainParts) {
        if (!/^[a-zA-Z0-9-]+$/.test(part)) {
            return false;
        }
    }
    return true;
}

// 验证邮箱
export const checkEmail = (fromRegiter = false) => async (rule: RuleObject, value: string) => {
    // const email = /^([a-zA-Z0-9]+[_|\_|\.|-]?)*[a-zA-Z0-9.]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
    if (value === '') {
        return Promise.reject('请输入邮箱')
    } else if (value.length > 64) {
        return Promise.reject('邮箱地址超过最大长度')
    } else if (!isValidEmail(value)) {
        return Promise.reject('邮箱格式不正确')
    } else {
        // const { flag: hasRegistered } = await registerCheck({ type: 'email', value }) as any
        // if (hasRegistered) {
        //     if (fromRegiter) {
        //         return Promise.reject('邮箱已存在')
        //     } else {
        //         return Promise.resolve()
        //     }
        // } else {
        //     if (fromRegiter) {
        //         return Promise.resolve()
        //     } else {
        //         return Promise.reject('邮箱不存在')
        //     }
        // }
    }
}

export const checkPwdLength = (value: string) => {
    return value.length >= 8 && value.length <= 26
}

export const checkPwdDiversity = (value: string) => {
    // 是否包含数字
    const reg_digit = /^.*[0-9]+.*$/
    // 是否包含大写字母
    const reg_lowcase = /^.*[a-z]+.*$/
    // 是否包含小写字母
    const reg_upcase = /^.*[A-Z]+.*$/
    // 是否包含特殊字符
    const reg_special = /((?=[\x21-\x7e]+)[^A-Za-z0-9])/
    return (
        (reg_digit.test(value) && reg_lowcase.test(value) && reg_upcase.test(value) && reg_special.test(value)) ||
        (!reg_digit.test(value) && reg_lowcase.test(value) && reg_upcase.test(value) && reg_special.test(value)) ||
        (reg_digit.test(value) && !reg_lowcase.test(value) && reg_upcase.test(value) && reg_special.test(value)) ||
        (reg_digit.test(value) && reg_lowcase.test(value) && !reg_upcase.test(value) && reg_special.test(value)) ||
        (reg_digit.test(value) && reg_lowcase.test(value) && reg_upcase.test(value) && !reg_special.test(value))
    )
}

// 是否含有非ASCII字符，
export const checkNoASCII = (value: string) => {
    // return /[\u4e00-\u9fff]/.test(value) // 匹配中文
    return /[^\x00-\x7F]/g.test(value)
}

// 是否含有空格
export const checkSpace = (value: string) => {
    return /\s/.test(value)
}

// 验证密码
export const checkPassword = (formState: { password: string, oldPasswd?: string }) =>
    async (rule: RuleObject, value: string) => {
        if (!value) return Promise.reject('请输入密码')
        if (value === formState.oldPasswd) return Promise.reject('新密码不能与原密码一致')
        if (!checkPwdLength(value)) return Promise.reject('密码长度应为8~26字符')
        if (checkNoASCII(value)) return Promise.reject('密码不能含有特殊字符')
        if (!checkPwdDiversity(value)) return Promise.reject('至少包含大写字母、小写字母、数字、字符中的三种')
        return Promise.resolve()
    }

// 验证确认密码
export const checkConfirmPwd = (formState: { password: string, oldPasswd?: string }) =>
    async (rule: RuleObject, value: string) => {
        if (formState.password === '') return Promise.resolve()
        if (value === '') return Promise.reject('请再次输入密码')
        if (value !== formState.password) return Promise.reject('两次输入的密码不一致')
        return Promise.resolve()
    }

// 验证身份证号码
export const checkCardNo = async (rule: RuleObject, value: string) => {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    if (value === '') {
        return Promise.reject('请输入身份证号')
    } else if (!reg.test(value)) {
        return Promise.reject('身份证输入不合法')
    } else {
        const born = value.slice(6, 14)
        const isValid = moment(born, 'YYYYMMDD', true).isValid()
        const isValid2 = moment(born).isBefore()
        if (!isValid || !isValid2) {
            return Promise.reject('身份证日期输入有误')
        } else {
            const data = await registerCheck({ type: 'id_card_num', value: value }) as any
            if (data.flag) {
                return Promise.reject('身份证号已存在')
            } else {
                return Promise.resolve()
            }
        }
    }
}

// 验证考试名称
export const checkPaperName = async (rule: RuleObject, value: string) => {
    if (value === '') return Promise.reject('请输入考试名称')
    if (value.length > 24) return Promise.reject('考试名称超过24个字符')
    if (/[\\/:*?"<>|]/.test(value)) return Promise.reject('不能包含下列任何字符：\/:*?"<>|')
    return Promise.resolve()
}

// 验证题干内容
export function checkBody(formState: any) {
    return async (rule: Rule, value: string) => {
        if (value.trim() === '') return Promise.reject('请输入题干内容')

        if (formState.type === QuestionEnum['填空题'] && !value.includes('__')) return Promise.reject('填空题题干内容中至少包含一个空位')

        return Promise.resolve()
    }
}

// 验证分值
export function checkScore(rule: Rule, value: number) {
    if (value <= 0) return Promise.reject('最小允许1分')
    if (value > 100) return Promise.reject('最大允许100分')
    return Promise.resolve()
}

// 验证选项
export function checkOptions(formState: any) {
    return async (rule: Rule, value: any[]) => {
        if (!value?.length) return Promise.reject('请添加选项')

        if (value.some((item: any) => item.content.trim() === '')) return Promise.reject('选项内容都不能为空')

        let arr = Array.from(new Set(value.map((item: any) => item.content.trim())))
        if (arr.length < value.length) return Promise.reject('选项内容不能相同')

        if (formState.type === QuestionEnum['单选题'] && !formState.answer) return Promise.reject('请选择正确答案')

        if (formState.type === QuestionEnum['多选题'] && formState.answer.length < 2) return Promise.reject('请选择至少两个正确答案')

        return Promise.resolve()
    }
}

export async function checkQuestionForm(formState: any) {
    await checkBody(formState)(null, formState.body)
    await checkScore(null, formState.score)
    if ([QuestionEnum['单选题'], QuestionEnum['多选题']].includes(formState.type)) {
        await checkOptions(formState)(null, formState.options)
    }
    if ([QuestionEnum['填空题'], QuestionEnum['问答题']].includes(formState.type)) {
        let word = formState.type === QuestionEnum['填空题'] ? '答案' : '得分点'

        if (!Array.isArray(formState.answer)) {
            throw new Error(word + '格式错误');
        }
        if (formState.answer.some((item: any) => !item.keyword?.length)) {
            throw new Error(word + '内容不能为空');
        }
    }
}