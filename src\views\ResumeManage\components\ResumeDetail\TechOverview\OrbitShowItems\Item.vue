<script setup lang="ts">
interface Props {
  // 尺寸大小，支持5种尺寸
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  // 边框半径类型，支持圆形和圆角矩形
  borderType?: 'circle' | 'rounded'
  // 样式类型，支持3种不同的背景和阴影组合
  styleType?: 'blue' | 'pink' | 'green'
}

withDefaults(defineProps<Props>(), {
  size: 'xl',
  borderType: 'circle',
  styleType: 'blue',
})

// 尺寸映射表，从最大72*72到最小14*14
const sizeMap = {
  xl: 72,
  lg: 56,
  md: 42,
  sm: 28,
  xs: 14,
}

// 边框半径映射
const borderRadiusMap = {
  circle: '100%',
  rounded: '12px',
}

// 样式类型映射
const styleTypeMap = {
  blue: {
    color: '#312E7B',
    background: 'linear-gradient(43deg, #a0b4f1 22%, #cdd7ff 75%)',
    boxShadow: '0px 5px 14px 0px rgba(60, 99, 227, 0.40), 4px -8px 7px 1px #9cb5ff inset, 2px 4px 5px 0px rgba(254, 234, 255, 0.65) inset',
  },
  pink: {
    color: '#6634E1',
    background: 'linear-gradient(43deg, #f1a0db 22%, #ffedd9 75%)',
    boxShadow: '0px 5px 14px 0px rgba(162, 60, 227, 0.20), 4px -8px 7px 1px #ff9cdc inset, 2px 4px 6px 0px rgba(255, 255, 255, 0.92) inset',
  },
  green: {
    color: '#2B8547',
    background: 'linear-gradient(211deg, #b7f2d8 18%, #3cd0b4 90%)',
    boxShadow: '0px 5px 14px 0px rgba(60, 99, 227, 0.20), 4px -8px 7px 1px #34d5bf inset, 2px 3px 5px 1px rgba(232, 247, 255, 0.81) inset',
  },
}
</script>

<template>
  <div 
    class="orbit-item"
    :style="{
      width: `${sizeMap[size]}px`,
      height: `${sizeMap[size]}px`,
      borderRadius: borderRadiusMap[borderType],
      background: styleTypeMap[styleType].background,
      boxShadow: styleTypeMap[styleType].boxShadow,
      color: styleTypeMap[styleType].color,
    }"
  >
    <slot />
  </div>
</template>

<style scoped>
.orbit-item {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}
</style>
