<template>
  <a-form
    class="form"
    ref="basicInfoFormRef"
    :model="formState"
    :hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
  >
    <a-form-item label="考试名称" name="name">
      <a-input v-model:value="formState.name" class="form-input" autocomplete="off" />
    </a-form-item>
    <a-form-item name="uniexam">
      <template #label>
        <span style="margin-right: 4px;">是否统考</span>
        <a-tooltip placement="right" overlayClassName="light">
          <template #title>
             <!-- 信息分段 -->
            <span style="display: block;">1. 统考：常规考试。所有人遵循统一的开始答题时间和交卷时间 </span>
            <span style="display: block;">2.非统考：考生可以在试卷有效期内的任意时间开始考试，所有人遵循统一的答题时长</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.uniexam">
        <a-radio value="1">是</a-radio>
        <a-radio value="0">否</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item>
      <template #label>
        <span style="margin-right: 4px;">单题限时</span>
        <a-tooltip placement="right" overlayClassName="light">
          <template #title>单题限时指每小题都限制答题时间，作答后不可返回修改答案</template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.individualTiming" @change="handleIndividualChange">
        <a-radio :value="1">是</a-radio>
        <a-radio :value="0">否</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="考试时长(分钟)" name="duration" v-if="formState.individualTiming != '1'">
      <a-input-number v-model:value="formState.duration" :min="1" :max="43200"  class="form-input" autocomplete="off" />
    </a-form-item>
    <a-form-item label="考试开始时间" name="startTime">
      <template v-if="formState.uniexam == '1'">
        <a-date-picker
          v-model:value="formState.startTime"
          format="YYYY-MM-DD HH:mm"
          :disabled-date="disabledStartDate"
          :disabled-time="disabledDateTime"
          placeholder="选择考试开始时间"
          :show-time="{
            defaultValue: defaultTime,
          }"
          @change="selectedStartDate"
        />

        <span style="margin-left: 8px;font-size: 12px;" v-if="endTime && formState.individualTiming == '0'">至 {{ endTime }}</span>
      </template>
      <a-range-picker
        v-else
        class="my_range_picker"
        dropdownClassName="rangePickerDropdown" 
        v-model:value="startEndTime"
        :disabled-date="disabledStartDate"
        :disabled-time="disabledDateTime"
        format="YYYY-MM-DD HH:mm"
        :placeholder="['选择最早开始时间', '选择最晚开始时间']"
        :allowEmpty="[false, true]"
        :show-time="{
          defaultValue: defaultTimeRange,
        }"
        @change="selectedStartRangeDate"
      >
        <template #renderExtraFooter v-if="isCurrentTimeShow">
          <span style="cursor: pointer;" @click.capture.prevent="getCurrentTime">此刻</span>
        </template>
      </a-range-picker>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'
import { checkPaperName } from '@/utils/validate'
import emitter from '@/utils/bus'

const props = defineProps<{
  paperInfo?: any
}>()

const emits = defineEmits<{
  (e: 'change', value: any): void
}>()

const basicInfoFormRef = ref()
const formState = ref({
  name: '',
  uniexam: '1',
  duration: 120,
  type: 'regular',
  startTime: ref<Dayjs>(),
  endTime: ref<Dayjs>(),
  /** 是否单题限时 0不限时  1限时 */
  individualTiming: 0
})
watch(() => props.paperInfo, (val) => {
  // 编辑、克隆回显
  Object.keys(formState.value).forEach((key) => {
    formState.value[key] = val[key]
  })
  formState.value.individualTiming = val.individualTiming ? parseInt(val.individualTiming) : 0
  formState.value.uniexam = val.uniexam.toString()
  if (val.startTime && val.endTime) {
    startEndTime.value = [dayjs(val.startTime), dayjs(val.endTime)]
  }
})


//使用事件总线通信
function handleIndividualChange(event: any) {
  emitter.emit('paper-isindividualtime-swich', event.target.value)
}

// 考试结束时间
const endTime = computed(() => {
  // 如果是统考且有考试时长和开始时间
  if (formState.value.uniexam == '1' && formState.value.duration && formState.value.startTime) {
    formState.value.endTime = dayjs(formState.value.startTime).add(formState.value.duration, 'minutes').format('YYYY-MM-DD HH:mm')
    return dayjs(formState.value.startTime).add(formState.value.duration, 'minutes').format('YYYY-MM-DD HH:mm')
  } else {
    return formState.value.endTime
  }
})

const range = (start: number, end: number) => {
  const result = []

  for (let i = start; i < end; i++) {
    result.push(i)
  }

  return result
}

const canNext = computed(
  () => {
    if (formState.value.individualTiming == '1')
      return !!(formState.value.name && formState.value.startTime)
    else
      return !!(formState.value.name && formState.value.duration && formState.value.startTime)
  }
)

const checkDuration = async (rule: RuleObject, value: 0) => {
  // 如果是单题限时则不做总时长的校验
  if (formState.value.individualTiming == '1')
    return Promise.resolve()
  const reg = /^[1-9][0-9]{0,}$/
  if (value == 0) {
    return Promise.reject('请输入考试时长')
  } else if (!reg.test(value)) {
    return Promise.reject('输入格式不正确')
  } else {
    return Promise.resolve()
  }
}

// '请选择考试截至时间'
function checkEndTime(rule: RuleObject, value: string) {
  if (formState.value.individualTiming == '1')
    return Promise.resolve()
  if (!value) 
    return Promise.reject('请选择考试截至时间')
  else 
    return Promise.resolve()
}

// 定义规则
const rules = {
  name: [{ required: true, validator: checkPaperName, trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择考试开始时间', trigger: 'blur' }],
  endTime: [{ required: true, message: checkEndTime, trigger: 'blur' }],
  duration: [{ required: true, validator: checkDuration, trigger: 'blur' }],
  type: [{ required: true, message: '请选择试卷类型', trigger: 'blur' }]
}

const disabledStartDate = (current: Dayjs) => {
  return current && current < dayjs().endOf('day').subtract(1, 'day')
}
const disabledDateTime = () => {
  return {
    disabledHours: () => range(0, 0),
    disabledMinutes: () => range(0, 0),
    disabledSeconds: () => []
  }
}
const defaultTime = dayjs('09:00', 'HH:mm')
const defaultTimeRange = [dayjs('09:00', 'HH:mm'), dayjs('09:00', 'HH:mm')]

const startEndTime = ref<[Dayjs?, Dayjs?]>([])

// “此刻”（非统考考试开始时间才显示）
function getCurrentTime() {
  let startTimeEle = document.querySelector('.my_range_picker')?.querySelector('.ant-picker-input-active')?.querySelector('input[id=form_item_startTime]')
  if (startTimeEle) {
    if(startEndTime.value === null) {
      // 点击清空icon，会将值赋为null
      startEndTime.value = [dayjs()]
    } else {
      startEndTime.value[0] = dayjs()
    }
  }
}
const isCurrentTimeShow = ref(false)
function myRangePickerClickHandler() {
  let startTimeEle = document.querySelector('.my_range_picker')?.querySelector('.ant-picker-input-active')?.querySelector('input[id=form_item_startTime]')
  isCurrentTimeShow.value = !!startTimeEle
}
onMounted(() => {
  document.addEventListener('click', myRangePickerClickHandler)
})
onUnmounted(() => {
  document.removeEventListener('click', myRangePickerClickHandler)
})


const selectedStartDate = (value: Dayjs, tring: string) => {
  if (value) {
    formState.value.startTime = value.second(0).millisecond(0)
    formState.value.endTime = dayjs(formState.value.startTime).add(formState.value.duration, 'minutes').format('YYYY-MM-DD HH:mm')
  } else {
    formState.value.startTime = null as unknown as Dayjs
    formState.value.endTime = null as unknown as Dayjs
  }
  startEndTime.value = [value, ]
}

const selectedStartRangeDate = (value: [Dayjs, Dayjs]) => {
  if (value) {
    formState.value.startTime = value[0]?.second(0).millisecond(0)
    formState.value.endTime = value[1]?.second(0).millisecond(0)
  } else {
    formState.value.startTime = null as unknown as Dayjs
    formState.value.endTime = null as unknown as Dayjs
  }
}

function validateForm() {
  return new Promise((resolve, reject) => {
    if ( formState.value.name !== '考前流程模拟') {
      if (dayjs(formState.value.startTime).isValid() && dayjs(formState.value.startTime).isBefore(dayjs())) {
        reject('请选择有效考试开始时间')
        return message.error('请选择有效考试开始时间')
      } else if (formState.value.uniexam === '0' && !formState.value.endTime) {
        // 非统考需要设置结束时间
        reject('请选择考试结束时间')
        return message.error('请选择考试结束时间')
      }
    }
    return basicInfoFormRef.value.validate().then(() => {
      emits('change', formState.value)
      resolve('')
    })
  })
}

defineExpose({
  canNext,
  formState,
  validateForm,
})
</script>

<style lang="less" scoped>
.form {
  margin-bottom: auto;
  :deep(.ant-col) {
    width: 90px;
  }
  :deep(.ant-input) {
    width: 220px;
    height: 32px;
    font-size: 12px;
    border-radius: 8px;
  }
  :deep(label) {
    font-size: 12px;
  }
  :deep(.form-input) {
    width: 220px;
    font-size: 12px;
    border-radius: 8px;
    .ant-input-number-input {
      font-size: 12px;
    }
  }
  .ant-picker {
    width: 220px;
    height: 32px;
    border-radius: 8px;
    :deep(.ant-picker-input > input) {
      font-size: 12px;
    }

    &.ant-picker-range {
      width: 340px;
    } 
  }
  .type-cards {
    display: flex;

    .item {
      width: 240px;
      background: #ffffff;
      border: 1px solid #b4b5ba;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      margin-right: 16px;
      .item-name {
        font-size: 16px;
        font-weight: bold;
        padding: 24px 0 12px;
      }
      .type-desc p {
        padding: 0 12px;
        text-align: center;
        font-size: 12px;
        color: #b5b5b7;
      }
    }
    .active {
      background: #f1f4fe;
      border: 1.5px solid #5377ee;
      .type-desc p {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
  // :deep(.ant-picker-footer){
  //   background-color: #5377ee;
  // }
</style>
<style lang="less">
.rangePickerDropdown{
  .ant-picker-range-wrapper
    .ant-picker-panel-container
      .ant-picker-footer {
        display: flex;
        .ant-picker-ranges {
          margin-left: auto;
        }
      }
}
</style>