<template>
  <div class="test-question-display">
    <div class="test-question-stem tool-bar" v-if="$slots['right-top-icon']">
      <div style="display: flex;">
        <span v-if="showType" class="type">{{ InterviewQuestionEnum[questionDetail.type] }}</span>
      </div>
      <slot name="right-top-icon"></slot>
    </div>
    <div class="test-question-stem" :class="{ deleted }">
      <span v-if="showType && !$slots['right-top-icon']" class="type">{{ InterviewQuestionEnum[questionDetail.type] }}</span>
      <slot name="serialNumber"></slot>
      <div class="body" :class="{ deleted }" v-html="getContent()" @click="$event => findImageAndPreview($event)"></div>
    </div>
    <template v-if="!deleted">
      <div class="test-question-content">
        <template v-if="questionDetail.type <= 2">
          <OptionsBox
            v-if="print"
            :options="questionDetail.options"
            :renderFn="(content: string) => questionDetail.complicatedediting ? content : transferStr(content)"
          />
          <div class="test-question-options" v-else>
            <template v-for="option in questionDetail.options" :key="option.value">
              <div class="item-option">
                <option-btn
                  v-if="optionLetterType === 'button'"
                  class="option-btn"
                  :option="option.value"
                  :class="getDynamicClass(option.value)"
                />
                <span v-else :class="[showCorrectAnswer && judgeCorrentOption(option.value) ? 'tag' : '']" style="margin-right: 14px">{{ option.value }}.</span>
                <div
                  :class="['option-content', showCorrectAnswer && judgeCorrentOption(option.value) ? 'tag' : '']"
                  v-html="
                    questionDetail.complicatedediting ? option.content : transferStr(option.content)
                  "
                  @click="$event => findImageAndPreview($event)"
                ></div>
                <span
                  class="correct-answer-tag"
                  v-if="showCorrectAnswer && judgeCorrentOption(option.value)"
                  >正确答案</span
                >
              </div>
            </template>
          </div>
        </template>
        <template v-if="questionDetail.type === 3">
          <div v-if="showQuestionPointsAndBasis">
            <div class="common-kv-item">
              <span class="common-label">评分依据</span>
              <div
                class="common-complexcontent common-value"
                style="font-weight: bold;"
                v-html="questionDetail.complicatedediting ? questionDetail.scorebasis : transferStr(questionDetail.scorebasis!)"
                @click="$event => findImageAndPreview($event)"
              ></div>
            </div>
            <div class="common-kv-item">
              <span class="common-label">参考答案</span>
              <div
                v-if="questionDetail.reference_answer"
                class="common-complexcontent common-value"
                style="font-weight: bold;"
                v-html="questionDetail.complicatedediting ? questionDetail.reference_answer : transferStr(questionDetail.reference_answer!)"
                @click="$event => findImageAndPreview($event)"
              ></div>
              <div class="common-value" style="font-weight: bold;" v-else>未填写</div>
            </div>
          </div>
        </template>
        <template v-if="questionDetail.type === 4">
          <div class="test-question-testcase">
            <div v-for="(item, index) in questionDetail.btestcase" :key="index">
              <h4 class="title">示例{{ index + 1 }}:</h4>
              <div class="content" :class="{ transparent: print }">
                <div class="item">
                  <span class="item-text">输入: </span>{{ formatTestcaseInput(item) }}
                </div>
                <div class="item">
                  <span class="item-text">输出: </span>{{ item.output[0].value }}
                </div>
              </div>
            </div>
          </div>
          <div v-if="showQuestionDifficulty && ALGORITHM_DIFFICULTY[questionDetail.difficulty]" class="test-question-difficulty">
            <span class="label">题目难度</span>
            <span>{{ ALGORITHM_DIFFICULTY[questionDetail.difficulty].label }}</span>
          </div>
        </template>
        <template
          v-if="
            questionDetail.type === InterviewQuestionEnum['填空题'] && showCorrectAnswer && !showStudentScore
          "
        >
          <div style="margin-bottom: 10px">正确答案: {{ getCorrectAnswer() }}</div>
        </template>
        <template v-if="questionDetail.type === 6">
          <div class="test-question-sequence">
            <div
              class="sort-option-wrapper"
              v-for="(option, index) in getRandomOptions(questionDetail.options)"
              :key="option.value"
            >
              <div v-if="print">{{ String.fromCharCode(65 + index) }}</div>
              <div v-else>{{ option.value }}</div>
              <div
                class="item"
                :class="{ transparent: print }"
                v-html="
                  questionDetail.complicatedediting ? option.content : transferStr(option.content)
                "
              ></div>
            </div>
          </div>
        </template>
        <div class="common-kv-item">
          <div class="common-label">建议时长</div>
          <div class="common-value">{{ questionDetail.suggested_time }}分钟</div>
        </div>
      </div>
      <div class="disableKws" v-if="hasDisablekws && props.showDisabledKws">
        <span class="label">禁用关键词</span>
        <div class="kw-list">
          <div class="kw-item" v-for="item in Object.keys(props.questionDetail.disablekws!)">
            <div class="language">{{ item }}:</div>
            <div class="kw-wrap">
              <div class="kw" v-for="keyword in props.questionDetail.disablekws![item]">{{ keyword }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 校对状态 -->
      <template v-if="questionDetail.proofreading_info?.proofreading && showQuestionProofreadingRecord">
        <p class="proofread-record">
            <img v-if="questionDetail.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
            <img v-if="questionDetail.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
            <span style="margin-left: 4px;">由{{ questionDetail.proofreading_info.is_ai ? 'AI' : questionDetail.proofreading_info.teacher_name }}于{{questionDetail.proofreading_info.update_at}}{{ questionDetail.proofreading_info.is_ai ? '判断' : '确认' }}该题</span>
            <span style="color: #38910b;font-weight: bold;" v-if="questionDetail.proofreading_info.proofreading === 1">正确</span>
            <span style="color: #dc2b28;font-weight: bold;" v-if="questionDetail.proofreading_info.proofreading === 2">错误</span>
        </p>
      </template>
      <template v-if="showStudentScore && questionDetail.type === 4">
        <CodeBox
          autodetect
          :language="questionDetail.language"
          :code="questionDetail.stuanswer"
        ></CodeBox>
        <div class="out-answer">
          通过测试用例数
          <span :class="getPasscaseClass(questionDetail.passcase!)">{{
            questionDetail.passcase
          }}</span>
        </div>
      </template>
      <div
        v-if="showStudentScore"
        class="test-question-answer"
        :class="getScoreCondition()"
      >
        <div class="correct-answer" v-if="questionDetail.type !== 4">
          <span class="label">正确答案</span>
          <div class="content">{{ getCorrectAnswer() }}</div>
        </div>
        <div class="stu-answer" v-if="questionDetail.type !== 4">
          <span class="label">考生答案</span>
          <div class="content" v-if="questionDetail.type !== 3">{{ questionDetail.stuanswer }}</div>
          <template v-else>
            <div class="content">
              <pre>{{ getQATextAnswer(questionDetail.stuanswer) }}</pre>
              <div class="answer-imgs">
                <template v-for="img in getAnswerImgs(questionDetail.stuanswer)" :key="img">
                  <div class="item">
                    <a-image :src="getImgUrl(img)" />
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useSlots } from 'vue'
import OptionBtn from '@/components/RadioBtn/index.vue'
import { transferStr } from '@/utils/common'
import OptionsBox from './OptionsBox.vue'
import { findImageAndPreview } from '@/utils/common'
import { ALGORITHM_DIFFICULTY } from '@/config/constants'
import CodeBox from '@/components/CodeBox.vue'
import { useStore } from 'vuex'
import moment from 'moment'
import { message } from 'ant-design-vue'
import { InterviewQuestionDetail, InterviewQuestionEnum } from '@/types/interviewQuestion'

const store = useStore()

const props = withDefaults(
  defineProps<{
    showType?: boolean // 是否显示题型

    showCorrectAnswer?: boolean // 是否展示正确答案
    showQuestionDifficulty?: boolean // 是否显示算法题难度
    showQuestionPointsAndBasis?: boolean // 是否展示问答题得分点
    showQuestionProofreadingRecord?: boolean
    questionDetail: InterviewQuestionDetail
    print?: boolean // 是否打印预览
    optionLetterType?: 'button' | 'text'
    showDisabledKws?: boolean // 是否展示禁用关键词
    deleted?: boolean // 是否处于删除状态

    showStudentScore?: boolean // 是否展示学生得分
    studentId?: string // 学生id
    paperId?: string // 试卷id
  }>(),
  {
    showType: false,
    showScore: false,
    showCorrectAnswer: false,
    showStudentScore: false,
    showQuestionDifficulty: false,
    showQuestionPointsAndBasis: false,
    showQuestionProofreadingRecord: false,
    print: false,
    optionLetterType: 'button',
    showDisabledKws: false,
    deleted: false
  }
)

const emits = defineEmits<{
  (e: 'editScore', score: number): void
}>()

// 获取题干文本
const getContent = () => {
  let ans = ''
  if (props.questionDetail.complicatedediting) {
    ans = props.questionDetail.complexcontent!
    if (props.print) {
      ans = ans.replace(/<\/p>$/, ' (' + props.questionDetail.score + '分)</p>')
    }
  } else {
    ans = transferStr(props.questionDetail.body)
    if (props.print) {
      ans += ' (' + props.questionDetail.score + '分)'
    }
  }
  return ans
}

const hasDisablekws = computed(() => Object.values(props.questionDetail?.disablekws ?? []).flat().length)

const getRandomOptions = (options: any) => {
  if (!props.print) return options
  var len = (options && options.length) || 0
  for (var i = 0; i < len - 1; i++) {
    var index = parseInt((Math.random() * (len - i)) as any)
    var temp = options[index]
    options[index] = options[len - i - 1]
    options[len - i - 1] = temp
  }
  return options
}

// 格式化问答题得分点数据结构
const formatPoints = () => {
  if (Array.isArray(props.questionDetail?.answer)) {
    return props.questionDetail.answer.map((item: any) => {
      return { keywords: item.keyword, score: item.score }
    })
  }
  if (props.questionDetail?.answer) {
    return JSON.parse(props.questionDetail.answer as any).map((item: any) => {
      return { keywords: item.keyword, score: item.score }
    })
  }
  return []
}

// 动态样式class
const getDynamicClass = (option: string) => {
  if (!props.showStudentScore) return
  let stuanswer = props.questionDetail.stuanswer,
    answer = props.questionDetail.answer
  if (stuanswer?.includes(option) && answer?.includes(option)) return 'correct-tag'
  if (stuanswer?.includes(option) && !answer?.includes(option)) return 'error-tag'
  return ''
}
const getPasscaseClass = (passcase: string) => {
  let arr = passcase.split('/')
  return arr[0] === arr[1] ? 'correct-text' : 'error-text'
}

// 判断是否未正确选项
const judgeCorrentOption = (option: string) => {
  return props.questionDetail.answer?.includes(option)
}

// 格式化算法题示例输入
const formatTestcaseInput = (item: any) => {
  let str = ''
  item.input.forEach((param: any) => {
    str += Object.keys(param)[0] + ' = ' + Object.values(param)[0] + ', '
  })

  return str.slice(0, -2)
}

// 获取正确答案
const getCorrectAnswer = () => {
  if ([0, 2].includes(props.questionDetail.type)) {
    return props.questionDetail.answer
  }
  if (props.questionDetail.type === 1) {
    const answer = props.questionDetail.answer
    return answer?.sort().join(',')
  }
  if ([3, 5].includes(props.questionDetail.type)) {
    if (Array.isArray(props.questionDetail.answer)) {
      return props.questionDetail.answer.map((item: any) => item.keyword?.join(' / ')).join(';')
    }
    const answerArr: any = []
    JSON.parse(props.questionDetail.answer || ('[]' as any)).forEach((item: any) => {
      answerArr.push(item.keyword?.join(' / '))
    })

    return answerArr.join('; ')
  }
  if (props.questionDetail.type === 6) {
    return JSON.parse(props.questionDetail.answer as any).join(',')
  }
}

// 问答题考生答案
const getQATextAnswer = (answer: string = '') => {
  const index = answer?.indexOf('#@$')
  if (typeof index === 'number' && index !== -1) {
    answer = answer.substring(0, index)
  }
  return answer
}
const getAnswerImgs = (sAnswer: string = '') => {
  const index = sAnswer?.indexOf('#@$')
  if (index === -1 || typeof index !== 'number') {
    return []
  } else {
    const answerImgsString = sAnswer.split('#@$')[1]
    console.log(answerImgsString.split(','))
    return answerImgsString.split(',')
  }
}

function getScoreCondition() {
  let stuscore = props.questionDetail.stuscore!
  let score = props.questionDetail.score
  if (stuscore >= score) {
    return 'correct'
  } else if (stuscore === 0) {
    return 'error'
  } else {
    return 'part'
  }
}

const getImgUrl = (item: string) => {
  return 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/' + item
}

</script>

<style lang="less" scoped>
.common-kv-item {
  display: flex;
  // align-items: center;
  margin-bottom: 16px;
}
.common-label {
  color: #626262;
  margin-right: 32px;
  flex-shrink: 0;
}

.common-value {
  color: rgba(0,0,0,0.85);
}

:deep(.common-complexcontent) {
  p {
    word-wrap: break-word;
    img {
      max-width: 100%;
      cursor: zoom-in!important;
    }
  }
}

.test-question-display {
  
  .btn-wrap {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      background: #f5f5f5;
    }
  }
}

.transparent {
  border-color: transparent !important;
  background-color: transparent !important;
}
.test-question-stem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  white-space: pre-wrap;
  &.deleted {
    margin-bottom: 0;
  }

  &.tool-bar {
    display: flex;
    justify-content: space-between;
  }
  .type {
    align-self: flex-start;
    flex-shrink: 0;
    width: 52px;
    text-align: center;
    line-height: 20px;
    background: #f1f4fe;
    border: 1px solid #5478ee;
    border-radius: 4px;
    font-size: 12px;
    color: #5478ee;
    margin-right: 8px;
  }
  :deep(.body) {
    font-size: 16px;
    line-height: 22px;
    // word-break: break-all;
    text-align: justify;
    flex: 1;
    min-width: 0;
    // overflow: auto;
    overflow-x: auto;
    overflow-y: hidden;
    &.deleted {
      text-decoration: line-through;
    }
    p {
      font-size: 16px;
      word-break: break-word;
      img {
        max-width: 100%;
        cursor: zoom-in!important;
      }
    }
  }
  .score {
    align-self: flex-start;
    flex-shrink: 0;
    width: 41px;
    text-align: center;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 8px;
    margin-top: 1px;
  }
}
.out-answer {
  color: rgba(0, 0, 0, 0.45);
  padding-left: 50px;
  margin-bottom: 10px;
  .correct-text {
    color: #52c41a;
  }
  .error-text {
    color: #de504e;
  }
}
.test-question-content {
  .test-question-options {
    .item-option {
      display: flex;
      align-items: center;
      .tag {
        color: #000;
        font-weight: bolder;
      }
      + .item-option {
        margin-top: 16px;
      }
      :deep(.option-content) {
        p {
          word-wrap: break-word;
          img {
            max-width: 100%;
            cursor: zoom-in!important;
          }
        }
      }
    }
    .option-btn {
      margin-right: 16px;
    }
    .correct-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .error-tag {
      background: #f34b34;
      border: 1px solid #f34b34;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .correct-answer-tag {
      font-size: 12px;
      color: #2f8c00;
      background: #f3f7f1;
      border-radius: 4px;
      padding: 0 2px;
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
  .test-question-testcase {
    .title {
      padding: 0 0 8px;
      font-size: 14px;
      font-weight: 600;
    }
    .content {
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 16px;

      .item {
        font-size: 14px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
      .item-text {
        font-weight: 600;
        padding-right: 6px;
      }
    }
  }
  .test-question-difficulty {
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
    }
  }
  .test-question-sequence {
    margin-bottom: 16px;
    .item {
      display: flex;
      min-height: 34px;
      padding-left: 8px;
      align-items: center;
      border: 1px solid #dad8d8;
      color: #121633;
      border-radius: 2px;
    }
  }
  .sort-option-wrapper {
    margin-bottom: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    .item {
      flex: 1;
      margin-left: 10px;
    }
  }
}
.test-question-tag {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.disableKws {
  margin-top: 10px;
  .label {
    color: #626262;
    margin-right: 32px;
  }
  .kw-list {
    margin-top: 8px;
    padding-left: 16px;
    .kw-item {
      display: flex;
      .language {
        flex: none;
        width: 80px;
        height: 24px;
        margin-right: 8px;
        color: #626262;
        line-height: 24px;
      }
      .kw-wrap {
        line-height: 24px;
        display: flex;
        flex-wrap: wrap;
        color: #626262;
        .kw {
          margin-right: 4px;
          margin-bottom: 4px;
          padding: 0 4px;
          background: #f5f5f5;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
  .value {
    padding-left: 16px;
  }
}
.test-question-answer {
  padding: 16px 24px 16px 48px;
  border-radius: 8px;
  margin-top: 20px;
  .label {
    color: #626262;
    font-size: 12px;
    margin-right: 32px;
    white-space: nowrap;
  }
  .correct-answer,
  .stu-answer,
  .get-score {
    margin-bottom: 8px;
    font-size: 12px;
    display: flex;
    &:last-child {
      margin-bottom: 0;
    }
    .content {
      font-size: 12px;
      word-break: break-word;
    }
  }
  .answer-imgs {
    &:has(.item) {
      margin-top: 16px;
    }
    display: flex;
    :deep(.item) {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 8px;
      border-radius: 5px;
      overflow: hidden;
      .ant-image {
        height: 100%;
        width: 100%;
      }
      .ant-image-img {
        height: 100%;
        border-radius: 5px;
        object-fit: cover;
      }
    }
  }
  
  &.error {
    border: 1px solid #ff4d4f;
    background: url('@/assets/images/exam/error.png') no-repeat 95%, #fcf0f0;
  }
  &.part {
    border: 1px solid #faad14;
    background: url('@/assets/images/exam/part.png') no-repeat 95%, #faf4ee;
  }
  &.correct {
    background: #f6ffed;
    border: 1px solid #52c41a;
  }
}

.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 16px;
  display: flex;
  align-items: center;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
  }
}
</style>

<style lang="less">

.popconfirm-editscore {
  .ant-popover-message-title {
    padding-left: 0;
  }
}

</style>
