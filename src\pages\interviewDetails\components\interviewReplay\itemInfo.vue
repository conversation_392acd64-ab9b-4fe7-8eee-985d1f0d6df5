<script setup lang="ts">
import { DArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import QuestionTag from './questionTag.vue'

const props = defineProps(['info', 'activeItem', 'examDetail'])
const emits = defineEmits(['jump'])
const showMoreAns = ref(false)
const anchor = useTemplateRef('anchor')
const numMap: any = {
  1: '首',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
  6: '六',
  7: '七',
  8: '八',
  9: '九',
  10: '十',
}
function clickAnswer(item: any) {
  if (!item || !item.createTime) 
    return
  const startTime = dayjs(props.examDetail.exam.recordTime)
  const endTime = dayjs(item.createTime)
  const secondsDifference = endTime.diff(startTime, 'second')
  emits('jump', secondsDifference)
  // videoRef.value && videoRef.value.jump(secondsDifference)
}
watch(() => props.activeItem, (val) => {
  if (val.question.id === props.info.question.id) {
    anchor.value?.scrollIntoView({ behavior: 'smooth' })
  }
})
</script>

<template>
  <div class="mt-[22px]">
    <div ref="anchor" class="text-[#5478ee] flex">
      第{{ info.question.displaySeq }}题
      <QuestionTag :tags="info.question.tags" />
    </div>
    <div class="font-bold text-[#000] mt-[8px] mb-[10px]">
      {{ info.question.displaySeq }}. {{ info.question.configSchema.content }}
    </div>
    <div class="flex mt-[8px] mb-[8px]">
      <div class="w-[110px] pl-[5px]">
        参考答案
      </div>
      <div style="width: calc(100% - 110px);" class="px-[5px]">
        {{ info.question.correctAnswer.reference }}
      </div>
    </div>
    <div v-show="showMoreAns && info.records.length > 1"> 
      <div v-for="ans in info.records.filter((_:any, index:number) => index < info.records.length - 1)" :key="ans.id" class="flex mt-[8px] mb-[8px]">
        <div class="w-[110px] p-[5px]">
          {{ numMap[ans.recordCount] }}次答案
        </div>
        <div style="width: calc(100% - 110px);" class="answer" :style="{ cursor: !ans.speechText ? 'default' : '', background: !ans.speechText ? 'none' : '' }" @click="clickAnswer(ans)">
          {{ ans.speechText || '无作答' }}
        </div>
      </div>
    </div>
    
    <div v-if="info.records.length > 1" class="hover:bg-[#f1f4fe] hover:text-[#5478ee] bg-[#f8f9fa] rounded-[8px] h-[44px] w-full flex items-center justify-center my-[8px] cursor-pointer" @click="showMoreAns = !showMoreAns">
      {{ showMoreAns ? '收起' : '查看' }}历史作答
      <el-icon style="margin-left: 10px;margin-bottom: -2px;" :style="{ transform: showMoreAns ? 'rotate(-90deg)' : 'rotate(90deg)' }">
        <DArrowRight />
      </el-icon>
    </div>

    <div class="flex mt-[8px]">
      <div class="w-[110px] p-[5px]">
        最终作答
      </div>
      <div style="width: calc(100% - 110px);" class="answer" :style="{ cursor: !info.records.length ? 'default' : '', background: !info.records.length ? 'none' : '' }" @click="clickAnswer(info.records[info.records.length - 1] && info.records[info.records.length - 1])">
        {{ info.records[info.records.length - 1] && info.records[info.records.length - 1].speechText || '无作答' }}
      </div>
    </div>
  </div>
  <el-divider />
</template>

<style lang="scss" scoped>
.answer{
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  &:hover{
    background:#f1f4fe;
  }
  &:focus{
    background: #daf1ff;
  }
  &:active{
    background: #daf1ff;
  }
}
</style>