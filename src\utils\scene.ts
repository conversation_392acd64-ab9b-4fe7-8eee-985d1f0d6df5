export default class Scene {
    offset = { x: 0, y: 0 }; // 拖动偏移
    curOffset = { x: 0, y: 0 }; // 记录上一次的偏移量
    mousePosition = { x: 0, y: 0 }; // 记录鼠标滚轮点击时的坐标位置
    maxScale = 8;
    minScale = 1;
    scaleStep = 0.2;
    scale = 1;
    preScale = 1;

    x = 0; // 记录鼠标点击Canvas时的横坐标
    y = 0; // 记录鼠标点击Canvas时的纵坐标

    canvas: HTMLCanvasElement
    width: number
    height: number
    img: HTMLImageElement
    imgSrc: string
    ctx: CanvasRenderingContext2D

    constructor(id: string, options = {
        width: 600,
        height: 400,
        imgSrc: 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/idcard/bc0b0a9f-996b-44f6-ad95-4d57a6b76763/idcardfront.png?Expires=1706603642&AccessKeyId=TWGBWRLVSEB3UWEBKBXR&Signature=SInMNlVw%2BiteM%2B5LU24mvNTiiVA%3D'
    }) {
        this.canvas = document.querySelector('#' + id)!
        this.canvas.style.cursor = 'grab';
        this.width = options.width;
        this.height = options.height;
        this.canvas.width = options.width;
        this.canvas.height = options.height;
        this.ctx = this.canvas.getContext('2d')!;

        this.imgSrc = options.imgSrc;
        this.img = new Image()
        if (this.imgSrc) {
            this.img.src = this.imgSrc;
            this.img.onload = () => {
                this.draw();
            };
        }

        this.onMousedown = this.onMousedown.bind(this);
        this.onMousemove = this.onMousemove.bind(this);
        this.onMouseup = this.onMouseup.bind(this);
        this.onMousewheel = this.onMousewheel.bind(this);
        this.canvas.addEventListener('mousewheel', this.onMousewheel);
        this.canvas.addEventListener('mousedown', this.onMousedown);
    }

    onMousewheel(e: any) {
        e.preventDefault();

        this.mousePosition.x = e.offsetX; // 记录当前鼠标点击的横坐标
        this.mousePosition.y = e.offsetY; // 记录当前鼠标点击的纵坐标
        if (e.wheelDelta > 0) {
            // 放大
            this.scale = parseFloat((this.scaleStep + this.scale).toFixed(2)); // 解决小数点运算丢失精度的问题
            if (this.scale > this.maxScale) {
                this.scale = this.maxScale;
                return;
            }
        } else {
            // 缩小
            this.scale = parseFloat((this.scale - this.scaleStep).toFixed(2)); // 解决小数点运算丢失精度的问题
            if (this.scale < this.minScale) {
                this.scale = this.minScale;
                return;
            }
        }

        this.zoom();
    }

    zoom() {
        this.offset.x = this.mousePosition.x - (this.mousePosition.x - this.offset.x) * this.scale / this.preScale;
        this.offset.y = this.mousePosition.y - (this.mousePosition.y - this.offset.y) * this.scale / this.preScale;

        this.paint();
        this.preScale = this.scale;
        this.curOffset.x = this.offset.x;
        this.curOffset.y = this.offset.y;
    }

    onMousedown(e: any) {
        if (e.button === 0) {
            // 鼠标左键
            this.x = e.x;
            this.y = e.y

            // 设置拖拽时的样式
            this.canvas.style.cursor = 'grabbing';

            window.addEventListener('mousemove', this.onMousemove);
            window.addEventListener('mouseup', this.onMouseup)
        }
    }

    onMousemove(e: any) {
        this.offset.x = this.curOffset.x + (e.x - this.x);
        this.offset.y = this.curOffset.y + (e.y - this.y);

        this.paint();
    }

    onMouseup() {
        this.curOffset.x = this.offset.x;
        this.curOffset.y = this.offset.y;

        // 恢复正常样式
        this.canvas.style.cursor = 'grab';

        window.removeEventListener('mousemove', this.onMousemove);
        window.removeEventListener('mouseup', this.onMouseup);
    }

    zoomIn() {
        this.scale += this.scaleStep;
        if (this.scale > this.maxScale) {
            this.scale = this.maxScale;
            return;
        }
        this.mousePosition.x = this.width / 2;
        this.mousePosition.y = this.height / 2;
        this.zoom();
    }

    zoomOut() {
        this.scale -= this.scaleStep;
        if (this.scale < this.minScale) {
            this.scale = this.minScale;
            return;
        }
        this.mousePosition.x = this.width / 2;
        this.mousePosition.y = this.height / 2;
        this.zoom();
    }

    // 重置
    reset() {
        this.clear();
        this.scale = 1; // 当前缩放
        this.preScale = 1; // 上一次缩放
        this.offset = { x: 0, y: 0 }; // 拖动偏移
        this.curOffset = { x: 0, y: 0 }; // 当前偏移
        this.mousePosition = { x: 0, y: 0 };
        this.draw();
    };

    draw() {
        // 计算图片绘制的起始坐标和宽高，以实现 'contain' 效果并使其居中
        let aspectRatio = this.img.width / this.img.height;
        let canvasAspectRatio = this.width / this.height;

        let drawWidth, drawHeight, drawX, drawY;

        if (aspectRatio > canvasAspectRatio) {
            drawWidth = this.width * this.scale;
            drawHeight = drawWidth / aspectRatio;
        } else {
            drawHeight = this.height * this.scale;
            drawWidth = drawHeight * aspectRatio;
        }

        drawX = (this.width - drawWidth) / 2 + this.offset.x / this.scale;
        drawY = (this.height - drawHeight) / 2 + this.offset.y / this.scale;

        this.ctx.drawImage(this.img, drawX, drawY, drawWidth, drawHeight);
    }

    clear() {
        this.canvas.width = this.width;
    }

    paint() {
        this.clear();
        this.ctx.translate(this.offset.x, this.offset.y);
        this.ctx.scale(this.scale, this.scale);
        this.draw();
    }
}