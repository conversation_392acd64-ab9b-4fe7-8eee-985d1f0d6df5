<template>
  <div class="manual-mark-page-wrap">
    <div class="title-wrap">
      <span class="title">人工阅卷</span>
    </div>
    <div class="filter-wrapper">
      <div style="flex: 1;">
        <div class="filter-wrapper-row">
          <Switch :columns="stateColums" v-model="params.markedList"></Switch>
          <a-input-search v-model:value.trim="searchValue" placeholder="请输入考试名称/教师名称" allow-clear @blur="handleSearch"
            @search="handleSearch" />
        </div>
        <div class="filter-wrapper-row more-filter" v-if="filterMoreVisible">
          <div class="filter-item">
            <span class="filter-label">试卷类型</span>
            <a-select v-model:value="params.uniexamList" :options="PaperTypeList" placeholder="全部" allowClear />
          </div>
          <div class="filter-item">
            <span class="filter-label">创建人</span>
            <a-select placeholder="全部" allowClear v-model:value="params.createBy">
              <a-select-option :value="1">本人</a-select-option>
              <a-select-option :value="0">其他</a-select-option>
            </a-select>
          </div>
          <div class="filter-item">
            <span class="filter-label">考试时间</span>
            <a-range-picker class="time-range-wrap" v-model:value="params.start_time_range"
              :placeholder="['最早开始时间', '最晚开始时间']" valueFormat="YYYY-MM-DD">
              <template #suffixIcon>
                <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
              </template>
            </a-range-picker>
          </div>
          <div class="filter-item">
            <span class="filter-label">发布状态</span>
            <a-select placeholder="全部" allowClear v-model:value="params.publishedList">
              <a-select-option :value="1">已发布</a-select-option>
              <a-select-option :value="0">未发布</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
      <div class="filter-btns">
        <div class="filter-btn filter-more" @click="filterMoreVisible = !filterMoreVisible">
          <span v-if="filterMoreActiveNumber" class="filter-number">{{ filterMoreActiveNumber }}</span>
          <img v-else src="@/assets/icons/svg/filter2.svg" alt="">
          <span style="margin-left: 4px;">更多条件</span>
        </div>
        <div class="filter-btn" style="margin-top: 16px;" @click="handleReset" v-if="filterMoreVisible">
          <img width="16" height="16" style="margin-right: 4px;" src="@/assets/images/paper/clear.png" alt="">
          <div class="label">清空条件</div>
        </div>
      </div>
    </div>
    <ListWrapper ref="listWrapperRef" :card-min-width="300" :params="params" :getListFn="getListFn">
      <template #item="{ item }">
        <PaperCard class="list-item" :paperInfo="item" :search-text="params.condition" />
      </template>
    </ListWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import _ from 'lodash'
import Switch from '@/components/Switch.vue';
import { PaperStatus, PaperType, PaperTypeList } from '@/models/paperModel'
import { markingtask } from '@/api/admin/paperManage'
import ListWrapper from '@/components/ListWrapper.vue'
import PaperCard from './components/PaperCard.vue'
import { useState } from '@/hooks';

const store = useStore()

// 更多条件
const filterMoreVisible = ref(false)
const filterMoreActiveNumber = computed(() => {
  let filterMoreKeys = ['uniexamList', 'createBy', 'start_time_range', 'publishedList']
  return filterMoreKeys.reduce((pre, cur) => {
    if (params.value[cur] !== rawParams[cur]) pre++
    return pre
  }, 0)
})

// 模糊查询
const searchValue = ref('')
function handleSearch() {
    params.value.condition = searchValue.value
}

const stateColums = [ // 阅卷状态
  { value: null, label: '全部' },
  { value: 0, label: '待阅卷' },
  { value: 1, label: '已阅卷' },
]

// 接口查询
const [params, resetParams, rawParams] = useState<{
  action: 'query',
  condition: string
  exam_status: PaperStatus | null
  markedList: number | null
  publishedList?: boolean
  uniexamList?: PaperType | null
  createBy?: number | null
  start_time_range: null | string[]
}>({
  action: 'query',
  condition: '',
  exam_status: null, // 考试状态
  markedList: null, // 阅卷状态
  publishedList: undefined, // 发布状态
  uniexamList: undefined, // 试卷类型
  createBy: undefined, // 创建人
  start_time_range: null, // 开始结束时间
})
params.value.basePager = {
}
const getListFn = (params: any) => {
    return markingtask({
        ...params,
        basePager:{
          current: params.page,
          size: params.per_page
        },
        markedList: params.markedList === null ? undefined : [params.markedList],
        uniexamList: params.uniexamList === undefined ? null : [params.uniexamList],
        createBy: params.createBy === undefined ? null : params.createBy,
        publishedList: params.publishedList === undefined ? null : [params.publishedList],
        teacher: store.getters.userid,
        startTime: params.start_time_range == null ? null : params.start_time_range[0],
        endTime: params.start_time_range == null ? null : params.start_time_range[1]
      })
}
const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()

// 重置
function handleReset() {
    searchValue.value = ''
    resetParams()
}

</script>

<style lang="less" scoped>
.manual-mark-page-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title-wrap {
        height: 64px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }

        .btns {
            height: 32px;

            :deep(.ant-btn) {
                margin-left: 8px;
            }
        }
    }
}

.filter-wrapper {
    padding: 24px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
    background: #ffffff;
    border-radius: 8px;
    margin: 0px 20px 20px;
    display: flex;

    :deep(.filter-wrapper-row) {
        display: flex;
        justify-content: space-between;
        &.more-filter {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-row-gap: 16px;
        }

        +.filter-wrapper-row {
            margin-top: 16px;
        }

        .ant-select,
        .ant-picker,
        .ant-input-search {
            width: 240px;
        }
    }

    .filter-item {
        display: flex;
        align-items: center;
        .filter-label {
            margin-right: 16px;
            color: #626262;
        }
        &:nth-child(3n+2) {
            justify-content: center;
        }
        &:nth-child(3n+3) {
            justify-content: flex-end;
        }
    }

    .filter-btns {
        margin-left: 8px;
    }

    .filter-btn {
        width: 108px;
        height: 32px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.filter-more {
            .filter-number {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #FF4D4F;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
            }
        }

    }


}

.list-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid transparent;
    position: relative;
    user-select: none;
}
</style>
