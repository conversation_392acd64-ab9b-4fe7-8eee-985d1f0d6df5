<!-- 考试分析雷达图 -->
<template>
  <div ref="radarRef" style="width: 460px;height:360px;"></div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ECharts, init } from 'echarts'

const props = defineProps<{
  joinRate: number
  passRate: number
  saturationRate: number
  diffRate: number
}>()

const radarRef = ref<HTMLElement | null>(null)
const initEcharts = () => {
  // 基于准备好的dom，初始化echarts实例
  var myChart = init(radarRef.value!)

  // 指定图表的配置项和数据
  var option = {
    radar: [
      {
        indicator: [
          { text: '参加率', max: 1, color: 'rgba(0,0,0,0.45)', key: 'joinRate' },
          { text: '试卷区分度', max: 1, color: 'rgba(0,0,0,0.45)', key: 'diffRate' },
          { text: '题量充实度', max: 1, color: 'rgba(0,0,0,0.45)', key: 'saturationRate' },
          { text: '通过率', max: 1, color: 'rgba(0,0,0,0.45)', key: 'passRate' },
        ],
        center: ['50%', '50%'],
        silent: true,
        nameGap: 5,
        axisLine: {
          lineStyle: {
            color: '#BFBFBF',
          },
        },
        axisName: {
          width: 60,
          lineHeight: 17,
          formatter: function(value: string, indicator: any) {
            return [
              `{label| ${value}}`,
              `{value| ${props[indicator.key]}}`,
            ].join('\n')
          },
          rich: {
            label: {
              fontSize: 12,
              color: 'rgba(0,0,0,0.45)',
              align: 'center',
            },
            value: {
              fontSize: 12,
              fontWeight: 'bold',
              color: 'rgba(0,0,0,0.85)',
              align: 'center',
            },
          }
        }
      },
    ],
    series: [
      {
        type: 'radar',
        symbol: 'none',
        lineStyle: {
          color: '#1890FF',
        },
        areaStyle: {
          color: 'rgba(91,143,249,0.60)',
        },
        data: [
          {
            value: [props.joinRate, props.diffRate, props.saturationRate, props.passRate],
            name: '试卷分析',
          }
        ]
      },
    ]
  }

  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option)
}

watch(radarRef, (val) => {
  console.log('radarRef', val)
  if (val) initEcharts()
}, {
  immediate: true
})
</script>

<style lang="less" scoped>

</style>
