.ai-answer-panel {
      background-color: #f5f5f5;
      width: 752px;
      border-radius: 8px;
      padding: 16px 48px;
      margin-top: 16px;

      >div {
            display: flex;

            .label {
                  width: 80px;
                  font-size: 12px;
                  color: #626262;
                  margin-right: 16px;
            }

            .value {
                  flex: 1;
                  min-width: 0;
                  font-size: 12px;
            }
      }
}

:deep(.tagify) {
      border: none;
      font-size: 12px;
      align-items: center;

      .tagify__input {
            &::before {
                  font-size: 12px;
            }
      }

      .tagify__tag-text {
            font-size: 12px;
      }
}

:deep(.ant-tree-select-dropdown) {
      .ant-select-tree-node-content-wrapper {
            display: flex;
            align-items: center;
            flex-shrink: 0 !important;
      }
}

.err-tip {
      font-size: 14px;
      color: #f5222d;
}

.addItem {
      margin-top: -1px;
}

:deep(.ant-collapse-item) {
      background-color: #f2f5fc;
}

:deep(.ant-collapse-header) {
      font-size: 12px;
}

:deep(.ant-dropdown-menu-item-only-child) {
      font-size: 12px;
}

:deep(.ant-dropdown-menu-item-only-child):hover {
      background-color: #f1f4fe;
}

:deep(.sortingQuestionOptions .ant-radio) {
      display: none;
}

.ant-form label {
      font-size: 12px;
}

.create-question-container {
      position: relative;

      .close-btn {
            position: absolute;
            top: -60px;
            font-size: 18px;
            right: 10px;
            cursor: pointer;
            color: #929294;
            transition: all ease 0.2s;

            &:hover {
                  color: #696969;
            }
      }

      height: 100%;
      padding: 0 20px;

      .create-title {
            line-height: 48px;
            font-size: 14px;
            font-weight: 600;
      }

      .create-question-content {
            display: flex;
            flex-direction: column;
            position: relative;
            min-width: 968px;
            overflow: auto;
            height: 100%;
            background-color: #fff;

            .form-input {
                  width: 220px;
                  height: 32px;
                  border-radius: 8px;
                  font-size: 12px;
            }

            :deep(.tag-select .ant-select-selector) {
                  width: 220px;
                  border-radius: 8px;
                  font-size: 12px;
            }

            :deep(.ant-select-tree-title) {
                  font-size: 12px;
            }

            :deep(.ant-form-item-label > label) {
                  font-size: 12px;
                  height: 26px;
                  color: rgba(0, 0, 0, 0.65);
            }

            :deep(.ant-select-selector) {
                  border-radius: 8px;
                  font-size: 12px;
            }

            :deep(textarea.ant-input) {
                  border-radius: 8px;
                  font-size: 12px;
            }

            :deep(.ant-tabs-tab) {
                  font-size: 12px;
                  background-color: #f5f5f5;
            }

            :deep(.ant-tabs-tab-active) {
                  color: #5478EE;
                  background: #fff;
            }

            .form {
                  margin-bottom: auto;
                  flex: 1;
                  min-height: 0;
                  overflow: hidden scroll;
                  padding-right: 20px;

                  :deep(.ant-col) {
                        width: 80px;
                  }

                  .disablekws-wrapper {
                        display: flex;
                        align-items: flex-start;

                        .item {
                              display: flex;
                              align-items: flex-start;
                              margin-bottom: 8px;
                              line-height: 26px;

                              &:last-of-type {
                                    margin-bottom: 0;
                              }

                              .label {
                                    width: 70px;
                                    line-height: 32px;
                              }
                        }

                        .tags-select {
                              display: block;
                              width: 520px;

                              :deep(.ant-select-selection-item-content) {
                                    font-size: 12px;
                              }
                        }
                  }

                  .standard-tip {
                        margin-top: 4px;
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.45);
                  }

                  .points-wrapper {
                        .item {
                              display: flex;
                              align-items: flex-start;
                              margin-bottom: 8px;
                              font-size: 12px;

                              .index {
                                    flex: none;
                                    width: 20px;
                                    height: 28px;
                                    text-align: left;
                                    line-height: 28px;
                              }

                              .tags-select {
                                    width: 80%;
                                    margin: 0 16px 0 6px;
                              }

                              :deep(.ant-select-selection-item) {
                                    font-size: 12px;
                              }
                        }

                        .score-wrapper {
                              display: flex;
                              justify-content: space-between;
                              align-items: center;
                              font-size: 12px;
                              margin-right: 16px;

                              .score {
                                    flex-grow: 0;
                                    flex-shrink: 0;
                                    margin-right: 16px;
                              }

                              .score-input {
                                    flex-grow: 0;
                                    flex-shrink: 0;
                              }
                        }

                        .del-icon-wrap {
                              flex: none;
                              width: 16px;
                              height: 32px;
                              display: flex;
                              align-items: center;

                              .del-icon {
                                    cursor: pointer;
                              }
                        }

                        .addpoints-btn {
                              display: flex;
                              justify-content: center;
                              align-items: center;
                              width: 82px;
                              height: 24px;
                              background: #ffffff;
                              border: 1px solid rgba(0, 0, 0, 0.15);
                              border-radius: 4px;
                              font-size: 12px;
                              cursor: pointer;
                              margin-top: 10px;
                        }
                  }

                  .complex-switch-box {
                        padding: 0 5px;
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.85);
                        margin-right: 20px;

                        .label {
                              margin-right: 10px;
                        }
                  }
            }

            .func-name-wrapper,
            .rtype-wrapper {
                  display: flex;
                  align-items: center;

                  .func-extra-info {
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.45);
                        margin-left: 12px;
                  }

                  .rtype-extra-info {
                        font-size: 12px;
                  }

                  .form-checkbox {
                        position: relative;
                        bottom: -1px;
                        margin: 0 10px 0 12px;
                  }
            }

            .fill-blank-config {
                  div {
                        display: flex;
                        align-items: center;
                        line-height: 28px;
                  }

                  .check-box {
                        margin-right: 8px;
                  }

                  .set-keyword-btn {
                        width: 70px;
                        height: 24px;
                        margin-left: 16px;
                        border: 1px solid rgba(0, 0, 0, 0.15);
                        border-radius: 4px;
                        justify-content: center;
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.65);
                        text-align: center;
                        line-height: 23px;
                        cursor: pointer;
                  }

                  span {
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.85);
                  }

                  .tip-icon {
                        margin-left: 8px;
                  }
            }

            .body-tip {
                  display: flex;
                  align-items: center;
                  background: #f1f4fe;
                  border-radius: 4px;
                  font-size: 12px;
                  line-height: 24px;
                  margin-top: 8px;

                  .tip-icon {
                        margin: 0 8px;
                  }
            }
      }

      .footer {
            width: 100%;
            display: flex;
            align-items: center;
            padding-top: 24px;
            border-top: 1px solid #e8e8e8;

            .ant-btn {
                  padding: 5px 16px;
                  border-radius: 8px;
                  font-size: 12px;
                  cursor: pointer;
            }

            .save-btn {
                  background: #5478ee;
                  color: #fff;
                  margin-right: 8px;
                  border-color: transparent;
            }

            .submit-btn {
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  color: rgba(0, 0, 0, 0.65);
                  margin-right: 8px;
            }
      }

      .ai-aide-wrap {
            position: fixed;

            &:hover .tip {
                  opacity: 1;
                  animation: sway .3s ease-in-out;
            }

            .tip {
                  transition: all ease .2s;
                  opacity: 0;
                  position: absolute;
                  top: -30px;
                  left: -32px;
                  width: 88px;
                  height: 27px;
                  background: #ffffff;
                  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
                  border-radius: 8px;
                  font-size: 12px;
                  color: rgba(0, 0, 0, 0.65);
                  text-align: center;
                  line-height: 27px;

                  &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        bottom: -6px;
                        right: 9px;
                        width: 0;
                        height: 0;
                        border-right: 8px solid transparent;
                        border-left: 8px solid transparent;
                        border-top: 8px solid #fff;
                  }
            }

            .ai-aide {
                  width: 80px;
                  height: 80px;
                  cursor: pointer;
            }
      }
}

.compound {
      margin-top: 24px;
}

.question-options {
      .sortingQuestionOptions {
            margin-left: 72px;
            margin-top: -5px;
      }

      .rightOrder {
            font-size: 12px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: left;
            line-height: 20px;
            color: rgba(0, 0, 0, 0.5);
            margin-left: 70px;
      }

      .optionContent {
            position: absolute;
            width: 48px;
            height: 18px;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: left;
            color: #626262;
            line-height: 18px;
      }

      .compoundQuestions {
            width: 120px;
            height: 32px;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: left;
            color: #5478ee;
            line-height: 18px;
            border-radius: 8px;
      }

      .item-option {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 12px;
            position: relative;
            max-width: 100%;

            .option-radio {
                  // margin-right: 18px;
                  display: inline-block;
                  width: 20px;
            }

            .option-content {
                  font-size: 12px;
                  border: none;
                  box-shadow: none;
                  width: 220px;
            }

            .order-option-content {
                  width: 220px;
            }

            .editor-wrapper {
                  padding: 3px 11px;
                  min-width: 0;
            }

            .del-icon {
                  visibility: hidden;
                  cursor: pointer;
                  flex-shrink: 0;
            }
      }

      .fill-blank-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .label {
                  width: 70px;
                  font-size: 12px;
            }

            .score-wrapper {
                  width: 160px;
                  // min-width: 160px;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .score {
                        flex-grow: 0;
                        flex-shrink: 0;
                  }

                  .score-input {
                        flex-grow: 0;
                        flex-shrink: 0;
                  }
            }

            :deep(.ant-select-show-search) {
                  // width: 600px !important;
            }

            :deep(.ant-select-selector) {
                  border: none;
                  box-shadow: none;
            }
      }

      :deep(.item-option > span:nth-child(2)) {
            display: flex;
            align-items: center;
      }

      :deep(.item-option > span:nth-child(2) > .ant-input) {
            font-size: 12px;
            border: 0px;
      }

      .item-option:hover .del-icon {
            position: relative;
            visibility: visible;
      }

      .add-option-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 82px;
            height: 24px;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
            cursor: pointer;
      }

      .get-option-btn {
            width: 82px;
            height: 24px;
      }

      .sort-question {
            margin-left: 77px;
      }
}

// textarea图标间距
:deep(.ql-toolbar.ql-snow .ql-formats) {
      margin-right: 0px;
}

.ant-alert-info {
      border: none;
}