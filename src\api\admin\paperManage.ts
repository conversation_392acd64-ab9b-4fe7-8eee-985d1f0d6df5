// @ts-nocheck
import request from '@/utils/http'

// 查询试卷
export function queryTestPaper(data?: object) {
  return request({
    url: '/papertemplate/page',
    data
  })
}

// 通过试卷ids查询试卷列表
export function queryPaperListByIds(data?: object) {
  return request({
    url: '/papertemplate/testpaperlistbyids',
    data
  })
}

// 创建试卷
export function createTestPaper(data?: object) {
  delete data.createTime
  return request({
    url: '/papertemplate/create',
    data
  })
}

// 编辑试卷
export function editTestPaper(data?: object) {
  delete data.createTime
  return request({
    url: '/papertemplate/update',
    data
  })
}

// 查看试卷
export function getTestPaper(data?: object) {
  const res = request({
    url: '/papertemplate/get',
    data
  })
  return res
}

// 编辑试卷
export function delTestPaper(data?: object) {
  return request({
    url: '/papertemplate/delete',
    data
  })
}

// 编辑试卷
export function getPaperDetail(data?: object) {
  return request({
    url: '/papertemplate/get',
    data
  })
}

// 查看试卷试题信息
export function getPaperInfo(data?: object) {
  return request({
    url: '/stupdetail',
    data
  })
}

// 上传
export function uploadOBS(data?: object) {
  return request({
    url: '/obs/uploadOBS',
    data
  })
}

// 查询已关联考生
export function queryRelatedResume(data?: object) {
  let url = '/papertemplate/rel/student/create'
  if(data.action == 'query') {
    url = '/papertemplate/rel/student/page'
    data.basePager = {
      current: data.page,
      size: data.per_page
    }
  }
  return request({
    // url: '/associate',
    url,
    data
  })
}

export function queryRelatedStu(data?: object) {
  return request({
    url: '/mgrexam',
    data
  })
}

// 查询已关联考生
export function monitorPhotos(data?: object) {
  return request({
    url: '/facecheck',
    data
  })
}

// 保留监控
export function reserveMonitor(data?: {
  // reserve: boolean
  // paper: string
  // student: string
  id: number
  reserveMonitor: number
}) {
  return request({
    url: '/papertemplate/stupaper/monitor/reservemonitor',
    data
  })
}

// 过滤试卷
export function filterPaper(data?: object) {
  return request({
    url: '/pfilter',
    data
  })
}

// 公布考试分数
export function publishExamScore(data?: object) {
  return request({
    url: '/publishscore',
    data
  })
}

// 创建邀请码
export function createInviteCode(data?: object) {
  return request({
    // url: '/tokenpaper',
    url: '/papertemplate/token/create',
    data
  })
}

// 下载试卷
export function downloadPaper(data?: object) {
  return request({
    url: '/dlpaper',
    data
  })
}

// 人工阅卷列表
export function markingtask(data?: object) {
  return request({
    url: '/markingtask/page',
    data
  })
}

// 通过id批量查询人工阅卷列表
export function queryMarkingtaskByids(data?: object) {
  return request({
    url: '/markingtaskbyids',
    data
  })
}

//人工阅卷
export function needcorrected(data?: object) {
  return request({
    url: '/needcorrected',
    data
  })
}

// 试卷详细信息
export function manualmarking(data?: object) {
  return request({
    url: '/papertemplate/exam/check/manualmarking',
    data
  })
}

// 评分
export function addmanualmarking(data?: object) {
  return request({
    url: '/papertemplate/exam/check/score',
    data
  })
}

// 试卷历史记录
export function markinghistory(data?: object) {
  return request({
    url: '/papertemplate/exam/check/markinghistory',
    data
  })
}

export function markinghistoryUpdateScore(data?: object) {
  return request({
    url: '/papertemplate/exam/check/updatemarkinghistory',
    data
  })
}

// 所有教师
export function allteacher(data?: object) {
  return request({
    url: '/teacher/pageByResourceGroup',
    data
  })
}

// 查询联合阅卷绑定关系
export function joinmarking(data?: object) {
  return request({
    url: '/markingtask/list',
    data,
    throttleTime: 1000,
  })
}

// 新增联合阅卷绑定关系
export function addjoinmarking(data?: object) {
  return request({
    url: '/markingtask/create',
    data,
    throttleTime: 1000,
  })
}

// 获取教师批改过试题
export function markedques(data?: object) {
  return request({
    url: '/papertemplate/exam/check/markedques',
    data
  })
}

// 试卷管理
export function viewgrades(data?: object) {
  return request({
    // url: '/viewgrades',
    url: 'papertemplate/stupaper/page',
    data
  })
}
// 查看考生邀请码
export function papertoken(data?: object) {
  return request({
    url: '/papertemplate/token/page',
    data
  })
}

export function papertokenDelete(data?: object) {
  return request({
    url: '/papertemplate/token/delete',
    data
  })
}

export function papertokenCreate(data?: object) {
  return request({
    url: '/papertemplate/token/create',
    data
  })
}

// 查询延长时间学生列表
export function delaystu(data?: object) {
  return request({
    url: '/papertemplate/delay/page',
    data
  })
}

// 延长考试时间
export function examdelay(data?: object) {
  return request({
    url: '/papertemplate/delay/student',
    data
  })
}

// 查看考试状态
export function candstatus(data?: object) {
  return request({
    url: '/papertemplate/examcenter/candstatus',
    data
  })
}

//  查看考生实时画面
export function livephoto(data?: object) {
  return request({
    url: '/livephoto',
    data
  })
}

// 草稿箱
export function draftpaper(data?: object) {
  return request({
    url: '/draftpaper',
    data
  })
}

// 参考文献
export function previewfile(
  data?: { filename: string; originalname?: string },
  onUploadProgress?: () => void
) {
  return request({
    url: '/previewfile',
    data,
    onUploadProgress
  })
}

// 分数详情
export function scoredetail(data?: object) {
  return request({
    // url: '/scoredetail',
    url: '/papertemplate/stupaper/result',
    data
  })
}

// 查看考生邮件邀请列表
export function examemail(data?: object) {
  return request({
    url: '/papertemplate/email/page',
    data
  })
}

export function examemailCreate(data?: object) {
  return request({
    url: '/papertemplate/email/create',
    data
  })
}

export function examemailDelete(data?: object) {
  return request({
    url: '/papertemplate/email/delete',
    data
  })
}

// 发送考生邮件邀请
export function sendpaperemail(data?: object) {
  return request({
    url: '/papertemplate/email/send',
    data
  })
}

// 学生邮件模板
export function examemailtemplate(data?: object) {
  return request({
    url: '/papertemplate/email/template/get',
    data
  })
}
// 编辑学生邮件模板
export function updateExamemailtemplate(data?: object) {
  return request({
    url: '/papertemplate/email/template/save',
    data
  })
}


// 监考总人数状态
export function candnumberstatus(data?: object) {
  return request({
    url: '/papertemplate/examcenter/candnumberstatus',
    data
  })
}

// 候选人信息
export function candsinfo(data?: object) {
  return request({
    url: '/papertemplate/stupaper/monitor/info',
    data
  })
}

// 监控实时画面
export function monitorscreen(data?: object) {
  return request({
    url: '/papertemplate/examcenter/monitorscreen',
    data
  })
}

// 模拟考试
export function mockexam(data?: object) {
  return request({
    url: '/papertemplate/mockexam',
    data
  })
}

// 关联考生
export function relatestu(data?: object) {
  return request({
    // url: '/relatestu',
    url: '/papertemplate/rel/student/page',
    data
  })
}

// 移除关联
export function delassociate(data?: object) {
  return request({
    // url: '/delassociate',
    url: 'papertemplate/rel/student/delete',
    data
  })
}

// 导出成绩
// export function exportstuscore(data?: object) {
//   return request({
//     url: '/exportstuscore',
//     data,
//     responseType: 'blob'
//   })
// }

// 克隆之前查看该试卷是否有已被删除的题目
export function beforeclonepaper(data?: { paper: string }) {
  return request({
    url: '/papertemplate/beforeclonepaper',
    data,
  })
}

// 获取试卷共享部门|教师
export function getPaperShare(data: Object) {
  return request({
    // url: '/querysharepaper',
    url: 'papertemplate/share/list',
    data,
  })
}

// 修改试卷部门|教师共享权限
export function modifyPaperShare(data: Object) {
  return request({
    url: '/papertemplate/share/save',
    data,
  })
}

// 移除试卷部门|教师共享权限
export function deletePaperShare(data: Object) {
  return request({
    url: '/papertemplate/share/delete',
    data,
  })
}

// 添加试卷部门|教师共享权限
export function addPaperShare(data: Object) {
  return request({
    url: '/papertemplate/share/save',
    data,
  })
}

// 当前部门组织架构
export function getDeptandResource(data?: Object) {
  return request({
    url: '/papertemplate/share/teachers',
    data: {},
  })
}

// 根据ids查询试卷名称
export function getPaperNameByIds(data?: Object) {
  return request({
    url: '/papertemplate/papernamebyids',
    data,
  })
}

export type MonitorModel = {
  /** 1=整卷展示 2=逐题可返回 3=逐题不可返回 */
  exam_model: 1 | 2 | 3
  images: {
    /** 1=PC摄像头 2=手机摄像头 3=电脑录屏 */
    type: 1 | 2 | 3
    image_prefix: string // 拼接地址的前缀
    /** 所有帧从最小帧号到最大帧号从小到大排列 */
    frames: string[]
    /** 仅当exam_model==3时不为null */
    questions_groups: {
      title: string
      children: {
        id: string
        number?: number // 题号
        min_frame: number, //最小帧号
        max_frame: number, //最大帧号
      }[]
    }[] | null
  }[]
}

export function monitorimgs(data: { id: number }): Promise<MonitorModel> {
  return request({
    url: '/papertemplate/stupaper/monitor/imgs',
    data,
  })
}

// 查看成绩-修改分数
export function modifyscore(data) {
  return request({
    // url: '/modifyscore',
    url: '/papertemplate/stupaper/result/modifyscore',
    data,
  })
}

// 收藏试卷
export function addpaperfocus(data?: {
  templeteId: string
}) {
  return request({
    url: '/papertemplate/focus/create',
    data,
  })
}

// 取消收藏试卷
export function delpaperfocus(data?: {
  templeteId: string
}) {
  return request({
    url: '/papertemplate/focus/delete',
    data,
  })
}