<template>
  <div class="question-form-wrapper">
    <CloseOutlined class="close-btn" @click="handleCancel" />
    <a-form ref="formRef" class="question-form" :model="formState" :rules="rules" autocomplete="off" :colon="false"
      labelAlign="left" :hideRequiredMark="true">
      <a-row>
        <a-col :span="8">
          <a-form-item label="题目类型" name="type">
            <a-select v-model:value="formState.type" placeholder="请选择题目类型" :disabled="!typeSwitch">
              <a-select-option v-for="item in QUESTIONS.filter(i => i.label === '问答题')" :key="item.value"
                :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="所属题库" name="category">
            <a-tree-select show-search :tree-data="categoryList" placeholder="添加题库" v-model:value="formState.category"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" allow-clear :field-names="{
                key: 'id',
                value: 'id',
                label: 'name'
              }" treeNodeFilterProp="name" :virtual="false" :getPopupContainer="(triggerNode: HTMLElement) => {
                return triggerNode.parentNode
              }">
            </a-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="建议时间" name="suggested_time">
            <a-input-number :value="formState.suggested_time" :formatter="(v) => v.toString()"
              @change="handleSuggestedTimeChange" :min="0.5" :max="60" :precision="1" :step="0.5" />
            <span style="font-size: 12px; margin-left: 8px;color: #626262;position: relative;top: -1px;">分钟</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="深度" name="depth">
            <a-select v-model:value="formState.depth" :options="depthList"></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="偏度" name="expertise">
            <a-select v-model:value="formState.expertise" :options="expertiseList"></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="难度" name="type">
            <svg-icon v-for="i in getStarByScore(formState.difficulty)" name="active_star" width="16px" height="16px"
              style="margin-top: 4px;"></svg-icon>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="题干内容" name="body">
        <a-textarea v-model:value="formState.body" :rows="4" :maxLength="10000" placeholder="点击编辑" />
      </a-form-item>
      <a-form-item label="AI朗读" name="">
        <a-radio-group v-model:value="formState.AI_reading" @change="handleAIReadingChange">
          <a-radio :value="1">朗读自定义题干</a-radio>
          <a-radio :value="0">朗读原文题干</a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- 因为要确保切换到自定义朗读时能获取到组件的list[0]所以这里不能用v-if -->
      <a-form-item v-show="formState.AI_reading === 1" label=" " :name="formState.AI_reading === 1 ? 'AI_reading_content' : ''">
        <a-textarea v-model:value="formState.AI_reading_content" :rows="4" :maxLength="1000" placeholder="点击编辑" />
        <UsefulSentence ref="usefulSentenceRef" style="margin-top: 16px;" @select="handleUsefulSentenceSelect" />
      </a-form-item>
      <QaFrom v-model:formState="formState" v-if="formState.type === InterviewQuestionEnum['问答题']"></QaFrom>
      <!-- 校对状态 -->
      <template v-if="formState.proofreading_info?.proofreading">
        <p class="proofread-record">
          <img v-if="formState.proofreading_info.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
          <img v-if="formState.proofreading_info.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
          <span style="margin-left: 4px;">由{{ formState.proofreading_info.is_ai ? 'AI' :
            formState.proofreading_info.teacher_name }}于{{ formState.proofreading_info.update_at }}{{
              formState.proofreading_info.is_ai ? '判断' : '确认' }}该题</span>
          <span style="color: #38910b;font-weight: bold;"
            v-if="formState.proofreading_info.proofreading === 1">正确</span>
          <span style="color: #dc2b28;font-weight: bold;"
            v-if="formState.proofreading_info.proofreading === 2">错误</span>
        </p>
        <div class="proofread-reason" v-if="formState.proofreading_info.proofreading === 2">
          <span class="label">错误原因</span>
          <div>{{ formState.proofreading_info.wrong_reason }}</div>
        </div>
      </template>
      <AiAnswer :formState="formState" api-name="getaiinterviewanswer"></AiAnswer>

    </a-form>
    <a-divider style="margin-bottom: 24px;background-color: #e8e8e8;margin-top: 0;"></a-divider>
    <div class="form-footer">
      <a-button type="primary" :loading="saveBtnLoading" @click="handleSubmit">{{ draftId ? '发布' : '保存' }}</a-button>
      <a-button v-if="!qid" style="margin-left: 8px;" @click="handleSaveDraft">保存草稿箱</a-button>
      <a-button style="margin-left: 8px;" @click="handleCancel">取消</a-button>
    </div>

    <a-modal v-model:visible="confirmSaveVisible" :maskClosable="false" wrapClassName="confirm-modal"
      @cancel="saveBtnLoading = false">
      <template #title>
        <p style="display: flex; align-items: center">
          <ExclamationCircleOutlined style="color: #faad14;font-size: 20px;" />
          <span style="margin-left: 8px; font-size: 18px; font-weight: bold">确定保存当前编辑？</span>
        </p>
      </template>
      保存后可能需要重新校对该题目
      <template #footer>
        <div>
          <a-button @click="confirmSaveVisible = false, saveBtnLoading = false">取消</a-button>
          <a-button type="primary" @click="save">确认</a-button>
        </div>
        <a-checkbox v-model:checked="isNoMoreReminders" @change="handleNoMoreRemindersChange">不再提醒</a-checkbox>
      </template>
    </a-modal>

    <AiBot @click="aiAideVisible = true"></AiBot>
    <!-- AI出题 -->
    <AiAideModal :visible="aiAideVisible" api-name="aiinterviewgeneratequestion" :type="formState.type"
      @close="aiAideVisible = false" @created="handleAiCreated" />
  </div>
</template>

<script lang="ts" setup>
import { watch, ref, nextTick, computed, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useState } from '@/hooks';
import { QUESTIONS } from '@/config/constants';
import QaFrom from './QaFrom.vue';
import type { Rule } from 'ant-design-vue/es/form';
import _ from 'lodash';
import { FormInstance, message, Modal } from 'ant-design-vue';
import { checkBody } from '@/utils/validate';
import { CloseOutlined } from '@ant-design/icons-vue'
import type { InterviewQuestionDetail, InterviewQuestionFormState } from '@/types/interviewQuestion';
import { DepthEnum, ExpertiseEnum, InterviewQuestionEnum } from '@/types/interviewQuestion';
import { addaiinterviewdraftquestion, addaiinterviewquestion, aiinterviewdraftquestioncontent, aiinterviewquestioncontent, delaiinterviewdraftquestion, modifyaiinterviewdraftquestion, modifyaiinterviewquestion, queryaiinterviewsubjcatgs } from '@/api/interview';
import { isIdInTrees, getStarByScore } from '@/utils';
import emitter from '@/utils/bus';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import AiBot from '@/components/AiBot.vue';
import AiAideModal from '@/views/QuestionManage/components/AiAideModal.vue'
import AiAnswer from '@/components/AiAnswer.vue';
import UsefulSentence from './components/UsefulSentence.vue';
import { scrollToFirstErrFormItem } from '@/utils/common';

const store = useStore()

const props = withDefaults(defineProps<{
  qid?: string
  subjectId?: string
  draftId?: string
  /** 是否支持题型切换 */
  typeSwitch?: boolean
}>(), {
  typeSwitch: true
})

const emits = defineEmits<{
  (e: 'cancel', needRefresh?: boolean): void
}>();

const [formState] = useState<InterviewQuestionDetail>({
  type: 3,
  category: undefined,
  body: '',
  complexcontent: '',
  reference_answer: '',
  scorebasis: '', // 评分依据
  options: [
    { value: 'A', content: '', id: _.uniqueId() },
    { value: 'B', content: '', id: _.uniqueId() },
    { value: 'C', content: '', id: _.uniqueId() },
    { value: 'D', content: '', id: _.uniqueId() }
  ],
  AI_reading: 0,
  AI_reading_content: '',
  depth: 1,
  expertise: 1,
  difficulty: 35,
  complicatedediting: false,
  suggested_time: 2.0
})

// 设置初始的表单状态
let initialFormState = ref<InterviewQuestionDetail>();
function setInitialFormState() {
  initialFormState.value = _.cloneDeep(formState.value)
}
setInitialFormState()

/** 是否有改动 */
const isModified = computed(() => {
  return !_.isEqual(formState.value, initialFormState.value)
})

const rules: Record<string, Rule[]> = {
  category: [{ required: true, message: '请选择题库', trigger: 'change' }],
  body: [{ required: true, validator: checkBody(formState.value), trigger: 'blur' }],
  AI_reading_content: [{ required: true, message: '请输入', trigger: 'blur' }],
  scorebasis: [{ required: true, message: '请输入', trigger: 'blur' }],
};

// 题库
const categoryList = ref<any[]>([])
async function getCategoryList() {
  try {
    let res = await queryaiinterviewsubjcatgs()
    categoryList.value = res[0].children

    // 编辑草稿
    if (props.draftId) return

    // 如果存在传入的题库id，则需要检查一下id是否真的存在（可能被删掉）
    if (props.subjectId && isIdInTrees(props.subjectId, categoryList.value)) {
      formState.value.category = props.subjectId
      setInitialFormState()
    }
  } catch (error) {
    console.log(error)
  }
}
getCategoryList()


// 建议时间
function handleSuggestedTimeChange(value: number) {
  if (value === null) {
    // 如果删为空值，则置为0.5
    formState.value.suggested_time = 0.5
    return
  }
  if (!Number.isInteger(value) && value) {
    // 如果value不是整数，则取floor+0.5
    formState.value.suggested_time = Math.floor(value) + 0.5
  } else {
    formState.value.suggested_time = +value
  }
}

// 深度
const depthList = [
  { label: DepthEnum[1], value: DepthEnum['基础知识'] },
  { label: DepthEnum[2], value: DepthEnum['底层原理'] },
  { label: DepthEnum[3], value: DepthEnum['灵活应用'] },
  { label: DepthEnum[4], value: DepthEnum['深入思考'] },
]

// 深度
const expertiseList = [
  { label: ExpertiseEnum[1], value: ExpertiseEnum['经常用到'] },
  { label: ExpertiseEnum[2], value: ExpertiseEnum['很少用到'] },
]

// 难度
watch([() => formState.value.depth, () => formState.value.expertise], ([depth, expertise]) => {
  formState.value.difficulty = depth * 25 * 0.6 + expertise * 50 * 0.4
}, { immediate: true })


// 编辑题目回显
async function getDetail() {
  if (!props.qid) return
  let res = await aiinterviewquestioncontent({ ques_id: props.qid })
  formState.value = res

  // 编辑题目的表单初始值以获取到的数据为准
  setInitialFormState()
}

// 草稿箱回显
async function getDraftDetail() {
  if (!props.draftId) return
  let res = await aiinterviewdraftquestioncontent({ id: props.draftId })
  formState.value = res.content
  setInitialFormState()
}

// AI朗读
function handleAIReadingChange() {
  if (!formState.value.AI_reading_content) {
    formState.value.AI_reading_content = usefulSentenceRef.value?.list[0].text ?? ''
    formRef.value?.validate(['AI_reading_content'])
  }
}

// 常用语
const usefulSentenceRef = ref<InstanceType<typeof UsefulSentence>>()
function handleUsefulSentenceSelect(text: string) {
  formState.value.AI_reading_content = text
  formRef.value?.validate(['AI_reading_content'])
}

// 可能清空校对状态的保存前的确认
const confirmSaveVisible = ref(false)
const isNoMoreReminders = ref(false)
isNoMoreReminders.value = store.getters.appConfig.isEditInterviewQuestionResetProofreadConfirm || false
function handleNoMoreRemindersChange() {
  store.commit('SET_APP_CONFIG_WITH_OBJ', { isEditInterviewQuestionResetProofreadConfirm: isNoMoreReminders.value })
}

// ai生成题目，填充信息
const aiAideVisible = ref(false)
const handleAiCreated = (obj: any) => {
  aiAideVisible.value = false
  Object.assign(formState.value, obj)
  formRef.value?.validate()
}

// 提交数据预处理
function formatFormState(formState: InterviewQuestionDetail) {
  // do something
}

// 题目保存
const formRef = ref<FormInstance>()
const saveBtnLoading = ref(false)
async function handleSubmit() {
  try {
    saveBtnLoading.value = true
    await formRef.value?.validate()
    formatFormState(formState.value)
    // 判断原题目是否已校对，已校对的题目才需要弹框提醒（res为null或者res.proofreading === 0都是未校对）
    if (isModified.value && !isNoMoreReminders.value && formState.value?.proofreading) {
      confirmSaveVisible.value = true
    } else {
      await save()

    }

  } catch (error) {
    console.log(error)
    scrollToFirstErrFormItem()
  } finally {
    saveBtnLoading.value = false
  }
}

async function save() {
  confirmSaveVisible.value = false
  if (props.qid) {
    // 题目编辑
    await modifyaiinterviewquestion({ id: props.qid, question: formState.value })
    message.success('保存成功')

  } else if (props.draftId) {
    // 草稿发布（题目新增）
    Reflect.deleteProperty(formState.value, 'id')
    await addaiinterviewquestion({ question: formState.value })
    await delaiinterviewdraftquestion({ ids: [props.draftId] })
    message.success('发布成功')
    emitter.emit('updateInterviewQuestionList')

  } else {
    // 题目新增
    await addaiinterviewquestion({ question: formState.value })
    message.success('保存成功')
  }

  emits('cancel', true)
  saveBtnLoading.value = false
}

const draftSaveLoadng = ref(false)
async function handleSaveDraft() {
  try {
    await formRef.value?.validateFields(['body'])
  } catch (error) {
    return message.error('请填写题干');
  }

  draftSaveLoadng.value = true
  try {
    if (props.draftId) {
      await modifyaiinterviewdraftquestion({ id: props.draftId, question: formState.value })
      emits('cancel', true)
    } else {
      await addaiinterviewdraftquestion({ question: formState.value })
      emits('cancel')
    }
    message.success('已成功保存至草稿箱')
  } catch (error) {
    console.log(error)
  } finally {
    draftSaveLoadng.value = false
  }
}


// 询问用户是否确认退出编辑
function showConfirmExitModal() {
  return new Promise((resolve, reject) => {
    if (!formState.value.body.trim() || props.qid) {
      // 如果没有输入题干或者是编辑状态，则提醒时不提供保存草稿箱功能
      Modal.confirm({
        title: '确定退出此页面？',
        content: '系统可能不会保存您所做的更改',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          resolve('')
        }
      })
    } else {
      const modal = Modal.confirm({
        title: '是否需要保存草稿箱？',
        content: '直接退出系统不会保存当前内容',
        okText: '保存草稿箱',
        cancelText: '直接退出',
        closable: true,
        async onOk() {
          await handleSaveDraft()
          resolve('')
        },
        onCancel(e) {
          if (!e.triggerCancel) {
            modal.destroy()
            resolve('')
          }
        }
      })
    }
  })
}
async function handleCancel() {
  if (isModified.value) {
    await showConfirmExitModal();
  }
  emits('cancel');
}


/** 初始化 */
function init() {
  if (props.qid)
    getDetail()

  else if (props.draftId)
    getDraftDetail()
}
init()

</script>

<style lang="less" scoped>
.question-form-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .close-btn {
    position: absolute;
    top: -60px;
    font-size: 18px;
    right: 10px;
    cursor: pointer;
    color: #929294;
    transition: all ease 0.2s;

    &:hover {
      color: #696969;
    }
  }
}

:deep(.question-form) {
  flex: 1;
  min-height: 0;
  overflow: auto;

  .ant-form-item-label {
    width: 104px;
  }

  label {
    font-size: 12px;
    color: #626262;
  }

  .ant-input {
    font-size: 12px;
  }

  .ant-select {
    width: 200px;

    .ant-select-selector {
      font-size: 12px;
    }
  }

  .body-tip {
    display: flex;
    align-items: center;
    background: #f1f4fe;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    margin-top: 8px;

    .tip-icon {
      margin: 0 8px;
    }
  }
}

.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 16px;
  display: flex;
  align-items: center;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
    flex-shrink: 0;
  }
}
</style>

<style lang="less">
.confirm-modal {
  .ant-modal-header {
    border: none;
  }

  .ant-modal-body {
    padding: 10px 0 10px 55px;
  }

  .ant-modal-footer {
    border: none;
    padding-bottom: 22px;
    padding-top: 22px;
    padding-left: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-btn {
      border-radius: 8px;
    }
  }
}
</style>