/**
 * 环境配置封装
 * <AUTHOR>
 */

const env = import.meta.env.MODE || 'prod'
const EnvConfig = {
  dev: {
    baseApi: '/api',
    // baseApi: 'https://teacher-dev.exam.isrc.ac.cn/api/v1/portal',
    // baseApi: 'http://192.168.3.206:8081/jeecg-boot',
    mockApi: ''
  },
  prod: {
    // baseApi: '/api/v1/portal',
    baseApi: '/api',
    mockApi: ''
  }
}

export default {
  env,
  namespace: 'exam-system',
  mock: false,
  ...EnvConfig[env]
}
