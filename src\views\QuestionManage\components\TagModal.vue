<template>
  <a-modal
    v-model:visible="visible"
    :title="props.tagData.id ? '编辑标签' : '新增标签 '"
    okText="保存"
    cancelText="取消"
    :loading="loading"
    @ok="handleOk"
    @cancel="closeModal"
  >
    <a-form :model="tagForm" :label-col="labelCol">
      <a-form-item label="标签名称">
        <a-input v-model:value="tagForm.name" />
      </a-form-item>
      <a-form-item label="父标签">
        <a-input readonly v-model:value="tagForm.pname" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { tagsMange } from '@/api/admin/tagsManage'
import { useStore } from 'vuex'

const props = defineProps({
  modalVisible: {
    type: Boolean,
    default: false
  },
  tagData: {
    type: Object,
    default: () => {}
  },
  type: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['closeModal', 'refreshTagList'])

const labelCol = ref({ span: 5 })
const visible = ref(false)
const tagForm = <any>ref({})
const loading = ref(false)
const store = useStore()

const handleOk = async () => {
  if (!tagForm.value.name) {
    message.error('标签名称不能为空!')
    return
  }

  const addParams = {
    action: props.type,
    category: tagForm.value.category,
    parent: tagForm.value.parent,
    name: tagForm.value.name,
    creator: store.getters.userid
  }
  const modifyParams = {
    action: props.type,
    id: tagForm.value.id,
    name: tagForm.value.name,
    creator: store.getters.userid
  }

  try {
    loading.value = true
    await tagsMange(tagForm.value.id ? modifyParams : addParams)
    loading.value = false
    message.success(tagForm.value.id ? '标签编辑成功!' : '标签新增成功!')
    emits('refreshTagList')
    closeModal()
  } catch (error) {
    loading.value = false
  }
}

const closeModal = () => {
  emits('closeModal', false)
}

watch(
  () => props.modalVisible,
  (val) => {
    visible.value = val
  }
)

watch(
  () => props.tagData,
  (val) => {
    tagForm.value = {}
    Object.assign(tagForm.value, val)
    tagForm.value.pname = tagForm.value.pname ? tagForm.value.pname : '无'
  }
)
</script>
