<template>
  <div class="paper-anaylsis">
    <div class="header">
      <div class="headerLeft">
        <img src="@/assets/icons/svg/back.svg" @click="goBack" />
        <span>考试分析</span>
      </div>
      <img style="cursor: pointer" src="@/assets/icons/svg/close.svg" @click="goBack" />
    </div>
    <div class="content">
      <p>考试基本信息</p>
      <a-table
        :columns="columns"
        :row-key="(record:any) => record.id"
        :data-source="tableData"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <span>
              {{ record.type === 'regular' ? '指定试题' : '指定范围' }}
            </span>
          </template>
        </template>
      </a-table>
    </div>

    <div class="chart-container">
      <div class="item">
        <PieChart :data="catgQuesData" name="题库题目数量" />
      </div>
      <div class="item">
        <BarChart :data="catgCorrectRateData" name="题库正确率" />
      </div>
      <div class="item">
        <LineChart :data="submitData" name="交卷情况" />
      </div>
      <div class="item">
        <BarChart :data="scoreData" name="分数段分布" />
      </div>
      <div class="item table">
        <h3>错题排行</h3>
        <a-table
          :columns="wrongQuesColumns"
          :row-key="(record:any) => record.id"
          :data-source="wrongData"
          :pagination="false"
        >
        </a-table>
      </div>
      <div class="item table">
        <h3>学生成绩排行</h3>
        <a-table
          :columns="scoreColumns"
          :row-key="(record:any) => record.id"
          :data-source="scoreRateData"
          :locale="{ filterConfirm: '确定', filterReset: '重置', emptyText: '暂无数据' }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sex'">
              <span>
                {{ record.sex }}
              </span>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <span class="close-btn" @click="goBack">关闭</span>
  </div>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { paperreport } from '@/api/admin/statisticAnalysis'
import PieChart from './components/PieChart.vue'
import LineChart from './components/LineChart.vue'
import BarChart from './components/BarChart.vue'

const columns = [
  {
    title: '考试名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    ellipsis: false
  },
  {
    title: '创建人',
    dataIndex: 'teacher',
    key: 'teacher',
    ellipsis: false
  },

  {
    title: '考试时间',
    dataIndex: 'time',
    key: 'time',
    ellipsis: false
  },
  {
    title: '考试时长',
    dataIndex: 'duration',
    key: 'duration',
    align: 'right',
    ellipsis: false
  },
  {
    title: '题目数量',
    dataIndex: 'quesnum',
    key: 'quesnum',
    align: 'right',
    ellipsis: false
  },
  {
    title: '应考人数',
    dataIndex: 'candidates',
    key: 'candidates',
    align: 'right',
    ellipsis: false
  },
  {
    title: '实考人数',
    dataIndex: 'realstus',
    key: 'realstus',
    align: 'right',
    ellipsis: false
  },
  {
    title: '缺考人数',
    dataIndex: 'absentees',
    key: 'absentees',
    align: 'right',
    ellipsis: false
  },
  {
    title: '平均分',
    dataIndex: 'avgscore',
    key: 'avgscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '最高分',
    dataIndex: 'hscore',
    key: 'hscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '及格线',
    dataIndex: 'pscore',
    key: 'pscore',
    align: 'right',
    ellipsis: false
  },
  {
    title: '及格率',
    dataIndex: 'prate',
    key: 'prate',
    align: 'right',
    ellipsis: false
  }
]
const scoreColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex',
    ellipsis: false
  },
  {
    title: '考试进入时间',
    dataIndex: 'start',
    key: 'start',
    ellipsis: false
  },

  {
    title: '交卷时间',
    dataIndex: 'completedtime',
    key: 'completedtime',
    ellipsis: false
  },
  {
    title: '答题时长',
    dataIndex: 'duration',
    key: 'duration',
    align: 'right',
    ellipsis: false
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    align: 'right',
    ellipsis: false
  }
]
const wrongQuesColumns = [
  {
    title: '题库',
    dataIndex: 'catg',
    key: 'catg'
  },
  {
    title: '题干',
    dataIndex: 'body',
    key: 'body',
    ellipsis: true
  },
  {
    title: '题型',
    dataIndex: 'type',
    key: 'type',
    ellipsis: false
  },

  {
    title: '错误次数',
    dataIndex: 'wrong',
    key: 'wrong',
    align: 'right',
    ellipsis: false
  },
  {
    title: '错误率(百分制)',
    dataIndex: 'wrate',
    key: 'wrate',
    align: 'right',
    ellipsis: true
  }
]

const tableData = ref<any>([])
const catgQuesData = ref<any>([])
const catgCorrectRateData = ref<any>([])
const submitData = ref<any>({})
const scoreData = ref<any>([])
const scoreRateData = ref<any>([])
const wrongData = ref<any>([])

const router = useRouter()
const goBack = () => {
  router.push('/admin/statisticAnalysis/testPaperAnalysis')
}

const route = useRoute()

watch(
  () => route.query.id,
  (val) => {
    if (val) {
      paperreport({ id: val }).then((data: any) => {
        tableData.value = [data.basicinfo]
        catgQuesData.value = data.catg_ques_dist
        catgCorrectRateData.value = data.catg_correct_dist
        submitData.value = data.subcondition
        scoreData.value = data.score_dists
        scoreRateData.value = data.score_ranks
        wrongData.value = data.wrong_table
      })
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less">
.paper-anaylsis {
  padding: 32px 32px 0;
  .header {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-bottom: 32px;
    // padding: 32px;

    .headerLeft {
      display: flex;
      align-items: center;
      img {
        width: 36px;
        cursor: pointer;
      }
      span {
        font-size: 20px;
        font-weight: 700;
      }
    }
  }
  .content {
    margin: 0 auto;
    width: 100%;
    padding: 24px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    p {
      font-size: 20px;
      margin-bottom: 34px;
    }
  }
  .chart-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    .item {
      width: 49%;
      height: 292px;
      overflow: auto;
      background: #fff;
      margin-bottom: 20px;
      &.table {
        padding: 16px;
        h3 {
          font-size: 15px;
          font-weight: bold;
          margin-bottom: 20px;
        }
      }
    }
    .item:nth-child(2n + 1) {
      margin-right: 20px;
    }
  }
  .close-btn {
    display: block;
    margin: 0 auto;
    width: 60px;
    line-height: 32px;
    text-align: center;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 20px;
  }
  .ant-table-thead > tr > th {
    font-weight: bold;
    &:first-child {
      padding-left: 8px !important;
    }
    background: #f1f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-size: 14px;
    p,
    span {
      font-family: PingFang HK;
      font-size: 14px;
      color: #121633;
      text-align: center;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
}
</style>
