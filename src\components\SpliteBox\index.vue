<template>
  <div class="splite-box">
    <div class="lf" ref="leftDom">
      <slot name="lArea"></slot>
      <div class="touch-div" ref="moveDom">
        <span></span>
        <span></span>
      </div>
    </div>
    <div class="rt" ref="rightDom">
      <slot name="rArea"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

let leftDom = ref()
let moveDom = ref()
let rightDom = ref()

const handleMove = () => {
  moveDom.value.onmousedown = function (e: any) {
    const iEvent = e
    const dx = iEvent.clientX //当你第一次单击的时候，存储x轴的坐标。//相对于浏览器窗口
    const leftWidth = leftDom.value.offsetWidth
    const rightWidth = rightDom.value.offsetWidth
    document.onmousemove = function (e) {
      const iEvent = e
      const diff = iEvent.clientX - dx //移动的距离（向左滑时为负数,右滑时为正数）
      if (280 < leftWidth + diff && 480 < rightWidth - diff) {
        //两个div的最小宽度均为100px
        leftDom.value.style.width = leftWidth + diff + 'px'
        rightDom.value.style.width = rightWidth - diff + 'px'
      }
    }
    document.onmouseup = function () {
      document.onmousedown = null
      document.onmousemove = null
    }
    return false
  }
}

onMounted(() => {
  handleMove()
})
</script>

<style lang="less" scoped>
.splite-box {
  width: 100%;
  height: 100%;
  display: flex;
  .lf {
    height: 100%;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    width: 280px;
    padding: 10px 20px;
    position: relative;
  }
  .lf .touch-div {
    position: absolute;
    top: 0;
    height: 100%;
    left: 100%;
    width: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: col-resize;
  }
  .lf .touch-div span {
    width: 2px;
    background: #bbb;
    margin: 0 2px;
    height: 15px;
  }
  .rt {
    height: 100%;
    overflow-y: auto;
    flex: 1;
    padding-top: 10px !important;
  }
}
</style>
