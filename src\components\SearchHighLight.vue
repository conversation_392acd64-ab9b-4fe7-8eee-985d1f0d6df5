<template>
    <span v-if="text && (text?.indexOf(searchText) > -1)">{{ text.substring(0,
        text?.indexOf(searchText)) }}<span style="color: #5478ee;font-weight: bold;">{{ searchText }}</span>{{
        text.substring(text?.indexOf(searchText) + searchText.length) }}</span>
    <span v-else>{{ text }}</span>
</template>

<script lang="ts" setup>
const props = defineProps<{
    text: string
    searchText: string
}>()

</script>

<style lang="less" scoped></style>