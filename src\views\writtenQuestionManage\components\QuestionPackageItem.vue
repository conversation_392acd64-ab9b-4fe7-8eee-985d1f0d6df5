<script setup lang="ts">
import { CheckOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'
import { WarningFilled } from '@element-plus/icons-vue'
import { ElInput, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { deletePackageItem, updatePackageApi } from '../hooks/api'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'package', // 'package' | 'selection'
  },
})
const emits = defineEmits(['select'])

const router = useRouter()

const wrapItem = ref(JSON.parse(JSON.stringify(props.item)))
const rawItem = JSON.parse(JSON.stringify(props.item))
const pkg = inject('pkg', {
  getPkgList: () => {},
})
const activePopupPath = defineModel('activePopupPath')
const loading = ref(false)

const confirmVis = ref(false)
const impactRes = ref<any>([])
const dialogVisible = ref(false)
const currentItem = ref<any>(null)
async function deletePkgConfirm(item: any) {
  activePopupPath.value = item.path
  const result = await deletePackageItem({
    path: (item as any).path,
    confirm: false,
  })
  impactRes.value = result || []
  if (!impactRes.value.length) {
    confirmVis.value = true
    return
  }
  dialogVisible.value = true
  currentItem.value = item
  return
  ElMessageBox.confirm(
    `确认删除当前题库及所含题目吗？可能会影响以下AI面试：<br/>${impactRes.value.map((item: any) => `- ${item.title}`).join('<br/>')}`,
    '',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    },
  )
    .then(() => {
      confirm(item)
    })
    .catch(() => {
      cancel()
    })
}
async function confirm(item: any) {
  activePopupPath.value = ''
  await deletePackageItem({
    path: (item as any).path,
    confirm: true,
  })
  pkg.getPkgList()
  cancel()
}
function cancel() {
  activePopupPath.value = ''
  confirmVis.value = false
  dialogVisible.value = false
  currentItem.value = null
  impactRes.value = null
}

const editable = ref(false)

const editInputRef = useTemplateRef<typeof ElInput>('editInput')
function editPackageItemInfo(e: any) {
  e.stopPropagation()
  editable.value = true
  nextTick(() => {
    editInputRef.value?.focus()
  })
}
function clickItem(e: any) {
  e.stopPropagation()
  if (props.type === 'selection')
    return selectItem(e)
  if (props.type === 'package') {
    return blurEdit(e, true)
  }
}
function blurEdit(e: any, saveFlag: boolean = true) {
  e.stopPropagation()
  if (!editable.value) 
    return
  editable.value = false
  if (saveFlag) {
    saveEditInfo()
  }
  else {
    Object.keys(wrapItem.value).forEach((key) => {
      wrapItem.value[key] = rawItem[key]
    })
    pkg.getPkgList()
  }
}
async function saveEditInfo() {
  try {
    loading.value = true
    await updatePackageApi({
      ...wrapItem.value,
      path: `/${wrapItem.value.name}`,
    })
  }
  catch (e: unknown) {
    console.log(e)
  }
  finally {
    loading.value = false
    editable.value = false
    pkg.getPkgList()
  }
}

function goDetail() {
  router.push({
    name: 'writtenQuestionList',
    query: { 
      name: wrapItem.value.name,
      path: wrapItem.value.path,
      id: wrapItem.value.id,
    },
  })
}

function selectItem(e: any) {
  e.stopPropagation()
  if (props.type !== 'selection') 
    return
  if (props.type === 'selection') {
    if (!props.item.correctTotal) 
      return
    emits('select', props.item)
  }
}

function domBlur(e: any) {
  blurEdit(e, true)
}
onMounted(() => {
  document.addEventListener('click', domBlur)
})
onBeforeUnmount(() => {
  document.removeEventListener('click', domBlur)
})
</script>

<template>
  <div v-loading="loading" class="container p-[16px] rounded-[8px] box-border" :class="[type === 'selection' ? item.correctTotal ? 'cursor-pointer' : 'cursor-not-allowed' : '', item.selected ? 'selected-border' : '']" @click.stop="clickItem">
    <div class="rounded-[8px] p-[8px] py-[16px] flex flex-wrap bg-file">
      <div class="flex flex-nowrap w-full justify-between items-center relative">
        <div v-show="!editable" class="w-[80%] flex flex-nowrap">
          <img src="@/assets/icons/svg/file_icon.svg" class="mr-[12px]">
          <div class="font-bold text-[18px] text-ellipsis whitespace-nowrap overflow-hidden">
            <span>{{ item.name }}</span> 
          </div>
        </div>
        <div v-show="!editable" class="relative" @click.stop="selectItem">
          <a-tooltip title="编辑" v-if="!disabled">
            <EditOutlined v-if="type === 'package'" class="mr-[12px] cursor-pointer operation-btn normal-op-0" @click.stop="editPackageItemInfo" />
          </a-tooltip>
          <a-tooltip title="删除" v-if="!disabled">
            <DeleteOutlined v-if="type === 'package'" class="cursor-pointer operation-btn  normal-op-0" @click="deletePkgConfirm(item as any)" />
          </a-tooltip>  
          <div v-if="type === 'selection'" class="flex items-center p-[2px] justify-center w-[16px] h-[16px] rounded-[50%]" :class="[item.selected ? 'bg-[#597cec] opacity-1' : 'operation-btn operation-btn-checkbox', item.correctTotal ? 'cursor-pointer' : 'cursor-not-allowed']">
            <CheckOutlined v-show="item.selected" style="color:#fff;font-size: 12px;" />  
          </div>
          <div v-show="confirmVis && activePopupPath === item.path" class="show-arrow absolute bg-[#fff] z-[99] shadow-[0px_6px_18px_0px_rgba(0,0,0,0.10)] rounded-[8px] p-[16px] w-[200px] text-center">
            <div class="w-full text-start px-[8px]">
              <span class="i-fluent-emoji-warning mr-[3px]" /> 
              <!-- {{ impactRes.length ? `` : '确认删除当前题库及所含题目吗？' }} -->
              确认删除当前题库及所含题目吗？
            </div>
            <div class="w-full flex justify-end mt-[8px]">
              <a-button size="small" type="primary" class="mr-[8px]" @click="confirm(item)">
                确认
              </a-button>
              <a-button size="small" @click="cancel">
                取消
              </a-button>
            </div>
          </div>
        </div>
        <ElInput v-show="editable" ref="editInput" v-model="wrapItem.name" maxlength="16" @click.stop="editable = true" @keyup.enter="blurEdit" @keyup.esc="(e:any) => blurEdit(e, false)">
          <template #prefix>
            <img src="@/assets/icons/svg/file_icon.svg" class="mr-[12px]">
          </template>
        </ElInput>
      </div>

      <div class="my-[16px] w-full">
        <div v-show="!editable" class="show-desc">
          {{ item.description || '暂无描述' }} 
        </div>
        <ElInput v-show="editable" v-model="wrapItem.description" maxlength="52" resize="none" :rows="3" placeholder="暂无描述" type="textarea" style="width: 100%;" @keyup.enter="blurEdit" @keyup.esc="blurEdit" @click.stop="editable = true" />
      </div>
            
      <div class="flex flex-wrap min-h-[30px] w-full">
        <div v-for="tag in item.children.filter((_:any, index:any) => index <= 2)" :key="tag.path" class="text-[12px] mb-[8px] mr-[3px] px-[5px] py-[1px] text-[#5478ee] bg-[#f1f4fe] rounded-[2px]">
          {{ tag.name }}
        </div>
      </div>
    </div>
    <el-divider style="margin-top: 8px;" />
    <div v-if="type === 'package'" class="flex flex-nowrap w-full justify-between mt-[24px]">
      <div><span class="text-[20px] text-[#5B7DEF]">{{ item.total }}</span> 题</div>
      <a-button v-show="props.type !== 'selection'" type="primary" @click.stop="goDetail">
        查看题目
      </a-button>
    </div>
    <div v-if="type === 'selection'" class="progress-bar w-full justify-between mt-[24px] flex flex-wrap">
      <div class="w-full flex justify-between">
        <span class="mt-[12px]">可用题量/总题数</span>
        <span class="text-[#5478ee]">
          <span class="text-[26px]">{{ item.correctTotal || 0 }}</span> / {{ item.total }}</span>
      </div>
      <a-progress :percent="(item.correctTotal / item.total) * 100" :show-info="false" stroke-color="linear-gradient(90deg,#78c5ff 1%, rgba(84,120,238,0.85))" />
    </div>
  </div>
  
  <el-dialog
    v-model="dialogVisible"
    width="500"
    :before-close="cancel"
  >
    <template #title>
      <div class="text-[18px] flex items-center ">
        <el-icon class="mr-[12px]">
          <WarningFilled color="#e6a23c" />
        </el-icon>
        提示
      </div>
    </template>
    <div class="pl-[12px] mb-[10px] mt-[15px]">
      确认删除当前题库及所含题目吗？可能会影响以下AI面试：
    </div>
    <div v-for="item in impactRes" :key="item.title" class="pl-[12px] mb-[8px]">
      - {{ (item as any).title }}
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">
          取消
        </el-button>
        <el-button type="primary" @click="confirm(currentItem.value)">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.progress-bar{
  :deep(.ant-progress-bg){
    background-image: linear-gradient(90deg, #78c5ff 1%, rgba(84, 120, 238, 0.85))!important;
  }
}
.show-desc{
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.bg-file{
  background-image: url('../assets/glass.svg');
  background-repeat: no-repeat;
  background-position: right -20px;
}
.show-arrow{
  transform: translateY(-126%) translateX(-69%);
}
.show-arrow::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 90%;
  transform: translateX(-50%);
  border-width: 8px;
  border-style: solid;
  border-color: #fff transparent transparent transparent;

}
.operation-btn{
  opacity: 1;
}
.normal-op-0 {
  opacity: 0;
  padding: 3px;
  &:hover{
    border-radius: 3px;
    background-color: #ccd8ff;
  }
}
.operation-btn-checkbox {
  border: 1px solid #dedede;
  background-color: #fff;
}
.container {
  transition: all 0.5s ease;
  background: linear-gradient(180deg,#e7edff, #ffffff 50%);
  border: 2px solid #fff;
  border-radius: 8px;
  box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.10); 
  :deep(.el-input__wrapper){
    background: transparent;
  }
  &:hover{
    box-shadow: 0px 6px 18px 0px rgba(0,0,0,0.10); 
  }
}
.container:hover .operation-btn{
  opacity: 1;
}
.selected-border{
  // border: 1px solid #5478ee;
  border-color: #5478ee;
  box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.10); 
}
:deep(.el-input__wrapper){
  border: 2px solid #5478ee;
}
</style>