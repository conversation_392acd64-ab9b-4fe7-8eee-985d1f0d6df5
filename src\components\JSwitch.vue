<template>
    <div class="j-switch" :class="{checked}" @click="handleClick">
        <div class="switch-handle"></div>
    </div>
</template>

<script lang="ts" setup>
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import {InfoCircleFilled } from '@ant-design/icons-vue'
const props = defineProps({
    checked: {
        type: Boolean,
        default: false
    }
})

const emits = defineEmits(['update:checked', 'change'])

function handleClick () {
    if (props.checked) {
        Modal.confirm({
          title: '确定切换为普通编辑？',
          icon: () => createVNode(InfoCircleFilled),
          content: '将丢弃复杂编辑器中的公式图片',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            emitChange()
          }
        })
    } else {
        emitChange()
    }
}

function emitChange () {
    let checked = !props.checked
    emits('update:checked', checked)
    emits('change', checked)
}

</script>

<style lang="less" scoped>
.j-switch {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    width: 44px;
    height: 22px;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 100px;
    cursor: pointer;

    &.checked {
        background-color: #5478EE;
        .switch-handle {
            left: 24px;
        }
    }

    .switch-handle {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background-color: #fff;
        position: absolute;
        left: 2px;
        top: 2px;
        transition: all 0.1s ease-in-out;
    }
}

</style>