<template>
    <div class="subjects">
        <div class="category-container" :style="{ width: boxWidth + 'px' }">
            <div class="header">
                <h2>选择题库范围</h2>
                <div class="search-ipt-wrapper" :class="{ active: searchOpen }">
                    <a-input ref="searchIptRef" class="search-ipt" v-model:value.trim="searchValue"
                        @blur="searchOpen = false" :bordered="false" placeholder="请输入关键词" allowClear>
                    </a-input>
                    <SearchOutlined @click="handleSearch" />
                </div>
            </div>
            <div class="body">
                <a-spin v-if="fetchLoading" class="loading-style"></a-spin>
                <div v-else-if="!fetchLoading && !treeData.length" class="common-no-data"></div>
                <a-tree v-else checkable show-line :checkStrictly="true" :blockNode="true" :tree-data="treeData"
                    :autoExpandParent="autoExpandParent" :field-names="{ title: 'name', key: 'id' }"
                    v-model:expandedKeys="expandedKeys" :selectedKeys="selectedKeys" v-model:checkedKeys="checkedKeys"
                    @select="onSelect" @expand="onExpand" @check="onCheck">
                    <template #title="item">
                        <div class="custom-title" @mouseover="(e: any) => onHover(e, item)" @mouseleave="start">
                            <SearchHighLight class="title" :text="item.name" :search-text="searchValue">
                            </SearchHighLight>
                            <span class="badge-dot" v-if="item.checkedChildrenNum"></span>
                        </div>
                    </template>

                    <template #switcherIcon="{ switcherCls }">
                        <Icon icon="DownOutlined" :class="switcherCls" />
                    </template>
                </a-tree>

            </div>
            <div class="specific-info" v-if="hoverItem" :style="{ top: hoverItem.top + 'px' }" @mouseover="stop"
                @mouseleave="start">
                <div class="specific-info-row" style="height: 28px;">
                    <div style="background-color: #f1f4fe;">题型</div>
                    <div style="background-color: #f1f4fe;">题量</div>
                </div>
                <ul>
                    <li v-for="t in Object.keys(hoverItem.type_ques_count)" class="specific-info-row"
                        style="height: 24px;">
                        <div class="label">
                            {{ InterviewQuestionEnum[t] }}
                        </div>
                        <div class="value" style="color: rgba(0,0,0,0.45);">{{ hoverItem.type_ques_count[t] }}道</div>
                    </li>
                </ul>
                <a-divider style="margin: 4px 0;"></a-divider>
                <div class="specific-info-row" style="height: 28px;">
                    <span><svg-icon name="exam-m" style="margin-right: 4px;font-size: 16px;"></svg-icon>合计</span>
                    <span>{{ hoverItem.num }}道</span>
                </div>
            </div>
            <div id="resizer" @mousedown="startResize"
                style="width:4px;height:100%;position:absolute;right:0;top:0;cursor:e-resize;border-right:2px solid #5478EE;transition:all ease 0.2s;opacity: 0;">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import SearchHighLight from '@/components/SearchHighLight.vue'
import { useTimeoutFn } from '@vueuse/core'
import { aiinterviewcatgquestypecount } from '@/api/interview'
import { InterviewQuestionEnum } from '@/types/interviewQuestion'
import _ from 'lodash'

const props = defineProps<{
    checkedKeys?: {
        checked: string[]
        halfChecked: string[]
    }
}>()

type TreeNode = {
    key?: string
    id: string
    name: string
    is_official_cert: boolean
    children: TreeNode[]
    pathName: string
    num: number
    type_ques_count: Record<number, number>
    isLeaf: boolean
    disableCheckbox?: boolean
    /** 有多少子节点被选中 */
    checkedChildrenNum?: number
    parent?: TreeNode
}

const emits = defineEmits<{
    /** 这里只会统计当前选中的节点，选中节点的子节点将不被统计 */
    (e: 'updateChecked', value: any[]): void
    (e: 'updateSingleChecked', type: 'checked' | 'unchecked', value: TreeNode): void
}>()

const treeData = ref<TreeNode[]>([])
const expandedKeys = ref<any[]>([''])
const selectedKeys = ref([''])

const onSelect = (keys: string[], { node }: any) => {
    if (keys.length === 0) return
    selectedKeys.value = keys
}

const onExpand = (keys: string[]) => {
    expandedKeys.value = keys;
    autoExpandParent.value = false;
}

// 节点选中
interface CheckedKeys {
    checked: string[]
    halfChecked: string[]
}
const checkedKeys = ref<CheckedKeys>({
    checked: [],
    halfChecked: []
})
watch(() => props.checkedKeys, (v) => {
    try {
        if (v && treeData.value.length) {
            checkedKeys.value = v
            updateCheck(v)
        }
    } catch (error) {
        console.error(error)
    }
}, { deep: true })

/** 根据数据整体更新选中状态 */
async function updateCheck({ checked }: CheckedKeys) {
    let totalCheckedKeys = restCheckedKeys.concat(checked)
    let ans: any[] = []
    let fn = (arr: any[]) => {
        if (!arr?.length) return
        arr.forEach(item => {
            let index = totalCheckedKeys.indexOf(item.id)
            if (index > -1) {
                ans[index] = item
            }
            fn(item.children)
        })
    }
    fn(originTreeData.value)
    emits('updateChecked', ans)
    await nextTick()
    updateBadgeViaTotalCheckedKeys(totalCheckedKeys)
}

/** 处理选中事件，逐个更新选中状态 */
async function onCheck({ checked }: CheckedKeys, e: any) {
    let totalCheckedKeys = restCheckedKeys.concat(checked)

    emits('updateSingleChecked', e.checked ? 'checked' : 'unchecked', e.node.dataRef)
    await nextTick()
    updateBadgeViaTotalCheckedKeys(totalCheckedKeys)
}

/**
 * Updates the badge count based on the total checked keys.
 *
 * @param {string[]} totalCheckedKeys - An array of keys representing the checked items.
 */
function updateBadgeViaTotalCheckedKeys(totalCheckedKeys: string[]) {
    // 统计选中的子节点数量
    calcCheckedChildrenNum(originTreeData.value, totalCheckedKeys)
    // diff比较新旧树，将旧树中的数字同步过来
    assignTreeData(originTreeData.value, treeData.value)
}

/**
 * Assigns the `checkedChildrenNum` property from the `oldTree` to the corresponding nodes in the `newTree`.
 *
 * @param {TreeNode[]} oldTree - The original tree data.
 * @param {TreeNode[]} newTree - The new tree data.
 */
function assignTreeData(oldTree: TreeNode[], newTree: TreeNode[]) {
    let oldArr = [...oldTree];
    let oldIndex = 0;

    let newArr = [...newTree];
    let newIndex = 0;

    while ((newIndex < newArr.length, oldIndex < oldArr.length)) {
        let newNode = newArr[newIndex];

        while (oldArr[oldIndex].id !== newNode.id) {
            oldIndex++;
        }

        let oldNode = oldArr[oldIndex];

        if (oldNode.checkedChildrenNum != newNode.checkedChildrenNum) {
            newNode.children?.length && newArr.push(...newNode.children);
            oldNode.children?.length && oldArr.push(...oldNode.children);
        }

        newNode.checkedChildrenNum = oldNode.checkedChildrenNum;

        oldIndex++;
        newIndex++;
    }
}

function setCheckedKeys(v: CheckedKeys, cb?: Function) {
    checkedKeys.value = v

    if (originTreeData.value.length !== 0) {
        updateCheck(v) // 执行时很可能源数据还没请求完，导致执行无效
        cb?.()
    } else {
        getSubjectDataCallBackFn = () => {
            updateCheck(v)
            cb?.()
        }
    }
}

// 统计选中的子节点数量
function calcCheckedChildrenNum(arr: TreeNode[], checkedKeys: string[]): number {
    return arr.reduce((p, c) => {
        c.checkedChildrenNum = calcCheckedChildrenNum(c.children, checkedKeys)
        let ans = p + c.checkedChildrenNum
        if (checkedKeys.includes(c.id)) {
            ans++
        }
        return ans
    }, 0)
}

// 获取源数据的回调(在获取之前或者过程中可能需要执行一个依赖与源数据的方法,此方法会在获取完源数据后被调用)
let getSubjectDataCallBackFn: Function | null = null

const originTreeData = ref<TreeNode[]>([])
const fetchLoading = ref(false)
async function getSubjectData() {
    fetchLoading.value = true
    try {
        let res: any = await aiinterviewcatgquestypecount()
        res = Array.isArray(res) ? res : []
        setPathNameAndNum(res)
        treeData.value = _.cloneDeep(res)
        originTreeData.value = _.cloneDeep(res)
        getSubjectDataCallBackFn?.()
        getSubjectDataCallBackFn = null
    } finally {
        fetchLoading.value = false
    }
}

// 设置每一层级的树节点的路劲名称
function setPathNameAndNum(arr: TreeNode[], parentPathName = '', parent?: TreeNode) {
    if (!arr?.length) return
    arr.forEach(item => {
        item.checkedChildrenNum = 0 // 初始化子节点选中数量
        item.parent = parent
        item.pathName = parentPathName ? parentPathName + '>' + item.name : item.name
        item.num = Object.values(item.type_ques_count).reduce((p, c) => p + c, 0)
        if (item.num === 0) {
            item.disableCheckbox = true
        }
        if (item.children?.length) {
            setPathNameAndNum(item.children, item.pathName, item)
            item.isLeaf = false
        } else {
            item.isLeaf = true
        }
    })
}

// 科目搜索
const searchIptRef = ref()
const searchValue = ref('')
const searchOpen = ref(false)
const autoExpandParent = ref(false)
function handleSearch() {
    searchOpen.value = true
    searchIptRef.value.focus()
}

function getParentId(id: string | number, tree: TreeNode[]): string | number | undefined {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
            if (node.children.some(item => item.id === id)) {
                parentKey = node.id;
            } else if (getParentId(id, node.children)) {
                parentKey = getParentId(id, node.children);
            }
        }
    }
    return parentKey;
}
let restCheckedKeys: string[] = [] // 被过滤了且已选中的节点id集合
watch(searchValue, (val) => {
    if (val === '' && restCheckedKeys?.length) {
        checkedKeys.value.checked.push(...restCheckedKeys)
        restCheckedKeys = []
    }
    treeData.value = filterTree(originTreeData.value, val)
    autoExpandParent.value = true
    if (val) return
    // 如果搜索词为空字符串，则只展开选中科目
    let id = getParentId(selectedKeys.value[0], originTreeData.value)
    expandedKeys.value = [id]
})

function filterTree(roots: TreeNode[], filterText: string): TreeNode[] {
    return roots.map((root) => filterSingleNode(root, filterText)).filter(Boolean) as TreeNode[];
}

function filterSingleNode(node: TreeNode, filterText: string): TreeNode | null {
    if (node.name.includes(filterText)) {
        // 如果当前节点符合条件，返回当前节点
        if (filterText) {
            let id = getParentId(node.id, originTreeData.value)
            expandedKeys.value.push(id)
        }
        return {
            ...node,
            children: filterTree(node.children, filterText),
        };
    } else {
        // 如果当前节点不符合条件，递归过滤子节点
        const filteredChildren = filterTree(node.children, filterText);

        // 如果自己和子节点不符合筛选条件且已经被勾选了（过滤后压根不展示），这里要记录下来，后续需要拼接。解决oncheck时，被过滤调的节点勾选丢失的问题。
        if (checkedKeys.value.checked.includes(node.id) && filteredChildren.length !== 0) {
            restCheckedKeys.push(node.id)
        }

        if (filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren,
            };
        } else {
            return null; // 如果当前节点及其子节点都不符合条件，返回null
        }
    }
}

// 查看每个题库的题目数量分布
const hoverItem = ref<any>()
function onHover(e: any, item: any) {
    if (isPending) stop()
    item.top = e.pageY - 150
    hoverItem.value = item
}
const { start, stop, isPending } = useTimeoutFn(() => {
    hoverItem.value = null
}, 200)


// 宽度拖拽
const lastBoxWidth = ref(240)
const BOX_MAX_WIDTH = 400
const boxWidth = ref(240)
const startX = ref(0);
const isMoving = ref(false)
const startResize = (e: MouseEvent) => {
    startX.value = e.clientX;
    console.log('startX:', startX.value)
    isMoving.value = true
    window.addEventListener('mousemove', resize);
    window.addEventListener('mouseup', stopResize);
};

const resize = (e: MouseEvent) => {
    if (!isMoving.value) return
    const newWidth = lastBoxWidth.value + e.clientX - startX.value;
    boxWidth.value = Math.min(newWidth, BOX_MAX_WIDTH);
};

const stopResize = () => {
    isMoving.value = false
    lastBoxWidth.value = boxWidth.value
    window.removeEventListener('mousemove', resize);
    window.removeEventListener('mouseup', stopResize);
};



onMounted(async () => {
    await getSubjectData()
    selectedKeys.value = [treeData.value[0].id]
    expandedKeys.value = treeData.value.map(item => item.id)
})

onUnmounted(() => {
    stopResize()
})

defineExpose({
    setCheckedKeys
})

</script>

<style lang="less" scoped>
:deep(.ant-tree) {
    width: min-content;

    .ant-tree-checkbox {
        margin-top: 8px !important;
    }

    .ant-tree-node-content-wrapper {
        padding-left: 4px !important;
    }

}

.subjects {
    height: 100%;
    user-select: none;
    border: 1px solid #e8e8e8;

    .collapse-menu-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 48px;
        background: #ffffff;
        border: 1px solid #e8e8e8;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
        z-index: 999;
        cursor: pointer;
    }

    .menu-icon {
        cursor: pointer;
    }

    .category-container {
        height: 100%;
        min-width: 240px;
        flex-shrink: 0 !important;
        background: #fff;
        display: flex;
        flex-direction: column;
        position: relative;

        .specific-info {
            width: 200px;
            padding: 12px 8px;
            position: absolute;
            left: calc(100% + 3px);
            border-radius: 4px;
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
            font-size: 12px;
            background-color: #fff;
            transition: top ease .1s;

            .specific-info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;

                >div,
                span {
                    flex: 1;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    padding: 0 12px;

                    &:first-child {
                        margin-right: 2px;
                    }
                }

                >ul {
                    line-height: 28px
                }
            }
        }

        #resizer:hover {
            opacity: 1 !important;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            line-height: 48px;
            padding: 0 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;

            h2 {
                font-family: PingFang SC;
                font-size: 14px;
                line-height: inherit;
                color: #121633;
                font-weight: bold;
            }

            .plus-icon {
                cursor: pointer;
            }

            .addsubject {
                display: none;
            }

            .search-ipt-wrapper {
                position: absolute;
                right: 0;
                background-color: #fff;
                padding: 4px;
                border-radius: 8px;
                display: flex;

                &.active {
                    border: 1px solid #5478ee;

                    .search-ipt {
                        padding: 0 2px;
                        width: 200px;
                    }
                }

                .anticon {
                    cursor: pointer;
                    padding: 4px;
                }

                .anticon-search {
                    z-index: 999;
                    border-radius: 4px;
                    background-color: #fff;

                    &:hover {
                        background-color: #e8e8e8;
                    }
                }

                .search-ipt {
                    width: 0;
                    height: 22px;
                    padding: 0;
                    border: none;
                    box-shadow: none;

                    :deep(.ant-input) {
                        font-size: 13px;
                    }
                }
            }
        }
    }

    .loading-style {
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body {
        padding-left: 16px;
        padding-top: 8px;
        overflow: auto;
        // height: calc(100% - 50px);
        flex: 1;
        min-height: 0;
    }

    :deep(.ant-tree-node-content-wrapper) {
        flex: auto;
        min-width: 0;
    }

    :deep(.ant-tree-title) {
        flex: auto;
        min-width: 0;
    }

    .custom-title {
        max-width: 100%;
        height: 24px;
        padding-right: 6px;
        display: flex;
        align-items: center;

        .title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .badge-dot {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #FF4D4F;
            position: relative;
            top: -6px;
        }
    }
}
</style>