<template>
  <div class="resume-table">
    <a-table
      :columns="data.columns.filter(item => item.show)"
      :rowKey="(record:any) => record.resumeId",
      :data-source="data.resumeList"
      :row-selection="{
        selectedRowKeys: selectedKeys,
        onChange: onSelectChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        getCheckboxProps: handleGetCheckboxProps
      }"
      :loading="data.resumeLoading"
      :scroll="{ x: 1200, y: scrollY }"
      :pagination="props.pagination"
      @change="handleTableChange"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'matchScore'">
          <span>
            <!-- 匹配度在[0,70%)为不符，在[70%,75%)为欠佳，在[75%,80%)为尚可，在[80%,85%)为胜任，在[85%,100%)为卓越 -->
            <span v-if="record.matchScore < 70">不符</span>
            <span v-else-if="record.matchScore < 75">欠佳</span>
            <span v-else-if="record.matchScore < 80">尚可</span>
            <span v-else-if="record.matchScore < 85">胜任</span>
            <span v-else>卓越</span>
          </span>
        </template>
        <template v-else-if="column.key === 'resumeStatus'">
            <a-tag color="green" v-show="record.resumeStatus === '合适'"> {{ record.resumeStatus }}</a-tag>
            <a-tag color="red" v-show="record.resumeStatus === '不合适'"> {{ record.resumeStatus }}</a-tag>
            <a-tag type="info" v-show="record.resumeStatus === '未筛选'"> {{ record.resumeStatus }}</a-tag>
            <a-tag color="warning" v-show="record.resumeStatus === '待定'"> {{ record.resumeStatus }}</a-tag>
        </template>
        <template v-else-if="column.key === 'interviewStatus'">
            <span class="inline-block w-[8px] h-[8px] rounded-[50%] mr-[6px]" :style="{ backgroundColor: statusColorMap[record.interviewStatus as keyof typeof statusColorMap] }"/>
            {{ record.interviewStatus }}
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-button type="text" size="small" @click="createInterview(record)">
              <span style="color:#5478EE;display: inline-block;width: 60px;">{{ record.interviewStatus === '未安排' ? '创建面试' : 'AI面试' }}</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="getDetailResume(record)" >
              <span style="color:#5478EE">查看</span>
            </a-button>
            <a-divider type="vertical" />
            <a-button type="text" size="small" @click="handleEditResume(record)" >
              <span style="color:#5478EE">编辑</span>
            </a-button>
            <a-divider type="vertical" />
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <a-popconfirm
                title="确定删除该候选人吗？"
                ok-text="确定"
                cancel-text="取消"
                placement="left"
                @confirm="() => { delResume(record.resumeId,record) }"
              >
                <a-button type="text" size="small">
                  <span style="color:#5478EE">删除</span>
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </template>
    </a-table>

    <!-- 编辑简历模态框 -->
    <ResumeEditModal
      v-model:visible="modalvisible"
      v-model:user-data="currentResume"
      @confirm="handleChangeResume"
      @cancel="handleCloseModal"
    />
  </div>
</template>
<script setup lang="ts">
// @ts-nocheck
import { onBeforeMount, onMounted, reactive, ref, watch } from 'vue'
import { modResume, deleteResume } from '@/api/admin/resumeManage'
import { teacherGroup } from '@/api/login'
import { message } from 'ant-design-vue'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import ResumeEditModal from './ResumeEditModal.vue'
import { deleteCandidate } from '@/pages/questionManage/hooks/api'
const router = useRouter()

const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  pagination: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits([
  'getPaperList',
  'editPaper',
  'previewPaper',
  'allocatePaper',
  'getResumes',
  'handleTableChange',
  'updateSelectedResume',
  'previewResume',
  'selectChange'
])

const statusColorMap = ref({
  '面试中':"#ff4d4f",
  '待面试':"#faad14",
  '未安排':"#52c41a",
  '已结束':"#c1c1c1",
})

const store = useStore()

// 已经选择的题目的ID列表
const selectedKeys = ref<(string | number)[]>([])
const selections = ref<any>([])
const createInterview = (record:any) => {

  if(record.interviewStatus === '未安排') {
    router.push({
      name: 'createInterview',
      query:{
        resumeId: record.resumeId,
        positionId: record.positionId || '1926875031301865473',
        position: record.position,
        candidateName: record.candidateName,
      }
    })
  }else {
    router.push({
      name: 'interviewDetails',
      query:{
        resumeId: record.resumeId,
        positionId: record.positionId || '1926875031301865473',
        position: record.position,
        candidateName: record.candidateName,
        interviewStatus: record.interviewStatus,
        age: record.age,
        gender:record.gender,
        email: record.email,
        phone: record.phone,
      }
    })
  }
}
const onSelect = (record: any, selected: any) => {
  const list = props.data.resumeList
  if (!selected) {
    // 取消勾选，删除对应的数组项
    selectedKeys.value.map((item, index) => {
      if (item === record.resumeId) {
        selectedKeys.value.splice(index, 1)
      }
    })
    selections.value = selections.value.filter((item) => item.resumeId !== record.resumeId)
    list.map((x: any, item: number) => {
      if (x === record.resumeId) {
        list.splice(item, 1)
      }
    })
  }
  if (selected) {
    // 点击勾选，添加id字段到selectedRowKeys数组里
    selectedKeys.value.push(record.resumeId)
    selections.value.push(record)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedKeys.value.push(item.resumeId)
      selections.value.push(item)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedKeys.value.map((x: any, indey: number) => {
        if (item.resumeId === x) {
          selectedKeys.value.splice(indey, 1)
          selections.value = selections.value.filter((item) => item.resumeId !== x)
        }
      })
    })
  }
}
const onSelectChange = (selectedRowKeys: (string | number)[], selectedRows: any) => {
  emits('selectChange', JSON.parse(JSON.stringify(selectedRowKeys)))
  emits('selectionsOfAll', JSON.parse(JSON.stringify(selections.value)))
}

const remove = (receiveSelections:any[]) => {
  selectedKeys.value = selectedKeys.value.filter((item) => !receiveSelections.includes(item))
  selections.value = selections.value.filter((item) => !receiveSelections.some((x) => x === item.resumeId))
}

defineExpose({
  remove
})
const handleGetCheckboxProps = (record: any) => {
  return {
    // defaultChecked: selectedKeys.value.includes(record.resumeId)
    selectedRowKeys: selectedKeys.value.includes(record.resumeId)
  }
}

const groups = <any>reactive([])
const modalvisible = ref(false)
const currentResume = ref<object>({})

// 查看简历

const getDetailResume = (record: any) => {
  const id = `${record.resumeId}`
  // let routeUrl = router.resolve({
  //   name: 'resumeDetail',
  //   query: { id }
  // })
  emits('previewResume', id)
  // window.open(routeUrl.href, '_blank')
  // window.open('/resume.html?' + record.resumeId)
}


/**
 * 编辑简历
 * @param record 
 */
function handleEditResume(record: any) {
  modalvisible.value = true
  Object.assign(currentResume.value, record)
}
/**
 * 编辑简历完成后，回调
 */
function handleChangeResume() {
  // 只刷新列表
  emits('getResumes')
}
/**
 * 取消编辑简历，回调
 */
function handleCloseModal() { }

const checkPhoneNo = async (rule: RuleObject, value: string) => {
  const reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
  if (value === '') {
    return Promise.reject('请输入手机号')
  } else if (!reg.test(value)) {
    return Promise.reject('请输入正确的手机号码')
  }
}

const checkEmail = async (rule: RuleObject, value: string) => {
  const email =
    /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9.]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
  if (value === '') {
    return Promise.reject('请输入邮箱')
  } else if (!email.test(value)) {
    return Promise.reject('邮箱格式不正确')
  }
}

const rules = {
  phone: [{ required: true, validator: checkPhoneNo, trigger: 'blur' }],
  email: [{ required: true, validator: checkEmail, trigger: 'blur' }]
}
// 删除简历
const delResume = (id: string, record: any) => {
  if(record.interviewStatus === '面试中') {
    message.error('该候选人正在考试中，无法删除')
    return
  }
  const params = {
    resumeIds: [id]
  }
  deleteCandidate(params).then(() => {
    message.success('删除成功！')
    emits('getResumes')
  })
}

// 按生成时间排序
const handleTableChange = (pagination: any, filters: any, sort: any, info:any) => {
  // const newSelectedKeys: any = []
  // selectedKeys.value.forEach((item) => {
  //   if (!prevSelectedRowKeys.includes(item)) {
  //     newSelectedKeys.push(item)
  //   }
  // })
  // prevSelectedRowKeys = [...newSelectedKeys, ...prevSelectedRowKeys]
  // console.log('--selectedKeys---', selectedKeys.value)

  emits('handleTableChange', { pagination, filters, sort, info })
}

// 批量操作标签
const delResumeList = <any>ref([])

watch(
  () => props.data.delResumeList,
  (val) => {
    delResumeList.value.push(...val)
  }
)

watch(
  selectedKeys,
  (val) => {
    emits('updateSelectedResume', JSON.parse(JSON.stringify(val)))
  },
  { deep: true }
)

const remark = (e: any) => {
  const elem: any = document.querySelector('.show-all-remark')
  if (elem) {
    if (e.target.className === 'remark') {
      if (elem!.style.display === 'none') {
        elem!.style.display = 'block'
      } else {
        elem!.style.display === 'none'
      }
    } else {
      elem.style.display = 'none'
    }
  }
}

const scrollY = ref(500)
function getScrollY() {
  let parentBox = document.querySelector('.resume-table') as HTMLElement
  if (!parentBox?.offsetHeight) return
  // 滚动高度 = 父盒子高度 - 去除表头和分页高度
  scrollY.value = parentBox.offsetHeight - 32 - 40
  scrollY.value = scrollY.value < 500 ? 500 : scrollY.value
}

onMounted(() => {
  getScrollY()
  // teacherGroup({ action: 'query' }).then((res: any) => {
  //   res.forEach((item: any) => {
  //     groups.push(item)
  //   })
  // })

  window.addEventListener('click', remark)
})

onBeforeMount(() => {
  window.removeEventListener('click', remark)
})
</script>

<style lang="less">
.eyeimg {
  margin-right: 8px;
  margin-bottom: 4px;
  cursor: pointer;
}
.resume-table {
  flex: 1;
  min-height: 0;
  .ant-table-thead > tr > th {
    font-weight: bold;
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
    .ant-table-header-column .ant-table-column-sorters:hover::before {
      background: none;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    color: #121633;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
  .ant-btn {
    font-size: 14px;
  }

  .ant-divider-vertical {
    margin: 0;
  }
  // .ant-table-header {
  //   overflow-y: scroll !important;
  // }
}
.tooltip-text {
  font-size: 12px;
  color: #666666;
}
.gray-style {
  color: #ccc;
}
.edit-resume-modal {
  width: 420px !important;
  .ant-modal-content {
    border-radius: 8px !important;
  }
  .ant-modal-header {
    border-radius: 8px !important;
  }
  .ant-modal-title {
    font-size: 15px;
    color: #121633;
    font-weight: bold;
  }
  .ant-form-item-label > label {
    font-size: 12px;
    color: #626262;
  }
  .ant-col {
    width: 80px;
  }
  .ant-input {
    border-radius: 8px;
    font-size: 12px;
    color: #181818;
  }
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
  }
  .ant-modal-footer {
    padding-bottom: 20px;
  }
  .ant-modal-body {
    padding-bottom: 0;
  }
}
.show-all-remark {
  position: absolute;
  z-index: 9999;
  word-break: break-all;
  background-color: #fff;
}
</style>
