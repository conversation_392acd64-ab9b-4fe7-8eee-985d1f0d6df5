<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="-43.4467278%" y1="-1.73472348e-13%" x2="58.6913615%" y2="55.8568962%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="80.2736353%" y1="-17.3580711%" x2="7.49705129%" y2="97.6111193%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.085,5 L18.8521232,11.5929276 C19.4679832,13.8913484 18.1039982,16.2538398 15.8055774,16.8696998 L8.40707241,18.8521232 C7.2068689,19.1737168 5.98919473,18.9554494 5.0118737,18.3461758 C5.00396867,18.231614 5,18.1162705 5,18 L5,10 C5,7.23857625 7.23857625,5 10,5 L17.085,5 Z" id="path-3"></path>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="11.2033731%" y1="23.58476%" x2="57.4734728%" y2="55.8568962%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.3419053%" y1="3.3834%" x2="26.299685%" y2="95.9599574%" id="linearGradient-6">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.085,5 L10,5 C7.23857625,5 5,7.23857625 5,10 L5,10 L5,18 C5,18.1162705 5.00396867,18.231614 5.0118737,18.3461758 C4.11720028,17.7881646 3.42448185,16.9034782 3.1303002,15.8055774 L1.14787677,8.40707241 C0.532016767,6.10865159 1.89600181,3.7461602 4.19442263,3.1303002 L11.5929276,1.14787677 C13.8913484,0.532016767 16.2538398,1.89600181 16.8696998,4.19442263 L18.8521232,11.5929276 L17.085,5 Z" id="path-7"></path>
        <linearGradient x1="8.81880296%" y1="9.30569435%" x2="93.0787331%" y2="93.1157159%" id="linearGradient-8">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-9" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-10"></use>
        </pattern>
        <image id="image-10" width="18" height="18" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEqADAAQAAAABAAAAEgAAAACaqbJVAAABq0lEQVQ4Ea2TYWrjMBCFx/UQ00AKPcD+38vsOXuEHqN/9wgLG+JQgxepEun3RlHp/iikTQfLksYzT+/NyMPfQ/mVq1nKjGKWc7GUzFYNfFnzWmMdPvaKzakaT8RWZh/dbGPYqBdW3cpYTP6RBBn4bZBQGBXHexAR8AkADmlgJFb2DojHKYJppuQY+BMU3tb4M3k+TQQq6JzAki2sChI7S3yiLxaSJLlRCuYoC34XrQkGAujylFShtToSAdM+JDGHJBioXlpX5nJKgy8LDlhNFGrL3EkQYxNBfU8PWtHXBqJGlJc06HyZLyunZg+gDUA7AHfb9rEkt3lBIqgrAAtjZkjaCRYtqr19htFIl1TgDccv6Nzdmt3ftYA93wWyP1bbz03Ge4C+DiDxBydMoGr9Fmb3d24/f7g9PGb7c/ifQQfosx+fVQ2Kdn6p6NEJ9MxLtqff0QYbhvGkkI/M53+A0JJo77kTqsVxLiZZl1owUmvjV1BbKX4v6KUgimvtByhxoXQ3dB2a2M/AAPQMgzc2AH3VuEewQZIu3DXmuqH6Aa+1m+8AEYmba5n0/FcYBxXi9kPYOQAAAABJRU5ErkJggg=="></image>
        <rect id="path-11" x="5" y="5" width="18" height="18" rx="5"></rect>
        <linearGradient x1="34.3857943%" y1="19.3414871%" x2="124.063702%" y2="107.658347%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M14.394,14.5620324 L14.78641,15.0378963 C15.3291695,15.6952921 16.1130903,16.0967222 16.9528826,16.1545908 L17.1638874,16.1618497 L17.2843461,16.1618497 L17.2843461,15.0168173 C17.2843461,14.9579399 17.3051263,14.9009517 17.3430253,14.8558939 C17.4319008,14.7502303 17.5896059,14.7366209 17.6952695,14.8254964 L17.6952695,14.8254964 L19.9136078,16.691381 C19.9245943,16.7006219 19.9347644,16.710792 19.9440053,16.7217784 C20.0328808,16.827442 20.0192714,16.9851471 19.9136078,17.0740227 L19.9136078,17.0740227 L17.6952695,18.9399072 C17.6502116,18.9778062 17.5932234,18.9985864 17.5343461,18.9985864 C17.3962749,18.9985864 17.2843461,18.8866576 17.2843461,18.7485864 L17.2843461,18.7485864 L17.2843461,17.603554 L17.1638874,17.603554 C15.9054084,17.603554 14.7078814,17.0746068 13.855008,16.1508324 L13.6891128,15.9608529 L13.464,15.6880324 L14.394,14.5620324 Z M17.5343461,9 C17.5932234,9 17.6502116,9.02078021 17.6952695,9.05867918 L19.9136078,10.9245638 C20.0192714,11.0134393 20.0328808,11.1711444 19.9440053,11.276808 C19.9347644,11.2877944 19.9245943,11.2979645 19.9136078,11.3072054 L17.6952695,13.17309 C17.5896059,13.2619655 17.4319008,13.2483561 17.3430253,13.1426926 C17.3051263,13.0976347 17.2843461,13.0406465 17.2843461,12.9817692 L17.2843461,11.8367367 L17.1638874,11.8367367 C16.2454185,11.8367367 15.3743995,12.2485113 14.78641,12.9606901 L12.3094138,15.9608529 C11.4500446,17.0017296 10.1770168,17.603554 8.83463911,17.603554 L8.71418047,17.603554 C8.31974949,17.603554 8,17.2808175 8,16.8827018 C8,16.4845862 8.31974949,16.1618497 8.71418047,16.1618497 L8.83463911,16.1618497 C9.75310804,16.1618497 10.624127,15.7500751 11.2121165,15.0378963 L13.6891128,12.0377335 C14.548482,10.9968568 15.8215098,10.3950324 17.1638874,10.3950324 L17.2843461,10.3950324 L17.2843461,9.25 C17.2843461,9.11192881 17.3962749,9 17.5343461,9 Z M8.83463911,10.3950324 C10.0931182,10.3950324 11.2906452,10.9239796 12.1435186,11.847754 L12.3094138,12.0377335 L12.534,12.3100324 L11.605,13.4360324 L11.2121165,12.9606901 C10.669357,12.3032943 9.88543623,11.9018642 9.04564395,11.8439957 L8.83463911,11.8367367 L8.71418047,11.8367367 C8.31974949,11.8367367 8,11.5140002 8,11.1158846 C8,10.7509452 8.26867839,10.4493453 8.61727041,10.401613 L8.71418047,10.3950324 L8.83463911,10.3950324 Z" id="path-13"></path>
        <filter x="-8.3%" y="-5.0%" width="116.7%" height="120.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-116.000000, -130.000000)">
            <g id="icon/06毛玻璃/24*24/02随机抽题" transform="translate(116.000000, 130.000000)">
                <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="url(#linearGradient-5)" xlink:href="#path-7"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                </g>
                <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                    <rect stroke="url(#linearGradient-8)" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                    <rect stroke="url(#pattern-9)" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    <use fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                </g>
            </g>
        </g>
    </g>
</svg>