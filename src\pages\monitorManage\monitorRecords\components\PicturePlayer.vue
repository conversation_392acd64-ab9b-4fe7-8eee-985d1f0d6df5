<template>
    <div class="picture-player" ref="focusRef" tabindex="0 ">
        <div class="banner">
            <img v-if="imgs[currentImgIndex]" :src="imgs[currentImgIndex]" alt="">
            <div v-else class="error-img"></div>
            <div class="controller" v-if="imgs?.length">
                <span class="play-btn">
                    <PauseOutlined v-if="isAutoPlay" @click="pause" />
                    <CaretRightOutlined v-else @click="resume" />
                </span>
                <a-popover trigger="click" overlayClassName="speed-popover" v-model:visible="speedPopoverVisible">
                    <template #content>
                        <div class="speed-item" :class="{ active: speed === item }" v-for="(item, index) in speedList"
                            @click="handleSpeedChange(item)" :key="item">
                            {{ item.toFixed(1) }}x
                        </div>
                    </template>
                    <div class="speed-btn">
                        {{ speed.toFixed(1) }}x
                    </div>
                </a-popover>
            </div>
        </div>
        <div class="progress-container">
            <div ref="progressBar" class="progress-bar">
                <div v-for="item in progressBarImgs" class="progress-bar-img-box"
                    :style="{ background: `url(${item}) no-repeat center` }" style="background-size: 100%;"></div>
            </div>
            <canvas ref="canvas" class="progress-canvas" @mousedown="handleMouseDown" @mousemove="handleDragging"></canvas>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useIntervalFn, useFocus, onKeyStroke } from '@vueuse/core'
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { CaretRightOutlined, PauseOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
    imgs: string[]
}>()

// ————————————————————————————————————————————————————————以下进度条部分————————————————————————————————————————————————————————————————
// 获取进度条图片数组
const progressBarImgs = ref<string[]>([])
const NUMBER = 10
function getProgressBarImgs(imgs: string[], number: number) {
    // 过滤掉空字符串
    const validImages = imgs.filter(img => img.trim() !== '');

    // 如果有效图片数量小于待取数量，返回所有有效图片
    if (validImages.length <= number) {
        return validImages;
    }

    // 计算均匀取图片的步长
    const step = Math.floor(validImages.length / number);

    // 初始化结果数组
    const result = [];

    // 循环取图片，保证尽可能均匀
    for (let i = 0; i < number; i++) {
        const index = i * step;
        result.push(validImages[index]);
    }

    return result;
}
watch(() => props.imgs, (imgs) => {
    progressBarImgs.value = getProgressBarImgs(imgs, NUMBER)
}, { immediate: true })

const canvas = ref<HTMLCanvasElement | null>(null);
let isDragging = ref(false);

/** 当前位置 (0 ~ 100) */
let progressPercentage = ref(0);

/** 
 * @description 设置canvas宽高
 * canvas.style.width 不是 canvas.width， canvas的宽高默认是300、150
 */
// canvas缩放比例
const scale = 2;
function resizeCanvas() {
    if (canvas.value) {
        const parentWidth = canvas.value.parentElement?.offsetWidth || 1400
        const parentHeight = (canvas.value.parentElement?.offsetHeight || 80) + 20
        canvas.value.width = parentWidth * scale;
        canvas.value.height = parentHeight* scale;

        canvas.value.style.width = parentWidth + 'px';
        canvas.value.style.height = parentHeight + 'px';
        drawProgressBar();
    }
}

onMounted(() => {
    resizeCanvas();
    document.addEventListener('mouseup', stopDragging);
});

onUnmounted(() => {
    document.removeEventListener('mouseup', stopDragging);
});

const handleMouseDown = (event: MouseEvent) => {
    isDragging.value = true;
    drawCanvas(event);
};

const stopDragging = () => {
    isDragging.value = false;
};

const handleDragging = (event: MouseEvent) => {
    if (isDragging.value) {
        drawCanvas(event);
    }
};

// 绘图唯一入口
const drawCanvas = (event?: MouseEvent) => {
    if (canvas.value) {
        if (event) {
            const rect = canvas.value.getBoundingClientRect();
            const newPosition = event.clientX - rect.left;
            progressPercentage.value = Math.min(100, Math.max(0, (newPosition / rect.width) * 100));
        } else {
            progressPercentage.value = 0
        }
        drawProgressBar();
    }
};

const drawProgressBar = () => {
    if (canvas.value) {
        const ctx = canvas.value.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
            const handlerX = (progressPercentage.value / 100) * canvas.value.width;

            // Draw Time
            ctx.fillStyle = '#000';
            ctx.font = 14 * scale + 'px Arial';
            ctx.textAlign = 'center';
            let currentTime = basicTimeStemp.value + currentImgIndex.value * 3
            if (Number.isNaN(currentTime)) return; // 如果没有监控则不继续绘制
            // 限制 x 在 [20, canvas.value.width - 20] 的范围内
            let x = Math.max(20, Math.min(handlerX, canvas.value.width - 20));
            ctx.fillText(formatTime(currentTime), x + 15, canvas.value.height - 5);

            // Draw handler
            ctx.fillStyle = '#fff';
            ctx.fillRect(Math.floor(handlerX - 1), 0, 2 * scale, canvas.value.height - 20 * scale);
            ctx.strokeStyle = 'rgba(22,22,22,0.65)';
            ctx.strokeRect(handlerX - 1, 0, 2 * scale, canvas.value.height - 20 * scale);
        }
    }
};

// ————————————————————————————————————————————————————————以上是进度条部分————————————————————————————————————————————————————————————————


// —————————————————————————————————————————————————————————以下是banner图部分————————————————————————————————————————————————————————————————

// 当前图片的下标
const currentImgIndex = computed({
    get() {
        let totalLength = props.imgs.length
        return Math.ceil(progressPercentage.value / 100 * totalLength)
    },
    set(val) {
        progressPercentage.value = val / props.imgs.length * 100
    }
})
const basicTimeStemp = ref(0) // 时间基数秒
function getBasicTimeStemp() {
    try {
        let firstImgSrc = props.imgs.find(i => i)
        let firstFrame = parseInt(firstImgSrc?.split('/').at(-1)?.split('.')[0]!, 10) // 第一个有效帧
        let firstImgIndex = props.imgs.findIndex(i => i) // 第一个有效帧下标
        basicTimeStemp.value = (firstFrame - firstImgIndex) * 3
    } catch (error) {
        console.log(error)
    }
}


// —————————————————————————————————————————————————————————以上是banner图部分————————————————————————————————————————————————————————————————

function formatTime(currentTime: number) {
    // 计算分钟和秒数
    const minutes = Math.floor(currentTime / 60);
    const seconds = currentTime % 60;
    console.log(currentTime, minutes)

    // 格式化为两位数
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(seconds).padStart(2, '0');

    // 返回格式化后的时间字符串
    return `${formattedMinutes}:${formattedSeconds}`;
}


// ————————————————————————————————————————————————自动播放
// 倍速
const speed = ref(3)
const speedList = [3, 6, 12, 24]
const speedPopoverVisible = ref(false)
function handleSpeedChange(item: number) {
    speed.value = item
    speedPopoverVisible.value = !speedPopoverVisible.value
}

/** 
 * @description 自动播放(修改currentImgIndex,调用drawProgressBar)
 */
function autoPlay() {
// 原定3s一帧  
// 按默认3倍速计算，1000ms走1帧，那么每10ms走0.01帧，进度条走 0.01/props.imgs.length * 100, 即props.imgs.length
    progressPercentage.value += speed.value / 3 / props.imgs.length
    progressPercentage.value %= 100
    drawProgressBar()
}
const { resume, pause, isActive: isAutoPlay } = useIntervalFn(autoPlay, 10, { immediate: false })

// 键盘控制
const focusRef = ref()
const { focused } = useFocus(focusRef, { initialValue: true })

onKeyStroke((e) => {
    if (!focused.value || !props.imgs?.length) return
    if (e.code === 'Space') {
        isAutoPlay.value ? pause() : resume()
        return
    }
    pause()
    if (e.code === 'ArrowLeft') {
        currentImgIndex.value = Math.max(0, currentImgIndex.value - 1)
    } else if (e.code === 'ArrowRight') {
        currentImgIndex.value = Math.min(props.imgs.length - 1, currentImgIndex.value + 1)
    }
    drawProgressBar()
})

// 组件入参发生变更的处理 
watch(() => props.imgs, () => {
    pause?.()
    getBasicTimeStemp()
    drawCanvas()
}, { immediate: true })

</script>

<style lang="less" scoped>
.picture-player {
    height: 100%;
    display: flex;
    flex-direction: column;
    user-select: none;

    &:focus {
        outline: none;
    }

    .banner {
        flex: 1;
        min-height: 0;
        position: relative;

        img {
            height: 100%;
            width: 100%;
            background-color: #D9D9D9;
            object-fit: contain;
        }

        .error-img {
            height: 100%;
            background: #000;
        }

        .controller {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            height: 48px;
            display: flex;
            align-items: center;
            padding-left: 24px;

            .play-btn {
                font-size: 30px;
                color: #fff;
                cursor: pointer;
            }

            .speed-btn {
                margin-left: 24px;
                font-size: 14px;
                color: #fff;
                cursor: pointer;
                position: relative;
            }
        }
    }

    .progress-container {
        height: 80px;
        position: relative;
        margin-top: 8px;

        .progress-bar {
            display: flex;
            height: 100%;

            .progress-bar-img-box {
                flex: 1;
                min-width: 0;
            }
        }

        .progress-canvas {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: calc(100% + 20px);
        }
    }
}
</style>
<style lang="less">
.speed-popover {
    border-radius: 4px;
    overflow: hidden;

    .ant-popover-inner-content {
        padding: 0;
    }

    .speed-item {
        width: 80px;
        height: 36px;
        background-color: #343434;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        cursor: pointer;

        &.active {
            color: #5478EE;
        }

        &:hover {
            background-color: #5A5A5A;
        }
    }
}
</style>