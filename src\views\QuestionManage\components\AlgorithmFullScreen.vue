<template>
  <div class="algorithm-full-screen" ref="algorithmRef">
    <a-modal
      v-if="question"
      v-model:visible="visible"
      width="100%"
      :footer="null"
      :closable="false"
      :getContainer="() => $refs.algorithmRef"
      wrapClassName="full-screen-modal"
      @cancel="closeModal"
    >
      <code-modal :code-modal-visible="codeModalVisible">
        <template #code-q>
          <div class="lf-main">
            <div class="question-stem">
              <span class="label">题干: </span>
              <span>{{ question.body }}</span>
            </div>
            <div class="question-btestcase">
              <template v-for="(item, index) in questionData.btestcase" :key="index">
                <h4 class="title">题干测试用例{{ index + 1 }}:</h4>
                <div class="content">
                  <div class="item">
                    <span class="item-text">输入: </span>{{ formatTestcase(item) }}
                  </div>
                  <div class="item">
                    <span class="item-text">输出: </span>{{ item.output[0].value }}
                  </div>
                </div>
              </template>
            </div>
            <div class="question-ptestcase">
              <template v-for="(item, index) in questionData.ptestcase" :key="index">
                <h4 class="title">批卷测试用例{{ index + 1 }}:</h4>
                <div class="content">
                  <div class="item">
                    <span class="item-text">输入: </span>{{ formatTestcase(item) }}
                  </div>
                  <div class="item">
                    <span class="item-text">输出: </span>{{ item.output[0].value }}
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
        <template #code-an>
          <div class="rt-main">
            <div class="tool-bar">
              <span class="lang">编程语言: {{ lang }}</span>
              <div class="validate-result">
                <a-button
                  class="validate-btn"
                  type="primary"
                  @click="runCode"
                  :loading="questionConfig[lang!].loading"
                  :disabled="!questionConfig[lang!].src"
                >
                  验证
                </a-button>
              </div>
              <div class="operate-btns">
                <a-tooltip placement="left">
                  <template #title>
                    <span style="font-size: 12px">刷新</span>
                  </template>
                  <a-popconfirm
                    class="pop-confirm"
                    placement="bottomRight"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="handleIconClick('reload')"
                  >
                    <template #title>
                      <p>是否确定重置编译器?</p>
                    </template>
                    <svg-icon class="item-icon" name="reload" />
                  </a-popconfirm>
                </a-tooltip>
                <a-tooltip placement="left">
                  <template #title>
                    <span style="font-size: 12px">退出全屏</span>
                  </template>
                  <svg-icon
                    class="item-icon"
                    name="comscreen"
                    @click="handleIconClick('comscreen')"
                  />
                </a-tooltip>
              </div>
            </div>
            <div class="editor-area">
              <code-editor
                v-model="questionConfig[lang!].src"
                :lang="questionConfig[lang!].lang"
                :template="funcTemplate"
                :counter="questionConfig[lang!].counter"
              />
            </div>

            <div class="validate-tips-head" v-if="currentValidateResult && !isPassValidate()">
              <span class="err-info">
                <svg-icon name="errortip" class="icon-err"></svg-icon>
                <span v-if="currentValidateResult.is_error">代码报错</span>
                <span v-else>未通过全部测试用例</span>
              </span>
            </div>

            <div class="validate-tips-content">
              <div v-if="currentValidateResult" style="height: 100%">
                <div v-if="currentValidateResult.is_error">
                  <div class="err-result">
                    {{ currentValidateResult.results[0] }}
                  </div>
                </div>
                <div
                  class="content"
                  v-else-if="currentValidateResult.results.filter((item: any) => !item.is_pass).length"
                >
                  <div class="item">
                    <span class="item-text">输入: </span
                    >{{ formatTestcase(getFailValidateResult()) }}
                  </div>
                  <div class="item">
                    <span class="item-text">期望输出: </span
                    >{{ getFailValidateResult().output[0].value }}
                  </div>
                  <div class="item">
                    <span class="item-text">实际输出: </span>{{ getFailValidateResult().result }}
                  </div>
                </div>
                <div v-else class="validate-pass">
                  <span class="tip"><svg-icon name="done" class="icon-result" /> 验证通过</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </code-modal>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import CodeEditor from '@/components/CodeEditor.vue'
import CodeModal from './CodeModal.vue'
import { ref, watch } from 'vue'
import { ojquesjudge } from '@/api/admin/questionManage'
import { useRoute } from 'vue-router'

const props = defineProps({
  isFullscreen: {
    type: Boolean,
    default: false
  },
  questionData: {
    type: Object,
    default: () => {}
  },
  modelValue: Object as any,
  lang: String,
  funcMain: Object as any,
  funcMainParams: Object as any,
  funcTemplate: Object as any
})

const emits = defineEmits(['update:modelValue', 'close', 'updateFullScreen'])

const visible = ref(false)
const closeModal = () => {
  questionConfig.value[props.lang!]['latestAnswer'] = questionConfig.value[props.lang!]['src']
  questionConfig.value[props.lang!].showTip = true
  emits('close')
}

const codeModalVisible = ref(true)

const formatTestcase = (item: any) => {
  let str = ''
  item.input.forEach((param: any) => {
    str += Object.keys(param)[0] + ' = ' + Object.values(param)[0] + ', '
  })

  return str.slice(0, -2)
}
const getFailValidateResult = () => {
  const result = currentValidateResult.value.results.filter((item: any) => !item.is_pass)
  return result[0]
}

// 处理点击icon-tool事件
const handleIconClick = (iconName: string) => {
  if (iconName === 'reload') {
    // if (route.query.id) {
    //   emits('updateTemplate', props.lang!, questionConfig.value[props.lang!]['lang'])
    // } else {
    //   questionConfig.value[props.lang!].counter++
    // }
    emits('updateFullScreen', props.lang!)
    // questionConfig.value[props.lang!].counter++
  }
  if (iconName === 'comscreen') {
    closeModal()
  }
}

const currentValidateResult = ref<any>()
// 判断是否通过校验
const isPassValidate = () => {
  if (
    currentValidateResult.value.is_error ||
    currentValidateResult.value.results.some((result: any) => !result.is_pass)
  ) {
    questionConfig.value[props.lang!].pass = -1
    return false
  }
  questionConfig.value[props.lang!].pass = 1
  return true
}

const runCode = () => {
  questionConfig.value[props.lang!].showTip = false
  questionConfig.value[props.lang!].loading = true
  questionConfig.value[props.lang!].status = 'process'
  return ojquesjudge({
    src: questionConfig.value[props.lang!].src,
    // type: questionConfig.value[props.lang!].lang,
    codeType: questionConfig.value[props.lang!].lang,
    ...props.funcMainParams,
    // btestcase: props.questionData.btestcase,
    // ptestcase: props.questionData.ptestcase
    btestcase: JSON.stringify(props.questionData.btestcase),
    ptestcase: JSON.stringify(props.questionData.ptestcase)
  })
    .then((res) => {
      currentValidateResult.value = res
      questionConfig.value[props.lang!].loading = false
      // 没有通过验证给提示框
      if (!isPassValidate()) {
        questionConfig.value[props.lang!].status = 'error'
      } else {
        questionConfig.value[props.lang!].status = 'finish'
      }
    })
    .catch(() => {
      questionConfig.value[props.lang!].loading = false
    })
    .finally(() => {
      questionConfig.value[props.lang!].loading = false
    })
}

watch(
  () => props.isFullscreen,
  (val) => {
    visible.value = val
  },
  {
    immediate: true
  }
)

const question = ref<any>({})
watch(
  () => props.questionData,
  (val) => {
    question.value = val
  },
  {
    immediate: true
  }
)

const questionConfig = ref()
watch(
  () => props.modelValue,
  (val) => {
    questionConfig.value = val
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.algorithm-full-screen {
  :deep(.ant-modal-mask) {
    background: none !important;
    z-index: 1;
  }
  :deep(.ant-modal-wrap) {
    z-index: 2 !important;
  }

  .lf-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    padding-right: 10px;
    .question-stem {
      color: rgba(0, 0, 0, 0.85);
      .label {
        font-weight: bold;
      }
    }
    .question-btestcase,
    .question-ptestcase {
      .title {
        padding: 24px 0 8px;
        font-size: 14px;
        font-weight: 600;
      }
      .content {
        padding: 16px;
        background: #f5f5f5;
        border-radius: 8px;
        margin-bottom: 8px;

        .item {
          font-size: 14px;
          &:first-child {
            margin-bottom: 8px;
          }
        }
        .item-text {
          font-weight: 600;
          padding-right: 6px;
        }
      }
    }
  }
  .rt-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    .tool-bar {
      height: 26px;
      display: flex;
      align-items: center;
      font-size: 12px;
      margin-bottom: 5px;
      .lang {
        width: 127px;
      }
      .validate-result {
        display: flex;
        align-items: center;
        position: relative;
        margin-right: auto;
        .validate-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px 8px;
          height: 26px;
          border-radius: 4px;
          border-color: transparent;
          scale: 0.83;
          font-size: 12px;
          cursor: pointer;
          margin-right: 4px;
        }
        .loading {
          position: absolute;
          transform: translate(-42%, -50%);
          // left: 0;
          // top: 0;
        }
        .result {
          font-size: 12px;
          display: flex;
          align-items: center;
          .icon-result {
            font-size: 16px;
            margin-right: 4px;
          }
        }
      }
      .item-icon {
        margin-left: 16px;
        cursor: pointer;
      }
    }
    .editor-area {
      flex: 3;
      margin-bottom: 16px;
    }

    .validate-tips-head {
      margin-bottom: 8px;
      .err-info {
        display: flex;
        align-items: center;
        .icon-err {
          margin-right: 4px;
        }
      }
    }

    .validate-tips-content {
      background: #f5f5f5;
      min-height: 160px;
      border-radius: 8px;
      overflow: hidden;
      .content {
        padding: 16px;
        margin-bottom: 8px;
        .item {
          font-size: 14px;
          &:first-child {
            margin-bottom: 8px;
          }
        }
        .item-text {
          font-weight: 600;
          padding-right: 6px;
        }
      }
      .err-result {
        padding: 16px;
        color: #ff4d4f;
      }
      .validate-pass {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .tip {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
