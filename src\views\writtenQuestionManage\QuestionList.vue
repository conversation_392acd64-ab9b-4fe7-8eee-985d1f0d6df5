<script lang="ts" setup>
import type { QuestionModel } from '@/models/questionModel'
import { addQuestion, addCategory, editCategory,alltags, beforedelques, delQuestion } from '@/api/admin/questionManage'
import { queryQuestion } from './api/index'
import { aiproofreadingbycatg, getaiproofreadingtaskids } from '@/api/exam/index'
import { QuestionEnum } from '@/models/questionModel'

import ProofreadingTask from '@/pages/questionManage/components/ProofreadingTask.vue'
import QuestionItemDisplay from '@/pages/questionManage/QuestionItemDisplay/index.vue'
import QuestionProofreadForm from '@/pages/questionManage/QuestionProofread.vue'
import { getQuestionContentByRecord } from '@/utils'
import emitter from '@/utils/bus'
import QuestionAddForm from '@/views/QuestionManage/createQuestion.vue'
import QuestionEditForm from '@/views/QuestionManage/editQuestion.vue'
import { CheckCircleFilled, ExclamationCircleFilled, InfoCircleFilled, InfoCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { computed, createVNode, h, nextTick, onActivated, onDeactivated, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import ProofreadingTaskFinished from './components/ProofreadingTaskFinished.vue'
import QuestioEditor from './QuestionEditor/QuestionEditor.vue'


const props = withDefaults(
  defineProps<{
    subjectId?: string
    /** 是否是官方题库 */
    isOfficialSubject?: boolean
    defaultType?: number | null
    defaultSelectedRows?: any[]
    defaultExcludedRowKeys?: string[]
    disabledRowKeys?: string[]
    writable?: boolean
    current?: number,
    subjectUUID:any,
    operationFlag?:any
  }>(),
  {
    subjectId: '',
    isOfficialSubject: false,
    defaultType: null,
    defaultSelectedRows: () => [],
    defaultExcludedRowKeys: () => [],
    disabledRowKeys: () => [],
    writable: false,
    current: 1,
    subjectUUID:"",
    operationFlag:false
  },
)

const emits = defineEmits<{
  (e: 'select', record: any, selected: boolean): void
}>()

const store = useStore()

// 可选/已选/已排除
const listTypeEnum = ['可选', '已选', '已排除']
const listType = ref('可选')

// 是否包含子题库
const includeSubCatg = ref(true)

// 快速搜索
const searchContent = ref('')
function getFirstPageList() {
  otherParams.value.includeChildrenUUIDs = includeSubCatg.value
  paginationConfig.current = 1
  getList()
}

// 标签
const activeTagIds = reactive<string[]>([])
const selectedTags = computed(() =>
  tagsList.value.filter((item: any) => activeTagIds.includes(item.id)).map((i: any) => i.name),
)
function handleClickTag(id: string) {
  const index = activeTagIds.findIndex(i => i === id)
  if (index === -1) {
    activeTagIds.push(id)
  }
  else {
    activeTagIds.splice(index, 1)
  }
  getList()
}

// 其他参数
const otherParams = ref<{
  types?: number[] | null
  proofreading?: (0 | 1 | 2)[]
  order_field?: string
  order_type?: 'ascend' | 'descend'
  /** 是否仅显示当前题库的试题（不包含子题库） */
  includeChildrenUUIDs: boolean
  create_by?: number[] | null
}>({
      types: typeof props.defaultType === 'number' ? [props.defaultType] : null,
      includeChildrenUUIDs: true,
    })

function handleTableChange(pagination: any, filters: any = {}, sort: any = {}) {


  // 处理排序
  otherParams.value.order_type = sort.order
  otherParams.value.order_field = sort.order ? sort.field : undefined
  // 处理分页
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  // 处理筛选
  Object.assign(otherParams.value, filters)
  getList()
}

// 获取所有标签
const tagBtnState = ref<'隐藏' | '更多' | '收起'>('隐藏')
const tagsHeight = computed(() => (tagBtnState.value === '更多' ? 'auto' : '30px'))
const tagsRotate = computed(() =>
  tagBtnState.value === '更多' ? 'rotate(0deg)' : 'rotate(180deg)',
)

const allTagsContainerRef = ref<HTMLDivElement>()
const tagsList = ref<any>([])
async function getAllTags() {
  layoutTags()
  const res = await alltags({ tag: 'question', categoryUUID: props.subjectId })
  tagsList.value = res
  // dom更新后查看高度，如果超过一行则展示“更多”，并设置为单行高度
  nextTick().then(layoutTags)
}
function handleTagBtnStateChange() {
  tagBtnState.value = tagBtnState.value === '更多' ? '收起' : '更多'
}
async function layoutTags() {
  tagBtnState.value = '更多' // 先自适应高度
  await nextTick()
  const initialHeight = allTagsContainerRef.value!.offsetHeight // 获取实际高度
  if (initialHeight > 35) {
    // 当一行宽度不够展示所有标签时，才展示展开和收起的按钮
    // 当有选择标签时,默认展开
    if (selectedTags.value.length) {
      tagBtnState.value = '更多'
    }
    else {
      tagBtnState.value = '收起'
    }
  }
  else {
    tagBtnState.value = '隐藏'
  }
}
onActivated(() => {
    setTimeout(() => {
    if(!props.subjectId) return
    if(watchTrigger.value) return
    getList()
  }, 100);
})
onMounted(() => {
  getAllTags()
})

// 表格配置
const proofreadingColumn = {
  title: '校对状态',
  dataIndex: 'proofreading',
  key: 'proofreading',
  width: 120,
  ellipsis: true,
  resizable: true,
  filters: [
    { text: '校对正确', value: 1 },
    { text: '校对错误', value: 2 },
    { text: '未校对', value: 0 },
  ],
}
const actionColumn = {
  title: '操作',
  key: 'action',
  width: 170,
  align: 'center',
  fixed: 'right',
}
const tableColumns = ref([
  {
    title: '题型',
    dataIndex: 'type',
    key: 'types',
    width: 100,
    ellipsis: false,
    filters: [
      { text: '单选题', value: 0 },
      { text: '多选题', value: 1 },
      { text: '判断题', value: 2 },
      { text: '填空题', value: 5 },
      { text: '问答题', value: 3 },
      { text: '算法题', value: 4 },
      { text: '排序题', value: 6 },
    ],
    resizable: true,
  },
  {
    title: '题干',
    dataIndex: 'body',
    key: 'body',
    ellipsis: true,
    resizable: true,
    width: 500,
  },
  proofreadingColumn,
  {
    title: '题库',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 160,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '分值',
    dataIndex: 'score',
    key: 'score',
    width: 120,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '正确率',
    dataIndex: 'accuracy',
    key: 'accuracy',
    width: 120,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '引用次数',
    dataIndex: 'referenceCount',
    key: 'referenceCount',
    width: 120,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    ellipsis: true,
    resizable: true,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'teacherName',
    key: 'teacherName',
    width: 100,
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '本人', value: 1 },
      { text: '其他', value: 0 },
    ],
  },
  actionColumn,
])

// 如果传入默认题型，则不展示题型过滤下拉框
if (props.defaultType !== null) 
  Reflect.deleteProperty(tableColumns.value[0], 'filters')
// 根据是否官方题库和是否可写控制操作栏显隐
watch([() => props.isOfficialSubject, () => props.writable], ([isOfficialSubject, writable]) => {
  const officialCert = store.getters.userInfo.officialCert
  const hideAction = !officialCert && isOfficialSubject
  const index = tableColumns.value.findIndex(item => item.key === 'action')
  if (writable && !hideAction) {
    if (index === -1) 
      tableColumns.value.push(actionColumn)
  }
  else {
    if (index !== -1) 
      tableColumns.value.splice(index, 1)
  }
  // 官方题库题目不展示校对结果列
  const proofreadIndex = tableColumns.value.findIndex(item => item.key === 'proofreading')
  if (!isOfficialSubject && !officialCert || isOfficialSubject && officialCert) { // 同或运算
    if (proofreadIndex === -1) {
      const authorIndex = tableColumns.value.findIndex(item => item.title === '题干')
      tableColumns.value.splice(authorIndex + 1, 0, proofreadingColumn) // 添加到在题干后面
    }
  }
  else {
    if (proofreadIndex !== -1) 
      tableColumns.value.splice(proofreadIndex, 1)
  }
}, { immediate: true })

const paginationConfig = reactive({
  current: props.current,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total: number) => `总条数：${total}`,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small',
})

// 获取已排除列表长度
const excludedListLength = ref(0)
async function getExcludedListLength() {
  excludedListLength.value = props.defaultExcludedRowKeys.length
  if (props.defaultExcludedRowKeys.length) {
    const params: any = {
      // action: 'query',
      tagList: selectedTags.value,
      basePager:{
        current: paginationConfig.current,
        size: paginationConfig.pageSize,
      },
      body: searchContent.value,
      categoryId: props.subjectId || null,
      ids: props.defaultExcludedRowKeys,
      ...otherParams.value,
    }
    params.typeList = params.types
    params.proofreadingList = params.proofreading
    // params.notQuestionBankUUIDList = params.notids
    // params.questionBankUUIDList = params.ids
    // delete params.ids
    // delete params.notids
    delete params.proofreading
    const res = (await queryQuestion(params)) as any
    excludedListLength.value = res.total
  }
}
getExcludedListLength()

// 表格数据
const list = ref<any[]>([])
const loading = ref(false)
const watchTrigger = ref(false)
async function getList() {
  if (!props.subjectId) 
    return
  loading.value = true
  const params: any = {
    action: 'query',
    basePager:{
      current: paginationConfig.current,
      size: paginationConfig.pageSize,
    },
    body: searchContent.value,
    categoryUUID: props.subjectId || null,
    tagList: selectedTags.value,
    notids: props.defaultExcludedRowKeys,
    ...otherParams.value,
    officialCert: props.isOfficialSubject ? 1 : 0
  }
  if (['可选', '已选'].includes(listType.value)) {
    // 如果是可选或者已选，需要将虽已排除但是已选的也展示出来
    params.notids = params.notids.filter((id: string) => !selectedRowKeys.value.includes(id))
  }
  if (listType.value === '已选') {
    params.ids = selectedRowKeys.value
    if (params.ids && !params.ids.length) {
      params.ids = [0]
    }
    // params.questionBankUUIDList = params.ids
  }
  else if (listType.value === '已排除') {
    params.ids = props.defaultExcludedRowKeys
    if(!params.ids.length){
      params.ids = [0]
    }
    params.notids = []
    params.excludeDept = true
    // params.questionBankUUIDList = params.ids
    // params.notQuestionBankUUIDList = params.notids
  }
  params.createBy = params.teacherName
  delete params.teacherName
  params.typeList = params.types
  params.proofreadingList = params.proofreading
  delete params.proofreading
  // switch(otherParams.value.order_field){
  //   case "score":
  //     params.scoreOrder = otherParams.value.order_type == 'ascend' ? "asc" : "desc"
  //     break;
  //   case "accuracy":
  //     params.accuracyOrder = otherParams.value.order_type == 'ascend' ? "asc" : "desc"
  //     break;
  //   case "referenceCount":
  //     params.referenceCountOrder = otherParams.value.order_type == 'ascend' ? "asc" : "desc"
  //     break;
  //   case "updateTime":
  //     params.updateTimeOrder = otherParams.value.order_type == 'ascend' ? "asc" : "desc"
  //     break;
  // }

  // delete params.ids
  // delete params.notids
  try {
    const res = (await queryQuestion(params)) as any
    list.value = res.records
    paginationConfig.total = res.total
    if((res.current - 1) * res.size + 1 > res.total && paginationConfig.current > 1) {

      paginationConfig.current--
      getList()
    }
  }
  finally {
    loading.value = false
    watchTrigger.value = false
  }
}

// 已选列表
const selectedRows = ref<any[]>([...props.defaultSelectedRows])
const selectedRowKeys = computed(() => selectedRows.value.map(i => i.id))

function updateSelectedRowKeys(record: any) {
  const key = record.id
  const index = selectedRowKeys.value.findIndex(i => i === key)
  if (index === -1) {
    selectedRows.value.push(record)
    emits('select', record, true)
  }
  else {
    selectedRows.value.splice(index, 1)
    emits('select', record, false)
  }
}
function onSelectAll(selected: boolean, selectedRows: any, changeRows: any[]) {
  return changeRows.forEach(updateSelectedRowKeys)
}

const getType = (type: number) => QuestionEnum[type]

function formatAccuracy(record: any) {
  const {accuracy,referenceCount} = record
  if (accuracy === null || accuracy < 0 || referenceCount < 10) {
    return '-'
  }
  else {
    return `${Math.floor(accuracy * 100)}%`
  }
}

// 新建题目
const questionAddFormVisible = ref(false)
function createQuestion() {
  questionAddFormVisible.value = true
}
function onAddFormClose(needRefresh?: boolean) {
  if (needRefresh) {
    getAllTags()
    getList()
  }
  questionAddFormVisible.value = false
}

// 查询题目ids关联的（未开始考试）试卷
function getPapersWithQids(ids: string[]) {
  const h = createVNode
  return new Promise((resolve, reject) => {
    beforedelques({ ids }).then((res: any) => {
      if (res.length) {
        Modal.confirm({
          title: '删除题目风险提醒',
          content: h('div', {}, [
            h('p', {}, '删除当前题目后，会影响以下考试：'),
            ...res.map((item: any) => h('p', { style: 'margin-top: 6px' }, `《${item.name}》——${item.teacherName}`)),
            h('p', { style: 'margin-top: 6px' }, '确认删除吗？'),
          ]),
          icon: () => h(InfoCircleFilled),
          onOk() {
            resolve(res)
          },
          onCancel() {
            reject()
          },
        })
      }
      else {
        resolve(res)
      }
    })
  })
}

// 删除题目
async function deleteQuestion(record: QuestionModel) {
  try {
    await getPapersWithQids([record.id!])
    await delQuestion({ ids: [record.id] })
    message.success('删除成功!')
    await getAllTags()
    getList()
  }
  catch (error) {
    
  }
}

// 批量删除题目
function deleteMultiQuestion() {
  if (!selectedRows.value.length) 
    return message.error('请勾选要删除的题目')
  Modal.confirm({
    title: () => `确定删除勾选的${selectedRows.value.length}个题目?`,
    icon: () => createVNode(ExclamationCircleFilled),
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await getPapersWithQids(selectedRowKeys.value)
        await delQuestion({
          ids: selectedRowKeys.value,
        })
        message.success('批量删除成功!')
        selectedRows.value = []
        await getAllTags()
        getList()
      }
      catch (error) {
        
      }
    },
    onCancel() {},
  })
}

// 编辑题目
const qid = ref('')
const questionEditFormVisible = ref(false)
function editQuestion(record) {
  qid.value = record.questionBankUUID
  questionEditFormVisible.value = true
}
function onEditFormClose(needRefresh?: boolean) {
  if (needRefresh) {
    getAllTags()
    getList()
  }
  questionEditFormVisible.value = false
}

// 题目校对
const questionProofreadFormVisible = ref(false)
function proofreadQuestion(record: any) {
  qid.value = record.questionBankUUID
  questionProofreadFormVisible.value = true
}

// 监听切换题库
const debounceTimer = ref<any>(null)


watch(
  () => props.subjectId,
  () => {
    watchTrigger.value = true
    paginationConfig.current = 1
    if (debounceTimer.value) 
      clearTimeout(debounceTimer.value)
      debounceTimer.value = setTimeout(async () => {
        await getAllTags()
        getList()
      }, 200)
  },
)

// 获取上一题/下一题并高亮展示
const currentQIndex = computed(() => list.value.findIndex(item => item.questionBankUUID === qid.value))
const lastBtnDisabled = computed(() => (paginationConfig.current === 1) && (currentQIndex.value === 0))
const nextBtnDisabled = computed(() => (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) && (currentQIndex.value === list.value.length - 1))
async function getSiblingQuestion(direction: 'last' | 'next') {
  if (!qid.value) 
    return
  const len = list.value.length
  const index = currentQIndex.value
  let newQid = qid.value
  let newIndex = index

  if (direction === 'last') {
    if (index !== 0) {
      newIndex = index - 1
    }
    else {
      // 获取上一页最后一条
      if (paginationConfig.current === 1) {
        throw new Error('没有上一题了')
      }
      else {
        paginationConfig.current -= 1
        await getList()
        newIndex = list.value.length - 1
      }
    }
  }
  else {
    if (index !== len - 1) {
      newIndex = index + 1
    }
    else {
      // 获取下一页第一条
      if (paginationConfig.current === Math.ceil(paginationConfig.total / paginationConfig.pageSize)) {
        throw new Error('没有下一题了')
      }
      else {
        paginationConfig.current += 1
        await getList()
        newIndex = 0
      }
    }
  }
  try {
    newQid = list.value[newIndex].questionBankUUID
  }
  catch (error) {
    throw new Error('题目列表为空')
  }
  nextTick(() => highLightRowByIndex(newIndex))
  return newQid
}

// 根据index将某一行高亮
function highLightRowByIndex(index: number) {
  const rows = document.querySelectorAll('.questions-table .ant-table-tbody tr.ant-table-row')
  rows.forEach((ele, i) => {
    if (i === index) {
      ele.classList.add('active')
    }
    else {
      ele.classList.remove('active')
    }
  })
}

getList()
emitter.on('updateQuestionList', getList) // 其他菜单（回收站、草稿箱、ai批量出题）可能会对当前题库的题目进行更新

// ——————————————————————————————————————————————ai校对——————————————————————————————————————————————————————
const aiProofreadLoading = ref(false)
const taskVisible = ref(false) // 进度条弹层显隐
const taskId = ref('')
const proofreadingTaskRef = ref<InstanceType<typeof ProofreadingTask>>()
const aiProofreadingConfirmVisible = ref(false) // 批量校对确认框显隐控制

// ai校对按钮的点击事件
async function handleAiProofreadingBtnClick(e: Event) {
  if (aiProofreadLoading.value || proofreadingTaskRef.value?.isActive) {
    // 如果AI校对按钮正在转圈则展示信息
    const taskIds = await getaiproofreadingtaskids({})
    if (taskIds.includes(taskId.value) && taskIds[0] === taskId.value) {
      taskVisible.value = true
    }
    else {
      message.warning('排队处理中')
    }
  }
}

// 创建一个ai校对任务
async function handleAiproofreading() {
  const taskIds = await getaiproofreadingtaskids({})
  if (taskIds && taskIds.length) 
    message.info('当前部门已有AI校对任务，正在排队等候处理')
  try {
    aiProofreadLoading.value = true
    taskId.value = await aiproofreadingbycatg({
      categoryUUID: props.subjectUUID, // bab1072e-cf74-4088-bb7e-81cd8a789c88
    })
    if (!taskIds.length) 
      taskVisible.value = true // 只有不需要排队立即执行的任务才展示进度条
  }
  catch (error) {
    console.log(error)
  }
  finally {
    aiProofreadLoading.value = false
    aiProofreadingConfirmVisible.value = false
  }
}

// 查看ai批量校对的结果
const taskFinishedModalVisible = ref(false)
const taskFinishDetail = ref<any>()
function showAiProofreadResult(result: any) {
  taskFinishDetail.value = result
  taskFinishedModalVisible.value = true
}

onDeactivated(() => {
  questionAddFormVisible.value = false
  questionEditFormVisible.value = false
  questionProofreadFormVisible.value = false
})

defineExpose({
  selectedRows,
  layoutTags,
})
</script>

<template>
  <div class="question-manage">
    <div class="question-manage-header">
      <div class="header-left">
        <a-input-search
          v-model:value.trim="searchContent"
          placeholder="请输入题干"
          allow-clear
          @search="getFirstPageList"
        />
        <div style="display: flex;align-items: center;">
          <a-checkbox v-model:checked="includeSubCatg" @change="getFirstPageList">
            包含子题库试题
          </a-checkbox>
          <a-tooltip placement="right" overlay-class-name="light">
            <template #title>
              <span>勾选后，题目列表包含所选题库及其子题库的题目</span>
            </template>
            <svg-icon class="common-info-icon" name="info2" />
          </a-tooltip>
        </div>
        <a-radio-group
          v-if="!writable"
          v-model:value="listType"
          size="large"
          @change="getFirstPageList"
        >
          <a-radio v-for="item in listTypeEnum" :value="item">
            <div style="display: flex; align-items: center; white-space: nowrap;">
              <span>{{ item }}</span>
              <span v-if="item !== '可选'" style="padding-left: 2px; line-height: 1.8">
                (<a-badge
                  :count="item === '已选' ? selectedRowKeys.length : defaultExcludedRowKeys.length"
                  :overflow-count="999"
                  :number-style="{
                    backgroundColor: '#fff',
                    color: '#5478EE',
                    padding: '0',
                    fontSize: '14px',
                    display: 'inline-block',
                  }"
                  :show-zero="true"
                />)
              </span>
            </div>
          </a-radio>
        </a-radio-group>
      </div>
      <!--v-if="
          writable
            && store.getters.permiss.quesmag.write
            && (store.getters.userInfo.officialCert || !isOfficialSubject)
        "  -->
      <div
        class="header-right"
        v-if="operationFlag"
      >
        <a-button type="primary" @click="createQuestion">
          新增题目
        </a-button>
        <div style="margin-left: 2px;" @click="$router.push(`/admin/subjectManage/question-generator?categoryId=${subjectId}`)">
          <a-button class="common-ai-button" style="border-radius: 8px;">
            <template #icon>
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
            </template>
            批量出题
          </a-button>
        </div>
        <a-popconfirm
          v-model:visible="aiProofreadingConfirmVisible"
          title="确定对当前题库及其子题库进行AI校对吗？"
          ok-text="确定"
          cancel-text="取消"
          placement="bottom"
          :disabled="aiProofreadLoading || proofreadingTaskRef?.isActive"
          @confirm="handleAiproofreading"
          @cancel="aiProofreadingConfirmVisible = false"
        >
          <div style="margin-left: 2px;" @click="handleAiProofreadingBtnClick">
            <!-- 这里必须用div包裹，不然loading状态的a-btn不会响应点击 -->
            <a-button :loading="aiProofreadLoading || proofreadingTaskRef?.isActive" class="common-ai-button" style="border-radius: 8px;">
              <template #icon>
                <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
              </template>
              校对
            </a-button>
          </div>
        </a-popconfirm>
        <a-button @click="deleteMultiQuestion">
          批量删除
        </a-button>
      </div>
    </div>
    <div v-show="tagsList.length && includeSubCatg" ref="allTagsContainerRef" class="all-tags-container">
      <span
        v-if="tagsList.length && tagBtnState !== '隐藏'"
        class="more-btn"
        @click="handleTagBtnStateChange"
      >{{ tagBtnState === '更多' ? '收起' : '更多' }} <svg-icon name="uparrow" class="arrow-icon" /></span>
      <template v-for="(item, index) in tagsList" :key="item.id">
        <span
          class="item"
          :class="{ active: activeTagIds.includes(item.id) }"
          @click="handleClickTag(item.id)"
        >{{ item.name }}</span>
      </template>
    </div>
    <!-- || (isOfficialSubject && !store.getters.userInfo.officialCert) -->
    <a-table
      class="questions-table common-table"
      sticky
      :columns="tableColumns"
      :row-key="(record: any) => record.id"
      :data-source="list"
      :row-selection="{
        selectedRowKeys,
        onSelect: updateSelectedRowKeys,
        onSelectAll,
        getCheckboxProps: (record: any) => ({ disabled: disabledRowKeys.includes(record.id) }),
      }"
      :loading="loading"
      :scroll="{ x: 1200, y: 'calc(98% - 20px)' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      @resize-column="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'body'">
          <div style="display: flex;align-items: start;">
            <span class="tooltip-style">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ record.complicatedediting ? record.body?.slice(0, 100)  : record.body }}
                </template>
                <div class="question-content" v-html="getQuestionContentByRecord(record)" />
              </a-tooltip>
            </span>
            <span v-if="record.recover && writable" class="recover-tag">已恢复</span>
            <span v-if="defaultExcludedRowKeys.includes(record.id)" class="exclude-tag">已排除</span>
          </div>
        </template>
        <template v-else-if="column.key === 'types'">
          <span style="font-family: PingFang-SC-Regular;">{{ getType(record.type) }}</span>
        </template>
        <template v-else-if="column.key === 'accuracy'">
          <span style="font-family: PingFang-SC-Regular;">
            {{ formatAccuracy(record) }}
          </span>
        </template>
        <template v-else-if="column.key === 'referenceCount'">
          <span style="font-family: PingFang-SC-Regular;">
            {{ record.referenceCount <= 0 ? '-' : record.referenceCount }}
            <!-- {{record.referenceCount }} -->
          </span>
        </template>
        <template v-else-if="column.key === 'proofreading'">
          <div style="display: flex; align-items: center;">
            <span :class="[['status_gray', 'status_green', 'status_red'][record.proofreading]]" />
            <img v-if="record.proofreading_info?.is_ai" style="margin-right: 4px;" src="@/assets/icons/svg/ai.svg" alt="">
            <span style="font-family: PingFang-SC-Regular;">{{ ['未校对', '校对正确', '校对错误'][record.proofreading] }}</span>
          </div>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <template #title>
                <span class="tooltip-text">校对</span>
              </template>
              <a-button type="text" size="small" @click="proofreadQuestion(record)">
                <img src="@/assets/images/svg/proofread.svg" alt="">
              </a-button>
            </a-tooltip>
            <a-divider type="vertical" />
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <template #title>
                <span class="tooltip-text">编辑</span>
              </template>
              <a-button
                type="text"
                size="small"
                @click="editQuestion(record)"
              >
                <!-- 
                  :disabled="!store.getters.userInfo.officialCert && record.official_cert"
                -->
                <img v-if="!store.getters.userInfo.officialCert && record.official_cert" src="@/assets/icons/svg/disabled_edit.svg" alt="">
                <img v-else src="@/assets/icons/svg/editable.svg" alt="">
              </a-button>
            </a-tooltip>
            <a-divider type="vertical" />
            <a-tooltip placement="bottom" color="#fff" :destroy-tooltip-on-hide="true">
              <template #title>
                <span class="tooltip-text">删除</span>
              </template>
              <a-popconfirm
                title="确定删除该题目？"
                ok-text="确定"
                cancel-text="取消"
                placement="left"
                :disabled="!store.getters.userInfo.officialCert && record.official_cert"
                @confirm="() => { deleteQuestion(record) }"
              >
                <a-button
                  type="text"
                  danger
                  size="small"
                  :disabled="!store.getters.userInfo.officialCert && record.official_cert"
                >
                  <img
                    v-if="!store.getters.userInfo.officialCert && record.official_cert"
                    src="@/assets/icons/svg/disabled_delte.svg"
                    style="width: 16px; margin-bottom: 3px"
                  >
                  <img
                    v-else
                    src="@/assets/images/svg/icon_delete.svg"
                    style="width: 16px; margin-bottom: 3px"
                  >
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded-row">
          <QuestionItemDisplay
            :question-detail="record"
            show-score
            show-type
            show-tag
            show-correct-answer
            show-question-difficulty
            show-question-points-and-basis
            show-question-proofreading-record
            option-letter-type="text"
          />
        </div>
      </template>
    </a-table>
  </div>

    <a-modal v-model:visible="questionAddFormVisible" title="新增题目" wrap-class-name="full-screen-modal" width="100%" :mask-closable="false" :closable="false" :keyboard="false" :footer="null" destroy-on-close>
     <QuestionAddForm v-if="questionAddFormVisible" :subject-id="$props.subjectId" :subjectUUID="subjectUUID" @close="onAddFormClose" />
      <!-- <QuestioEditor v-if="questionAddFormVisible" :subject-id="$props.subjectId" @close="onAddFormClose" /> -->
    </a-modal>

    <a-modal v-model:visible="questionEditFormVisible" title="编辑题目" wrap-class-name="full-screen-modal" width="100%" :mask-closable="false" :closable="false" :keyboard="false" :footer="null" destroy-on-close>
      <QuestionEditForm v-if="questionEditFormVisible" :qid="qid" :can-convert="true" @close="onEditFormClose" />
    </a-modal>

    <a-modal v-model:visible="questionProofreadFormVisible" title="题目校对" wrap-class-name="full-screen-modal proofread-modal" width="100%" :mask-closable="false" :keyboard="false" :footer="null" @cancel="getList">
      <QuestionProofreadForm v-if="questionProofreadFormVisible" v-model:qid="qid" :get-sibling-question-fn="getSiblingQuestion" :last-btn-disabled="lastBtnDisabled" :next-btn-disabled="nextBtnDisabled" @close="(questionProofreadFormVisible = false), getList()" />
    </a-modal>

    <a-modal v-model:visible="taskVisible" title="AI校对" :footer="null">
      <ProofreadingTask ref="proofreadingTaskRef" :task-id="taskId" @close="taskVisible = false" @finish="getList" @show-result="showAiProofreadResult" />
    </a-modal>

    <a-modal v-model:visible="taskFinishedModalVisible" :footer="null" :width="260" :z-index="9999">
      <template #title>
        <CheckCircleFilled style="font-size: 20px; color: #52c41a;" />
        <span style="margin-left: 8px;">AI校对完成</span>
      </template>
      <ProofreadingTaskFinished :data="taskFinishDetail" @close="taskFinishedModalVisible = false" />
    </a-modal>
</template>

<style lang="less">
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.ques-drawer {
  .ant-drawer-title {
    font-weight: bold;
  }
}

.proofread-modal {
  .ant-modal-header {
    padding: 24px;
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-close{
    .ant-modal-close-x{
        line-height: 71px;
    }
  }
}
</style>

<style lang="less" scoped>
.question-manage {
  height: 100%;
  overflow: auto;
  padding: 0 10px 0 16px;
  display: flex;
  flex-direction: column;
}
.question-manage-header {
  margin: 0px 0;
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;

    :deep(.ant-checkbox-wrapper) {
      margin-left: 16px;
      span {
        font-size: 14px;
        white-space: nowrap;
      }
    }

    .ant-radio-group {
      margin-left: 24px;
      display: flex;
      align-items: center;

      :deep(span) {
        font-size: 14px;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    .ant-btn {
      border-radius: 8px;
      margin-left: 8px;
      font-size: 14px;
    }
  }
}

.recover-tag {
  font-size: 12px !important;
  color: #2f8c00 !important;
  background-color: #f3f7f1;
  border-radius: 4px;
  padding: 2px 4px;
}

.exclude-tag {
  font-size: 12px;
  color: #d71310 !important;
  background-color: #fcf0f0;
  border-radius: 4px;
  padding: 2px 4px;
}

.all-tags-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding: 0 100px 0 0;
  height: v-bind(tagsHeight);
  overflow: hidden;

  .more-btn {
    cursor: pointer;
    position: absolute;
    top: 3px;
    right: 16px;
    color: #626262;
    font-size: 12px;

    .arrow-icon {
      transform: v-bind(tagsRotate);
    }
  }

  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  }

  .active {
    background: #5478ee;
    color: #fff;
  }
}

.table-bar {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-checkbox-wrapper {
    font-size: 15px;
  }

  .show-columns {
    font-size: 15px;

    span {
      margin-right: 10px;
    }
  }
}

:deep(.ant-table) {
  .ant-table-expanded-row td:nth-child(1) {
    box-shadow: 2px 0px 0px 0px #5478ee inset;
  }
}

.expanded-row {
  padding: 16px;
}
.tooltip-style {
  display: block;
  // width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left !important;
  margin-right: 8px;
}

:deep(.question-content) {
  display: inline-block;
  width: 100%;
  font-family: PingFang-SC-Regular !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 23px;
  align-content: center;
  img {
    height: 50px;
    width: auto;
    cursor: initial!important;
    display: inline-block!important;
    float: none!important;
  }
  p {
    display: inline-block;
  }
}
.full-screen-modal{
  .ant-modal-body{
    .create-question-container
    :deep(.close-btn){
      line-height: 38px;
    }
  }
}
</style>
