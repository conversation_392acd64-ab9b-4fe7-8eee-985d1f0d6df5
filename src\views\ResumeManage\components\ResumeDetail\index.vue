<script setup lang="ts">
// import { getResumeByIdApi } from '@/api/admin/resumeManage'
import type { CampusExperienceItemProps, CertificateItemProps, EducationItemProps, EvaluationDataProps, ExperienceItemProps, ImprovementProps, ProfileHeaderProps, ProjectItemProps, SkillMapDataProps, StrengthProps, SummaryDataProps, TechSkillsDataProps, WeaknessItemProps } from './shared'
import { getResumeByIdApi } from '@/api/admin/resumeManage'
import { motion } from 'motion-v'
import CampusExperience from './CampusExperience.vue'
import CertificateExperience from './CertificateExperience.vue'
import CoreStrengths from './CoreStrengths.vue'
import EducationExperience from './EducationExperience.vue'
import Evaluation from './Evaluation.vue'
import ImprovementAreas from './ImprovementAreas.vue'
import ProfileHeader from './ProfileHeader.vue'
import ProfileSummary from './ProfileSummary.vue'
import ProjectExperience from './ProjectExperience.vue'
import ResumeFooter from './ResumeFooter.vue'
import SkillMap from './SkillMap.vue'
import TechOverview from './TechOverview.vue'
import WeaknessAnalysis from './WeaknessAnalysis.vue'
import WorkExperience from './WorkExperience.vue'

const props = withDefaults(defineProps<{
  propsCurrentResumeId: string
}>(), {
  propsCurrentResumeId: '',
})

onMounted(() => {
  getList(props.propsCurrentResumeId)
})

const currentResumeData = ref<{
  userData: ProfileHeaderProps
  evaluationData: EvaluationDataProps
  strengthsData: StrengthProps
  improvementData: ImprovementProps
  skillMapData: SkillMapDataProps
  summaryData: SummaryDataProps
  weaknessData: WeaknessItemProps
  techSkillsData: TechSkillsDataProps // 技能总览
  workExperienceData: ExperienceItemProps // 工作经历
  educationExperienceData: EducationItemProps // 教育经历
  projectExperienceData: ProjectItemProps // 项目经历
  campusExperienceData: CampusExperienceItemProps // 校园经历
  certificateExperienceData: CertificateItemProps // 获奖证书
}>({
  userData: {
    resumeId: '',
    avatar: 'https://picsum.photos/id/1/100/100',
    candidateName: '',
    englishName: '',
    position: '',
    workYears: '',
    gender: '',
    graduateSchool: '',
    highestEducation: '',
    location: '',
    birthday: '',
    age: '',
    phone: '',
    email: '',
    resumeStatus: '',
    updateTime: '',
    matchScore: 0,
    resumeFileUrl: '',
    downloadFileUrl: '',
    hometown: [],
  },
  evaluationData: {
    matchScore: 75,
    overallEvaluation: '<span class="sf-1">拥有3年Java开发经验，</span>精通Java语言和Spring框架，具备扎实的算法和数据结构基础，参与过多个核心项目的开发，展现出良好的架构设计和编码能力。缺乏另一种开发语言的精通和数据库优化的具体经验。建议进入面试环节，重点考察其多语言能力和数据库优化经验。',
  },
  strengthsData: [
    {
      description: '持续<span class="sf-2">学习能力强</span>，对新技术保持高度敏感性和学习热情',
    },
    {
      description: '有丰广经验，参与过垂直业务系统开发，技术视野开阔',
    },
    {
      description: '具备团队管理经验，善于协调和通过有效沟通解决问题',
    },  
  ],
  improvementData: [
    {
      description: '后端技术栈<span class="sf-2">有待增强</span>，特别是 Python 相关技术',
    },
    {
      description: '容器化部署经验较少，Docker 技术掌握不够深入',
    },
    {
      description: '业务经验偏电商领域，其他领域经验较少',
    },
  ],
  skillMapData: [],
  summaryData: [
    { word: '乐观', weight: 10 },
    { word: '数学好', weight: 20 },
    { word: '六边形战士', weight: 30 },
    { word: '善于沟通', weight: 40 },
    { word: '爱学习', weight: 50 },
    { word: '资深程序员', weight: 60 },
    { word: '学历优秀', weight: 70 },
    { word: '逻辑能力强', weight: 80 },
    { word: '多次获奖', weight: 90 },
    { word: '严谨', weight: 100 },
    { word: '开朗', weight: 90 },
  ],
  weaknessData: [
    {
      title: '工作经历连续性存疑',
      description: '2018年毕业后立即加入阿里巴巴工作，但简历中未来及实习经历，需要进一步询问入职途径。',
    },
    {
      title: '技术描述与项目经验不匹配',
      description: '简历中提到精通 React，但工作经历中未展现相关经验，需要进一步考察技术深度。',
    },
    {
      title: '管理经验真实性待验证',
      description: '2018年毕业后立即加入阿里巴巴工作，但简历中未来及实习经历，需要进一步询问入职途径。',
    },
  ],
  workExperienceData: [],
  educationExperienceData: [],
  projectExperienceData: [],
  campusExperienceData: [],
  techSkillsData: {
    required: {
      label: '必备',
      skills: [],
    },
    bonus: {
      label: '加分',
      skills: [],
    },
    extra: {
      label: '额外',
      skills: [],
    },
    missing: {
      label: '缺失',
      skills: [],
    },
  },
  certificateExperienceData: [],
})

const loading = ref(false)

function getList(propsCurrentResumeId: string) {
  loading.value = true
  getResumeByIdApi({ resumeId: propsCurrentResumeId })
    .then((res: any) => {
      const userData: ProfileHeaderProps = {
        resumeId: res.resumeId,
        avatar: res.avatar,
        candidateName: res.candidateName,
        englishName: res.candidateNamePy,
        position: '',
        workYears: res.workYears,
        gender: res.gender,
        graduateSchool: res.graduateSchool,
        highestEducation: res.highestEducation,
        location: res.location,
        birthday: res.birthday,
        age: res.age,
        phone: res.phone,
        email: res.email,
        resumeStatus: res.resumeStatus,
        updateTime: '',
        matchScore: 0,
        hometown: res.hometown,
        resumeFileUrl: res.resumeFileUrl,
        downloadFileUrl: res.downloadFileUrl,
      }

      // 更新当前简历数据的个人信息
      currentResumeData.value.userData = userData

      // 综合评价
      currentResumeData.value.evaluationData = res.coreEvaluate
      // 核心优势
      currentResumeData.value.strengthsData = res.strengths
      // 待提升项
      currentResumeData.value.improvementData = res.weaknesses
      // 技能总览
      currentResumeData.value.techSkillsData.required.skills = res.skillMatches.requiredSkills
      currentResumeData.value.techSkillsData.bonus.skills = res.skillMatches.additionalSkills
      currentResumeData.value.techSkillsData.extra.skills = res.skillMatches.preferredSkills
      currentResumeData.value.techSkillsData.missing.skills = res.skillMatches.missingSkills

      // 能力维度雷达图
      currentResumeData.value.skillMapData = res.radarChartEvaluation?.radarChartItems
      // 能力总结
      currentResumeData.value.summaryData = res.wordCloud
      // 质疑点分析
      currentResumeData.value.weaknessData = res.suspiciousPoints
      // 工作经历
      currentResumeData.value.workExperienceData = res.workExperiences
      // 教育经历
      currentResumeData.value.educationExperienceData = res.educations
      // 项目经历
      currentResumeData.value.projectExperienceData = res.projectExperiences
      // 校园经历
      currentResumeData.value.campusExperienceData = res.schoolExperiences
      // 获奖证书
      currentResumeData.value.certificateExperienceData = res.certificates
    })
    .catch((err: any) => {
      console.log('查询简历的所有信息失败：', err)
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <!-- <div v-loading="loading" class="relative z-[999] container bg-[white] shadow-[0px_6px_18px_0px_rgba(0,0,0,0.10)] p-[16px] rounded-[8px] overflow-hidden box-border">
    666
  </div> -->
  <motion.div 
    class=" z-10 gap-5 flex flex-col w-full" 
    :animate="{ opacity: 1 }"
    :initial="{ opacity: 0 }"
    :transition="{ duration: 0.8 }"
  >
    <!-- 个人信息 -->
    <motion.div class="w-full grid grid-cols-1">
      <ProfileHeader 
        :user-data="currentResumeData.userData" 
        @edit-resume-completed="getList(props.propsCurrentResumeId)"
      />
    </motion.div>

    <!-- 评估和优势部分 -->
    <motion.div class=" grid grid-cols-3 gap-4">
      <Evaluation :evaluation-data="currentResumeData.evaluationData" />
      <CoreStrengths :strengths-data="currentResumeData.strengthsData" />
      <ImprovementAreas :improvement-data="currentResumeData.improvementData" />
    </motion.div>

    <!-- 能力和技术部分 -->
    <div class="grid grid-cols-3 gap-4">
      <SkillMap :skill-map-data="currentResumeData.skillMapData" />
      <TechOverview :tech-skills-data="currentResumeData.techSkillsData" />
      <ProfileSummary :summary-data="currentResumeData.summaryData" />
    </div>

    <!-- 质疑点分析 -->
    <div v-show="currentResumeData?.weaknessData?.length > 0" class="grid grid-cols-1">
      <WeaknessAnalysis :weakness-data="currentResumeData.weaknessData" />
    </div>

    <!-- 教育经历 -->
    <EducationExperience :education-experience-data="currentResumeData.educationExperienceData" />

    <!-- 工作经历 -->
    <WorkExperience :work-experience-data="currentResumeData.workExperienceData" />
    
    <!-- 项目经历 -->
    <ProjectExperience :project-experience-data="currentResumeData.projectExperienceData" />

    <!-- 校园经历 -->
    <CampusExperience :campus-experience-data="currentResumeData.campusExperienceData" />

    <!-- 获奖证书 -->
    <CertificateExperience :certificate-experience-data="currentResumeData.certificateExperienceData" />

    <!-- 页脚 -->
    <motion.div
      :initial="{ opacity: 0, y: 10, filter: 'blur(4px)' }"
      :while-in-view="{
        opacity: 1,
        y: 0,
        filter: 'blur(0px)',
      }"
      :transition="{
        delay: 0,
        duration: 0.8,
        ease: 'easeInOut',
      }"
      class="grid grid-cols-1"
    >
      <ResumeFooter 
        :user-data="currentResumeData.userData" 
      />
    </motion.div> 
  </motion.div>
</template>
