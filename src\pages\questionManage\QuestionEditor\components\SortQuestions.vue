<script setup lang="ts">
const props = defineProps<{
  formState: any
  optionEditorsRef: any
  handleOptionBlur: () => void
  delOption: (event: Event, index: number) => void
  addOption: () => void
  showGenerateOptionModal: () => void

}>()
</script>

<template>
  <a-form-item v-if="formState.type === 6" name="options" class="question-options">
    <!-- eslint-disable -->
    <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
      <span class="optionContent">选项内容</span>
      <template v-for="(item, index) in formState.options" :key="index">
        <div class="sortingQuestionOptions item-option">
          <a-radio :value="item.value">
            <span class="option-radio">{{ item.value }}</span>
          </a-radio>
          <a-textarea
            v-if="!formState.complicatedediting"
            v-model:value="item.content"
            class="option-content order-option-content"
            :auto-size="{ minRows: 1 }"
            placeholder="点击，编辑选项"
            @blur="handleOptionBlur"
          />
          <div v-else class="editor-wrapper">
            <VueQuillEditor
              :ref="optionEditorsRef"
              v-model:content="item.content"
              @blur="handleOptionBlur"
            />
          </div>
          <svg-icon
            class="del-icon"
            name="circle-del"
            width="16px"
            height="16px"
            @click.prevent="delOption($event, index)"
          />
        </div>
      </template>
      <span class="rightOrder">（按正确顺序设置）</span>
    </a-radio-group>
    <div class="add-option-btn sort-question" @click="addOption">
      <svg-icon name="plus" />
      <span>添加选项</span>
    </div>
  </a-form-item>
  <div v-if="formState.type === 6" class="fill-blank-config">
    <div>
      <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
      <span>本题适用于“部分回答正确时可得分”</span>
      <a-tooltip placement="right">
        <template #title>
          <span>当考试设置为“部分回答正确时可得分”，并且本题勾选了此选项，则学生的答案中包含连续正确的顺序不少于2个时，可按照比例得分；否则，在没有完全回答正确的情况下不得分。</span>
        </template>
        <svg-icon class="common-info-icon" name="info2" />
      </a-tooltip>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>