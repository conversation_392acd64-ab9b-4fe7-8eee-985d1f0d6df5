export default (formState: any) => {
  // 算法题添加变量
  const handleAddParameter = () => {
    formState.value.parameters.push({ name: '', type: '', is_list: false })
  }
  // 算法题删除变量
  const handleDelParameter = (payload: number) => {
    formState.value.parameters.splice(payload, 1)
  }
  const handleAddInstance = () => {
    const newItem: any = { input: [], output: [] }
    const paramNameList = formState.value.parameters.map((item: any) => item.name)
    paramNameList.forEach((name: any) => {
      newItem.input.push({ [name]: '' })
    })
    newItem.output.push({ type: 'value', value: '' })
    formState.value.btestcase.push(newItem)
  }

  const handleDelInstance = (payload: number) => {
    formState.value.btestcase.splice(payload, 1)
  }

  const addTestCase = () => {
    const newItem: any = { input: [], output: [] }
    const paramNameList = formState.value.parameters.map((item: any) => item.name)
    paramNameList.forEach((name: any) => {
      newItem.input.push({ [name]: '' })
    })
    newItem.output.length = 0
    newItem.output.push({ type: 'value', value: '' })
    formState.value.ptestcase.push(newItem)
  }

  const delTestCase = (payload: number) => {
    formState.value.ptestcase.splice(payload, 1)
  }

  return {
    handleAddParameter,
    handleDelParameter,
    handleAddInstance,
    handleDelInstance,
    addTestCase,
    delTestCase
  }
}
