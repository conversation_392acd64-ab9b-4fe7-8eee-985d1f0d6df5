<script lang="ts" setup>
import type { InterviewQuestionFormState } from '@/types/interviewQuestion'
import { useVModel } from '@vueuse/core'
import _ from 'lodash'

const props = defineProps<{
  formState: InterviewQuestionFormState
}>()

const emits = defineEmits<{
  (e: 'update:formState', v: any): void
}>()

const proxy = useVModel(props, 'formState', emits, {
  passive: true,
  deep: true,
})
</script>

<template>
  <div>
    <a-form-item label="评分依据" name="scorebasis">
      <!-- show-count maxlength -->
      <a-textarea v-model:value="proxy.scorebasis" :rows="4" :max-length="1000" placeholder="点击编辑" />
    </a-form-item>
    <a-form-item label="参考答案（选填）">
      <a-textarea v-model:value="proxy.reference_answer" :rows="4" :max-length="10000" placeholder="非必填" />
    </a-form-item>
  </div>
</template>

<style lang="less" scoped>
</style>