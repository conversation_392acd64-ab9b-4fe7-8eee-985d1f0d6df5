<template>
  <a-drawer
    class="custom-drawer-container"
    v-model:visible="visible"
    :closable="false"
    :width="width"
    @close="closeDrawer"
    v-bind="$attrs"
  >
    <slot name="header"></slot>
    <slot></slot>
    <slot name="footer"></slot>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
const props = defineProps({
  drawerVisible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Custom Drawer'
  },
  width: {
    type: String,
    default: '520'
  }
})

const emits = defineEmits(['closeDrawer'])

const visible = ref(false)

const closeDrawer = () => {
  emits('closeDrawer')
}

watch(
  () => props.drawerVisible,
  (val) => {
    visible.value = val
  }
)
</script>
