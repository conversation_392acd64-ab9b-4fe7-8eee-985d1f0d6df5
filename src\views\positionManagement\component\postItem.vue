<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
function editPost() {
  router.push({
    name: 'postEdit',
    query: {
      id: 1,
    },
  })
}
</script>

<template>
  <div class="p-[24px] bg-white shadow-sm border-b border-gray-200 mb-6 min-h-[80px] rounded-[8px] w-full flex flex-wrap items-center justify-between">
    <div class="flex flex-nowrap items-center">
      <a-tag color="#f1f4fe" style="color:#5478ee" class="mr-[12px]">
        社招
      </a-tag>
      <div class="font-bold text-[18px]">
        Java前端工程师
      </div>
    </div>

    <div class="flex flex-nowrap">
      <div class="w-[50px] flex flex-wrap justify-center">
        <div class="w-full justify-center text-[#5478ee] text-[18px] text-center font-bold">
          1209
        </div>
        <div class="text-[12px] text-[rgba(0,0,0,0.45)]">
          投递数量
        </div>
      </div>
    </div>

    <div class="text-[rgba(0,0,0,0.45)]">
      <div class="mb-[12px] flex items-center">
        <el-icon><User /></el-icon>
        <span class="ml-[12px]">
          张三三 2025-08-08 10:09
        </span>
      </div>
      <div class="flex items-center">
        <el-icon><Timer /></el-icon>
        <span class="ml-[12px]">
          招聘时长45天
        </span>
      </div>
    </div>

    <div>
      <span class="text-[12px] text-[#5478ee] cursor-pointer" @click="editPost">编辑</span>
      <el-divider direction="vertical" />
      <span class="text-[12px] text-[#5478ee] cursor-pointer">删除</span>
    </div>
  </div>
</template>

<style scoped>

</style>