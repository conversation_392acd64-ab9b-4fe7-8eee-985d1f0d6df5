import { deleteFile } from '@/pages/questionManage/hooks/api'
import { DeleteOutlined } from '@ant-design/icons-vue'
import './statusItem.css'

function PDF() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
      <title>PDF</title>
      <g id="问卷管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="面试管理-人才库-添加候选人01" transform="translate(-136.000000, -322.000000)" fill-rule="nonzero">
          <g id="弹窗备份-2" transform="translate(81.000000, 51.000000)">
            <g id="编组-2" transform="translate(32.000000, 259.000000)">
              <g id="PDF" transform="translate(23.000000, 12.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="24" height="24" />
                <path d="M14.4550078,1.99999219 L21,8.154 L21,20.0000156 C21,21.1045808 20.104573,22.0000078 19,22.0000078 L5,22.0000078 C4.46955722,22.0000078 3.96084783,21.7892917 3.58577586,21.4142154 C3.2107039,21.039139 3,20.5304272 3,19.9999922 L3,4.00000781 C3,3.46957284 3.2107039,2.96086098 3.58577586,2.58578462 C3.96084783,2.21070826 4.46955722,1.99999219 5,1.99999219 L14.4550078,1.99999219 Z M20.1820078,8.92300781 L13.6360078,2.77000781 L13.6360078,6.924 C13.6360078,8.02856518 14.5314348,8.92399219 15.6360078,8.92399219 L20.1820078,8.92399219 L20.1820078,8.92300781 Z M5.45500781,12 L5.45500781,17.2689844 L6.11299219,17.2689844 L6.11299219,14.865 L6.45,14.865 C7.098,14.865 7.54999219,14.8389844 7.80499219,14.7889922 C8.05999219,14.739 8.27500781,14.652 8.45200781,14.529 C8.628,14.4079922 8.763,14.253 8.85700781,14.0659922 C8.952,13.8790078 8.99899219,13.6669922 8.99899219,13.4320078 C8.99899219,13.1970234 8.95099219,12.9859922 8.85400781,12.7990078 C8.75860508,12.6136129 8.620889,12.4532867 8.45200781,12.3310078 C8.28374134,12.207556 8.09222216,12.1194502 7.88899219,12.072 C7.68499219,12.024 7.25599219,12 6.603,12 L5.45500781,12 L5.45500781,12 Z M6.11299219,14.2669922 L6.11299219,12.6049922 L7.032,12.6049922 C7.88899219,12.6049922 8.31801563,12.8779922 8.31801563,13.425 C8.32201786,13.6608444 8.21907933,13.8858323 8.03800781,14.037 C7.85200781,14.2000078 7.50100781,14.277 6.98599219,14.2669922 L6.11299219,14.2669922 L6.11299219,14.2669922 Z M9.987,12 L9.987,17.2689844 L11.793,17.2689844 C12.543,17.2689844 13.113,17.157 13.5040078,16.9339922 C13.8940078,16.7110078 14.196,16.401 14.4079922,16.005 C14.619,15.6100078 14.7250078,15.1879922 14.7250078,14.742 C14.7250078,14.2959844 14.6539922,13.902 14.511,13.5619922 C14.3741836,13.2299541 14.1635167,12.933451 13.8949922,12.6949922 C13.6190888,12.4528308 13.2939537,12.2733792 12.942,12.1690078 C12.5740078,12.0559922 11.987,12 11.181,12 L9.987,12 L9.987,12 Z M10.6219922,16.6780078 L10.6219922,12.6049922 L10.959,12.6049922 C11.5870078,12.6049922 12.069,12.6349922 12.4060078,12.6949922 C12.7429922,12.7549922 13.0360078,12.8749922 13.2859922,13.0549922 C13.5359766,13.2349922 13.7290078,13.467 13.8640078,13.7530078 C13.9990078,14.0380078 14.067,14.349 14.067,14.685 C14.067,15.021 14.01,15.3169922 13.8949922,15.5739844 C13.7818949,15.8275874 13.6097782,16.05046 13.3930078,16.224 C13.173,16.4020078 12.9160078,16.5220078 12.6199922,16.584 C12.324,16.647 11.8519922,16.6780078 11.2039922,16.6780078 L10.6219922,16.6780078 L10.6219922,16.6780078 Z M18.5450156,14.7349922 L18.5450156,14.1229922 L16.341,14.1229922 L16.341,12.612 L18.5450156,12.612 L18.5450156,12 L15.6820313,12 L15.6820313,17.2689844 L16.3410234,17.2689844 L16.3410234,14.7350156 L18.5450156,14.7350156 L18.5450156,14.7349922 Z" id="形状" fill="#EA5454" />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default defineComponent({
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const deleteItem = async () => {
      await deleteFile({
        taskIds: [props.item.taskId],
      })
    }
    return () => (
      <div>
        <div class="w-full h-[60px] overflow-hidden relative bg-[#f7f7f7] rounded-[8px] px-[23px] py-[16px] mb-[8px] flex items-center justify-between bg-gradient-linear">
          {/* 进度条 */}
          <div class="h-full absolute top-0 left-0 bg-liner" style={{ width: `${props.item.progress / 10}%` }} />
          <div class="flex items-center z-[19]">
            <PDF />
            {/* 处理文件名过长 省略号显示 */}
            <span class="text-ellipsis overflow-hidden whitespace-nowrap w-[400px]">
              {props.item.originalFilename}
            </span>
          </div>
          <span class="z-[19] flex items-center">
            <DeleteOutlined v-show={props.item.status === 'FAILED'} class="mr-[12px] cursor-pointer" onClick={deleteItem} />
            <span style={{ color: props.item.status === 'FAILED' ? '#d10000' : '' }}>
              {props.item.statusDesc}
            </span>
          </span>
        </div>
      </div>
    )
  },
})