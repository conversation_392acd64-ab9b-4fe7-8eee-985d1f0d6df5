import { watch } from 'vue'

export default (formState: any, scoreInputTotalRef: any, options: any, judge: any) => {
  watch(
    () => formState.value.type,
    (val: any) => {
      scoreInputTotalRef.value.score = 0
      if (formState.value) {
        switch (val) {
          case 0:
          case 1:
          case 3:
          case 7:
          case 5:
          case 6:
            formState.value.answer = ''
            formState.value.options = JSON.parse(JSON.stringify(options))
            break
          case 2:
            formState.value.answer = ''
            formState.value.options = JSON.parse(JSON.stringify(judge))
            break
          case 4:
            delete formState.value.options
            break
        }
      }
    }
  )
}
