<script lang="ts">
import { computed, toRefs, reactive, defineComponent, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  props: {
    msg: String
  },
  setup() {
    const store = useStore()
    const { t, locale } = useI18n()
    const data = reactive({
      check: localStorage.getItem('lang') === 'zh-CN'
    })
    onMounted(() => {
      getData()
    })
    const handleClick = () => {
      store.dispatch('add')
    }
    const getData = () => {
      const params = {
        username: 'lcs',
        roles: 'student',
        password: '123'
      }
      // getDetail('/api/v1/login', params).then((res) => {
      //   console.log(res)
      // })
    }
    const changeLang = () => {
      locale.value = data.check ? 'zh-CN' : 'en'
      localStorage.setItem('lang', locale.value)
    }
    return {
      count: computed(() => store.getters.count),
      handleClick,
      ...toRefs(data),
      changeLang
    }
  }
})
</script>

<template>
  <h1>{{ msg }}</h1>
  <div>{{ $t('header.text') }}</div>
  <a-button type="primary" @click="handleClick"> Start {{ count }} </a-button>
  <a-switch
    :checked-children="$t('lang.CN')"
    :un-checked-children="$t('lang.EN')"
    v-model:checked="check"
    @click="changeLang"
  />
</template>

<style lang="less" scoped>
a {
  color: #42b983;
}

label {
  margin: 0 0.5em;
  font-weight: bold;
}

code {
  background-color: #eee;
  padding: 2px 4px;
  border-radius: 4px;
  color: #304455;
}
</style>
