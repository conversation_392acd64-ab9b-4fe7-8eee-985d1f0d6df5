<script setup lang="ts">
import { getPostList, getResumeTaskList } from '@/pages/questionManage/hooks/api'
import { CloseOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import StatusList from './statusList'
import StatusPanel from './statusPanel.vue'
import Upload from './upload.vue'

const props = defineProps({
  viewType: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:file-list'])
const addCandidateVisible = defineModel()
const post = ref('')
const fileList = ref([])
const backEndFileList = ref([])
const postList = ref<any>([])
const disabledSelectPost = computed(() => !!fileList.value.length)
const intervelId = ref<any>(0)
const uploadRef = useTemplateRef<any>('upload')

function handleClose() {
  post.value = ''
  fileList.value = []
  backEndFileList.value = []
  addCandidateVisible.value = false
  clearInterval(intervelId.value)
  uploadRef.value.clear()
}

function getPostListExec() {
  getPostList({
    pageNo: 1,
    pageSize: 10,
  }).then((res: any) => {
    postList.value = res.records
    // post.value = res.records[0]?.id || ''
  })
}

const handlingList = ref([])
const readonlyLoading = ref(false)
async function getHandlingList() {
  const res = await getResumeTaskList({
    pageNo: 1,
    pageSize: 30,
    statusList: ['PENDING', 'PROCESS'],
  })
  // handlingList.value = res.records.filter((item:any) => item.statusDesc !== '已完成')
  handlingList.value = res.records
  readonlyLoading.value = false
}

function poolingTaskList() {
  readonlyLoading.value = true
  intervelId.value = setInterval(() => {
    getHandlingList()
  }, 1000)
}
function init() {
  if (props.viewType === 'readOnly')
    poolingTaskList()
  else
    getPostListExec()
}
function checkPost() {
  if (!post.value) {
    message.warning('请先选择岗位')
  }
}

onUnmounted(() => {
  clearInterval(intervelId.value)
})

watch(() => addCandidateVisible.value, (newVal) => {
  if (newVal)
    init()
})

watch(() => fileList.value, (newVal) => {
  emits('update:file-list', newVal)
}, {
  deep: true,
})
/**
 * TODO:
 * 1. 选择岗位后，查询此岗位下正在上传的列表
 */
</script>

<template>
  <!-- 添加候选人弹框 -->
  <el-dialog
    v-model="addCandidateVisible"
    width="630px"
    :show-close="false"
    :before-close="handleClose"
    z-index="10"
  >
    <template #title>
      <div class="w-full flex justify-between">
        <div class="text-[20px] font-bold">
          {{ viewType === 'readOnly' ? '处理进度' : '添加候选人' }}
        </div>
        <CloseOutlined class="cursor-pointer" @click="handleClose" />
      </div>
    </template>
    <div class="mt-[20px]">
      <!-- 添加候选人 -->
      <div v-if="viewType !== &quot;readOnly&quot;">
        <el-select
          v-model="post"
          :disabled="disabledSelectPost"
          placeholder="请选择应聘岗位"
          size="mini"
          style="width: 100%"
        >
          <el-option
            v-for="item in postList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
        <div class="relative">
          <Upload ref="upload" v-model="fileList" v-model:back-end-file-list="backEndFileList" :post-id="post" />
          <StatusPanel v-show="backEndFileList.length" :back-end-file-list="backEndFileList" />
          <div v-show="!post" class="cursor-pointer absolute w-full top-0 h-full" @click.stop="checkPost" />
        </div>
      </div>
      <!-- 上传进度 readOnly -->
      <div v-else v-loading="readonlyLoading" class="max-h-[600px] overflow-auto min-h-[100px]">
        <StatusList v-for="item in handlingList" :key="(item as any).taskId" :item="item" />
      </div>
    </div>

    <template #footer>
      <span v-show="viewType !== 'readOnly'" class="dialog-footer flex w-full justify-center">
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="less">
:deep(.el-tabs__nav-wrap) {
  &:after {
    display: none;
  }
}
</style>