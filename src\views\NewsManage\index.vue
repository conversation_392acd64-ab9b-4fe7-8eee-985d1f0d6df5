<template>
  <div class="account-manage">
    <div class="account-manage-header">
      <h2>新闻列表</h2>
      <div class="btn-group">
        <a-button type="primary" @click="openDrawer(data.newsData)">新增新闻</a-button>
        <a-button>批量删除</a-button>
      </div>
    </div>
    <a-table
      :columns="columns"
      :loading="data.loading"
      :row-key="(record:any) => record.id"
      :data-source="data.newsList"
      :row-selection="rowSelection"
      bordered
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #image="{ text: image }">
        <img :src="image" alt="" style="width: 100px" />
      </template>
      <template #action="{ record }">
        <span>
          <a-tooltip placement="bottom">
            <template #title>
              <span>编辑新闻</span>
            </template>
            <a-button type="text" primary size="small" @click="openDrawer(record)"
              ><form-outlined style="color: #ec6f00"
            /></a-button>
          </a-tooltip>
          <a-divider type="vertical" />
          <a-tooltip placement="bottom">
            <template #title>
              <span>删除该新闻</span>
            </template>
            <a-popconfirm
              title="确定删除该新闻？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteNews(record)"
            >
              <a-button type="text" danger size="small"><delete-outlined /></a-button>
            </a-popconfirm>
          </a-tooltip>
        </span>
      </template>
    </a-table>
    <news-drawer
      :isVisible="data.isVisible"
      :newsData="data.newsData"
      @cancelDrawer="handleCancelDrawer"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive } from 'vue'
import { FormOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import NewsDrawer from './components/NewsDrawer.vue'
import { getNews, delNews } from '@/api/admin/newsManage'
import { message } from 'ant-design-vue'
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    ellipsis: true,
    width: 100,
    resizable: true
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 300,
    ellipsis: true,
    resizable: true
  },
  {
    title: '摘要',
    dataIndex: 'abstract',
    key: 'abstract',
    ellipsis: true,
    width: 400,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'image',
    key: 'image',
    slots: { customRender: 'image' },
    ellipsis: true,
    width: 200,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'create_at',
    key: 'create_at',
    ellipsis: true,
    width: 200,
    resizable: true
  },
  {
    title: '操作',
    key: 'operate',
    width: 220,
    slots: { customRender: 'action' }
  }
]
interface DataItem {
  key: string
  id: string
  title: string
  abstract: string
  image: string
  content: string
  createDate: string
}
const rowSelection = {
  onChange: (selectedRowKeys: (string | number)[], selectedRows: DataItem[]) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows)
  },
  onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
    console.log(record, selected, selectedRows)
  },
  onSelectAll: (selected: boolean, selectedRows: DataItem[], changeRows: DataItem[]) => {
    console.log(selected, selectedRows, changeRows)
  }
}

export default defineComponent({
  components: {
    FormOutlined,
    DeleteOutlined,
    NewsDrawer
  },
  setup() {
    const data = reactive({
      loading: false,
      newsList: <any>[],
      newsData: {},
      isVisible: false
    })

    const openDrawer = (newsData?: object) => {
      if (newsData) {
        data.newsData = { ...newsData }
      }
      data.isVisible = true
    }

    // 关闭NewsDrawer
    const handleCancelDrawer = (val: boolean) => {
      data.isVisible = val
      data.newsData = {}
      // 刷新新闻列表
      getNewsList()
    }

    // 获取新闻列表
    const getNewsList = () => {
      data.loading = true
      getNews({ action: 'query', page: 1, per_page: 10 })
        .then((res: any) => {
          data.loading = false
          data.newsList = res
        })
        .finally(() => {
          data.loading = false
        })
    }
    // 删除教师账号
    const deleteNews = (record: any) => {
      const params = {
        action: 'del',
        id: record.id
      }
      data.loading = true
      delNews(params)
        .then(() => {
          message.success('删除成功！')
          data.loading = false
          // 刷新
          getNewsList()
        })
        .finally(() => {
          data.loading = false
        })
    }

    onMounted(() => {
      getNewsList()
    })
    return {
      data,
      columns,
      rowSelection,
      openDrawer,
      handleCancelDrawer,
      deleteNews
    }
  }
})
</script>

<style lang="less">
.account-manage {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-btn {
    font-size: 14px;
  }
}
</style>
<style lang="less" scoped>
.account-manage-header {
  display: flex;
  padding-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  .btn-group {
    .ant-btn-primary {
      margin-right: 10px;
    }
  }
}
</style>
