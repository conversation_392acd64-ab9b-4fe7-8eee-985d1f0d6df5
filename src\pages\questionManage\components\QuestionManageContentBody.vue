<script lang="ts" setup>
import type { LanguageFunc } from '@/models/questionModel'
import { alltags, draftquesbyid, getCategory, ojtemplate, subjcatgs } from '@/api/admin/questionManage'
import { getAiAnswer } from '@/api/exam/index'
import FoldText from '@/components/FoldText.vue'
import JSwitch from '@/components/JSwitch.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import languageFunctions from '@/config/languageFunctions'
import { QuestionEnum, questionType } from '@/models/questionModel'
import SetKeyword from '@/pages/questionManage/components/SetKeyword.vue'
import OptionGenerator from '@/pages/questionManage/QuestionForm/components/OptionGenerator.vue'
import { hasImageOrFormula, uuid } from '@/utils'
import emitter from '@/utils/bus'
import { CloseOutlined, InfoCircleFilled, StarFilled } from '@ant-design/icons-vue'
import { useDraggable, useEventListener, useWindowSize } from '@vueuse/core'
import { Form, message, Modal } from 'ant-design-vue'
import { computed, createVNode, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'

import { useStore } from 'vuex'
import AiAideModal from '../../components/AiAideModal.vue'
import BlankFilling from '../../components/BlankFilling.vue'
import JudgingQuestions from '../../components/JudgingQuestions.vue'
import MultipleChoice from '../../components/MultipleChoice.vue'
import OrderQuestion from '../../components/OrderQuestion.vue'

import QuestionAndAnswer from '../../components/QuestionAndAnswer.vue'
import ScoreInput2 from '../../components/ScoreInput2.vue'
import ScoreInput from '../../components/ScoreInput.vue'
import SingleChoice from '../../components/SingleChoice.vue'
import {
  judge,
  lettersAlpht,
  options,
  questionsObj,
  replaceFields,
} from '../../data'
import useAlgorithm from '../../hooks/useAlgorithm'
import useCompose from '../../hooks/useCompose'
import useGetPoints from '../../hooks/useGetPoints'
import useGetScore from '../../hooks/useGetScore'
import useOption from '../../hooks/useOption'
import useSubmitQuestion from '../../hooks/useSubmitQuestion'
import useWatchBody from '../../hooks/useWatchBody'
import useWatchParameters from '../../hooks/useWatchParameters'
import useWatchType from '../../hooks/useWatchType'
import {
  CHECK_VARIATE_NAME_REG,
  checkParameters,
} from '../../rules'
import { useAlgo, useFillBlank, useQuestionManage } from '../composable/index'

import ALGORITHM_CODING from '../QuestionSpec/ALGORITHM_CODING.vue'
import FILL_BLOCK from '../QuestionSpec/FILL_BLOCK.vue'
import MULTIPLE_CHOICE from '../QuestionSpec/MULTIPLE_CHOICE.vue'
import SHORT_ANSWER from '../QuestionSpec/SHORT_ANSWER.vue'
import SINGLE_CHOICE from '../QuestionSpec/SINGLE_CHOICE.vue'
import SORT_ORDER from '../QuestionSpec/SORT_ORDER.vue'
import TRUE_OR_FALSE from '../QuestionSpec/TRUE_OR_FALSE.vue'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import 'vue-json-pretty/lib/styles.css'

const props = defineProps<{
  draftId?: any
  subjectId?: string // 所属题库、
  subjectUUID?: string // 所属题库、
}>()

const emits = defineEmits<{
  (e: 'close', needRefresh: boolean): void
}>()

const disalbedBth = ref(false)
provide('ImageAlignActionHook', {
  before: () => {
    disalbedBth.value = true
  },
  after: () => {
    disalbedBth.value = false
  },
})

// 对浏览器自带的返回按钮的处理
onBeforeRouteLeave(async (to, from, next) => {
  if (!isSubmit.value) {
    await showConfirmExitModal()
    next()
  }
  else {
    next()
  }
})

useEventListener('beforeunload', (event) => {
  if (!isSubmit.value) {
    event.preventDefault()
  }
})

// 询问用户是否确认退出编辑
function showConfirmExitModal() {
  return new Promise((resolve, reject) => {
    if (!formState.value.body.trim()) {
      Modal.confirm({
        title: '确定退出此页面？',
        icon: () => createVNode(InfoCircleFilled),
        content: '系统可能不会保存您所做的更改',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          resolve('')
        },
      })
    }
    else {
      const modal = Modal.confirm({
        title: '是否需要保存草稿箱？',
        content: '直接退出系统不会保存当前内容',
        okText: '保存草稿箱',
        cancelText: '直接退出',
        closable: true,
        closeIcon: '取消',
        class: 'draft-cancel',
        async onOk() {
          await handleSaveDraft()
          resolve('')
        },
        onCancel(e) {
          if (!e.triggerCancel) {
            modal.destroy()
            resolve('')
          }
        },
      })
    }
  })
}

// 关闭事件
async function handleCancel() {
  if (!isSubmit.value) {
    await showConfirmExitModal()
    emits('close', true) // 保存草稿弹框后，如果是草稿箱列表需要刷新（处理了true），题目列表不需要刷新（父组件没处理）
  }
  else {
    emits('close', false)
  }
}

const useForm = Form.useForm

const categoryList = ref<any>([])
const disabledKeywords = ref(false)

const defaultOptions = [
  {
    content: '',
    value: 'A',
  },
  {
    content: '',
    value: 'B',
  },
  {
    content: '',
    value: 'C',
  },
  {
    content: '',
    value: 'D',
  },
]

const { formState, subQuestionList, createQuestionFormRef, scorepts, scoreInputTotalRef, rules, clearFormState } = useQuestionManage()

const scoreInputRef = ref()
const isSubmit = ref(false) // 表单是否发生变化， 否是有, 有变化返回上一页就会给提示

const { checkSubQuestionList, addCompoundQuestions, handleDelete, handleUp, handleDown }
  = useCompose(subQuestionList)
// 校验规则

const { validate, validateInfos } = useForm(formState, rules)

// 判断方法名是否是禁用关键词中的一个
function isFnNameEqualKeyword(keywords: string[], fnName: string) {
  return keywords.includes?.(fnName)
}

const store = useStore()
const route = useRoute()
async function getSubjectData() {
  try {
    let res = await getCategory({ path: route.query.path })
    categoryList.value = res
    console.log('this is categoryList', categoryList.value)
    // 编辑草稿
    if (props.draftId) 
      return
    
    await nextTick()
    const flag = props.subjectId ? findSubjectId(props.subjectId, categoryList.value) : false
    formState.value.category = flag ? props.subjectUUID : undefined
  }
  catch (error) {
    console.log(error)
  }
}

// 监听题库切换
// watch(
//   () => formState.value.category,
//   (val) => {
//     const flag = findSubjectId(val, categoryList.value)
//     formState.value.category = flag ? val : undefined
//   }
// )

function findSubjectId(id: string, subjectTree: any) {
  let result = false
  for (let i = 0; i < subjectTree.length; i++) {
    const item = subjectTree[i]
    if (item.id === id) {
      return true // 如果找到了匹配的id，立即返回true
    }
    else if (item.children && item.children.length) {
      // 如果有子节点，则递归查找
      result = findSubjectId(id, item.children)
      if (result) {
        return true // 如果子节点中找到了匹配的id，立即返回true
      }
    }
  }
  return result
}

const { fillBlank, fillBlankCount } = useFillBlank()
const { validateAlgorithmRef, activeTab, varType, funcMainObj, funcMainParams, funcTemplateObj } = useAlgo()
let { oldFillBlank } = useFillBlank()
const { getScore, getFillBlankScore, getPointsScore } = useGetScore(
  formState,
  fillBlank,
  scorepts,
  scoreInputTotalRef,
)

// 自动保存至草稿箱
// const { uuid, realtime } = useSaveDraft(formState)

function clearValidateAlgorithm() {
  funcTemplateObj.value = {
    c: '',
    cpp: '',
    java: '',
    js: '',
    python: '',
    go: '',
  }
  Object.keys(validateAlgorithmRef.value.langMap).forEach((key) => {
    validateAlgorithmRef.value.langMap[key].showTip = false
  })
}
const { loading, addLoading, saveAdd, saveDraft } = useSubmitQuestion(
  formState,
  subQuestionList,
  createQuestionFormRef,
  isSubmit,
  scorepts,
  fillBlank,
  fillBlankCount,
  oldFillBlank,
  scoreInputTotalRef,
  clearValidateAlgorithm,
  disabledKeywords,
  activeTab,
  props,
  emits,
)

// 保存并新增
async function handleSaveAdd() {
  addLoading.value = true
  const res
    = disabledKeywords.value
      && (!Object.values(formState.value.disablekws).flat().length || isFnNameEqualKeyword(Object.values(formState.value.disablekws).flat(), formState.value.func_name))
  if (res) {
    // 高级录入
    if (activeTab.value === '2') {
      message.error('题目表单校验失败,请检查输入项内容')
    }
    addLoading.value = false
    return
  }
  try {
    // 如果算法题不存在禁用关键词，则将禁用关键词清空
    if (!disabledKeywords.value && formState.value.type == QuestionEnum['算法题']) {
      Object.keys(formState.value.disablekws).forEach(key => formState.value.disablekws[key] = [])
    }
    let cb = formState.value.complicatedediting ? handleComplexContentBlur : handleBodyBlur
    await saveAdd(cb, validateAlgorithmRef, clearAiAnswer)

    if (props.draftId) {
      // 如果是草稿箱的发布需要通知更新试题列表（虽然次数不一定是在提交成功后才会执行，但是之前的代码太乱，暂时先这样，多执行一次也不坏）
      emitter.emit('updateQuestionList')
    }
  }
  catch (error) {
    console.log(error)
  }
}

async function handleSaveDraft() {
  if (!formState.value.body.trim()) {
    message.error('题干内容不能为空')
    return
  }
  try {
    if (!disabledKeywords.value && formState.value.type == QuestionEnum['算法题']) {
      Object.keys(formState.value.disablekws).forEach(key => formState.value.disablekws[key] = [])
    }
    await saveDraft(validateAlgorithmRef, clearAiAnswer)
  }
  catch (error) {
    console.log(error)
  }
}

const { addOption, delOption } = useOption(formState)
const { addPoints, delPoints } = useGetPoints(formState, scorepts, scoreInputTotalRef)
function delOptionWrap(a, b) {
  delOption(a, b)
  createQuestionFormRef.value.validateFields(['options'])
}
useWatchBody(formState, fillBlank, fillBlankCount, oldFillBlank)

watch(
  () => formState.value.sepscore,
  (val) => {
    let totalScore = 0
    if (val) {
      if (formState.value.type === 5) {
        fillBlank.value.forEach((item: any) => {
          totalScore += Number(item.score)
          formState.value.score = totalScore
        })
      }
    }
  },
)

useWatchParameters(formState)

watch(
  () => subQuestionList,
  (val) => {
    formState.value.score = 0
    val.value.forEach((item: any) => {
      formState.value.score += Number(item.formState.score || 0)
    })
  },
  {
    deep: true,
  },
)

provide('funcTemplateObj', funcTemplateObj)
// 监听方法名称、返回值类型、参数列表的变化，获取oj模板
watch([() => formState.value.func_name, () => formState.value.rtype, () => formState.value.parameters], async ([func_name, rtype, parameters]) => {
  // 有效的方法名称
  const validFuncname = CHECK_VARIATE_NAME_REG.test(formState.value.func_name) && !isFnNameEqualKeyword(Object.values(formState.value.disablekws).flat(), formState.value.func_name)
  // 有效的返回值类型
  const validRtype = !!formState.value.rtype.type
  // 有效的参数列表
  let validParameters
  try {
    await checkParameters({}, formState.value.parameters)
    validParameters = true
  }
  catch (error) {
    validParameters = false
  }
  if (validFuncname && validRtype && validParameters) {
    getOjTemplate()
  }
}, {
  deep: true,
  immediate: true,
})

/** 获取oj模板 */
async function getOjTemplate() {
  funcMainParams.value = {
    funcName: formState.value.func_name,
    rtype: JSON.stringify(formState.value.rtype),
    parameters: JSON.stringify(formState.value.parameters),
  }
  const result: any = await ojtemplate(funcMainParams.value)
  funcMainObj.value = result.func_main
  funcTemplateObj.value = pasteValidatecode || result.func_templete
  pasteValidatecode = null
}

watch(
  () => formState.value.validatecode,
  (val) => {
    if (activeTab.value === '2' && !pasteValidatecode) {
      nextTick(() => {
        funcTemplateObj.value = val
      })
    }
  },
  {
    deep: true,
  },
)

async function updateTemplate(lang: string, item: any) {
  const result: any = await ojtemplate({
    func_name: formState.value.func_name,
    rtype: formState.value.rtype,
    parameters: formState.value.parameters,
  })
  funcTemplateObj.value[item] = JSON.parse(JSON.stringify(result.func_templete[item]))
  validateAlgorithmRef.value.langMap[lang].counter++
}

const jsonEditorRef = ref(null)
const showJsonEditor = ref(true)
watch(
  formState,
  () => {
    if (loadedFinish.value) {
      isSubmit.value = false
    }
    if (jsonEditorRef.value) {
      showJsonEditor.value = false
      nextTick(() => {
        showJsonEditor.value = true
      })
    }
  },
  { deep: true },
)

useWatchType(formState, scoreInputTotalRef, options, judge)

function handleBodyBlur() {
  return validate('body', { trigger: 'blur' })
}
function handleComplexContentBlur() {
  return validate('complexcontent', { trigger: 'blur' })
}
function handleOptionBlur() {
  createQuestionFormRef.value.validateFields(['options'])
}

const tagsList = ref<any>([])
const tagsOptions = ref<any>([])
function getAllTags() {
  alltags({ tag: 'question' /** categoryId: props.subjectId */ }).then((res: any) => {
    tagsList.value = res
    tagsOptions.value = res.map((item: any) => ({ value: item.name, label: item.name }))
  })
}

const subQuestionsRef = ref()
function handleComplexSwitchChange(formState: any, value: boolean, index?: number) {
  if (!value) {
    try {
      handleComplexToNormal(formState)
      if (subQuestionsRef.value?.length && index !== undefined) {
        subQuestionsRef.value[index].handleComplexToNormal?.(formState)
      }
    }
    catch (error) {
      console.log(error)
      message.error('出错了，请刷新重试')
    }
  }
  else {
    formState.complexcontent = formState.body.replaceAll(/</g, '&lt;').replaceAll(/>/g, '&gt;').split('\n').reduce((acc: string, cur: string) => acc += `<p>${cur}</p>`)
  }
}

const scorebasisEditorRef = ref()
const optionEditorsRef = ref()
function handleComplexToNormal(formState: any) {
  if (optionEditorsRef.value && optionEditorsRef.value.length) {
    formState.options.forEach((option: any, index: number) => {
      option.content = optionEditorsRef.value[index].getText()
    })
  }
  if (scorebasisEditorRef.value) {
    formState.scorebasis = scorebasisEditorRef.value.getText()
  }
}
function convertFieldJsonContent(data, toJson = false) {
  const defaultKeys = ['options', 'disablekws', 'funcMain', 'funcTemplate', 'parameters', 'ptestcase', 'rtype', 'btestcase', 'validatecode']
  defaultKeys.forEach((key) => {
    if (toJson) {
      if (data[key]) {
        const _temp = data[key]
        data[key] = JSON.stringify(_temp)
        data[`${key}Json`] = _temp
      }
    }
    else {
      if (data[`${key}Json`]) {
        data[`${key}`] = data[`${key}Json`]
      }
    }
  })
  data.func_name = data.funcName 
  data.func_templete = data.funcTemplete
}
function convertBoolean(data) {
  Object.keys(data).forEach((key) => {
    if (typeof data[key] === 'boolean') {
      data[key] = data[key] ? 1 : 0
    }
  })
}

// 查看草稿内容
const loadedFinish = ref(false) // 是否回填完草稿箱详情数据，回填之后formstate变化isSubmit才会变化
function getDraftDetail() {
  if (props.draftId) {
    draftquesbyid({ id: props.draftId }).then((res: any) => {
      convertFieldJsonContent(res.questionBankWrite)
      convertBoolean(res.questionBankWrite)

      // res.questionBankWrite.categoryUUID = res.questionBankWrite.category
      res.questionBankWrite.tags = res.questionBankWrite.tagList ? res.questionBankWrite.tagList.filter(item => item != '[]') : []
      pasteValidatecode = res.questionBankWrite.validatecode

      formState.value = res.questionBankWrite
      const score = res.questionBankWrite.score
      if (formState.value.type == QuestionEnum['算法题']) {
        disabledKeywords.value = Boolean(Object.values(formState.value.disablekws).flat().length)
      }
      setTimeout(() => {
        scoreInputTotalRef.value.score = score
        formState.value.options = res.questionBankWrite.options
        formState.value.answer = res.questionBankWrite.answer
        if (formState.value.type === 5) {
          let answer = JSON.parse(res.questionBankWrite.answer)
          answer.forEach((item: any) => {
            item.id = uuid()
          })
          fillBlank.value = answer
        }
        formState.value.type === 3 && (scorepts.value = JSON.parse(res.questionBankWrite.answer))
        // funcTemplateObj.value = formState.value.validatecode
        nextTick(() => {
          // loadedFinish.value = true
          const flag = findSubjectId(res.category, categoryList.value)
          formState.value.category = flag ? res.category : undefined
          if (formState.value.type === 4) {
            delete formState.value.options
          }
          nextTick(() => {
            loadedFinish.value = true
          })
          formState.value.category = formState.value.categoryUUID
        })
      }, 700)
      // nextTick(() => {})
    })
  }
  else {
    loadedFinish.value = true
  }
}

let pasteValidatecode: any = null
function pasteHandler(event: Event) {
  // @ts-ignore
  const clipboardData = event.clipboardData || window.clipboardData
  const pastedText = clipboardData.getData('text')
  if (pastedText.includes('validatecode')) {
    const data = JSON.parse(pastedText)
    if (data.validatecode) {
      pasteValidatecode = data.validatecode
    }
  }
}

// 请求题库树后再请求草稿详情
getSubjectData().then(getDraftDetail)

onMounted(() => {
  getAllTags()
  isSubmit.value = true
  document.addEventListener('paste', pasteHandler)
})

onBeforeUnmount(() => {
  document.removeEventListener('paste', pasteHandler)
})

// 题型切换 
function handleTypeChange(type: any): any {
  clearAiAnswer()
  if ([3, 5].includes(type)) {
    formState.value.sepscore = true
  }
  setTimeout(calcTotalScore, 1000)
}

// 计算题目总分
function calcTotalScore() {
  if (formState.value.type === 3) {
    formState.value.score = scorepts.value.reduce((acc: number, cur: any) => acc + cur.score, 0)
  }
  else if (formState.value.type === 5) {
    formState.value.score = fillBlank.value.reduce((acc: number, cur: any) => acc + cur.score, 0)
  }
}

// 插入空位
const bodyInputRef = ref()
const bodyEditorRef = ref<InstanceType<typeof VueQuillEditor>>()
function insetBlank() {
  let TEXT = '__ '
  if (formState.value.complicatedediting) {
    bodyEditorRef.value?.insetText(TEXT)
  }
  else {
    let textareaEle = bodyInputRef.value.$el
    const startPos = textareaEle.selectionStart
    const endPos = textareaEle.selectionEnd
    let rawText = formState.value.body
    formState.value.body = rawText.substring(0, startPos) + TEXT + rawText.substring(endPos)
  }
}

// 添加关键词
const setKeywordVisible = ref<boolean>(false)
const addKeyword = () => setKeywordVisible.value = true
function handleSetKeywordOk(data: LanguageFunc) {
  formState.value.disablekws = data
  setKeywordVisible.value = false
}

// —————————————————————————————————————————————————— AI 生成干扰项 ———————————————————————————————————————————————————————
const generateOptionModalVisible = ref(false)
const generateParams = ref()
const optionGeneratorRef = ref<InstanceType<typeof OptionGenerator>>()
function showGenerateOptionModal() {
  let type = formState.value.type
  let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  let options = formState.value.options.map((option: any, index: number) => ({ content: option.content, isTrue: formState.value.answer?.includes(option.value) })).filter((i: any) => !!i.content)
  if (![QuestionEnum['单选题'], QuestionEnum['多选题']].includes(type)) {
    return message.error('只有单选题和多选题才能生成干扰项')
  }
  if (!body) {
    return message.error('题干内容不能为空')
  }
  if (options.filter((i: any) => i.isTrue).length === 0) {
    return message.error('使用该功能需要先输入正确的选项，如果您已经输入了正确的选项，请勾选它。')
  }
  if (options.some((i: any) => hasImageOrFormula(i.content))) {
    return message.error('很抱歉，由于AI能力限制，暂不支持基于公式或图片的干扰项生成。')
  }
  // 剔除所有选项的所有标签，将纯文本提交
  options = options.map((option: any, index: number) => {
    let content = formState.value.complicatedediting ? optionEditorsRef.value[index].getText() : option.content
    return { content, ...option }
  })
  generateParams.value = { type, body, answer: formState.value.answer, optionJson: options }
  generateOptionModalVisible.value = true
}

function handleGenerateOptionsChoose() {
  let arr = optionGeneratorRef.value?.list.filter(i => i.checked)
  if (!arr?.length) 
    return message.error('请至少选择一个干扰项')
  let options = formState.value.options
  let arrIndex = 0; let optionIndex = 0
  while (optionIndex < options.length && arrIndex < arr.length) {
    if (options[optionIndex].content.trim() === '') {
      options[optionIndex].content = arr[arrIndex].content
      arrIndex++
      continue
    }
    optionIndex++
  }
  let restOptionNum = 11 - options.length // 最多可添加11个
  // 如果arr剩下的比option剩下还多，就提示
  if (restOptionNum < arr.length - arrIndex) {
    message.warning('已达最多选项个数')
  }
  
  // 如果有多余干扰项，则直接添加到选项末尾
  if (arrIndex < arr.length && restOptionNum > 0) {
    options.push(...arr.slice(arrIndex, arrIndex + restOptionNum).map((i, k) => ({ content: i.content, value: lettersAlpht[options.length + k] })))
  }
  generateOptionModalVisible.value = false
}

// —————————————————————————————————————————————————— AI 答题 ———————————————————————————————————————————————————————
const aiAnswer = ref('')
const aiExplain = ref('')
const aiResult = ref<any>(null)
const getAiAnswerLoading = ref(false)
const getaianswerInput = computed(() => {
  let type = formState.value.type
  let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  let options = formState.value.options
  return { body, type, options }
})
function clearAiAnswer() {
  aiAnswer.value = ''
  aiExplain.value = ''
  aiResult.value = null
}
async function handleGetAiAnswer() {
  clearAiAnswer()
  let type = formState.value.type
  let body = formState.value.complicatedediting ? formState.value.complexcontent : formState.value.body
  if (!body) 
    return message.error('题干内容不能为空')
  if (!formState.value.options?.filter((i: any) => i.content.trim())?.length && [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['排序题']].includes(type)) 
    return message.error('选项内容不能都为空')
  getAiAnswerLoading.value = true
  try {
    let res: any = await getAiAnswer({ body, type, options: formState.value.options })
    aiResult.value = res.results
    // aiAnswer.value = res.answer
    // aiExplain.value = res.explain
  }
  catch (error) {
    console.log(error)
  }
  finally {
    getAiAnswerLoading.value = false
  }
}

// ai创建题目悬浮球，可拖拽
const aiAideVisible = ref(false)
const aiAideRef = ref<HTMLElement | null>(null)
let dragStartTimestamp = 0
const { x, y, style: aiCreateCircleStyle } = useDraggable(aiAideRef, {
  initialValue: { x: window.innerWidth - 200, y: 240 },
  onMove(position, event) {
    x.value = Math.max(0, Math.min(position.x, windowWidth.value - 80))
    y.value = Math.max(0, Math.min(position.y, windowHeight.value - 80))
  },
  preventDefault: true,
  onStart() {
    dragStartTimestamp = Date.now()
  },
  onEnd() {
    let dragDuration = Date.now() - dragStartTimestamp
    if (dragDuration <= 200) {
      // 鼠标松开和按下间隔200ms以内认为是点击，打开弹窗
      aiAideVisible.value = true
    }
  },
})

const { width: windowWidth, height: windowHeight } = useWindowSize()

watch([windowWidth, windowHeight], ([w, h]) => {
  // 0 ~ w - 80
  // 0 ~ h - 80
  x.value = Math.min(x.value, w - 80)
  y.value = Math.min(y.value, h - 80)
})

function handleAiCreated(value: any) { // ai生成题目，填充信息
  aiAideVisible.value = false
  formState.value = {
    ...formState.value,
    body: value?.body || '',
    complexcontent: formState.value.complicatedediting ? `<p>${value?.body}</p>` : '',
    answer: value?.answer || '',
    options: Array.isArray(value?.options) ? value.options : defaultOptions,
    scorebasis: value?.scorebasis || '',
  }
  if ([3, 5].includes(formState.value.type)) {
    let answer = []
    try {
      answer = typeof value?.answer == 'string' ? JSON.parse(value?.answer) : value?.answer
      answer = Array.isArray(answer) ? answer : []
    }
    catch (error) {
      // do nothing
    }
    let totalScore = 0
    answer.forEach((item: any) => {
      item.id = uuid()
      totalScore += item.score
    })
    formState.value.score = totalScore
    if (formState.value.type === 5) {
      fillBlankCount.value = answer.length
      fillBlank.value = answer
    }
    if (formState.value.type === 3) {
      scorepts.value = answer
    }
  }
}
const CompMap = {
  SINGLE_CHOICE,
  MULTIPLE_CHOICE,
  TRUE_OR_FALSE,
  FILL_BLOCK,
  SORT_ORDER,
  SHORT_ANSWER,
  ALGORITHM_CODING,
}
</script>

<template>
  <div class="create-question-container">
    <CloseOutlined class="close-btn" @click="handleCancel" />
    <div class="create-question-content">
      <a-form
        ref="createQuestionFormRef"
        class="form"
        :model="formState"
        :hide-required-mark="true"
        :rules="rules"
        :colon="false"
        label-align="left"
      >
        <!-- 头部操作栏 -->
        <a-row :gutter="44">
          <a-col :span="8">
            <a-form-item label="题目类型" name="type">
              <!-- :disabled="props.draftId" -->
              <a-select
                v-model:value="formState.type"
                disabled
                placeholder="请选择"
                @select="handleTypeChange"
              >
                <a-select-option :value="questionType.SINGLE_CHOICE">
                  单选题 
                </a-select-option>
                <a-select-option :value="questionType.MULTIPLE_CHOICE">
                  多选题
                </a-select-option>
                <a-select-option :value="questionType.TRUE_OR_FALSE">
                  判断题
                </a-select-option>
                <a-select-option :value="questionType.FILL_BLOCK">
                  填空题
                </a-select-option>
                <a-select-option :value="questionType.SHORT_ANSWER">
                  问答题
                </a-select-option>
                <a-select-option :value="questionType.AlGORITHM_CODING">
                  算法题
                </a-select-option>
                <a-select-option :value="questionType.SORT_ORDER">
                  排序题
                </a-select-option>
                <!-- <a-select-option :value="questionType.COMPOSITE">复合题</a-select-option> -->
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所属题库" name="category">
              <a-tree-select
                v-model:value="formState.category"
                show-search
                :tree-data="categoryList"
                placeholder="添加题库"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                allow-clear
                :field-names="replaceFields"
                tree-node-filter-prop="name"
                :virtual="false"
                :get-popup-container="
                  (triggerNode: any) => {
                    return triggerNode.parentNode
                  }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4" style="min-width: 180px">
            <a-form-item label="建议时间" name="score">
              <ScoreInput
                v-if="formState.type !== 7"
                ref="scoreInputTotalRef"
                v-model="formState.score"
                :disabled="
                  ((formState.type === 5 || formState.type === 3) && formState.sepscore)
                    || formState.type === 7
                "
                :type="formState.type"
                @get-score="getScore"
              />
              <ScoreInput2
                v-else
                ref="scoreInputTotalRef"
                v-model="formState.score"
                :disabled="
                  ((formState.type === 5 || formState.type === 3) && formState.sepscore)
                    || formState.type === 7
                "
                :type="formState.type"
                @get-score="getScore"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item>
              <template #label>
                <span>复杂编辑</span>
                <a-tooltip placement="right" overlay-class-name="light">
                  <template #title>
                    <span>开启此功能后，可以在文本中插入图片和公式，并对文本进行特殊样式编辑</span>
                  </template>
                  <svg-icon class="common-info-icon" name="info2" style="width: 54%;height: 54%;" />
                </a-tooltip>
              </template>
              <JSwitch
                v-model:checked="formState.complicatedediting"
                @change="handleComplexSwitchChange(formState, $event)"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="44">
          <a-col :span="8">
            <a-form-item label="深度" name="category">
              <a-tree-select
                v-model:value="formState.category"
                show-search
                :tree-data="categoryList"
                placeholder="添加题库"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                allow-clear
                :field-names="replaceFields"
                tree-node-filter-prop="name"
                :virtual="false"
                :get-popup-container="
                  (triggerNode: any) => {
                    return triggerNode.parentNode
                  }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="偏度" name="category">
              <a-tree-select
                v-model:value="formState.category"
                show-search
                :tree-data="categoryList"
                placeholder="添加题库"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                allow-clear
                :field-names="replaceFields"
                tree-node-filter-prop="name"
                :virtual="false"
                :get-popup-container="
                  (triggerNode: any) => {
                    return triggerNode.parentNode
                  }"
              />
            </a-form-item>
          </a-col>

          <a-col :span="4">
            <a-form-item label="难度">
              <StarFilled style="color:#FAAD14" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 题干内容 -->

        <!-- 编程 -->
        <a-tabs v-if="formState.type === 4" v-model:active-key="activeTab" type="card">
          <a-tab-pane key="1" tab="普通录入" />
          <a-tab-pane key="2" tab="高级录入" />
        </a-tabs>

        <div v-show="activeTab === '1'">
          <a-form-item
            v-if="!formState.complicatedediting"
            label="题干内容"
            v-bind="validateInfos.body"
          >
            <a-textarea
              ref="bodyInputRef"
              v-model:value="formState.body"
              :rows="4"
              placeholder="点击编辑"
              @blur="handleBodyBlur"
            />
            <div v-if="formState.type === 5" class="body-tip">
              <svg-icon name="tip" class="tip-icon" />
              <span>点击</span>
              <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
              <span>连续输入两个下划线"__"可增加空位</span>
            </div>
          </a-form-item>
          <a-form-item v-else label="题干内容" v-bind="validateInfos.complexcontent">
            <VueQuillEditor
              ref="bodyEditorRef"
              v-model:text="formState.body"
              v-model:content="formState.complexcontent"
              @blur="handleComplexContentBlur"
            />
            <div v-if="formState.type === 5" class="body-tip">
              <svg-icon name="tip" class="tip-icon" />
              <span>点击</span>
              <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
              <span>连续输入两个下划线"__"可增加空位</span>
            </div>
          </a-form-item>
        </div>

        <a-form-item
          v-if="!formState.complicatedediting"
          label="AI朗读"
          v-bind="validateInfos.body"
        >
          <a-textarea
            ref="bodyInputRef"
            v-model:value="formState.body"
            :rows="4"
            placeholder="点击编辑"
            @blur="handleBodyBlur"
          />
          <div v-if="formState.type === 5" class="body-tip">
            <svg-icon name="tip" class="tip-icon" />
            <span>点击</span>
            <span style="color: #5478ee;cursor: pointer;" @click="insetBlank">插入空位</span>
            <span>连续输入两个下划线"__"可增加空位</span>
          </div>
        </a-form-item>

        <!-- 添加复合题 -->
        <a-collapse v-if="formState.type == 7 && subQuestionList.length" :accordion="false">
          <template v-for="(item, index) in subQuestionList" :key="index">
            <a-collapse-panel :header="`【${item.label}】 ${item.formState.body}`">
              <template #extra>
                <span class="complex-switch-box" @click.stop>
                  <span class="label">复杂编辑</span>
                  <JSwitch
                    v-model:checked="item.formState.complicatedediting"
                    @change="handleComplexSwitchChange(item.formState, $event, index)"
                  />
                </span>
                <img src="@/assets/images/delete.png" alt="删除" @click="handleDelete(index)">
                <img
                  v-if="index !== 0"
                  style="margin-left: 16px"
                  src="@/assets/icons/svg/compoundUp.svg"
                  alt="上移"
                  @click="handleUp($event, index)"
                >
                <img
                  v-if="index !== subQuestionList.length - 1"
                  style="margin-left: 16px"
                  src="@/assets/icons/svg/compoundDown.svg"
                  alt="下移"
                  @click="handleDown(index)"
                >
              </template>
              <template v-if="item.type == 0">
                <SingleChoice ref="subQuestionsRef" v-model="item.formState" />
              </template>
              <template v-else-if="item.type == 1">
                <MultipleChoice ref="subQuestionsRef" v-model="item.formState" />
              </template>
              <template v-else-if="item.type == 2">
                <JudgingQuestions
                  ref="subQuestionsRef"
                  v-model="item.formState"
                />
              </template>
              <template v-else-if="item.type == 3">
                <QuestionAndAnswer
                  ref="subQuestionsRef"
                  v-model="item.formState"
                />
              </template>
              <template v-else-if="item.type == 5">
                <BlankFilling ref="subQuestionsRef" v-model="item.formState" />
              </template>
              <template v-else-if="item.type == 6">
                <OrderQuestion ref="subQuestionsRef" v-model="item.formState" />
              </template>
            </a-collapse-panel>
          </template>
        </a-collapse>

        <!-- 单组件抽离component分发 -->
        <component :is="CompMap[questionType[formState.type] as keyof typeof CompMap]" :show-generate-option-modal="showGenerateOptionModal" :show-json-editor="showJsonEditor" />
        
        <!-- 复合题 -->
        <a-form-item
          v-if="formState.type == 7"
          class="question-options compound"
          name="subQuestionList"
        >
          <a-radio-group v-model:value="formState.answer">
            <a-dropdown trigger="click" class="compoundQuestions">
              <template #overlay>
                <a-menu class="compoundItem">
                  <template v-for="item in questionsObj" :key="item.key">
                    <a-menu-item @click="addCompoundQuestions(item)">
                      {{
                        item.questionCate
                      }}
                    </a-menu-item>
                  </template>
                </a-menu>
              </template>
              <!-- <img src="@/assets/images/add.png" alt=""> -->
              <a-button class="compoundQuestionsBtn flex items-center" type="text">
                <img src="@/assets/icons/svg/addItem.svg" alt="请选择子试题" class="addItem mr-[3px] mt-[2px]">
                请选择子试题
              </a-button>
            </a-dropdown>
          </a-radio-group>
        </a-form-item>

        <template v-if="formState.type === 4">
          <div v-show="activeTab === '1'" class="fill-blank-config">
            <div style="margin-bottom: 6px">
              <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
              <span>本题适用于“部分测试用例通过可得分”</span>
              <a-tooltip placement="right">
                <template #title>
                  <span>若勾选“部分测试用例通过可得分”，则算法题得分按通过测试用例占总数的百分比计算；未勾选时，需全部通过测试用例才能得分。</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
            </div>
            <div style="margin-bottom: 4px">
              <a-checkbox v-model:checked="disabledKeywords" class="check-box" />
              <span>存在禁用关键词</span>
              <a-tooltip placement="right">
                <template #title>
                  <span>禁止学生答题时使用一些系统内置的方法或关键字</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" />
              </a-tooltip>
              <div v-if="disabledKeywords" class="set-keyword-btn" @click="addKeyword">
                批量设置
              </div>
            </div>
            <a-form-item
              v-if="disabledKeywords"
              label="禁用关键词"
              class="disablekws-wrapper"
            >
              <template v-for="item in Object.keys(formState.disablekws)" :key="item">
                <div class="item">
                  <span class="label">{{ item }}</span>
                  <a-select
                    v-model:value="formState.disablekws[item]"
                    class="tags-select"
                    mode="tags"
                    placeholder="输入完成后按回车添加多个关键词"
                    :get-popup-container="
                      (triggerNode: any) => {
                        return triggerNode.parentNode
                      }"
                  >
                    <a-select-opt-group
                      v-for="group in languageFunctions.find(el => el.language === item)?.groups"
                      :key="group.key"
                      :label="group.label"
                    >
                      <a-select-option
                        v-for="func in group.functions"
                        :key="`${group.key}#${func.funcname}`"
                        :value="func.funcname"
                      >
                        {{ func.funcname }}
                      </a-select-option>
                    </a-select-opt-group>
                  </a-select>
                </div>
              </template>
              <div v-if="!Object.values(formState.disablekws).flat().length" class="err-tip">
                请输入禁用关键词
              </div>
              <div
                v-if="
                  isFnNameEqualKeyword(
                    Object.values(formState.disablekws).flat(),
                    formState.func_name,
                  )
                "
                class="err-tip"
              >
                方法名称含有禁用关键词，请修改方法名
              </div>
            </a-form-item>
          </div>
        </template>

        <a-form-item v-show="activeTab === '1'" name="tags" style="width: 220px">
          <a-select
            v-model:value="formState.tags"
            class="tag-select"
            mode="tags"
            :options="tagsOptions"
            placeholder="添加标签"
            dropdown-class-name="tag-select-wrapper"
            :get-popup-container="
              (triggerNode: any) => {
                return triggerNode.parentNode
              }"
          />
        </a-form-item>

        <div v-if="![QuestionEnum['算法题']].includes(formState.type)" style="display: flex;align-items: center;margin-top: 16px;">
          <a-button :loading="getAiAnswerLoading" class="common-ai-button" style="font-size: 14px; margin-left: 2px;border-radius: 8px;" @click="handleGetAiAnswer">
            <template #icon>
              <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;">
            </template>
            答题
          </a-button>
          <a-tooltip placement="right" overlay-class-name="light">
            <template #title>
              <span>自动生成当前题目的答案解析</span>
            </template>
            <svg-icon class="common-info-icon" name="info2" />
          </a-tooltip>
        </div>

        <template v-if="aiAnswer || aiExplain || (aiResult && aiResult.length)">
          <template v-for="({ answer: aiAnswer, explain: aiExplain }) in aiResult" :key="aiAnswer">
            <div class="ai-answer-panel">
              <div>
                <div class="label">
                  AI 答案
                </div>
                <div class="value">
                  {{ aiAnswer || '-' }}
                </div>
              </div>
              <div style="margin-top: 8px;">
                <div class="label">
                  答题解析
                </div>
                <div class="value">
                  <FoldText
                    :text="aiExplain" 
                    :fold-line-clamp="3" 
                    background-color="#f5f5f5"
                    :feedback-params="{
                      apiname: 'getaianswer',
                      input: getaianswerInput,
                      output: {
                        answer: aiAnswer,
                        explain: aiExplain,
                      },
                    }"
                  />
                </div>
              </div>
            </div>
          </template>

          <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
            <template #icon>
              <InfoCircleFilled />
            </template>
            <template #message>
              温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
            </template>
          </a-alert>
        </template>
      </a-form>
      <!-- 底部操作栏 -->
      <div class="footer">
        <a-button class="save-btn" :loading="addLoading" :disabled="disalbedBth" @click="handleSaveAdd">
          {{ props.draftId ? '发布' : '保存' }}
        </a-button>
        <a-button class="submit-btn" :loading="loading" @click="handleSaveDraft">
          保存草稿箱
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </div>
      <div
        v-if="[0, 1, 2, 3, 5].includes(formState.type)"
        ref="aiAideRef"
        class="ai-aide-wrap"
        :style="aiCreateCircleStyle"
      >
        <img
          src="@/assets/images/ai-aide.png"
          class="ai-aide"
          alt=""
        >
        <div class="tip">
          Hi, 点我试试
        </div>
      </div>
    </div>
    <!-- 设置禁用关键词 -->
    <SetKeyword
      :visible="setKeywordVisible"
      :checked-functions="formState.disablekws"
      @close="setKeywordVisible = false"
      @ok="handleSetKeywordOk"
    />
    <a-modal v-model:visible="generateOptionModalVisible" title="生成干扰项" :destroy-on-close="true" :keyboard="false" :mask-closable="false" @ok="handleGenerateOptionsChoose" @cancel="optionGeneratorRef?.setList([])">
      <OptionGenerator ref="optionGeneratorRef" :generate-params="generateParams" />
    </a-modal>
    <!-- ai助手 -->
    <AiAideModal :visible="aiAideVisible" :type="formState.type" @close="aiAideVisible = false" @created="handleAiCreated" />
  </div>
</template>

<style lang="less" scoped>
@import url('./scoped.less');
</style>

<style lang="less">
@import url('./wrap.less');
</style>
