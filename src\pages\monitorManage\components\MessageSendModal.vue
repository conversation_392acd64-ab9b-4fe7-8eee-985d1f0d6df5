<template>
  <a-modal :footer="null" :width="650" @cancel="cancel">
    <div class="message-box">
      <ul>
        <li class="message-item" v-for="item in messageList" @click="modelRef.text = item">
          {{ item }}
        </li>
      </ul>
      <a-form>
        <a-form-item v-bind="validateInfos.text">
          <a-textarea
            class="message-ipt"
            v-model:value="modelRef.text"
            :maxlength="50"
            show-count
          ></a-textarea>
        </a-form-item>
      </a-form>
      <div class="btn-group">
        <span @click="submit">发送</span>
        <span @click="cancel">取消</span>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, useAttrs } from 'vue'
import { sendmsg } from '@/api/exam'
import { Form, message, Modal } from 'ant-design-vue'
const useForm = Form.useForm

const props = defineProps<{
  stupid: string
}>()

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const messageList = ref([
  '请按要求调整手机摄像头位置！手机画面需要包括完整的显示屏、键盘、桌面、草稿纸、双手。',
  '你的手机监控掉线，请尽快扫描二维码连接！',
  '请勿做出与考试无关的动作！',
  '请向摄像头展示你的草稿纸，每页草稿纸的正反面都需要在摄像头中出现并停留4秒。',
  '请调整你的电脑摄像头位置和坐姿，要求展示出你的上半身以及你的身后。',
  '请勿左顾右盼！'
])

const modelRef = reactive({
  text: ''
})

const rulesRef = reactive({
  text: [{ required: true, message: '请输入消息内容' }]
})
const { resetFields, validate, validateInfos, clearValidate } = useForm(modelRef, rulesRef)

async function submit() {
  await validate()
  await sendmsg({
    id: props.stupid,
    message: modelRef.text
  })
  message.success('发送成功')
  resetForm()
  emits('update:visible', false)
}

function cancel() {
  resetForm()
  emits('update:visible', false)
}

function resetForm() {
  resetFields()
  clearValidate()
}
</script>

<style lang="less" scoped>
.message-box {
  .message-item {
    padding-left: 8px;

    // border-radius: 8px;
    border: 1px dashed #969798;
    line-height: 34px;
    font-size: 14px;
    color: #010101;

    & + .message-item {
      margin-top: 8px;
    }

    &:hover {
      background-color: #f1f4fe;
      cursor: pointer;
    }
  }

  .message-ipt {
    border-radius: 8px;
    margin-top: 16px;
    height: 80px;
    :deep(.ant-input) {
      font-size: 14px;
    }
  }
}

.ant-form {
  padding-bottom: 28px;
}

.btn-group {
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    width: 60px;
    line-height: 32px;
    text-align: center;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    cursor: pointer;

    &:first-child {
      color: #fff;
      background: #5478ee;
      margin-right: 8px;
    }
  }
}
</style>
