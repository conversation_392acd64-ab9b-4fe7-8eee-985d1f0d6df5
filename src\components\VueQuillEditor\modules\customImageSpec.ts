import { AlignAction, ImageSpec, ResizeAction } from 'quill-blot-formatter'

class CustomeAlignAction extends AlignAction {
  onUpdate() {
    const customHook = (this.formatter.options as any).customHook
    const beforeAlignActionUpdate = customHook?.onImageAlignBefore
    const afterAlignActionUpdate = customHook?.onImageAlignAfter

    if (beforeAlignActionUpdate) {
      beforeAlignActionUpdate()
    }
    this.formatter.overlay.blur()
    this.formatter.quill.setSelection(null)

    setTimeout(() => {
      afterAlignActionUpdate()
    }, 800)
  }
}
export default class CustomImageSpec extends ImageSpec {
  getActions() {
    return [CustomeAlignAction as any, ResizeAction]
  }
}