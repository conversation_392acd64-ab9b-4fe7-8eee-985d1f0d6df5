<script setup lang="ts">
import type { QuestionModel } from '@/models/questionModel'
import { modifyscore } from '@/api/admin/paperManage'
import CodeBox from '@/components/CodeBox.vue'
import OptionBtn from '@/components/RadioBtn/index.vue'
import { ALGORITHM_DIFFICULTY } from '@/config/constants'
import { QuestionEnum } from '@/models/questionModel'
import { findImageAndPreview, transferStr } from '@/utils/common'
import { computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import OptionsBox from './OptionsBox.vue'

const props = withDefaults(
  defineProps<{
    showType?: boolean // 是否显示题型

    showScore?: boolean // 是否显示分值
    scoreKey?: 'score' | 'new_score'

    showTag?: boolean // 是否展示标签
    showCorrectAnswer?: boolean // 是否展示正确答案
    showQuestionDifficulty?: boolean // 是否显示算法题难度
    showQuestionPointsAndBasis?: boolean // 是否展示问答题得分点
    showQuestionProofreadingRecord?: boolean
    questionDetail: QuestionModel
    print?: boolean // 是否打印预览
    optionLetterType?: 'button' | 'text'
    showDisabledKws?: boolean // 是否展示禁用关键词
    deleted?: boolean // 是否处于删除状态

    showStudentScore?: boolean // 是否展示学生得分
    scoreEditable?: boolean // 是否允许改分
    studentId?: string // 学生id
    paperId?: string // 试卷id
  }>(),
  {
    showType: false,
    showScore: false,
    showTag: false,
    showCorrectAnswer: false,
    showStudentScore: false,
    scoreEditable: false,
    showQuestionDifficulty: false,
    showQuestionPointsAndBasis: false,
    showQuestionProofreadingRecord: false,
    scoreKey: 'score',
    print: false,
    optionLetterType: 'button',
    showDisabledKws: false,
    deleted: false,
  },
)

const emits = defineEmits<{
  (e: 'editScore', score: number): void
}>()

const store = useStore()

function convertJsonField(questionDetail: any): void {
  questionDetail.btestcase = questionDetail.btestcaseJson
  questionDetail.options = questionDetail.optionsJson || questionDetail.options
  questionDetail.disablekws = questionDetail.disablekwsJson
  questionDetail.ptestcase = questionDetail.ptestcaseJson
  questionDetail.rtype = questionDetail.rtypeJson
  questionDetail.parameters = questionDetail.parametersJson
}
convertJsonField(props.questionDetail)

watch(() => props.questionDetail, (newVal) => {
  console.log('newVal', newVal)
  convertJsonField(newVal)
})
// 获取题干文本
function getContent(): string {
  let ans = ''
  if (props.questionDetail.complicatedediting) {
    ans = props.questionDetail.complexcontent || ''
  }
  else {
    ans = transferStr(props.questionDetail.content)
  }
  return ans
}

const hasDisablekws = computed(() => Object.values(props.questionDetail?.disablekws ?? []).flat().length)

function getRandomOptions(options: any[]): any[] {
  if (!props.print)
    return options
  const len = (options && options.length) || 0
  for (let i = 0; i < len - 1; i++) {
    const index = Math.floor(Math.random() * (len - i))
    const temp = options[index]
    options[index] = options[len - i - 1]
    options[len - i - 1] = temp
  }
  return options
}

// 格式化问答题得分点数据结构
function formatPoints(): Array<{ keywords: any, score: any }> {
  if (Array.isArray(props.questionDetail?.answer)) {
    return props.questionDetail.answer.map((item: any) => {
      return { keywords: item.keyword, score: item.score }
    })
  }
  if (props.questionDetail?.answer) {
    try {
      return JSON.parse(props.questionDetail.answer as string).map((item: any) => {
        return { keywords: item.keyword, score: item.score }
      })
    }
    catch (e) {
      console.error('Failed to parse answer:', e)
      return []
    }
  }
  return []
}

// 动态样式class
function getDynamicClass(option: string): string | undefined {
  if (!props.showStudentScore)
    return undefined
  const stuanswer = props.questionDetail.stuanswer
  const answer = props.questionDetail.answer
  if (stuanswer?.includes(option) && answer?.includes(option))
    return 'correct-tag'
  if (stuanswer?.includes(option) && !answer?.includes(option))
    return 'error-tag'
  return ''
}

function getPasscaseClass(passcase: string): string {
  const arr = passcase.split('/')
  return arr[0] === arr[1] ? 'correct-text' : 'error-text'
}

// 判断是否为正确选项
function judgeCorrentOption(option: string): boolean {
  return props.questionDetail.answer?.includes(option) || false
}

// 格式化算法题示例输入
function formatTestcaseInput(item: any): string {
  let str = ''
  item.input.forEach((param: any) => {
    str += `${Object.keys(param)[0]} = ${Object.values(param)[0]}, `
  })

  return str.slice(0, -2)
}

// 获取正确答案
function getCorrectAnswer(): string {
  if ([0, 2].includes(props.questionDetail.type)) {
    const answer = props.questionDetail.answer
    return Array.isArray(answer) ? answer.join(',') : (answer || '')
  }
  if (props.questionDetail.type === 1) {
    const answer = props.questionDetail.answer
    return answer ? ([] as any[]).concat(answer).sort().join(',') : ''
  }
  if ([3, 5].includes(props.questionDetail.type)) {
    if (Array.isArray(props.questionDetail.answer)) {
      return props.questionDetail.answer.map((item: any) => item.keyword?.join(' / ')).join(';')
    }
    const answerArr: string[] = []
    try {
      const parsedAnswer = typeof props.questionDetail.answer === 'string'
        ? JSON.parse(props.questionDetail.answer || '[]')
        : props.questionDetail.answer || []
      parsedAnswer.forEach((item: any) => {
        answerArr.push(item.keyword?.join(' / '))
      })
    }
    catch (e) {
      console.error('Failed to parse answer:', e)
    }
    return answerArr.join('; ')
  }
  if (props.questionDetail.type === 6) {
    try {
      const parsedAnswer = typeof props.questionDetail.answer === 'string'
        ? JSON.parse(props.questionDetail.answer || '[]')
        : props.questionDetail.answer || []
      return Array.isArray(parsedAnswer) ? parsedAnswer.join(',') : ''
    }
    catch (e) {
      console.error('Failed to parse answer:', e)
      return ''
    }
  }
  return ''
}

// 问答题考生答案
function getQATextAnswer(answer: string = ''): string {
  const index = answer?.indexOf('#@$')
  if (typeof index === 'number' && index !== -1) {
    answer = answer.substring(0, index)
  }
  return answer
}

function getAnswerImgs(sAnswer: string = ''): string[] {
  const index = sAnswer?.indexOf('#@$')
  if (index === -1 || typeof index !== 'number') {
    return []
  }
  else {
    const answerImgsString = sAnswer.split('#@$')[1]
    return answerImgsString.split(',')
  }
}

function getScoreCondition(): string {
  const stuscore = props.questionDetail.stuscore || 0
  const score = props.questionDetail.score || 0
  if (stuscore >= score) {
    return 'correct'
  }
  else if (stuscore === 0) {
    return 'error'
  }
  else {
    return 'part'
  }
}

function getImgUrl(item: string): string {
  return `https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/${item}`
}

// 改分
const editingScore = ref<number>(0) // 正在编辑的分数
const hasEditScore = ref<boolean>(false)
const editedTime = ref<string>('') // 已编辑的时间

async function handleEditScorePopConfirm(): Promise<void> {
  try {
    if (editingScore.value === null || editingScore.value === undefined)
      return
    await modifyscore({
      studentId: props.studentId!,
      templeteId: props.paperId!,
      quesScore: [{ ques: props.questionDetail.quesId!, score: editingScore.value }],
    })
    emits('editScore', editingScore.value)
  }
  catch (error) {
    console.error('Failed to modify score:', error)
  }
}

function handleEditScorePopVisibleChange(val: boolean): void {
  if (val) {
    editingScore.value = props.questionDetail.stuscore || 0
  }
}
</script>

<template>
  <div class="test-question-display">
    <div v-if="$slots['right-top-icon']" class="test-question-stem tool-bar">
      <div style="display: flex;">
        <span v-if="showType" class="type">{{ QuestionEnum[questionDetail.type] || '问答题' }}</span>
        <!-- <span v-if="showScore && !print" class="score">{{ questionDetail[scoreKey] }}分</span> -->
      </div>
      <slot name="right-top-icon" />
    </div>
    <div class="test-question-stem" :class="{ deleted }">
      <span v-if="showType && !$slots['right-top-icon']" class="type">{{ QuestionEnum[questionDetail.type] || '问答题' }}</span>
      <!-- <span v-if="showScore && !print && !$slots['right-top-icon']" class="score">{{ questionDetail[scoreKey] }}分</span> -->
      <slot name="serialNumber" />
      <div class="body" :class="{ deleted }" @click="$event => findImageAndPreview($event)" v-html="getContent()" />
    </div>
    <template v-if="!deleted">
      <div class="test-question-content">
        <template v-if="questionDetail.type <= 2">
          <OptionsBox
            v-if="print"
            :options="questionDetail.options"
            :render-fn="(content: string) => questionDetail.complicatedediting ? content : transferStr(content)"
          />
          <div v-else class="test-question-options">
            <template v-for="option in questionDetail.options" :key="option.value">
              <div class="item-option">
                <OptionBtn
                  v-if="optionLetterType === 'button'"
                  class="option-btn"
                  :option="option.value"
                  :class="getDynamicClass(option.value)"
                />
                <span v-else :class="[showCorrectAnswer && judgeCorrentOption(option.value) ? 'tag' : '']" style="margin-right: 14px">{{ option.value }}.</span>
                <div
                  class="option-content" :class="[showCorrectAnswer && judgeCorrentOption(option.value) ? 'tag' : '']"
                  @click="$event => findImageAndPreview($event)"
                  v-html="
                    questionDetail.complicatedediting ? option.content : transferStr(option.content)
                  "
                />
                <span
                  v-if="showCorrectAnswer && judgeCorrentOption(option.value)"
                  class="correct-answer-tag"
                >正确答案</span>
              </div>
            </template>
          </div>
        </template>
        <!-- <template v-if="questionDetail.type === 3"> -->
        <template v-if="true">
          <div v-if="showQuestionPointsAndBasis">
            <div class="test-question-scorebasis">
              <span class="label">参考答案</span>
              <div
                class="scorebais-content"
                @click="$event => findImageAndPreview($event)"
                v-html="transferStr(questionDetail.correctAnswer.reference || '')"
              />
            </div>
            <div class="test-question-scorebasis">
              <span class="label">建议时长</span>
              <div
                class="scorebais-content"
                @click="$event => findImageAndPreview($event)"
                v-html="`${questionDetail.recordDuration / 60}分钟`"
              />
            </div>
          </div>
        </template>
        <template v-if="questionDetail.type === 4">
          <div class="test-question-testcase">
            <div v-for="(item, index) in questionDetail.btestcase" :key="index">
              <h4 class="title">
                示例{{ index + 1 }}:
              </h4>
              <div class="content" :class="{ transparent: print }">
                <div class="item">
                  <span class="item-text">输入: </span>{{ formatTestcaseInput(item) }}
                </div>
                <div class="item">
                  <span class="item-text">输出: </span>{{ item.output[0].value }}
                </div>
              </div>
            </div>
          </div>
          <div v-if="showQuestionDifficulty && ALGORITHM_DIFFICULTY[questionDetail.difficulty]" class="test-question-difficulty">
            <span class="label">题目难度</span>
            <span>{{ ALGORITHM_DIFFICULTY[questionDetail.difficulty].label }}</span>
          </div>
        </template>
        <template
          v-if="
            questionDetail.type === QuestionEnum['填空题'] && showCorrectAnswer && !showStudentScore
          "
        >
          <div style="margin-bottom: 10px; font-weight: bold;">
            正确答案: {{ getCorrectAnswer() }}
          </div>
        </template>
        <template v-if="questionDetail.type === 6">
          <div class="test-question-sequence">
            <div
              v-for="(option, index) in getRandomOptions(questionDetail.options || [])"
              :key="option.value"
              class="sort-option-wrapper"
            >
              <div v-if="print">
                {{ String.fromCharCode(65 + index) }}
              </div>
              <div v-else>
                {{ option.value }}
              </div>
              <div
                class="item"
                :class="{ transparent: print }"
                v-html="
                  questionDetail.complicatedediting ? option.content : transferStr(option.content)
                "
              />
            </div>
          </div>
        </template>
      </div>
      <div v-if="showTag && questionDetail?.tagname?.length" class="test-question-tag">
        <template v-for="tag in questionDetail.tagname" :key="tag">
          <span class="item">
            {{ tag }}
          </span>
        </template>
      </div>
      <div v-if="hasDisablekws && props.showDisabledKws" class="disableKws">
        <span class="label">禁用关键词</span>
        <div class="kw-list">
          <div v-for="item in Object.keys(props.questionDetail.disablekws)" :key="item" class="kw-item">
            <div class="language">
              {{ item }}:
            </div>
            <div class="kw-wrap">
              <div v-for="keyword in props.questionDetail.disablekws[item]" :key="keyword" class="kw">
                {{ keyword }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 校对状态 -->
      <template v-if="questionDetail.proofreading_info?.proofreading && showQuestionProofreadingRecord">
        <p class="proofread-record">
          <img v-if="questionDetail.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
          <img v-if="questionDetail.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
          <span style="margin-left: 4px;">由{{ questionDetail.proofreading_info.is_ai ? 'AI' : questionDetail.proofreading_info.teacher_name }}于{{ questionDetail.proofreading_info.update_at }}{{ questionDetail.proofreading_info.is_ai ? '判断' : '确认' }}该题</span>
          <span v-if="questionDetail.proofreading_info.proofreading === 1" style="color: #38910b;font-weight: bold;">正确</span>
          <span v-if="questionDetail.proofreading_info.proofreading === 2" style="color: #dc2b28;font-weight: bold;">错误</span>
        </p>
        <!-- <div class="proofread-reason" v-if="questionDetail.proofreading_info.proofreading === 2">
            <span class="label">错误原因</span>
            <div>{{ questionDetail.proofreading_info.wrong_reason }}</div>
        </div> -->
      </template>
      <template v-if="showStudentScore && questionDetail.type === 4">
        <CodeBox
          autodetect
          :language="questionDetail.language"
          :code="questionDetail.stuanswer || ''"
        />
        <div class="out-answer">
          通过测试用例数
          <span :class="getPasscaseClass(questionDetail.passcase!)">{{
            questionDetail.passcase
          }}</span>
        </div>
      </template>
      <div
        v-if="showStudentScore"
        class="test-question-answer"
        :class="getScoreCondition()"
      >
        <div v-if="questionDetail.type !== 4" class="correct-answer">
          <span class="label">正确答案</span>
          <div class="content">
            {{ getCorrectAnswer() }}
          </div>
        </div>
        <div v-if="questionDetail.type !== 4" class="stu-answer">
          <span class="label">考生答案</span>
          <div v-if="questionDetail.type !== 3" class="content">
            {{ questionDetail.stuanswer }}
          </div>
          <template v-else>
            <div class="content">
              <pre>{{ getQATextAnswer(questionDetail.stuanswer) }}</pre>
              <div class="answer-imgs">
                <template v-for="img in getAnswerImgs(questionDetail.stuanswer)" :key="img">
                  <div class="item">
                    <a-image :src="getImgUrl(img)" />
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
        <div class="get-score">
          <span class="label">试题得分</span>
          <div class="content">
            {{ questionDetail.stuscore }}
          </div>
          <a-popconfirm v-if="scoreEditable" ok-text="确定" cancel-text="取消" overlay-class-name="popconfirm-editscore" @confirm="handleEditScorePopConfirm" @visible-change="handleEditScorePopVisibleChange">
            <template #icon />
            <template #title>
              修改得分为
              <a-input-number
                v-model:value="editingScore"
                :min="0"
                :max="questionDetail.score"
                :precision="0"
                :controls="false"
                style="text-align: right; width: 60px;border-radius: 8px;"
              />
              分
            </template>
            <span class="btn-wrap">
              <img class="score-edit-btn" src="@/assets/images/paper/edit.svg" title="改分" alt="">
            </span>
          </a-popconfirm>
        </div>
        <!-- 改分记录 -->
        <div v-if="hasEditScore || (props.questionDetail.correctiontime && props.questionDetail.corrector)" class="score-record">
          {{ hasEditScore ? editedTime : props.questionDetail.correctiontime }}由{{ hasEditScore ? store.getters.username : props.questionDetail.corrector }}确认评分
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.test-question-display {
  
  .btn-wrap {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      background: #f5f5f5;
    }
  }
}

.transparent {
  border-color: transparent !important;
  background-color: transparent !important;
}
.test-question-stem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  white-space: pre-wrap;
  &.deleted {
    margin-bottom: 0;
  }

  &.tool-bar {
    display: flex;
    justify-content: space-between;
  }
  .type {
    align-self: flex-start;
    flex-shrink: 0;
    width: 52px;
    text-align: center;
    line-height: 20px;
    background: #f1f4fe;
    border: 1px solid #5478ee;
    border-radius: 4px;
    font-size: 12px;
    color: #5478ee;
    margin-right: 8px;
  }
  :deep(.body) {
    font-size: 16px;
    line-height: 22px;
    // word-break: break-all;
    text-align: justify;
    flex: 1;
    min-width: 0;
    // overflow: auto;
    overflow-x: auto;
    overflow-y: hidden;
    &.deleted {
      text-decoration: line-through;
    }
    p {
      font-size: 16px;
      word-break: break-word;
      img {
        max-width: 100%;
        cursor: zoom-in!important;
      }
    }
  }
  .score {
    align-self: flex-start;
    flex-shrink: 0;
    width: 41px;
    text-align: center;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 8px;
    margin-top: 1px;
  }
}
.out-answer {
  color: rgba(0, 0, 0, 0.45);
  padding-left: 50px;
  margin-bottom: 10px;
  .correct-text {
    color: #52c41a;
  }
  .error-text {
    color: #de504e;
  }
}
.test-question-content {
  .test-question-options {
    .item-option {
      display: flex;
      align-items: center;
      .tag {
        color: #000;
        font-weight: bolder;
      }
      + .item-option {
        margin-top: 16px;
      }
      :deep(.option-content) {
        overflow-y: hidden  ;
        p {
          word-wrap: break-word;
          img {
            max-width: 100%;
            cursor: zoom-in!important;
          }
        }
      }
    }
    .option-btn {
      margin-right: 16px;
    }
    .correct-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .error-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .correct-answer-tag {
      font-size: 12px;
      color: #2f8c00;
      background: #f3f7f1;
      border-radius: 4px;
      padding: 0 2px;
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
  .test-question-scorebasis {
    display: flex;
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
      flex-shrink: 0;
    }

    :deep(.scorebais-content) {
      p {
        word-wrap: break-word;
        img {
          max-width: 100%;
          cursor: zoom-in!important;
        }
      }
    }
  }
  .test-question-points {
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #626262;
    margin-bottom: 8px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-items: baseline;
    .label {
      color: #626262;
    }
    
    .points-content {
      margin-left: 10px;
      flex: 1;
      min-width: 0;
      .item {
        display: flex;
        .scorePointIndex {
          flex: none;
          width: 18px;
          height: 28px;
          font-size: 14px;
          color: #121633;
          line-height: 28px;
        }
        .scoPointDetail {
          margin-bottom: 8px;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .scorePoint {
            margin-left: 8px;
            margin-bottom: 8px;
            padding: 1px 4px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            line-height: 24px;
          }
        }
        .score {
          height: 28px;
          margin-left: 8px;
          padding: 0 6px;
          background: #f0f0f0;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
          line-height: 28px;
          white-space: nowrap;
        }
      }
    }
    > div {
      margin-left: 52px;
    }
  }
  .test-question-testcase {
    .title {
      padding: 0 0 8px;
      font-size: 14px;
      font-weight: 600;
    }
    .content {
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 16px;

      .item {
        font-size: 14px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
      .item-text {
        font-weight: 600;
        padding-right: 6px;
      }
    }
  }
  .test-question-difficulty {
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
    }
  }
  .test-question-sequence {
    margin-bottom: 16px;
    .item {
      display: flex;
      min-height: 34px;
      padding-left: 8px;
      align-items: center;
      border: 1px solid #dad8d8;
      color: #121633;
      border-radius: 2px;
    }
  }
  .sort-option-wrapper {
    margin-bottom: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    .item {
      flex: 1;
      margin-left: 10px;
    }
  }
}
.test-question-tag {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.disableKws {
  margin-top: 10px;
  .label {
    color: #626262;
    margin-right: 32px;
  }
  .kw-list {
    margin-top: 8px;
    padding-left: 16px;
    .kw-item {
      display: flex;
      .language {
        flex: none;
        width: 80px;
        height: 24px;
        margin-right: 8px;
        color: #626262;
        line-height: 24px;
      }
      .kw-wrap {
        line-height: 24px;
        display: flex;
        flex-wrap: wrap;
        color: #626262;
        .kw {
          margin-right: 4px;
          margin-bottom: 4px;
          padding: 0 4px;
          background: #f5f5f5;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
  .value {
    padding-left: 16px;
  }
}
.test-question-answer {
  padding: 16px 24px 16px 48px;
  border-radius: 8px;
  margin-top: 20px;
  .label {
    color: #626262;
    font-size: 12px;
    margin-right: 32px;
    white-space: nowrap;
  }
  .correct-answer,
  .stu-answer,
  .get-score {
    margin-bottom: 8px;
    font-size: 12px;
    display: flex;
    &:last-child {
      margin-bottom: 0;
    }
    .content {
      font-size: 12px;
      word-break: break-word;
    }
  }
  .score-record {
    font-size: 12px;
    color: rgba(0,0,0,0.45);
  }
  .answer-imgs {
    &:has(.item) {
      margin-top: 16px;
    }
    display: flex;
    :deep(.item) {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 8px;
      border-radius: 5px;
      overflow: hidden;
      .ant-image {
        height: 100%;
        width: 100%;
      }
      .ant-image-img {
        height: 100%;
        border-radius: 5px;
        object-fit: cover;
      }
    }
  }
  
  &.error {
    border: 1px solid #ff4d4f;
    background: url('@/assets/images/exam/error.png') no-repeat 95%, #fcf0f0;
  }
  &.part {
    border: 1px solid #faad14;
    background: url('@/assets/images/exam/part.png') no-repeat 95%, #faf4ee;
  }
  &.correct {
    background: #f6ffed;
    border: 1px solid #52c41a;
  }
}

.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 16px;
  display: flex;
  align-items: center;
  position: relative;
  left: -3px;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
  }
}
</style>

<style lang="less">
.popconfirm-editscore {
  .ant-popover-message-title {
    padding-left: 0;
  }
}
</style>
