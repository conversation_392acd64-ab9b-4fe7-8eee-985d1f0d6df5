<template>
  <div class="number-chart">
    <span class="name">{{ name }}</span>
    <div>{{ data }}</div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  name: {
    type: String,
    default: '总题目'
  },
  data: {
    type: Number,
    default: 0
  }
})
</script>

<style lang="less" scoped>
.number-chart {
  width: 100%;
  height: 100%;
  background: #fff;
  .name {
    display: inline-block;
    padding: 16px;
    font-size: 15px;
    font-weight: 600;
  }
  div {
    // height: 100%;
    // transform: translateY(-50px);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 72px;
    color: #5478ee;
  }
}
</style>
