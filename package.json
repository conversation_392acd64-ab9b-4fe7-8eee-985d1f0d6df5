{"name": "exam-teacher-web", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode dev --host", "build": "vite build --mode prod", "serve": "vite preview", "bootstrap": "pnpm install && pnpm run dev", "postinstall": "npx simple-git-hooks", "lint": "eslint .", "lint:fix": "eslint --fix", "deploy_flow": "node ./script/deploy.cjs", "df": "pnpm deploy_flow", "claude": "set https_proxy=http://127.0.0.1:7890 && set http_proxy=http://127.0.0.1:7890 && set ANTHROPIC_AUTH_TOKEN=sk-MZoUbr3Ga7JZ89urPCABigZjg7b7EBwo4C7F2LSvAyG0R3Fu && set ANTHROPIC_BASE_URL=https://anyrouter.top && claude"}, "dependencies": {"@ant-design/icons-vue": "6.0.1", "@antfu/eslint-config": "^3.9.2", "@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@iconify-json/fluent-emoji": "^1.2.3", "@iconify/json": "^2.2.338", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vueup/vue-quill": "^1.1.1", "@vueuse/components": "^10.7.0", "@vueuse/core": "^10.7.0", "@vueuse/math": "^13.3.0", "@vueuse/router": "^10.7.0", "@yaireo/tagify": "^4.34.0", "ali-oss": "^6.17.1", "alova": "^3.2.11", "animejs": "^4.1.1", "ant-design-vue": "^4.2.6", "autoprefixer": "^10.4.20", "axios": "^0.22.0", "claude": "^0.1.2", "clsx": "^2.1.1", "crypto-js": "^4.1.1", "css-loader": "^6.4.0", "dayjs": "^1.11.13", "dompurify": "^3.2.1", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.10", "esdk-obs-browserjs": "^3.23.5", "eslint-plugin-file-progress": "^3.0.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-format": "^0.1.2", "gsap": "^3.13.0", "highlight.js": "^11.8.0", "hls.js": "^1.6.2", "json-editor-vue": "^0.10.5", "jszip": "^3.10.1", "katex": "^0.16.7", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "mitt": "^3.0.1", "moment": "^2.29.4", "monaco-editor": "^0.31.1", "motion-v": "1.1.0-alpha.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "postcss": "^8.4.49", "qrcode.vue": "^3.4.1", "quill": "^1.3.7", "quill-blot-formatter": "^1.0.5", "quill-image-uploader": "^1.3.0", "quill-paste-smart": "^1.4.12", "readline-sync": "^1.4.10", "simple-git-hooks": "^2.11.1", "ssh2": "^1.16.0", "style-loader": "^3.3.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.15", "unplugin-auto-import": "^0.18.5", "unplugin-vue-components": "^0.27.4", "uuid": "^9.0.0", "v-viewer": "^3.0.11", "vanilla-jsoneditor": "^0.11.8", "viewerjs": "^1.11.6", "vite-plugin-compression": "^0.3.5", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.2.4", "vue-echarts": "^7.0.3", "vue-i18n": "^9.1.7", "vue-json-pretty": "^2.2.4", "vue-router": "^4.0.3", "vue3-drr-grid-layout": "^1.9.1", "vue3-lottie": "^3.2.0", "vue3-print-nb": "^0.1.4", "vue3-ts-jsoneditor": "^2.9.0", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "wangeditor": "^4.7.9"}, "devDependencies": {"@egoist/tailwindcss-icons": "^1.9.0", "@iconify-json/eos-icons": "^1.2.2", "@iconify-json/lucide": "^1.2.43", "@types/ali-oss": "^6.16.7", "@types/crypto-js": "^4.1.1", "@types/katex": "^0.16.0", "@types/lodash": "^4.17.20", "@types/lodash-es": "^4.17.5", "@types/node": "^16.10.4", "@types/uuid": "^9.0.2", "@types/yaireo__tagify": "^4.17.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vitejs/plugin-vue": "^1.9.3", "@vue/babel-plugin-jsx": "^1.4.0", "@zougt/vite-plugin-theme-preprocessor": "^1.4.8", "eslint": "^8.57.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^7.19.1", "husky": "^7.0.2", "less": "^4.1.2", "lint-staged": "^11.2.3", "prettier": "^2.4.1", "typescript": "^4.4.3", "vite": "^2.6.4", "vite-plugin-remove-console": "^2.1.1", "vue-tsc": "^0.3.0"}, "resolutions": {"@typescript-eslint/utils": "^8.2.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": ["eslint --fix", "git add"]}}