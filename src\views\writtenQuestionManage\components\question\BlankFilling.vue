<template>
  <a-form
    class="form"
    ref="createQuestionFormRef"
    :model="formState"
    hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
  >
    <a-form-item label="分值" name="score">
      <score-input
        class="scoreInp"
        ref="scoreInputTotalRef"
        :disabled="formState.sepscore"
        v-model="formState.score"
        @getScore="getScore"
      />
    </a-form-item>
    <a-form-item label="题干内容" name="body">
      <a-textarea
        v-if="!formState.complicatedediting"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
      />
      <VueQuillEditor
        v-else
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
      ></VueQuillEditor>
      <div class="body-tip">
        <svg-icon name="tip" class="tip-icon" /><span>连续输入两个下划线"__"可增加空位</span>
      </div>
    </a-form-item>
    <a-form-item class="question-options">
      <template v-for="(item, index) in formState.fillBlank" :key="index">
        <div class="fill-blank-item">
          <span class="label">填空{{ index + 1 }}答案</span>
          <a-select
            v-model:value="item.keyword"
            mode="tags"
            placeholder="点击，编辑选项；点击回车设置多个关键词"
            @change="handleChangeSelect"
          >
          </a-select>
          <div v-if="formState.sepscore" class="score-wrapper">
            <span>分值</span>
            <score-input
              ref="scoreInputRef"
              v-model="item.score"
              @getScore="getFillBlankScore($event, item)"
            />
          </div>
        </div>
      </template>
    </a-form-item>
    <div class="fill-blank-config">
      <!-- <div>
        <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
        <span>按空得分，每个填空项单独计分</span>
        <a-tooltip placement="right">
          <template #title>
            <span
              >当考试设置为“按空得分，每个填空项单独计分”，并且本题目勾选了此选项，则每个填空项单独设置分值并计分。否则，需每个填空项均答对，方可得分。</span
            >
          </template>
          <svg-icon name="gray-tip" class="tip-icon" />
        </a-tooltip>
      </div> -->
      <div>
        <a-checkbox v-model:checked="formState.ordered" class="check-box" />
        <span>判分时区分答案先后顺序</span>
      </div>
      <div>
        <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
        <span>忽略大小写</span>
      </div>
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import ScoreInput from './ScoreInput.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import { nextTick, ref, watch } from 'vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { findDeletedBlanks } from '@/utils'
// import { Form } from 'ant-design-vue'

// const useForm = Form.useForm

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['update:modelValue'])

const scoreInputTotalRef = ref()
const formState = ref<any>({
  complicatedediting: false,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  exampleIO: [],
  sepscore: true,
  ordered: true,
  ignorecase: true,
  tags: [],
  fillBlank: ref([])
})

// 获取填空题分值
const getFillBlankScore = (score: any, item: any) => {
  item.score = score
  let totalScore = 0
  formState.value.fillBlank.forEach((item: any) => {
    totalScore += Number(item.score)
  })
  formState.value.score = totalScore
}

// 校验分值
const checkScore = async (rule: RuleObject, value: string) => {
  if (value == '0') {
    return Promise.reject('请给该题目匹配合适的分值')
  } else {
    return Promise.resolve()
  }
}

// 校验题干
const checkBody = async (rule: RuleObject, value: '') => {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  } else {
    return Promise.resolve()
  }
}

// 定义规则
const rules = {
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }]
}

// const { validate, validateInfos } = useForm(formState, rules)

// 获取分数
const getScore = (score: any) => {
  formState.value.score = score
}

function handleChangeSelect(tagsValue: string[]) {
  tagsValue.forEach((tag: string) => tag.trim())
}

// 监听题干内容
// const fillBlank = ref<any>([])
const fillBlankCount = ref(0)
let oldFillBlank = ref('')
// let oldFillBlankCount = ref(0)
watch(
  () => formState.value.body,
  (val) => {
    if (val.length === 0) {
      formState.value.fillBlank.length = 0
      fillBlankCount.value = 0
      oldFillBlank.value = ''
      return
    }
    const res = val.match(/_{2,}/g)
    if (res) {
      if (fillBlankCount.value > res.length) {
        // 说明删除了空格
        let deletedBlankIndexArray = []
        if (res.length === 0) {
          deletedBlankIndexArray.push(0)
        } else {
          deletedBlankIndexArray = findDeletedBlanks(oldFillBlank.value, val)
        }
        deletedBlankIndexArray.forEach((item) => {
          formState.value.fillBlank.splice(item, 1)
        })
        // 动态更新该题总分
        if (formState.value.fillBlank.length > 0) {
          formState.value.score = formState.value.fillBlank.reduce(
            (prev: any, current: any) => prev + Number(current.score),
            0
          )
        } else {
          formState.value.score = 0
        }
        console.log('deletedBlankIndexArray', deletedBlankIndexArray)
      } else if (fillBlankCount.value < res.length) {
        // 说明增加了空格, 于删除空格的逻辑相反
        let addedBlankIndexArray = []
        if (res.length === 1) {
          addedBlankIndexArray.push(0)
        } else {
          addedBlankIndexArray = findDeletedBlanks(val, oldFillBlank.value)
        }
        console.log('addedBlankIndexArray', addedBlankIndexArray)
        addedBlankIndexArray.forEach((item) => {
          formState.value.fillBlank.splice(item, 0, { score: 0 })
        })
      }

      fillBlankCount.value = res.length
    } else {
      if (formState.value.fillBlank.length > 0) {
        formState.value.fillBlank.length = 0
        fillBlankCount.value = 0
      }
    }
    oldFillBlank.value = val
  }
)
// 监听填空个数
// watch(fillBlankCount, (newVal) => {
//   formState.value.fillBlank.length = 0
//   for (let i = 0; i < newVal; i++) {
//     formState.value.fillBlank.push({ score: 0 })
//   }
// })

watch(
  () => formState.value.sepscore,
  async (val) => {
    let totalScore = 0
    if (val) {
      await nextTick()
      formState.value.fillBlank.forEach((item: any) => {
        totalScore += Number(item.score)
        scoreInputTotalRef.value.score = totalScore
      })
    } else {
      scoreInputTotalRef.value.score = 0
      formState.value.fillBlank.forEach((item: any) => {
        item.score = 0
      })
    }
  }
)

watch(
  formState,
  (val) => {
    emits('update:modelValue', val)
  },
  {
    deep: true
  }
)

watch(
  () => props.modelValue,
  (val) => {
    try {
      formState.value = Object.assign(formState.value, val)
      if (formState.value.answer) {
        formState.value.fillBlank = JSON.parse(formState.value.answer)
        console.log(222, formState.value.fillBlank, JSON.parse(formState.value.answer))
      }
    } catch (error) {
      console.log(111, error)
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.form {
  margin-bottom: auto;
  .question-options {
    .fill-blank-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .label {
        width: 70px;
        font-size: 12px;
      }
      .score-wrapper {
        width: 160px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 20px;
      }
      :deep(.ant-select-selector) {
        border: none;
        box-shadow: none;
      }
    }
    .item-option {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 12px;
      position: relative;
      .option-radio {
        margin-right: 32px;
        display: inline-block;
        width: 20px;
      }
      .option-content {
        margin-left: -18px;
        font-size: 12px;
        border: none;
        box-shadow: none;
        width: 220px;
      }
      .del-icon {
        display: none;
      }
    }
    .item-option:hover .del-icon {
      position: relative;
      display: inline-block;
      // bottom: -3px;
    }
    .add-option-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 82px;
      height: 24px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      margin-top: 8px;
      cursor: pointer;
    }
  }
  .body-tip {
    display: flex;
    align-items: center;
    background: #f1f4fe;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    margin-top: 8px;
    .tip-icon {
      margin: 0 8px;
    }
  }
  .fill-blank-config {
    div {
      display: flex;
      align-items: center;
      line-height: 28px;
    }
    .check-box {
      margin-right: 8px;
    }
    span {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.85);
    }
    .tip-icon {
      margin-left: 8px;
    }
  }
}
</style>
