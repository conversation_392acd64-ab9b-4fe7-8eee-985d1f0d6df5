<script setup lang="ts">
import type { ProfileHeaderProps } from './shared'
import { downloadResumeFileApi, updateResumeInfoApi } from '@/api/admin/resumeManage'
import { downloadFile } from '@/utils/common'
import { ElMessage } from 'element-plus'
import ResumeEditModal from '../ResumeEditModal.vue'
import { resumeListDataInFilter } from '../ResumeMultipleSelect/shared'

/**
 * 个人信息这里，从这个组件开始独立
 * 因为这个模块的编辑信息也就在这里展示
 * 
 * 初始值是从父组件传过来的，更新后的数据，往外跑抛出一下就行
 * 当然业务需求，要更新一下侧边栏的值 resumeListDataInFilter
 */
const props = defineProps<{
  userData: ProfileHeaderProps
}>()
const updatedUserData = ref(props.userData)
watch(() => props.userData, (newVal) => {
  updatedUserData.value = newVal
}, {
  deep: true,
})

const modalvisible = ref(false)

/**
 * 编辑简历
 * @param record 
 */
function handleEditResume(_record: any) {
  modalvisible.value = true
}
/**
 * 编辑简历完成后
 */
function handleChangeResume(res: any) {
  // 刷新个人信息
  updatedUserData.value.candidateName = res.candidateName
  updatedUserData.value.gender = res.gender
  updatedUserData.value.birthday = res.birthday
  updatedUserData.value.phone = res.phone
  updatedUserData.value.email = res.email
  updatedUserData.value.highestEducation = res.highestEducation
  updatedUserData.value.graduateSchool = res.graduateSchool
  updatedUserData.value.workYears = res.workYears
  updatedUserData.value.updateTime = res.updateTime
  updatedUserData.value.matchScore = res.matchScore
  updatedUserData.value.age = res.age
  updatedUserData.value.hometown = res.hometown
  // 刷新侧边sidebar的信息
  resumeListDataInFilter.value.forEach((item: any) => {
    if (item.resumeId === updatedUserData.value.resumeId) {
      item.candidateName = res.candidateName
      item.gender = res.gender
      item.birthday = res.birthday
      item.phone = res.phone
      item.email = res.email
      item.highestEducation = res.highestEducation
      item.graduateSchool = res.graduateSchool
      item.workYears = res.workYears
      item.updateTime = res.updateTime
      item.avatar = res.avatar
      item.position = res.position
      item.location = res.location
      item.hometown = res.hometown
      item.resumeStatus = res.resumeStatus
      item.matchScore = res.matchScore
      item.age = res.age
    }
  })
}

/**
 * 取消编辑简历，回调
 */
function handleCloseModal() { }

/**
 * 设置适应度
 */
function handleSetFitness(resumeStatus: string) {
  // 1. 更新当前组件显示的
  const lastResumeStatus = JSON.stringify(updatedUserData.value.resumeStatus)
  updatedUserData.value.resumeStatus = resumeStatus
 
  // 因为这里只需要更改 resumeStatus 和 updateTime 两个字段
  // 所以调用接口成功后，更新这两个字段
  updateResumeInfoApi({ 
    resumeId: updatedUserData.value.resumeId,
    resumeStatus,
  })
    .then((res: any) => {
      resumeListDataInFilter.value.forEach((item: any) => {
        if (item.resumeId === updatedUserData.value.resumeId) {
          item.resumeStatus = res.resumeStatus
          item.updateTime = res.updateTime
        }
      })

      ElMessage.success('设置成功')
    })
    .catch((err: any) => {
      ElMessage.error('设置失败')
      updatedUserData.value.resumeStatus = lastResumeStatus
      console.log(err)
    })
}

const previewModalVisible = ref(false)
function handlePreviewResume() {
  previewModalVisible.value = true
}

async function handleDownloadResume() {
  // window.open(updatedUserData.value.downloadFileUrl, '_blank')

  try {
    const res = await downloadResumeFileApi({
      resumeId: updatedUserData.value.resumeId,
    })
    if (!res) 
      return

    downloadFile(res)
  }
  catch (error) {
    console.log(error)
  }
}

const isFullscreenPreviewFile = ref(false)

function handleFullscreenPreviewFile() {
  isFullscreenPreviewFile.value = !isFullscreenPreviewFile.value
}
</script>

<template>
  <div class="w-full min-w-[950px] min-h-[152px] bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 ">
    <div class="w-full h-full flex flex-row justify-between items-center ">
      <div class="w-full h-full flex items-center">
        <!-- start 这里写了四个一样的，只是判断显示头像，如果使用同一个 img ，动态 src ，必须先将四个图片 import ，动态 alias 路径无法展示 -->
        <img v-if="updatedUserData?.avatar" :src="updatedUserData.avatar" alt="头像" class="w-[104px] h-[104px] mr-10 flex-shrink-0 rounded-full object-cover">
        <img v-else-if="!updatedUserData?.avatar && updatedUserData?.gender === '男'" src="@/assets/images/resume/男avatar.png" alt="头像" class="w-[104px] h-[104px] mr-10 flex-shrink-0 rounded-full object-cover">
        <img v-else-if="!updatedUserData?.avatar && updatedUserData?.gender === '女'" src="@/assets/images/resume/女avatar.png" alt="头像" class="w-[104px] h-[104px] mr-10 flex-shrink-0 rounded-full object-cover">
        <img v-else-if="!updatedUserData?.avatar && updatedUserData?.gender !== '男' && updatedUserData?.gender !== '女'" src="@/assets/images/resume/未知avatar.png" alt="头像" class="w-[104px] h-[104px] mr-10 flex-shrink-0 rounded-full object-cover">
        <!-- end btw code有点丑 \doge -->

        <div class="h-full flex flex-col justify-around items-start">
          <div class="flex items-center">
            <h2 class="text-xl font-bold">
              {{ updatedUserData?.candidateName || '未知' }}
            </h2>
            <span class="text-gray-600 ml-2">{{ `（${updatedUserData?.englishName || '未知'}）` }}</span>
            <div class="ml-4 px-2 py-1 rounded text-sm ">
              {{ updatedUserData?.position || '未知' }}
            </div>
            <el-tag type="primary" class="ml-4">
              {{ updatedUserData?.workYears || '未知' }} 
            </el-tag>
            <el-tag type="success" class="ml-4">
              {{ updatedUserData?.gender || '未知' }}
            </el-tag>

            <div class="ml-4 flex justify-center items-center cursor-pointer " @click="handleEditResume(updatedUserData)">
              <span class="i-lucide-pen-line" />
            </div>
          </div>

          <div class="mt-2 text-sm text-[#8c8c8c] flex flex-row justify-start items-center">
            <span class="mr-3">{{ updatedUserData?.graduateSchool || '未知' }}</span>
            <div class="mr-3 w-[1px] h-[14px] inline-block bg-gray-300" />
            <span class="mr-3">{{ updatedUserData?.highestEducation || '未知' }}</span>
            <div class="mr-3 w-[1px] h-[14px] inline-block bg-gray-300" />
            <span class="mr-3">{{ updatedUserData?.hometown?.length > 0 && Array.isArray(updatedUserData?.hometown) ? updatedUserData?.hometown?.join('') : '未知' }}</span>
            <div class="mr-3 w-[1px] h-[14px] inline-block bg-gray-300" />
            <span class="mr-3">{{ updatedUserData?.birthday?.replace(/-(\d{2})$/, "") || '未知' }} {{ `（${updatedUserData?.age ? `${updatedUserData?.age}岁` : '未知'}）` }}</span>
          </div>
          <div class="mt-2 text-sm text-[#8c8c8c] flex flex-row justify-start items-center">
            <span class="mr-4">{{ updatedUserData?.phone || '未知' }}</span>
            <div class="mr-3 w-[1px] h-[14px] inline-block bg-gray-300" />
            <span>{{ updatedUserData?.email || '未知' }}</span>
          </div>
        </div>
      </div>
      <div class="w-[292px] h-full flex-shrink-0 flex flex-col justify-center items-end gap-7">
        <div class="flex justify-center items-center gap-3">
          <div class="flex justify-center items-center cursor-pointer" @click="handleSetFitness('合适')">
            <div class="relative w-8 h-8">
              <div v-if="updatedUserData?.resumeStatus !== '合适'" class="i-fluent-emoji-slightly-frowning-face w-8 h-8" style="filter:grayscale(1)" />
              <Transition name="flip">
                <img
                  v-if="updatedUserData?.resumeStatus === '合适'" 
                  class="absolute top-0 left-0 z-[1]" 
                  src="@/assets/images/resume/Grinning%20Squinting%20Face.png" 
                  alt="Grinning Squinting Face" 
                  width="32" 
                  height="32"
                >
              </Transition>
            </div>
            <div :class="updatedUserData?.resumeStatus === '合适' ? 'bg-[#FAF4EE] ' : 'bg-[#F3F3F3]'" class="w-[76px] h-6 ml-[-26px] pl-[15px] text-center rounded-xl flex justify-center items-center">
              合适
            </div>
          </div>

          <div class="flex justify-center items-center cursor-pointer" @click="handleSetFitness('待定')">
            <div class="relative w-8 h-8">
              <div v-if="updatedUserData.resumeStatus !== '待定'" class="i-fluent-emoji-thinking-face w-8 h-8 " style="filter:grayscale(1)" />
              <Transition name="flip">
                <img
                  v-if="updatedUserData.resumeStatus === '待定'" 
                  class="absolute top-0 left-0 z-[1]" 
                  src="@/assets/images/resume/Thinking%20Face.png" 
                  alt="Thinking Face" 
                  width="32" 
                  height="32"
                >
              </Transition>
            </div>
            <div :class="updatedUserData.resumeStatus === '待定' ? 'bg-[#FAF4EE] ' : 'bg-[#F3F3F3]'" class="w-[76px] h-6 ml-[-26px] pl-[15px] text-center rounded-xl flex justify-center items-center">
              待定
            </div>
          </div>

          <div class="flex justify-center items-center cursor-pointer" @click="handleSetFitness('不合适')">
            <div class="relative w-8 h-8">
              <div v-if="updatedUserData.resumeStatus !== '不合适'" class="i-fluent-emoji-slightly-frowning-face w-8 h-8" style="filter:grayscale(1)" />
              <Transition name="flip">
                <img
                  v-if="updatedUserData.resumeStatus === '不合适'" 
                  class="absolute top-0 left-0 z-[1]" 
                  src="@/assets/images/resume/Slightly%20Frowning%20Face.png" 
                  alt="Slightly Frowning Face" 
                  width="32" 
                  height="32"
                >
              </Transition>
            </div>
            <div :class="updatedUserData.resumeStatus === '不合适' ? 'bg-[#FAF4EE] ' : 'bg-[#F3F3F3]'" class="w-[76px] h-6 ml-[-26px] pl-[15px] text-center rounded-xl flex justify-center items-center">
              不合适
            </div>
          </div>
        </div>

        <div>
          <el-button @click="handlePreviewResume">
            查看简历
          </el-button>
          <el-button @click="handleDownloadResume">
            &nbsp;&nbsp; 下载 &nbsp;&nbsp;
          </el-button>
        </div>
      </div>
    </div>

    <ResumeEditModal
      v-model:visible="modalvisible"
      v-model:user-data="updatedUserData"
      @confirm="handleChangeResume"
      @cancel="handleCloseModal"
    />

    <el-dialog
      v-model="previewModalVisible"
      :fullscreen="isFullscreenPreviewFile"
      class="!p-8 !pt-10 !min-w-[68vw] relative "
    >
      <!-- class="!p-8 !pt-10 !min-w-[68vw] relative transition-all duration-300" -->
      <span v-if="!isFullscreenPreviewFile" title="全屏" class="i-lucide-expand w-[14px] h-[14px] text-[#91939a] absolute top-[14px] right-[64px] cursor-pointer" @click="handleFullscreenPreviewFile" />
      <span v-else title="退出全屏" class="i-lucide-shrink w-[14px] h-[14px] text-[#91939a] absolute top-[14px] right-[64px] cursor-pointer" @click="handleFullscreenPreviewFile" />

      <iframe
        class=" w-full min-h-[68vh]"
        :class="isFullscreenPreviewFile ? 'h-[calc(100vh-108px)]' : 'h-[68vh]'"
        title="预览文档"
        :src="updatedUserData.resumeFileUrl"
      />
    </el-dialog>
  </div>
</template> 

<style scoped>
/* 上下翻转动画 */
.flip-enter-active {
  transition: all 0.5s ease;
  transform-style: preserve-3d;
}

.flip-enter-from {
  opacity: 0;
  transform: rotateX(-180deg);
}

.flip-enter-to {
  opacity: 1;
  transform: rotateX(0deg);
}

/* 移除离开动画 */
.flip-leave-active {
  transition: none;
}

.flip-leave-to {
  opacity: 1;
  transform: rotateX(0deg);
}
</style>