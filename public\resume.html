<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>个人简历</title>
    <style>
      * {
        padding: 0;
        margin: 0;
        word-break: break-all;
      }

      body {
        background-color: #f5f5f5;
        font-family: 'Times New Roman', '宋体', serif;
      }

      .subtitle {
        font-size: 20px;
        font-weight: 600;
      }

      .wrapper .baseinfo,
      .eduinfowrapper,
      .proinfowrapper,
      .schoolinfowrapper,
      .awardinfowrapper,
      .skillinfowrapper {
        /* border-bottom: 1px solid #ccc; */
        padding: 24px 24px 0;
      }

      /* img {
        width: 100px;
        height: 100px;
        float: left;
      } */

      .wrapper {
        margin: 0 auto;
        width: 944px;
        background-color: #fff;
      }

      .spantitle {
        font-weight: 600;
      }

      .active {
        display: -webkit-box;
        display: flex;
        -webkit-flex-wrap: wrap;
        align-items: center;
        flex-wrap: wrap;
        font-size: 14px;
        margin: 0 auto;
        margin-top: 28px;
        /* border-bottom: 1px solid #eee; */
      }

      button {
        font-size: 14px;
        float: right;
        margin-right: 20px;
        margin-top: 24px;
        font-family: PingFang SC, PingFang SC-Regular;
        padding: 6px 20px;
        background-color: rgba(255, 255, 255, 0);
        border-radius: 5px;
        border: 1px solid #d9d9d9;
        cursor: pointer;
      }

      /* 基本信息 */

      .wrapper .baseinfoitem {
        font-size: 14px;
        display: -webkit-box;
        display: flex;
        -webkit-box-pack: space-around;
        justify-content: space-around;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        margin: 28px auto 0 auto;
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      }

      .wrapper .baseinfoitem .baseinfoname,
      .baseinfosex,
      .baseinfophone {
        margin-bottom: 10px;
      }

      .wrapper .baseinfoitem .spantitle {
        width: 100px;
        margin-bottom: 10px;
      }

      .wrapper .baseinfoitem > span:nth-child(2n) {
        width: 35%;
      }

      /* 教育经历 */
      /* .wrapper .eduinfowrapper .eduinfoitem {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      } */
      .wrapper .eduinfowrapper > div:last-child {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      }

      .eduinfoitem.active .school-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 30px;
      }
      .eduinfoitem.active .school-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 30px;
      }
      .eduinfoitem.active .school-info {
        display: flex;
        margin-bottom: 16px;
      }
      .eduinfoitem.active .school-info .school {
        font-size: 16px;
        font-weight: 600;
        width: 140px;
      }
      .eduinfoitem.active .master-info .item {
        padding: 0 10px 0 6px;
        border-right: 1px solid #d9d9d9;
      }
      .eduinfoitem.active .master-info .item:last-child {
        border: none;
      }

      .wrapper .eduinfowrapper .eduinfoitem > span {
        margin-bottom: 10px;
      }

      .wrapper .eduinfowrapper .eduinfoitem .spantitle {
        width: 100px;
        margin-bottom: 10px;
      }

      .wrapper .eduinfowrapper .eduinfoitem .eduinfodetail {
        width: 35%;
      }

      /* 项目经历 */
      .wrapper .proinfowrapper > div:last-child {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      }

      .wrapper .proinfowrapper .subtitle {
        margin-bottom: 30px;
      }
      .wrapper .proinfowrapper .proinfoitem {
        margin-bottom: 16px;
      }
      .wrapper .proinfowrapper .proinfoitem img {
        /* max-width: 100px; */
        max-height: 100px;
      }
      .wrapper .proinfowrapper .proinfoitem li {
        margin-left: 1em;
      }
      .wrapper .proinfowrapper > div:last-child .proinfoitem {
        margin-bottom: 0;
      }

      .wrapper .proinfowrapper .pro-basic-info {
        margin-bottom: 8px;
      }
      .wrapper .proinfowrapper .pro-name {
        font-size: 16px;
        font-weight: 600;
        margin-right: 48px;
      }

      /* 校园经历 */
      .wrapper .schoolinfowrapper > div:last-child {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      }
      .wrapper .schoolinfowrapper .subtitle {
        margin-bottom: 30px;
      }
      .wrapper .schoolinfowrapper .schoolinfoitem {
        margin-bottom: 30px;
      }
      .wrapper .schoolinfowrapper .schoolinfoitem img {
        /* max-width: 100px; */
        max-height: 100px;
      }
      .wrapper .schoolinfowrapper .schoolinfoitem li {
        margin-left: 1em;
      }
      .wrapper .schoolinfowrapper .collage-basic-info {
        margin-bottom: 16px;
      }
      .wrapper .schoolinfowrapper .pro-name {
        font-size: 16px;
        font-weight: 600;
        margin-right: 48px;
      }
      .wrapper .schoolinfowrapper .collage-dept-info {
        display: flex;
        margin-bottom: 16px;
      }
      .wrapper .schoolinfowrapper .collage-dept-info .job-name {
        padding-right: 16px;
      }

      /* 奖项证书 */
      .wrapper .awardinfowrapper > div:last-child {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      }
      .wrapper .awardinfowrapper .awardinfoitem .award-images {
        overflow: hidden;
      }
      .wrapper .awardinfowrapper .awardinfoitem .spantitle {
        width: 15%;
      }

      .wrapper .awardinfowrapper .awardinfoitem .awardinfoname,
      .awardinfotime {
        width: 35%;
        margin-bottom: 10px;
      }

      .wrapper .awardinfowrapper .awardinfoitem .spantitle {
        margin-bottom: 10px;
      }

      .wrapper .awardinfowrapper .awardinfoitem .awardinforank {
        width: 85%;
        margin-bottom: 10px;
      }

      .wrapper .awardinfowrapper .awardinfoitem .awardinfopic {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      .wrapper .awardinfowrapper .subtitle {
        display: block;
        margin-bottom: 30px;
      }
      .wrapper .awardinfowrapper .award-info {
        margin-bottom: 16px;
      }
      .wrapper .awardinfowrapper .award-info .award-name {
        font-size: 16px;
        font-weight: 600;
        padding-right: 16px;
      }
      .wrapper .awardinfowrapper .award-info .award-rank {
        padding-right: 16px;
      }

      /* 技能特长 */
      .wrapper .skillinfowrapper .skillinfoitem {
        font-size: 14px;
        display: -webkit-box;
        display: flex;
        /* justify-content: space-around; */
        /* flex-wrap: wrap; */
        margin: 28px auto;
      }

      .wrapper .skillinfowrapper .skills-category {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 30px;
      }
      .wrapper .skillinfowrapper .skills-category > span {
        margin-right: 50px;
      }

      .wrapper .skillinfowrapper .skills-category .degree {
        display: inline-block;
        font-size: 12px;
        margin-left: 12px;
        background: #f5f5f5;
        border-radius: 4px;
        padding: 4px 8px;
      }
      .degree-0 {
        background: #f5f5f5;
        color: #626262;
      }
      .degree-1 {
        background: #e08b34;
        color: #d96e00;
      }
      .degree-2 {
        background: #f3f7f1;
        color: #2f8c00;
      }

      .wrapper .skillinfowrapper .subtitle {
        margin-bottom: 30px;
      }

      .wrapper .skillinfowrapper .skillinfoitem .spantitle {
        width: 15%;
      }

      .wrapper .skillinfowrapper .skillinfoitem .skillinfoname,
      .skillinfodegname {
        width: 35%;
        margin-bottom: 10px;
      }

      @media print {
        body {
          font-family: 'Times New Roman', '宋体', serif;
        }

        .subtitle {
          font-size: 20px;
          font-weight: bold !important;
        }

        .wrapper .baseinfo,
        .eduinfowrapper,
        .proinfowrapper,
        .schoolinfowrapper,
        .awardinfowrapper,
        .skillinfowrapper {
          /* border-bottom: 1px solid #ccc; */
          padding: 24px 24px 0;
        }

        /* img {
        width: 100px;
        height: 100px;
        float: left;
      } */

        .wrapper {
          margin: 0 auto;
          width: 944px;
          background-color: #fff;
        }

        .spantitle {
          font-weight: 600 !important;
        }

        .active {
          display: -webkit-box;
          display: flex;
          -webkit-flex-wrap: wrap;
          align-items: center;
          flex-wrap: wrap;
          font-size: 14px;
          margin: 0 auto;
          margin-top: 28px;
          /* border-bottom: 1px solid #eee; */
        }

        button {
          font-size: 14px;
          float: right;
          margin-right: 20px;
          margin-top: 24px;
          font-family: PingFang SC, PingFang SC-Regular;
          padding: 6px 20px;
          background-color: rgba(255, 255, 255, 0);
          border-radius: 5px;
          border: 1px solid #d9d9d9;
          cursor: pointer;
        }

        /* 基本信息 */

        .wrapper .baseinfoitem {
          font-size: 14px;
          display: -webkit-box;
          display: flex;
          -webkit-box-pack: space-around;
          justify-content: space-around;
          -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
          margin: 28px auto 0 auto;
          border-bottom: 1px solid #6d6d6d;
          padding-bottom: 24px;
        }

        .wrapper .baseinfoitem .baseinfoname,
        .baseinfosex,
        .baseinfophone {
          margin-bottom: 10px;
        }

        .wrapper .baseinfoitem .spantitle {
          width: 100px;
          margin-bottom: 10px;
        }

        .wrapper .baseinfoitem > span:nth-child(2n) {
          width: 35%;
        }

        /* 教育经历 */
        /* .wrapper .eduinfowrapper .eduinfoitem {
        border-bottom: 1px solid #6d6d6d;
        padding-bottom: 24px;
      } */
        .wrapper .eduinfowrapper > div:last-child {
          border-bottom: 1px solid #6d6d6d;
          padding-bottom: 24px;
        }

        .eduinfoitem.active .school-avatar img {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 30px;
        }
        .eduinfoitem.active .school-avatar img {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 30px;
        }
        .eduinfoitem.active .school-info {
          display: flex;
          margin-bottom: 16px;
        }
        .eduinfoitem.active .school-info .school {
          font-size: 16px;
          font-weight: 600;
          width: 140px;
        }
        .eduinfoitem.active .master-info .item {
          padding: 0 10px 0 6px;
          border-right: 1px solid #d9d9d9;
        }
        .eduinfoitem.active .master-info .item:last-child {
          border: none;
        }

        .wrapper .eduinfowrapper .eduinfoitem > span {
          margin-bottom: 10px;
        }

        .wrapper .eduinfowrapper .eduinfoitem .spantitle {
          width: 100px;
          margin-bottom: 10px;
        }

        .wrapper .eduinfowrapper .eduinfoitem .eduinfodetail {
          width: 35%;
        }

        /* 项目经历 */
        .wrapper .proinfowrapper > div:last-child {
          border-bottom: 1px solid #6d6d6d;
          padding-bottom: 24px;
        }

        .wrapper .proinfowrapper .subtitle {
          margin-bottom: 30px;
        }
        .wrapper .proinfowrapper .proinfoitem {
          margin-bottom: 16px;
        }
        .wrapper .proinfowrapper .proinfoitem img {
          /* max-width: 100px; */
          max-height: 100px;
        }
        .wrapper .proinfowrapper .proinfoitem li {
          margin-left: 1em;
        }
        .wrapper .proinfowrapper > div:last-child .proinfoitem {
          margin-bottom: 0;
        }

        .wrapper .proinfowrapper .pro-basic-info {
          margin-bottom: 8px;
        }
        .wrapper .proinfowrapper .pro-name {
          font-size: 16px;
          font-weight: 600;
          margin-right: 48px;
        }

        /* 校园经历 */
        .wrapper .schoolinfowrapper > div:last-child {
          border-bottom: 1px solid #6d6d6d;
          padding-bottom: 24px;
        }
        .wrapper .schoolinfowrapper .subtitle {
          margin-bottom: 30px;
        }
        .wrapper .schoolinfowrapper .schoolinfoitem {
          margin-bottom: 30px;
        }
        .wrapper .schoolinfowrapper .schoolinfoitem img {
          /* max-width: 100px; */
          max-height: 100px;
        }
        .wrapper .schoolinfowrapper .schoolinfoitem li {
          margin-left: 1em;
        }
        .wrapper .schoolinfowrapper .collage-basic-info {
          margin-bottom: 16px;
        }
        .wrapper .schoolinfowrapper .pro-name {
          font-size: 16px;
          font-weight: 600;
          margin-right: 48px;
        }
        .wrapper .schoolinfowrapper .collage-dept-info {
          display: flex;
          margin-bottom: 16px;
        }
        .wrapper .schoolinfowrapper .collage-dept-info .job-name {
          padding-right: 16px;
        }

        /* 奖项证书 */
        .wrapper .awardinfowrapper > div:last-child {
          border-bottom: 1px solid #6d6d6d;
          padding-bottom: 24px;
        }
        .wrapper .awardinfowrapper .awardinfoitem .award-images {
          overflow: hidden;
        }
        .wrapper .awardinfowrapper .awardinfoitem .spantitle {
          width: 15%;
        }

        .wrapper .awardinfowrapper .awardinfoitem .awardinfoname,
        .awardinfotime {
          width: 35%;
          margin-bottom: 10px;
        }

        .wrapper .awardinfowrapper .awardinfoitem .spantitle {
          margin-bottom: 10px;
        }

        .wrapper .awardinfowrapper .awardinfoitem .awardinforank {
          width: 85%;
          margin-bottom: 10px;
        }

        .wrapper .awardinfowrapper .awardinfoitem .awardinfopic {
          display: inline-block;
          margin-right: 10px;
          margin-bottom: 10px;
        }
        .wrapper .awardinfowrapper .subtitle {
          display: block;
          margin-bottom: 30px;
        }
        .wrapper .awardinfowrapper .award-info {
          margin-bottom: 16px;
        }
        .wrapper .awardinfowrapper .award-info .award-name {
          font-size: 16px;
          font-weight: 600;
          padding-right: 16px;
        }
        .wrapper .awardinfowrapper .award-info .award-rank {
          padding-right: 16px;
        }

        /* 技能特长 */
        .wrapper .skillinfowrapper .skillinfoitem {
          font-size: 14px;
          display: -webkit-box;
          display: flex;
          /* justify-content: space-around; */
          /* flex-wrap: wrap; */
          margin: 28px auto;
        }

        .wrapper .skillinfowrapper .skills-category {
          display: flex;
          flex-wrap: wrap;
          padding-bottom: 30px;
        }
        .wrapper .skillinfowrapper .skills-category > span {
          margin-right: 50px;
        }

        .wrapper .skillinfowrapper .skills-category .degree {
          display: inline-block;
          font-size: 12px;
          margin-left: 12px;
          background: #f5f5f5;
          border-radius: 4px;
          padding: 4px 8px;
        }
        .degree-0 {
          background: #f5f5f5;
          color: #626262;
        }
        .degree-1 {
          background: #e08b34;
          color: #d96e00;
        }
        .degree-2 {
          background: #f3f7f1;
          color: #2f8c00;
        }

        .wrapper .skillinfowrapper .subtitle {
          margin-bottom: 30px;
        }

        .wrapper .skillinfowrapper .skillinfoitem .spantitle {
          width: 15%;
        }

        .wrapper .skillinfowrapper .skillinfoitem .skillinfoname,
        .skillinfodegname {
          width: 35%;
          margin-bottom: 10px;
        }
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <button class="print-btn" onClick="download()">打印简历</button>
      <div class="baseinfo">
        <span class="subtitle">基本信息</span>
        <div class="baseinfoitem">
          <span class="spantitle">姓名</span><span class="baseinfoname"></span>
          <span class="spantitle">身份证号</span><span class="baseinfoidcard"></span>
          <span class="spantitle">性别</span><span class="baseinfosex"></span>
          <span class="spantitle">生日</span><span class="baseinfobirth"></span>
          <span class="spantitle">手机号</span><span class="baseinfophone"></span>
          <span class="spantitle">户籍</span><span class="baseinfonative"></span>
          <span class="spantitle">邮箱</span><span class="baseinfoemail"></span>
          <span class="spantitle">报考部门</span><span class="baseinfounit"></span>
        </div>
      </div>
      <div class="eduinfowrapper">
        <span class="subtitle">教育经历</span>
      </div>
      <div class="proinfowrapper">
        <div class="subtitle">项目经历</div>
      </div>
      <div class="schoolinfowrapper">
        <div class="subtitle">校园经历</div>
      </div>
      <div class="awardinfowrapper">
        <span class="subtitle">奖项证书</span>
      </div>
      <div class="skillinfowrapper">
        <div class="subtitle">技能特长</div>
        <div class="skills-category"></div>
      </div>
    </div>
    <script>
      const userid = location.href.split('?')[1]
      const authorization = JSON.parse(localStorage.getItem('exam-system')).accessToken

      let baseinfo = {}
      let eduinfo = []
      let proinfo = []
      let schoolinfo = []
      let awardinfo = []
      let skillinfo = []
      const degreeName = ['了解', '熟练', '精通']

      fetch('/api/v1/portal/stuallinfo', {
        method: 'POST',
        body: JSON.stringify({
          student: userid
        }),
        headers: {
          'Content-Type': 'application/json',
          // Authorization: 'Bearer ' + authorization
          Authorization:  authorization
        }
      }).then(
        (response) => {
          response.json().then((res) => {
            baseinfo = res.data.baseinfo
            eduinfo = res.data.eduexps
            proinfo = res.data.projexps
            schoolinfo = res.data.schoolexps
            awardinfo = res.data.certificates
            skillinfo = res.data.skills

            renderbaseInfo()
            rendereduinfo()
            renderproinfo()
            renderschoolinfo()
            renderawardinfo()
            renderskillinfo()
          })
        },
        (error) => {
          console.log(error)
        }
      )

      // 渲染基本信息
      function renderbaseInfo() {
        console.log(location.href.path)
        let baseinfoname = document.querySelector('.baseinfoname')
        let baseinfoidcard = document.querySelector('.baseinfoidcard')
        let baseinfosex = document.querySelector('.baseinfosex')
        let baseinfobirth = document.querySelector('.baseinfobirth')
        let baseinfophone = document.querySelector('.baseinfophone')
        let baseinfonative = document.querySelector('.baseinfonative')
        let baseinfoemail = document.querySelector('.baseinfoemail')
        let baseinfounit = document.querySelector('.baseinfounit')
        baseinfoname.textContent = baseinfo.username
        baseinfoidcard.textContent = baseinfo.id_card_num
        baseinfosex.textContent = baseinfo.sex
        let born = baseinfo.id_card_num.slice(6, 14)

        baseinfobirth.textContent = born.replace(/(.{4})(.{2})/, '$1-$2-')
        baseinfophone.textContent = baseinfo.phone
        baseinfonative.textContent = baseinfo.nativeplace
        baseinfoemail.textContent = baseinfo.email
        baseinfounit.textContent = baseinfo.dept_name
      }

      // 渲染教育经历
      function rendereduinfo() {
        let eduinfowrapper = document.querySelector('.eduinfowrapper')
        if (!eduinfo.length) {
          eduinfowrapper.style.display = 'none'
          return
        }
        for (let i = 0; i < eduinfo.length; i++) {
          let item = `
          <div class="eduinfoitem active">
            <div class="school-avatar">
              <img src='/unilogo.svg' />
            </div>
            <div class="school-wrapper">
              <div class="school-info">
                <span class="school">${eduinfo[i].school}</span>
                <span class="time">${eduinfo[i].schooltime}</span>
              </div>
              <div class="master-info">
                <span class="item">${eduinfo[i].edubackground}</span>
                <span class="item">${eduinfo[i].college}</span>
                <span class="item">${eduinfo[i].rank}</span>
                <span class="item">${eduinfo[i].major}</span>
              </div>
            </div>
          </div>
            `
          let itemElement = document.createElement('div')
          itemElement.innerHTML = item
          eduinfowrapper.appendChild(itemElement)
        }
      }

      // 渲染项目经历
      function renderproinfo() {
        let proinfowrapper = document.querySelector('.proinfowrapper')
        if (!proinfo.length) {
          proinfowrapper.style.display = 'none'
          return
        }
        for (let i = 0; i < proinfo.length; i++) {
          let item = `
                    <div class="proinfoitem">
                      <div class="pro-basic-info">
                        <span class="pro-name">${proinfo[i].projname}</span>
                        <span>${proinfo[i].projtime}</span>
                      </div>
                      <div>
                        ${proinfo[i].projdesc}
                      </div>
                    </div>
                `
          let itemElement = document.createElement('div')
          itemElement.innerHTML = item
          proinfowrapper.appendChild(itemElement)
        }
      }

      // 渲染校园经历
      function renderschoolinfo() {
        let schoolinfowrapper = document.querySelector('.schoolinfowrapper')
        if (!schoolinfo.length) {
          schoolinfowrapper.style.display = 'none'
          return
        }
        for (let i = 0; i < schoolinfo.length; i++) {
          let item = `
                    <div class="schoolinfoitem">
                      <div class="collage-basic-info">
                        <span class="pro-name">${schoolinfo[i].assname}</span>
                        <span>${schoolinfo[i].workingtime}</span>
                      </div>
                      <div class="collage-dept-info">
                        <span class="job-name">${schoolinfo[i].position}</span>
                        <span>${schoolinfo[i].group}</span>
                      </div>
                      <div>
                        ${schoolinfo[i].desc}
                      </div>
                    </div>
                `
          let itemElement = document.createElement('div')
          itemElement.innerHTML = item
          schoolinfowrapper.appendChild(itemElement)
        }
      }

      // 渲染奖项证书
      function renderawardinfo() {
        let awardinfowrapper = document.querySelector('.awardinfowrapper')
        if (!awardinfo.length) {
          awardinfowrapper.style.display = 'none'
          return
        }
        for (let i = 0; i < awardinfo.length; i++) {
          let imgurl = []
          for (let j = 0; j < awardinfo[i].img.length; j++) {
            imgurl.push(awardinfo[i].img[j].url)
          }
          let item = `
                    <div class="awardinfoitem">
                      <div class="award-info">
                        <span class="award-name">${awardinfo[i].awardname}</span>
                        <span class="award-rank">${awardinfo[i].rank}</span>
                        <span class="award-time">${awardinfo[i].awardtime}</span>
                      </div>
                      <div class="award-images">
                        ${imgurl
                          .map((item) => {
                            return `<img class="awardinfopic" style="max-width:100px;max-height:100px;float:left" src=${item} />`
                          })
                          .join('')}
                      </div>
                    </div>
                `
          let itemElement = document.createElement('div')
          itemElement.innerHTML = item
          awardinfowrapper.appendChild(itemElement)
        }
      }

      // 渲染技能特长
      function renderskillinfo() {
        let skillinfowrapper = document.querySelector('.skillinfowrapper')
        let skillscategory = document.querySelector('.skills-category')
        if (!skillinfo.length) {
          skillinfowrapper.style.display = 'none'
          return
        }
        for (let i = 0; i < skillinfo.length; i++) {
          let item = `
                    <span>
                        <span>${skillinfo[i].skillname}</span>
                        <span class="degree degree-${skillinfo[i].degree}">${
            degreeName[skillinfo[i].degree]
          }</span>
                    </span>
                `
          let itemElement = document.createElement('span')
          itemElement.innerHTML = item
          skillscategory.appendChild(itemElement)
        }
      }

      function download() {
        const printBtn = document.querySelector('.print-btn')
        const idcard = document.querySelector('.baseinfoidcard')
        printBtn.style.display = 'none'
        idcard.textContent = '******************'
        window.print()
      }

      window.addEventListener('afterprint', function () {
        const printBtn = document.querySelector('.print-btn')
        const idcard = document.querySelector('.baseinfoidcard')
        printBtn.style.display = 'block'
        idcard.textContent = baseinfo.id_card_num
      })
    </script>
  </body>
</html>
