<template>
    <div class="create-paper-container">
        <h3 class="create-title">
            <span><span style="color: rgba(0, 0, 0, 0.45)" @click="$router.back()">能力测评 /
                </span><span>{{ mode + '测评' }}</span>
            </span>
        </h3>
        <a-alert class="tip" type="info" show-icon>
            <template #icon>
                <ExclamationCircleFilled />
            </template>

            <template #message>
                允许考生在有效期内的任意时间开始考试，每位考生的试卷内容不同，逐题生成，作答后不可返回修改。
            </template>
        </a-alert>
        <div class="create-paper-content">
            <div class="content-main">
                <AbilityTestFormSubject v-show="currentStep === 0" ref="abilityTestFormSubjectRef"
                    :paperInfo="paperInfo" @change="handlePaperInfoChange" />
                <AbilityTestFormConfig v-show="currentStep === 1" ref="abilityTestFormConfigRef" :paperInfo="paperInfo"
                    @change="handlePaperInfoChange" />
            </div>
            <div class="footer">
                <a-button @click="$router.back()">取消</a-button>
                <a-button v-if="currentStep > 0" @click="currentStep = currentStep - 1">上一步</a-button>
                <a-button v-if="currentStep < steps.length - 1" type="primary" :disabled="isNextDisabled"
                    @click="currentStep = currentStep + 1">下一步</a-button>
                <a-button v-else type="primary" @click="addPaper" :loading="loading">立即发布</a-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { nextTick, onUnmounted, ref, computed, reactive, watch, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { queryPaperQuestions } from '@/api/admin/questionManage'
import _ from 'lodash'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import AbilityTestFormSubject from './AbilityTestFormSubject.vue'
import AbilityTestFormConfig from './AbilityTestFormConfig.vue'
import { addabilityassessment, assessmentdetail, modifyabilityassessment } from '@/api/admin/abilityTest'

const route = useRoute()
const router = useRouter()


const abilityTestFormSubjectRef = ref<InstanceType<typeof AbilityTestFormSubject>>()
const abilityTestFormConfigRef = ref<InstanceType<typeof AbilityTestFormConfig>>()

// 获取当前模式
const mode = ref<'新增' | '编辑' | '克隆'>('新增')
mode.value = route.query.mode as any

const paperInfo = ref<Record<string, any>>({})

async function getDetail() {
    let res: any = await assessmentdetail({ id: route.query.id as string })
    try {
        if (res.cutscreen > 0) {
            res.iscutscreen = true
        } else {
            res.iscutscreen = false
            res.cutscreen = null
        }
        if (res.neterror > 0) {
            res.isneterror = true
        } else {
            res.isneterror = false
            res.neterror = null
        }
        res.hiddenname = !res.showname
        if (mode.value === '编辑') {
            res.startTime = dayjs(res.startTime)
            res.endTime = dayjs(res.endTime)
        }
        if (mode.value === '克隆') {
            delete res.id
            delete res.create_at
            delete res.endTime
            delete res.startTime
            delete res.examtime
            delete res.author
            delete res.teacher
        }
        paperInfo.value = res
        await nextTick()

    } catch (error) {

    }
}
if (mode.value !== '新增' && route.query.id) {
    getDetail()
}

// 步骤
const currentStep = ref(0)
const isNextDisabled = computed(() => !abilityTestFormSubjectRef.value?.validate())
const steps = reactive<{
    name: string
    status: 'wait' | 'process' | 'finish' | 'error'
}[]>([
    { name: '选择题库范围', status: 'wait' },
    { name: '测评配置', status: 'wait' },
])
async function handleStepClick(index: number, isBtnClick = false) {
    try {
        // 执行当前组件的校验和保存
        if (currentStep.value === 0) {
            // await basicInfoRef.value?.validateForm()
        } else if (currentStep.value === 1) {
            // await selectRef.value?.validateForm()
        }
        currentStep.value = index
        await nextTick()
    } catch (error) {
        console.log(error)
    }
}


// 创建|编辑试卷
const loading = ref(false)
async function addPaper() {
    await abilityTestFormConfigRef.value?.validateForm()
    const param: any = {
        action: mode.value === '编辑' ? 'modify' : 'add',
        paper: _.cloneDeep(paperInfo.value) // 这里要深克隆一下，否则下面改动会造成原本数据的变化
    }
    // 题库范围
    param.paper.select_category = abilityTestFormSubjectRef.value?.list.map(i => i.id)

    param.paper.cutscreen = param.paper.cutscreen || 0
    param.paper.neterror = param.paper.neterror || 0


    param.paper.startTime = dayjs(param.paper.startTime).format('YYYY-MM-DD HH:mm:ss')
    param.paper.endTime = dayjs(param.paper.endTime).format('YYYY-MM-DD HH:mm:ss')
    param.paper.showname = !param.paper.hiddenname
    loading.value = true
    try {
        if (mode.value === '新增') {
            await addabilityassessment(param)
        } else if (mode.value === '编辑') {
            await modifyabilityassessment(param.paper)
        }
        loading.value = false
        message.success(mode.value + '成功!')
        router.back()
    } catch (error) {
        loading.value = false
    }
}

// 处理每个步骤的表单提交
function handlePaperInfoChange(kv: Record<string, any>) {
    Object.assign(paperInfo.value, kv)
}

</script>

<style lang="less" scoped>
.create-paper-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    padding: 0 20px 20px 20px;

    .create-title {
        line-height: 48px;
        font-size: 14px;
        font-weight: 600;
    }

    :deep(.tip) {
        border-radius: 4px;
        background: #f1f4fe;
        border: 1px solid #5478ee;

        .ant-alert-message {
            font-size: 12px !important;
        }
    }

    .create-paper-content {
        min-height: 0;
        flex: 1;
        display: flex;
        margin-top: 16px;
        flex-direction: column;
        padding: 24px;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);

        .content-main {
            display: flex;
            flex-direction: column;
            position: relative;
            flex: 1;
            min-height: 0;
            min-width: 0;
        }

        .footer {
            width: 100%;
            display: flex;
            margin-top: 24px;
            border-top: 1px solid #e8e8e8;
            padding-top: 24px;

            .ant-btn {
                border-radius: 8px;
                font-size: 12px;
                margin-right: 8px;
            }
        }
    }
}
</style>