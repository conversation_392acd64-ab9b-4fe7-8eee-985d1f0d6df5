export default class FormulaModule {
  quill: any
  isComposing: boolean
  compositionStartIndex: any
  compositionstartWatch: boolean
  constructor(quill) {
    this.quill = quill
    this.isComposing = false // 是否正在拼音输入
    this.compositionStartIndex = null
    this.compositionstartWatch = false
    // 监听文本变化事件
    quill.root.addEventListener('compositionstart', (event) => {
      if (this.compositionstartWatch)
        return
      const range = quill.getSelection()

      this.compositionstartWatch = true
      if (range && range.index !== null) {
        this.compositionStartIndex = range.index
      }
    })

    // 监听 compositionend 事件
    quill.root.addEventListener('compositionend', (event) => {
      const range = quill.getSelection()

      const oldOps = quill.getContents().ops
      const oldOpsLen = oldOps.length
      if (this.compositionStartIndex + event.data.length !== range.index && range) {
        quill.deleteText(this.compositionStartIndex, range.index, 'user')
        quill.insertText(this.compositionStartIndex, event.data, 'user')

        if (oldOps[oldOpsLen - 1].insert.endsWith('\n') && oldOps[oldOpsLen - 2].insert.formula) {
          // quill.insertEmbed(this.compositionStartIndex + event.data.length, 'formula', oldOps[oldOpsLen - 2].insert.formula, 'user')
          const splitArr = oldOps[0].insert.split('\n')
          splitArr[splitArr.length - 1] = event.data
          oldOps[0].insert = splitArr.join('\n')
          quill.setContents(oldOps)
          quill.setSelection(this.compositionStartIndex + event.data.length)
        }
      }
      this.compositionStartIndex = null
      this.compositionstartWatch = false
    })
  }
}