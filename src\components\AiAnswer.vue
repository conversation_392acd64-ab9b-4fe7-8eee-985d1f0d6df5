<template>
    <div style="display: flex;align-items: center;margin-top: 16px;">
        <a-button :loading="getAiAnswerLoading" class="common-ai-button"
            style="font-size: 14px; margin-left: 2px;border-radius: 8px;" @click="handleGetAiAnswer">
            <template #icon><img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 14px;" /></template>
            答题
        </a-button>
        <a-tooltip placement="right" overlayClassName="light">
            <template #title>
                <span>自动生成当前题目的答案解析</span>
            </template>
            <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
    </div>
    <template v-if="aiAnswer || aiExplain">
        <div class="ai-answer-panel">
            <div>
                <div class="label">AI 答案</div>
                <div class="value">{{ aiAnswer }}</div>
            </div>
            <div style="margin-top: 8px;">
                <div class="label">答题解析</div>
                <div class="value">
                    <FoldText :text="aiExplain" :fold-line-clamp="3" background-color="#f5f5f5" :feedback-params="{
                        apiname: apiName,
                        input: getaianswerInput,
                        output: {
                            answer: aiAnswer,
                            explain: aiExplain
                        }
                    }"></FoldText>
                </div>
            </div>
        </div>
        <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
            <template #icon>
                <InfoCircleFilled />
            </template>
            <template #message>
                温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
            </template>
        </a-alert>
    </template>
</template>

<script lang="ts" setup>
import { getAiAnswer } from '@/api/exam';
import { getaiinterviewanswer } from '@/api/interview';
import { QuestionEnum } from '@/models/questionModel';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue'
import FoldText from './FoldText.vue';
import { ExclamationCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'

const props = withDefaults(defineProps<{
    apiName?: 'getaianswer' | 'getaiinterviewanswer'
    formState: {
        complicatedediting: boolean
        body: string
        complexcontent: string
        type: number
        options?: any[]
        [k: string]: any
    }
}>(), {
    apiName: 'getaianswer'
})

const API_MAP = {
    getaianswer: getAiAnswer,
    getaiinterviewanswer
}


const aiAnswer = ref('')
const aiExplain = ref('')
const getAiAnswerLoading = ref(false)
const getaianswerInput = computed(() => {
    let type = props.formState.type
    let body = props.formState.complicatedediting ? props.formState.complexcontent : props.formState.body
    let options = props.formState.options
    return { body, type, options }
})
function clearAiAnswer() {
    aiAnswer.value = ''
    aiExplain.value = ''
}
async function handleGetAiAnswer() {
    clearAiAnswer()
    let type = props.formState.type
    let body = props.formState.complicatedediting ? props.formState.complexcontent : props.formState.body
    if (!body) return message.error('题干内容不能为空')
    if (props.apiName === 'getaianswer' && props.formState.options?.every((i: any) => !i.content.trim()) && [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['排序题']].includes(type)) {
        return message.error('选项内容不能都为空')
    }
    getAiAnswerLoading.value = true
    try {
        let res = await API_MAP[props.apiName]({ body, type, options: props.formState.options! })
        aiAnswer.value = res.answer
        aiExplain.value = res.explain
    } catch (error) {
        console.log(error)
    } finally {
        getAiAnswerLoading.value = false
    }
}

</script>

<style lang="less" scoped>
.ai-answer-panel {
    background-color: #f5f5f5;
    width: 752px;
    border-radius: 8px;
    padding: 16px 48px;
    margin-top: 16px;

    >div {
        display: flex;

        .label {
            width: 80px;
            font-size: 12px;
            color: #626262;
            margin-right: 16px;
        }

        .value {
            flex: 1;
            min-width: 0;
            font-size: 12px;
        }
    }
    .ant-alert-info {
        border: none;
    }
}
</style>