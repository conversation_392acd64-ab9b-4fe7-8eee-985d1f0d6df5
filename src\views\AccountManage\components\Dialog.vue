<template>
  <div>
    <a-modal
      :title="$props.accountData!.id ? '编辑账号' : '新增账号'"
      :maskClosable="false"
      v-model:visible="visible"
      :confirm-loading="confirmLoading"
      :footer="null"
      @cancel="handlecancelSuccess"
    >
      <account-form
        ref="formRef"
        :formData="$props.accountData"
        @onsubmitSuccess="handleSubmitSuccess"
        @oncancelSuccess="handlecancelSuccess"
      />
    </a-modal>
  </div>
</template>
<script lang="ts">
import { ref, defineComponent, watchEffect, nextTick } from 'vue'
import AccountForm from './AccountForm.vue'
import { addAccount } from '@/api/admin/accountManage'
import { message } from 'ant-design-vue'
export default defineComponent({
  components: { AccountForm },
  emits: ['cancelDialog', 'refreshAccountList'],
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    accountData: Object
  },
  setup(props, { emit }) {
    const visible = ref(false)
    const formRef = ref<any>(null)
    const confirmLoading = ref<boolean>(false)

    watchEffect(() => {
      visible.value = !!props.isVisible
      // if (visible.value) {
      //   nextTick(() => {
      //     // DOM 渲染后
      //     formRef.value.resetForm()
      //     Object.assign(formRef.value.formState, props.accountData)
      //   })
      // }
    })

    // const addTeacherAccount = (params: Object) => {
    //   addAccount(params).then(() => {
    //     visible.value = false
    //     message.success(props.accountData!.id ? '编辑成功!' : '新增账号成功！')
    //     emit('cancelDialog', visible.value)
    //     emit('refreshAccountList')
    //   })
    // }
    // const handleOk = () => {
    //   formRef.value
    //     .onSubmit()
    //     .then((formData: any) => {
    //       if (!formData.id) {
    //         delete formData.id
    //       }
    //       const params = {
    //         action: formData.id ? 'modify' : 'add',
    //         teacher: { ...formData }
    //       }
    //       addTeacherAccount(params)
    //     })
    //     .catch((err: Error) => {
    //       console.log(err)
    //     })
    // }

    // // 取消事件
    // const handleCancel = (e: Event) => {
    //   e?.stopPropagation()
    //   emit('cancelDialog', visible.value)
    // }

    const handleSubmitSuccess = () => {
      visible.value = false
      message.success(props.accountData!.id ? '编辑成功!' : '新增账号成功！')
      emit('cancelDialog', visible.value)
      emit('refreshAccountList')
    }

    const handlecancelSuccess = () => {
      emit('cancelDialog', false)
    }
    return {
      visible,
      formRef,
      confirmLoading,
      handleSubmitSuccess,
      handlecancelSuccess
    }
  }
})
</script>
