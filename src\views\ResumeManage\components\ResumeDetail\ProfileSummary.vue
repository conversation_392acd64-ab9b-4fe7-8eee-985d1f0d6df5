<script setup lang="ts">
import type { SummaryDataProps } from './shared'
import { computed, onMounted, ref } from 'vue'
import 'echarts-wordcloud'

const props = withDefaults(defineProps<{
  summaryData: SummaryDataProps
}>(), {
  summaryData: () => ([
    { word: '乐观', weight: 10 },
    { word: '数学好', weight: 20 },
    { word: '六边形战士', weight: 30 },
    { word: '善于沟通', weight: 40 },
    { word: '爱学习', weight: 50 },
    { word: '资深程序员', weight: 60 },
    { word: '学历优秀', weight: 70 },
    { word: '逻辑能力强', weight: 80 },
    { word: '多次获奖', weight: 90 },
    { word: '严谨', weight: 100 },
    { word: '开朗', weight: 90 },
  ]),
})

const chartRef = ref<any>(null)

// 模拟简历摘要数据
const colorPalette = [
  '#000000', 
  '#4ECDC4', 
  '#D96E00', 
  '#4E6688',
  '#FB5607', 
  '#8338EC', 
  '#5478EE', 
  '#FF006E',
]

const option = computed(() => ({
  series: [
    {
      type: 'wordCloud', // 词云
      shape: 'pentagon', // 可用的形状有(circle)圆形(默认)、(cardioid)心形，(diamond)菱形，(triangle-forward)三角形向前，(triangle)三角形，(pentagon)五边形和(star)星形。
      keepAspect: false, // 保持maskImage的宽高比或1:1的形状，他的选项是支持从echarts-wordcloud@2.1.0
      // 一个轮廓图像，其白色区域将被排除在绘制文本之外
      // 意思就是可以通过图片，来自定义词云的形状
      // maskImage: maskImage,

      // 设置显示区域的位置以及大小
      left: '0',
      top: '0',
      width: '100%',
      height: '100%',
      right: '0',
      bottom: '0',
      // 数据中的值将映射到的文本大小范围。默认大小为最小12px，最大60px。
      sizeRange: [12, 28],
      // 网格尺寸越大，单词之间的间距越大。
      gridSize: 24,
      // 文本旋转范围和步进度。文本将通过rotationStep:45在[- 90,90]范围内随机旋转
      rotationRange: [0, 0],
      rotationStep: 0,
      // 以像素为单位的网格大小，用于标记画布的可用性

      // 设置为true，允许文字部分在画布外绘制。
      // 允许绘制大于画布大小的单词
      // 从echarts-wordcloud@2.1.0开始支持此选项
      drawOutOfBound: false,
      // 如果字体太大而无法显示文本，
      // 是否收缩文本。如果将其设置为false，则文本将不渲染。如果设置为true，则文本将被缩小。
      // 从echarts-wordcloud@2.1.0开始支持此选项
      shrinkToFit: false,
      // 执行布局动画。当有大量的单词时，关闭它会导致UI阻塞。
      layoutAnimation: true,
      // 全局文本样式
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: '',
        color(params: any) {
          return params?.data?.color 
            || colorPalette[Math.floor(Math.random() * colorPalette.length)]
        },
      },
      emphasis: {
        // focus: 'self',
        // textStyle: {
        //   textShadowBlur: 1,
        //   textShadowColor: '#333'
        // }
      },
      // data属性中的value值却大，权重就却大，展示字体就却大
      data: props.summaryData.map(item => ({
        name: item.word,
        value: item.weight,
      })),
    },
  ],
}))

onMounted(() => {
  setTimeout(() => {
    chartRef.value?.resize()
  }, 600)
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        简历摘要
      </h3>
    </div>
    <div class="flex flex-wrap gap-2">
      <VChart ref="chartRef" class="w-100% h-[260px]" :option="option" autoresize />
    </div>
  </div>
</template>