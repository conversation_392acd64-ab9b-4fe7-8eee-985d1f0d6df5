<template>
    <div class="paper-manage-page-wrap">
      <div class="title-wrap">
        <span class="title">能力测评</span>
        <div class="btns">
          <a-button type="primary" @click="newPaper">新增测评</a-button>
          <a-button :disabled="!checkedIds.length" @click="handleMultiDelete">批量删除</a-button>
        </div>
      </div>
      <div class="filter-wrapper">
        <div style="flex: 1;">
          <div class="filter-wrapper-row">
            <Switch :columns="stateColums" v-model="params.status"></Switch>
            <div style="display: flex;align-items: center;">
              <a-input-search v-model:value.trim="searchValue" placeholder="请输入考试名称/教师名称" allow-clear class="select-wrap" @blur="handleSearch" @search="handleSearch" />
            </div>
          </div>
          <div class="filter-wrapper-row more-filter" v-if="filterMoreVisible">
            <div class="filter-item">
              <span class="filter-label">创建人</span>
              <a-select v-model:value="params.create_by" placeholder="全部" :options="createrList" allowClear />
            </div>
            <div class="filter-item">
              <span class="filter-label">创建时间</span>
              <a-range-picker v-model:value="params.start_time_range" :placeholder="['最早开始时间', '最晚开始时间']" valueFormat="YYYY-MM-DD">
                <template #suffixIcon>
                  <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                </template>
              </a-range-picker>
            </div>
          </div>
        </div>
        <div class="filter-btns">
            <div class="filter-btn filter-more" @click="filterMoreVisible = !filterMoreVisible">
                <span v-if="filterMoreActiveNumber" class="filter-number">{{ filterMoreActiveNumber }}</span>
                <img v-else src="@/assets/icons/svg/filter2.svg" alt="">
                <span style="margin-left: 4px;">更多条件</span>
            </div>
            <div class="filter-btn" style="margin-top: 16px;" @click="handleReset" v-if="filterMoreVisible">
                <img width="16" height="16" style="margin-right: 4px;" src="@/assets/images/paper/clear.png" alt="">
                <div class="label">清空条件</div>
            </div>
        </div>
      </div>
      <ListWrapper ref="listWrapperRef" :card-min-width="580" :params="params" :getListFn="getListFn">
        <template #item="{ item }">
          <Card
            :paperInfo="item"
            :checked="checkedIds.includes(item.id)"
            :search-text="params.name"
            @changeCheck="handleChangeChecked(item.id)"
            @deletePaperOk="handleDeletePaper(item.id)" />
        </template>
      </ListWrapper>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, createVNode } from 'vue'
  import { useRouter } from 'vue-router'
  import { Modal, message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import _ from 'lodash'
  import Switch from '@/components/Switch.vue';
  import { PaperStatus } from '@/models/paperModel'
  import { delabilityassessment, getabilityassessment }  from '@/api/admin/abilityTest'
  import ListWrapper from '@/components/ListWrapper.vue'
  import Card from './AbilityTestCard.vue'
  import { useState } from '@/hooks'
  import { useStore } from 'vuex'
  
  const router = useRouter()
  const store = useStore()
  
  // 更多条件
  const filterMoreVisible = ref(false)
  const filterMoreActiveNumber = computed(() => {
      const filterMoreKeys = ['create_by', 'start_time_range']
      return filterMoreKeys.reduce((pre, cur) => {
          if (params.value[cur] !== rawParams[cur]) pre++
          return pre
      }, 0)
  })
  
  // 考试状态
  const stateColums = [
      { label: '全部', value: null },
      { label: '未开始', value: PaperStatus.NOT_START },
      { label: '考试中', value: PaperStatus.DURING_EXAMS },
      { label: '已结束', value: PaperStatus.OVER },
  ]
  
  const createrList = [ // 创建人筛选项
    { label: '本人', value: 1 },
    { label: '其他', value: 0 },
  ]
  
  const checkedIds = ref<string[]>([]) // 已选试卷ids
  const handleChangeChecked = (id: string) => { // 修改考试选中状态
    if (checkedIds.value.includes(id)) {
      checkedIds.value = checkedIds.value.filter(el => el !== id)
    } else {
      checkedIds.value = [...checkedIds.value, id]
    }
  }
  
  // 模糊查询
  const searchValue = ref('')
  function handleSearch() {
      params.value.name = searchValue.value
  }
  
  const [params, resetParams, rawParams] = useState<{
    action: 'query'
    name: string
    status: PaperStatus | null
    create_by?: number | null
    start_time_range: null | string[]
    only_focus: boolean
  }>({
    action: 'query',
    name: '', // 关键词
    status: null, // 考试状态
    create_by: undefined, // 创建人
    start_time_range: null, // 开始结束时间
    only_focus: false
  })
  
  const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()
  
  const getListFn = (params: any) => {
      return getabilityassessment({
          ...params,
          status: params.status === null ? null : [params.status],
          create_by: params.create_by === undefined ? null : [params.create_by],
      })
  }
  
  // 重置
  function handleReset() {
      searchValue.value = ''
      resetParams()
  }
  
  // 新建考试
  const newPaper = () => {
    // 如果当前部门不是测试部门，则提示不可用
    if (!store.getters.userInfo.dept_name.includes('测试') && store.getters.userInfo.dept_name !== '智能软件研究中心') {
      message.error('本功能处于演示阶段，暂不可用')
      return
    }
    router.push({ 
      name: 'ability-test-form',
      query: {
        mode: '新增'
      }
   })
  }
  
  // 考试卡片数据删除后，从list中删除掉当前数据
  const handleDeletePaper = (id: string) => {
    listWrapperRef.value?.handleBatchDelete([id])
  }
  
  // 批量删除
  const handleMultiDelete = () => {
    const params = {
      action: 'del',
      id: checkedIds.value,
    }
    Modal.confirm({
      title: () => `确定删除勾选的${checkedIds.value.length}场考试?`,
      icon: () => createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          await delabilityassessment(params)
          message.success('批量删除成功！')
          listWrapperRef.value?.handleBatchDelete(checkedIds.value)
          checkedIds.value = []
        } catch (error) {
          // do nothing
          console.log(error)
        }
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onCancel() {}
    })
  }
  
  </script>
  
  <style lang="less" scoped>
  .filter-wrapper {
      padding: 24px;
      box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
      background: #ffffff;
      border-radius: 8px;
      margin: 0px 20px 20px;
      display: flex;
  
      :deep(.filter-wrapper-row) {
          display: flex;
          justify-content: space-between;
          &.more-filter {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-row-gap: 16px;
          }
  
          +.filter-wrapper-row {
              margin-top: 16px;
          }
  
          .ant-select,
          .ant-picker,
          .ant-input-search {
              width: 240px;
          }
      }
  
      .filter-item {
          display: flex;
          align-items: center;
          .filter-label {
              margin-right: 16px;
              color: #626262;
          }
          &:nth-child(3n+2) {
              justify-content: center;
          }
          &:nth-child(3n+3) {
              justify-content: flex-end;
          }
      }
  
      .filter-btns {
          margin-left: 8px;
      }
  
      .filter-btn {
          width: 108px;
          height: 32px;
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.15);
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
  
          &.filter-more {
              .filter-number {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  border-radius: 50%;
                  background-color: #FF4D4F;
                  color: #fff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 10px;
              }
          }
  
      }
  
  
  }
  .paper-manage-page-wrap {
    width: 100%;
    height: 100%;
    min-width: 1150px;
    display: flex;
    flex-direction: column;
    .title-wrap {
      height: 64px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        height: 48px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0,0,0,0.85);
        text-align: left;
        line-height: 48px;
      }
      .btns {
        height: 32px;
        :deep(.ant-btn) {
          border-radius: 8px;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
  </style>
  