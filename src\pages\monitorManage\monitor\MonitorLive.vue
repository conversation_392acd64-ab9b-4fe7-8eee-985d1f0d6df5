<template>
  <div class="live-monitor-container">
    <a-spin wrapperClassName="common-full-spin" :spinning="loading" tip="正在加载监控画面...">
      <template v-if="monitorlivePhotos.length">
        <div class="monitor-live-photos">
          <template v-for="(item, index) in monitorlivePhotos" :key="item.id">
            <div class="live-item" :style="getItemStyle(item.status)">
              <div class="live-img-title">
                <div class="left-info">
                  <svg-icon v-if="item.status === -1" name="icon_err" width="16px" height="16px" />
                  <svg-icon v-if="item.status === -2" name="icon_wait" width="16px" height="16px" />
                  <svg-icon v-if="item.status === 1" name="icon_ing" width="16px" height="16px" />
                  <svg-icon v-if="item.status === 2" name="icon_end" width="16px" height="16px" />
                  <span class="username" :title="item.username">{{ item.username }}</span>
                </div>
                <div v-if="![0, 2].includes(item.status)" class="right-icon">
                  <svg-icon name="expand_img" width="14px" height="14px" class="expand-icon"
                    @click="showSingleStudentMonitor(item)" />
                  <svg-icon name="message" width="16px" height="16px" class="expand-icon"
                    @click="showMessageModal(item.stupid, item.username)" />
                </div>
              </div>
              <div v-if="item.status === 0" class="monitor-wrapper">
                <div class="temp-img">
                  <img src="@/assets/icons/svg/wait.svg" />
                  <span>未考试</span>
                </div>
              </div>
              <div v-else-if="item.status === 2" class="monitor-wrapper">
                <div class="temp-img">
                  <img src="@/assets/icons/svg/finished-paper.svg" />
                  <span>已交卷</span>
                </div>
              </div>
              <div v-else-if="item.status === -2" class="monitor-wrapper">
                <div class="temp-img">
                  <img src="@/assets/icons/svg/wait.svg" />
                  <span>等待中</span>
                </div>
              </div>
              <div v-else class="monitor-wrapper">
                <!-- 电脑监控画面 -->
                <div class="computer-monitor" v-if="monitorTuple[0]">
                  <div v-if="!item.now_face" class="monitor-temp-img">未要求开启电脑监控</div>
                  <template v-else>
                    <img v-if="item.now_face.length" :src="item.now_face[0]" />
                    <div v-else class="monitor-temp-img" style="height: 200px;">
                      <svg-icon name="no-signal" width="100px" height="100px" />
                      <span>电脑信号丢失</span>
                    </div>
                  </template>
                </div>
                <!--  -->
                <!-- 手机监控画面 -->
                <div class="photo-monitor" v-if="monitorTuple[1]">
                  <div v-if="!item.monitor_phone" class="monitor-temp-img">未要求开启手机监控</div>
                  <template v-else>
                    <img v-if="item.monitor_phone.length" :src="item.monitor_phone[0]" />
                    <div v-else class="monitor-temp-img" style="height: 200px;">
                      <svg-icon name="no-signal" width="100px" height="100px" />
                      <span>手机信号丢失</span>
                    </div>
                  </template>
                </div>
                <!--  -->
              </div>
            </div>
          </template>
        </div>
        <div class="pagination-style">
          <a-pagination v-model:current="paginationConfig.page" v-model:pageSize="paginationConfig.pageSize"
            :total="paginationConfig.total" show-size-changer :pageSizeOptions="['5', '10', '20']"
            @change="onShowSizeChange" />
        </div>
      </template>
      <div class="common-no-data" v-else style="background-size: 289px;"></div>
    </a-spin>
  </div>
  <div v-if="singleStudentMonitorVisible" class="expand-img-wrapper">
    <div class="header">
      <span class="title">考生实时监控</span>
      <svg-icon name="comscreen" width="16px" height="16px" class="close-icon"
        @click="singleStudentMonitorVisible = false" />
    </div>
    <SingleStudentMonitor v-bind="singleStudentMonitorParams"></SingleStudentMonitor>
  </div>
  <MessageSendModal :title="`发送消息给${stuName}`" v-model:visible="messageVisible" :stupid="stupid"></MessageSendModal>
  
  <Teleport :disabled="isSplitScreen" to="#teleported-monitor-btnbox" v-if="monitorlivePhotos?.length">
    <div>
      <a-button type="primary" @click="handleMultipleMonitorScreen">多屏监考</a-button>
      <a-tooltip placement="right" overlayClassName="light">
        <template #title>
          <span>利用多块屏幕进行监考</span>
        </template>
        <svg-icon class="common-info-icon" name="info2" style="width: 16px; height: 16px;"></svg-icon>
      </a-tooltip>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { getPaperNameByIds, monitorscreen } from '@/api/admin/paperManage'
import { ref, watch, onBeforeUnmount, reactive, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import MessageSendModal from '@/pages/monitorManage/components/MessageSendModal.vue'
import SingleStudentMonitor from '@/pages/monitorManage/SingleStudentMonitor.vue'
import { useIntervalFn, useDebounceFn, useLocalStorage } from '@vueuse/core'
import moment from 'moment'
import { useDelayIntervalFn } from '@/hooks/useDelayIntervalFn'
import _ from 'lodash'

const route = useRoute()

const props = withDefaults(defineProps<{
  /** 更新频率多少秒一次 */
  updateFrequency?: number
  /** 本次进入的时间戳 */
  timeStamp: string
  /** 给过滤条件 */
  filter?: {
    username: string
    /** 多场考试 , 分隔 */
    paperIds: string
  }
}>(), {
  updateFrequency: 30,
  filter: () => ({
    username: '',
    paperIds: ''
  })
})

const timeStamp = route.query.timeStamp ?? props.timeStamp

/** 是否是分屏（如果是分屏就禁用传送门teleport，否则会报错） */
const isSplitScreen = route.name === 'multipleMonitorScreen'

const searchContent = useLocalStorage('monitorlive__searchContent__' + timeStamp, '')
const paperIds = useLocalStorage('monitorlive__paperIds__' + timeStamp, '')

const monitorlivePhotos = ref<any[]>([])
const paginationConfig = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const getItemStyle = (status: number) => {
  switch (status) {
    case -2:
      return { borderColor: '#5478EE', background: '#f1f4fe' }
    case -1:
      return { borderColor: '#FF4D4F', background: '#fcf0f0' }
    case 0:
      return { borderColor: '#D9D9D9', background: '#f0f0f0' }
    case 1:
      return { borderColor: '#5478EE', background: '#f1f4fe' }
    case 2:
      return { borderColor: '#D9D9D9', background: '#f0f0f0' }
  }
}

const loading = ref(false)
const showLoading = ref(true)
let requestId = ''
async function getLivePhotoList() {
  let currentRequestId = _.uniqueId()
  requestId = currentRequestId

  if (!props.filter.paperIds && !paperIds.value) return monitorlivePhotos.value = []
  if (showLoading.value) {
    loading.value = true
    showLoading.value = false
  }
  try {
    const route = useRoute()
    let res = await monitorscreen({
      // templeteIdList: [...props?.filter?.paperIds.split(',') || paperIds.value],
      templeteIdList: [...paperIds.value.split(',') ],
      basePager:{
        current: paginationConfig.value.page,
        size: paginationConfig.value.pageSize
      },
      page: paginationConfig.value.page,
      per_page: paginationConfig.value.pageSize,
      condition: props.filter.username || searchContent.value
    }) as any
        
    if (requestId !== currentRequestId) return

    monitorlivePhotos.value = res.cand_status
    paginationConfig.value.total = res.total
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const debouncedGetLivePhotoList = useDebounceFn(() => {
  showLoading.value = true
  paginationConfig.value.page = 1
  getLivePhotoList()
}, 1000)

// 定时器
const { pause, resume, isActive } = useDelayIntervalFn(getLivePhotoList, 1000 * props.updateFrequency, { immediateCallback: true })

const onShowSizeChange = (current: number, pageSize: number) => {
  showLoading.value = true
  paginationConfig.value.page = current
  paginationConfig.value.pageSize = pageSize
  getLivePhotoList()
}

watch([() => props.filter.username, searchContent], debouncedGetLivePhotoList, { deep: true })
watch([() => props.filter.paperIds, paperIds], () => {

  showLoading.value = true
  paginationConfig.value.page = 1
  resume()
})


// ---------------------------------- 布局 ---------------------------------------------
// 根据ids获取试卷信息
const paperInfos = ref<{
  id: string
  name: string
  endTime: string
  isEnd?: boolean
  [key: string]: any
}[]>([])
async function getPaperInfos() {
  try {
    let res = await getPaperNameByIds({ ids: route.query.paperIds.split(',') }) as any
    // 如果所有考试都结束了则关闭浏览器当前标签页
    res.forEach((item: any) => {
      item.isEnd = moment(item.endTime).isSameOrBefore(moment())
    })
    if (res.every((i: any) => i.isEnd)) {
      window.close()
    }
    paperInfos.value = res
  } catch (error) {
    console.log(error)
  }
}

/** 本次监考的所有考试监考的配置取并集 */
const monitorTuple = reactive<[computermonitor: boolean, phonemonitor: boolean]>([false, false])

watch(paperIds, async (ids) => {
  await getPaperInfos()
  monitorTuple.fill(false)
  paperInfos.value.forEach((item: any) => {
    if (ids.includes(item.id)) {
      if (item.computermonitor === 1) monitorTuple[0] = true
      if (item.phonemonitor === 1) monitorTuple[1] = true
    }
  })
}, { immediate: true })

// -----------------------------------------------------------------------------


// 发送消息
const stupid = ref('')
const stuName = ref('')
const messageVisible = ref(false)
function showMessageModal(id: string, name: string) {
  stupid.value = id
  stuName.value = name
  messageVisible.value = true
}

// 单个考生的监考画面
const singleStudentMonitorVisible = ref(false)
const singleStudentMonitorParams = reactive({
  stupid: '',
  paperId: '',
  paperName: '',
  computerMonitor: false,
  phoneMonitor: false,
  status: 0
})
function showSingleStudentMonitor(item: any) {
  singleStudentMonitorParams.paperId = item.paper
  singleStudentMonitorParams.stupid = item.stupid
  singleStudentMonitorParams.paperName = item.paper_name
  singleStudentMonitorParams.computerMonitor = item.computermonitor === 'yes'
  singleStudentMonitorParams.phoneMonitor = item.phonemonitor === 'yes'
  singleStudentMonitorParams.status = item.status
  singleStudentMonitorVisible.value = true
}


// 多屏监考
const multipleWindowList: Window[] = []
function handleMultipleMonitorScreen() {
  // 获取屏幕尺寸
  const width = screen.width;
  const height = screen.height;
  // 新起一个窗口
  const windowInstance = window.open(`/#/multipleMonitorScreen?paperIds=${route.query.paperIds}&updateFrequency=${props.updateFrequency}&timeStamp=${timeStamp}&multi=1`, '_blank', `width=${width},height=${height}`)
  multipleWindowList.push(windowInstance!)
}
// 销毁后退出所有多屏
onUnmounted(() => {
  multipleWindowList.forEach(item => item?.close())
})

</script>

<style lang="less" scoped>
.live-monitor-container {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: auto;
  min-width: 1150px;

  &:not(.stu-monitor .live-monitor-container) {
    height: 100vh;
  }

  .monitor-live-photos {
    flex: 1;
    overflow: auto;
    padding-right: 10px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-column-gap: 16px;
    grid-row-gap: 16px;

    .live-item {
      position: relative;
      background: #f3f7f1;
      border: 1px solid #52c41a;
      border-radius: 8px;
      overflow: hidden;
      padding: 8px;
      padding-top: 0;
      display: flex;
      flex-direction: column;

      .temp-img {
        // height: 428px;
        min-height: 210px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        height: 100%;
      }

      .monitor-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;

        .computer-monitor,
        .photo-monitor {
          margin: 2px 0;
          height: 210px;

          .monitor-temp-img {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: rgba(0, 0, 0, 0.45);
          }

          img {
            border-radius: 4px;
            object-fit: contain;
          }

          overflow: hidden;
        }

        .computer-monitor {
          border-radius: 4px;

          &:not(:has(.monitor-temp-img)) {
            display: flex;
            align-items: center;
            background-color: #D9D9D9;
          }

          img {
            width: 100%;
            max-height: 100%;
          }
        }

        .photo-monitor {
          text-align: center;
          border-radius: 4px;

          &:not(:has(.monitor-temp-img)) {
            background-color: #D9D9D9;
          }

          img {
            object-fit: contain;
            width: auto;
            height: 100%;
          }
        }
      }

      .live-img-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 28px;
        font-size: 14px;

        .left-info {
          display: flex;
          align-items: center;

          .username {
            margin-left: 8px;
            margin-top: -2px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .right-icon {
          margin-right: 10px;
          margin-top: 2px;
        }

        .expand-icon {
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
  }

  .pagination-style {
    margin-top: 20px;
    width: 100%;
    text-align: center;
  }
}

.expand-img-wrapper {
  position: absolute;
  inset: 0;
  z-index: 999;
  overflow: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 24px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28px;

    .title {
      font-size: 20px;
      font-weight: bold;
    }
  }

  .userinfo {
    margin-bottom: 30px;

    .basic-info {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;

      .username {
        font-size: 24px;
        font-weight: bold;
        margin-right: 16px;
      }

      .sex {
        width: 20px;
        height: 18px;
        background: #faf4ee;
        border-radius: 4px;
        text-align: center;
        color: #d96e00;
        margin-right: 8px;
      }

      .age {
        width: 35px;
        height: 18px;
        background: #faf4ee;
        border-radius: 4px;
        text-align: center;
      }
    }

    .bachor-info {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 8px;

      .school {
        margin-right: 24px;
      }
    }

    .send-btn {
      border-radius: 8px;
      font-size: 14px;
    }
  }

  .other-info-item {
    display: flex;
    margin-bottom: 16px;
    font-size: 12px;

    .label {
      width: 80px;

      color: #626262;
    }

    .value {
      color: #181818;
    }
  }

  .close-icon {
    cursor: pointer;
  }
}
</style>