<script setup lang="ts">
import DiffType from './component/diffType.vue'

const emits = defineEmits(['setDisabledCreate'])
const diffTypeRef = useTemplateRef('diffType')
function getRequestParamsPipe() {
  return diffTypeRef.value!.getRequestParamsPipe()
}
function validateContentRequired() {
  return diffTypeRef.value!.validateContentRequired()
}
function setDisabledCreate(val: boolean) {
  emits('setDisabledCreate', val)
}
defineExpose({
  contentList: () => diffTypeRef.value!.contentList,
  getRequestParamsPipe,
  validateContentRequired,
})
</script>

<template>
  <div class="dh pt-[20px] relative flex items-center justify-center content-center flex-wrap w-full">
    <DiffType ref="diffType" @set-disabled-create="setDisabledCreate" />
  </div>
</template>

<style scoped lang="scss">
.dh {
      height: calc(100vh - 400px);
      @media screen and (max-width: 1440px) {
       height: calc(100vh - 380px);
      }
      padding-left: 20px;
      padding-right: 10px;
      overflow-y: auto;
}
.normal_bg{
      background: linear-gradient(180deg,#f1f4ff, #f8faff);
}
</style>