<template>
    <div class="question-manage-container">
        <div v-if="initialOptions" style="display: flex;align-items: center;margin-bottom: 16px;">
            必答题量
            <!-- <a-input-number v-model:value="options" :disabled="optionsDisabled" size="small" :precision="0" :min="1" :max="Math.max(listRef?.selectedRows.length! - 1, 1)" :controls="false" style="width: 40px;border-radius: 8px;margin-left: 16px;margin-right: 7px;"></a-input-number> -->
            {{ options }} / {{ listRef?.selectedRows.length }}
            <a-tooltip placement="right" overlayClassName="light">
                <template #title>
                    <span>仅支持N选一，分母表示可选择的题量，分子表示考生需作答的题量</span>
                </template>
                <svg-icon class="common-info-icon" name="info2" style="margin-left: 8px;"></svg-icon>
            </a-tooltip>
        </div>
        <div class="question-manage-content">
            <div class="questions-subjects">
                <SubjectTree ref="treeRef" :allSelectRows="allSelectRows" :footer="true" @select="handleSelectSubject"
                    @visible-change="handleTreeVisibleChange" />
            </div>
            <div class="questions-list">
                <div v-if="isOfficialSubject && store.getters.userInfo?.tryout" class="locked-box">
                    <div class="locked">
                        <img src="@/assets/images/locked.png" alt="" style="width: fit-content;" />
                        <span class="desc">升级正式版可解锁</span>
                    </div>
                </div>
                <QuestionList v-else ref="listRef" :subject-id="subjectId" :defaultSelectedRows="defaultSelectedRows"
                    :defaultExcludedRowKeys="defaultExcludedRowKeys" :defaultType="defaultType"
                    :disabledRowKeys="disabledRowKeys" @select="handleQuestionListSelect" />
            </div>
        </div>
    </div>
    <div class="btn-group">
        <a-button type="primary" @click="handleSave">确定</a-button>
        <a-button @click="emits('close')">取消</a-button>
    </div>
</template>
<script lang="ts" setup>
import SubjectTree from './SubjectTree.vue'
import QuestionList from './QuestionList.vue'
import { ref, nextTick } from 'vue'
import { Modal } from 'ant-design-vue'
import { useStore } from 'vuex'

const store = useStore()

const props = withDefaults(defineProps<{
    defaultType: number | null
    defaultSelectedRows: any[]
    defaultExcludedRowKeys?: string[]
    disabledRowKeys?: string[]
    initialOptions?: number // 选做题量
    allSelectRows?: any[]
    optionsDisabled?: boolean
}>(), {
    initialOptions: 0,
    optionsDisabled: true
})

const emits = defineEmits<{
    (e: 'confirm', value: { addList?: string[], delList?: string[] }, options: number): void
    (e: 'close'): void
}>()

const listRef = ref<InstanceType<typeof QuestionList>>()
const options = ref(props.initialOptions)

// 题库树
const subjectId = ref('')
const isOfficialSubject = ref(store.getters.userInfo.is_official_cert ? true : false)
function handleSelectSubject({ id, is_official_cert }: any) {
    subjectId.value = id
    isOfficialSubject.value = is_official_cert
}
async function handleTreeVisibleChange() {
    if (!listRef.value) return
    await nextTick()
    listRef.value.layoutTags()
}

// 列表选择后更新左侧题库树统计数字
const treeRef = ref<InstanceType<typeof SubjectTree>>()
function handleQuestionListSelect(record: any, selected: boolean) {
    if (selected) {
        treeRef.value?.addItemToCatgsQuesNumMap(record)
    } else {
        treeRef.value?.removeItemFromCatgsQuesNumMap(record)
    }
}

function handleSave() {
    if (!listRef.value) return
    let warningText = ''
    if (options.value && options.value >= listRef.value.selectedRows.length) {
        // 必答题量不能大于或等于题量
        warningText = '必答题量不能大于或等于题量！'
    } else if (options.value && listRef.value.selectedRows.length > 10) {
        // 选做题最多选择10道题
        warningText = '选做题最多可选10道题！'
    } else if (options.value && listRef.value.selectedRows.length < 2) {
        // 选做题至少要选2道题
        warningText = '选做题至少要选2道题！'
    }
    if (warningText) {
        Modal.warning({
            title: '提示',
            content: warningText,
            centered: true,
            okText: '我已知晓',
        })
        return
    }
    let selectedRows = listRef.value.selectedRows,
        initialRows = props.defaultSelectedRows
    let addList = selectedRows.filter((item: any) => !initialRows.some((i: any) => i.id === item.id))
    let delList = initialRows.filter((item: any) => !selectedRows.some((i: any) => i.id === item.id))
    emits('confirm', { addList, delList }, options.value)
}

</script>
  
<style lang="less" scoped>
.question-manage-container {
    height: 620px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .question-manage-title {
        line-height: 48px;
        font-size: 14px;
        font-weight: 600;
    }

    .question-manage-content {
        overflow: hidden;
        flex: 1;
        display: flex;

        .questions-subjects {
            top: 96px;
            bottom: 22px;
            z-index: 2;
            background-color: #fff;
            flex-shrink: 0;
            border: 1px solid #e8e8e8;
        }

        .questions-list {
            background-color: #fff;
            flex: 1;
            min-width: 530px;

            .locked-box {
                height: 100%;

                .locked {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: center;
                    align-items: center;

                    .desc {
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.45);
                        line-height: 20px;
                    }
                }
            }
        }
    }
}

.btn-group {
    display: flex;
    align-items: center;
    justify-content: center;

    .ant-btn {
        border-radius: 8px;

        &:first-child {
            margin-right: 8px;
        }
    }
}
</style>