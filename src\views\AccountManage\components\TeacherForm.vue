<template>
  <a-form
    ref="teacherFormRef"
    class="teacher-form"
    :model="formState"
    labelAlign="left"
    :colon="false"
    :rules="rules"
    :hideRequiredMark="true"
  >
    <a-form-item label="姓名" name="username" :colon="false">
      <a-input v-model:value.trim="formState.username" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="部门" name="dept_id" :colon="false">
      <a-tree-select
        show-search
        :tree-data="treeData"
        placeholder="选择部门"
        v-model:value="formState.dept_id"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        allow-clear
        :field-names="replaceFields"
        treeNodeFilterProp="name"
        tree-default-expand-all
        :virtual="false"
        :getPopupContainer="
        (triggerNode: HTMLElement) => {
          return triggerNode.parentNode
        }"
      >
        <template #title="{ name }">
          <span>{{ name }}</span>
        </template>
      </a-tree-select>
    </a-form-item>
    <a-form-item label="工号" name="jobnumber" :colon="false">
      <a-input v-model:value.trim="formState.jobnumber" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="电话" name="phone" :colon="false">
      <a-input v-model:value="formState.phone" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="邮箱" name="email" :colon="false">
      <a-input v-model:value="formState.email" placeholder="请输入" />
    </a-form-item>
    <!-- <a-form-item label="角色" name="roles" :colon="false">
      <a-select
        class="tag-select"
        mode="multiple"
        dropdownClassName="tag-select-wrapper"
        :options="roles"
        placeholder="添加角色"
        v-model:value="formState.roles"
      >
      </a-select>
    </a-form-item> -->
  </a-form>
</template>

<script setup lang="ts">
import { checkUsername } from '@/utils/validate'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  },
  treeData: {
    type: Array,
    default: () => []
  },
  roles: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['update:modelValue'])

const formState = ref({
  username: '',
  dept_id: '',
  jobnumber: '',
  phone: '',
  email: '',
  roles: undefined
})

const replaceFields = { title: 'name', key: 'id', value: 'id' }

// 验证手机号码
const checkPhoneNo = async (rule: RuleObject, value: string) => {
  const reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
  if (value === '') {
    return Promise.reject('请输入手机号')
  } else if (!reg.test(value)) {
    return Promise.reject('请输入正确的手机号码')
  } else {
    return Promise.resolve()
  }
}
const checkEmail = async (rule: RuleObject, value: string) => {
  const email =
    /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9.]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
  if (value === '') {
    return Promise.reject('请输入邮箱')
  } else if (!email.test(value)) {
    return Promise.reject('邮箱格式不正确')
  } else {
    return Promise.resolve()
  }
}

const rules = {
  username: [{ required: true, validator: checkUsername, trigger: 'blur' }],
  dept_id: [{ required: true, message: '请选择部门', trigger: 'blur' }],
  jobnumber: [{ required: true, message: '请输入工号', trigger: 'blur' }],
  phone: [{ required: true, validator: checkPhoneNo, trigger: 'blur' }],
  email: [{ required: true, validator: checkEmail, trigger: 'blur' }],
  // roles: [{ required: true, message: '请选择角色', trigger: 'blur' }]
}

const teacherFormRef = ref()
const validateForm = () => {
  return teacherFormRef.value.validate()
}

defineExpose({
  validateForm
})

watch(
  formState,
  (val) => {
    emits('update:modelValue', val)
  },
  {
    deep: true
  }
)

watch(
  () => props.modelValue,
  (val) => {
    formState.value = val
  },
  {
    deep: true
  }
)
</script>

<style lang="less" scoped>

:deep(.ant-tree-select-dropdown) {
  .ant-select-tree-node-content-wrapper {
    display: flex;
    align-items: center;
    flex-shrink: 0 !important;
  }
  .ant-select-tree-list-holder > div {
    overflow: auto!important;
  }
}
.teacher-form {
  padding-left: 20px;
  :deep(.ant-form-item-label > label) {
    font-size: 12px;
    color: #626262;
  }
  :deep(.ant-col) {
    width: 50px;
  }
  :deep(.ant-select-selector) {
    border-radius: 8px;
    font-size: 12px;
  }
  .ant-input,
  .ant-select {
    width: 280px;
    min-height: 32px;
    border-radius: 8px;
    font-size: 12px;
    color: #181818;
  }
  :deep(.ant-select-selection-item-content) {
    font-size: 12px;
  }
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
  }
}
</style>
