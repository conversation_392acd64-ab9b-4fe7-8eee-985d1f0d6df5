<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/06毛玻璃/24*24/15任务or面试备份</title>
    <defs>
        <linearGradient x1="-43.4467278%" y1="-1.73472348e-13%" x2="58.6913615%" y2="55.8568962%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="80.2736353%" y1="-17.3580711%" x2="7.49705129%" y2="97.6111193%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.085,5 L18.8521232,11.5929276 C19.4679832,13.8913484 18.1039982,16.2538398 15.8055774,16.8696998 L8.40707241,18.8521232 C7.2068689,19.1737168 5.98919473,18.9554494 5.0118737,18.3461758 C5.00396867,18.231614 5,18.1162705 5,18 L5,10 C5,7.23857625 7.23857625,5 10,5 L17.085,5 Z" id="path-3"></path>
        <filter x="-21.4%" y="-21.4%" width="142.9%" height="142.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="11.2033731%" y1="23.58476%" x2="57.4734728%" y2="55.8568962%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.3419053%" y1="3.3834%" x2="26.299685%" y2="95.9599574%" id="linearGradient-6">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.085,5 L10,5 C7.23857625,5 5,7.23857625 5,10 L5,10 L5,18 C5,18.1162705 5.00396867,18.231614 5.0118737,18.3461758 C4.11720028,17.7881646 3.42448185,16.9034782 3.1303002,15.8055774 L1.14787677,8.40707241 C0.532016767,6.10865159 1.89600181,3.7461602 4.19442263,3.1303002 L11.5929276,1.14787677 C13.8913484,0.532016767 16.2538398,1.89600181 16.8696998,4.19442263 L18.8521232,11.5929276 L17.085,5 Z" id="path-7"></path>
        <linearGradient x1="8.81880296%" y1="9.30569435%" x2="93.0787331%" y2="93.1157159%" id="linearGradient-8">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-9" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-10"></use>
        </pattern>
        <image id="image-10" width="18" height="18" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEqADAAQAAAABAAAAEgAAAACaqbJVAAABq0lEQVQ4Ea2TYWrjMBCFx/UQ00AKPcD+38vsOXuEHqN/9wgLG+JQgxepEun3RlHp/iikTQfLksYzT+/NyMPfQ/mVq1nKjGKWc7GUzFYNfFnzWmMdPvaKzakaT8RWZh/dbGPYqBdW3cpYTP6RBBn4bZBQGBXHexAR8AkADmlgJFb2DojHKYJppuQY+BMU3tb4M3k+TQQq6JzAki2sChI7S3yiLxaSJLlRCuYoC34XrQkGAujylFShtToSAdM+JDGHJBioXlpX5nJKgy8LDlhNFGrL3EkQYxNBfU8PWtHXBqJGlJc06HyZLyunZg+gDUA7AHfb9rEkt3lBIqgrAAtjZkjaCRYtqr19htFIl1TgDccv6Nzdmt3ftYA93wWyP1bbz03Ge4C+DiDxBydMoGr9Fmb3d24/f7g9PGb7c/ifQQfosx+fVQ2Kdn6p6NEJ9MxLtqff0QYbhvGkkI/M53+A0JJo77kTqsVxLiZZl1owUmvjV1BbKX4v6KUgimvtByhxoXQ3dB2a2M/AAPQMgzc2AH3VuEewQZIu3DXmuqH6Aa+1m+8AEYmba5n0/FcYBxXi9kPYOQAAAABJRU5ErkJggg=="></image>
        <rect id="path-11" x="5" y="5" width="18" height="18" rx="5"></rect>
        <filter x="-31.2%" y="-25.0%" width="162.5%" height="150.0%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="问卷管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="面试管理-岗位管理" transform="translate(-244.000000, -192.000000)">
            <g id="考试列表今日备份-11" transform="translate(220.000000, 168.000000)">
                <g id="icon/06毛玻璃/24*24/15任务or面试备份" transform="translate(24.000000, 24.000000)">
                    <rect id="_mockplus_fix_" x="0" y="0" width="24" height="24"></rect>
                    <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                    <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                        <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                        <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    </g>
                    <g id="形状结合" fill-rule="nonzero">
                        <use fill="url(#linearGradient-5)" xlink:href="#path-7"></use>
                        <use fill-opacity="0.6" fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                    </g>
                    <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                        <rect stroke="url(#linearGradient-8)" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                        <rect stroke="url(#pattern-9)" x="5.25" y="5.25" width="17.5" height="17.5" rx="5"></rect>
                    </g>
                    <g id="编组" filter="url(#filter-12)" transform="translate(10.000000, 9.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <path d="M6.53072267,2.5027 C6.53072267,1.11708 5.40152267,0 4.00082267,0 C2.60022267,0 1.47102267,1.11708 1.47102267,2.5027 C1.47102267,3.8776 2.60022267,5.0054 4.00082267,5.0054 C5.40152267,5.0054 6.53072267,3.8883 6.53072267,2.5027 Z" id="路径"></path>
                        <path d="M1.09092267,10 L6.91082267,10 C7.77942267,10 8.30062267,9.0333 7.81202267,8.3136 C7.07362267,7.2288 5.64042267,6.4984 4.00082267,6.4984 C2.36132267,6.4984 0.928122672,7.2288 0.189722672,8.3136 C-0.298877328,9.0333 0.211422672,10 1.09092267,10 Z" id="路径"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>