// 折叠进入动画
@keyframes polygon-in {
  0% {
    shape-inside: polygon(74% 42%, 100% 0, 100% 100%, 71% 100%, 67% 90%, 58% 68%);
    -webkit-clip-path: polygon(74% 42%, 100% 0, 100% 100%, 71% 100%, 67% 90%, 58% 68%);
  }
  40% {
    shape-inside: polygon(42% 43%, 100% 0, 100% 100%, 71% 100%, 35% 87%, 1% 75%);
    -webkit-clip-path: polygon(42% 43%, 100% 0, 100% 100%, 71% 100%, 35% 87%, 1% 75%);
  }
  80% {
    shape-inside: polygon(38% 36%, 100% 0, 100% 100%, 71% 100%, 35% 87%, 1% 75%);
    -webkit-clip-path: polygon(38% 36%, 100% 0, 100% 100%, 71% 100%, 35% 87%, 1% 75%);
  }
  100% {
    shape-inside: polygon(38% 36%, 100% 0, 100% 100%, 71% 100%, 31% 93%, 1% 75%);
    -webkit-clip-path: polygon(38% 36%, 100% 0, 100% 100%, 71% 100%, 31% 93%, 1% 75%);
  }
}

// 左右晃动
@keyframes sway {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
  75% {
    transform: translateX(-5px);
  }
  100% {
    transform: translateX(5px);
  }
}
