<template>
  <a-modal
    class="modify-dept-modal"
    v-model:visible="visible"
    title="更改部门"
    @ok="saveModify"
    @cancel="closeModal"
  >
    <template #footer>
      <a-button type="primary" @click="saveModify">确认</a-button>
      <a-button @click="closeModal">取消</a-button>
    </template>
    <div class="selected-teachers">
      <template v-for="item in teachers" :key="item.id">
        <span class="item">{{ item.name }}</span>
      </template>
    </div>
    <a-form
      ref="deptFormRef"
      class="dept-form"
      :model="deptForm"
      labelAlign="left"
      :colon="false"
      :rules="rules"
      :hideRequiredMark="true"
    >
      <!-- <a-form-item label="原部门">
        <a-input v-model:value="deptForm.olddept" disabled></a-input>
      </a-form-item> -->
      <a-form-item label="新部门" name="newdept">
        <a-tree-select
          v-if="treeData.length"
          show-search
          :tree-data="treeData"
          placeholder="新部门"
          v-model:value="deptForm.newdept"
          allow-clear
          :field-names="replaceFields"
          tree-default-expand-all
          :getPopupContainer="
          (triggerNode: HTMLElement) => {
            return triggerNode.parentNode
          }
        "
        >
        </a-tree-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { getDepList, alterteachersdept, allteachers } from '@/api/admin/systemManage'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = defineProps({
  modifyDeptVisible: {
    type: Boolean,
    default: false
  },
  teachers: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['close', 'refreshTeachers'])

const visible = ref(false)
const replaceFields = ref({
  children: 'children',
  key: 'id',
  value: 'id',
  label: 'name'
})

const deptForm = ref({
  olddept: '',
  newdept: undefined
})
const rules = {
  newdept: [{ required: true, message: '请选择新部门', trigger: 'blur' }]
}

const closeModal = () => {
  emits('close')
}

const deptFormRef = ref<any>(null)
const saveModify = () => {
  deptFormRef.value
    .validate()
    .then(() => {
      return alterteachersdept({
        teachers: props.teachers.map((item: any) => item.id),
        dept: deptForm.value.newdept
      })
    })
    .then(() => {
      message.success('部门更改成功!')
      closeModal()
      emits('refreshTeachers')
    })
}

watch(
  () => props.modifyDeptVisible,
  (val) => {
    visible.value = val
    if (val) {
      getDepData()
    }
  }
)

const treeData = ref<any>([])
const getDepData = () => {
  getDepList({ action: 'query' }).then((res: any) => {
    treeData.value = res
  })
}
</script>

<style lang="less">
.modify-dept-modal {
  width: 420px !important;
  .ant-modal-content {
    border-radius: 8px !important;
  }
  .ant-modal-header {
    border-radius: 8px !important;
  }
  .ant-modal-title {
    font-size: 15px;
    color: #121633;
    font-weight: bold;
  }
  .ant-form-item-label > label {
    font-size: 12px;
    color: #626262;
  }
  .ant-col {
    width: 80px;
  }
  .ant-input {
    // width: 280px;
    height: 32px;
    border-radius: 8px;
    font-size: 12px;
    color: #181818;
  }

  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
  }
  .ant-modal-footer {
    border-radius: 8px;
    text-align: center;
    padding-bottom: 30px;
    .ant-btn {
      margin-left: 20px;
      font-size: 14px;
    }
  }
  .ant-modal-body {
    padding-bottom: 0;
    .selected-teachers {
      display: flex;
      margin-bottom: 16px;
      .item {
        font-size: 12px;
        padding: 4px;
        background: #f5f5f5;
        border-radius: 4px;
        margin-right: 4px;
      }
    }
  }
  .dept-form {
    .ant-select-selector {
      // width: 280px;
      height: 32px;
      border-radius: 8px;
      font-size: 12px;
      color: #181818;
    }
  }
}
</style>
