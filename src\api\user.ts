import request from '@/utils/http'

export function getUserInfo(data?: object) {
  return request({
    url: '/teacher/load',
    // data
  })
  // return new Promise((resolve) => {
  //   resolve({
  //     roles: ['teacher']
  //   })
  // })
}

// 修改密码
export function chgPwd(data?: object) {
  return request({
    // url: '/user/chgpwd',
    url: '/forget/modifyPwd',
    data
  })
}

// 忘记密码
export function resetpwd(data?: object) {
  return request({
    // url: '/tchresetpwd',
    url: '/forget/pwd',
    data
  })
}

// 修改密码
export function changepwd(data?: object) {
  return request({
    // url: '/tchresetpwd',
    url: '/forget/resetPwd',
    data
  })
}

// 辅助摄像头登录
export function auxlogin(data?: object) {
  return request({
    url: '/auxlogin',
    data
  })
}

// 校验修改密码的token是否有效
export function checktoken(data?: object) {
  return request({
    // url: '/checktoken',
    url: '/common/validate/token/forgetPwd',
    data
  })
}

// 校验修改密码的token是否有效
export function upgradevip(data?: { id: string }) {
  return request({
    url: '/upgradevip',
    data
  })
}