<template>
  <a-form
    class="form"
    ref="createQuestionFormRef"
    :model="formState"
    hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
  >
    <a-form-item label="分值" name="score">
      <score-input
        class="scoreInp"
        ref="scoreInputTotalRef"
        :disabled="formState.sepscore"
        v-model="formState.score"
        @getScore="getScore"
      />
    </a-form-item>
    <a-form-item label="题干内容" v-bind="validateInfos.body">
      <a-textarea
        v-if="!formState.complicatedediting"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      />
      <VueQuillEditor
        v-else
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      ></VueQuillEditor>
    </a-form-item>
    <a-form-item label="评分依据" name="standard">
      <a-textarea
        v-if="!formState.complicatedediting"
        v-model:value="formState.standard"
        :auto-size="{ minRows: 4 }"
        placeholder="点击编辑"
      />
      <VueQuillEditor
        ref="standardEditorRef"
        v-else
        v-model:content="formState.standard"
      ></VueQuillEditor>
      <p class="standard-tip">(仅阅卷老师可见)</p>
    </a-form-item>
    <a-form-item label="得分点" name="answer" class="points-wrapper">
      <template v-for="(item, index) in scorepts" :key="index">
        <div class="item">
          <span>{{ index + 1 }}</span>
          <a-select
            class="tags-select"
            v-model:value="item.keyword"
            mode="tags"
            placeholder="输入完成后按回车添加多个关键词"
          >
          </a-select>
          <div v-if="formState.sepscore" class="score-wrapper">
            <span class="score">分值</span>
            <score-input
              class="score-input"
              v-model="item.score"
              @getScore="getPointsScore($event, item)"
            />
          </div>
          <svg-icon
            class="del-icon"
            name="circle-del"
            width="16px"
            height="16px"
            @click="delPoints(index)"
          />
        </div>
      </template>
      <div class="addpoints-btn">
        <span @click="addPoints"><svg-icon name="plus" />添加得分点</span>
      </div>
    </a-form-item>
    <div class="fill-blank-config">
      <div>
        <a-checkbox v-model:checked="formState.ignorecase" class="check-box" />
        <span>忽略大小写</span>
      </div>
      <!-- <div>
        <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
        <span>按得分点得分，每个得分点单独计分</span>
        <a-tooltip placement="right">
          <template #title>
            <span
              >当考试设置为“按得分点得分，每个得分点单独计分”，并且本题目勾选了此选项，则每个得分点单独设置分值并计分。否则，需每个得分点均答对，方可得分。</span
            >
          </template>
          <svg-icon name="gray-tip" class="tip-icon" />
        </a-tooltip>
      </div> -->
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import ScoreInput from './ScoreInput.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import { ref, watch } from 'vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { Form } from 'ant-design-vue'

const useForm = Form.useForm

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['update:modelValue'])

const scorepts = ref<any>([{ keyword: [], score: 0 }])
const scoreInputTotalRef = ref()
const formState = ref({
  complicatedediting: false,
  ignorecase: true,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  ordered: true,
  standard: '',
  sepscore: true,
  options: [
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    }
  ]
})

const standardEditorRef = ref()
const handleComplexToNormal = (formState: any) => {
  if (standardEditorRef.value) {
    formState.standard = standardEditorRef.value.getText()
  }
}
defineExpose({ handleComplexToNormal })

const getPointsScore = (score: any, item: any) => {
  item.score = score
  let totalScore = 0
  scorepts.value.forEach((item: any) => {
    totalScore += Number(item.score)
  })
  formState.value.score = totalScore
  formState.value.answer = JSON.stringify(scorepts.value)
}

// 删除问答题得分点
const delPoints = (index: number) => {
  scorepts.value.splice(index, 1)
}

// 添加得分点
const addPoints = () => {
  scorepts.value.push({ keyword: [], score: 0 })
}

// 校验分值
const checkScore = async (rule: RuleObject, value: string) => {
  if (value == '0') {
    return Promise.reject('请给该题目匹配合适的分值')
  } else {
    return Promise.resolve()
  }
}

// 校验选项内容
const checkOptions = async (rule: RuleObject, value: []) => {
  if (!value.every((item: any) => item.content)) {
    return Promise.reject('选项内容都不能为空')
  } else {
    return Promise.resolve()
  }
}

// 校验题干
const checkBody = async (rule: RuleObject, value: '') => {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  } else {
    return Promise.resolve()
  }
}

// 校验评分依据
// const checkStandard = async(rule: RuleObject, value: '') => {
//   if (!value) {
//     return Promise.reject('请输入评分依据')
//   } else {
//     return Promise.resolve()
//   }
// }

// 校验得分点
// const checkAnswer = async(rule: RuleObject, value: '') => {
//   console.log(value);
//   if (value) {
//     return Promise.reject('请输入得分点')
//   } else {
//     return Promise.resolve()
//   }
// }

// 定义规则
const rules = {
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }]
  // standard: [{ required: true, validator: checkStandard, trigger: 'blur' }],
  // answer: [{ required: true, validator: checkAnswer, trigger: 'blur' }]
}

const { validate, validateInfos } = useForm(formState.value, rules)

// 获取分数
const getScore = (score: any) => {
  formState.value.score = score
}

watch(
  formState,
  (val) => {
    console.log(val)

    emits('update:modelValue', val)
  },
  {
    deep: true
  }
)

watch(
  () => props.modelValue,
  (val: any) => {
    formState.value = Object.assign(formState.value, val)

    if (formState.value.answer) {
      scorepts.value = JSON.parse(formState.value.answer)
    }
    if (formState.value.sepscore) {
      formState.value.score = scorepts.value.reduce(
        (prev: any, current: any) => prev + Number(current.score),
        0
      )
    }
  },
  {
    immediate: true
  }
)

watch(
  () => scorepts,
  (val) => {
    let totalScore = 0
    val.value.forEach((item: any) => {
      totalScore += Number(item.score)
    })
    formState.value.score = totalScore
    formState.value.answer = JSON.stringify(scorepts.value)
  },
  {
    deep: true
  }
)
</script>

<style lang="less" scoped>
.form {
  margin-bottom: auto;
  .fill-blank-config {
    div {
      display: flex;
      align-items: center;
      line-height: 28px;
    }
    .check-box {
      margin-right: 8px;
    }
    span {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.85);
    }
    .tip-icon {
      margin-left: 8px;
    }
  }
  .standard-tip {
    margin-top: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
  .points-wrapper {
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      .tags-select {
        width: 80%;
        margin: 0 16px;
      }
      :deep(.ant-select-selection-item) {
        font-size: 12px;
      }
    }
    .score-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      margin-right: 16px;

      .score,
      .score-input {
        flex-grow: 0;
        flex-shrink: 0;
      }
      .score {
        margin-right: 16px;
      }
    }
    .del-icon {
      cursor: pointer;
    }
    .addpoints-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 82px;
      height: 24px;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      margin-top: 10px;
    }
  }
  .question-options {
    .item-option {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 12px;
      position: relative;
      .option-radio {
        margin-right: 32px;
      }
      .option-content {
        margin-left: 4px;
        font-size: 12px;
        border: none;
        box-shadow: none;
        width: 220px;
      }
      .del-icon {
        display: none;
      }
    }
    .item-option:hover .del-icon {
      position: relative;
      display: inline-block;
    }
    .add-option-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 82px;
      height: 24px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      margin-top: 8px;
      cursor: pointer;
    }
  }
}
</style>
