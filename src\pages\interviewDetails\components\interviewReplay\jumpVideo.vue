<script setup lang="ts">
const props = defineProps({
  examDetail: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const videoRef = useTemplateRef<any>('video')
function handleLoad() {
  console.log('this is ok')
}
function jump(secondsDifference: any) {
  console.log('--- Debugging clickItem ---')
  console.log('Calculated secondsDifference:', secondsDifference, 'Type:', typeof secondsDifference) // 检查秒数是否有效

  if (!videoRef.value) {
    console.error('Video element not found. Cannot jump time.')
    return
  }

  if (typeof secondsDifference !== 'number' || Number.isNaN(secondsDifference) || secondsDifference < 0) {
    console.error('Invalid time calculated:', secondsDifference)
    return
  }

  // 内部辅助函数，用于实际的跳转和播放逻辑
  const performJumpAndPlay = (time: number) => {
    // 确保跳转时间不超过视频总时长
    if (videoRef.value!.duration && time > videoRef.value!.duration) {
      console.warn(`Attempting to jump to ${time}s, but video duration is ${videoRef.value!.duration.toFixed(2)}s. Jumping to end.`)
      videoRef.value!.currentTime = videoRef.value!.duration
    }
    else {
      videoRef.value!.currentTime = time
    }
    
    console.log(`Successfully set video currentTime to: ${time}s`)
    
    // 尝试播放视频，并捕获可能的自动播放错误
    videoRef.value!.play().then(() => {
      console.log('Video started playing from new time.')
    }).catch((error: Error) => {
      console.error('Video auto-play failed after seek:', error)
      // 通常是 DOMException: play() failed because the user didn't interact with the document first.
      // 可以在这里显示一个用户提示，例如：“视频未能自动播放，请手动点击播放按钮。”
    })
  }

  // 检查视频是否已加载元数据
  if (videoRef.value.readyState >= HTMLMediaElement.HAVE_METADATA) {
    // 如果元数据已加载，直接执行跳转和播放
    performJumpAndPlay(secondsDifference)
  }
  else {
    // 如果元数据未加载，等待 loadedmetadata 事件触发
    console.warn('Video metadata not yet loaded. Waiting for \'loadedmetadata\' event before jumping.')
    // 使用 { once: true } 确保事件监听器只触发一次，避免重复监听
    videoRef.value.addEventListener('loadedmetadata', () => {
      console.log('Video metadata loaded. Now performing jump.')
      performJumpAndPlay(secondsDifference)
    }, { once: true })
  }
}
defineExpose({
  jump,
})
</script>

<template>
  <div class="overflow-hidden pt-[24px] rounded-[8px] w-fit w-media">
    <video ref="video" :src="props.examDetail.exam.replayUrl" class="rounded-[8px] video-media" controls autoplay @loadeddata="handleLoad" />
  </div>
</template>

<style scoped>
.video-media{
      @media screen and (max-width: 1880px){
            display: inline-block;
            width: 400px;
            height: 76vh;
      }
      @media screen and (min-width: 1881px){
            display: inline-block;
            width: 468px;
            height: 83vh;
      }
}
.w-media{
       @media screen and (max-width: 1880px){
            max-width: 468px;
            width: 468px;
      }
      @media screen and (min-width: 1881px){
            max-width: 850px;
            width: 850px;
      } 
}
</style>