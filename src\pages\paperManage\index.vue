<template>
  <div class="paper-manage-page-wrap">
    <div class="title-wrap">
      <span class="title">试卷管理</span>
      <div class="btns">
        <a-button type="primary" @click="newPaper">新增考试</a-button>
        <a-button :disabled="!checkedIds.length" @click="handleMultiDelete">批量删除</a-button>
      </div>
    </div>
    <div class="filter-wrapper">
      <div style="flex: 1;">
        <div class="filter-wrapper-row">
          <Switch :columns="stateColums" v-model="params.status"></Switch>
          <div style="display: flex;align-items: center;">
            <div style="display: flex;align-items: center;margin-right: 16px;">
              <a-checkbox v-model:checked="params.onlyFocus">
                <span style="color: #181818;">仅显示收藏考试</span>
              </a-checkbox>
            </div>
            <a-input-search v-model:value.trim="searchValue" placeholder="请输入考试名称/教师名称" allow-clear class="select-wrap" @blur="handleSearch" @search="handleSearch" />
          </div>
        </div>
        <div class="filter-wrapper-row more-filter" v-if="filterMoreVisible">
          <div class="filter-item">
            <span class="filter-label">试卷类型</span>
            <a-select v-model:value="params.uniexam" :options="PaperTypeList" placeholder="全部" allowClear />
          </div>
          <div class="filter-item">
            <span class="filter-label">创建人</span>
            <a-select v-model:value="params.createBy" placeholder="全部" :options="createrList" allowClear />
          </div>
          <div class="filter-item">
            <span class="filter-label">考试时间</span>
            <a-range-picker v-model:value="params.start_time_range" :placeholder="['最早开始时间', '最晚开始时间']" valueFormat="YYYY-MM-DD">
              <template #suffixIcon>
                <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
              </template>
            </a-range-picker>
          </div>
        </div>
      </div>
      <div class="filter-btns">
          <div class="filter-btn filter-more" @click="filterMoreVisible = !filterMoreVisible">
              <span v-if="filterMoreActiveNumber" class="filter-number">{{ filterMoreActiveNumber }}</span>
              <img v-else src="@/assets/icons/svg/filter2.svg" alt="">
              <span style="margin-left: 4px;">更多条件</span>
          </div>
          <div class="filter-btn" style="margin-top: 16px;" @click="handleReset" v-if="filterMoreVisible">
              <img width="16" height="16" style="margin-right: 4px;" src="@/assets/images/paper/clear.png" alt="">
              <div class="label">清空条件</div>
          </div>
      </div>
    </div>
    <ListWrapper ref="listWrapperRef" :card-min-width="580" :params="params" :getListFn="getListFn" :updateFn="updateFn">
      <template #item="{ item }">
        <PaperCard
          :paperInfo="item"
          :checked="checkedIds.includes(item.id)"
          :search-text="params.name"
          @changeCheck="handleChangeChecked(item.id)"
          @deletePaperOk="handleDeletePaper(item.id)" />
      </template>
    </ListWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, createVNode, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import { InfoCircleFilled, ExclamationCircleFilled } from '@ant-design/icons-vue'
import _ from 'lodash'
import Switch from '@/components/Switch.vue';
import {
  PaperStatus,
  PaperType,
  PaperTypeList,
} from '@/models/paperModel'
import {
  queryTestPaper,
  delTestPaper,
  queryPaperListByIds,
} from '@/api/admin/paperManage'
import ListWrapper from '@/components/ListWrapper.vue'
import PaperCard from './components/paperCard.vue'
import { useState } from '@/hooks'

const router = useRouter()

// 更多条件
const filterMoreVisible = ref(false)
const filterMoreActiveNumber = computed(() => {
    const filterMoreKeys = ['uniexam', 'createBy', 'start_time_range']
    return filterMoreKeys.reduce((pre, cur) => {
        if (params.value[cur] !== rawParams[cur]) pre++
        return pre
    }, 0)
})

// 考试状态
const stateColums = [
    { label: '全部', value: null },
    { label: '未开始', value: PaperStatus.NOT_START },
    { label: '考试中', value: PaperStatus.DURING_EXAMS },
    { label: '已结束', value: PaperStatus.OVER },
]

const createrList = [ // 创建人筛选项
  { label: '本人', value: 1 },
  { label: '其他', value: 0 },
]

const checkedIds = ref<string[]>([]) // 已选试卷ids
const handleChangeChecked = (id: string) => { // 修改考试选中状态
  if (checkedIds.value.includes(id)) {
    checkedIds.value = checkedIds.value.filter(el => el !== id)
  } else {
    checkedIds.value = [...checkedIds.value, id]
    console.log(checkedIds.value.length)
  }
}

// 实时更新试卷列表的方法
const updateFn = queryPaperListByIds


// 模糊查询
const searchValue = ref('')
function handleSearch() {
    params.value.name = searchValue.value
}

const [params, resetParams, rawParams] = useState<{
  action: 'query'
  name: string
  status: PaperStatus | null
  uniexam?: PaperType | null
  createBy?: number | null
  start_time_range: null | string[]
  onlyFocus: boolean
}>({
  action: 'query',
  name: '', // 关键词
  status: null, // 考试状态
  uniexam: undefined, // 试卷类型
  createBy: undefined, // 创建人
  start_time_range: null, // 开始结束时间
  onlyFocus: false
})

const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()
const paperList = ref<any[]>([])

const getListFn = (params: any) => {
    params.basePager = {
      current: params.page,
      size: params.per_page,
    }
    params.uniexam = params.uniexam && params.uniexam.toString()
    params.status = params.status && params.status.toString()
    return queryTestPaper({
        ...params,
        statusList: params.status === null ? null : [params.status],
        uniexam: params.uniexam === undefined ? null : params.uniexam,
        createBy: params.createBy === undefined ? null : params.createBy,
        startTime: params.start_time_range === null ? null : params.start_time_range[0],
        endTime: params.start_time_range === null ? null : params.start_time_range[1],
    })
}

// 重置
function handleReset() {
    searchValue.value = ''
    resetParams()
}

// 新建考试
const newPaper = () => {
  router.push({ name: 'createPaper' })
}

// 考试卡片数据删除后，从list中删除掉当前数据
const handleDeletePaper = (id: string) => {
  listWrapperRef.value?.handleBatchDelete([id])
}

// 批量删除
const handleMultiDelete = () => {
  const params = {
    action: 'del',
    ids: checkedIds.value,
  }
  Modal.confirm({
    title: () => `确定删除勾选的${checkedIds.value.length}场考试?`,
    icon: () => createVNode(ExclamationCircleFilled),
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await delTestPaper(params)
        message.success('批量删除成功！')
        listWrapperRef.value?.handleBatchDelete(checkedIds.value)
        checkedIds.value = []
      } catch (error) {
        // do nothing
        console.log(error)
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {}
  })
}

</script>

<style lang="less" scoped>
.filter-wrapper {
    padding: 24px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
    background: #ffffff;
    border-radius: 8px;
    margin: 0px 20px 20px;
    display: flex;

    :deep(.filter-wrapper-row) {
        display: flex;
        justify-content: space-between;
        &.more-filter {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-row-gap: 16px;
        }

        +.filter-wrapper-row {
            margin-top: 16px;
        }

        .ant-select,
        .ant-picker,
        .ant-input-search {
            width: 240px;
        }
    }

    .filter-item {
        display: flex;
        align-items: center;
        .filter-label {
            margin-right: 16px;
            color: #626262;
        }
        &:nth-child(3n+2) {
            justify-content: center;
        }
        &:nth-child(3n+3) {
            justify-content: flex-end;
        }
    }

    .filter-btns {
        margin-left: 8px;
    }

    .filter-btn {
        width: 108px;
        height: 32px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.filter-more {
            .filter-number {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #FF4D4F;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
            }
        }

    }


}
.paper-manage-page-wrap {
  width: 100%;
  height: 100%;
  min-width: 1150px;
  display: flex;
  flex-direction: column;
  .title-wrap {
    height: 64px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      height: 48px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0,0,0,0.85);
      text-align: left;
      line-height: 48px;
    }
    .btns {
      height: 32px;
      :deep(.ant-btn) {
        border-radius: 8px;
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
}
</style>
