<template>
  <div class="account-manage-container">
    <h3 class="account-manage-title">教师列表</h3>
    <div class="account-manage">
      <div class="account-manage-header">
        <div class="header-left">
          <a-input-search
            class="search-wrapper"
            v-model:value.trim="searchContent"
            placeholder="请输入关键词"
            @search="getSearchData"
            @keyup.enter="getSearchData"
          />
        </div>
        <div class="header-right">
          <span class="add-btn" @click="addTeacher">新增教师</span>
          <span class="del-btn" @click="delMultiTeacher">批量删除</span>
        </div>
      </div>
      <account-table
        class="account-table"
        :data="data"
        :pagination="paginationConfig"
        @updateTableData="updateTableData"
        @editTeacher="editTeacher"
        @forbidTeacher="forbidTeacher"
        @activeTeacher="activeTeacher"
        @delTeacher="delTeacherAccount"
        @getPermission="getPermission"
        @getSelectedTeacher="getSelectedTeacher"
      />
    </div>

    <custom-dialog
      :width="420"
      :dialogVisible="dialogVisible"
      :txtConfig="txtConfig"
      :keyboard="false"
      :maskClosable="false"
      @updateDialog="updateDialog"
      @closeDialog="closeDialog"
      @cancel-dialog="closeDialog"
    >
      <teacher-form
        v-if="dialogVisible"
        ref="teacherFormRef"
        v-model="formState"
        :treeData="treeData"
        :roles="oproles"
      />
    </custom-dialog>

    <custom-dialog
      :width="420"
      :dialogVisible="permissDialogVisible"
      :txtConfig="permissTxtConfig"
      @updateDialog="closePermissDialog"
      @closeDialog="closePermissDialog"
      @cancel-dialog="closePermissDialog"
    >
      <div class="permiss-item" v-if="quesmagList.length">
        <label>题库管理</label>
        <template v-for="item in quesmagList" :key="item.name">
          <span class="tag" :style="{ color: item.color }">{{ item.name }}</span>
        </template>
      </div>
      <div class="permiss-item" v-if="papermagList.length">
        <label>考试管理</label>
        <template v-for="item in papermagList" :key="item.name">
          <span class="tag" :style="{ color: item.color }">{{ item.name }}</span>
        </template>
      </div>
      <div class="permiss-item" v-if="stumagList.length">
        <label>考生管理</label>
        <template v-for="item in stumagList" :key="item.name">
          <span class="tag" :style="{ color: item.color }">{{ item.name }}</span>
        </template>
      </div>
      <div class="permiss-item" v-if="analysisList.length">
        <label>统计分析</label>
        <template v-for="item in analysisList" :key="item.name">
          <span class="tag" :style="{ color: item.color }">{{ item.name }}</span>
        </template>
      </div>
      <div class="permiss-item" v-if="!permissionList.length">
        <div class="no-data">暂无权限,请先添加角色</div>
      </div>
    </custom-dialog>
    <!-- <add-dialog
      :isVisible="data.isVisible"
      :accountData="data.accountData"
      @cancelDialog="handleCancelDialog"
      @refreshAccountList="refreshAccountList"
    /> -->
  </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, computed, createVNode, nextTick } from 'vue'
import {
  getAccountList,
  delAccount,
  resetAccountPassword,
  adminRoles,
  addAccount,
  teacherdept,
  teacherpermissions,
  teacherroles
} from '@/api/admin/accountManage'
import { getDepList } from '@/api/admin/systemManage'
import AccountTable from './components/AccountTable.vue'
import CustomDialog from '@/components/CustomDialog.vue'
import TeacherForm from './components/TeacherForm.vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

const treeData = ref([])
const deptData = ref([])
const rolesData = ref([])
const data = reactive({
  columns: [
    {
      title: '教师姓名',
      dataIndex: 'username',
      key: 'username',
      ellipsis: true,
      width: 200,
      resizable: true
    },
    {
      title: '部门',
      dataIndex: 'dept_name',
      key: 'dept_name',
      ellipsis: true,
      filters: deptData,
      width: 200,
      onFilter: (value: string, record: any) => record.dept_id.includes(value),
      resizable: true
    },
    {
      title: '工号',
      dataIndex: 'jobnumber',
      key: 'jobnumber',
      ellipsis: true,
      width: 300,
      resizable: true
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      ellipsis: true,
      width: 200,
      resizable: true
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
      width: 300,
      resizable: true
    },
    // {
    //   title: '角色',
    //   dataIndex: 'oproles',
    //   key: 'oproles',
    //   ellipsis: true,
    //   width: 300,
    //   filters: rolesData,
    //   onFilter: (value: string, record: any) =>
    //     record.oproles.map((item: any) => item.id).includes(value),
    //   resizable: true
    // },
    {
      title: '操作',
      key: 'action',
      width: 220,
      fixed: 'right'
    }
  ],
  accountList: [],
  total: 0,
  loading: false
})

// 快速搜索
const searchContent = ref('')
const getSearchData = () => {
  paginationConfig.value.current = 1
  paginationConfig.value.pageSize = 10
  getTeacherAccountList()
}

// 获取试卷列表
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

const teacherFormRef = ref()

// 自定义弹框配置
const dialogVisible = ref(false)
const txtConfig = ref({ title: '新增教师', confirm: '确认' })
const updateDialog = () => {
  teacherFormRef.value
    .validateForm()
    .then(() => {
      const params: any = { ...formState.value }
      params.oproles = params.roles
      delete params.roles
      return addAccount({ action: formState.value.id ? 'modify' : 'add', teacher: params })
    })
    .then(() => {
      message.success(formState.value.id ? '编辑教师成功!' : '新增教师成功!')
      dialogVisible.value = false
      // 更新教师列表
      getTeacherAccountList()
    })
}
const closeDialog = () => {
  dialogVisible.value = false
}

// 查看权限Dialog
const permissDialogVisible = ref(false)
const permissTxtConfig = ref({ title: '查看权限', confirm: '确认' })
const closePermissDialog = () => {
  permissDialogVisible.value = false
}

// 新增教师
const addTeacher = () => {
  txtConfig.value.title = '新增教师'
  dialogVisible.value = true
}

// 批量删除
const selectedTeacher = ref<string[]>([])
const delMultiTeacher = () => {
  if (!selectedTeacher.value.length) {
    message.error('请先勾选需要删除的教师账号')
    return
  }
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确认删除已选中的' + selectedTeacher.value.length + '个教师账号?',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      delAccount({
        action: 'del',
        teacher: {
          ids: selectedTeacher.value
        }
      }).then(() => {
        message.success('教师账号批量删除成功!')
        getTeacherAccountList()
      })
    }
  })
}

const getSelectedTeacher = (payload: string[]) => {
  selectedTeacher.value = payload
}

const formState = ref({})

// 获取教师账号列表
const getTeacherAccountList = () => {
  data.loading = true
  getAccountList({
    action: 'query',
    page: paginationConfig.value.current,
    per_page: paginationConfig.value.pageSize,
    condition: searchContent.value
  })
    .then((res: any) => {
      data.loading = false
      data.accountList = res.data
      data.total = res.total
      paginationConfig.value.total = data.total
    })
    .finally(() => {
      data.loading = false
    })
}

const updateTableData = (payload: any) => {
  paginationConfig.value.current = payload.pagination.current
  paginationConfig.value.pageSize = payload.pagination.pageSize
  getTeacherAccountList()
}

// 编辑教师
async function editTeacher(payload: any) {
  dialogVisible.value = true
  await nextTick()
  formState.value = JSON.parse(JSON.stringify(payload))
  formState.value.roles = payload.oproles.map((item: any) => item.id)
  txtConfig.value.title = '编辑教师'
}

// 禁用教师账号
const forbidTeacher = (payload: any) => {
  addAccount({ action: 'modify', teacher: { id: payload.id, status: false } }).then(() => {
    message.success('教师账号禁用成功!')
    getTeacherAccountList()
  })
}

// 启用教师账号
const activeTeacher = (payload: any) => {
  addAccount({ action: 'modify', teacher: { id: payload.id, status: true } }).then(() => {
    message.success('教师账号启用成功!')
    getTeacherAccountList()
  })
}

// 刷新教师账号列表
const refreshAccountList = () => {
  getTeacherAccountList()
}
// 删除教师账号
const delTeacherAccount = (id: string) => {
  const params = {
    action: 'del',
    teacher: {
      ids: [id]
    }
  }

  delAccount(params).then(() => {
    message.success('删除成功！')
    refreshAccountList()
  })
}

// 获取权限
const permission = {
  quesmag: {
    read: '查看题库',
    write: '查看/编辑题库'
  },
  papermag: {
    read: '查看试卷',
    write: '查看/编辑试卷',
    proctor: '监考',
    scoring: '人工阅卷'
  },
  stumag: {
    read: '查看考生',
    write: '查看/编辑考生'
  },
  analysis: {
    paper: '考试分析',
    ques: '题库分析',
    stu: '考生分析'
  }
}
const permissionList = ref<any>([])
const getPermission = (id: string) => {
  teacherpermissions({ teacher: id }).then((res: any) => {
    permissionList.value = []
    Object.keys(res.quesmag).forEach((key) => {
      if (res.quesmag[key]) {
        permissionList.value.push({
          type: 'quesmag',
          name: permission['quesmag'][key],
          color: '#2F8C00'
        })
        if (key === 'write') {
          const index = permissionList.value.findIndex((o: any) => o.name === '查看题库')
          permissionList.value.splice(index, 1)
        }
      }
    })
    Object.keys(res.papermag).forEach((key) => {
      if (res.papermag[key]) {
        permissionList.value.push({
          type: 'papermag',
          name: permission['papermag'][key],
          color: '#6733B1'
        })
        if (key === 'write') {
          const index = permissionList.value.findIndex((o: any) => o.name === '查看试卷')
          permissionList.value.splice(index, 1)
        }
      }
    })
    Object.keys(res.stumag).forEach((key) => {
      if (res.stumag[key]) {
        permissionList.value.push({
          type: 'stumag',
          name: permission['stumag'][key],
          color: '#D96E00'
        })
        if (key === 'write') {
          const index = permissionList.value.findIndex((o: any) => o.name === '查看考生')
          permissionList.value.splice(index, 1)
        }
      }
    })
    Object.keys(res.analysis).forEach((key) => {
      if (res.analysis[key]) {
        permissionList.value.push({
          type: 'analysis',
          name: permission['analysis'][key],
          color: '#626262'
        })
      }
    })
    permissDialogVisible.value = true
  })
}

const quesmagList = computed(() => {
  return permissionList.value.filter((item: any) => item.type === 'quesmag')
})
const papermagList = computed(() => {
  return permissionList.value.filter((item: any) => item.type === 'papermag')
})
const stumagList = computed(() => {
  return permissionList.value.filter((item: any) => item.type === 'stumag')
})
const analysisList = computed(() => {
  return permissionList.value.filter((item: any) => item.type === 'analysis')
})

// 重置密码
// const resetTeacherAccountPassword = (record: any) => {
//   const params = {
//     action: 'reset',
//     teacher: {
//       id: record.id
//     }
//   }
//   resetAccountPassword(params).then(() => {
//     message.success('重置密码成功！')
//   })
// }

const getDepData = () => {
  getDepList({ action: 'query' }).then((res: any) => {
    treeData.value = res
  })
}

const oproles = ref([])
const getRoles = () => {
  adminRoles({ action: 'query' }).then((res: any) => {
    oproles.value = res.map((item: any) => ({ label: item.name, value: item.id }))
  })
}

const getTeacherdept = () => {
  teacherdept().then((res: any) => {
    deptData.value = res.map((item: any) => ({ text: item.name, value: item.id }))
  })
}

const getTeacherRoles = () => {
  teacherroles().then((res: any) => {
    rolesData.value = res.map((item: any) => ({ text: item.name, value: item.id }))
  })
}

onMounted(() => {
  getTeacherdept()
  getTeacherRoles()
  getTeacherAccountList()
  getDepData()
  getRoles()
})
</script>

<style lang="less" scoped>
.account-manage-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  overflow: auto;
  padding: 0 20px 20px 20px;
  :global(.ant-dropdown-menu-item) {
    font-size: 14px !important;
  }
}
.account-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}
.account-manage {
  flex: 1;
  padding: 10px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  .account-manage-header {
    margin: 8px 15px;
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;
    align-items: center;
    .search-wrapper {
      --antd-wave-shadow-color: #fff !important;
      width: 240px;
      line-height: 32px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;

      :deep(.ant-input) {
        font-size: 13px;
        box-shadow: none;
        border: none;
        border-radius: 8px;
        line-height: 26px;
      }
      :deep(.ant-input-group-addon) {
        border-radius: 8px;
      }
      :deep(.ant-input-search-button) {
        border-radius: 8px !important;
        box-shadow: none !important;
        border: none !important;
      }
    }
    .header-right {
      display: flex;
      align-items: center;
      font-size: 14px;
      span {
        width: 88px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 8px;
        font-size: 14px;
        margin-left: 8px;
        cursor: pointer;
      }
      .add-btn {
        background: #5478ee;
        color: #fff;
      }
      .import-btn,
      .del-btn {
        border: 1px solid rgba(0, 0, 0, 0.15);
      }
    }
  }
  .table-bar {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .ant-checkbox-wrapper {
      font-size: 15px;
    }
    .show-columns {
      font-size: 15px;
      span {
        margin-right: 10px;
      }
    }
  }
}
.account-table {
  padding: 0 15px;
}
.permiss-item {
  margin-bottom: 16px;
  font-size: 12px;
  &:last-child {
    margin-bottom: 20px;
  }
  label {
    margin-right: 32px;
  }
  .no-data {
    text-align: center;
    font-size: 12px;
  }
  .tag {
    display: inline-block;
    margin-right: 8px;
    font-size: 12px;
    padding: 0 4px;
    border-radius: 4px;
    background: #f5f5f5;
  }
}
</style>
