import { RuleObject } from 'ant-design-vue/es/form/interface'
import CryptoJS from 'crypto-js'
import obsClient from 'esdk-obs-browserjs'
import { api as viewerApi } from 'v-viewer'

/**根据身份证计算年龄
 *@param UUserCard 身份证号码
 */

const IdCard = (UUserCard: string): number => {
  //获取年龄
  const myDate = new Date()
  const month = myDate.getMonth() + 1
  const day = myDate.getDate()
  let age = myDate.getFullYear() - <any>UUserCard.substring(6, 10) - 1
  if (
    <any>UUserCard.substring(10, 12) < month ||
    (<any>UUserCard.substring(10, 12) == month && <any>UUserCard.substring(12, 14) <= day)
  ) {
    age++
  }

  return age
}

// 验证身份证号码
const checkCardNo = async (rule: RuleObject, value: string) => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (value === '') {
    return Promise.reject('请输入身份证号')
  } else if (!reg.test(value)) {
    return Promise.reject('身份证输入不合法')
  } else {
    return Promise.resolve()
  }
}
// 验证手机号码
const checkPhoneNo = async (rule: RuleObject, value: string) => {
  const reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
  if (value === '') {
    return Promise.reject('请输入手机号')
  } else if (!reg.test(value)) {
    return Promise.reject('请填写正确的手机号码')
  } else {
    return Promise.resolve()
  }
}

// AES解密
//TODO: 验证是否解密成功
const aesDecrypt = (ciphertext: string, key: any, iv: any) => {
  key = CryptoJS.enc.Utf8.parse(key)
  iv = CryptoJS.enc.Utf8.parse(iv)
  const hexString = CryptoJS.enc.Hex.parse(ciphertext)
  const srcs = CryptoJS.enc.Base64.stringify(hexString)
  let decrypt: any = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  decrypt = decrypt.toString(CryptoJS.enc.Utf8)
  return decrypt.toString()
}

/** base64转化为Blob*/
const dataURItoBlob = (dataURI: string) => {
  const byteString = atob(dataURI.split(',')[1])
  const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
  const ab = new ArrayBuffer(byteString.length)
  const ia = new Uint8Array(ab)
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i)
  }
  return new Blob([ab], { type: mimeString })
}

// base64转成文件
const dataURLtoFile = (dataUrl: string, fileName: string, fileType: string) => {
  const arr = dataUrl.split(',')
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], fileName, {
    type: fileType
  })
}

const blobToDataURI = (blob: Blob) => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = (e: any) => {
      resolve(e.target.result)
    }
    fileReader.readAsDataURL(blob)
    fileReader.onerror = () => {
      reject(new Error('blobToBase64 error'))
    }
  })
}
// OSS对象

const client = (accessKeyId: string, accessKeySecret: string, securityToken: string) => {
  return new obsClient({
    access_key_id: accessKeyId,
    secret_access_key: accessKeySecret,
    security_token: securityToken,
    server: 'https://obs.cn-north-4.myhuaweicloud.com'
  })
}

function debounce(fn: Function, delay: number) {
  let timer: any = null
  return function () {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}

// 转义字符串中的 HTML 字符
function transferStr(str: string) {
  if(!str) return ''
  if (typeof str !== 'string')  str = str.toString()
  return str
    ?.replaceAll(/[<]/gi, '&lt;')
    .replaceAll(/[>]/gi, '&gt;')
    .replaceAll(/\n/gi, '<br>')
  // .replaceAll(/\s/gi, '&nbsp;')
}

function generateObjectHash(obj: any) {
  try {
    // 将对象转换为 JSON 字符串
    const jsonString = JSON.stringify(obj);

    // 计算 MD5 哈希
    const md5Hash = CryptoJS.MD5(jsonString).toString();

    return md5Hash;
  } catch (error) {
    console.error("Error generating object hash:", error);
    return '';
  }
}

// 图片预览
export function findImageAndPreview(e: any) {
  if (e.target.tagName === 'IMG') {
    viewerApi({
      images: [e.target.src],
      options: {
        title: false,
        navbar: false,
        toolbar: false
      }
    })
  }
}

// 将第一个校验失败的表单元素并将其滚动到可是区域
export function scrollToFirstErrFormItem() {
  let firstErrFormItemEle = document.querySelector('.ant-form-item-has-error')
  firstErrFormItemEle?.scrollIntoView({ behavior: 'smooth' })
}


// 读取文件并进行压缩
export function compressAndConvertToWebP(file: File, quality: number = 0.8) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);

    reader.onload = function () {
      const img = new Image();
      img.src = reader.result as string;

      img.onload = function () {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = img.width;
        canvas.height = img.height;
        ctx!.drawImage(img, 0, 0, img.width, img.height);

        canvas.toBlob(
          (blob) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob!);

            reader.onloadend = () => {
              resolve(reader.result);
            };
          },
          'image/webp',
          quality // 可调整压缩质量
        );
      };
    };

    reader.onerror = function (error) {
      reject(error);
    };
  });
}

export function downloadFile(res) {
  let fileName = decodeURIComponent(
    res.headers['content-disposition'].split(';')[1].split('=')[1].replace(/\"/g, '')
  )
  const blob = new Blob([res.data], { type: 'application/octet-stream' }) // 构造一个blob对象来处理数据，并设置文件类型 excel：application/vnd.ms-excel zip: application/zip

  if (window.navigator.msSaveOrOpenBlob) {
    //兼容IE10
    navigator.msSaveBlob(blob, fileName)
  } else {
    const href = URL.createObjectURL(blob) //创建新的URL表示指定的blob对象
    const a = document.createElement('a') //创建a标签
    a.style.display = 'none'
    a.href = href // 指定下载链接
    a.download = fileName //指定下载文件名
    document.body.appendChild(a)
    a.click() //触发下载
    URL.revokeObjectURL(a.href) //释放URL对象
    document.body.removeChild(a)
  }
}

const convertKeysToCamelCase = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }

  const camelCaseObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const camelCaseKey = key.replace(/[-_]([a-z])/g, (match, letter) => letter.toUpperCase());
      if(camelCaseObj[camelCaseKey] == null || camelCaseObj[camelCaseKey] == undefined) { 
        camelCaseObj[camelCaseKey] = convertKeysToCamelCase(obj[key]);
      };
    }
  }
  return camelCaseObj;
}

const convertBoolean = (obj) => {
  const camelCaseObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && typeof obj[key] === 'boolean') {
      camelCaseObj[key] = obj[key] ? 1 : 0;
    }else{
      camelCaseObj[key] = obj[key];
    }
  }
  return camelCaseObj;
}

const convertJson = (obj) => {
  const excludeKeys = ['ids', 'basePager', 'notpaperidsJSON'];
  const camelCaseObj = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key) && typeof obj[key] === 'object' && !key.includes('List') && !excludeKeys.includes(key)) {
      camelCaseObj[key] = obj[key] ? JSON.stringify(obj[key]) : '';
    }else{
      camelCaseObj[key] = obj[key];
    }
  }
  
  return camelCaseObj;
}

export {
  IdCard,
  checkCardNo,
  checkPhoneNo,
  aesDecrypt,
  client,
  dataURItoBlob,
  dataURLtoFile,
  blobToDataURI,
  debounce,
  transferStr,
  generateObjectHash,
  convertBoolean,
  convertJson,
  convertKeysToCamelCase
}
