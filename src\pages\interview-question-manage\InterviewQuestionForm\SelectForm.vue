<template>
  <div>
    <a-form-item name="options" class="question-options">
      <a-radio-group v-if="proxy.type === QuestionEnum['单选题']" v-model:value="proxy.answer" style="max-width:100%;">
        <div class="option-item" v-for="(item, index) in proxy.options" :key="item.id">
          <a-radio :value="item.value">
            <span class="option-radio">{{ item.value }}</span>
          </a-radio>
          <a-textarea v-model:value.trim="item.content" :auto-size="{ minRows: 1 }" placeholder="点击，编辑选项；选中即正确答案" />
          <svg-icon class="del-icon" name="circle-del" width="16px" height="16px"
            @click.prevent="handleOptionRemove($event, index)" />
        </div>
      </a-radio-group>
      <a-checkbox-group v-else-if="proxy.type === QuestionEnum['多选题']" v-model:value="proxy.answer"
        style="max-width:100%;">
        <template v-for="(item, index) in proxy.options" :key="item.id">
          <div class="option-item">
            <a-checkbox :value="item.value">
              <span class="option-radio">{{ item.value }}</span>
            </a-checkbox>
            <a-textarea :auto-size="{ minRows: 1 }" v-model:value.trim="item.content" placeholder="点击，编辑选项；选中即正确答案" />
            <svg-icon class="del-icon" name="circle-del" width="16px" height="16px"
              @click.prevent="handleOptionRemove($event, index)" />
          </div>
        </template>
      </a-checkbox-group>
      <a-radio-group v-else-if="proxy.type === QuestionEnum['判断题']" v-model:value="proxy.answer" style="max-width:100%;">
        <template v-for="item in proxy.options">
          <div class="option-item">
            <a-radio :value="item.value">
              <span class="option-radio">{{ item.value }}</span>
              <a-input style="cursor: initial;" readonly v-model:value="item.content" />
            </a-radio>
          </div>
        </template>
      </a-radio-group>
      <div v-if="proxy.type !== QuestionEnum['判断题']" style="display: flex; align-items: center; margin-top: 16px;">
        <div class="add-option-btn" @click="handleOptionAdd">
          <svg-icon name="plus" />
          <span>添加选项</span>
        </div>
      </div>
    </a-form-item>
  </div>
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import { convertABCTo123, convertToABC } from '@/utils';
import { QuestionEnum } from '@/models/questionModel';
import { message } from 'ant-design-vue';

const props = defineProps<{
  formState: any
}>();

const emits = defineEmits<{
  (e: 'update:formState', v: any): void
}>();

const proxy = useVModel(props, 'formState', emits, {
  passive: true,
  deep: true
});

/** 增加选项 */
function handleOptionAdd() {
  const currentOptionsNumber = proxy.value.options.length
  if (currentOptionsNumber >= 26) return message.warn('已达最多选项个数')
  proxy.value.options.push({ value: convertToABC(currentOptionsNumber), content: '' })
}

/** 删除选项 */
function handleOptionRemove(e: any, index: number) {
  e.preventDefault()
  e.stopPropagation()
  if (proxy.value.type === QuestionEnum['单选题'] && proxy.value.options.length <= 2) {
    return message.warning('至少保留2个选项')
  }
  if ([QuestionEnum['多选题'], QuestionEnum['排序题']].includes(proxy.value.type) && proxy.value.options.length <= 3) {
    return message.warning('至少保留3个选项')
  }
  proxy.value.options.splice(index, 1)

  // 重新设置选项值
  proxy.value.options.forEach((item: any, i: number) => {
    item.value = convertToABC(i)
  })

  // 处理已选答案
  if (!proxy.value.answer?.length) return; // 如果没有答案，则直接返回
  if (proxy.value.type === QuestionEnum['单选题']) {
    if (proxy.value.answer === convertToABC(index)) {
      // 如果删除的是答案，需要清空答案
      proxy.value.answer = ''
    } else {
      // 如果删除的在答案之前，答案需要减1
      const answerIndex = convertABCTo123(proxy.value.answer)
      if (answerIndex > index) {
        proxy.value.answer = convertToABC(answerIndex - 1)
      }
    }
  } else if (proxy.value.type === QuestionEnum['多选题']) {
    let newAnswer: string[] = []
    proxy.value.answer.forEach((item: string) => {
      let answerIndex = convertABCTo123(item)
      if (answerIndex > index) {
        newAnswer.push(convertToABC(answerIndex - 1))
      } else if (answerIndex < index) {
        newAnswer.push(convertToABC(answerIndex))
      }
    })
    proxy.value.answer = newAnswer
  }
}
</script>

<style lang="less" scoped>
.option-item {
  display: flex;
  align-items: center;

  .option-radio {
    display: inline-block;
    width: 24px;
  }

  .ant-input {
    border: none;
    box-shadow: none !important;
    width: 240px;
    background: transparent !important;
    cursor: text;
  }

  .del-icon {
    opacity: 0;
    transition: all 0.2s;
    cursor: pointer;
  }

  &:hover .del-icon {
    position: relative;
    opacity: 1;
  }
}

.add-option-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 82px;
  height: 24px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}
</style>
