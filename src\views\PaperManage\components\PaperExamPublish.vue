<template>
  <a-form
    class="form"
    ref="basicInfoFormRef"
    hideRequiredMark="true"
    :colon="false"
    labelAlign="left"
  >
    <p class="title">{{ paperInfo.name }}</p>
    <div class="detail">
      <a-row>
        <a-col :span="12">
          <a-form-item v-if="paperInfo.uniexam === '1'" label="考试时间">
            <span>{{ dayjs(paperInfo.startTime).format('YYYY-MM-DD HH:mm') }}</span>
          </a-form-item>
          <a-form-item v-else-if="paperInfo.uniexam === '0'" label="时间范围">
            <span
              >{{ dayjs(paperInfo.startTime).format('YYYY-MM-DD HH:mm') }} 至
              {{ dayjs(paperInfo.endTime).format('YYYY-MM-DD HH:mm') }}</span
            >
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="考试时长">
            <span>
              {{ Math.floor(totalSeconds/60) }}分<template v-if="!(totalSeconds % 60)">钟</template><template v-if="totalSeconds % 60">{{ totalSeconds % 60 }}秒</template>
            </span>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="总分值">
            <span>{{ paperInfo.score }}分</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="总题量">
            <span>{{ questionNumber }}道</span>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12" v-for="t in 7">
          <a-form-item :label="QuestionEnum[t - 1]">
            <span
              >{{
                paperInfo.fbody.find((item: any) => item.type === t - 1 && !item.options)?.children
                  .length ?? 0
              }}道</span
            >
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="选做题">
            <span>{{ paperInfo.fbody.filter((item: any) => item.options).length }}道</span>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="试卷类型">
            <span>指定试题</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="题目顺序">
            <span>{{ paperInfo.quesorder ? '固定' : '随机' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="选项顺序">
            <span>{{ paperInfo.opsorder ? '固定' : '随机' }}</span>
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-item label="阅卷姓名">
            <span>{{ !paperInfo.hiddenname ? '实名' : '匿名' }}</span>
          </a-form-item>
        </a-col> -->
        <a-col :span="12">
          <a-form-item v-if="paperInfo.uniexam === '1'" label="限时迟到">
            <span>{{ paperInfo.limitlateness ? `${paperInfo.limitlateness}分钟` : `不限时间`}}</span>
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <div>
      <a-form-item label="考试配置">
        <img src="@/assets/icons/svg/testKnown.svg" />
        <span class="configDetail">考试须知</span>
        <img v-if="paperInfo.examvideo" src="@/assets/icons/svg/testVideo.svg" />
        <span v-if="paperInfo.examvideo" class="configDetail">宣传视频</span>
        <img v-if="paperInfo.reference" src="@/assets/icons/svg/calculator.svg" />
        <span v-if="paperInfo.reference" class="configDetail">参考文档</span>
        <img v-if="paperInfo.calculator" src="@/assets/icons/svg/calculator.svg" />
        <span v-if="paperInfo.calculator" class="configDetail">计算器</span>
      </a-form-item>
      <!-- <div class="content">
        <div class="text" v-if="data.neterror">允许学生最多因网络原因离开考试<span style="color: #D71310">{{ data.neterror }}</span>次</div>
        <div class="text" v-else>允许学生因网络原因离开考试次数不限</div>
        <div class="text" v-if="data.cutscreen">允许学生最多切屏<span style="color: #D71310">{{ data.cutscreen }}</span>次，超出次数后强制交卷</div>
        <div class="text" v-else>允许学生切屏次数不限</div>
        <div class="text">考生作答页面，<span style="color: #D71310">{{ data.watermark ? '' : '不要求' }}使用</span>考生姓名和考生编号作为背景页面</div>
        <div class="text">考试期间，<span style="color: #D71310">{{ data.nocopy ? '禁止' : '允许' }}</span>考生对试卷内容进行复制粘贴，且<span style="color: #D71310">{{ data.nocopy ? '不得' : '允许' }}</span>粘贴外部内容</div>
      </div> -->
      <a-form-item label="反作弊">
        <div class="config-text-wrap">
          <div v-if="paperInfo.isneterror">
            <span class="testnorm">
              <span class="insertInput">允许</span>
              学生最多因网络原因离开考试
              <span class="insertInput">{{ paperInfo.neterror }}</span>次</span>
          </div>
          <div v-else>
            <span class="testnorm">
              允许学生因网络原因离开考试次数不限
            </span>
          </div>

          <div v-if="paperInfo.iscutscreen">
            <span class="testnorm">
              <span class="insertInput">允许</span >学生最多切屏
                <span class="insertInput">{{ paperInfo.cutscreen }}</span>
                次，超出次数后强制交卷
              </span>
          </div>
          <div v-else>
            <span class="testnorm">允许学生切屏次数不限</span>
          </div>

          <div>
            <span class="testnorm">考生作答页面，<span class="insertInput">{{ paperInfo.watermark ? '' : '不要求' }}使用</span>考生姓名和考生编号作为背景页面</span>
          </div>

          <div>
            <span class="testnorm">
              考试期间，<span class="insertInput">{{ paperInfo.nocopy ? '禁止' : '允许' }}</span>考生对试卷内容进行复制粘贴，且<span class="insertInput">{{ paperInfo.nocopy ? '不得' : '允许' }}</span>粘贴外部内容
            </span>
          </div>
        </div>
        <!-- <div class="config-text-wrap">
          <div v-if="paperInfo.isneterror">
            <span class="testnorm"
              >断网检测：<span class="insertInput">允许</span>学生最多离开考试<span
                class="insertInput"
                >{{ paperInfo.neterror }}</span
              >次</span
            >
          </div>
          <div v-if="paperInfo.iscutscreen">
            <span class="testnorm"
              ><span class="insertInput">禁止</span>切屏：<span class="insertInput">允许</span
              >学生最多切屏<span class="insertInput">{{ paperInfo.cutscreen }}</span
              >次，超出次数后强制交卷</span
            >
          </div>
          <div v-else>
            <span class="testnorm"
              >切屏：<span class="insertInput">允许</span>学生切屏次数不限</span
            >
          </div>
          <div v-if="paperInfo.watermark">
            <span class="testnorm">答题水印：考生作答页面，使用考生姓名和考生编号作为背景页面</span>
          </div>
          <div v-if="paperInfo.nocopy">
            <span class="testnorm"
              ><span class="insertInput">禁止</span>复制：考试过程中，<span class="insertInput"
                >禁止</span
              >考生对题干及选项等文字进行复制</span
            >
          </div>
        </div> -->
      </a-form-item>
      <a-form-item label="其他">
        <div class="config-text-wrap">
          <div class="other-info">阅卷时<span style="color: red;">{{ paperInfo.hiddenname ? '隐藏' : '显示'}}</span>考生真实姓名</div>
          <div class="other-info">本部门教师<span style="color: red;">{{ paperInfo.shareToDept ? '' : '不'}}可</span>编辑</div>
        </div>
      </a-form-item>
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue'
import dayjs from 'dayjs'
import { QuestionEnum } from '@/models/questionModel'
import { getTotalSeconds } from '@/utils'


const props = defineProps<{
  paperInfo?: any
}>()

// 计算时间总时长（秒）
const totalSeconds = computed(() => {
  if (props.paperInfo.individualTiming == '1') {
    return getTotalSeconds(props.paperInfo.fbody)
  } else {
    return props.paperInfo.duration * 60 ?? 0
  }
})

// 总题量
const questionNumber = computed(() => {
  return props.paperInfo.fbody?.map((item: any) => item.children).flat().length ?? 0
})

onMounted(() => {})
</script>

<style lang="less" scoped>
.form {
  margin-bottom: auto;
  flex: 1;
  min-height: 0;
  overflow: auto;
  :deep(.ant-col) {
    width: 90px;
  }
  :deep(.ant-input) {
    width: 220px;
    height: 32px;
    font-size: 12px;
    border-radius: 8px;
  }
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
  :deep(.ant-form-item-no-colon) {
    color: #626262;
    font-size: 12px;
  }
  .configDetail {
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    line-height: 20px;
    margin-right: 32px;
    margin-left: 8px;
    ~img {
      width: 16px;
      height: 16px;
    }
  }
  .title {
    text-align: center;
    height: 36px;
    font-size: 24px;
    font-weight: 600;
    color: rgb(0, 0, 0);
    line-height: 36px;
    margin: 0px auto 30px;
  }
  .detail {
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 25px;

    .totalNum {
      margin: -4px 0 0 8px;
    }
  }
  .testnorm {
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    line-height: 20px;
    margin-top: 8px;
    .insertInput {
      color: red;
    }
  }

  label {
    font-size: 12px;
  }
  .form-input {
    width: 220px;
    font-size: 12px;
    border-radius: 8px;
  }
  .config-text-wrap {
    padding-top: 5px;
  }
  .other-info {
    height: 20px;
    font-size: 12px;
    line-height: 20px;
  }
}
</style>
