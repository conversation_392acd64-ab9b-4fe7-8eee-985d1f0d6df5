<script setup lang="ts">
import { EditOutlined } from '@ant-design/icons-vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps<{
  item: {
    type: string
    id: string
    name: string
    total: number
    isEdit: boolean
    extractNum: number | string | any
  }
  type: string
}>()
const emits = defineEmits(['deletePkg', 'switchType', 'switchItemEdit'])

const wrapItem = useModel(props, 'item')
function deletePackageItem(item: any) {
  emits('deletePkg', item)
}
const addBtnRef = useTemplateRef('addBtn')
const anchorRef = useTemplateRef('anchor')
function highlightFlash() {
  if (anchorRef.value) {
    anchorRef.value.style.transition = 'background-color 0.3s'
    anchorRef.value.style.backgroundColor = '#f1f4fe'
    setTimeout(() => {
      anchorRef.value!.style.backgroundColor = ''
    }, 1000)
  }
}
defineExpose({
  item: props.item,
  $el: anchorRef,
  highlightFlash,
})
function popoverShowChange(vis: any) {
  addBtnRef.value!.style.visibility = vis ? 'visible' : ''
}
</script>

<template>
  <div ref="anchor" :class="!item.isEdit ? 'hover:bg-[#f1f4fe]' : ''" class="pkg-container cursor-pointer relative  text-[16px] p-[12px] rounded-[8px] w-full border-[1px] border-solid border-[#d9d9d9] flex flex-nowrap justify-between items-center mb-[20px]">
    <div v-if="!item.isEdit" ref="addBtn" class="add-btn">
      <a-popover placement="bottom" :overlay-inner-style="{ padding: 0 }" @open-change="popoverShowChange">
        <template #content>
          <div class="select-type">
            <p class="px-[12px] py-[8px] h-[32px] hover:text-[#5478ee] cursor-pointer active:bg-[#5478ee] active:text-[#fff] flex items-center" @click="emits('switchType', 1, item.id)">
              新增抽题范围
            </p>
            <p class="px-[12px] py-[8px] h-[32px] hover:text-[#5478ee] cursor-pointer active:bg-[#5478ee] active:text-[#fff] flex items-center" @click="emits('switchType', 2, item.id)">
              选择已有题目
            </p>   
            <p class="px-[12px] py-[8px] h-[32px] hover:text-[#5478ee] cursor-pointer active:bg-[#5478ee] active:text-[#fff] flex items-center" @click="emits('switchType', 3, item.id)">
              手动输入题目
            </p>
          </div>
        </template>
        <template #title />
        <div class="w-full border-[2px] border-solid border-[#5478ee] absolute top-[5px] left-[10px]" />
        <img src="@/assets/icons/svg/title_add.svg" class="absolute left-[-12px] top-[-6.5px]">
      </a-popover>
    </div>
    <div class="w-[90%]">
      <slot />
    </div>

    <div class="h-[100%] flex flex-nowrap " :class="type !== 'pkg' ? 'self-start' : ''">
      <el-icon v-if="type !== 'pkg' && !item.isEdit" class="del-icon mr-[18px]">
        <EditOutlined class="del-icon" @click="emits('switchItemEdit', item.id)" />
      </el-icon>
      <el-icon v-if="!item.isEdit" class="del-icon">
        <Delete @click="deletePackageItem(wrapItem)" />
      </el-icon>
    </div>
  </div>
</template>

<style scoped lang="less">
.del-icon{
      visibility: hidden;
}
.pkg-container{
      &:hover .del-icon{
            visibility: visible;
      }
            &:hover .add-btn{
            visibility: visible;
      }
}
.select-type:hover{
    .add-btn{
            visibility: visible;
      }  
}
.add-btn{
      padding-top: 10px;
      height: 30px;
      width: 100%;
      top: -15px;
      left: -10px;
      position: absolute;
      visibility: hidden;
}
</style>