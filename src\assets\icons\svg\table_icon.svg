<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="教师端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="教师端-统计分析-题库分析-添加图表" transform="translate(-592.000000, -431.000000)">
            <g id="弹窗" transform="translate(260.000000, 270.000000)">
                <g id="-mockplus-" transform="translate(324.000000, 153.000000)">
                    <g id="编组" transform="translate(8.000000, 8.000000)">
                        <rect id="bg" fill="#000000" opacity="0" transform="translate(16.000000, 16.000000) scale(-1, 1) translate(-16.000000, -16.000000) " x="0" y="0" width="32" height="32"></rect>
                        <rect id="矩形" fill="#5478EE" x="4" y="6" width="24" height="20" rx="4"></rect>
                        <rect id="矩形" fill="#FFFFFF" x="6" y="8" width="20" height="16" rx="2"></rect>
                        <rect id="矩形备份-4" fill="#5478EE" opacity="0.5" x="8.5" y="15" width="5" height="2.5" rx="1.25"></rect>
                        <rect id="矩形备份-3" fill="#5478EE" opacity="0.5" x="15" y="11" width="8.5" height="2.5" rx="1.25"></rect>
                        <rect id="矩形备份-5" fill="#5478EE" opacity="0.5" x="15" y="15" width="8.5" height="2.5" rx="1.25"></rect>
                        <rect id="矩形备份-7" fill="#5478EE" opacity="0.5" x="8.5" y="19" width="5" height="2.5" rx="1.25"></rect>
                        <rect id="矩形备份-6" fill="#5478EE" opacity="0.5" x="15" y="19" width="8.5" height="2.5" rx="1.25"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>