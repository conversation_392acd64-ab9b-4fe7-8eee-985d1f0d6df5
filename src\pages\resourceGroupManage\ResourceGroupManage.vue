<template>
    <div class="resource-group-manage">
        <div class="title-wrap">
            <span class="title">资源组管理</span>
        </div>
        <div class="filter-wrapper">
            <a-input-search class="search-ipt" v-model:value.trim="searchValue" placeholder="请输入关键词" allow-clear
            @blur="handleSearch" @search="handleSearch" />
            <a-button type="primary" @click="showFormModal()">新增资源组</a-button>
        </div>
        <ListWrapper ref="listWrapperRef" :card-min-width="386" :params="params" :getListFn="getListFn">
            <template #item="{ item, index }">
                <div class="list-item">
                    <div class="item-left" @click="showDetailModal(item)">{{ item.name.slice(0, 1) }}</div>
                    <div class="item-main" @click="showDetailModal(item)">
                        <div class="title">
                            <SearchHighLight class="item-name" :text="item.name" :search-text="params.name"></SearchHighLight>
                        </div>
                        <div class="info-item">
                            <span class="label">部门</span>
                            <span class="value">
                                <TagEllipsis :data="item.dept_names"></TagEllipsis>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="label">说明</span>
                            <span class="value">{{ item.description }}</span>
                        </div>
                    </div>
                    <div class="item-right">
                        <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                            <template #title>
                                <span style="color: #666666;">编辑</span>
                            </template>
                            <img class="icon-img" src="@/assets/icons/svg/edit.svg" alt="" @click="showFormModal(item)">
                        </a-tooltip>
                        <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
                            <template #title>
                                <span style="color: #666666;">删除</span>
                            </template>
                            <a-popconfirm title="确认删除该资源组吗？" ok-text="确定" cancel-text="取消" placement="left"
                                @confirm="handleRemove(item)">
                                <a-button type="text" danger size="small">
                                    <img class="icon-img" src="@/assets/icons/svg/delete.svg" alt="">
                                </a-button>
                            </a-popconfirm>
                        </a-tooltip>
                    </div>
                </div>
            </template>
        </ListWrapper>
        <a-modal :title="formMode + '资源组'" v-model:visible="formVisible" width="400px" :okText="formState.id ? '保存' : '新增'"
            @ok="hanldeSubmit" :confirmLoading="confirmLoading" :maskClosable="false" :keyboard="false"
            :afterClose="resetFormState">
            <a-form ref="formRef" :model="formState" :rules="rules" :labelCol="{ span: 7 }" labelAlign="left"
                :colon="false">
                <a-form-item label="资源组名称" name="name">
                    <a-input v-model:value="formState.name" autocomplete="off" />
                </a-form-item>
                <a-form-item label="说明" name="description">
                    <a-textarea v-model:value="formState.description" autocomplete="off" :maxlength="100" showCount
                        :rows="4" />
                </a-form-item>
            </a-form>
        </a-modal>
        <a-modal wrapClassName="resource-group-info-modal" :title="detailItem?.name" v-model:visible="detailVisible"
            :footer="null">
            <ul class="detail-info">
                <li>
                    <span class="detail-info-label">部门</span>
                    <span class="detail-info-value">
                        <template v-for="(deptName, i) in detailItem?.dept_names">
                            <span class="dept-tag">{{ deptName }}</span>
                        </template>
                    </span>
                </li>
                <li>
                    <span class="detail-info-label">说明</span>
                    <span class="detail-info-value">{{ detailItem?.description }}</span>
                </li>
            </ul>
            <a-button type="primary" style="margin: 40px auto 0;display: block;"
                @click="detailVisible = false">关闭</a-button>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import ListWrapper from '@/components/ListWrapper.vue';
import { resourcegroup } from '@/api/admin/systemManage'
import { ref, reactive, onMounted, computed } from 'vue';
import type { Rule, FormInstance } from 'ant-design-vue/es/form';
import { message } from 'ant-design-vue';
import { useState } from '@/hooks';
import TagEllipsis from '@/components/TagEllipsis.vue';
import SearchHighLight from '@/components/SearchHighLight.vue';


// 模糊查询
const searchValue = ref('')
function handleSearch() {
    params.name = searchValue.value
}

const params = reactive({
    action: 'query',
    name: ''
})
const getListFn = (params: any) => resourcegroup(params)
const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()

// 表单部分
const formRef = ref<FormInstance>()
const formVisible = ref(false)
const formMode = ref('新增')
const [formState, resetFormState] = useState<{
    id?: string
    name: string
    description: string
    shared_data_type: string[]
}>({
    name: '',
    description: '',
    shared_data_type: ["question", "paper", "student"]
})

const rules: Record<string, Rule[]> = {
    name: [{ required: true, trigger: 'blur' }],
    description: [{ required: true, trigger: 'blur' }],
};

/** 
 * @description 新增/编辑资源组
 * @param id 编辑时传入id
 */
function showFormModal(record?: any) {
    formMode.value = record ? '编辑' : '新增'
    if (record) {
        formState.value.id = record.id
        formState.value.name = record.name
        formState.value.description = record.description
    }
    formVisible.value = true
}

const confirmLoading = ref(false)
async function hanldeSubmit() {
    confirmLoading.value = true
    await formRef.value?.validate()
    try {
        let param;
        if (formState.value.id) {
            param = { action: 'modify', id: formState.value.id, ...formState.value }
        } else {
            param = { action: 'add', ...formState.value }
        }
        await resourcegroup(param)
        message.success(formState.value.id ? '编辑成功' : '新增成功')
        formVisible.value = false
        formRef.value?.resetFields()
        listWrapperRef.value?.research()
    } catch (error) {

    } finally {
        confirmLoading.value = false
    }
}

// 详情部分
const detailItem = ref<{
    dept_names: string[]
    description: string
    name: string
}>()
const detailVisible = ref(false)
function showDetailModal(item: any) {
    detailItem.value = item
    detailVisible.value = true
}

/** 
 * @description 删除资源组
 * 
 */
async function handleRemove({ id }: any) {
    try {
        await resourcegroup({ action: 'del', id })
        message.success('删除成功')
        listWrapperRef.value?.research()
    } catch (error) {

    }
}

// 循坏10~100 新增资源组
// onMounted(() => {
//     for (let i = 10; i < 100; i++) {
//         resourcegroup({ action: 'add', name: `资源组${i}`, description: `资源组${i}的说明`, shared_data_type: ["question", "paper", "student"] })
//     }
// })



</script>
<style lang="less">
.resource-group-info-modal {
    .detail-info {
        display: flex;
        flex-direction: column;

        li {
            margin-bottom: 16px;
            display: flex;
        }

        .detail-info-label {
            color: #919191;
            display: inline-block;
            width: 100px;
            flex-shrink: 0;
        }

        .detail-info-value {
            flex: 1;
            min-width: 0;
        }
    }
}

.dept-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    color: #2f8c00;
    background-color: #f3f7f1;
    margin-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
}
</style>

<style lang="less" scoped>
.resource-group-manage {
    height: 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
        height: 64px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }

        .btns {
            height: 32px;

            :deep(.ant-btn) {
                margin-left: 8px;
            }
        }
    }
}

.filter-wrapper {
    padding: 24px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
    background: #ffffff;
    border-radius: 8px;
    margin: 0 20px 20px;
    display: flex;
    justify-content: space-between;

    .search-ipt {
        width: 240px;
    }
}

.list-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 24px;
    user-select: none;
    
    .item-left {
        width: 50px;
        height: 50px;
        background-color: #F1F4FE;
        color: #5478EE;
        font-size: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        cursor: pointer;
    }

    .item-main {
        margin-left: 32px;
        flex: 1;
        min-width: 0;
        cursor: pointer;

        .title {
            margin-bottom: 20px;
        }

        .info-item {
            margin-top: 8px;
            display: flex;
            height: 30px;
            font-size: 12px;

            .label {
                color: #919191;
                display: inline-block;
                width: 100px;
                flex-shrink: 0;
                img {
                    width: 14px;
                }
            }

            .value {
                flex: 1;
                min-width: 0;
            }
        }
    }

    .item-right {
        font-size: 18px;

        .icon-img:not(:is(.ant-btn .icon-img)) {
            margin-left: 16px;
            cursor: pointer;
        }

        .ant-btn:has(.icon-img) {
            margin-left: 16px;
            cursor: pointer;
        }
    }

    .dot {
        font-weight: bold;
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 18px;
        transform: rotate(90deg);
        cursor: pointer;
    }

    .item-name {
        width: 100%;
        font-weight: 600;
        font-size: 18px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
            color: #5478EE;
        }
    }
}
</style>