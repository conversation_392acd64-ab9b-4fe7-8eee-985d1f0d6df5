import { findDeletedBlanks, uuid } from '@/utils'
import { watch } from 'vue'

export default (formState: any, fillBlank: any, fillBlankCount: any, oldFillBlank: any) => {
  // 监听题干内容
  // let oldFillBlankCount = ref(0)
  watch(
    [() => formState.value.body, () => formState.value.type],
    ([val, newType]) => {
      if (newType === 5) {
        if (val.length === 0) {
          fillBlank.value.length = 0
          fillBlankCount.value = 0
          oldFillBlank.value = ''
          return
        }
        const res = val.match(/_{2,}/g)
        if (res) {
          if (fillBlankCount.value > res.length) {
            // 说明删除了空格
            let deletedBlankIndexArray = []
            if (res.length === 0) {
              deletedBlankIndexArray.push(0)
            } else {
              deletedBlankIndexArray = findDeletedBlanks(oldFillBlank.value, val)
            }
            deletedBlankIndexArray.forEach((item: any) => {
              fillBlank.value.splice(item, 1)
            })
            console.log(fillBlank.value)
            // 动态更新该题总分
            if (fillBlank.value.length > 0) {
              formState.value.score = fillBlank.value.reduce(
                (prev: any, current: any) => prev + Number(current.score),
                0
              )
            } else {
              formState.value.score = 0
            }
            console.log('deletedBlankIndexArray', deletedBlankIndexArray)
          } else if (fillBlankCount.value < res.length) {
            // 说明增加了空格, 于删除空格的逻辑相反
            let addedBlankIndexArray = []
            if (res.length === 1) {
              addedBlankIndexArray.push(0)
            } else {
              addedBlankIndexArray = findDeletedBlanks(val, oldFillBlank.value)
            }
            addedBlankIndexArray.forEach((item: any) => {
              fillBlank.value.splice(item, 0, { score: 0, id: uuid() })
            })
          }

          fillBlankCount.value = res.length
        } else {
          if (fillBlank.value.length > 0) {
            fillBlank.value.length = 0
            fillBlankCount.value = 0
          }
        }
        oldFillBlank.value = val
      }
    }
  )
}
