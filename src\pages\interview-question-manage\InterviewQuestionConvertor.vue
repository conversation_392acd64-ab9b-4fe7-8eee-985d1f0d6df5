<template>
  <a-modal
    @ok="handleConfirm"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    :okButtonProps="{ disabled: convertLoading || !convertedFormState }"
    okText="确认"
  >
    <template #title>
      <span style="display: flex; align-items: center;">
        <img src="@/assets/icons/svg/ai.svg" style="margin-right: 12px; width: 22px;" />更改题型
      </span>
    </template>
    <div v-if="typeof formState?.type === 'number'" class="type-wrapper">
      更改当前题型为
      <ul>
        <li
          v-for="tp in typeMap[rawType]"
          :class="{ active: activeType === tp, disabled: convertLoading }"
          @click="handleConvert(tp)"
        >
          <svg-icon :name="QUESTIONS.find((i) => i.value == tp)?.icon" style="font-size: 16px; margin-right: 4px;" />
          <span>{{ QuestionEnum[tp] }}</span>
        </li>
      </ul>
    </div>
    <a-spin :spinning="convertLoading">
      <div class="question-item-dispaly-box">
        <QuestionItemDisplay
          v-if="convertedFormState"
          :question-detail="convertedFormState"
          :show-correct-answer="true"
          option-letter-type="text"
          :show-type="true"
        ></QuestionItemDisplay>
        <p v-else style="user-select: none; color: rgba(0, 0, 0, 0.4);">请点击题型进行生成</p>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { QuestionEnum } from '@/models/questionModel';
import { QUESTIONS } from '@/config/constants';
import QuestionItemDisplay from './QuestionItemDisplay/index.vue';
import { aichangequestype } from '@/api/exam';
import { uuid } from '@/utils';

const props = defineProps<{
  id: string;
  formState: any;
  rawType: number;
}>();

const emits = defineEmits<{
  (e: 'confirm', val: any): void;
}>();

const typeMap = {
  0: [QuestionEnum['判断题'], QuestionEnum['填空题']],
  1: [QuestionEnum['单选题'], QuestionEnum['判断题'], QuestionEnum['填空题']],
  2: [QuestionEnum['单选题'], QuestionEnum['填空题']],
  5: [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['判断题']],
};

function changeTypeMap() {
  if (props.formState.type === QuestionEnum['填空题']) {
    // 如果是一个空的填空题则，不支持转多选
    let res = props.formState.body.match(/_{2,}/g);
    if (res.length <= 1) {
      typeMap['5'] = [QuestionEnum['单选题'], QuestionEnum['判断题']];
    } else {
      typeMap['5'] = [QuestionEnum['单选题'], QuestionEnum['多选题'], QuestionEnum['判断题']];
    }
  }
}
watch(
  () => props.formState.body,
  changeTypeMap,
  { immediate: true }
);

// 题型转换
const activeType = ref(-1);
const convertLoading = ref(false);
const convertedFormState = ref();
let requestId = '';
async function handleConvert(type: number) {
  if (convertLoading.value) return;

  // 记录最新的请求id
  let tempRequestId = uuid();
  requestId = tempRequestId;

  activeType.value = type;

  try {
    convertLoading.value = true;
    let res = await aichangequestype({
      old_ques: props.id,
      new_type: type,
    });

    // 如果不是最新点击的回调，则忽略本次请求的返回
    if (tempRequestId !== requestId) return;

    convertedFormState.value = res;
  } catch (error) {
    console.log(error);
  } finally {
    convertLoading.value = false;
  }
}

function handleConfirm() {
  emits('confirm', convertedFormState.value);
}
</script>

<style lang="less" scoped>
.type-wrapper {
  display: flex;
  align-items: center;

  ul {
    display: flex;
    align-items: center;

    li {
      width: 76px;
      height: 28px;
      border: 1px solid transparent;
      background-color: #f5f5f5;
      border-radius: 8px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all ease 0.2s;
      cursor: pointer;

      &.active {
        border: 1px solid #5478ee;
        &.disabled {
          border: 1px solid rgba(84, 120, 238, 0.25);
        }
      }
      &.disabled {
        color: rgba(0, 0, 0, 0.25);
        background: #f5f5f5;
        text-shadow: none;
        box-shadow: none;
        cursor: not-allowed;
      }
    }
  }
}
.question-item-dispaly-box {
  margin-top: 16px;
  background-color: #f5f5f5;
  min-height: 100px;
  border-radius: 8px;
  padding: 16px;
}
</style>