<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>交卷</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="90.9761015%" id="linearGradient-1">
            <stop stop-color="#C9CCD4" offset="0%"></stop>
            <stop stop-color="#C9CCD4" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#DFE2E4" offset="0%"></stop>
            <stop stop-color="#DDDFE2" offset="100%"></stop>
        </linearGradient>
        <path d="M8.11768152,19.5313705 C9.19082849,17.9889167 11.2796889,8.54446363 4.8311112,2.43230481 C-0.658246305,-2.77067579 -0.0246778097,1.75944296 0.0697192933,3.29446889 C0.438362301,9.28910811 4.2937658,14.6916401 5.97783343,16.8211283 C7.66190106,18.9506165 7.57931717,20.3051714 8.11768152,19.5313705 Z" id="path-3"></path>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#C9CCD4" offset="0%"></stop>
            <stop stop-color="#B3B8C3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#C9CCD4" offset="0%"></stop>
            <stop stop-color="#B3B8C3" offset="100%"></stop>
        </linearGradient>
        <path d="M10.3047255,12.2666084 C6.94750281,12.0646549 3.5961936,10.6588898 1.58094688,6.11936398 C0.638567755,3.99656962 -0.271197734,0.525272139 0.0752379326,0.115710318 C0.640891861,-0.553014862 4.20044793,1.84566833 5.32977547,2.98686436 C7.45699782,5.13644238 8.19082536,6.87712297 9.59364437,9.31931399 C10.2417508,10.4476133 11.1790476,12.3192031 10.3047255,12.2666084 Z" id="path-7"></path>
        <linearGradient x1="66.1306417%" y1="52.2565174%" x2="6.96117663%" y2="22.3752604%" id="linearGradient-9">
            <stop stop-color="#CDD0D7" offset="0%"></stop>
            <stop stop-color="#B5BBC6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="53.6226988%" y1="-60.8712576%" x2="53.6226988%" y2="114.964266%" id="linearGradient-10">
            <stop stop-color="#CCCCCC" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="53.6226988%" y1="-44.9713169%" x2="53.6226988%" y2="125.284869%" id="linearGradient-11">
            <stop stop-color="#CCCCCC" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="144.803927%" id="linearGradient-12">
            <stop stop-color="#999FAA" offset="0%"></stop>
            <stop stop-color="#B1B8C1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-13">
            <stop stop-color="#D1D3DB" offset="0%"></stop>
            <stop stop-color="#B0B4C0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#D1D3DB" offset="0%"></stop>
            <stop stop-color="#B0B4C0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="83.6097859%" y1="87.6360868%" x2="20.3448833%" y2="11.8921209%" id="linearGradient-15">
            <stop stop-color="#DADCE1" offset="0%"></stop>
            <stop stop-color="#CED3D9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="教师端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="教师端-试卷管理-监考" transform="translate(-1238.000000, -885.000000)">
            <g id="摄像头备份-11" transform="translate(1180.000000, 714.000000)">
                <g id="交卷" transform="translate(58.000000, 171.000000)">
                    <rect id="bg" x="0" y="0" width="100" height="100"></rect>
                    <path d="M49.7755063,62.4019979 C69.6071572,62.4019979 87.1428123,72.2783005 97.7755063,87.402394 L1.77550632,87.402394 C12.4082004,72.2783005 29.9438554,62.4019979 49.7755063,62.4019979 Z" id="背景" fill-opacity="0.3" fill="url(#linearGradient-1)"></path>
                    <g id="植物" transform="translate(23.008584, 49.186423)">
                        <g id="路径-10" transform="translate(1.879956, 0.000000)">
                            <mask id="mask-4" fill="white">
                                <use xlink:href="#path-3"></use>
                            </mask>
                            <use id="路径-5" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                            <path d="M7.54603997,19.4233121 C6.12905805,13.8837468 5.10670026,10.1802891 4.4789666,8.31293886 C3.76995008,6.20379251 1.79454337,2.53649254 2.18484144,3.11699814 C4.32545157,6.30081136 5.12296282,8.44703537 5.69128072,10.2222536 C5.91425808,10.9187537 6.28346324,12.1440457 6.89842749,14.427306 C7.18507448,15.4915788 7.72744534,17.5640295 8.52554008,20.6446584 L7.54603997,19.4233121 Z" id="路径-8" fill="#F0F0F0" mask="url(#mask-4)"></path>
                        </g>
                        <g id="编组-3" transform="translate(0.000000, 10.222254)">
                            <path d="M8.90660705,9.52904293 C5.54938437,9.32708949 2.85593625,8.32007824 0.840689527,3.78055244 C-1.6403893,-1.80830238 2.08511142,0.279245932 2.85593625,0.887791639 C3.62676108,1.49633735 6.79270692,4.13955753 8.19552593,6.58174855 C8.84363237,7.71004787 9.78092914,9.5816377 8.90660705,9.52904293 Z" id="路径-6" fill="url(#linearGradient-5)"></path>
                            <path d="M9.17899664,9.40598865 C8.74297981,9.20531069 8.13940807,8.74849959 7.79830832,8.5212519 C6.71766737,7.80130661 5.98743077,7.23210583 5.19712209,6.46407647 C4.02657727,5.32653008 3.45718866,4.53457994 2.3015809,3.16606427 C0.725820219,1.29998718 5.10358933,5.95352086 6.43600998,7.0396636 C7.205363,7.66681329 7.67519871,8.09807967 8.29766941,8.59970343 C8.68069948,8.90837175 9.61501346,9.60666661 9.17899664,9.40598865 Z" id="路径-7" fill="#F0F0F0"></path>
                        </g>
                        <g id="编组-2" transform="translate(9.678797, 7.484688)">
                            <mask id="mask-8" fill="white">
                                <use xlink:href="#path-7" transform="translate(5.329775, 6.133847) scale(-1, 1) translate(-5.329775, -6.133847) "></use>
                            </mask>
                            <use id="路径-6" fill="url(#linearGradient-6)" transform="translate(5.329775, 6.133847) scale(-1, 1) translate(-5.329775, -6.133847) " xlink:href="#path-7"></use>
                            <path d="M0.898394625,11.57711 C1.69369394,10.7154304 2.42430235,9.90909733 3.09021987,9.15811096 C5.32977547,6.6324592 7.50163401,3.77178476 8.085222,3.18504977 C9.26837323,1.99551837 6.49832566,5.59611572 5.71823286,6.6324592 C3.95652324,8.97286828 2.5524217,10.4141881 2.10278278,10.850514 C1.45404177,11.480047 0.75311418,12.0041868 0,12.4229335 L0.898394625,11.57711 Z" id="路径-9" fill="#F0F0F0" mask="url(#mask-8)"></path>
                        </g>
                        <path d="M17.1122323,16.1059949 C18.307599,16.0115283 21.8319323,16.0514949 22.0935323,20.7021616 C20.651099,20.7348616 0.293532327,20.7021616 0.293532327,20.7021616 C0.293532327,20.7021616 4.07219899,19.9972949 4.46096566,19.5067949 C4.84973233,19.0162949 8.27596566,16.9053283 10.4123657,18.6820283 C10.8810657,18.0861616 12.3707323,17.6210949 13.2245657,18.6820283 C13.6169657,17.0034283 16.051299,15.9315949 17.1122323,16.1059949 Z" id="路径-47" fill="url(#linearGradient-9)" transform="translate(11.193532, 18.401109) scale(-1, 1) translate(-11.193532, -18.401109) "></path>
                    </g>
                    <path d="M62.4703068,18.792183 C62.3968477,18.614657 62.3213482,18.4391715 62.3213482,18.2616455 C62.3213482,17.2638267 63.2865184,16.3823181 64.5475654,16.3823181 C65.0679004,16.3823181 65.5127357,16.5598442 65.884112,16.794505 C66.7737827,15.4436747 68.406206,14.5029908 70.3365466,14.5029908 C73.0075992,14.5029908 75.1583169,16.3231428 75.1583169,18.5554817 L75.1583169,18.792183 C76.3459049,19.2023294 77.1621165,20.1430133 77.1621165,21.2591828 C77.1621165,22.7263232 75.7521109,23.901668 73.970729,23.901668 L64.1027301,23.901668 C62.3213482,23.7833173 60.8378835,22.6691884 60.8378835,21.1408321 C60.8378835,20.1430133 61.5051365,19.2615047 62.4703068,18.792183 Z" id="云" fill="url(#linearGradient-10)"></path>
                    <path d="M24.8472831,32.3635466 C22.9190035,32.3635466 21.2104888,33.3088707 20.1601264,34.7611275 C19.5023415,34.3894675 18.7423056,34.1773505 17.9323788,34.1773505 C15.4286495,34.1773505 13.398323,36.207679 13.398323,38.7114082 C13.398323,39.6222596 13.6665955,40.4700179 14.1283591,41.1801832 C14.4129761,42.8671703 15.8818919,44.1534994 17.6494186,44.1534994 L32.2738909,44.1534994 C34.2449808,44.1534994 35.8447556,42.5537246 35.8447556,40.5825734 L35.8447556,39.7888076 C35.8447556,37.8176565 34.2449808,36.2178816 32.2738909,36.2178816 L30.3010257,36.2178816 C29.5070762,33.9729364 27.3648675,32.3635466 24.8472831,32.3635466 Z" id="云" fill="url(#linearGradient-11)"></path>
                    <g id="编组-4" transform="translate(29.750000, 30.499918)">
                        <g id="页面">
                            <path d="M46.5,7.00032994 L6.5,7.00032994 L6.5,5.00032994 C6.5,2.23890619 8.73857625,0.000329940996 11.5,0.000329940996 C21.7805655,0.000329940996 32.061131,0.000329940996 42.3416965,0.000329940996 C45.2539803,0.000329940996 46.5,2.60119659 46.5,5.53957579 C46.5,6.26361601 46.5,6.75053406 46.5,7.00032994 Z" id="路径-2" fill="url(#linearGradient-12)"></path>
                            <path d="M6.5,42.0003299 L34.5,42.0003299 C36.709139,42.0003299 38.5,40.2094689 38.5,38.0003299 L38.5,7.39619074 L38.5,7.39619074 C38.5,6.15313348 38.5,5.29098093 38.5,4.80973308 C38.5,4.08786131 38.5612157,3.2165705 39.404777,1.81010455 C40.0564437,0.723583633 41.0354169,0.12021545 42.3416965,0 L11.5,0 C8.73857625,5.07265313e-16 6.5,2.23857625 6.5,5 L6.5,42.0003299 L6.5,42.0003299 Z" id="路径-3" fill="url(#linearGradient-13)"></path>
                            <path d="M30.5,34.0003299 L30.5,37.0003299 C30.5,39.7617537 31.7385763,42.0003299 34.5,42.0003299 L5.5,42.0003299 C2.73857625,42.0003299 0.5,39.7617537 0.5,37.0003299 L0.5,34.0003299 L30.5,34.0003299 Z" id="路径" fill="url(#linearGradient-14)"></path>
                        </g>
                        <g id="内容" transform="translate(12.671475, 10.856855)" fill="#FFFFFF" fill-opacity="0.550948563">
                            <circle id="椭圆形" cx="1.5" cy="1.5" r="1.5"></circle>
                            <line x1="7" y1="1.5" x2="19" y2="1.5" id="路径-4" stroke-opacity="0.55" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"></line>
                            <circle id="椭圆形" cx="1.5" cy="7.5" r="1.5"></circle>
                            <line x1="7" y1="7.5" x2="19" y2="7.5" id="路径-4" stroke-opacity="0.55" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"></line>
                        </g>
                        <g id="对号" transform="translate(29.500000, 28.498332)">
                            <circle id="椭圆形" fill="#949CA8" cx="8" cy="8" r="8"></circle>
                            <circle id="椭圆形" fill="url(#linearGradient-15)" cx="8.50084823" cy="8.50183295" r="8"></circle>
                            <polyline id="路径" stroke="#A5ACB6" stroke-linecap="round" stroke-linejoin="round" transform="translate(8.500848, 7.087619) rotate(-45.000000) translate(-8.500848, -7.087619) " points="12.5008482 9.08761939 4.50084823 9.08761939 4.50084823 5.08761939"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>