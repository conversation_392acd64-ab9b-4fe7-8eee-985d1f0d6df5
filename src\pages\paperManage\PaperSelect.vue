<template>
    <div class="paper-select">
        <div class="top-bar">
            <a-input-search class="search-wrapper" v-model:value.trim="searchContent" placeholder="请输入考试名称/创建人"
                @search="getFirstPageList" @keyup.enter="getFirstPageList" />
            <div style="margin-right: 16px;">
                <span style="color: #626262;">考试开始时间：</span>
                <a-range-picker class="time-range-wrapper" v-model:value="otherParams.start_time_range" :placeholder="['选择时间', '选择时间']" valueFormat="YYYY-MM-DD" @change="getFirstPageList" />
            </div>
            <div>总计排除<span style="color: #FF0000;">{{ total }}</span>道
                <a-tooltip placement="right" overlayClassName="light">
                    <template #title>
                        <span>将所选试卷中的题目标记为“已排除”，以便您区分题目中哪些是已经考过的试题</span>
                    </template>
                    <svg-icon class="common-info-icon" name="info2"></svg-icon>
                </a-tooltip>
            </div>
        </div>
        <a-table 
            :dataSource="dataSource" :columns="tableColumns" :row-key="(record: any) => record.id" 
            :scroll="{ x: 1060, y: 500 }"
            :row-selection="{
                selectedRowKeys: selectedRowKeys,
                onSelect: updateSelectedRowKeys,
                onSelectAll: onSelectAll
            }" :loading="loading" :pagination="paginationConfig" @change="handleTableChange" @resizeColumn="(w: any, col: any) => col.width = w">
            <template #bodyCell="{ column, record }">
                <span v-if="column.key === 'body'">{{ record.quesIdList.length }}</span>
                <span v-if="column.key === 'startTime'">{{ record.startTime?.slice(0, 16) + ' 至 ' + record.endTime?.slice(0, 16) }}</span>
            </template>
        </a-table>
    </div>
    <div class="btn-group">
        <a-button type="primary" @click="handleSave">确定</a-button>
        <a-button @click="emits('close')">取消</a-button>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, reactive } from 'vue'
import { queryTestPaper } from '@/api/admin/paperManage'

const props = defineProps<{
    defaultSelectedMap: Record<string, string[]>
}>()

const emits = defineEmits<{
    (e: 'confirm', value: Record<string, string[]>): void
    (e: 'close'): void
}>()

// 请求参数
const searchContent = ref('')
const otherParams = ref<{
    start_time_range?: [string, string]
    order_field?: string
    order_type?: 'ascend' | 'descend'
}>({})

// 表格配置
const tableColumns = ref([
    {
        title: '考试名称',
        dataIndex: 'name',
        key: 'name',
        width: 480,
        ellipsis: true,
        resizable: true
    },
    {
        title: '创建人',
        dataIndex: 'teacherName',
        key: 'create_by',
        width: 120,
        ellipsis: true,
        resizable: true,
        filters: [
            { text: '本人', value: 1 },
            { text: '其他', value: 0 },
        ],
    },
    {
        title: '考试时间',
        dataIndex: 'startTime',
        width: 300,
        key: 'startTime',
        ellipsis: true,
        resizable: true,
        sorter: true
    },
    {
        title: '题量',
        dataIndex: 'body',
        width: 100,
        key: 'body',
        ellipsis: true,
        resizable: true
    },
])
const paginationConfig = reactive({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total: number) => '总条数：' + total,
    total: 0,
    pageSizeOptions: ['10', '20', '50', '100'],
    size: 'small'
})

// 数据源
const dataSource = ref<any[]>([])
const loading = ref(false)
async function getList() {
    loading.value = true
    const params = {
            action: 'query',
            basePager:{
               current: paginationConfig.current,
               size: paginationConfig.pageSize
            },
            page: paginationConfig.current,
            per_page: paginationConfig.pageSize,
            name: searchContent.value,
            ...otherParams.value
        }
    if (params.start_time_range && params.start_time_range.length) {
        params.startTime  = params.start_time_range[0]
        params.endTime = params.start_time_range[1]
    }
    params.createByList = params.create_by
    params.startTimeOrder = params.order_type == 'ascend' ? 'asc' : 'desc'

    try {
        let res = await queryTestPaper(params) as any
        dataSource.value = res.records
        paginationConfig.total = res.total
    } finally {
        loading.value = false
    }
}
const handleTableChange = (pagination: any, filters: any = {}, sort: any = {}) => {
    // 处理排序
    otherParams.value.order_type = sort.order
    otherParams.value.order_field = sort.order ? sort.field : undefined
    // 处理分页
    paginationConfig.current = pagination.current
    paginationConfig.pageSize = pagination.pageSize
    // 处理筛选
    Object.assign(otherParams.value, filters)
    getList()
}

getList()

function getFirstPageList() {
    paginationConfig.current = 1
    getList()
}

// 已选列表
const selectMap = ref<Record<string, string[]>>({ ...props.defaultSelectedMap })
const selectedRowKeys = computed(() => Object.keys(selectMap.value).map(Number))
const total = computed(() => [...new Set(Object.values(selectMap.value).flat())].length)
function updateSelectedRowKeys(record: any) {
    let id = record.id
    let index = selectedRowKeys.value.indexOf(id)
    if (index > -1) {
        Reflect.deleteProperty(selectMap.value, id)
    } else {
        Reflect.set(selectMap.value, id, record.quesIdList)
    }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any[]) => changeRows.forEach(updateSelectedRowKeys)

function handleSave() {
    emits('confirm', selectMap.value)
}
</script>
<style lang="less">
.paper-select {
    height: 100%;

    .ant-table-thead>tr>th {
        background: #f1f4fe !important;
        color: #121633;
        font-weight: bold;
        font-size: 15px;

        .ant-table-column-title {
            color: #121633;
            font-weight: bold;
            font-size: 15px;
        }
    }

    .ant-table-tbody>tr>td {
        padding: 10px;
        font-size: 14px;

        p,
        span {
            font-family: PingFang HK;
            font-size: 14px;
            color: #121633;
            text-align: center;
        }
    }

    .ant-table-row-expand-icon:focus,
    .ant-table-row-expand-icon:hover {
        color: #5478ee;
    }

    .ant-table-tbody>tr:hover:not(.ant-table-expanded-row)>td,
    .ant-table-tbody>tr.ant-table-row-selected td {
        background: #f0f4fe;
    }
}

.tooltip-text {
    font-family: PingFang-SC-Regular;
    font-size: 12px;
    color: #666666;
}

.ques-drawer {
    .ant-drawer-title {
        font-weight: bold;
    }
}
</style>

<style lang="less" scoped>
.paper-select {
    padding: 0 10px 10px;

    .top-bar {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .search-wrapper {
            --antd-wave-shadow-color: #fff !important;
            width: 240px;
            line-height: 32px;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            margin-right: 16px;

            :deep(.ant-input) {
                font-size: 13px;
                box-shadow: none;
                border: none;
                border-radius: 8px;
                line-height: 26px;
            }

            :deep(.ant-input-group-addon) {
                border-radius: 8px;
            }

            :deep(.ant-input-search-button) {
                border-radius: 8px !important;
                box-shadow: none !important;
                border: none !important;
            }
        }
        .time-range-wrapper {
            border-radius: 8px;
            :deep(input) {
                font-size: 13px;
                border-radius: 8px;
                line-height: 26px;
            }
        }
    }
}

.btn-group {
    display: flex;
    align-items: center;
    justify-content: center;

    .ant-btn {
        border-radius: 8px;

        &:first-child {
            margin-right: 8px;
        }
    }
}
</style>