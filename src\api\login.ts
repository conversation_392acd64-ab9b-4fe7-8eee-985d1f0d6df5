import request from '@/utils/http'

export function userLogin(data?: object) {
  return request({
    url: '/admin/login',
    data
  })
}

export function studentRegister(data?: object) {
  return request({
    url: '/register',
    data
  })
}

export function teacherGroup(data?: object) {
  return request({
    url: '/group',
    data
  })
}

export function studentLogin(data?: object) {
  return request({
    url: '/teacher/login',
    data
  })
}

// 注册信息核实
export function registerCheck(data?: object) {
  return request({
    url: '/register_check',
    data
  })
}

// 注册验证码
export function varifyCode(data?: object) {
  return request({
    url: '/captcha/gen',
    // data
  })
}

//注册码验证
export function valideCode(data?: object) {
  return request({
    url: '/captcha/verify',
    data
  })
}

// 邀请码登录
export function codelogin(data?: object) {
  return request({
    url: '/tokenlogin',
    data
  })
}

// 邀请码密码登录
export function codepwdlogin(data?: object) {
  return request({
    url: '/pwdlogin',
    data
  })
}

// 获取登录微信二维码
export function loginwxcode(data?: object): Promise<{ qrcode: string; sceneid: string }> {
  return request({
    url: '/createwxqrcode',
    data
  })
}

// 轮询扫码登录状态
export function checklogin(data?: object) {
  return request({
    url: '/checklogin',
    data
  })
}

export function checkwxlogin(data?: { sceneid: string }) {
  return request({
    url: '/checkwxlogin',
    data
  })
}

// 登录完善信息
export function regandbind(data?: object) {
  return request({
    url: '/regandbind',
    data
  })
}
export function smslogincode(data?: { phone: string }) {
  return request({
    url: '/teacher/sendSMS',
    data
  })
}

export function smscodelogin(data?: { phone: string, code: string }) {
  return request({
    url: '/teacher/smsLogin',
    data
  })
}
