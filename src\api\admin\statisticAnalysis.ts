import request from '@/utils/http'

// 获取所有图表
export function allgraph(data?: object) {
  return request({
    url: '/allgraph',
    data
  })
}

// 题型占比
export function questypeproportion(url: string) {
  return request({
    url
  })
}

// 总题目数
export function totalques(url: string) {
  return request({
    url
  })
}

// 考生分析
// 获取所有图表
export function studentreport(data?: object) {
  return request({
    url: '/analysis/studentreport',
    data
  })
}

// 题库分析汇总
export function analysisQuestion(data?: { catg: string }) {
  return request({
    url: '/analysis/question',
    data
  })
}

// 某题库下的题型分析
export function quescatgtypeproportion(data?: { categoryId: string }) {
  return request({
    url: '/analysis/analysisQuestionCount',
    data
  })
}

// 个性化设置
export function personalconfig(data?: object) {
  return request({
    url: '/personalconfig',
    data
  })
}

// 性别分布
export function sexproportion(data?: object) {
  return request({
    url: '/analysis/studentSex',
    data: {}
  })
}

// 考生列表
export function studentlist(data?: object) {
  return request({
    url: '/analysis/studentPage',
    data
  })
}

// 年龄分布
export function ageproportion(data?: object) {
  return request({
    url: '/analysis/studentAge',
    data: {}
  })
}

// 学历分布
export function eduproportion(data?: object) {
  return request({
    url: '/analysis/studentEdu',
    data: {}
  })
}

// 考试分析
// 未考试数
export function getWaitingPaper() {
  return request({
    url: '/analysis/analysisExamWait',
    data: {}
  })
}
// 已考试数
export function getDonePager() {
  return request({
    url: '/analysis/analysisExamComplete',
    data: {}
  })
}
// 考试总数
export function getTotalPaper() {
  return request({
    url: '/analysis/analysisExamSum',
    data: {}
  })
}
// 考试列表
export function getPagerList(data: object) {
  return request({
    url: '/analysis/examPage',
    data,
  })
}
// 考试详情
export function paperreport(data?: object) {
  return request({
    url: '/papertemplate/analysis',
    data
  })
}

// 考试分析
export function aipaperanalyze(data?: any) {
  return request({
    url: '/ai/aipaperanalyze',
    data
  })
}

// 考试分数段分析
export function aipaperanalyzenew(data?: any) {
  return request({
    url: '/ai/aipaperanalyzenew',
    data
  })
}

// 某题库下的题量分布
export function quescatgproportion(data?: { categoryId: string }) {
  return request({
    url: '/analysis/analysisChildrenCount',
    data
  })
}

// 某题库下的题题库考试频次与题量
export function quescatgrefproportion(data?: { categoryId: string }) {
  return request({
    url: '/analysis/analysisChildrenRefCount',
    data
  })
}
