<template>
  <div class="paper-card">
    <div class="header">
      <div class="left">
        <a-tooltip>
          <template #title>{{ paperInfo.name }}</template>
        <SearchHighLight class="title" :text="paperInfo.name" :search-text="searchText"></SearchHighLight>
        </a-tooltip>
        <div class="status" :style="statusStyles[paperInfo.exam_status]">{{ PaperStatusList.find(el => el.value === paperInfo.exam_status)?.label || paperInfo.exam_status }}</div>
      </div>
    </div>
    <div class="time">{{ dayjs(paperInfo.startTime).format('YYYY-MM-DD HH:mm') }} 至 {{ dayjs(paperInfo.endTime).format('YYYY-MM-DD HH:mm') }}</div>
    <div class="subinfo-wrap">
      <div class="duration-wrap">
        <img class="icon" src="@/assets/images/time.png" alt="">
        <div class="duration">{{ paperInfo.duration }}分钟</div>
      </div>
      <div class="creater-wrap">
        <img class="icon" src="@/assets/images/account.png" alt="">
        <SearchHighLight class="creater" :text="paperInfo.author" :search-text="searchText"></SearchHighLight>
      </div>
    </div>
    <div :class="['status-wrap', props.paperInfo.status || manualOver || beforeManual ? '' : 'active']">
      <template v-if="props.paperInfo.status || manualOver">
        <div class="text">阅卷已结束</div>
        <img class="manual-img" src="@/assets/images/paper/manual-end.png" alt="">
      </template>
      <template v-else-if="beforeManual">
        <div class="text">阅卷未开始</div>
        <img class="manual-img" src="@/assets/images/paper/manual-start.png" alt="">
      </template>
      <template v-else>
        <div class="number-wrap">
          <div class="label">待阅卷题次</div>
          <div class="number">{{ paperInfo.needCorrectNum }}</div>
        </div>
        <img class="manual-img" src="@/assets/images/paper/manual-active.png" alt="">
      </template>
    </div>
    <div class="bottom">
      <div
        v-if="!props.paperInfo.status && showManual"
        :class="{ 'manual-btn': true, 'disabled': !canManual }"
        @click="handleManual">
        阅卷
      </div>
      <div :class="{'manual-btn': true, 'disabled': !props.paperInfo.modifynum }" v-else @click="handleShowHistory">阅卷记录</div>
      <div class="manual-btn" v-if="props.paperInfo.is_self" @click="handleSetManual">联合阅卷</div>
    </div>
    <div class="type" v-if="paperInfo.uniexam === PaperType.NOT_UEC">非统考</div>
  </div>
  <!-- 联合阅卷 -->
  <JoinMarkPaper
    :id="props.paperInfo.id"
    :teacher="props.paperInfo.teacher"
    :papername="props.paperInfo.name"
    :joinMarkModalVisible="joinMarkModalVisible"
    :readonly="props.paperInfo.status || manualOver"
    @closeModal="joinMarkModalVisible = false" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import SearchHighLight from '@/components/SearchHighLight.vue';
import {
  PaperStatus,
  PaperStatusList,
  PaperType,
} from '@/models/paperModel'
import JoinMarkPaper from '@/views/PaperManage/components/JoinMarkPaper.vue'

const router = useRouter()
const props = defineProps<{
  paperInfo: any // 考试信息
  searchText: string
}>()

const statusStyles = { // 考试状态样式
  [PaperStatus.NOT_START]: {
    backgroundColor: '#f3f7f1',
    color: '#2F8C00',
  },
  [PaperStatus.DURING_EXAMS]: {
    backgroundColor: '#fcf0f0',
    color: '#D71310',
  },
  [PaperStatus.OVER]: {
    backgroundColor: '#f5f5f5',
    color: '#626262',
  },
}

const beforeManual = computed(() => { // 阅卷未开始
  // 考试未开始
  if (props.paperInfo.exam_status === PaperStatus.NOT_START) return true
  // 统考，考试中
  if (props.paperInfo.exam_status === PaperStatus.DURING_EXAMS && props.paperInfo.uniexam === PaperType.IS_UEC) return true
  return false
})

const showManual = computed(() => { // 是否展示阅卷
  // 考试未开始，展示阅卷按钮, 置灰
  if (props.paperInfo.exam_status === PaperStatus.NOT_START) return true
  // 统考，考试中，展示阅卷按钮, 置灰
  if (props.paperInfo.exam_status === PaperStatus.DURING_EXAMS && props.paperInfo.uniexam == PaperType.IS_UEC) return true
  // 非统考，考试中，待阅卷数大于0，展示阅卷按钮
  if (props.paperInfo.exam_status === PaperStatus.DURING_EXAMS && props.paperInfo.uniexam == PaperType.NOT_UEC && props.paperInfo.needCorrectNum > 0) return true
  // 考试已结束，待阅卷数大于0，展示阅卷按钮
  if (props.paperInfo.exam_status === PaperStatus.OVER && props.paperInfo.needCorrectNum > 0) return true
  return false
})
const canManual = computed(() => { // 是否可以阅卷
  // 考试未开始，展示阅卷按钮, 置灰
  if (props.paperInfo.exam_status === PaperStatus.NOT_START) return false
  // 统考，考试中，展示阅卷按钮, 置灰
  if (props.paperInfo.exam_status === PaperStatus.DURING_EXAMS && props.paperInfo.uniexam === PaperType.IS_UEC) return false
  return true
})

// 阅卷已结束 （考试已结束&待阅卷题次为0）
const manualOver = computed(() => props.paperInfo.exam_status === PaperStatus.OVER && props.paperInfo.needCorrectNum <= 0)

const handleManual = async () => { // 阅卷
  if (props.paperInfo.exam_status === PaperStatus.NOT_START) {
    message.warning('考试未开始，不可阅卷！')
    return
  }
  if (props.paperInfo.exam_status === PaperStatus.DURING_EXAMS && props.paperInfo.uniexam === PaperType.IS_UEC) {
    message.warning('考试中的统考试卷，不可阅卷！')
    return
  }
  router.push({
    name: 'correctPaper',
    query: {
      id: props.paperInfo.id,
      name: props.paperInfo.name,
    },
  })
}

const handleShowHistory = () => { // 阅卷记录
  if (!props.paperInfo.modifynum) {
    message.warning('当前试卷您未参与阅卷')
    return
  }
  router.push({
    name: 'correctPaper',
    query: {
      id: props.paperInfo.id,
      name: props.paperInfo.name,
      published: props.paperInfo.published,
    },
  })
  // router.push({
  //   name: 'historyRecord',
  //   query: {
  //     id: props.paperInfo.id,
  //     name: props.paperInfo.name,
  //     published: props.paperInfo.published,
  //   },
  // })
}

const joinMarkModalVisible = ref(false)
const handleSetManual = () => { // 设置联合阅卷
  joinMarkModalVisible.value = true
}
</script>

<style lang="less" scoped>
.paper-card {
  width: 100%;
  padding: 24px 24px 0 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px #0000001a;
  transition: all .3s ease;
  &:hover {
    box-shadow: 0 6px 18px #0003;
  }
  box-sizing: border-box;
  position: relative;
  user-select: none;
  .header {
    height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      min-width: 0;
      flex: auto;
      display: flex;
      align-items: center;
      .title {
        min-width: 0;
        height: 22px;
        font-size: 18px;
        font-weight: 600;
        color: rgba(0,0,0,0.85);
        text-align: left;
        line-height: 22px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .status {
        flex: none;
        width: 44px;
        height: 18px;
        margin-left: 16px;
        background: #fcf0f0;
        color: #d71310;
        border-radius: 4px;
        font-size: 12px;
        font-weight: Regular;
        text-align: center;
        line-height: 18px;
      }
      .operation {
        height: 16px;
        margin-left: 16px;
        align-items: center;
        display: none;
        .btn {
          flex: none;
          width: 16px;
          height: 16px;
          margin-left: 16px;
          object-fit: contain;
          cursor: pointer;
          &:first-child {
            margin-left: 0;
          }
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .time {
    height: 18px;
    margin-top: 16px;
    font-size: 12px;
    color: #181818;
    line-height: 18px;
  }
  .subinfo-wrap {
    height: 18px;
    margin-top: 8px;
    display: flex;
    .duration-wrap {
      display: flex;
      align-items: center;
      padding-right: 8px;
      position: relative;
      .icon {
        flex: none;
        width: 14px;
        height: 14px;
        margin-right: 8px;
      }
      .duration {
        height: 100%;
        font-size: 12px;
        color: #181818;
        line-height: 18px;
        position: relative;
      }
      &::after {
        display: block;
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        right: 0;
        width: 1px;
        height: 12px;
        background-color: #DFE1E6;
      }
    }
    .creater-wrap {
      margin-left: 8px;
      display: flex;
      align-items: center;
      .icon {
        flex: none;
        width: 14px;
        height: 14px;
        margin-right: 8px;
      }
      .creater {
        font-size: 12px;
        font-size: 12px;
        color: #181818;
        line-height: 18px;
      }
    }
  }
  .status-wrap {
    height: 90px;
    margin-top: 16px;
    padding: 16px 24px;
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &.active {
      background: #F1F4FE;
    }
    .text {
      height: 36px;
      font-size: 18px;
      font-weight: 600;
      color: rgba(0,0,0,0.30);
      line-height: 36px;
    }
    .number-wrap {
      height: 58px;
      .label {
        height: 18px;
        font-size: 12px;
        color: rgba(0,0,0,0.65);
        line-height: 18px;
      }
      .number {
        height: 40px;
        font-size: 36px;
        font-weight: 600;
        color: #5478ee;
        line-height: 40px;
      }
    }
    .manual-img {
      flex: none;
      width: 58px;
      height: 58px;
    }
  }
  .bottom {
    height: 42px;
    margin-top: 16px;
    border-top: 1px solid #E8E8E8;
    display: flex;
    justify-content: center;
    .manual-btn {
      flex: auto;
      width: 50%;
      height: 100%;
      cursor: pointer;
      text-align: center;
      line-height: 42px;
      color: #5478EE;
      position: relative;
      &:hover {
        color: rgba(140,167,255,1);
      }
      &.disabled {
        color: rgba(0,0,0,0.25);
        cursor: not-allowed;
      }
      &::before {
        display: block;
        content: '';
        width: 1px;
        height: 14px;
        background: #dfe1e6;
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 0;
      }
      &:first-of-type::before {
        display: none;
      }
    }
  }
  .type {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 20px;
    background: linear-gradient(90deg,#e8eeff, #cdd9ff);
    border-radius: 0px 8px 0px 8px;
    font-size: 12px;
    text-align: center;
    color: #5478ee;
    line-height: 20px;
  }
  &:hover {
    box-shadow: 0px 6px 18px 0px rgba(0,0,0,0.10);
  }
}
</style>
