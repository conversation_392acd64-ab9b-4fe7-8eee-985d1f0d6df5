<script setup lang="ts">
import type { ImprovementProps } from './shared'

defineProps<{
  improvementData: ImprovementProps
}>()
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 h-full">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        待提升项
      </h3>
    </div>
    <div v-if="improvementData?.length > 0" class="space-y-4">
      <div v-for="(item, index) in improvementData" :key="index" class="flex items-start">
        <div class="w-5 h-5 flex justify-center items-center flex-shrink-0">
          <svg width="16px" height="16px" viewBox="0 0 16 16">
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g transform="translate(-1056.000000, -315.000000)">
                <g transform="translate(1032.000000, 232.000000)">
                  <g transform="translate(24.000000, 80.000000)">
                    <g transform="translate(0.000000, 3.000000)">
                      <rect x="0" y="0" width="16" height="16" />
                      <rect x="0" y="0" width="16" height="16" />
                      <rect fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="16" height="16" />
                      <path
                        id="形状"
                        d="M8,1 C4.134375,1 1,4.134375 1,8 C1,11.865625 4.134375,15 8,15 C11.865625,15 15,11.865625 15,8 C15,4.134375 11.865625,1 8,1 Z M7.5,4.625 C7.5,4.55625 7.55625,4.5 7.625,4.5 L8.375,4.5 C8.44375,4.5 8.5,4.55625 8.5,4.625 L8.5,8.875 C8.5,8.94375 8.44375,9 8.375,9 L7.625,9 C7.55625,9 7.5,8.94375 7.5,8.875 L7.5,4.625 Z M8,11.5 C7.5859375,11.5 7.25,11.1640625 7.25,10.75 C7.25,10.3359375 7.5859375,10 8,10 C8.4140625,10 8.75,10.3359375 8.75,10.75 C8.75,11.1640625 8.4140625,11.5 8,11.5 Z" fill="#FA9841"
                      />
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>

        <div class="ml-2 text-sm" v-html="item.description" />
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-start h-full">
        <div class="text-gray-500">
          暂无待提升项
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.sf-2 {
  position: relative;
  z-index: 1;
  font-weight: 500;
}

.sf-2::after {
  z-index: -1;
  content: "";
  position: absolute;
  left: 0px;
  bottom: -2px;
  width: 100%;
  height: 70%;
  background: linear-gradient(133deg, #d6edff 0%, #d9e2ff 53%, #d4d3ff 98%);
  border-radius: 4px;
  transform: skew(-20deg);
}
</style>