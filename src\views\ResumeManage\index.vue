<template>
  <div class="resume-manage-container">
    <h3 class="resume-manage-title">人才库</h3>
    <div class="resume-manage">
      <div class="resume-manage-header">
        <div class="header-left flex items-center">
          <a-input-search v-model:value.trim="inputSearchName" placeholder="请输入姓名/岗位" allow-clear
            @search="getSearchData" style="flex: 1" @clear='getSearchData' />
            <div v-show="handlingTotal">
              <span class="i-eos-icons-bubble-loading"></span>  
              {{handlingTotal}} 份简历处理中  <el-link type="primary" class="mt-[-4px] ml-[3px]" @click="viewDetail">查看详情</el-link>
            </div>
        </div>
        <div class="header-right">
          <a-dropdown v-model:open="columnsVis">
            <div type="text" class="flex items-center justify-center rounded-[8px] flex-nowrap cursor-pointer hover:bg-[#f5f5f5] w-[104px] h-[35px]">
              <svg class="rotate mr-[9px]" xmlns="http://www.w3.org/2000/svg" width="20" viewBox="0 0 24 24"><!-- Icon from MingCute Icon by MingCute Design - https://github.com/Richard9394/MingCute/blob/main/LICENSE --><g fill="none" fill-rule="evenodd"><path d="m12.594 23.258l-.012.002l-.071.035l-.02.004l-.014-.004l-.071-.036q-.016-.004-.024.006l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.016-.018m.264-.113l-.014.002l-.184.093l-.01.01l-.003.011l.018.43l.005.012l.008.008l.201.092q.019.005.029-.008l.004-.014l-.034-.614q-.005-.019-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.003-.011l.018-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M16 15c1.306 0 2.418.835 2.83 2H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 1 1 0-2h9.17A3 3 0 0 1 16 15m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2M8 9a3 3 0 0 1 2.762 1.828l.067.172H20a1 1 0 0 1 .117 1.993L20 13h-9.17a3.001 3.001 0 0 1-5.592.172L5.17 13H4a1 1 0 0 1-.117-1.993L4 11h1.17A3 3 0 0 1 8 9m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2m8-8c1.306 0 2.418.835 2.83 2H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 0 1 0-2h9.17A3 3 0 0 1 16 3m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2"/></g></svg>
              <div style="line-height: 20px;">表头配置</div>
            </div>
            <template #overlay>
              <a-menu class="py-[20px]">
                <a-menu-item v-for="item in data.columns.filter(item => item.key !='action')" class="w-[160px]" :disabled="item.key === 'candidateName'">
                  <div class="flex justify-between" :class="item.key === 'candidateName' ? 'text-[rgba(0,0,0,0.25)]' :''" @click="item.show = !item.show">
                    <a href="javascript:;">{{item.title}}</a>
                    <EyeOutlined v-show="item.show"/>
                    <EyeInvisibleOutlined v-show="!item.show"/>
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button @click="CreateCandidateDialog" type="primary">
            添加候选人
          </a-button>
          <a-button @click="handleSelectResume">
            批量筛选
          </a-button>
          <a-button @click="deleteBatch">
            批量删除
          </a-button>
        </div>
      </div>

      <resume-table
        :data="data"
        :preview-resume-id="previewResumeId"
        :pagination="paginationConfig"
        ref="resumeTableRef"
        @getResumes="getResume"
        @handleTableChange="handleTableChange"
        @updateSelectedResume="updateSelectedResume"
        @previewResume="previewResume"
        @selectChange="tableSelectChange"
        @selectionsOfAll="selectionsOfAll"
      />

      <resume-detail
        v-if="resumeDetailVisible"
        :resume-detail-visible="resumeDetailVisible"
        :resume-detail-id="previewResumeId"
        @closePreviewResume="closePreviewResume"
      />
    </div>

    <!-- 编辑简历模态框 -->
    <ResumeSelectModal
      v-model:visible="modalvisible"
      @open="handleChangeResume"
      @close="handleCloseModal"
      :resume-list="tableSelectionData"
      :job-title="tableSelectionData[0]?.position"
    />

    <CreateCandidate v-model="addCandidateVisible" :viewType="viewType" @update:file-list="getTotalCount"/>
  </div>
</template>
<script setup lang="ts">
import { useUrlSearchParams } from '@vueuse/core'
import { reactive, onMounted, onBeforeMount,onUnmounted, createVNode, ref, watch } from 'vue'
import { useRouter, useRoute,onBeforeRouteLeave } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import { ElMessage,ElMessageBox } from 'element-plus'

import { queryResumeListApi, getstudept, exportstu, deleteResume } from '@/api/admin/resumeManage'
import { teacherGroup } from '@/api/login'

import ResumeTable from './components/ResumeTable.vue'
import ResumeDetail from './resumeDetail.vue'
import ResumeSelectModal from './components/ResumeMultipleSelect/ResumeSelectModal.vue'
import CreateCandidate from './components/createCandidate/index.vue'
import { getResumeTaskList, getResumeTaskCount, deleteCandidate, getPostList } from '@/pages/questionManage/hooks/api'
import { url } from 'inspector'
const router = useRouter()
onBeforeRouteLeave((to, from) => {
  if(modalvisible.value) {
    modalvisible.value = false
    return false
  }
  if(to.name === "questionManage"){
    localStorage.removeItem('PARAMS')
  }
})
const resumeTableRef = useTemplateRef<any>('resumeTableRef')
// 添加候选人弹框相关
const addCandidateVisible = ref(false)
const viewType = ref('')
const inputSearchName = ref('')

const viewDetail = () => {
  viewType.value = 'readOnly'
  addCandidateVisible.value = true
}
const CreateCandidateDialog = () => {
  viewType.value = ''
  addCandidateVisible.value = true
}

const handlingTotal = ref(0)
const getTotalCount = async () => {
  const res = await getResumeTaskCount({
      "positionId": "",
      "statusList": ["PENDING","PROCESS"]
  })
  handlingTotal.value = res.count
}

getTotalCount()


// 简历配置数据
const data = reactive({
  columns: [
    {
      title: '姓名',
      dataIndex: 'candidateName',
      key: 'candidateName',
      width: 180,
      align: 'left',
      ellipsis: true,
      resizable: true,
      show:true
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 100,
      align: 'left',
      ellipsis: true,
      resizable: true,
      filters: [
        { text: '男', value: "男" },
        { text: '女', value: '女' },
        { text: '未知', value: '未知' },
      ],
      filterMultiple: false,
      show:false
    },
    {
      title: '年龄',
      width: 80,
      dataIndex: 'age',
      key: 'age',
      align: 'left',
      ellipsis: true,
      resizable: true,
      sorter: true,
      defaultSortOrder: "",
      show:true
    },
    {
      title: '电话',
      width: 120,
      dataIndex: 'phone',
      key: 'phone',
      align: 'left',
      ellipsis: true,
      resizable: true,
      show:true
    },
    {
      title: '电子邮箱',
      width: 200,
      dataIndex: 'email',
      key: 'email',
      align: 'left',
      ellipsis: true,
      resizable: true,
      show:true
    },
    {
      title: '岗位',
      width: 200,
      dataIndex: 'position',
      key: 'position',
      align: 'left',
      ellipsis: true,
      resizable: true,
      filters: [],
      defaultFilteredValue: [],
      show:true
    },
    {
      title: '岗位匹配度',
      width: 130,
      align: 'left',
      dataIndex: 'matchScore',
      key: 'matchScore',
      ellipsis: true,
      resizable: true,
      sorter: true,
      show:true,
      defaultSortOrder: "",
    },  
    {
      title: '简历筛选',
      width: 120,
      dataIndex: 'resumeStatus',
      align: 'left',
      key: 'resumeStatus',
      ellipsis: true,
      resizable: true,
      filters: [
        { text: '未筛选', value: '未筛选', },
        { text: '合适', value: '合适' },
        { text: '不合适', value: '不合适' },
        { text: '待定', value: '待定' },
      ],
      defaultFilteredValue: [],
      show:true
    },
    {
      title: '面试状态',
      width: 110,
      dataIndex: 'interviewStatus',
      align: 'left',
      key: 'interviewStatus',
      ellipsis: true,
      resizable: true,
      // filters: [
      //   { text: '未安排', value: '未安排' },
      //   { text: '待面试', value: '待面试' },
      //   { text: '面试中', value: '面试中' },
      //   { text: '已结束', value: '已结束' },
      // ],
      show:true
    },
    {
      title: '上传时间',
      width: 200,
      dataIndex: 'createTime',
      align: 'left',
      key: 'createTime',
      ellipsis: true,
      resizable: true,
      show:true,
      sorter: true,
      defaultSortOrder: "",
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'center',
      key: 'action',
      width: 240,
      show:true
    }
  ],
  total: 0,
  resumeLoading: false,
  resumeList: <any>[],
  delResumesList: <any>[],
  groups: <any>[],
  visible: false
})
const columnsVis = ref(false)
// 获取报考部门列表
// let deptList = ref<{ text: string; value: string }[]>([])
// async function getStudentDept() {
//   deptList.value = await getstudept() as any
// }
// getStudentDept().then(() => {
//   data.columns.find(item => item.title === '报考部门')!.filters = deptList.value
// })

const params = reactive({
  condition: '',
  register_time_range: []
})

const otherParams = ref<{
  order_field?: string
  order_type?: 'ascend' | 'descend'
}>({})


const getSearchData = () => {
  if(inputSearchName.value){
    paginationConfig.value.current = 1
    getResume({
      content: inputSearchName.value,
    })
  }else{
    paginationConfig.value.current = 1
    getResume({})
  }
  PARAMS.content = inputSearchName.value
  localStorage.setItem('PARAMS', JSON.stringify(PARAMS))
}

const postList = ref([])
function getPostListExec() {
  getPostList({
    pageNo: 1,
    pageSize: 10,
  }).then((res: any) => {
    postList.value = res.records
    // 设置data.colums 岗位筛选参数
    data.columns.find(item => item.title === '岗位')!.filters = res.records.map((item:any) => {
      return { text: item.title, value: item.id }
    })
  })
}
getPostListExec()
const allSelected = ref(false)

const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  showTotal: (total: number) => '总条数：' + total,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})


async function getResume(receiveParams?:any) {
  data.resumeLoading = true
  const obj = {
    "positionId": "1926875031301865473",
    "pageNo": paginationConfig.value.current,
    "pageSize": paginationConfig.value.pageSize,
    "onlyCompleted": true,
    "content": inputSearchName.value ? inputSearchName.value : '',
  }
  const params = Object.assign(obj, receiveParams)

  try {
    let res = await queryResumeListApi(params) as any
    data.resumeList = res.records
    data.total = res.total
    paginationConfig.value.total = data.total
  } finally {
    data.resumeLoading = false
  }
}

const selectedBody = ref(0)
  
const PARAMS = {} as any
const handleTableChange = (filterState:any) => {
  const { pagination, filters, sort, info } = filterState
  // console.log('sort',sort)
  const action = info.action
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize

  if(action === 'filter' || action === 'sort') {
    paginationConfig.value.current = 1
  }
  const params = {} as any
  const {} = sort
  if(filters?.gender) {
    params['gender'] = filters.gender[0]
  }
  if(filters?.position) {
    params['positionIds'] = filters.position
  }
  if(filters?.resumeStatus) {
    params['resumeStatuses'] = filters.resumeStatus
  }
  if(filters?.interviewStatus) {
    params['interviewStatuses'] = filters.interviewStatus
  }

  if(sort) {
    params.sortField = sort.columnKey
    params.sortOrder = sort.order === 'ascend' ? 'asc' : 'desc'
  }

  Object.keys(params).forEach(key => {
    PARAMS[key] = params[key]
  })
  PARAMS.current = paginationConfig.value.current
  PARAMS.pageSize = paginationConfig.value.pageSize
  localStorage.setItem('PARAMS', JSON.stringify(PARAMS))
  getResume(params)
}

const updateSelectedResume = (val: any) => {
  data.delResumesList = val
  selectedBody.value = allSelected.value ? data.total : data.delResumesList.length
}

const tableSelection = ref<any>(null)

const tableSelectChange = (val: any) => {
  tableSelection.value = val
}
// const tableSelectionData = computed(() => {
//   return data.resumeList.filter((item:any) => data.delResumesList.includes(item.resumeId))
// })
const tableSelectionData = ref([])
const selectionsOfAll = (val: any) => {
  tableSelectionData.value = val
}


const deleteBatch = () => {
  if(!tableSelectionData.value || !tableSelectionData.value.length) {
    message.error('请勾选需要删除的候选人')
    return
  }
  ElMessageBox.confirm(
    `确认删除勾选的${tableSelectionData.value.length}位候选人吗？可能会影响未参加面试的候选人，且正在面试的候选人无法删除。`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
    const params = {
      resumeIds: tableSelectionData.value.map((item:any) => item.resumeId)
    }
    deleteCandidate(params).then((res:any) => {
      message.success(res.message || '删除成功！')
      tableSelection.value = []
      tableSelectionData.value =  [...tableSelectionData.value.filter((item:any) => !params.resumeIds.includes(item.resumeId))]
      resumeTableRef.value!.remove(params.resumeIds)
      getResume()
    })
      })
      .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    })

}

const modalvisible = ref(false)
const resumeDetailVisible = ref(false)
const previewResumeId = ref()

function handleChangeResume() {
  // alert('open')
  // 只刷新列表
}

function handleCloseModal() {
  // alert('close')
  // 关闭弹窗 刷新列表
  // getResume()
  revertParams()
}

function handleSelectResume() {
  const isSamePost = tableSelectionData.value.every((item:any) => item.position === tableSelectionData.value[0].position)
  if (!isSamePost) {
    message.error('请选择同岗位的候选人')
    return
  }
  if (tableSelectionData.value.length === 0) {
    message.error('请至少选择1位候选人')
    return
  }
  if (tableSelectionData.value.length > 100) {
    message.error('一次最多选择100份简历')
    return
  }
  modalvisible.value = true
}

const previewResume = (id: string) => {
  router.push({ name: 'resumeDetail', query: { id } })
}

const closePreviewResume = () => {
  resumeDetailVisible.value = false
}

const exportExcel = () => {
  if (!data.delResumesList.length) {
    message.error('请勾选需要导出的学生')
    return
  }

  exportstu({ students: data.delResumesList }).then((res: any) => {
    window.open(res)
    data.delResumesList.length = 0
  })
}

watch(() => allSelected.value, (val: boolean) => {
  selectedBody.value = val ? data.total : data.delResumesList.length
})

const revertParams = () => {
  const PARAMS= JSON.parse(localStorage.getItem('PARAMS') || '{}')

  if(Object.keys(PARAMS).length) {
    paginationConfig.value.current = PARAMS.current
    paginationConfig.value.pageSize = PARAMS.pageSize
    data.columns.forEach((item:any) => {
      if(item.dataIndex === 'position'){
        item.defaultFilteredValue = PARAMS.positionIds || []
      }else if(item.dataIndex === 'resumeStatus'){
        item.defaultFilteredValue = PARAMS.resumeStatuses || []
      }

      if(item.dataIndex === PARAMS.sortField){
        item.defaultSortOrder = PARAMS.sortOrder === 'asc' ? 'ascend' : 'descend'
      }
    })
    inputSearchName.value = PARAMS.content
    data.columns = [...data.columns]
    getResume(PARAMS)
  }else{
    getResume()
  }
}
onBeforeMount(() => {
  // getResume()
  revertParams()
})

</script>
<style lang="less" scoped>
.rotate {
  transform: rotate(-90deg);
}

.resume-manage-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px 20px 20px;
}

.resume-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}

.resume-manage {
  flex: 1;
  min-height: 0;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 10px 24px;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .resume-manage-header {
    margin: 8px 0;
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      gap: 16px;
    }

    .header-right {
      display: flex;
      align-items: center;
      font-size: 14px;
      gap: 16px;

      span {
        width: 88px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 8px;
        font-size: 14px;
        margin-left: 8px;
        cursor: pointer;
      }

      .add-btn {
        background: #5478ee;
        color: #fff;
      }

      .import-btn,
      .del-btn {
        // border: 1px solid rgba(0, 0, 0, 0.15);
        background: #5478ee;
        color: #fff;
      }
    }
  }

  .table-bar {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ant-checkbox-wrapper {
      font-size: 15px;
    }

    .show-columns {
      font-size: 15px;

      span {
        margin-right: 10px;
      }
    }
  }

  .resumes {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
  }

  .resume-card {
    width: 360px;
    margin-bottom: 20px;
    margin-right: 20px;
    overflow: hidden;
  }

  .resume-content {
    display: flex;

    .avatar-img {
      width: 1rem;
      height: 1rem;
    }

    .resume-info {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;

      span {
        line-height: 1.5;
      }
    }
  }

  .dynamic-column {
    height: 60px;
    line-height: 62px;
    padding-left: 15px;
  }
}
</style>
