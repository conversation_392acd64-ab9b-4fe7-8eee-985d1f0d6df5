<script setup>
import { onMounted } from 'vue'

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  dialogVisible: {
    type: Boolean,
    default: false,
  },
})
const videoUrl = ref('')
onMounted(async () => {
  const video = document.getElementById('video')
  watch(() => props.dialogVisible, (val) => {
    if (val) { 
      video.pause()
    }
  })
  videoUrl.value = props.url
  const videoSrc = props.url

  // if (Hls.isSupported()) {
  //   const hls = new Hls()
  //   await hls.loadSource(videoSrc)
  //   await hls.attachMedia(video)
  //   video.play()
  //   video.addEventListener('loadedmetadata', () => {
  //     let frameCount = 0
  //     const timelineElement = document.getElementById('timeline')
  //     const mediaStream = video.captureStream()
  //     const videoTrack = mediaStream.getVideoTracks()[0]
  //     const frameThumbnails = []
  //     const frameInterval = 1 // 抽帧间隔(秒)
  //     // 设置MediaStreamTrackProcessor
  //     const trackProcessor = new MediaStreamTrackProcessor({
  //       track: videoTrack,
  //     })

  //     captureAndProcessFrames()

  //     async function captureAndProcessFrames() {
  //       const reader = trackProcessor.readable.getReader()

  //       while (true) {
  //         const { done, value: frame } = await reader.read()
     
  //         if (done) 
  //           break

  //         const currentTime = frame.timestamp / 1000000 // 将时间戳转换为秒

  //         if (frameCount === 0 || currentTime >= frameCount * frameInterval) {
  //           frameCount++
  //           createFrameThumbnail(frame, currentTime) // 创建缩略图
  //         }

  //         frame.close() // 释放帧资源
  //       }
  //     }

  //     // 创建帧缩略图
  //     function createFrameThumbnail(frame, time) {
  //       const canvas = document.createElement('canvas')
  //       canvas.width = 120
  //       canvas.height = 80
  //       const ctx = canvas.getContext('2d')

  //       // 将帧绘制到 Canvas 上
  //       ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)

  //       // 创建缩略图元素
  //       const thumbnail = document.createElement('div')
  //       thumbnail.className = 'frame-thumbnail'
  //       thumbnail.appendChild(canvas)

  //       // 添加时间标记
  //       const timeLabel = document.createElement('div')
  //       timeLabel.className = 'frame-time'
  //       timeLabel.textContent = (time)
  //       thumbnail.appendChild(timeLabel)

  //       // 点击事件，跳转到对应时间
  //       thumbnail.addEventListener('click', () => {
  //         video.currentTime = time
  //       })

  //       // 添加到时间轴
  //       timelineElement.appendChild(thumbnail)

  //       // 保存缩略图信息
  //       frameThumbnails.push({ time, element: thumbnail })
  //     }
  //   })
  // }
})
</script>

<template>
  <video id="video" :src="videoUrl" controls height="360" crossorigin="anonymous" class="bg-[#f7f7f7] rounded-[8px]" style="background-color: #d9d9d9;" />
  <!-- <div id="timeline-container">
    <div id="timeline" />
  </div> -->
</template>

<style lang="scss" scoped>
#video-container {
  max-width: 700px;
  overflow-x: auto;
  margin-bottom: 20px;
}

#video {
  width: 100%;
  background: #000;
}

#timeline-container {
  max-width: 800px;
  width: 100%;
  overflow-x: auto;
  margin-top: 10px;
  border: 1px solid #ddd;
  padding: 10px 0;
}

#timeline {
  display: flex;
  height: 100px;
}

.frame-thumbnail {
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  margin-right: 5px;
  border: 1px solid #ccc;
  position: relative;
  cursor: pointer;
}

.frame-thumbnail:hover {
  border-color: #0078d7;
}

.frame-time {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px;
  text-align: center;
}

.controls {
  margin: 10px 0;
  display: flex;
  gap: 10px;
}

input,
button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  background: #0078d7;
  color: white;
  border: none;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

#status {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

#current-time {
  font-weight: bold;
}
</style>