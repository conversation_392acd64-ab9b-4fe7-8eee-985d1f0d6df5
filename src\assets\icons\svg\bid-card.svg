<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="125px" viewBox="0 0 200 125" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 16</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="200" height="125"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="200" height="125" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-3" x="0" y="0" width="122" height="76" rx="4"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="122" height="76" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="考前页面" transform="translate(-523.000000, -1039.000000)">
            <g id="身份证" transform="translate(359.000000, 953.000000)">
                <g id="编组-16" transform="translate(164.000000, 86.000000)">
                    <g id="编组-15">
                        <use id="矩形" stroke="#ADB0B8" mask="url(#mask-2)" stroke-width="2" fill="#EEF0F5" stroke-dasharray="4" xlink:href="#path-1"></use>
                        <g id="证" transform="translate(39.000000, 16.000000)">
                            <use id="矩形" stroke="#ADB0B8" mask="url(#mask-4)" stroke-width="2" fill="#FFFFFF" stroke-dasharray="1" xlink:href="#path-3"></use>
                            <path d="M23,54 L95,54 L95,56 L23,56 L23,54 Z M23,59 L84,59 L84,61 L23,61 L23,59 Z" id="形状" fill="#E7E9EF" fill-rule="nonzero"></path>
                            <g id="编组-3" transform="translate(59.000000, 26.000000)" fill="#ADB0B8" fill-rule="nonzero">
                                <polygon id="路径备份-2" points="0 6 46 6 46 8 0 8"></polygon>
                                <polygon id="路径备份-3" points="0 12 36 12 36 14 0 14"></polygon>
                                <polygon id="路径备份-4" points="0 0 36 0 36 2 0 2"></polygon>
                            </g>
                            <path d="M61,14 L63,14 C64.1045695,14 65,14.8954305 65,16 L65,18 C65,19.1045695 64.1045695,20 63,20 L61,20 C59.8954305,20 59,19.1045695 59,18 L59,16 C59,14.8954305 59.8954305,14 61,14 Z" id="路径备份-5" fill="#ADB0B8" fill-rule="nonzero"></path>
                            <path d="M70,14 L72,14 C73.1045695,14 74,14.8954305 74,16 L74,18 C74,19.1045695 73.1045695,20 72,20 L70,20 C68.8954305,20 68,19.1045695 68,18 L68,16 C68,14.8954305 68.8954305,14 70,14 Z" id="路径备份-6" fill="#ADB0B8" fill-rule="nonzero"></path>
                            <path d="M79,14 L81,14 C82.1045695,14 83,14.8954305 83,16 L83,18 C83,19.1045695 82.1045695,20 81,20 L79,20 C77.8954305,20 77,19.1045695 77,18 L77,16 C77,14.8954305 77.8954305,14 79,14 Z" id="路径备份-7" fill="#ADB0B8" fill-rule="nonzero"></path>
                            <path d="M88,14 L90,14 C91.1045695,14 92,14.8954305 92,16 L92,18 C92,19.1045695 91.1045695,20 90,20 L88,20 C86.8954305,20 86,19.1045695 86,18 L86,16 C86,14.8954305 86.8954305,14 88,14 Z" id="路径备份-8" fill="#ADB0B8" fill-rule="nonzero"></path>
                            <g id="编组-2" transform="translate(22.000000, 13.000000)" fill-rule="nonzero">
                                <path d="M24,11.9995685 C24,18.6265492 18.6268049,24 12.001048,24 C5.37134588,24 0,18.6265492 0,11.9995685 C0,5.37123184 5.37134578,0 12.001048,0 C18.6268049,0.00073958756 24,5.37123194 24,11.9995685 L24,11.9995685 Z" id="路径" fill="#ADB0B8"></path>
                                <path d="M13.0229916,8.08851682 L14.1822457,7.20368189 L12.7475267,7.20368189 L12.3079928,5.77125451 L11.8638851,7.20368189 L10.4318219,7.20368189 L11.5900432,8.08851682 L11.1460829,9.52125451 L12.3079928,8.63548886 L13.4635583,9.52125451 L13.0229916,8.08851682 L13.0229916,8.08851682 Z M9.10818658,8.56522991 L8.88671651,8.02125451 L8.75499486,8.59499537 L8.18156765,8.64418395 L8.67376446,8.94663079 L8.54241325,9.52125451 L8.97708223,9.13720503 L9.47125428,9.43965187 L9.24879658,8.90059533 L9.68173716,8.51641969 L9.10818658,8.56522991 L9.10818658,8.56522991 Z M10.6787663,10.2712545 L10.2976722,10.7099873 L9.7533428,10.4933946 L10.0627044,10.9813137 L9.68173716,11.4207861 L10.2566098,11.2825957 L10.5652111,11.7712545 L10.6094419,11.198646 L11.1819067,11.0625514 L10.6378307,10.8453423 L10.6787663,10.2712545 L10.6787663,10.2712545 Z M14.1005008,10.4839521 L13.5591935,10.7065312 L13.1770867,10.2712545 L13.2232574,10.84413 L12.6820762,11.065968 L13.2559301,11.1975144 L13.3021008,11.7712545 L13.6082656,11.2796537 L14.1822457,11.4110767 L13.7981205,10.9758 L14.1005008,10.4839521 L14.1005008,10.4839521 Z M15.8617886,9.34320792 L15.7235242,8.77125451 L15.5058811,9.31546537 L14.9323305,9.27353477 L15.371814,9.65496368 L15.1540474,10.2015814 L15.6436518,9.8898256 L16.0807897,10.2712545 L15.9452411,9.69714759 L16.4325,9.38767206 L15.8617886,9.34320792 Z" id="形状" fill="#FFFFFF"></path>
                                <path d="M20.9330085,11.6882395 C20.9330085,6.76270325 16.9041433,2.77125451 11.9349204,2.77125451 C6.96353494,2.77125451 2.93097437,6.76270325 2.93097437,11.6882395 C2.93097437,14.1881172 3.96834657,16.4468214 5.64504038,18.0667836 L5.26985023,19.8556897 C5.26985023,20.6225167 5.89666025,21.5212545 6.66820202,21.5212545 L17.1963869,21.5212545 C17.970709,21.5212545 18.5967467,20.6225167 18.5967468,19.8556897 L18.1940623,18.095094 C19.9480695,16.417066 20.9369471,14.1039057 20.9330085,11.6882395 Z M11.9332898,17.7712545 C10.2620259,17.773894 8.65231238,17.2375417 7.4314829,16.2712545 L16.4325,16.2712545 C15.2118252,17.2363517 13.6034831,17.7725563 11.9332898,17.7712545 L11.9332898,17.7712545 Z M17.2300564,15.5212545 L17.0584458,14.7807237 L14.5217613,14.7807237 L14.5217613,14.2114539 L14.7538028,14.2114539 L14.4099568,13.558653 L14.1006203,13.558653 L13.9396278,12.9862339 L9.92137616,12.9862339 L9.76381909,13.558653 L9.45073499,13.558653 L9.11297887,14.2114539 L9.34189723,14.2114539 L9.34189723,14.7807237 L6.80568134,14.7807237 L6.63360213,15.5212545 C5.69103698,14.3787713 5.17911109,12.9634063 5.18122863,11.5057724 C5.18122863,7.92459087 8.20167754,5.02125451 11.9330785,5.02125451 C15.6587019,5.02125451 18.6827542,7.92459087 18.6827542,11.5057724 C18.6863029,12.9636891 18.1740949,14.3795294 17.2300564,15.5212545 L17.2300564,15.5212545 Z" id="形状" fill="#FFFFFF"></path>
                            </g>
                        </g>
                        <g id="编组-9复制-3" transform="translate(143.000000, 73.000000)">
                            <circle id="椭圆形" fill="#5E7CE0" cx="12" cy="12" r="12"></circle>
                            <g id="add-3" transform="translate(4.000000, 4.000000)" fill="#FFFFFF">
                                <path d="M7.2,7.2 L7.2,0.8 C7.2,0.32 7.52,0 8,0 C8.48,0 8.8,0.32 8.8,0.8 L8.8,7.2 L15.2,7.2 C15.68,7.2 16,7.52 16,8 C16,8.48 15.68,8.8 15.2,8.8 L8.8,8.8 L8.8,15.2 C8.8,15.68 8.48,16 8,16 C7.52,16 7.2,15.68 7.2,15.2 L7.2,8.8 L0.8,8.8 C0.32,8.8 0,8.48 0,8 C0,7.52 0.32,7.2 0.8,7.2 L7.2,7.2 Z" id="路径"></path>
                            </g>
                        </g>
                        <path d="" id="形状结合" stroke="#ADB0B8"></path>
                    </g>
                    <text id="上传身份证国徽面" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="18" fill="#252B3A">
                        <tspan x="52" y="112">上传身份证国徽面</tspan>
                    </text>
                </g>
            </g>
        </g>
    </g>
</svg>