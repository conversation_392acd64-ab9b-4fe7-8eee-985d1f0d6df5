// 展开动画
export function transitionEnter(element: HTMLElement) {
  const height = element.scrollHeight
  element.style.height = '0'
  // 触发回流
  element.offsetHeight
  // 设置目标高度
  element.style.height = `${height}px`
  // 动画完成后清除高度限制
  element.addEventListener('transitionend', () => {
    element.style.height = ''
  }, { once: true })
}

// 收起动画
export function transitionLeave(element: HTMLElement) {
  // 设置初始高度
  const height = element.scrollHeight
  element.style.height = `${height}px`
  // 触发回流
  element.offsetHeight
  // 设置目标高度为0
  element.style.height = '0'
}

/**
 * @description: 简历详情页props
 * @return {*}
 */
export interface ResumeDetailProps {
  currentResumeId: string
}

/**
 * @description: 1. 简历头部信息
 * @return {*}
 */
export interface ProfileHeaderProps {
  resumeId: string
  avatar: string
  candidateName: string
  englishName: string
  position: string
  workYears: string
  gender: string
  graduateSchool: string
  highestEducation: string
  location: string
  birthday: string
  age: string
  phone: string
  email: string
  resumeStatus: string
  updateTime: string
  matchScore: number

  resumeFileUrl: string
  downloadFileUrl: string

  hometown: string[]
}

/**
 * @description: 待提升项数据类型
 */
export interface ImprovementItem {
  description: string
}
export type ImprovementProps = ImprovementItem[]

/**
 * @description: 核心优势数据类型
 */
export interface StrengthItem {
  description: string
}
export type StrengthProps = StrengthItem[]

/**
 * @description: 评价数据类型
 */
export interface EvaluationDataProps {
  matchScore: number
  overallEvaluation: string
}

/**
 * @description: 技能地图数据类型
 */
// export interface SkillMapDataProps {
//   technicalSkillScore: number
//   workExperienceScore: number
//   educationScore: number
//   projectScore: number
//   softSkillScore: number
// }
export type SkillMapDataProps = any[]
/**
 * @description: 词云数据类型
 */
export interface SummaryDataItem {
  word: string
  weight: number
}
export type SummaryDataProps = SummaryDataItem[]

/**
 * @description: 质疑点数据类型
 */
export interface WeaknessItem {
  title: string
  description: string
}
export type WeaknessItemProps = WeaknessItem[]

/**
 * @description: 工作经历数据类型
 */
export interface ExperienceItemItem {
  company: string
  department: string
  position: string
  startDate: string
  endDate: string
  description: string
  emblem?: string // 可选，用于教育经历
}
export type ExperienceItemProps = ExperienceItemItem[]

/**
 * @description: 教育经历数据类型
 */
export interface EducationItemItem {
  degree: string
  description: string
  emblemUrl: string
  endDate: string
  major: string
  school: string
  schoolTags: string[]
  startDate: string
}
export type EducationItemProps = EducationItemItem[]

/**
 * @description: 校园经历数据类型
 */
export interface CampusExperienceItemItem {
  description: string
  endDate: string
  school: string
  startDate: string
  position: string
}
export type CampusExperienceItemProps = CampusExperienceItemItem[]

/**
 * @description: 项目经历数据类型
 */
export interface ProjectItemItem {
  description: string
  endDate: string
  name: string
  responsibility: string
  role: string
  startDate: string
  technologies: string[]
}
export type ProjectItemProps = ProjectItemItem[]

/**
 * @description: 获奖证书数据类型
 */
export interface CertificateItemItem {
  description: string
  issueDate: string
  issuer: string
  name: string
}
export type CertificateItemProps = CertificateItemItem[]

export interface TechSkillsDataProps {
  required: {
    label: string
    skills: Array<{
      skillName: string
      proficiency: 1 | 2 
    }>
  }
  missing: {
    label: string
    skills: Array<{
      skillName: string
      proficiency: 1 | 2
    }>
  }
  bonus: {
    label: string
    skills: Array<{
      skillName: string
      proficiency: 1 | 2
    }>
  }
  extra: {
    label: string
    skills: Array<{
      skillName: string
      proficiency: 1 | 2
    }>
  }
}