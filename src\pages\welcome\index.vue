<template>
  <a-row class="welcome">
    <a-col :span="12" class="welcome-left">
      <h1><svg-icon name="qingzhu" style="margin-right: 0.53vw;"></svg-icon>欢迎使用图灵智面</h1>
      <h2>让我们了解一下全新的亮点功能吧</h2>
      <ul class="swipper-wrapper" @mousedown="startSwipe" @mousemove="swipe" @mouseup="endSwipe" @mouseleave="endSwipe" @wheel="wheel">
        <li class="swipper-item" v-for="(item, index) in list" :class="{ active: index === activeIndex }"
          :style="getSwiperItemStyle(index)" @click="activeIndex = index">
          <img class="swipper-item-icon" :src="item.icon" alt="">
          {{ item.title }}
        </li>
      </ul>
    </a-col>
    <a-col :span="12">
      <div class="welcome-right">
        <img class="gif-img" :src="list[activeIndex].img" alt="">
        <h1>
          <img style="margin-right: 0.66vw;width: 1.8vw;height: 1.8vw;" :src="list[activeIndex].icon" alt="">
          {{ list[activeIndex].title }}
        </h1>
        <ul>
          <li class="desc-item" v-for="item in list[activeIndex].desc">{{ item }}</li>
        </ul>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useIntervalFn, useTimeoutFn } from '@vueuse/core'

const NUM = 7 // 展示的卡片数量（奇数）

const list = [
  {
    title: 'AI答题',
    icon: new URL('../../assets/images/svg/welcome-1.svg', import.meta.url).href,
    img: new URL('../../assets/gif/1.gif', import.meta.url).href,
    desc: [
      '一键生成答案解析，辅助判断答案是否正确，提高出题正确率。',
    ]
  },
  // {
  //   title: '随机抽题',
  //   icon: new URL('../../assets/images/svg/welcome-2.svg', import.meta.url).href,
  //   img: new URL('../../assets/gif/2.gif', import.meta.url).href,
  //   desc: [
  //     '哈哈哈',
  //     '喜喜'
  //   ]
  // },
  {
    title: 'AI智能分析',
    icon: new URL('../../assets/images/svg/welcome-3.svg', import.meta.url).href,
    img: new URL('../../assets/gif/3.gif', import.meta.url).href,
    desc: [
      '一键对考生和试卷进行全方位分析，考生答题情况、试卷质量尽在掌握。',
    ]
  },
  {
    title: 'AI智能阅卷',
    icon: new URL('../../assets/images/svg/welcome-4.svg', import.meta.url).href,
    img: new URL('../../assets/gif/4.gif', import.meta.url).href,
    desc: [
      '对主观题进行智能分析，为教师提供判分参考，提高阅卷效率。'
    ]
  },
  {
    title: 'AI更改题型',
    icon: new URL('../../assets/images/svg/welcome-5.svg', import.meta.url).href,
    img: new URL('../../assets/gif/5.gif', import.meta.url).href,
    desc: [
      '一键转换题型，实现一个知识点扩展多种题型，高效扩充题库。'
    ]
  },
  {
    title: 'AI批量出题',
    icon: new URL('../../assets/images/svg/welcome-6.svg', import.meta.url).href,
    img: new URL('../../assets/gif/6.gif', import.meta.url).href,
    desc: [
      '只需简单描述即可一键批量生成题目，出题从此不是一件难事。'
    ]
  },
  {
    title: '题目校对',
    icon: new URL('../../assets/images/svg/welcome-7.svg', import.meta.url).href,
    img: new URL('../../assets/gif/7.gif', import.meta.url).href,
    desc: [
      '题目数量太多？AI智能批量校对，让您只需要关注校对为错误的题目。'
    ]
  },
  {
    title: '考试分析',
    icon: new URL('../../assets/images/svg/welcome-8.svg', import.meta.url).href,
    img: new URL('../../assets/gif/8.gif', import.meta.url).href,
    desc: [
      '数图结合，定性定量地展示考试结果，考生参考情况和成绩尽收眼底。'
    ]
  },
  {
    title: '合并监考',
    icon: new URL('../../assets/images/svg/welcome-9.svg', import.meta.url).href,
    img: new URL('../../assets/gif/9.gif', import.meta.url).href,
    desc: [
      '解决一场考试AB卷的问题，同时监考多张试卷，提高监考资源利用率。'
    ]
  },
  {
    title: '官方题库',
    icon: new URL('../../assets/images/svg/welcome-10.svg', import.meta.url).href,
    img: new URL('../../assets/gif/10.gif', import.meta.url).href,
    desc: [
      '大量题目开箱即用，创建试卷0成本。'
    ]
  }
]

const activeIndex = ref(0)

const { pause, resume } = useIntervalFn(() => {
  activeIndex.value = (activeIndex.value + 1) % list.length
}, 7000)

const { start: startTimeoutFn, stop: stopTimeoutFn } = useTimeoutFn(resume, 5000)

function getSurroundingNumbers(length: number, activeIndex: number) {
  var start = activeIndex - (NUM - 1) / 2;
  var end = activeIndex + (NUM + 1) / 2;

  if (activeIndex < (NUM - 1) / 2) {
    start = 0;
    end = NUM;
  }

  if (activeIndex > length - (NUM + 1) / 2) {
    start = length - NUM;
    end = length;
  }

  var result = [];
  for (var i = start; i < end; i++) {
    result.push(i);
  }

  return result;
}
const surroundingNumbers = computed(() => getSurroundingNumbers(list.length, activeIndex.value))

function getSwiperItemStyle(index: number) {
  const HIDEH = 15,
    H = 60,
    M = 16
  // 每个卡片高度60px 间隔16px
  // 最多正常展示NUM个，其余藏在后，只露出16px高度
  let y = 0,
    scaleX = 1,
    backgroundColor = '#f9fbfc',
    zIndex = 99,
    boxShadow = '2px 2px 1px 0px rgba(0,0,0,0.04), #fff 0px -2px 0px 0px';

  if (!surroundingNumbers.value.includes(index)) {
    backgroundColor = '#EBEDF2'
    zIndex = zIndex - Math.abs(activeIndex.value - index) + 2
    scaleX = 0.98 ** (Math.abs(activeIndex.value - index) + 2)

    if (index < surroundingNumbers.value[0]) {
      // 在上面
      y = HIDEH * index
      scaleX = 0.98 ** (surroundingNumbers.value[0] - index)
      boxShadow = '2px 2px 1px 0px rgba(255, 255, 255, .3) inset';
    } else {
      // 在下面
      y = surroundingNumbers.value[0] * HIDEH + (NUM - 1) * (H + M) + (index - surroundingNumbers.value.at(-1)!) * HIDEH
      scaleX = 0.98 ** (index - surroundingNumbers.value.at(-1)!)
      boxShadow = '2px 2px 1px 0px rgba(0,0,0,0.04)';
    }
  } else {
    y = surroundingNumbers.value[0] * HIDEH + (index - surroundingNumbers.value[0]) * (H + M)
  }

  if (activeIndex.value === index) {
    boxShadow = '5px 6px 12px 0px rgba(0,0,0,0.08), #fff 0px -2px 0px 0px';
  }


  return {
    transform: 'translateY(' + y / 19.2 + 'vw) scaleX(' + scaleX + ')',
    backgroundColor,
    zIndex,
    boxShadow
  }
}


// 处理滑动
let startY = 0; // 鼠标按下时的y坐标
let accumulatedDiffY = 0; // 累积的滑动距离
let swiping = false; // 是否正在滑动

function startSwipe(event: MouseEvent) {
  startY = event.clientY;
  swiping = true;
  pause()
  stopTimeoutFn()
}

function swipe(event: MouseEvent) {
  if (!swiping) return;
  const currentY = event.clientY;
  const diffY = currentY - startY;

  // 累积滑动距离
  accumulatedDiffY += diffY;

  // 假设每个元素的高度是100px
  const elementHeight = 84;

  // 使用累积的滑动距离计算滑动的元素数量，向下取整
  const numItems = Math.floor(Math.abs(accumulatedDiffY) / elementHeight);

  if (accumulatedDiffY > 0) {
    // 向下滑动，activeIndex减少
    if (activeIndex.value <= 2) return
    activeIndex.value = Math.min(list.length - 3, activeIndex.value)
    activeIndex.value = Math.max(0, activeIndex.value - numItems);
    accumulatedDiffY -= numItems * elementHeight; // 更新累积的滑动距离
  } else {
    // 向上滑动，activeIndex增加
    if (activeIndex.value >= list.length - 3) return
    activeIndex.value = Math.max(2, activeIndex.value)
    activeIndex.value = Math.min(list.length - 1, activeIndex.value + numItems);
    accumulatedDiffY += numItems * elementHeight; // 更新累积的滑动距离
  }

  // 更新起始Y坐标
  startY = currentY;
}

function endSwipe(event: MouseEvent) {
  swiping = false;
  accumulatedDiffY = 0; // 结束滑动时重置累积的滑动距离
  startTimeoutFn()
}

// 处理鼠标滚动
function wheel(event: WheelEvent) {
  event.preventDefault(); // 防止页面滚动
  pause()
  stopTimeoutFn()
  if (event.deltaY < 0) {
    // 向下滚动，activeIndex减少
    activeIndex.value = Math.max(0, activeIndex.value - 1);
  } else {
    // 向上滚动，activeIndex增加
    activeIndex.value = Math.min(list.length - 1, activeIndex.value + 1);
  }
  startTimeoutFn()
}

</script>

<style lang="less" scoped>
.welcome {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10vw;

  .welcome-left {
    display: flex;
    flex-direction: column;

    h1 {
      font-size: 1.88vw;
      font-weight: bold;
      display: flex;
      align-items: center;
    }

    h2 {
      margin-top: 0.83vw;
      font-size: 1.25vw;
      color: rgba(0, 0, 0, 0.65);
    }

    .swipper-wrapper {
      flex: 1;
      min-height: 0;
      margin-top: 1.4vw;
      position: relative;
      user-select: none;

      .swipper-item {
        position: absolute;
        top: 0;
        width: 15.63vw;
        height: 3.13vw;
        border-radius: 0.42vw;
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;
        font-size: 1.04vw;
        padding-left: 2.08vw;
        cursor: pointer;
        transition: all ease .5s;
        .swipper-item-icon {
          margin-right: 0.9375vw;
          width: 1.25vw;
          height: 1.25vw;
        }
      }
    }

  }

  .welcome-right {
    width: 34.64vw;
    background: linear-gradient(43deg, #def0ff 0%, #e4eaff 53%, #e1e0ff 100%);
    border-radius: 0.42vw;
    padding: 2.5vw 2.19vw 0 2.19vw;

    .gif-img {
      width: 100%;
      border-radius: 0.42vw;
    }

    h1 {
      // margin-top: 1.77vw;
      margin-top: 40px;
      margin-bottom: 0.83vw;
      font-size: 1.66vw;
      font-weight: bold;
      display: flex;
      align-items: center;
    }

    .desc-item {
      font-size: 0.83vw;
      color: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      line-height: 1.25vw;

      &::before {
        display: inline-block;
        content: '';
        width: 0.21vw;
        height: 0.21vw;
        border-radius: 50%;
        background-color: #5478EE;
        margin-right: 0.42vw;
      }
    }
  }

  .ant-col {
    height: 34.58vw;
    display: flex;
  }
}
</style>
