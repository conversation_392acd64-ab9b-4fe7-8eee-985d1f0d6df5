#!/usr/bin/env node

const { spawn } = require('node:child_process')
const fs = require('node:fs')
const path = require('node:path')
const dotEnv = require('dotenv')

function createScript(configContent) {
  const combinedEnv = { ...process.env, ...configContent }

  const commandToExecute = 'claude' // 假设 claude 仍是你直接要运行的命令
  const args = []

  // --- 新增或修改这里 ---
  const currentWorkingDirectory = process.cwd() // 获取当前 Node.js 脚本的执行目录
  // 或者指定一个绝对路径，例如：
  // const currentWorkingDirectory = path.resolve(__dirname, 'some_specific_directory');

  console.log(`子进程将在目录: ${currentWorkingDirectory} 中执行`)

  const child = spawn(commandToExecute, args, {
    env: combinedEnv,
    stdio: 'inherit',
    cwd: currentWorkingDirectory,
    shell: true,
  })
  // --- 修改结束 ---

  child.on('close', (code) => {
    if (code !== 0) {
      console.error(`子进程退出，退出代码 ${code}`)
    }
    else {
      console.log(`子进程成功退出`)
    }
  })

  child.on('error', (err) => {
    console.error(`无法启动子进程: ${err.message}`)
    console.error(`请确保 '${commandToExecute}' 命令已安装并可在 PATH 中找到.`)
    console.error(`尝试在终端运行 'claude --version' 来验证。`)
  })
}

function readClaudeConfig() {
  const configPath = path.join(process.cwd(), 'claude.conf')
  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, 'utf8')
      createScript(dotEnv.parse(configContent.toString()))
      // 不需要 return configContent; 这里，因为已经调用了 createScript
    }
    catch (error) {
      console.error('Error reading claude.conf:', error.message)
      process.exit(1)
    }
  }
  else { // 确保有 else 块来处理文件不存在的情况
    console.error('Error: No claude configuration found. Please create claude.conf file or add claudeConfig to package.json')
    process.exit(1)
  }
}

readClaudeConfig()