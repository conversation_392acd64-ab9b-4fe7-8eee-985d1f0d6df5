import { MaybeRefOrGetter, toValue } from "@vueuse/core";
import _ from "lodash";
import { isRef, onUnmounted, ref, watch } from "vue";


interface UseDelayIntervalFnOptions {
    immediateCallback?: boolean;
}
export function useDelayIntervalFn(
    fn: () => Promise<any>,
    delay: MaybeRefOrGetter<number> = 1000,
    options: UseDelayIntervalFnOptions
) {
    const { immediateCallback = false } = options;

    let timer: ReturnType<typeof setTimeout> | null = null;
    const isActive = ref(false);

    function clean() {
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
    }

    function pause() {
        isActive.value = false;
        clean();
    }

    let resumeId = "";
    let resume = () => {
        let tempResumeId = _.uniqueId();
        resumeId = tempResumeId;

        const intervalValue = toValue(delay);
        if (intervalValue <= 0) return;

        isActive.value = true;
        clean();
        fn().finally(() => {
            // 只执行最近的一次resume的回调
            if (tempResumeId !== resumeId) return;

            timer = setTimeout(() => {
                if (!isActive.value) return;
                resume();
            }, intervalValue);
        });
    };

    if (immediateCallback) {
        resume();
    }

    let stopWatch = () => { };
    if (isRef(delay) || typeof delay === "function") {
        stopWatch = watch(delay, () => {
            if (isActive.value) resume();
        });
    }

    onUnmounted(() => {
        pause();
        stopWatch();
    });

    return {
        isActive,
        resume,
        pause,
    };
}