<template>
    <div style="max-height: 50vh; overflow: auto;text-align: center;">
        <a-spin :spinning="loading"></a-spin>
        <a-tree
            v-if="treeData.length"
            class="part-tree"
            :tree-data="treeData"
            :defaultExpandAll="true"
            :fieldNames="{ title: 'name', key: 'id' }"
            show-icon
            show-line
            :selectedKeys="selectedKeys"
            @select="handleSelect">
            <template #icon="item">
                <img v-if="item.children?.length" src="@/assets/icons/svg/分组.svg" />
                <img v-else src="@/assets/icons/svg/账号.svg" />
            </template>
            <template #switcherIcon="{ switcherCls }">
                <Icon icon="DownOutlined" :class="switcherCls" />
            </template>
            <template #title="item">
              <div class="title" :title="item.name">{{ item.name }}</div>
            </template>
        </a-tree>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { subdepartment, switchdepartment } from '@/api/admin/accountManage'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
const store = useStore()
const router = useRouter()
const route = useRoute()

// 获取可切换的部门
const treeData = ref<any[]>([])
const loading = ref(false)
async function getAvailableDepartment() {
    loading.value = true
    try {
        let res = await subdepartment() as any
        treeData.value = res
    } finally {
        loading.value = false
    }
}
getAvailableDepartment()


// 切换部门
const selectedKeys = ref<string[]>([store.getters.userInfo.switch_dept_id])
function handleSelect(keys: string[]) {
    if (keys.length === 0) return
    selectedKeys.value = keys
}
async function confirm() {
    if (selectedKeys.value?.[0] === store.getters.userInfo.switch_dept_id) return
    await switchdepartment({ deptId: selectedKeys.value?.[0] })
    await store.dispatch('GET_USER_INFO')
    
    message.success('切换部门成功，即将刷新页面')
    setTimeout(() => {
        window.location.href = window.location.origin
    }, 1000);
    
}

defineExpose({
    confirm
})

</script>
<style lang="less">
.part-tree {
    .ant-tree-treenode-switcher-open {
        width: 100%;
    }
    .ant-tree-node-content-wrapper {
      flex: auto;
      min-width: 0;
    }
    .ant-tree-title {
      flex: auto;
      min-width: 0;
    }
    .title {
        margin-left: 4px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>