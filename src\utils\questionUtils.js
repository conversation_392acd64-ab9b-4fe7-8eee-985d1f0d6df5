/* eslint-disable */
export const rules = {
  type: [{ required: true, validator: checkType, trigger: 'change' }],
  category: [{ required: true, validator: checkCategory, trigger: 'blur' }],
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }],
  complexcontent: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }],
  subQuestionList: [{ required: true, validator: checkSubQuestionList, trigger: 'blur' }],
  func_name: [{ required: true, validator: checkVariateName, trigger: 'blur' }],
  rtype: [{ required: true, validator: checkRtype, trigger: 'blur' }],
  difficulty: [{ required: true, message: '请选择难度', trigger: 'blur', type: 'number' }],
  disablekws: [{ required: true, validator: checkDisablekws, trigger: 'blur' }],
  parameters: [{ required: true, validator: checkParameters, trigger: 'blur' }],
  btestcase: [{ required: true, validator: checkBtestcase, trigger: 'blur' }],
  ptestcase: [{ required: true, validator: checkPtestcase, trigger: 'blur' }],
};

// 校验方法
export async function checkType(rule, value) {
  if (value === undefined) {
    return Promise.reject('请选择题目类型');
  } else {
    return Promise.resolve();
  }
}

export async function checkCategory(rule, value) {
  if (value === undefined) {
    return Promise.reject('请选择所属题库');
  } else {
    return Promise.resolve();
  }
}

export async function checkOptions(rule, value) {
  let isDup = false;
  const allContents = [];
  value.forEach((item) => {
    if (allContents.includes(item.content)) {
      isDup = true;
    } else {
      allContents.push(item.content);
    }
  });
  if (!value.length) {
    return Promise.reject('请添加选项');
  }
  if (!value.every((item) => item.content)) {
    return Promise.reject('选项内容都不能为空');
  }
  if (isDup) {
    return Promise.reject('选项内容不能相同');
  } else {
    return Promise.resolve();
  }
}

export async function checkBody(rule, value) {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容');
  } else {
    return Promise.resolve();
  }
}

export async function checkSubQuestionList(rule, value) {
  if (value.length === 0) {
    return Promise.reject('请添加子题');
  } else {
    return Promise.resolve();
  }
}

export async function checkVariateName(rule, value) {
  
}
  
