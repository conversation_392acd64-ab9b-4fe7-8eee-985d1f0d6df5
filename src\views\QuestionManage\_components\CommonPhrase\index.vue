<template>
  <div>
    <a-dropdown destroyPopupOnHide class="mt-[12px]" :trigger="['click']" v-model:open="normalDropDownVisible">
    <a-button class="ant-dropdown-link" size="small" @click.prevent>
      <span class="text-[12px]">常用语</span>
    </a-button>
    <template #overlay>
      <a-menu class="bg-[#fff] w-[700px] shadow-[0px_10px_40px_0px_rgba(0,0,0,0.10)]">
        <div ref="dropdown" class="px-[22px] py-[30px]" v-if="normalDropDownVisible">
          <div class="text-[20px] font-bold w-full flex justify-between px-[10px]">
            常用语
            <CloseOutlined class="cursor-pointer" @click.stop.prevent="closeDropDown" />
          </div>
          <div class="max-h-[168px] overflow-auto mt-[26px]">
            <div
              class="cursor-pointer justify-between hover:bg-[#F1F4FE] rounded-[8px] text-[12px] h-[34px] mb-[8px] bg-[#F8F9FA] flex items-center px-[8px] pr-[20px]"
              v-for="item in commonPhraseList"
              :key="item.id"
              @click.stop="selectItem(item)"
            >
              <a-tooltip placement="top">
                <template #title>
                  <div class="w-[100%]">{{ item.content }}</div>
                </template>
                <div class="max-w-[80%] w-fit overflow-hidden text-ellipsis">{{ item.content }}</div>
              </a-tooltip>
              <div>
                <EditOutlined @click.stop="editItem(item)" />
                <a-popconfirm
                  title="确定要删除该常用语吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm.stop="deleteItem(item)"
                >
                  <DeleteOutlined class="ml-[10px]" @click.stop/>
                </a-popconfirm>
              </div>
            </div>
          </div>
          <div
            class="cursor-pointer hover:bg-[#F1F4FE] py-[12px] px-[10px] rounded-[8px]"
            @click="showModal"
          >
            <PlusOutlined class="mr-[10px]" />添加常用语
          </div>
        </div>
      </a-menu>
    </template>
  </a-dropdown>
  <a-modal
    class="add-normal-container"
    :zIndex="1080"
    v-model:open="visible"
    :closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>添加常用语</template>
    <a-form :model="phraseFormState" ref="form">
      <a-form-item
        name="commonPhraseContent"
        :rules="[{ required: true, message: '请输入常用语', trigger: 'blur' },{ validator: validateRepetition, trigger: 'blur' }]"
      >
        <a-textarea
          v-model:value="phraseFormState.commonPhraseContent"
          placeholder="请输入"
          :maxlength="1000"
          class="mt-[8px]"
          :auto-size="{ minRows: 6, maxRows: 8 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, CloseOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { createCommonPhrase, getCommonPhrase, editCommonPhrase, deleteCommonPhrase } from '@/pages/questionManage/hooks/api'

const modelValue = defineModel()
const normalDropDownVisible = ref(false)
const isEditing = ref(false)
const isEditItem = ref(null)

const closeDropDown = () => {
  normalDropDownVisible.value = false
}

const openDropDown = (recurFlag = false) => {
  normalDropDownVisible.value = true
  if (recurFlag) {
    execRecur()
  }

  function recur(timestamp = 0) {
    normalDropDownVisible.value = true
    if (timestamp++ >= 5) {
      return
    }
    requestAnimationFrame(() => recur(timestamp))
  }

  function execRecur() {
    requestAnimationFrame(() => recur(0))
  }
}

const editItem = (item: any) => {
  isEditing.value = true
  isEditItem.value = item
  phraseFormState.commonPhraseContent = item.content
  visible.value = true
}
const selectItem = (item: any) => {
  modelValue.value = item.content
  closeDropDown()
}
const visible = ref(false)
const commonPhraseList = ref<any>([])

const getCommonPhraseList = async () => {
  const res = await getCommonPhrase({
    pageNo: 1,
    pageSize: 300
  })
  commonPhraseList.value = res.records || []
}

getCommonPhraseList()

const phraseFormState = reactive({
  commonPhraseContent: ''
})

const formRef = useTemplateRef<any>('form')

const showModal = () => {
  visible.value = true
}


const isRepetition = ref(false)
const validateRepetition = async () => {
  if (isRepetition.value) {
    return Promise.reject('该常用语已存在');
  } else {
    return Promise.resolve();
  }
};

const handleOk = async () => {
  if(!formRef.value) return
  // @ts-ignore
  await formRef.value.validate()
    .then(async () => {
      const execFunc = isEditing.value ? editCommonPhrase : createCommonPhrase
      const params = {
        content: phraseFormState.commonPhraseContent
      }
      if (isEditing.value) {
        // @ts-ignore
        params.id = isEditItem.value.id
      }
      let res
      try {
        res = await execFunc(params)
        await getCommonPhraseList()
        visible.value = false
        phraseFormState.commonPhraseContent = ''
        isEditing.value = false
        openDropDown(true)
      } catch(e){
        if(!res) {
          isRepetition.value = true
          formRef.value && formRef.value?.validate()
        } 
      }
    })
    .catch(() => {
      return false
    })
}

const handleCancel = async () => {
  visible.value = false
  phraseFormState.commonPhraseContent = ''
  isEditing.value = false
  openDropDown(true)
}

// 在 script setup 部分添加删除方法
const deleteItem = async (item: any) => {
  await deleteCommonPhrase({
    ids: [item.id]
  })
  await getCommonPhraseList()
  openDropDown(true)
}
</script>

<style lang="scss" >
.ant-popover{
      z-index: 1090;
}
</style>