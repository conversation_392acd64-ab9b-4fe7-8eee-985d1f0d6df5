import type { ModalOptionsEx } from '/@/hooks/web/useMessage'

import type { ComputedRef, Ref } from 'vue'
import Clipboard from 'clipboard'
import { isRef, unref, watch } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'

/** 带复制按钮的弹窗 */
interface IOptions extends ModalOptionsEx {
  // 要复制的文本，可以是一个 ref 对象，动态更新
  copyText: string | Ref<string> | ComputedRef<string>
}

const COPY_CLASS = 'copy-this-text'
const CLIPBOARD_TEXT = 'data-clipboard-text'

export function useCopyModal() {
  return { createCopyModal }
}

const { createMessage, createConfirm } = useMessage()

/** 创建复制弹窗 */
function createCopyModal(options: Partial<IOptions>) {
  const modal = createConfirm({
    ...options,
    iconType: options.iconType ?? 'info',
    width: options.width ?? 500,
    title: options.title ?? '复制',
    maskClosable: options.maskClosable ?? true,
    okText: options.okText ?? '复制',
    okButtonProps: {
      ...options.okButtonProps,
      class: COPY_CLASS,
      [CLIPBOARD_TEXT]: unref(options.copyText),
    } as any,
    onOk() {
      return new Promise((resolve: any) => {
        const clipboard = new Clipboard(`.${COPY_CLASS}`)
        clipboard.on('success', () => {
          clipboard.destroy()
          createMessage.success('复制成功')
          resolve()
        })
        clipboard.on('error', () => {
          createMessage.error('该浏览器不支持自动复制')
          clipboard.destroy()
          resolve()
        })
      })
    },
  })

  // 动态更新 copyText
  if (isRef(options.copyText)) {
    watch(options.copyText, (copyText) => {
      modal.update({
        okButtonProps: {
          ...options.okButtonProps,
          class: COPY_CLASS,
          [CLIPBOARD_TEXT]: copyText,
        } as any,
      })
    })
  }
  return modal
}

export function useGo(_router?: Router) {
  // update-begin--author:liaozhiyang---date:20230908---for：【issues/694】404返回首页问题
  const userStore = useUserStore()
  const homePath = userStore.getUserInfo.homePath || PageEnum.BASE_HOME
  // update-end--author:liaozhiyang---date:20230908---for：【issues/694】404返回首页问题
  let router
  if (!_router) {
    router = useRouter()
  }
  const { push, replace } = _router || router
  function go(opt: PageEnum | RouteLocationRawEx | string = homePath, isReplace = false) {
    if (!opt) {
      return
    }
    if (isString(opt)) {
      isReplace ? replace(opt).catch(handleError) : push(opt).catch(handleError)
    }
    else {
      const o = opt as RouteLocationRaw
      isReplace ? replace(o).catch(handleError) : push(o).catch(handleError)
    }
  }
  return go
}