class OnionMiddleware {
  constructor() {
    this.middlewares = [] // 存储中间件函数的队列
  }

  // 注册中间件
  use(middleware) {
    this.middlewares.push(middleware)
  }

  // 执行中间件链
  execute(req, res, callback) {
    let index = -1 // 当前中间件索引

    const next = () => {
      index++
      if (index < this.middlewares.length) {
        // 调用下一个中间件，传入 req, res 和 next
        this.middlewares[index](req, res, next)
      }
      else {
        // 到达核心处理逻辑
        callback(req, res)
      }
    }

    // 开始执行第一个中间件
    next()
  }
}

function createApp() {
  const app = new OnionMiddleware()

  app.listen = (port) => {
    const req = { method: 'GET', url: '/api', query: { token: '123' } }
    const res = {
      statusCode: 200,
      body: '',
      send: (data) => {
        res.body = data
        console.log(`[响应] 状态码: ${res.statusCode}, 数据: ${data}`)
      },
      status: (code) => {
        res.statusCode = code
        return res
      },
    }

    // 模拟核心路由处理
    const coreHandler = (req, res) => {
      console.log('[核心层] 处理请求')
      res.send('核心处理完成: Hello, Onion!')
      console.log('[核心层] 响应已发送')
    }

    // 执行中间件链
    app.execute(req, res, coreHandler)
  }

  return app
}

const app = createApp()

app.use((req, res, next) => {
  console.log(`[请求进入] 日志中间件: ${req.method} ${req.url}`)
  next() // 进入下一层
  console.log('[响应返回] 日志中间件')
})

app.use((req, res, next) => {
  console.log('[请求进入] 认证中间件: 检查 token')
  if (req.query.token === '123') {
    next() // token 正确，继续
  }
  else {
    res.status(401).send('认证失败：无效的 token')
  }
  console.log('[响应返回] 认证中间件')
})

app.listen(3000)