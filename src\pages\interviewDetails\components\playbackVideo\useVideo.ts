import Hls from 'hls.js'

// const videoUrl = 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/record/20250425/admin/c830723/global/d7ddbbda6241c1b26939aa93e24d0248_c830723__uid_s_830723__uid_e_video.m3u8'
const videoUrl = 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/record/20250425/admin/c830723/3/1/1674f25ceb4f8d67fcd7a0a267cedb1c_c830723__uid_s_830723__uid_e_av.m3u8'
const videoSrc = videoUrl

export async function loadMedia(video: any) {
  if (Hls.isSupported()) {
    const hls = new Hls()
    await hls.loadSource(videoSrc)
    await hls.attachMedia(video)
    video.play()
    video.addEventListener('loadedmetadata', () => {
      let frameCount = 0
      const timelineElement = document.getElementById('timeline') as any      
      const mediaStream = video.captureStream()
      const videoTrack = mediaStream.getVideoTracks()[0]
      const frameThumbnails = []
      const frameInterval = 1 // 抽帧间隔(秒)
      // 设置MediaStreamTrackProcessor
      const trackProcessor = new (window as any).MediaStreamTrackProcessor({
        track: videoTrack,
      })

      if (!trackProcessor) {
        console.error('MediaStreamTrackProcessor is not supported in this environment.')
        return
      }
        
      captureAndProcessFrames()
        
      async function captureAndProcessFrames() {
        const reader = trackProcessor.readable.getReader()
        
        while (true) {
          const { done, value: frame } = await reader.read()
             
          if (done) 
            break
        
          const currentTime = frame.timestamp / 1000000 // 将时间戳转换为秒
        
          if (frameCount === 0 || currentTime >= frameCount * frameInterval) {
            frameCount++
            createFrameThumbnail(frame, currentTime) // 创建缩略图
          }
        
          frame.close() // 释放帧资源
        }
      }
        
      // 创建帧缩略图
      function createFrameThumbnail(frame: any, time: any) {
        const canvas = document.createElement('canvas')
        canvas.width = 120
        canvas.height = 80
        const ctx = canvas.getContext('2d') as any
        
        // 将帧绘制到 Canvas 上
        ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
        
        // 创建缩略图元素
        const thumbnail = document.createElement('div')
        thumbnail.className = 'frame-thumbnail'
        thumbnail.appendChild(canvas)
        
        // 添加时间标记
        const timeLabel = document.createElement('div')
        timeLabel.className = 'frame-time'
        timeLabel.textContent = (time)
        thumbnail.appendChild(timeLabel)
        
        // 点击事件，跳转到对应时间
        thumbnail.addEventListener('click', () => {
          video.currentTime = time
        })
        
        // 添加到时间轴
        timelineElement.appendChild(thumbnail)
        
        // 保存缩略图信息
        frameThumbnails.push({ time, element: thumbnail })
      }
    })
  }
}