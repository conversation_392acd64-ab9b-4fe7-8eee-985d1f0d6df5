<template>
    <div class="proofreading-task" v-if="processDetail">
        <div style="display: flex;justify-content: space-between;">
            <span>当前校对进度</span>
            <span>{{ (processDetail.successQues + processDetail.failedQues) || 0 }} / {{ processDetail.totalQues }}</span>
        </div>
        <a-progress :stroke-color="{ from: '#8db0f8', to: '#5478ee' }" :strokeWidth="12" :percent="(((processDetail.successQues + processDetail.failedQues) || 0) / processDetail.totalQues) * 100" :show-info="false"
            status="active" />
        <a-carousel style="margin-top: 16px; user-select: none;" autoplay effect="fade" :dots="false">
            <div v-for="item in tips">
                <h3 style="text-align: center;">{{ item }}</h3>
            </div>
        </a-carousel>
        <div style="text-align: center; margin-top: 24px;">
            <a-button type="primary" style="border-radius: 8px;font-size: 14px;" @click="emits('close')">我已知晓</a-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, h, watch } from 'vue'
import { useIntervalFn } from '@vueuse/core'
import { getaiproofreadingtask } from "@/api/exam/index";
import { notification } from 'ant-design-vue'
import { CheckCircleFilled } from '@ant-design/icons-vue'

const props = defineProps<{
    taskId: string
}>()

const emits = defineEmits<{
    (e: 'finish'): void
    (e: 'close'): void
    (e: 'show-result', result: any): void
}>()

const tips = [
    '校对范围为当前题库及其子题库',
    '仅校对单选、多选和判断题',
    'AI结果仅作为参考',
    '图片和公式可能导致校对失败',
    '校对任务不可自行结束'
]

const processDetail = ref<{
    catg: string
    completed: boolean
    create_at: string
    create_by: string
    dept: string
    failed_ques: number
    id: string
    success_ques: number
    correct_ques: number
    error_ques: number
    total_ques: number
    update_at: string
}>()

// 每隔2s请求一次校对进度
async function getProcessDetail() {
    try {
        let res = await getaiproofreadingtask({ id: props.taskId })
        processDetail.value = res
        if (res.completed) {
            pause()
            await new Promise(resolve => setTimeout(resolve, 500)) // 延迟500ms， 否则展示不了进度条100%的状态
            emits('finish')
            emits('close')
            notification.success({
                class: 'proofreading-task-notification',
                icon: h(CheckCircleFilled, { style: 'color: #52c41a;font-size: 16px;'  }),
                message: h('p', null, [
                    h('span', null, '校对完成'),
                    h('a', {
                        style: 'margin-left: 16px;text-decoration: underline;color: #5478ee;',
                        onClick: () => {
                            emits('show-result', res)
                        }
                    }, '查看结果')
                ]),
                duration: null,
                onClick: () => {
                    notification.close('校对完成')
                }
            })
            processDetail.value = undefined
            return
        }
    } catch (error) {
        console.log(error)
    } finally {

    }
}
const { pause, resume, isActive } = useIntervalFn(getProcessDetail, 2000, {
    immediateCallback: true
})

watch(() => props.taskId, resume)

defineExpose({
    isActive
})

</script>

<style lang="less">
.proofreading-task-notification {
    width: 240px;
    .ant-notification-notice-icon {
        font-size: initial;
        line-height: 20px;
    }
}
.proofreading-task-finished-modal {
    .ant-modal-confirm-btns {
        width: 100%;
        text-align: center;
    }
}
.ant-notification-notice{
    padding: 12px 24px;
    .ant-notification-notice-message{
        margin-left: 30px;
    }
}
.ant-notification-notice-close {
    top: 12px;
}
</style>