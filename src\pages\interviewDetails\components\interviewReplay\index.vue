<script setup lang="ts">
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import ItemInfo from './itemInfo.vue'
// import JumpVideo from './jumpVideo.vue'
import ReplayVideo from './video.vue'

const props = defineProps({
  examDetail: {
    type: Object,
    default: () => {
      return {}
    },
  },
})
const route = useRoute()
const videoRef = useTemplateRef<any>('video')
const activeItem = ref(props.examDetail.questions[0])

function clickItem(item: any) {
  activeItem.value = item
  const startTime = dayjs(props.examDetail.exam.recordTime)
  const endTime = dayjs(item.question.recordTime)
  const secondsDifference = endTime.diff(startTime, 'second')
  videoRef.value && videoRef.value.jump(secondsDifference)
}
function jump(secondsDifference: any) {
  videoRef.value && videoRef.value.jump(secondsDifference)
}
</script>

<template>
  <div class="grid-container">
    <div class="bg-[#fff] w-full box-border px-[32px] h-[70px] pt-[20px] pb-[24px] text-[20px] font-bold bt text-[#000]">
      {{ route.query.candidateName }} —— 面试结果
    </div>
    <div class="content-bg flex flex-nowrap overflow-hidden w-full">
      <div class="op-left p-[12px]">
        <div class="font-bold text-[#000] p-[12px]">
          面试题列表
        </div>
        <el-tooltip v-for="(item, index) in examDetail.questions" :key="item.question.id" placement="left" :content="item.question.configSchema.content">
          <div :class="[activeItem.question.id === item.question.id ? 'active' : '']" class="title-item text-[#000]  px-[12px] text-ellipsis overflow-hidden whitespace-nowrap cursor-pointer py-[10px] rounded-[8px] my-[5px]" @click="clickItem(item)">
            {{ index + 1 }}. {{ item.question.configSchema.content }}
          </div>
        </el-tooltip>
      </div>
      <div class="pt-[20px] flex-1 p-[20px] relative"> 
        <a-alert style="background-color: #f1f4fe;border: 1px solid #5478ee;" message="AI 面试评价仅作辅助参考，最终以人工评判结果为准。语音识别文字记录可能存在误差，仅供参考。" type="info" show-icon />
        <div class="flex flex-nowrap w-full">
          <!-- 题目内容部分 -->
          <div class="flex-1 box-border p-[24px] bg-[#fff] rounded-[8px] mr-[15px] mt-[24px] h-media">
            <div style="background: linear-gradient(90deg,#e7edff 37%, #ebe7ff 87%);" class="p-[24px] rounded-[8px] text-[#000]">
              <div class="text-[16px] mb-[24px] flex items-center font-bold">
                <img src="@/assets/icons/svg/ai.svg" class="mr-[8px]">
                面试评价：
                <span class="text-[16px] inline-block" :style="{ color: examDetail.exam.score >= 81 ? '#06B190' : examDetail.exam.score >= 61 ? '#06B190' : examDetail.exam.score >= 41 ? 'ff4d4f' : '#d10000' }">{{ examDetail.exam.score >= 81 ? '优秀' : examDetail.exam.score >= 61 ? '良好' : examDetail.exam.score >= 41 ? '合格' : '不合格' }}</span>
              </div>
              <div class="text-[14px]">
                {{ examDetail.exam.evaluation }}
              </div>
            </div>
            <div class="text-[20px] font-bold text-[#000] mt-[24px] mb-[12px]">
              文字记录
            </div>
            <div class="overflow-y-scroll relative scroll-container">
              <ItemInfo v-for="ques in examDetail.questions" :key="ques.question.id" :exam-detail="examDetail" :active-item="activeItem" :info="ques" @jump="jump" />
            </div>
          </div>
          <!-- 右侧视频 -->
          <ReplayVideo ref="video" :exam-detail="examDetail" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.h-media{
  position: relative;
  overflow: hidden;
  min-height: 600px;
  @media screen and (max-width: 1440px) {
    height: 76vh;
  }
  @media screen and (min-width: 1441px) {
    height: 83vh;
  }
}
.grid-container{
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  height: 100%
}
.scroll-container {
  scroll-padding-top:100px;
  overflow: auto;
  @media screen and (max-width: 1440px) {
    height: calc(100% - 330px);
  }
  @media screen and (min-width: 1441px) {
    height: calc(100% - 270px);;
  }
}
.title-item{
      &:hover{
            background-color: #f1f4fe;
      }
}
.active {
      background-color: #f1f4fe;

}
.bg-inherit{
  background: linear-gradient(90deg,#e7edff 37%, #ebe7ff 87%);
}
.bt{
  border-bottom: 1px solid #e8e8e8;
}
.op-left {
  flex-shrink: 0;
  width: 200px;
  // min-height: 812px;
  height: calc(100vh - 70px);
  background: #ffffff;
  overflow: auto;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.12); 
}
.content-bg {
  background: linear-gradient(230deg,#e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);
}
</style>

<style>
.replay-container.el-dialog.is-fullscreen{
  padding: 0;
  background-color: #F5F5F5;
}
.replay-container .el-dialog__headerbtn{
  margin-top: 8px;
}
.replay-container .el-dialog__header {
  padding-bottom: 0;
}
</style>