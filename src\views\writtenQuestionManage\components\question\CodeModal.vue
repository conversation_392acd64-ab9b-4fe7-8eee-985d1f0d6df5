<template>
  <div v-show="codeModalVisible" class="code-modal-wrapper">
    <div class="lf" ref="leftDom" style="width: 50%">
      <slot name="code-q"></slot>
      <div class="touch-div" ref="moveDom">
        <span></span>
        <span></span>
      </div>
    </div>
    <div class="rt" ref="rightDom">
      <slot name="code-an"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  codeModalVisible: {
    type: Boolean,
    default: false
  }
})

// const visible = ref(false)
const leftDom = ref()
const rightDom = ref()
const moveDom = ref()
const handleMove = () => {
  moveDom.value.onmousedown = function (e: any) {
    const iEvent = e
    const dx = iEvent.clientX //当你第一次单击的时候，存储x轴的坐标。//相对于浏览器窗口
    const leftWidth = leftDom.value.offsetWidth
    const rightWidth = rightDom.value.offsetWidth
    document.onmousemove = function (e) {
      const iEvent = e
      const diff = iEvent.clientX - dx //移动的距离（向左滑时为负数,右滑时为正数）
      if (480 < leftWidth + diff && 480 < rightWidth - diff) {
        //两个div的最小宽度均为100px
        leftDom.value.style.width = leftWidth + diff + 'px'
        rightDom.value.style.width = rightWidth - diff + 'px'
      }
    }
    document.onmouseup = function () {
      document.onmousedown = null
      document.onmousemove = null
    }
    return false
  }
}

watch(
  () => props.codeModalVisible,
  (val) => {
    // visible.value = val
    if (val) {
      setTimeout(() => {
        handleMove()
      }, 0)
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.code-modal-wrapper {
  position: absolute;
  z-index: 9999;
  width: 100%;
  // min-height: calc(100vh - 80px);
  display: flex;
  top: 30px;
  left: 0;
  bottom: 24px;
  background-color: #fff;
}
.code-modal-wrapper .lf {
  height: 100%;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  width: 280px;
  padding: 0 20px;
  position: relative;
}
.code-modal-wrapper .lf .touch-div {
  position: absolute;
  top: 0;
  height: 100%;
  left: 100%;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: col-resize;
}
.code-modal-wrapper .lf .touch-div span {
  width: 2px;
  background: #bbb;
  margin: 0 2px;
  height: 15px;
}
.code-modal-wrapper .rt {
  height: 100%;
  overflow-y: auto;
  flex: 1;
  padding: 0 20px;
  z-index: -1;
}
</style>
