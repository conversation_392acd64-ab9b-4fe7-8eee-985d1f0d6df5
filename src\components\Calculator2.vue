<template>
    <div class="calculator" draggable="true" ref="calculatorRef"
    @dragstart="handleDragStart($event)"
    @dragend="handleDragEnd($event)">
        <div class="cal-content">
            <div class="cal-header">
                <span class="title">计算器</span>
                <span class="close">×</span>
            </div>
            <div class="cal-input">
                <input class="inp" v-model="current" ref="content"/>
            </div>
            <div class="equal">=</div>
            <div class="results">
                <span>{{ typeof(current.value) === 'number' ? current : '' }}</span>
            </div>
            <div class="toggle">
                <div class="comput active">计算</div>
                <div class="hisrec">历史记录</div>
            </div>
            <ul class="edit">
                <li class="editbtn" v-for="editbtn in editbtns" @click="editPress">{{ editbtn }}</li>
            </ul>
            <input type="text" class="search">
            <div class="btns">
                <button class="btn" v-for="btn in buttons" @click="press">{{ btn }}</button>
            </div>
        </div>

        <div class="tip" v-if="showTip" :style="{ left: tipLeft + 'px', top: tipTop + 'px' }">
            <span class="item" @click.stop="mark(underline)">下划线</span>
            <span class="item" @click.stop="cancelMark(underline, true)">取消下划线</span>
            <span class="item" @click.stop="markOver(overline)">上划线</span>
            <span class="item" @click.stop="cancelMark(overline, true)">取消上划线</span>
        </div>
        
    </div>
</template>

<script lang="ts" setup>
    import { ref } from 'vue'
    import useMarkText from '@/views/Exam/hooks/useMaskText2';
    const editbtns = ref<any>(['MC', 'MR', 'M+', 'M-', 'AC'])
    const current = ref<any>('')
    const content = ref<any>('')
    const buttons = ref<any>(['2^', 'pi', 'e', 'C', 'Del', 'x^2', 
    '1/x', '|x|', 'exp', 'mod','sqrt', '(', ')', '%', '/', 'x^y', 
    '7', '8', '9', '*', '10^x', '4', '5', '6', '-','log', '1',
    '2', '3', '+', 'ln', '±', '0', '.', '='])
    const {showTip, tipLeft, tipTop, underline, overline, mark, markOver, cancelMark} = useMarkText();
    // 输入框改变字体大小
    const setSize = () => {
        const dom = content.value;
        if (dom.scrollWidth > dom.offsetWidth) {
            dom.style.fontSize = `14px`
        }
    }
    
    // 编辑功能
    const editPress = (event: any) => {
        const key = event.target.textContent;
        if (key === 'MC') {
            current.value = '';
        } else if (key === 'MR') {
            current.value = current.value;
        } else if (key === 'M+') {
            console.log('M+');
        }

    }

    // 计算功能
    const press = (event: any) => {
        setSize();
        const key = event.target.textContent
        if (
            key != '2^' &&
            key != '1/x' &&
            key != '|x|' && 
            key != 'exp' &&
            key != 'mod' &&
            key != '10^x' &&
            key != 'x^y' &&
            key != '=' &&
            key != 'C' &&
            key != '*' &&
            key != '/' &&
            key != 'sqrt' &&
            key != 'x^2' &&
            key != '%' &&
            key != 'Del' &&
            key != '±' &&
            key != 'sin' &&
            key != 'cos' &&
            key != 'tan' &&
            key != 'log' &&
            key != 'ln' &&
            key != 'pi' &&
            key != 'e' 
        ) {
            current.value += key
        } else if (key === '=') {
            if (current.value.indexOf('^') > -1) {
            var base = current.value.slice(0, current.value.indexOf('^'))
            var exponent = current.value.slice(current.value.indexOf('^') + 1)
            current.value = eval('Math.pow(' + base + ',' + exponent + ')')
            } else {
                current.value = eval(current.value)
            }
        } else if (key === 'C') {
            current.value = ''
        } else if (key === '*') {
            current.value += '*'
        } else if (key === '/') {
            current.value += '/'
        } else if (key === '+') {
            current.value += '+'
        } else if (key === '-') {
            current.value += '-'
        } else if (key === '±') {
            if (current.value.charAt(0) === '-') {
            current.value = current.value.slice(1)
            } else {
            current.value = '-' + current.value
            }
        } else if (key === 'Del') {
            current.value = current.value.substring(0, current.value.length - 1)
        } else if (key === '%') {
            current.value = current.value / 100
        } else if (key === '2^') {
            let n = current.value;
            let temp = 1;
            for (let i = 0; i < n; i++) {
                temp = temp * 2;
            }
            current.value = temp;
        } else if (key === 'mod') {
            current.value += '%'
        } else if (key === 'exp') {
            current.value = Math.exp(current.value);
        } else if (key === 'pi') {
            current.value = current.value * Math.PI
        } else if (key === 'x^2') {
            current.value = eval(current.value * current.value + '')
        } else if (key === 'sqrt') {
            current.value = Math.sqrt(current.value)
        } else if (key === 'sin') {
            current.value = Math.sin(current.value)
        } else if (key === 'cos') {
            current.value = Math.cos(current.value)
        } else if (key === 'tan') {
            current.value = Math.tan(current.value)
        } else if (key === 'log') {
            current.value = Math.log10(current.value)
        } else if (key === 'ln') {
            current.value = Math.log(current.value)
        } else if (key === 'x^') {
            current.value += '^'
        } else if (key === 'x!') {
            if (current.value === 0) {
            current.value = '1'
            } else if (current.value < 0) {
            current.value = NaN
            } else {
            var _number = 1
            for (var i = current.value; i > 0; i--) {
                _number *= i
            }
            current.value = _number
            }
        } else if (key === 'e') {
            current.value = Math.exp(current.value)
        }
    }

    // 拖拽
    const calculatorRef = ref<any>(null)
    const offsetX = ref(0)
    const offsetY = ref(0)
    const handleDragStart = (e: any) => {
        offsetX.value = e.offsetX
        offsetY.value = e.offsetY
    }
    const handleDragEnd = (e: any) => {
        let x = e.pageX
        let y = e.pageY
        if (x === 0 && y === 0) return
        x -= offsetX.value
        y -= offsetY.value
        calculatorRef.value.style.left = x + 'px'
        calculatorRef.value.style.top = y + 'px'
    }

    // 关闭计算器
    // const closeAll = () => {
    //     let calculator = document.getElementsByClassName('calculator')[0];
    //     calculator.style.display = 'none';
    // }

</script>

<style lang="less" scoped>
    .active {
        color: #5478EE;
        border-bottom: 2px solid #5478EE;
    }
    .markLine {
        border-bottom: 1px solid #fa8919;
        // background-color: #fa8919;
    }
    .markOverLine {
        border-top: 1px solid #5478EE;
    }
    .calculator {
        position: absolute;
        width: 280px;
        height: 436px;
        background: #333333;
        border-radius: 8px;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.13); 
        overflow: hidden;
        display: block;
        .cal-content {
            position: relative;
            .cal-header {
                height: 16px;
                font-size: 14px;
                font-family: PingFang SC, PingFang SC-5;
                font-weight: 400;
                text-align: left;
                color: #ffffff;
                line-height: 16px;
                .title {
                    position: absolute;
                    top: 16px;
                    left: 16px;
                }
                .close {
                    position: absolute;
                    top: 16px;
                    right: 16px;
                }
            }
            .cal-input {
                .inp {
                    position: absolute;
                    top: 44px;
                    left: 10px;
                    width: 260px;
                    height: 61px;
                    border: 1px solid rgba(255,255,255,0.09);
                    background-color: #333333;
                    border-radius: 4px;
                    color: #d9d9d9;
                    padding: 20px 6px 19px auto;
                    font-size: 24px;
                    text-align: right;
                    outline: none;
                    overflow-x: auto;
                }
            }
            .equal {
                position: absolute;
                top: 102px;
                right: 16px;
                width: 9px;
                height: 22px;
                line-height: 22px;
                font-size: 14px;
                font-family: PingFang SC, PingFang SC-5;
                font-weight: 400;
                text-align: center;
                color: #d9d9d9;
            }
            .results {
                position: absolute;
                top: 128px;
                right: 16px;
                height: 28px;
                font-size: 20px;
                font-family: PingFang SC, PingFang SC-6;
                font-weight: 500;
                text-align: center;
                color: #ffffff;
                line-height: 28px;
                
            }
            .toggle {
                height: 14px;
                font-family: PingFang SC, PingFang SC-5;
                color: #ffffff;
                line-height: 14px;
                .comput {
                    position: absolute;
                    left: 16px;
                    top: 164px;
                    font-size: 10px;
                }
                .hisrec {
                    position: absolute;
                    top: 164px;
                    left: 52px;
                    font-size: 10px;
                }
            }
            .edit {
                position: absolute;
                left: 16px;
                top: 190px;
                .editbtn {
                    float: left;
                    width: 20px;
                    height: 14px;
                    font-size: 10px;
                    font-family: PingFang SC, PingFang SC-5;
                    font-weight: 400;
                    text-align: right;
                    color: #ffffff;
                    line-height: 14px;
                    margin-right: 26px;
                }
            }
            .search {
                position: absolute;
                top: 212px;
                left: 16px;
                width: 248px;
                height: 24px;
                border: 1px solid rgba(255,255,255,0.08);
                background: rgba(255,255,255,0.08);
                border-radius: 4px;
                outline: none;
            }
            .btns {
                position: absolute;
                left: 15px;
                top: 240px;
                .btn {
                    width: 48px;
                    height: 24px;
                    text-align: center;
                    background: linear-gradient(180deg,#2b2b2b 2%, #222222 98%);
                    border: 0.5px solid #141414;
                    border-radius: 2px;
                    color: #ffffff;
                    box-shadow: 1px 1px 3px 0px rgba(0,0,0,0.49), 0px 1px 1px 0px rgba(255,255,255,0.20) inset; 
                    margin: 1px;
                }
            }
        }
        
    }
    .tip {
        position: fixed;
        // width: 120px;
        height: 30px;
        line-height: 30px;
        color: #fff;
        text-align: center;
        background: #484848;
        border-radius: 5px;
        cursor: pointer;
        &::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #484848;
            width: 0px;
            height: 0px;
            top: 100%;
        }
        .item {
            padding: 0 4px;
            font-size: 12px;
        }
    }
</style>