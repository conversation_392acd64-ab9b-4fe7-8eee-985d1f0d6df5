<?xml version="1.0" encoding="UTF-8"?>
<svg width="371px" height="126px" viewBox="0 0 371 126" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>配图 暂无考试</title>
    <defs>
        <linearGradient x1="-47.2424666%" y1="-184.376302%" x2="63.797525%" y2="101.776185%" id="linearGradient-1">
            <stop stop-color="#DEE9E9" offset="56.63%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="97.08%"></stop>
        </linearGradient>
        <linearGradient x1="105.336227%" y1="-111.729318%" x2="51.6507801%" y2="54.6809007%" id="linearGradient-2">
            <stop stop-color="#DEE9E9" offset="62.91%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.99527%" y1="-221.009945%" x2="49.99527%" y2="99.8624001%" id="linearGradient-3">
            <stop stop-color="#DDE9E8" offset="51%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="69.4590029%" y1="-98.6732534%" x2="44.3633283%" y2="103.85835%" id="linearGradient-4">
            <stop stop-color="#DEE9E9" offset="49.63%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-9.27060613%" y1="49.8160256%" x2="63.1255707%" y2="51.203632%" id="linearGradient-5">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#C0D5D5" offset="92.79%"></stop>
        </linearGradient>
        <linearGradient x1="49.9963697%" y1="-1.23376413%" x2="49.9963697%" y2="86.9527986%" id="linearGradient-6">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#B6CCCC" offset="92.79%"></stop>
        </linearGradient>
        <linearGradient x1="70.5952521%" y1="112.674694%" x2="36.2199241%" y2="-0.735667969%" id="linearGradient-7">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#C0D5D5" offset="92.79%"></stop>
        </linearGradient>
        <linearGradient x1="44.9392171%" y1="24.9187742%" x2="70.0986884%" y2="149.559417%" id="linearGradient-8">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#C0D5D5" offset="92.79%"></stop>
        </linearGradient>
        <linearGradient x1="50.000765%" y1="73.0394308%" x2="50.000765%" y2="-17.9113795%" id="linearGradient-9">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#C0D5D5" offset="92.79%"></stop>
        </linearGradient>
        <linearGradient x1="50.0065971%" y1="24.0531563%" x2="50.0065971%" y2="128.820634%" id="linearGradient-10">
            <stop stop-color="#E9F0F1" offset="0.1198948%"></stop>
            <stop stop-color="#C0D5D5" offset="92.79%"></stop>
        </linearGradient>
    </defs>
    <g id="学生系统" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="我的考试-空状态" transform="translate(-1017.000000, -559.000000)" fill-rule="nonzero">
            <g id="配图-暂无考试" transform="translate(1017.000000, 559.200000)">
                <g id="编组-2备份-7" transform="translate(0.000000, 23.520842)">
                    <path d="M73.3,62.4791576 C65.4,58.8791576 56.8,49.2791576 59.2,40.2791576 C60.4,39.9791576 61.7,40.4791576 62.4,41.4791576 C62.4,41.4791576 76.1,62.8791576 72.3,43.1791576 C70.5,33.8791576 67.4,24.9791576 67.1,15.3791576 C66.7,-2.0208424 82.7,-6.8208424 87.3,12.3791576 C87.9,14.8791576 90.2,35.5791576 95.5,33.2791576 C96.2,32.8791576 96.7,32.3791576 97.1,31.6791576 C99.8,27.2791576 100.6,20.7791576 105.5,19.3791576 C109.8,17.9791576 114.1,21.7791576 116.4,25.5791576 C121.1,33.4791576 127.4,52.9791576 117.8,59.5791576 C108,66.0791576 83.7,67.1791576 73.3,62.4791576 L73.3,62.4791576 Z" id="路径" fill="url(#linearGradient-1)"></path>
                    <path d="M296.9,75.9791576 C294,69.2791576 294.1,58.5791576 300.7,54.2791576 C301.6,54.7791576 302.1,55.7791576 301.9,56.8791576 C301.9,56.8791576 298.4,77.8791576 307.1,63.4791576 C311.2,56.6791576 314.3,49.3791576 319.5,43.2791576 C329,32.1791576 341.6,38.1791576 333.7,52.6791576 C332.7,54.5791576 322.5,68.7791576 327.1,70.2791576 C327.7,70.4791576 328.4,70.3791576 329,70.1791576 C333.1,68.9791576 337.3,65.3791576 341.2,67.1791576 C344.6,68.7791576 345.2,73.4791576 344.4,77.1791576 C342.9,84.6791576 335.6,93.0791576 325.9,91.7791576 C316,90.4791576 300.7,84.6791576 296.9,75.9791576 L296.9,75.9791576 Z" id="路径_1_" fill="url(#linearGradient-2)"></path>
                    <path d="M370.5,102.279158 C323.9,70.3791576 258.2,50.3791576 185.2,50.3791576 C112.2,50.3791576 46.6,70.2791576 -5.689893e-15,102.279158 L370.5,102.279158 Z" id="路径_2_" fill="url(#linearGradient-3)"></path>
                    <path d="M293.5,5.9791576 C290.5,9.6791576 288.7,14.9791576 290.4,19.5791576 C291.6,22.7791576 295.3,22.8791576 295,27.3791576 C294.7,29.5791576 294,31.7791576 292.8,33.6791576 C290.7,37.2791576 288.2,40.6791576 285.2,43.6791576 C287.7,45.5791576 288.9,48.6791576 288.5,51.7791576 C288,54.8791576 286.6,57.6791576 284.3,59.8791576 C283.6,60.6791576 282.8,61.2791576 281.8,61.6791576 C280.9,61.8791576 279.9,61.8791576 279,61.6791576 C274.4,60.8791576 269.9,59.4791576 265.8,57.4791576 C263.2,56.2791576 260.5,54.4791576 259.6,51.7791576 C258.6,48.6791576 260.2,47.6791576 262,46.6791576 L262.5,46.3791576 C263.4,45.8791576 264.3,45.2791576 264.8,44.4791576 C266.1,42.3791576 266.1,39.4791576 266.6,37.1791576 C267.3,33.6791576 266.3,27.2791576 270.6,25.9791576 C272.3,25.4791576 274.1,25.5791576 275.8,24.9791576 C279.5,23.7791576 281,20.3791576 282.5,16.8791576 L282.8,16.2791576 C283.6,14.2791576 284.5,12.2791576 285.8,10.7791576 C287.6,8.4791576 292.2,7.4791576 293.5,5.9791576 Z M282.8,26.6791576 C282.8,26.6791576 269.5,42.1791576 269.9,56.4791576 L274.2,56.4791576 C274.2,56.4791576 275.8,39.1791576 282.8,26.6791576 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                </g>
                <g id="编组-21" transform="translate(104.800000, 0.000000)">
                    <polygon id="路径_3_" fill="url(#linearGradient-5)" points="72.6 0 72.1 31 143.1 9.5"></polygon>
                    <polygon id="路径_6_" fill="url(#linearGradient-6)" points="16 12.5 72.6 0.1 72.6 19"></polygon>
                    <polygon id="路径_7_" fill="url(#linearGradient-7)" points="16 12.5 82.8 19.1 82.8 103.6 16 87.4"></polygon>
                    <polygon id="路径_8_" fill="url(#linearGradient-8)" points="82.8 19.1 143.1 9.5 143.1 85.2 82.8 103.6"></polygon>
                    <polygon id="路径_9_" fill="url(#linearGradient-9)" points="16 12.5 1.42108547e-14 39.6 67.4 52.5 82.8 19.1"></polygon>
                    <polygon id="路径_10_" fill="url(#linearGradient-10)" points="82.8 19.1 101.7 52.5 162.5 37.3 143.1 9.5"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>