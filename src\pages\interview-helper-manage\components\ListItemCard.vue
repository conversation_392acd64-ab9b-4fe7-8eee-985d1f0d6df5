<template>
    <div class="list-item-card">
        <div class="card-header">
            <img style="width: 58px;" src="@/assets/images/interview-helper.png" alt="">
            <div class="card-header-mid">
                <SearchHighLight class="title" :title="data?.name" :text="data?.name" :searchText="searchText" />
                <div class="desc">
                    <SearchHighLight :text="data?.teacher_name" :search-text="searchText" />
                    于{{ data?.create_at }}创建
                </div>
            </div>
            <div class="card-header-right" :class="{ 'btn--disabled': !data?.is_self }" @click="handleRemove">
                <svg-icon class="delete-icon" style="width: 16px;height: 18px;" name="delete-close"></svg-icon>
                <svg-icon class="delete-open-icon" style="width: 16px;height: 20px;" name="delete-open"></svg-icon>
            </div>
        </div>
        <div class="card-content">
            <span :title="data?.select_category_tiling_name?.join('、')">
                {{ isEmpty ? '' : data?.select_category_tiling_name?.join('、') }}
            </span>
        </div>
        <div class="card-footer">
            <div class="footer-btn" @click="handleRun(data.id)">
                <svg-icon class="footer-btn-icon" name="play" style="width: 16px;height: 16px;"></svg-icon>
                启动
            </div>
            <div class="footer-btn" @click="handleEdit(data.id)">
                <svg-icon class="footer-btn-icon" name="edit" style="width: 16px;height: 16px;"></svg-icon>
                编辑
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { aiinterviewassistantcontent, beforemodifyaiinterviewassistant, delaiinterviewassistant, queryaiinterviewassistantrecord } from '@/api/interview';
import SearchHighLight from '@/components/SearchHighLight.vue';
import { MyConfirm, MyInfoModal } from '@/utils/antdUtil';
import { message, Modal } from 'ant-design-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router'

const props = defineProps<{
    data: any
    searchText: string
}>()

const emits = defineEmits<{
    (e: 'removedOk'): void
}>()

const router = useRouter()

const isEmpty = ref(false)
async function handleRun(id: string) {
    let res = await aiinterviewassistantcontent({ id })
    if (!res.select_category_tiling?.length) {
        await MyInfoModal({
            title: '提示',
            content: '因全部题库被删除，面试助手无法使用'
        })
        isEmpty.value = true
    } else {
        window.open(`/#/interview/interview-helper-runner?id=${id}`, '_blank')
    }
}

async function handleEdit(id: string) {
    let res = await beforemodifyaiinterviewassistant({ id })
    if (res === 'updated') {
        await MyInfoModal({
            title: '面试助手变更提醒',
            content: '因部分题库被删除，面试助手内容已自动更新'
        })
    }
    router.push({
        path: '/admin/interview/interview-helper-form',
        query: { id, mode: 'edit' }
    })
}

function handleRemove() {
    if (!props.data.is_self) {
        message.info('很抱歉，您不是此面试助手的创建人，所以无法进行删除操作')
        return
    }
    Modal.confirm({
        title: '确定删除该面试助手？',
        async onOk() {
            try {
                await delaiinterviewassistant({ id: props.data.id })
                message.success('删除成功！')
                emits('removedOk')
            } catch (error) {

            }
        }
    })
}

</script>

<style lang="less" scoped>
.list-item-card {
    padding: 24px;
    height: 214px;
    display: flex;
    flex-direction: column;

    .card-header {
        display: flex;
        justify-content: space-between;

        .card-header-mid {
            flex: 1;
            padding: 0 14px 0 16px;
            min-width: 0;

            .title {
                display: inline-block;
                width: 100%;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                font-weight: bold;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .desc {
                margin-top: 8px;
                font-size: 12px;
                line-height: 18px;
                color: rgba(0, 0, 0, 0.45);
            }
        }

        .card-header-right {
            height: 20px;
            display: flex;
            align-items: flex-end;
            cursor: pointer;

            .delete-icon {
                display: block;
            }

            .delete-open-icon {
                display: none;
            }

            &:hover:not(.btn--disabled) {
                .delete-icon {
                    display: none;
                }

                .delete-open-icon {
                    display: block;
                }
            }
        }
    }

    .card-content {
        flex: 1;
        margin-top: 16px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 12px;
        color: #181818;
        line-height: 18px;
    }

    .card-footer {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        gap: 24px;

        .footer-btn {
            flex: 1;
            border-radius: 20px;
            height: 40px;
            font-size: 14px;
            line-height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            background: #f1f4fe;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.65);
            transition: all ease .2s;

            &:hover {
                color: #fff;
                background-color: #5478ee;

                .footer-btn-icon {
                    color: #fff;
                }
            }

            .footer-btn-icon {
                transition: all ease .2s;
                color: #5478ee;
            }
        }
    }
}

.btn--disabled {
    cursor: not-allowed !important;
    color: gray;
}
</style>