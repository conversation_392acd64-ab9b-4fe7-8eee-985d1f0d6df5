<template>
  <div ref="myRef" :style="{ width, height }" class="pie-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Object,
    default: () => {}
  },
  name: {
    type: String,
    default: '交卷情况'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 20)
})

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  setChartOption()
  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}
const setChartOption = () => {
  Chart.value.setOption({
    title: {
      text: props.name,
      left: 'center',
      textStyle: {
        fontSize: 15,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    // toolbox: {
    //   feature: {
    //     dataView: { show: true, readOnly: false },
    //     magicType: { show: true, type: ['line', 'bar'] },
    //     restore: { show: true },
    //     saveAsImage: { show: true }
    //   }
    // },
    legend: {
      data: ['每分钟交卷人次', '总交卷数量'],
      left: 'left'
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData.value,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '每分钟交卷人次',
        min: minSubmit.value,
        max: maxSubmit.value, // 这里要取这一项的最大值
        interval: Math.round(maxSubmit.value / 10), // 这个值= math.round(max/10)
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '总交卷数量',
        min: minSum.value,
        max: maxSum.value,
        interval: Math.round(maxSum.value / 10), // 这个值= math.round(max/10)
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0
      },
      {
        start: 0
      }
    ],
    series: [
      // 这里一定注意这两组数据的顺序，与yAxis其实是反的
      {
        name: '总交卷数量',
        type: 'line',
        yAxisIndex: 1,
        data: sumData.value
      },
      {
        name: '每分钟交卷人次',
        type: 'bar',
        data: chartData.value.submissionsByMinute
      }
    ]
  })
}

const chartData = ref<any>([])
const xAxisData = ref<any>([])
const minSubmit = ref(0)
const maxSubmit = ref(0)
const minSum = ref(0)
const maxSum = ref(0)
const sumData = ref<any>([])
watch(
  () => props.data,
  (val) => {
    if (Object.keys(val).length) {
      chartData.value = val
      xAxisData.value.length = 0
      for (let i = 1; i <= chartData.value.durationMinutes; i++) {
        xAxisData.value.push(i)
      }
      minSubmit.value = Math.min(...chartData.value.submissionsByMinute)
      maxSubmit.value = Math.max(...chartData.value.submissionsByMinute)
      minSum.value = minSubmit.value
      maxSum.value = chartData.value.submissionsByMinute.reduce(
        (prev: number, current: number) => prev + current
      )

      let sum = 0
      chartData.value.submissionsByMinute.forEach((item: number) => {
        sum += item
        sumData.value.push(sum)
      })
      setTimeout(() => {
        setChartOption()
      }, 200)
    }
  },
  {
    immediate: true
  }
)

watch(
  () => props.width,
  (val) => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  (val) => {
    Chart.value.resize()
  }
)
</script>

<style lang="less" scoped>
.pie-chart {
  padding: 16px;
  background: #fff;
}
</style>
