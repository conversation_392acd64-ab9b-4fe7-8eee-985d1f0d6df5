import { setupRouter } from '@/router'
import { 
  otherSetup, 
  setupAntd, 
  setupAppConfig, 
  setupGlobalComponent,
  setupHighlight,
  setupI18n,
  setupKatex,
  setupPrint,
  setupStyle,
  setupVueGridLayout,
  setupVuePrototype,
  setupVueViewer,
} from '@/setup'
import store from '@/store'

import { createApp } from 'vue'
import App from './App.vue'

async function setupApp() {
  const app = createApp(App)

  setupAppConfig(app)
  setupStyle(app)
  setupGlobalComponent(app)
  setupPrint(app)
  setupHighlight(app)
  setupVueViewer(app)
  setupVueGridLayout(app)
  setupKatex(app)
  setupAntd(app)
  setupI18n(app)
  setupVuePrototype(app)
  setupStyle(app)
  await setupRouter(app)

  // 挂载 vuex
  app.use(store)

  // 其他配置(项目上原来加的关闭加载弹框、移除Vue的warning)
  otherSetup(app)

  app.mount('#app')
}

setupApp()
