<template>
    <div>
        <div v-if="list?.length === 0" class="common-no-data">
            <span class="desc">无子题库</span>
        </div>
        <a-spin v-else :spinning="loading">
            <div ref="chartRef" style="height: 100%;"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, watch } from 'vue'
import { quescatgproportion } from '@/api/admin/statisticAnalysis'
import { ECharts, init } from 'echarts'
import type { ZRColor } from 'echarts/types/src/util/types'
import { QUESTIONS } from '@/config/constants'

const props = defineProps<{
    id?: string
}>()

// 调色板
const colorList: ZRColor[] = QUESTIONS.map((item) => ({
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color: item.color
    }, {
        offset: 1, color: item.color + '80'
    }],
    global: false
}))

// 获取数据
const list = ref<{
    name: string
    value: number
}[]>()
const total = ref(0)

const loading = ref(false)
async function getList() {
    if (!props.id) return
    loading.value = true
    try {
        let res = await quescatgproportion({ categoryId: props.id }) as any
        total.value = res.reduce((a: number, b: any) => a + b.value, 0)
        list.value = res.filter((item: any) => item.value > 0)
    } catch {
        total.value = 0
        list.value = []
    } finally {
        loading.value = false
    }
}

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
    // 检查图表容器是否存在
    if (!chartRef.value) return;

    // 初始化图表实例
    chart.value = init(chartRef.value);

    // 设置图表选项
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}题` // 格式化提示框中的值
        },
        grid: {
            left: 0,
            right: 0,
            top: 0 // 设置图表的网格边距
        },
        color: colorList, // 使用预定义的调色板
        series: [
            {
                type: 'pie', // 设置图表类型为饼图
                radius: ['63%', '70%'], // 设置饼图的内外半径
                minAngle: 10, // 设置最小角度，避免数据过小不可见
                label: {
                    formatter: '{b}: {d}%', // 格式化标签显示内容
                    alignTo: 'labelLine', // 标签对齐到引导线
                    bleedMargin: 0, // 防止标签溢出
                    distanceToLabelLine: 5, // 标签与引导线的距离
                },
                labelLine: {
                    length: 5, // 引导线的第一段长度
                    length2: 20, // 引导线的第二段长度
                },
                itemStyle: {
                    borderWidth: 2, // 设置分隔线宽度
                    borderRadius: '50%', // 设置分隔线圆角
                    borderColor: '#F5F5F5', // 设置分隔线颜色
                },
                data: list.value // 设置饼图数据
            }
        ]
    });

    // 设置图表中心文本
    setCenterText();

    // 添加窗口大小变化监听器以调整图表大小
    window.addEventListener('resize', resizeChart);
}

function setCenterText() {
    // 设置图表中心的文本内容
    chart.value?.setOption({
        graphic: {
            elements: [
                {
                    type: 'text',
                    left: 'center',
                    top: '110px',
                    style: {
                        text: total.value, // 显示总题量
                        fill: 'rgba(0,0,0,0.85)', // 文字颜色
                        fontSize: 36, // 文字大小
                        fontWeight: 'bold' // 文字加粗
                    },
                },
                {
                    type: 'text',
                    left: 'center',
                    top: '140px',
                    style: {
                        text: '总题量', // 显示描述文字
                        fill: 'rgba(0,0,0,0.45)', // 文字颜色
                        fontSize: 14, // 文字大小
                        lineHeight: 40, // 行高
                    },
                },
            ],
        },
    });
}

function resizeChart() {
    // 调整图表大小
    chart.value?.resize();
    // 重新设置中心文本
    setCenterText();
}

watch(() => props.id, async (val) => {
    if (!val) return
    await getList()
    draw()
}, { immediate: true })

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}

.common-no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    .desc {
        position: relative;
        top: 60px;
        color: rgba(0, 0, 0, 0.3);
        font-size: 16px;
        user-select: none;
    }
}
.ant-spin-container > div {
    // overflow: clip;
    display: flex;
    justify-content: center;
}
</style>