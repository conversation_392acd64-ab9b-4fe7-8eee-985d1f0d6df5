<template>
  <a-modal
    class="paper-config"
    :visible="props.visible"
    :title="`《${data?.name}》考试配置`"
    :footer="null"
    width="640px"
    centered
    destroyOnClose
    :maskClosable="false"
    @cancel="handleClose">
    <div class="card">
      <div class="title">考试前</div>
      <div class="content">
        <div class="item" :style="{ width: data.examnotes ? '100%' : '50%'}">
          <div class="label">考试须知</div>
          <div class="value" v-if="data.examnotes">自定义</div>
          <div class="value" v-else>默认</div>
        </div>
        <div class="item">
          <div class="label">开启手机监控</div>
          <div class="value">{{ data?.phonemonitor === 1 ? '是' : '否' }}</div>
        </div>
        <div class="item">
          <div class="label">开启电脑监控</div>
          <div class="value">{{ data?.computermonitor === 1 ? '是' : '否' }}</div>
        </div>
        <div class="item" v-if="data.uniexam === '1'">
          <div class="label">
            限时迟到（分钟）
            <a-tooltip placement="right" overlayClassName="light">
              <template #title>
                <span>开考后，迟于设定的时间，不允许考生入场，考试中退出的考生不受此影响。留空或0表示不限制</span>
              </template>
              <svg-icon name="info2" class="common-info-icon" />
            </a-tooltip>
          </div>
          <div class="value">{{ data?.limitlateness ?? '不限时间' }}</div>
        </div>
        <div class="item" style="width: 100%;">
          <div class="label">宣传视频（mp4格式）</div>
          <div class="value">
            <video v-if="data?.examvideo" controls width="200" height="150" :src="data.examvideo_url"></video>
            <span v-else>无</span>
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="title">考试中</div>
      <div class="content">
        <div class="item">
          <div class="label">题目顺序</div>
          <div class="value">{{ data.quesorder ? '固定' : '随机' }}</div>
        </div>
        <div class="item">
          <div class="label">选项顺序</div>
          <div class="value">{{ data.opsorder ? '固定' : '随机' }}</div>
        </div>
        <div class="item">
          <div class="label">题目展示</div>
          <div class="value">{{ ['整卷', '逐题'][data.showType] }}</div>
        </div>
        <div class="item" v-if="data.showType === '1'">
          <div class="label">返回修改</div>
          <div class="value">{{ data.can_back ? '允许' : '禁止' }}</div>
        </div>
        <div class="item">
          <div class="label">计算器</div>
          <div class="value">{{ data.calculator ? '有' : '无' }}</div>
        </div>
        <div class="item">
          <div class="label">拍照上传答案</div>
          <div class="value">{{ data.uploadanswer ? '允许' : '禁止' }}</div>
        </div>
        <div class="item">
          <div class="label">考试时间（分钟）</div>
          <div class="value">{{ data.duration }}</div>
        </div>
        <div class="item">
          <div class="label">最短交卷时间（分钟）</div>
          <div class="value">{{ data.presubmit ? data.presubmit : '不限时间' }}</div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="title">反作弊</div>
      <div class="content">
        <div class="text" v-if="data.neterror"><span style="color: #D71310">允许</span>学生最多因网络原因离开考试<span style="color: #D71310">{{ data.neterror }}</span>次</div>
        <div class="text" v-else>允许学生因网络原因离开考试次数不限</div>
        <div class="text" v-if="data.cutscreen"><span style="color: #D71310">允许</span>学生最多切屏<span style="color: #D71310">{{ data.cutscreen }}</span>次，超出次数后强制交卷</div>
        <div class="text" v-else>允许学生切屏次数不限</div>
        <div class="text">考生作答页面，<span style="color: #D71310">{{ data.watermark ? '' : '不要求' }}使用</span>考生姓名和考生编号作为背景页面</div>
        <div class="text">考试期间，<span style="color: #D71310">{{ data.nocopy ? '禁止' : '允许' }}</span>考生对试卷内容进行复制粘贴，且<span style="color: #D71310">{{ data.nocopy ? '不得' : '允许' }}</span>粘贴外部内容</div>
      </div>
    </div>
    <div class="card">
      <div class="title">其他</div>
      <div class="content">
        <div class="text">阅卷<span style="color: #D71310">{{ data.showname ? '显示' : '隐藏' }}</span>考生真实姓名</div>
        <div class="text">本部门教师<span style="color: #D71310">{{ data.shareToDept ? '' : '不' }}可</span>编辑</div>
      </div>
    </div>
    <div class="footer">
      <a-button type="primary" @click="handleClose">关闭</a-button>
    </div>
  </a-modal>

</template>

<script lang="ts" setup>
import { ref } from 'vue'
import AEditor from '@/components/Editor/index.vue'

const props = defineProps<{
  visible: boolean
  data: any
}>()
const emits = defineEmits(['close'])

const handleClose = () => emits('close')
</script>

<style lang="less" scoped>
.paper-config {
  .card {
    margin-bottom: 32px;
    .title {
      height: 20px;
      font-size: 14px;
      font-weight: 600;
      color: rgba(0,0,0,0.85);
      line-height: 20px;
    }
    .content {
      padding-top: 4px;
      display: flex;
      flex-wrap: wrap;
      .item {
        flex: none;
        width: 50%;
        margin-top: 12px;
        line-height: 18px;
        display: flex;
        align-items: flex-start;
        .label {
          flex: none;
          width: 142px;
          height: 18px;
          color: #626262;
          font-size: 12px;
          line-height: 18px;
          display: flex;
          align-items: center;
        }
        .value {
          font-size: 12px;
          text-align: left;
          color: rgba(0,0,0,0.85);
          line-height: 18px;
        }
      }
      .text {
        flex: none;
        width: 100%;
        height: 18px;
        margin-top: 12px;
        font-size: 12px;
        text-align: left;
        color: rgba(0,0,0,0.85);
        line-height: 18px;
      }
    }
  }
  .footer {
    height: 32px;
    display: flex;
    justify-content: center;
  }
}
</style>
