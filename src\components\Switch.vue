<template>
    <div class="j-switch">
        <span class="slider" :style="{ width: sliderWidth + 'px', left: sliderLeft + 'px' }"></span>
        <div class="switch_items_wrapper">
            <span class="switch_item" v-for="(item, index) in columns" :key="index" ref="switchItemRef"
                @click="handleClick($event, item, index)" :class="{ active: index === activeIndex }">
                {{ item.label }}
            </span>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';

type column = {
    label: string
    value: any
}

const props = defineProps<{
    modelValue?: any
    columns: column[]
}>()

const emits = defineEmits<{
    (e: 'update:modelValue', value: any): void
    (e: 'change', item: column): void
}>()

watch(() => props.modelValue, render)

// 初始化
const activeIndex = ref(0)
const switchItemRef = ref()
const sliderWidth = ref(0)
const sliderLeft = ref(0)
function render() {
    if (props.modelValue !== undefined) {
        activeIndex.value = props.columns.findIndex(i => i.value === props.modelValue)
    }
    // console.log(switchItemRef.value)
    // 获取active的offsetWidth和offsetLeft
    let { offsetWidth, offsetLeft } = switchItemRef.value[activeIndex.value]
    sliderWidth.value = offsetWidth
    sliderLeft.value = offsetLeft
}

/** 点击事件 */
function handleClick(e: any, item: column, index: number) {
    sliderWidth.value = e.target.offsetWidth
    sliderLeft.value = e.target.offsetLeft
    emits('change', item)
    emits('update:modelValue', item.value)
    activeIndex.value = index
}

onMounted(() => {
    render()
})
</script>
  
<style lang="less" scoped>
@height: 32px;

.j-switch {
    position: relative;
    border-radius: 10px;

    .slider {
        position: absolute;
        top: 0;
        height: @height;
        background-color: #F5F5F5;
        border-radius: 8px;
        z-index: 99;
        transition: all ease 0.2s;
    }

    .switch_items_wrapper {
        position: relative;
        z-index: 999;

        .switch_item {
            cursor: pointer;
            color: #181818;
            padding: 0 16px;
            display: inline-block;
            border-radius: 10px;
            height: @height;
            line-height: @height;
            position: relative;
            transition: all ease 0.2s;

            &.active {
                font-weight: bold;
                color: #000;
            }
        }
    }
}
</style>