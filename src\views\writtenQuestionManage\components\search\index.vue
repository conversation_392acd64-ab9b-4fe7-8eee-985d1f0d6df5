<template>
  <div class="px-5">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="w-full flex">
        <div class="flex-auto flex flex-wrap justify-between">
          <slot name="fixedSearch"></slot>
        </div>
        <div class="flex-none w-27 h-8 ml-2 rounded-lg border border-black/15 flex justify-center items-center cursor-pointer" @click="showmore = !showmore">
          <a-badge
            v-if="badgeCount"
            :count="badgeCount"
            class="mr-1"
            :number-style="{
              backgroundColor: '#FF4D4F',
              color: '#fff',
              minWidth: '16px',
              width: '16px',
              height: '16px',
              padding: '0',
              lineHeight: '16px'
            }" />
          <img class="w-4 h-4 mr-1" src="@/assets/images/paper/filter.png" alt="" v-else>
          <div>更多条件</div>
        </div>
      </div>
      <div class="w-full flex mt-4" v-show="showmore">
        <div class="flex-auto flex flex-wrap">
          <slot name="moreSearch"></slot>
        </div>
        <div class="flex-none w-27 h-8 ml-2 rounded-lg border border-black/15 flex justify-center items-center cursor-pointer" @click="handleReset">
          <img class="w-4 h-4 mr-1" src="@/assets/images/paper/clear.png" alt="">
          <div>清空条件</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  badgeCount: number
}>()

const emits = defineEmits(['handleReset'])

const showmore = ref(false) // 显示更多筛选条件

const handleReset = () => emits('handleReset')

</script>

<style scoped>
/* 自定义 Ant Design Badge 样式 */
:deep(.ant-badge-count) {
  padding: 0 !important;
  line-height: 16px !important;
}

:deep(.ant-scroll-number-only) {
  height: 16px !important;
}

:deep(.ant-scroll-number-only-unit) {
  height: 16px !important;
  font-size: 10px !important;
  line-height: 16px !important;
}
</style>
