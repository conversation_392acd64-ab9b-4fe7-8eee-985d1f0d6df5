<template>
  <div ref="myRef" :style="{ width, height }" class="pie-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: '题库题目难度分布'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 20)
})

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  setChartOption()
  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}
const setChartOption = () => {
  Chart.value.setOption({
    title: {
      text: props.name,
      left: 'center',
      textStyle: {
        fontSize: 15,
        fontWeight: 600
      }
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
      }
    },
    legend: {
      top: '5%'
    },
    grid: {
      left: '1%',
      right: '7%',
      bottom: '2%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '占比%',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: chartData.value.subjects // 根据数据填充
    },
    series: [
      {
        name: '简单',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
          formatter: '{c}%'
        },
        emphasis: {
          focus: 'series'
        },
        data: chartData.value.easy // 根据数据填充
      },
      {
        name: '一般',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
          formatter: '{c}%'
        },
        emphasis: {
          focus: 'series'
        },
        data: chartData.value.medium // 根据数据填充
      },
      {
        name: '困难',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
          formatter: '{c}%'
        },
        emphasis: {
          focus: 'series'
        },
        data: chartData.value.hard // 根据数据填充
      },
      {
        name: '未统计',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
          formatter: '{c}%'
        },
        emphasis: {
          focus: 'series'
        },
        data: chartData.value.notAnalyzed // 根据数据填充
      }
    ]
  })
}

const chartData = ref<any>([])
watch(
  () => props.data,
  (val) => {
    chartData.value = val
    setTimeout(() => {
      setChartOption()
    }, 200)
  },
  {
    immediate: true
  }
)

watch(
  () => props.width,
  () => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  () => {
    nextTick(() => {
      Chart.value && Chart.value.resize()
    })
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.pie-chart {
  padding: 16px;
  background: #fff;
}
</style>
