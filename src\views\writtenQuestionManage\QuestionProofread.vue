<template>
    <a-spin :spinning="loading">
        <div class="question-proofread">
            <div class="question-content">
                <p class="sub-title">
                    <span>题目基本信息</span>
                </p>
                <QuestionItemDispaly v-if="currentData" :question-detail="currentData" show-score show-type show-tag
                    show-correct-answer show-question-difficulty show-question-points-and-basis
                    :option-letter-type="'text'" showDisabledKws />
                <div style="display: flex;align-items: center;margin-top: 16px;" v-if="currentData?.type !== QuestionEnum['算法题']">
                    <a-button :loading="getAiAnswerLoading" :disabled="aiAnswerDisabled" class="common-ai-button"
                        style="font-size: 14px; border-radius: 8px;padding: 0 8px; height: 32px;"
                        @click="handleGetAiAnswer">
                        <template #icon><img src="@/assets/icons/svg/ai.svg"
                                style="margin-right: 4px;width: 14px;" /></template>
                        辅助校对
                    </a-button>
                    <a-tooltip placement="right" overlayClassName="light">
                        <template #title>
                            <span>自动生成当前题目的答案解析，每道题仅可使用一次</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                    </a-tooltip>
                </div>
                <template v-if="aiAnswer || aiExplain || (aiResult && aiResult.length)">
                    <template v-for="({answer: aiAnswer, explain:aiExplain}) in aiResult" :key="aiAnswer">
                    <div class="ai-answer-panel">
                        <div>
                            <div class="label">AI 答案</div>
                            <div class="value">{{ aiAnswer }}</div>
                        </div>
                        <div style="margin-top: 8px;">
                            <div class="label">答题解析</div>
                            <div class="value">
                                <FoldText 
                                    :text="aiExplain" 
                                    :fold-line-clamp="3" 
                                    background-color="#f5f5f5"
                                    :feedback-params="{
                                        apiname: 'getaianswer',
                                        input: getaianswerInput,
                                        output: {
                                            answer: aiAnswer,
                                            explain: aiExplain
                                        }
                                    }"
                                ></FoldText>
                            </div>
                        </div>
                    </div>
                    <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
                        <template #icon><ExclamationCircleFilled /></template>
                        <template #message>
                            温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
                        </template>
                    </a-alert>
                    </template>
                </template>
            </div>
            <div class="proofread-content">
                <div class="proofread-content-top">
                    <a-button type="primary" style="font-size: 14px;border-radius: 8px;margin-bottom: 24px;"
                        @click="questionEditFormVisible = true">编辑题目</a-button>
                    <p class="sub-title">题目其他信息</p>
                    <ul class="other-info" v-if="currentData">
                        <li>
                            <span class="label">所属题库</span>
                            <div class="value">{{ currentData.categoryName }}</div>
                        </li>
                        <li v-if="![QuestionEnum['单选题'], QuestionEnum['判断题']].includes(currentData.type)">
                            <span class="label">题目配置</span>
                            <div class="value">
                                <p v-if="[QuestionEnum['多选题'], QuestionEnum['排序题']].includes(currentData.type)">
                                    <a-checkbox v-model:checked="currentData.sepscore" :disabled="true" />
                                    <span>本题适用于“部分回答正确时可得分”</span>
                                    <a-tooltip placement="right">
                                        <template #title>
                                            <span
                                                v-if="currentData.type === QuestionEnum['多选题']">当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span>
                                            <span
                                                v-if="currentData.type === QuestionEnum['排序题']">当考试设置为“部分回答正确时可得分”，并且本题勾选了此选项，则学生的答案中包含连续正确的顺序不少于2个时，可按照比例得分；否则，在没有完全回答正确的情况下不得分。</span>
                                        </template>
                                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                                    </a-tooltip>
                                </p>
                                <p v-if="[QuestionEnum['填空题']].includes(currentData.type)">
                                    <a-checkbox v-model:checked="currentData.ordered" :disabled="true" />
                                    <span>判分时区分答案先后顺序</span>
                                </p>
                                <p v-if="[QuestionEnum['填空题'], QuestionEnum['问答题']].includes(currentData.type)">
                                    <a-checkbox v-model:checked="currentData.ignorecase" :disabled="true" />
                                    <span>忽略大小写</span>
                                </p>
                                <p v-if="[QuestionEnum['算法题']].includes(currentData.type)">
                                    <a-checkbox v-model:checked="currentData.sepscore" :disabled="true" />
                                    <span>本题适用于“部分测试用例通过可得分”</span>
                                    <a-tooltip placement="right">
                                        <template #title>
                                            <span>若勾选“部分测试用例通过可得分”，则算法题得分按通过测试用例占总数的百分比计算；未勾选时，需全部通过测试用例才能得分。</span>
                                        </template>
                                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                                    </a-tooltip>
                                </p>
                                <p v-if="[QuestionEnum['算法题']].includes(currentData.type)">
                                    <a-checkbox :checked="hasDisablekws" :disabled="true" />
                                    <span>存在禁用关键词</span>
                                    <a-tooltip placement="right">
                                        <template #title>
                                            <span>禁止学生答题时使用一些系统内置的方法或关键字</span>
                                        </template>
                                        <svg-icon class="common-info-icon" name="info2" style="margin-left: 8px;"></svg-icon>
                                    </a-tooltip>
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="proofread-content-main">
                    <p class="sub-title">校对结果</p>
                    <div style="margin-bottom: 16px;">
                        <a-checkbox v-model:checked="autoSwitchToNext">
                            <span style="font-size: 14px;">校对正确后自动切换下一题</span>
                        </a-checkbox>
                    </div>
                    <a-radio-group v-model:value="ruleForm.proofreading" class="operation-container">
                        <div class="operation-panel" :class="{ active: ruleForm.proofreading === 1 }"
                            @click="handleProofread(1)">
                            <Vue3Lottie ref="proofreadOkRef" :animationData="ProofreadOk" :auto-play="false" :loop="false"
                                :height="40" :width="40" :no-margin="true"></Vue3Lottie>
                            <span>校对正确</span>
                            <a-radio :value="1"></a-radio>
                        </div>
                        <div class="operation-panel" :class="{ active: ruleForm.proofreading === 2 }"
                            @click="handleProofread(2)">
                            <Vue3Lottie ref="proofreadNoRef" :animationData="ProofreadNo" :auto-play="false" :loop="false"
                                :height="40" :width="40" :no-margin="true"></Vue3Lottie>
                            <span>校对错误</span>
                            <a-radio :value="2"></a-radio>
                        </div>
                    </a-radio-group>
                    <p class="proofread-record" v-if="ruleForm.proofreading">
                        <img v-if="ruleForm.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
                        <img v-if="ruleForm.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
                        <span style="margin-left: 4px;">由{{ ruleForm.teacherName }}于{{
                            ruleForm.updateTime }}{{ ruleForm.teacherName === 'AI' ? '判断' : '确认' }}该题</span>
                        <span style="color: #38910b;font-weight: bold;" v-if="ruleForm.proofreading === 1">正确</span>
                        <span style="color: #dc2b28;font-weight: bold;" v-if="ruleForm.proofreading === 2">错误</span>
                        <img style="margin-left: 10px; cursor: pointer;" src="@/assets/images/svg/delete_black.svg" alt=""
                            @click="handleRecordDelete">
                    </p>
                    <div class="ai-answer-panel" style="width: 100%" v-if="ruleForm.proofreading === 1 && ruleForm.ai && ruleForm.detail">{{
                        ruleForm.detail }}</div>
                    <div class="proofread-reason" v-if="ruleForm.proofreading === 2">
                        <span class="label">错误原因</span>
                        <a-textarea v-model:value="ruleForm.wrongReason" placeholder="请输入错误原因" :rows="4"
                            @change="prepareSubmit" :maxlength="400" showCount />
                    </div>
                </div>
                <div class="footer">
                    <a-button :disabled="nextBtnDisabled" type="primary" @click="getSiblingQuestion('next')">下一题</a-button>
                    <a-button :disabled="lastBtnDisabled" @click="getSiblingQuestion('last')">上一题</a-button>
                    <a-button @click="emits('close')">退出校对</a-button>
                    <span class="text-info">{{ loadText }}</span>
                </div>
            </div>
        </div>
    </a-spin>
    <a-modal title="编辑题目" v-model:visible="questionEditFormVisible" wrapClassName="full-screen-modal" width="100%"
        :maskClosable="false" :closable="false" :keyboard="false" :footer="null">
        <QuestionEditForm v-if="questionEditFormVisible" :qid="qid" @close="onEditFormClose"></QuestionEditForm>
    </a-modal>
</template>
<script lang="ts" setup>
import { computed, ref, watch, watchEffect } from 'vue'
import QuestionItemDispaly from './QuestionItemDisplay/index.vue'
import { quescontent } from '@/api/admin/questionManage'
import { QuestionEnum } from '@/models/questionModel'
import { Vue3Lottie } from 'vue3-lottie'
import ProofreadOk from '@/assets/animation/proofread_ok.json'
import ProofreadNo from '@/assets/animation/proofread_no.json'
import { getAiAnswer, getaianswerrecord, proofreadingrecords , proofreadingrecordsSave} from '@/api/exam/index'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import QuestionEditForm from './editQuestion.vue'
import moment from 'moment'
import { useDebounceFn } from '@vueuse/core'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import FoldText from '@/components/FoldText.vue'

const props = withDefaults(defineProps<{
    qid: string
    getSiblingQuestionFn: (direction: 'last' | 'next') => Promise<any>
    lastBtnDisabled?: boolean
    nextBtnDisabled?: boolean
}>(), {
    lastBtnDisabled: false,
    nextBtnDisabled: false
})

const emits = defineEmits<{
    (e: 'close'): void
    (e: 'update:qid', qid: string): void
}>()

const store = useStore()

// 获取题目信息
const currentData = ref()
async function getDetail(id: string) {
    const data = await quescontent({ questionBankUUID: id })
    data.category = data.categoryUUID
    data.tags = data.tagname = data.tagList
    convertFieldJsonContent(data)
    currentData.value = data
}

// 获取校对记录
async function getRecord(id: string) {
    let res = await proofreadingrecords({
        quesUUID:id
    }) as any
    if (res) {
        res.teacherName = res.ai ? 'AI' : res.teacherName
        ruleForm.value = res
    } else {
        resetForm()
    }
}
const loading = ref(false)
function init() {
    if (props.qid) {
        loading.value = true
        Promise.all([getDetail(props.qid), getRecord(props.qid), getAiAnswerRecord(props.qid)]).finally(() => {
            loading.value = false
        })
    }
}

// 是否含有禁用关键词（算法题）
const hasDisablekws = computed(() => Object.values(currentData.value.disablekws || {}).flat().length)

// 表单部分
const ruleForm = ref<{
    proofreading: 0 | 1 | 2
    wrongReason: string
    updateTime?: string
    teacherName?: string
    /** 是否是ai校对 */
    ai?: boolean
    /** ai校对正确/错误的理由 */
    detail?: string
    ques?: string
}>({
    proofreading: 0,
    wrongReason: ''
})
function resetForm() {
    ruleForm.value.proofreading = 0
    ruleForm.value.wrongReason = ''
}

const proofreadOkRef = ref<InstanceType<typeof Vue3Lottie>>()
const proofreadNoRef = ref<InstanceType<typeof Vue3Lottie>>()
function handleProofread(proofreading: 1 | 2) {
    ruleForm.value.proofreading = ruleForm.value.proofreading === proofreading ? 0 : proofreading
    ruleForm.value.teacherName = store.getters.userInfo.username
    ruleForm.value.teacherUUID = store.getters.userInfo.teacherUUID
    ruleForm.value.updateTime = moment().format('YYYY-MM-DD HH:mm:ss')
    prepareSubmit()
}
watch(() => ruleForm.value.proofreading, (val) => {
    if (val === 1) {
        proofreadOkRef.value?.goToAndPlay(0, true)
        proofreadNoRef.value?.goToAndStop(0, true)
    } else if (val === 2) {
        proofreadOkRef.value?.goToAndStop(0, true)
        proofreadNoRef.value?.goToAndPlay(0, true)
    } else {
        proofreadOkRef.value?.goToAndStop(0, true)
        proofreadNoRef.value?.goToAndStop(0, true)
    }
}, { immediate: true })

// 提交校对
// 输入时候文字一直是保存中
// 1秒之后（提交防抖）才真正调用保存接口
// 保存成功才会显示"已保存校对结果"，后面不会消失，除非切换题目
const loadText = ref('')
async function submit(params: any) {
    try {
        await proofreadingrecordsSave(params)
        if (loadText.value === '保存中...') {
            loadText.value = '已保存校对结果'
        }
        // 校对正确后自动切换下一题
        if (autoSwitchToNext.value && params.proofreading === 1) {
            getSiblingQuestion('next')
        } else if(!autoSwitchToNext.value){
            // 重新获取校验信息
            getRecord(params.quesUUID)
        }
    } catch (error) {
        if (loadText.value === '保存中...') {
            loadText.value = '保存失败'
        }
    }
}
const debounceSubmit = useDebounceFn(submit, 1000)
function prepareSubmit() {
    loadText.value = '保存中...'
    // 这里使用闭包将当前准备提交的参数保存起来，防止切换题目后，组件参数变更导致提交参数错误
    const params = {
        quesUUID: props.qid,
        teacherUUID: store.getters.userInfo.teacherUUID,
        proofreading: ruleForm.value.proofreading,
        wrongReason: ruleForm.value.proofreading === 2 ? ruleForm.value.wrongReason : ''
    }
    debounceSubmit(params)
}

// 清空校对记录
async function handleRecordDelete() {
    ruleForm.value.proofreading = 0
    prepareSubmit()
}

// 上一题/下一题
async function getSiblingQuestion(direction: 'last' | 'next') {
    loading.value = true
    try {
        let id = await props.getSiblingQuestionFn(direction)
        emits('update:qid', id)
        await Promise.all([getDetail(id), getRecord(id), getAiAnswerRecord(id)])
        loadText.value = ''
    } catch (error) {
        console.log('error', error)
        message.info((error as Error).message)
    } finally {
        loading.value = false
    }
}

// 编辑题目
const questionEditFormVisible = ref(false)
function onEditFormClose(needRefresh?: boolean) {
    needRefresh && init()
    questionEditFormVisible.value = false
}


// 自动切换下一题
const autoSwitchToNext = ref(true)



// —————————————————————————————————————————————————— AI 答题 ———————————————————————————————————————————————————————
const aiAnswer = ref('')
const aiExplain = ref('')
const aiResult = ref<any>(null)
const getAiAnswerLoading = ref(false)
const aiAnswerDisabled = ref(false)
const getaianswerInput = computed(() => {
    if (!currentData.value) return null
    let type = currentData.value.type
    let body = currentData.value.complicatedediting ? currentData.value.complexcontent : currentData.value.body
    let options = currentData.value.options
    return { body, type, options, question_id: props.qid }
})
async function handleGetAiAnswer() {
    aiAnswer.value = ''
    aiExplain.value = ''
    aiResult.value = null
    if (!getaianswerInput.value) return
    getAiAnswerLoading.value = true
    try {
        let res = await getAiAnswer(getaianswerInput.value)
        aiResult.value = res.results
        aiAnswer.value = res.answer
        aiExplain.value = res.explain
        aiAnswerDisabled.value = true
    } catch (error) {
        console.log(error)
    } finally {
        getAiAnswerLoading.value = false
    }
}
function convertFieldJsonContent(data, toJson = false) {    
    const defaultKeys = ['options', 'disablekws', 'funcMain', 'funcTemplate', 'parameters', 'ptestcase', 'rtype', 'btestcase', 'validatecode']
    defaultKeys.forEach(key => {
      if (toJson) {
        if (data[key]) {
          const _temp = data[key]
          data[key] = JSON.stringify(_temp)
          data[`${key}Json`] = _temp
        }
      } else {
        if (data[`${key}Json`]) {
          data[`${key}`] = data[`${key}Json`]
        }
      }
    })
    data.func_name = data.funcName 
    data.func_templete = data.funcTemplete
  }
function convertBoolean(data) {
  Object.keys(data).forEach(key => {
    if (typeof data[key] === 'boolean') {
      data[key] = data[key] ? 1 : 0
    }
  })
}
/** 获取ai答题记录 */
async function getAiAnswerRecord(id: string) {
    try {
        aiAnswer.value = ''
        aiExplain.value = ''
        //点击下一题重置上一题AI解析
        aiResult.value = null
        aiAnswerDisabled.value = false
        let res = await getaianswerrecord({ quesUUID: id })
        if (res) {
            aiAnswer.value = res.answer
            aiExplain.value = res.explain
            // aiResult.value = [
            //     { answer: res.answer, explain: res.explain, ai: true, teacherName: 'AI', updateTime: res.updateTime, detail: res.detail }
            // ]
            aiResult.value = res.results

            aiAnswerDisabled.value = true
        }
    } catch (error) {
        console.log(error)
    }
}

// 初始化
init()

</script>
<style lang="less" scoped>
.question-proofread {
    display: flex;
    height: 100%;

    .sub-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .question-content {
        padding: 20px 20px 20px 32px;
        min-height: 0;
        overflow: auto;
        border-right: 1px solid #e8e8e8;
        flex: 1;
    }

    .footer {
        height: 80px;
        display: flex;
        align-items: center;
        padding-left: 32px;
        border-top: 1px solid #e8e8e8;

        .ant-btn {
            border-radius: 8px;
            margin-right: 8px;
            font-size: 14px;
        }

        .text-info {
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
        }
    }

    .proofread-content {
        width: 450px;
        display: flex;
        flex-direction: column;

        .proofread-content-top {
            padding: 20px 0 20px 32px;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        .proofread-content-main {
            border-top: 1px solid #e8e8e8;
            padding: 20px 32px;
        }

        .other-info {
            width: 100%;
            padding-right: 20px;
            font-size: 14px;
            flex: 1;
            min-height: 0;
            overflow: auto;

            &>li {
                margin-bottom: 16px;
                display: flex;

                .label {
                    margin-right: 32px;
                    color: rgba(0, 0, 0, 0.65);
                }

                .value {
                    color: rgba(0, 0, 0, 0.85);

                    .ant-checkbox-wrapper {
                        margin-right: 8px;
                    }

                    p {
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

        .operation-container {
            display: flex;

            .operation-panel {
                width: 184px;
                height: 64px;
                border-radius: 8px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                transition: all ease .1s;
                color: #252B3A;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-around;

                &.active {
                    border-color: rgb(84, 120, 238);
                    background-color: rgba(84, 120, 238, 0.1);
                }
                +.operation-panel {
                    margin-left: 16px;
                }
            }
        }

        .proofread-record {
            color: rgba(0, 0, 0, 0.45);
            margin-top: 16px;
            display: flex;
            align-items: center;
        }

        .proofread-reason {
            display: flex;
            margin-top: 16px;

            .label {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                margin-right: 32px;
                flex-shrink: 0;
            }

            .ant-input-textarea {
                width: 688px;

                :deep(.ant-input) {
                    border-radius: 8px;
                    font-size: 14px;

                    &::placeholder {
                        font-size: 14px;
                        color: #adb0b8;
                    }
                }
            }
        }
    }
}
.ai-answer-panel {
    background-color: #f5f5f5;
    width: 752px;
    border-radius: 8px;
    padding: 16px 48px;
    margin-top: 16px;

    >div {
        display: flex;

        .label {
            width: 80px;
            font-size: 12px;
            color: #626262;
            margin-right: 16px;
        }

        .value {
            flex: 1;
            min-width: 0;
            font-size: 12px;
        }
    }
}
.ant-alert-info {
  border: none;
}
</style>