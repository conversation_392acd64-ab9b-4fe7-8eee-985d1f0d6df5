<template>
  <a-modal class="tag-modal" v-model:visible="visible" title="新建标签" width="627px" @cancel="closeModal">
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" @click="onSubmit">保存</a-button>
    </template>
     <a-form class="tag-form" ref="formRef" :label-col="labelCol" :model="formState" :hideRequiredMark="true" :rules="rules">
      <a-form-item ref="name" label="标签名称" name="name">
        <a-input v-model:value="formState.name" placeholder="标签名称" />
      </a-form-item>
      <a-form-item label="父标签">
        <a-input v-model:value="formState.pname" readonly />
      </a-form-item>
      <!-- <a-form-item class="btn-group">
        <a-button
          type="primary"
          :loading="loading"
          :disabled="!formState.parent"
          :title="!formState.parent ? '根标签无法编辑' : ''"
          @click="onSubmit"
          >保存</a-button
        >
      </a-form-item> -->
    </a-form> 
  </a-modal>
 
</template>
<script lang="ts" setup>
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface'
import { reactive, ref, UnwrapRef, watch } from 'vue'
import { tagsMange } from '@/api/admin/tagsManage'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
interface FormState {
  name: string
  id: string | null
  category: string | null
  parent: string | null
  pname: string | null
}

const props = defineProps({
  tagModalVisible: {
    type: Boolean,
    default: false
  },
  tagData: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits(['refreshTagList','closeTagModal'])

const visible = ref(false)
const labelCol = ref({ span: 5 })
const formRef = ref()
const formState: UnwrapRef<FormState> = reactive({
  name: '',
  id: null,
  category: null,
  parent: null,
  pname: null
})
const rules = {
  name: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }]
}
// 新增标签
const store = useStore()
const operateTag = async () => {
  const addParams = {
    action: 'add',
    category: formState.category,
    parent: formState.parent,
    name: formState.name,
    creator: store.getters.userid
  }
  const modifyParams = {
    action: 'modify',
    id: formState.id,
    name: formState.name,
    creator: store.getters.userid
  }
  await tagsMange(formState.id ? modifyParams : addParams)
}

const loading = ref(false)
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      loading.value = true
    })
    .then(() => {
      operateTag().then(() => {
        loading.value = false
        message.success(formState.id ? '标签编辑成功!' : '标签创建成功!')
        emits('refreshTagList')
      })
    })
    .catch((error: ValidateErrorEntity<FormState>) => {
      console.log('error', error)
      loading.value = false
    })
}

const closeModal = () => {
  emits('closeTagModal')
}

watch(
  () => props.tagData,
  () => {
    formRef.value && formRef.value.resetFields()
    Object.assign(formState, props.tagData)
    formState.pname = formState.pname ? formState.pname : '无'
  }
)

watch(() => props.tagModalVisible, (val)=>{
  visible.value = val
})
</script>

<style lang="less" scoped>
.tag-form {
  padding: 20px 60px;
  margin: 0 auto;
  min-width: 460px;
}
.btn-group {
  padding: 30px 0;
  padding-left: 100px;
  text-align: center;
  .ant-btn {
    width: 60%;
  }
}
</style>
<style lang="less">
.tag-modal {
  .ant-modal-footer {
    border: none;
    text-align: center;
    .ant-btn {
      &:first-child {
        color: #3158BD;
        border-color: #3158BD;
        margin-right: 14px;
      }
      width: 97px;
      height: 36px;
      margin-bottom: 26px;
    }
  }
  .ant-modal-body {
    padding-bottom: 0;
  }
  .ant-modal-title {
    font-size: 15px;
    font-weight: bold;
  }
}
</style>
