<script setup lang="ts">
import { MotionConfig } from 'motion-v'
import { ref } from 'vue'
import { transitionEnter, transitionLeave } from './shared'

interface Props {
  title: string
  defaultExpanded?: boolean
  showExpandBtn?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultExpanded: true,
  showExpandBtn: true,
})

// 控制展开/收起状态
const isExpanded = ref(props.defaultExpanded)

// 切换展开/收起
function toggleExpand() {
  isExpanded.value = !isExpanded.value
}
</script>

<template>
  <div :class="isExpanded ? 'pb-6' : ''" class="bg-white rounded-lg px-6 pt-6 shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="mb-6 flex justify-between items-center border-b border-gray-100">
      <h3 class="text-xl font-medium">
        {{ title }}
      </h3>
      <MotionConfig :transition="{ duration: 0.7, type: 'spring', bounce: 0.5 }">
        <button
          v-show="showExpandBtn"
          class="mr-4 flex justify-center items-center gap-2 cursor-pointer opacity-85 rounded-md outline-0 transition-transform duration-300"
          @click="toggleExpand"
        >
          <template v-if="isExpanded">
            <span class="i-lucide-square-divide transition-transform duration-300" />
            收起
          </template>
          <template v-else>
            <span class="i-lucide-square-plus expand-btn transition-transform duration-300" />
            展开
          </template>
        </button>
      </MotionConfig>
    </div>

    <transition
      name="expand" @enter="el => transitionEnter(el as HTMLElement)"
      @leave="el => transitionLeave(el as HTMLElement)"
    >
      <div v-if="isExpanded">
        <slot />
      </div>
    </transition>
  </div>
</template>

<style scoped>
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  height: 0;
  opacity: 0;
}

/* 按钮动画效果 */
button {
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: scale(1.05);
}

button:active {
  transform: scale(0.95);
}

.shrink-btn,
.expand-btn {
  display: inline-block;
}

/* 图标旋转动画 */
.expand-btn {
  transform: rotate(0deg);
}

.shrink-btn {
  transform: rotate(0deg);
}

button:hover .expand-btn,
button:hover .shrink-btn {
  transform: rotate(90deg);
}
</style>