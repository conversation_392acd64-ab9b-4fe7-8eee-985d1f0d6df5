<?xml version="1.0" encoding="UTF-8"?>
<svg width="96px" height="96px" viewBox="0 0 96 96" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>遮挡</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="96" height="96" rx="8"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="考前页面" transform="translate(-836.000000, -1304.000000)">
            <g id="人脸识别" transform="translate(804.000000, 954.000000)">
                <g id="遮挡" transform="translate(32.000000, 350.000000)">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <use id="蒙版" fill="#EEF0F5" xlink:href="#path-1"></use>
                    <g id="编组" mask="url(#mask-2)">
                        <g transform="translate(-4.444444, 10.666667)">
                            <rect id="矩形" fill="#FFD9D8" x="40.6" y="48.4168368" width="24.5" height="25.2190942"></rect>
                            <path d="M40.6,31.6041073 L65.1,31.6041073 L65.1,53.0020309 C63.751617,58.3515995 59.7212157,61.1230357 53.0087963,61.3163393 C46.2963769,61.5096429 42.1601114,58.491226 40.6,52.2610887 L40.6,31.6041073 Z" id="矩形备份" fill="#FAC4C3"></path>
                            <path d="M36.8432019,32.7633992 C33.9188915,32.7633992 32.0678073,33.6309179 31.2899492,35.3659555 C30.123162,37.9685118 30.1753706,42.3280957 34.0665756,44.1900824 C36.6607122,45.4314069 38.2752963,46.0791495 38.9103279,46.13331 L36.8432019,32.7633992 Z" id="路径-2" fill="#FAC4C3"></path>
                            <path d="M73.9432019,32.7633992 C71.0188915,32.7633992 69.1678073,33.6309179 68.3899492,35.3659555 C67.223162,37.9685118 67.2753706,42.3280957 71.1665756,44.1900824 C73.7607122,45.4314069 75.3752963,46.0791495 76.0103279,46.13331 L73.9432019,32.7633992 Z" id="路径-2备份" fill="#FAC4C3" transform="translate(71.867722, 39.448355) scale(-1, 1) translate(-71.867722, -39.448355) "></path>
                            <path d="M53.0509009,59.6253231 C56.731545,59.6253231 61.2104102,57.7101242 63.7168209,55.0740568 C67.6540993,50.9331026 69.7043303,45.5713643 69.7043303,38.6094113 C69.7043277,32.5565836 69.9806112,23.4806406 66.7356948,19.6458603 C63.7582317,16.1271513 57.4519542,17.5934995 53.0509009,17.5934995 C48.7018793,17.5934995 41.8505906,16.200738 38.8844683,19.6458603 C35.5777505,23.4865812 36.3974715,32.4909219 36.3974715,38.6094113 C36.3974715,44.9965162 38.5660132,51.2196486 42.1294005,55.0740568 C45.041468,58.2239519 48.9147356,59.6253231 53.0509009,59.6253231 Z" id="椭圆形" fill="#FFD9D8"></path>
                            <path d="M36.3074136,32.7633992 C36.245105,24.1818815 39.1087178,20.3194537 44.8982521,21.1761156 C53.5825535,22.4611085 56.6400781,19.8742872 58.0409175,19.506689 C59.4417568,19.1390908 66.9641121,17.3414834 69.710179,33.5943819 C69.710179,33.5943819 72.5407027,24.8429447 71.8677222,17.2904855 C71.1947417,9.73802635 63.8467732,7.10985002 63.8467732,6.71743501 C63.8467732,6.32502 62.8858397,0 54.6954447,0 C46.5050498,0 37.5815697,4.33286529 34.0259716,12.111994 C32.3290672,15.8245726 31.8719453,21.4020887 33.4628046,26.602104 C33.8837416,27.9780138 34.8319446,30.0317789 36.3074136,32.7633992 Z" id="路径" fill="#3D3D44"></path>
                            <path d="M40.6,65.5206112 L0.334327209,79.408753 L0,99.5555556 L112,99.5555556 L112,80.8475352 L65.1,65.5206112 C62.0572716,69.7630485 57.9739383,71.8842672 52.85,71.8842672 C47.7260617,71.8842672 43.6427284,69.7630485 40.6,65.5206112 Z" id="路径-3" fill="#6CA3FF"></path>
                        </g>
                    </g>
                    <g id="编组-2" mask="url(#mask-2)">
                        <g transform="translate(32.000000, 40.000000)">
                            <line x1="3.49104183" y1="6.22222222" x2="-4.33062525e-18" y2="4.14814815" id="路径-5" stroke="#3D3D44" stroke-width="2" fill="none" stroke-linecap="round"></line>
                            <line x1="33.7056742" y1="6.51851852" x2="30.2146324" y2="4.14814815" id="路径-5备份-2" stroke="#3D3D44" stroke-width="2" fill="none" stroke-linecap="round" transform="translate(31.996205, 5.333333) scale(-1, 1) translate(-31.996205, -5.333333) "></line>
                            <line x1="14.7969511" y1="4.74074074" x2="20.2406454" y2="4.74074074" id="路径-4" stroke="#3D3D44" stroke-width="2" fill="none"></line>
                            <ellipse id="椭圆形" stroke="none" fill="#3D3D44" fill-rule="evenodd" cx="9.2047923" cy="6.22222222" rx="6.23550446" ry="6.22222222"></ellipse>
                            <ellipse id="椭圆形备份" stroke="none" fill="#3D3D44" fill-rule="evenodd" cx="25.2389466" cy="6.22222222" rx="6.23550446" ry="6.22222222"></ellipse>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>