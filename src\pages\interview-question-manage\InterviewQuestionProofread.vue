<template>
    <a-spin :spinning="loading">
        <div class="question-proofread">
            <div class="question-content">
                <p class="sub-title">
                    <span>题目基本信息</span>
                </p>
                <InterviewQuestionItemDispaly v-if="currentData" :question-detail="currentData" show-type
                    show-correct-answer show-question-difficulty show-question-points-and-basis
                    :option-letter-type="'text'" showDisabledKws />
                <div style="display: flex;align-items: center;margin-top: 16px;" v-if="currentData?.type !== InterviewQuestionEnum['算法题']">
                    <a-button :loading="getAiAnswerLoading" :disabled="aiAnswerDisabled" class="common-ai-button"
                        style="font-size: 14px; border-radius: 8px;padding: 0 8px; height: 32px;"
                        @click="handleGetAiAnswer">
                        <template #icon><img src="@/assets/icons/svg/ai.svg"
                                style="margin-right: 4px;width: 14px;" /></template>
                        辅助校对
                    </a-button>
                    <a-tooltip placement="right" overlayClassName="light">
                        <template #title>
                            <span>自动生成当前题目的答案解析，每道题仅可使用一次</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                    </a-tooltip>
                </div>
                <template v-if="aiAnswer || aiExplain">
                    <div class="ai-answer-panel">
                        <div>
                            <div class="label">AI 答案</div>
                            <div class="value">{{ aiAnswer }}</div>
                        </div>
                        <div style="margin-top: 8px;">
                            <div class="label">答题解析</div>
                            <div class="value">
                                <FoldText 
                                    :text="aiExplain" 
                                    :fold-line-clamp="3" 
                                    background-color="#f5f5f5"
                                    :feedback-params="{
                                        apiname: 'getaiinterviewanswer',
                                        input: getaianswerInput,
                                        output: {
                                            answer: aiAnswer,
                                            explain: aiExplain
                                        }
                                    }"
                                ></FoldText>
                            </div>
                        </div>
                    </div>
                    <a-alert class="tip" type="info" show-icon style="margin-top: 10px;width: 752px;">
                        <template #icon><InfoCircleFilled /></template>
                        <template #message>
                            温馨提示：AI生成的答案仅供参考，请审慎判断并自行核实题目内容和答案。
                        </template>
                    </a-alert>
                </template>
            </div>
            <div class="proofread-content">
                <div class="proofread-content-top">
                    <a-button type="primary" style="font-size: 14px;border-radius: 8px;margin-bottom: 24px;"
                        @click="questionEditFormVisible = true">编辑题目</a-button>
                    <p class="sub-title">题目其他信息</p>
                    <ul class="other-info" v-if="currentData">
                        <li>
                            <span class="label">所属题库</span>
                            <div class="value">{{ currentData.category_name }}</div>
                        </li>
                        <li>
                            <span class="label">难度</span>
                            <div class="value">
                                <svg-icon v-for="i in getStarByScore(currentData.difficulty)" name="active_star" width="16px" height="16px" style="margin-top: 4px;"></svg-icon>
                            </div>
                        </li>
                        <li>
                            <span class="label">深度</span>
                            <div class="value">{{ DepthEnum[currentData.depth] }}</div>
                        </li>
                        <li>
                            <span class="label">偏度</span>
                            <div class="value">{{ ExpertiseEnum[currentData.expertise] }}</div>
                        </li>
                    </ul>
                </div>
                <div class="proofread-content-main">
                    <p class="sub-title">校对结果</p>
                    <div style="margin-bottom: 16px;">
                        <a-checkbox v-model:checked="autoSwitchToNext">
                            <span style="font-size: 14px;">校对正确后自动切换下一题</span>
                        </a-checkbox>
                    </div>
                    <a-radio-group v-model:value="ruleForm.proofreading" class="operation-container">
                        <div class="operation-panel" :class="{ active: ruleForm.proofreading === 1 }"
                            @click="handleProofread(1)">
                            <Vue3Lottie ref="proofreadOkRef" :animationData="ProofreadOk" :auto-play="false" :loop="false"
                                :height="40" :width="40" :no-margin="true"></Vue3Lottie>
                            <span>校对正确</span>
                            <a-radio :value="1"></a-radio>
                        </div>
                        <div class="operation-panel" :class="{ active: ruleForm.proofreading === 2 }"
                            @click="handleProofread(2)">
                            <Vue3Lottie ref="proofreadNoRef" :animationData="ProofreadNo" :auto-play="false" :loop="false"
                                :height="40" :width="40" :no-margin="true"></Vue3Lottie>
                            <span>校对错误</span>
                            <a-radio :value="2"></a-radio>
                        </div>
                    </a-radio-group>
                    <p class="proofread-record" v-if="ruleForm.proofreading">
                        <img v-if="ruleForm.proofreading === 1" src="@/assets/images/svg/proofread_ok.svg" alt="">
                        <img v-if="ruleForm.proofreading === 2" src="@/assets/images/svg/proofread_no.svg" alt="">
                        <span style="margin-left: 4px;">由{{ ruleForm.teacher_name }}于{{
                            ruleForm.update_at }}{{ ruleForm.teacher_name === 'AI' ? '判断' : '确认' }}该题</span>
                        <span style="color: #38910b;font-weight: bold;" v-if="ruleForm.proofreading === 1">正确</span>
                        <span style="color: #dc2b28;font-weight: bold;" v-if="ruleForm.proofreading === 2">错误</span>
                        <img style="margin-left: 10px; cursor: pointer;" src="@/assets/images/svg/delete_black.svg" alt=""
                            @click="handleRecordDelete">
                    </p>
                    <div class="ai-answer-panel" style="width: 100%" v-if="ruleForm.proofreading === 1 && ruleForm.is_ai">{{
                        ruleForm.detail }}</div>
                    <div class="proofread-reason" v-if="ruleForm.proofreading === 2">
                        <span class="label">错误原因</span>
                        <a-textarea v-model:value="ruleForm.wrong_reason" placeholder="请输入错误原因" :rows="4"
                            @change="prepareSubmit" :maxlength="400" showCount />
                    </div>
                </div>
                <div class="footer">
                    <a-button :disabled="nextBtnDisabled" type="primary" @click="getSiblingQuestion('next')">下一题</a-button>
                    <a-button :disabled="lastBtnDisabled" @click="getSiblingQuestion('last')">上一题</a-button>
                    <a-button @click="emits('close')">退出校对</a-button>
                    <span class="text-info">{{ loadText }}</span>
                </div>
            </div>
        </div>
    </a-spin>
    <a-modal title="编辑题目" v-model:visible="questionEditFormVisible" wrapClassName="full-screen-modal" width="100%"
        :maskClosable="false" :closable="false" :keyboard="false" :footer="null" destroyOnClose>
        <InterviewQuestionForm :qid="qid" @cancel="onEditFormClose"></InterviewQuestionForm>
    </a-modal>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import InterviewQuestionItemDispaly from './InterviewQuestionItemDisplay/index.vue'
import { DepthEnum, ExpertiseEnum, InterviewQuestionEnum, type InterviewQuestionDetail } from '@/types/interviewQuestion'
import { Vue3Lottie } from 'vue3-lottie'
import ProofreadOk from '@/assets/animation/proofread_ok.json'
import ProofreadNo from '@/assets/animation/proofread_no.json'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import InterviewQuestionForm from '@/pages/interview-question-manage/InterviewQuestionForm/index.vue'
import moment from 'moment'
import { useDebounceFn } from '@vueuse/core'
import { ExclamationCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'
import FoldText from '@/components/FoldText.vue'
import { addaiinterviewquestionrecord, aiinterviewquestioncontent, getaiinterviewanswer, getaiinterviewanswerrecord, queryaiinterviewquestionrecord } from '@/api/interview'
import { getStarByScore } from '@/utils'

const props = withDefaults(defineProps<{
    qid: string
    getSiblingQuestionFn: (direction: 'last' | 'next') => Promise<any>
    lastBtnDisabled?: boolean
    nextBtnDisabled?: boolean
}>(), {
    lastBtnDisabled: false,
    nextBtnDisabled: false
})

const emits = defineEmits<{
    (e: 'close'): void
    (e: 'update:qid', qid: string): void
}>()

const store = useStore()

// 获取题目信息
const currentData = ref<InterviewQuestionDetail>()
async function getDetail(id: string) {
    currentData.value = await aiinterviewquestioncontent({ ques_id: id })
}

// 获取校对记录
async function getRecord(id: string) {
    let res = await queryaiinterviewquestionrecord({
        id
    }) as any
    if (res) {
        res.teacher_name = res.is_ai ? 'AI' : res.teacher_name
        ruleForm.value = res
    } else {
        resetForm()
    }
}
const loading = ref(false)
function init() {
    if (props.qid) {
        loading.value = true
        Promise.all([getDetail(props.qid), getRecord(props.qid), getAiAnswerRecord(props.qid)]).finally(() => {
            loading.value = false
        })
    }
}

// 表单部分
const ruleForm = ref<{
    proofreading: 0 | 1 | 2
    wrong_reason: string
    update_at?: string
    teacher_name?: string
    /** 是否是ai校对 */
    is_ai?: boolean
    /** ai校对正确/错误的理由 */
    detail?: string
    ques?: string
}>({
    proofreading: 0,
    wrong_reason: ''
})
function resetForm() {
    ruleForm.value.proofreading = 0
    ruleForm.value.wrong_reason = ''
}

const proofreadOkRef = ref<InstanceType<typeof Vue3Lottie>>()
const proofreadNoRef = ref<InstanceType<typeof Vue3Lottie>>()
function handleProofread(proofreading: 1 | 2) {
    ruleForm.value.proofreading = ruleForm.value.proofreading === proofreading ? 0 : proofreading
    ruleForm.value.teacher_name = store.getters.userInfo.username
    ruleForm.value.update_at = moment().format('YYYY-MM-DD HH:mm:ss')
    prepareSubmit()
}
watch(() => ruleForm.value.proofreading, (val) => {
    if (val === 1) {
        proofreadOkRef.value?.goToAndPlay(0, true)
        proofreadNoRef.value?.goToAndStop(0, true)
    } else if (val === 2) {
        proofreadOkRef.value?.goToAndStop(0, true)
        proofreadNoRef.value?.goToAndPlay(0, true)
    } else {
        proofreadOkRef.value?.goToAndStop(0, true)
        proofreadNoRef.value?.goToAndStop(0, true)
    }
}, { immediate: true })

// 提交校对
// 输入时候文字一直是保存中
// 1秒之后（提交防抖）才真正调用保存接口
// 保存成功才会显示"已保存校对结果"，后面不会消失，除非切换题目
const loadText = ref('')
async function submit(params: any) {
    try {
        await addaiinterviewquestionrecord(params)
        if (loadText.value === '保存中...') {
            loadText.value = '已保存校对结果'
        }
        // 校对正确后自动切换下一题
        if (autoSwitchToNext.value && params.proofreading === 1) {
            getSiblingQuestion('next')
        }
    } catch (error) {
        if (loadText.value === '保存中...') {
            loadText.value = '保存失败'
        }
    }
}
const debounceSubmit = useDebounceFn(submit, 1000)
function prepareSubmit() {
    loadText.value = '保存中...'
    // 这里使用闭包将当前准备提交的参数保存起来，防止切换题目后，组件参数变更导致提交参数错误
    const params = {
        action: 'add',
        id: props.qid,
        teacher: store.getters.userid,
        proofreading: ruleForm.value.proofreading,
        wrong_reason: ruleForm.value.proofreading === 2 ? ruleForm.value.wrong_reason : ''
    }
    debounceSubmit(params)
}

// 清空校对记录
async function handleRecordDelete() {
    ruleForm.value.proofreading = 0
    prepareSubmit()
}

// 上一题/下一题
async function getSiblingQuestion(direction: 'last' | 'next') {
    loading.value = true
    try {
        let id = await props.getSiblingQuestionFn(direction)
        emits('update:qid', id)
        await Promise.all([getDetail(id), getRecord(id), getAiAnswerRecord(id)])
        loadText.value = ''
    } catch (error) {
        message.info((error as Error).message)
    } finally {
        loading.value = false
    }
}

// 编辑题目
const questionEditFormVisible = ref(false)
function onEditFormClose(needRefresh?: boolean) {
    needRefresh && init()
    questionEditFormVisible.value = false
}


// 自动切换下一题
const autoSwitchToNext = ref(true)



// —————————————————————————————————————————————————— AI 答题 ———————————————————————————————————————————————————————
const aiAnswer = ref('')
const aiExplain = ref('')
const getAiAnswerLoading = ref(false)
const aiAnswerDisabled = ref(false)
const getaianswerInput = computed(() => {
    if (!currentData.value) return null
    let type = currentData.value.type
    let body = currentData.value.complicatedediting ? currentData.value.complexcontent : currentData.value.body
    let options = currentData.value.options
    return { body, type, options, question_id: props.qid }
})
async function handleGetAiAnswer() {
    aiAnswer.value = ''
    aiExplain.value = ''
    if (!getaianswerInput.value) return
    getAiAnswerLoading.value = true
    try {
        let res = await getaiinterviewanswer(getaianswerInput.value)
        aiAnswer.value = res.answer
        aiExplain.value = res.explain
        aiAnswerDisabled.value = true
    } catch (error) {
        console.log(error)
    } finally {
        getAiAnswerLoading.value = false
    }
}

/** 获取ai答题记录 */
async function getAiAnswerRecord(id: string) {
    try {
        aiAnswer.value = ''
        aiExplain.value = ''
        aiAnswerDisabled.value = false
        let res = await getaiinterviewanswerrecord({ question_id: id })
        if (res) {
            aiAnswer.value = res.answer
            aiExplain.value = res.explain
            aiAnswerDisabled.value = true
        }
    } catch (error) {
        console.log(error)
    }
}

// 初始化
init()

</script>
<style lang="less" scoped>
.question-proofread {
    display: flex;
    height: 100%;

    .sub-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .question-content {
        padding: 20px 20px 20px 32px;
        min-height: 0;
        overflow: auto;
        border-right: 1px solid #e8e8e8;
        flex: 1;
    }

    .footer {
        height: 80px;
        display: flex;
        align-items: center;
        padding-left: 32px;
        border-top: 1px solid #e8e8e8;

        .ant-btn {
            border-radius: 8px;
            margin-right: 8px;
            font-size: 14px;
        }

        .text-info {
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
        }
    }

    .proofread-content {
        width: 450px;
        display: flex;
        flex-direction: column;

        .proofread-content-top {
            padding: 20px 0 20px 32px;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        .proofread-content-main {
            border-top: 1px solid #e8e8e8;
            padding: 20px 32px;
        }

        .other-info {
            width: 100%;
            padding-right: 20px;
            font-size: 14px;
            flex: 1;
            min-height: 0;
            overflow: auto;

            &>li {
                margin-bottom: 16px;
                display: flex;

                .label {
                    width: 60px;
                    margin-right: 32px;
                    color: rgba(0, 0, 0, 0.65);
                }

                .value {
                    color: rgba(0, 0, 0, 0.85);

                    .ant-checkbox-wrapper {
                        margin-right: 8px;
                    }

                    p {
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

        .operation-container {
            display: flex;

            .operation-panel {
                width: 184px;
                height: 64px;
                border-radius: 8px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                transition: all ease .1s;
                color: #252B3A;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-around;

                &.active {
                    border-color: rgb(84, 120, 238);
                    background-color: rgba(84, 120, 238, 0.1);
                }
                +.operation-panel {
                    margin-left: 16px;
                }
            }
        }

        .proofread-record {
            color: rgba(0, 0, 0, 0.45);
            margin-top: 16px;
            display: flex;
            align-items: center;
        }

        .proofread-reason {
            display: flex;
            margin-top: 16px;

            .label {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                margin-right: 32px;
                flex-shrink: 0;
            }

            .ant-input-textarea {
                width: 688px;

                :deep(.ant-input) {
                    border-radius: 8px;
                    font-size: 14px;

                    &::placeholder {
                        font-size: 14px;
                        color: #adb0b8;
                    }
                }
            }
        }
    }
}
.ai-answer-panel {
    background-color: #f5f5f5;
    width: 752px;
    border-radius: 8px;
    padding: 16px 48px;
    margin-top: 16px;

    >div {
        display: flex;

        .label {
            width: 80px;
            font-size: 12px;
            color: #626262;
            margin-right: 16px;
        }

        .value {
            flex: 1;
            min-width: 0;
            font-size: 12px;
        }
    }
}
.ant-alert-info {
  border: none;
}
</style>