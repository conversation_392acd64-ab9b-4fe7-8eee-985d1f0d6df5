//@ts-nocheck
import request from '@/utils/http'

// 题目题库查询
export function queryCategory(data?: object) {
  return request({
    url: '/subjcatg',
    data
  })
}

// 题库列表
export function subjcatgs(data?: object) {
  return request({
    // url: '/subjectCategory/write/tree',
    url:'written/subCategory/tree',
    data
  })
}

export function getCategory(data?: object) {
  return request({
    url: '/q/bank/tree',
    data
  }) 
}

export function getFistLevelTree(data?:ob){
  return request({
    url: '/q/bank/packagesStatistic',
    data
  }) 
}

// 题目题库新增
export function addCategory(data?: object) {
  return request({
    // url: '/subjectCategory/write/create',
    url: '/q/bank/add',
    data
  })
}

// 题目题库修改
export function editCategory(data?: object) {
  return request({
    url: '/subjectCategory/write/update',
    data
  })
}

export function deleteCategory(data?: object) {
  return request({
    url: '/subjectCategory/write/delete',
    data
  })
}

// 新增题目
export function addQuestion(data?: object) {
  return request({
    url: '/questionBankWrite/create',
    data
  })
}
// 查询题目
export function queryQuestion(data?: object) {
  return request({
    // url: '/questionBankWrite/page',
    url:"/written/question/page",
    data
  })
}

export function editQuestion(data?: object) {
  return request({
    url: '/questionBankWrite/update',
    data
  })
}


// 查询题目
export async function queryPaperQuestions(data?: object) {
  const res = await request({
    // url: '/paperdetail',
    url: 'papertemplate/get',
    data
  })

  res.dept_id = res.deptId
  res.end_time = res.endTime
  res.fbody = res.fbodyJson
  res.ojlan = res.ojlanList
  res.share_to_dept = res.shareToDept
  res.individualTiming = res.individualTiming ? res.individualTiming.toString() : res.individualTiming
  res.earliest_sub = res.earliestSub
  res.examvideo_url = res.examvideo
  res.notpaperids = res.notpaperids ? JSON.parse(res.notpaperids) : {}
  
  return Promise.resolve(res)
}

// 快速搜索
export function fastSearch(data?: object) {
  return request({
    url: '/qsques',
    data
  })
}

// 查询指定类型题目
export function queryTypeQuestion(data?: object) {
  return request({
    url: '/questions',
    data
  })
}

// 删除题目
export function delQuestion(data?: object) {
  return request({
    url: '/questionBankWrite/delete',
    data
  })
}

// 测试用例
export function addTestCase(data?: object) {
  return request({
    url: '/testcase',
    data
  })
}

// 条件筛选
export function filterQuestion(data?: object) {
  return request({
    url: '/qfilter',
    data
  })
}

// 批量操作标签
export function modifyQuestionTags(data?: object) {
  return request({
    url: '/optags',
    data
  })
}



// 题库列表(带题量统计)
export function catgquestypecount(data?: object) {
  return request({
    url: '/catgquestypecount',
    data
  })
}

// 拖动题库
export function altersubcatgstru(data?: object) {
  return request({
    url: '/subjectCategory/write/parent/update',
    data
  })
}

// 所有标签
export function alltags(data?: object) {
  return request({
    // url: '/questionBankWrite/tags',
    url:"/written/question/tags",
    data
  })
}

// 添加复合题
export function compoundques(data?: object) {
  return request({
    url: '/compoundques',
    data
  })
}

// 获取算法题的function模板
export function ojtemplate(data?: object) {
  return request({
    url: '/questionBankWrite/ojtemplate',
    data
  })
}

// 算法题验证
export function ojquesjudge(data?: object) {
  return request({
    // url: '/ojquesjudge',
    url: '/questionBankWrite/ojudge',
    data
  })
}

// 草稿箱
export function draftques(data?: object) {
  data.basePager = {
    current:data.page,
    size:data.per_page
  }
  data.typeList - data.type

  delete data.type
  delete data.page
  delete data.per_page

  return request({
    url: '/draftquestion/write/page',
    data
  })
}

// 删除草稿箱
export function deldraftques(data?: object) {
  return request({
    url: '/draftquestion/write/delete',
    data
  })
}

export function draftCreate(data?: object) {
  return request({
    url: '/draftquestion/write/save',
    data
  })
}

export function draftDelete(data?: object) {
  return request({
    url: '/draftquestion/write/delete',
    data
  })
}


// 根据id查看草稿箱数据
export function draftquesbyid(data?: object) {
  return request({
    // url: '/draftquesbyid',
    url: 'draftquestion/write/get',
    data
  })
}

// 根据id查看题目详情
export function quescontent(data?: object) {
  return request({
    url: '/questionBankWrite/get',
    data
  })
}

// 根据id查看题目是否已有绑定试卷
export function beforedelques(data?: { ids: string[] }) {
  return request({
    // url: '/beforedelques',
    url: '/questionBankWrite/before/delete',
    data
  })
}

// 根据试卷id，试题id获取试题详情
export function getPaperQuesDetail(data: any) {
  return request({
    url: '/papertemplate/exam/check/getpaperques',
    data
  })
}

// 批量导入题目
export function exportquestions(data: any) {
  return request({
    url: '/exportquestions',
    data
  })
}

// 再来一卷
export function onemorerollpaper(data: { templeteId: number }) {
  return request({
    url: '/papertemplate/onemorepaper',
    data
  })
}
