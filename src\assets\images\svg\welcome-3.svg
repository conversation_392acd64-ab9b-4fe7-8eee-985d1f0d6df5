<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="-34.7300932%" y1="-33.380269%" x2="28.4272559%" y2="31.9454038%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="77.515495%" y1="-66.6721712%" x2="-37.2185482%" y2="100%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M19.424,9.75 L19.425,20.25 C19.425,21.0784271 18.7534271,21.75 17.925,21.75 L15.3,21.75 C14.4715729,21.75 13.8,21.0784271 13.8,20.25 L13.799,9.75 L19.424,9.75 Z M12.899,9.75 L12.9,20.25 C12.9,21.0784271 12.2284271,21.75 11.4,21.75 L9.81327212,21.7510222 C9.77196989,21.5909045 9.75,21.4230179 9.75,21.25 L9.75,11.75 C9.75,10.6454305 10.6454305,9.75 11.75,9.75 L12.899,9.75 Z" id="path-3"></path>
        <filter x="-23.3%" y="-18.7%" width="146.5%" height="137.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0.75" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="14.3220675%" y1="23.58476%" x2="56.8727124%" y2="55.8568962%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="69.6263211%" y1="3.3834%" x2="28.2048516%" y2="95.9599574%" id="linearGradient-6">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M5.125,4 C5.95342712,4 6.625,4.67157288 6.625,5.5 L6.625,20.5 C6.625,21.3284271 5.95342712,22 5.125,22 L2.5,22 C1.67157288,22 1,21.3284271 1,20.5 L1,5.5 C1,4.67157288 1.67157288,4 2.5,4 L5.125,4 Z M11.65,5.875 C12.4784271,5.875 13.15,6.54657288 13.15,7.375 L13.15,10 L12,10 C10.9456382,10 10.0818349,10.8158778 10.0054857,11.8507377 L10,12 L10,21.5 C10,21.6730179 10.0219699,21.8409045 10.0632721,22.0010222 L9.025,22 C8.19657288,22 7.525,21.3284271 7.525,20.5 L7.525,7.375 C7.525,6.54657288 8.19657288,5.875 9.025,5.875 L11.65,5.875 Z M18.175,1 C19.0034271,1 19.675,1.67157288 19.675,2.5 L19.675,10 L14.05,10 L14.05,2.5 C14.05,1.67157288 14.7215729,1 15.55,1 L18.175,1 Z" id="path-7"></path>
        <linearGradient x1="8.81880296%" y1="9.30569435%" x2="93.0787331%" y2="93.1157159%" id="linearGradient-8">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-9" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-10"></use>
        </pattern>
        <image id="image-10" width="14" height="14" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADqADAAQAAAABAAAADgAAAAC98Dn6AAABLklEQVQoFZ1RQU7DMBCcNKtEREqlPoA7n+GdPIFncOUJIKImIpIlm7XCjNMUqeVAWXnX9nrGO15XH0d/TAkIkZ58nQMw0z9DLnPgOhRMRiIucm2toZgzOgzujlivOcWcCaQnho0USbam4ekJKFA2QzBHzZz2rhxdZFWSMq2Njq49c6F9y8OtKNUjUHKRy7wk+1esbJqBhsSelfuOLJpHwzhTMm8RQe8d6ZK6LLESxobJ0fD6mY/t74DDXmlg4IUiDVPGMPKyE2E9FZFJWV3zXWxUx8qHveHh3vD0nPB2XCusqJ9o7yP1cKiriXOkvnFOeHlVBqiqeimLi0CpmeC1U3rLNHqReYG72hap5XODnxtwhfolYeqe/mZmM6j0z1a+Q8Rbbfcfkorsbq204b8Bt462tii+cxkAAAAASUVORK5CYII="></image>
        <path d="M2,0 L11,0 C12.1045695,-2.02906125e-16 13,0.8954305 13,2 L13,11 C13,12.1045695 12.1045695,13 11,13 L2,13 C0.8954305,13 1.3527075e-16,12.1045695 0,11 L0,2 C-1.3527075e-16,0.8954305 0.8954305,2.02906125e-16 2,0 Z" id="path-11"></path>
        <linearGradient x1="34.3857943%" y1="15.2468045%" x2="124.063702%" y2="115.359067%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M3.49801332,0.996746805 C3.82647882,1.1062353 4.08422591,1.36398239 4.19371441,1.69244789 L5.71937276,6.26942294 C5.82416142,6.58378893 5.65426543,6.92358092 5.33989944,7.02836958 C5.02553345,7.13315825 4.68574147,6.96326225 4.5809528,6.64889626 L4.25,5.659 L2.048,5.659 L1.71937276,6.64889626 C1.62506296,6.93182565 1.34040048,7.09773435 1.05523827,7.05169586 L0.960426122,7.02836958 C0.646060131,6.92358092 0.476164139,6.58378893 0.580952803,6.26942294 L2.10661115,1.69244789 C2.2987237,1.11611024 2.92167567,0.804634255 3.49801332,0.996746805 Z M3.14916278,2.3551596 L2.448,4.459 L3.85,4.459 L3.14916278,2.3551596 Z M6.85032556,0.94 C7.18169641,0.94 7.45032556,1.20862915 7.45032556,1.54 L7.45032556,6.46 C7.45032556,6.79137085 7.18169641,7.06 6.85032556,7.06 C6.51895471,7.06 6.25032556,6.79137085 6.25032556,6.46 L6.25032556,1.54 C6.25032556,1.20862915 6.51895471,0.94 6.85032556,0.94 Z" id="path-13"></path>
        <filter x="-29.0%" y="-16.3%" width="158.0%" height="165.4%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-180.000000, -130.000000)">
            <g id="icon/06毛玻璃/24*24/03AI智能分析" transform="translate(180.000000, 130.000000)">
                <rect id="bg备份-12" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="url(#linearGradient-5)" xlink:href="#path-7"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                </g>
                <g id="编组" transform="translate(10.000000, 10.000000)" fill-rule="nonzero">
                    <g id="矩形" stroke-linejoin="square" stroke-width="0.499999999">
                        <path stroke="url(#linearGradient-8)" d="M11,0.249999999 C11.4832492,0.249999999 11.9207492,0.445875421 12.2374369,0.762563132 C12.5541246,1.07925084 12.75,1.51675084 12.75,2 L12.75,2 L12.75,11 C12.75,11.4832492 12.5541246,11.9207492 12.2374369,12.2374369 C11.9207492,12.5541246 11.4832492,12.75 11,12.75 L11,12.75 L2,12.75 C1.51675084,12.75 1.07925084,12.5541246 0.762563132,12.2374369 C0.445875421,11.9207492 0.249999999,11.4832492 0.249999999,11 L0.249999999,11 L0.249999999,2 C0.249999999,1.51675084 0.445875421,1.07925084 0.762563132,0.762563132 C1.07925084,0.445875421 1.51675084,0.249999999 2,0.249999999 L2,0.249999999 Z" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd"></path>
                        <path stroke="url(#pattern-9)" d="M11,0.249999999 C11.4832492,0.249999999 11.9207492,0.445875421 12.2374369,0.762563132 C12.5541246,1.07925084 12.75,1.51675084 12.75,2 L12.75,2 L12.75,11 C12.75,11.4832492 12.5541246,11.9207492 12.2374369,12.2374369 C11.9207492,12.5541246 11.4832492,12.75 11,12.75 L11,12.75 L2,12.75 C1.51675084,12.75 1.07925084,12.5541246 0.762563132,12.2374369 C0.445875421,11.9207492 0.249999999,11.4832492 0.249999999,11 L0.249999999,11 L0.249999999,2 C0.249999999,1.51675084 0.445875421,1.07925084 0.762563132,0.762563132 C1.07925084,0.445875421 1.51675084,0.249999999 2,0.249999999 L2,0.249999999 Z"></path>
                    </g>
                    <g id="AI" transform="translate(2.500000, 2.500000)">
                        <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="8" height="8"></rect>
                        <g id="形状结合">
                            <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                            <use fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>