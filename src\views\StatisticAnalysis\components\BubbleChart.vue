<template>
  <div ref="myRef" :style="{ width, height }" class="bubble-chart"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: '考生能力分析'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 1000)
})

const schema = [
  { name: 'subjects', index: 0, text: '题库' },
  { name: 'accuracyRate', index: 1, text: '正确率' },
  { name: 'questionCount', index: 2, text: '答题数量' }
]
const itemStyle = {
  opacity: 0.8,
  shadowBlur: 10,
  shadowOffsetX: 0,
  shadowOffsetY: 0,
  shadowColor: 'rgba(0,0,0,0.3)'
}

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  Chart.value.setOption({
    color: ['#dd4444', '#fec42c', '#80F1BE'],
    legend: {
      top: 10,
      data: [`${props.name}`], // 对于单个学生，这里数组就只有一个元素
      textStyle: {
        fontSize: 16
      }
    },
    grid: {
      left: '10%',
      right: 150,
      top: '18%',
      bottom: '10%'
    },
    tooltip: {
      backgroundColor: 'rgba(255,255,255,0.7)',
      formatter: function (param: any) {
        var value = param.value
        // prettier-ignore
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
                + param.seriesName + '的' + value[0] + '</div>'
                + schema[1].text + '：' + (value[1]*100).toFixed(2) + '%<br>'
                + schema[2].text + '：' + value[2] + '题<br>';
      }
    },
    xAxis: {
      type: 'category',
      name: '题库',
      nameGap: 16,
      nameTextStyle: {
        fontSize: 16
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '正确率',
      nameLocation: 'end',
      nameGap: 20,
      nameTextStyle: {
        fontSize: 16
      },
      splitLine: {
        show: false
      }
    },
    visualMap: [
      {
        left: 'right',
        top: '10%',
        dimension: 2,
        min: 0, // 这里注意，需要取答题数量的最小值
        max: 50, // 需要取答题数量的最大值
        itemWidth: 30,
        itemHeight: 120,
        calculable: true,
        precision: 0.1,
        text: ['答题数量'],
        textGap: 30,
        inRange: {
          symbolSize: [10, 70]
        },
        outOfRange: {
          symbolSize: [10, 70],
          color: ['rgba(255,255,255,0.4)']
        },
        controller: {
          inRange: {
            color: ['#c23531']
          },
          outOfRange: {
            color: ['#999']
          }
        }
      }
    ],
    series: [
      {
        name: props.name,
        type: 'scatter',
        itemStyle: itemStyle,
        data: props.data
      }
    ]
  })
  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}

watch(
  () => props.width,
  (val) => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  (val) => {
    Chart.value.resize()
  }
)
</script>

<style lang="less" scoped>
.bubble-chart {
  padding: 16px;
  background: #fff;
}
</style>
