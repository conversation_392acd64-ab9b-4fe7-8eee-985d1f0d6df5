<template>
  <div class="pure-jsx-component">
    <!-- 全部内容 -->
    <div v-if="currentType === 'all'" class="pure-jsx-component">
      <!-- 题库 -->
      <div class="flex items-center">
        题库
        <RightOutlined class="ml-[10px]" />
      </div>
      <div class="w-full py-[8px] flex flex-wrap justify-between">
        <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
          <img :src="fileIcon" class="mr-[10px]" />
          <div>前端面试题库</div>
        </div>
      </div>

      <!-- 科目 -->
      <div class="flex items-center">
        科目
        <RightOutlined class="ml-[10px]" />
      </div>
      <div class="py-[8px]">
        <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
          <div class="w-full mb-[8px]">
            操作系统
          </div>
          <div class="flex flex-nowrap justify-between w-full">
            <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
              前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
            </div>
            <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-end text-[16px]">
              前端
              >
              输入输出
            </div>
          </div>
        </div>
      </div>
      <!-- 题目 -->
      <div class="flex items-center">
        题目
        <RightOutlined class="ml-[10px]" />
      </div>
      <div class="py-[8px]">
        <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
          <div class="w-full mb-[8px]">
            操作系统
          </div>
          <div class="flex flex-nowrap justify-between w-full">
            <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
              前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 题目包内容 -->
    <div v-else-if="currentType === 'package'" class="w-full py-[8px] flex flex-wrap justify-between">
      <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
        <img :src="fileIcon" class="mr-[10px]" />
        <div>前端面试题库</div>
      </div>
      <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
        <img :src="fileIcon" class="mr-[10px]" />
        <div>前端面试题库</div>
      </div>
      <div class="flex items-center h-[46px] bg-[#f1f4fe] w-[48%] rounded-[8px] px-[8px] mb-[8px]">
        <img :src="fileIcon" class="mr-[10px]" />
        <div>前端面试题库</div>
      </div>
    </div>

    <!-- 科目内容 -->
    <div v-else-if="currentType === 'subject'" class="py-[8px]">
      <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
        <div class="w-full mb-[8px] cursor-pointer">
          <CaretRightOutlined class="mr-[8px]" />
          操作系统
        </div>
        <div class="flex flex-nowrap justify-between w-full">
          <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
            前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
          </div>
          <div class="w-[50%] overflow-hidden text-ellipsis whitespace-nowrap text-end text-[16px]">
            前端
            >
            输入输出
          </div>
        </div>
      </div>
    </div>

    <!-- 题目内容 -->
    <div v-else-if="currentType === 'question'" class="py-[8px]">
      <div class="p-[12px] mb-[8px] flex flex-wrap bg-[#F1F4FE] rounded-[8px]">
        <div class="w-full mb-[8px] cursor-pointer">
          <CaretRightOutlined class="mr-[8px]" />
          操作系统
        </div>
        <div class="flex flex-nowrap justify-between w-full">
          <div class="w-full overflow-hidden text-ellipsis whitespace-nowrap text-[16px]">
            前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式前端计算机网络制式
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import fileIcon from '@/assets/icons/svg/file_icon.svg'
import { CaretRightOutlined, RightOutlined } from '@ant-design/icons-vue'
import { computed } from 'vue'

// 接收 type 属性
const props = defineProps<{
  type?: 'all' | 'package' | 'subject' | 'question'
}>()

// 默认值处理
const currentType = computed(() => props.type || 'all')
</script>