<template>
  <div class="test-instance">
    <template v-for="(item, index) in instanceList" :key="index">
      <div class="head">
        示例{{ index + 1 }}:
        <svg-icon
          v-if="instanceList.length > 1"
          class="del-icon"
          name="circle-del"
          width="16px"
          height="16px"
          @click="delInstance(index)"
        />
      </div>
      <div class="content">
        <div class="content-input">
          <p>输入</p>
          <template v-for="(test, index) in item.input" :key="index">
            <div class="item">
              <span v-if="Object.keys(test)[0]" class="item-name">{{ Object.keys(test)[0] }}</span>
              <a-input
                v-if="Object.keys(test)[0]"
                v-model:value="test[Object.keys(test)[0]]"
                autocomplete="off"
              />
            </div>
          </template>
        </div>
        <div class="content-output">
          <p>输出</p>
          <div class="item">
            <a-radio v-model:checked="item.output[0].type" />
            <span>返回值</span>
          </div>
          <div class="item">
            <a-input v-model:value="item.output[0].value" autocomplete="off" />
          </div>
        </div>
      </div>
    </template>
    <div class="addparam-btn">
      <span @click="addInstance"><svg-icon name="plus" />添加示例</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
const props = defineProps({
  exampleIO: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['addInstance', 'delInstance'])

const instanceList = ref<any>([])
const addInstance = () => {
  emits('addInstance')
}
const delInstance = (index: number) => {
  emits('delInstance', index)
}

watch(
  () => props.exampleIO,
  (val) => {
    instanceList.value = val
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style lang="less" scoped>
.test-instance {
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    padding: 0 16px;
    font-weight: 600;
    line-height: 34px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 8px 8px 0 0;
    .del-icon {
      cursor: pointer;
    }
  }
  .content {
    display: flex;
    padding: 16px 0;
    margin-bottom: 8px;
    background: #f5f5f5;
    border-radius: 0 0 8px 8px;
    .content-input,
    .content-output {
      flex: 1;
      padding: 0 16px;
      p {
        font-size: 12px;
        font-weight: 600;
        line-height: 34px;
      }
      .ant-input {
        border-radius: 8px;
        font-size: 12px;
        height: 32px;
        width: 220px;
      }
    }
    .content-input {
      border-right: 1px solid #e8e8e8;
      .item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        .item-name {
          width: 60px;
          word-wrap: break-word;
          margin-right: 16px;
        }
      }
    }
    .content-output {
      .item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
      }
    }
  }
  .addparam-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 82px;
    height: 24px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-top: 10px;
  }
}
</style>
