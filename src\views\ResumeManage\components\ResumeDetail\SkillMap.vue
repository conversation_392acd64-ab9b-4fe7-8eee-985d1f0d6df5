<script setup lang="ts">
import type { SkillMapDataProps } from './shared'
import { RadarChart } from 'echarts/charts'
import { LegendComponent, TitleComponent } from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'

const props = withDefaults(defineProps<{
  skillMapData: SkillMapDataProps
}>(), {
  skillMapData: () => [
    {
      label: '专业能力',
      value: 100,
      desc: '与岗位所需技能的匹配度。与岗位所需技能的匹配度。与岗位所需技能的匹配度。与岗位所需技能的匹配度。与岗位所需技能的匹配度。',
    },
    {
      label: '沟通能力',
      value: 50,
      desc: '表达清晰度、工作经历中的沟通描述以及学生组织工作经验。',
    },
    {
      label: '逻辑思维',
      value: 100,
      desc: '清晰、有条理地阐述工作流程、解决问题的思路和步骤。',
    },
    {
      label: '工作经验',
      value: 100,
      desc: '工作经历的连贯性、工作内容与应聘岗位的相关性。',
    },
    {
      label: '教育背景',
      value: 100,
      desc: '毕业院校、专业、学位的岗位匹配度与竞争力。',
    },
  ],
})

use([TitleComponent, LegendComponent, RadarChart, CanvasRenderer])

const chartRef = ref<any>(null)
const currentHoverCircleIndex = ref(undefined)
function handleMouseOver(params: any) {
  if (params.event.target.shape.symbolType === 'circle') {
    currentHoverCircleIndex.value = params.event.topTarget.__dimIdx
  }
  else {
    currentHoverCircleIndex.value = undefined
  }
}
const option = computed(() => {
  if (!props.skillMapData?.length) {
    return {}
  }
  return {
    tooltip: {
      trigger: 'item',
      show: currentHoverCircleIndex.value !== undefined,
      // position: 'center', 
      confine: true,
      formatter: () => {
        const tooltipContents = props.skillMapData.map((item) => {
          // 这里每 50 字符换一行 
          return `<b>${item.label}:</b> ${item.desc
            .match(/.{1,30}/g) // 每 50 个字符切一段
            ?.map((chunk: any) => `<div>${chunk}</div>`)
            .join('')}`
        })

        if (currentHoverCircleIndex.value !== undefined) {
          console.log(`
          ${tooltipContents[currentHoverCircleIndex.value]}
        `)
          return `
          ${tooltipContents[currentHoverCircleIndex.value]}
        `
        }
        else {
          return ``
        }
      },
    },
    radar: {
      splitNumber: 4,
      nameGap: 10,
      axisName: {
        color: '#666',
        fontSize: 12,
        padding: [2, 0],
      },
      axisTick: {
        show: true,
        length: 3,
        lineStyle: {
          color: '#ddd',
          width: 1,
        },
      },
      indicator: props.skillMapData?.map((item, index) => {
        if (index === 0) {
          return {
            name: item.label,
            max: 100,
            axisLabel: {
              show: true,
              fontSize: 8,
              color: '#9b9b9b',
            },
          }
        }
        else {
          return {
            name: item.label,
            max: 100,
          }
        }
      }),
    },
    series: [
      {
        type: 'radar',
        symbol: 'circle',
        symbolSize: 0,
        lineStyle: {
          width: 2,
          color: '#448ef7',
        },
        areaStyle: {
          color: 'rgba(61, 149, 255, 0.4)',
        },

        data: [
          {
            value: props.skillMapData.map(item => item.value),
            name: '能力评估',
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#73a2f4',
              borderColor: 'transparent',
              borderWidth: 30,
            },
          },
        ],
      },
    ],
  }
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 h-full">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        能力维度
      </h3>
    </div>
    <div class="flex justify-center items-center">
      <VChart ref="chartRef" class="w-full h-[260px]" :option="option" autoresize @mouseover="handleMouseOver" />
    </div>
  </div>
</template>