<template>
  <a-collapse
    class="filter-collapse"
    v-model:activeKey="filterActiveKey"
    ghost
    :bordered="false"
    accordion
  >
    <template #expandIcon="{ isActive }">
      <svg-icon
        name="filter"
        color="#3158BD"
        class="filter-icon"
        style="transition: transform 0.2s"
        :style="isActive ? { transform: 'rotate(180deg)' } : { transform: 'rotate(0)' }"
      />
    </template>
    <a-collapse-panel key="1" header="条件筛选">
      <div class="filter-form">
        <a-form ref="filterFormRef" :model="filterForm" :label-col="labelCol" :colon="false">
          <a-form-item label="候选人姓名" name="name">
            <a-input
              v-model:value="filterForm.name"
              placeholder="输入候选人姓名"
              style="max-width: 480px"
            />
          </a-form-item>
          <a-form-item label="标签" name="tags">
            <a-tree-select
              v-if="tagsList.length"
              show-search
              :tree-data="tagsList"
              style="max-width: 480px"
              placeholder="选择标签"
              v-model:value="filterForm.tags"
              :dropdown-style="{ maxHeight: '280px', overflow: 'auto' }"
              allow-clear
              multiple
              :replace-fields="replaceFields"
              treeNodeFilterProp="name"
              tree-default-expand-all
              :getPopupContainer="
                  (triggerNode:HTMLElement) => {
                    return triggerNode.parentNode
                  }
                "
            >
            </a-tree-select>
          </a-form-item>

          <a-form-item class="filter-btn-group">
            <a-button type="primary" @click="filterSearch">查询</a-button>
            <a-button style="margin-left: 10px" @click="resetForm">重置</a-button>
          </a-form-item>
        </a-form>
      </div>
    </a-collapse-panel>
      <a-collapse-panel key="2" header="批量操作">
      <a-tabs v-model:activeKey="tabActiveKey" size="small" class="oper-tabs">
        <a-tab-pane key="1" tab="批量修改">
          <div class="filter-form oper-form">
            <a-form ref="modFormRef" :model="modifyForm" :label-col="labelCol">
              <a-form-item>
                <p>当前已选中 {{ props.selectedBody }} 个项目</p>
              </a-form-item>
              <a-form-item>
                <a-radio-group v-model:value="operateType">
                  <a-radio value="add">添加标签</a-radio>
                  <a-radio value="del">删除标签</a-radio>
                </a-radio-group>
              </a-form-item>

              <a-form-item>
                <a-tree-select
                  v-if="tagsList.length"
                  show-search
                  :tree-data="tagsList"
                  style="width: 100%"
                  v-model:value="modifyForm.tags"
                  :dropdown-style="{ maxHeight: '230px', overflow: 'auto' }"
                  allow-clear
                  multiple
                  :replace-fields="replaceFields"
                  treeNodeFilterProp="name"
                  tree-default-expand-all
                  :getPopupContainer="
                  (triggerNode:HTMLElement) => {
                    return triggerNode.parentNode
                  }
                "
                >
                </a-tree-select>
              </a-form-item>

              <a-form-item class="filter-btn-group">
                <a-button type="primary" @click="multiModify">批量修改</a-button>
                <a-button style="margin-left: 10px" @click="resetForm2">重置</a-button>
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="批量删除">
          <div class="mul-del">
            <a-button type="danger" @click="delMultiResume">批量删除</a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" tab="批量导入" disabled>Content of Tab Pane 3</a-tab-pane>
        <a-tab-pane key="4" tab="批量导出" disabled>Content of Tab Pane 4</a-tab-pane>
      </a-tabs>
    </a-collapse-panel>
  </a-collapse>
</template>

<script setup lang="ts">
import { FilterOutlined } from '@ant-design/icons-vue'
import { ref, onMounted } from 'vue'
import { tagsMange } from '@/api/admin/tagsManage'

const props = defineProps({
  selectedBody: {
    type: Number,
    required: true
  }
})

const emits = defineEmits(['delMultiResume', 'handleFilterResume', 'multiModify'])

const filterActiveKey = ref([])
const tabActiveKey = ref('1')
const labelCol = ref({ span: 8 })
const replaceFields = ref({
  title: 'name',
  key: 'id',
  value: 'id'
})

const filterFormRef = ref()
const filterForm = ref({
  name: '',
  tags: []
})

// 查询标签
const tagsList = <any>ref([])
const queryTags = () => {
  const params = {
    action: 'query',
    type: 'student'
  }
  tagsMange(params).then((res: any) => {
    tagsList.value = res.tags
  })
}

const filterSearch = () => {
  emits('handleFilterResume', filterForm.value)
}
const resetForm = () => {
  filterFormRef.value.resetFields()
}
const resetForm2 = () => {
  modifyForm.value.tags = []
}

// 批量操作
const operateType = ref('add')
const modifyForm = <any>ref({
  tags: []
})

// 批量删除
const delMultiResume = () => {
  emits('delMultiResume')
}

// 批量修改
const multiModify = () => {
  emits('multiModify', { tags: modifyForm.value.tags, action: operateType.value, obj: 'student' })
}

onMounted(() => {
  queryTags()
})
</script>

<style lang="less" scoped>
.filter-form {
  padding: 60px 30px 20px;
  background-color: #fff;
  :deep(.ant-form-item-label > label),
  :deep(.ant-input),
  :deep(.ant-select-selector),
  :deep(.ant-select-tree-node-content-wrapper) {
    font-family: PingFang-SC;
    min-height: 32px;
    font-size: 14px;
    color: #121633;
  }
  .filter-btn-group {
    text-align: center;
    .ant-btn {
      width: 160px;
      height: 40px;
      border-radius: 3px;
      font-size: 14px;
      color: #3158bd;
      border-color: #3158bd;
    }
    .ant-btn-primary {
      color: #fff;
    }
  }
}

.oper-form {
  max-width: 600px;
  padding: 20px 0;
  margin: 0 auto;
  text-align: center;
  color: #121633;
}

.filter-collapse.ant-collapse {
  border-top: 1px solid #d9d9d9;
  background: #fff;
  :deep(.ant-collapse-item > .ant-collapse-header) {
    font-family: PingFang HK;
    font-weight: bold;
    padding-left: 20px;
    font-size: 14px;
    color: #121633;
    border-bottom: 1px solid #f5f5f5;
    .icon-filter {
      position: relative;
      left: -6px;
      bottom: 1px;
    }
  }
}

.oper-tabs {
  :deep(.ant-tabs-tab) {
    padding: 8px 2px;
    font-size: 14px;
  }

}

.mul-del {
  padding: 20px;
  text-align: center;
  .ant-btn {
    width: 300px;
  }
}
</style>
