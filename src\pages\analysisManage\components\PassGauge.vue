<!-- 通过率仪表盘 -->
<template>
  <div class="pass-gauge-wrap">
    <div ref="chartRef" style="height: 100%;"></div>
    <div class="tip-wrap">
      <div class="start">0</div>
      <div class="label">通过率</div>
      <div class="end">100%</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ECharts, init } from 'echarts'

const props = defineProps<{
  value: number
}>()

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
  chart.value = init(chartRef.value)
  chart.value?.setOption({
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        radius: '90%',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        progress: {
          show: true,
          width: 18,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [{
                offset: 0, color: '#b0d0ff',
              }, {
                offset: 1, color: '#5478ee',
              }]
            }
          }
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 18,
            color: [[1, 'rgba(0, 0, 0, 0.15)']],
          }
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        anchor: {
          show: false
        },
        title: {
          show: false,
        },
        detail: {
          color: '#000000',
          valueAnimation: true,
          width: '80%',
          lineHeight: 30,
          offsetCenter: [0, -12],
          fontSize: 24,
          fontWeight: 'bolder',
          formatter: (value: any) => ([
            `{a| ${value}}`,
            '{b| %}'
          ].join('')),
          rich: {
            a: {
              height: 30,
              color: '#000',
              fontSize: 24,
              verticalAlign: 'bottom',
            },
            b: {
              height: 24,
              color: '#000',
              fontSize: 12,
              verticalAlign: 'bottom',
            }
          }
        },
        data: [
          {
            value: 0,
          },
        ]
      },
    ]
  })
  window.addEventListener('resize', resizeChart)
}

function resizeChart() {
  chart.value?.resize()
}

onMounted(async () => {
  draw()
})

watch([() => props.value, chart], ([value, myChart]) => {
  if (!myChart) return
  myChart.setOption({
    series: [
      {
        data: [
          {
            value: value
          }
        ]
      }
    ]
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.pass-gauge-wrap {
  position: relative;
  .tip-wrap {
    width: 190px;
    height: 22px;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    bottom: 44px;
    display: flex;
    justify-content: space-between;
    .start, .end {
      width: 40px;
      font-size: 12px;
      text-align: center;
      color: rgba(0,0,0,0.45);
      line-height: 22px;
    }
    .label {
      position: absolute;
      left: 0;
      right: 0;
      margin: auto;
      width: 60%;
      height: 100%;
      font-size: 14px;
      text-align: center;
      color: rgba(0,0,0,0.45);
      line-height: 22px;
    }
  }
}
</style>