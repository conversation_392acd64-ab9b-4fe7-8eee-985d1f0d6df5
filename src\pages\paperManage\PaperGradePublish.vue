<template>
    <div class="paper-grade-publish">
        <a-form :colon="false" :labelCol="{ span: 4, offset: 0 }" labelAlign="left">
            <a-form-item label="实考人数" style="margin-bottom: 8px;">
                <div style="text-align: right;">
                    <span style="color: #5478EE;">{{ examInfo.actual_candidates_nums }}</span>/{{ examInfo.candidates_nums
                    }}
                </div>
            </a-form-item>
            <a-progress :strokeWidth="10" strokeColor="#5478EE" style="margin-bottom: 24px;"
                :percent="(examInfo.actual_candidates_nums / examInfo.candidates_nums) * 100" :show-info="false" />
            <a-form-item label="分数分布" style="margin-bottom: 8px;"></a-form-item>
            <div ref="chartRef" style="height: 60px; margin-bottom: 24px;"></div>
            <a-form-item>
                <template #label>
                    <span>通过标准</span>
                    <a-tooltip placement="right" overlayClassName="light">
                        <template #title>
                            <span>（1）按分数：设置具体的通过成绩；（2）按排名：通过设置排名范围，自动计算通过分数和人数</span>
                        </template>
                        <svg-icon class="common-info-icon" name="info2"></svg-icon>
                    </a-tooltip>
                </template>
                <a-radio-group v-model:value="ruleForm.type">
                    <a-radio value="score">按分数</a-radio>
                    <a-radio value="rank">按排名</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item label="具体数值">
                <template v-if="ruleForm.type === 'score'">
                    <a-input-number
                      v-model:value="ruleForm.scoreValue"
                      :max="examInfo.score"
                      :min="1"
                      :precision="0"
                      :controls="false"
                    ></a-input-number>
                    <span>分</span>
                </template>
                <template v-else-if="ruleForm.type === 'rank'">
                    前
                    <a-input-number
                      v-model:value="ruleForm.rankValue"
                      :max="examInfo.candidates_nums"
                      :min="1"
                      :precision="0"
                      :controls="false"
                    ></a-input-number>
                    <span>名</span>
                </template>
            </a-form-item>
            <a-form-item label="通过分数">
                <span>{{ passScore }}</span>分
            </a-form-item>
            <a-form-item label="通过人数">
                {{ passNum }}/{{ examInfo.candidates_nums }}
            </a-form-item>
            <a-form-item label="公布范围">
                <a-checkbox-group
                  v-model:value="ruleForm.range"
                  :options=" [
                    { label: '通过考生', value: 1 },
                    { label: '不通过考生', value: 0 }
                  ]"
                />
            </a-form-item>
        </a-form>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { examscoreinfo, publishscores } from '@/api/exam'
import * as echarts from 'echarts'
import { message } from 'ant-design-vue'
const props = defineProps<{
    paperId: string
}>()

const examInfo = ref<{
    actual_candidates_nums: number
    all_scores: number[]
    average_score: number
    candidates_nums: number
    median: number
    name: string
    score: number
}>({
    actual_candidates_nums: 0,
    all_scores: [],
    average_score: 0,
    candidates_nums: 0,
    median: 0,
    name: '',
    score: 100
})
async function getScoreDetail() {
    let res = await examscoreinfo({ templeteId: props.paperId }) as any
    res.all_scores = res.all_scores.sort((a: number, b: number) => b - a) // 降序排列
    examInfo.value = res
}

// 绘制分数分布图
const chartRef = ref()
function initChart(arr: number[]) {
    let countMap: Map<number, number> = new Map()
    arr.forEach(i => countMap.set(i, countMap.has(i) ? countMap.get(i)! + 1 : 1))
    let chart = echarts.init(chartRef.value)
    let option = {
        grid: {
            left: 0,
            right: 35,
            bottom: 5,
            top: 25,
            containLabel: true
        },
        tooltip: {
            trigger: "item",
        },
        color: ['#a7d691'],
        xAxis: {
            type: "value",
            max: examInfo.value.score,
            splitLine: {
                show: false
            },
            axisLine: {
                lineStyle: {
                    color: '#999'
                }
            }
        },
        yAxis: {
            show: false
        },
        series: [{
            name: "分数",
            type: "scatter",
            data: [...countMap.keys()].map(i => ({
                value: [i, 1],
                tooltip: {
                    formatter: i + "分：" + countMap.get(i) + "人",
                    position: 'top'
                }
            })),
            symbolSize: function (dataItem: any) {
                return 5 + countMap.get(dataItem[0])!;
            },
            markLine: {
                silent: true,
                label: {
                    distance: 10,
                    formatter: "平均分：" + examInfo.value.average_score,
                },
                symbol: ['none', 'none'], // 去掉箭头
                lineStyle: {
                    color: "#6e7079",
                    type: "dashed",
                },
                data: [
                    {
                        xAxis: examInfo.value.average_score,
                        emphasis: {
                            disabled: true
                        }
                    }
                ],
            },
        }]
    }
    chart.setOption(option)
}
onMounted(() => {
    setTimeout(async () => {
        // dialog的宽度有个从0到100%的动画，所以要延迟一下
        await getScoreDetail()
        initChart(examInfo.value.all_scores)
    }, 0);
})

// 发布成绩
const ruleForm = ref<{
    type: 'rank' | 'score'
    rankValue: number
    scoreValue: number
    range: number[]
}>({
    type: 'score',
    rankValue: 1,
    scoreValue: 1,
    range: []
})
const passScore = ref(0)
const passNum = ref(0)
watchEffect(() => {
    if (ruleForm.value.type === 'score') {
        passScore.value = ruleForm.value.scoreValue
    } else if (ruleForm.value.type === 'rank') {
        // 如果按排名，则通过分数则为该排名的分数和所有分数中非0的最低分中的较大者
        let valideScores = examInfo.value.all_scores.filter(score => !!score)
        if (valideScores.length === 0) {
            passScore.value = 1
        } else {
            let minScore = Math.min(...valideScores)
            passScore.value = Math.max(examInfo.value.all_scores[ruleForm.value.rankValue - 1], minScore)
        }
    }
    getPassNum()
})
function getPassNum() {
    let count = 0
    examInfo.value.all_scores.forEach(i => {
        if (i >= passScore.value) count++
    })
    passNum.value = count
}
async function publish() {
    await publishscores({
        templeteId: props.paperId,
        publishType: ruleForm.value.type,
        passScore: ruleForm.value.type === 'rank' ? passNum.value : ruleForm.value.scoreValue,
        range: ruleForm.value.range
    })
    message.success('发布成功')
}

defineExpose({
    publish
})

</script>

<style lang="less" scoped>
.paper-grade-publish {}

:deep(.ant-form) {
    .ant-form-item-no-colon {
        font-size: 14px;
        color: #626262;
    }
}

:deep(.ant-radio-group) {
    .ant-radio-wrapper {
        font-size: 14px;
    }
}

:deep(.ant-checkbox-wrapper) {
    font-size: 14px;
}

:deep(.ant-input-number) {
    border-radius: 8px;
    margin-right: 10px;
}
</style>