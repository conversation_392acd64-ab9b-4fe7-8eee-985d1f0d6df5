<template>
  <a-modal
    class="paper-type-modal"
    v-model:visible="visible"
    title="查看考试邀请码"
    width="700px"
    style="margin-top: 100px"
    :footer="null"
    :maskClosable="false"
    @cancel="close"
  >
    <div style="font-size: 12px;color: #626262;margin-bottom: 20px;">
      <span style="margin-right: 20px;">考试名称</span>
      <span>{{ papername }}</span>
    </div>
    <a-table
      class="paper-table"
      :columns="columns"
      :rowKey="(record:any) => record.id"
      :data-source="inviteList"
      :locale="{ filterConfirm: '确定', filterReset: '重置', emptyText: '暂无数据' }"
      :pagination="paginationConfig"
      @change="handleTableChange"
      :scroll="{ x: 400 }"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <span :class="['code-name', { active: record.status === 0 }]" @click="copy(record)"
            >{{ record.name }}
            <svg-icon v-if="record.status === 1" name="copy" style="margin-bottom: -1px" />
          </span>
        </template>
        <template v-if="column.key === 'status'">
          <div style="display: flex; align-items: center;">
            <span :class="[['status_gray', 'status_green'][record.status]]"></span>
            <span> {{ ['无效', '有效'][record.status] ?? record.status }} </span>
          </div>
        </template>
        <template v-if="column.key === 'etime'">
          <div style="display: flex; align-items: center;">
            <span>{{record.etime ? record.etime.replace(',','至') : '-' }}</span>
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <span>
            <a-popconfirm
              title="确定作废该邀请码？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="record.status === 0"
              @confirm="handleConfirm(record.id)"
            >
              <a-button
                :disabled="record.status === 0"
                type="link"
                style="font-size: 14px; color: #7fa0fa; padding-left: 0"
                >作废</a-button
              >
            </a-popconfirm>
          </span>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup lang="ts">
import { invitecodeList } from '@/api/admin/invitecodeManage'
import { papertoken, papertokenDelete } from '@/api/admin/paperManage'
import { message } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import clipboard3 from 'vue-clipboard3'

const props = defineProps({
  invitationCodeModalVisible: {
    type: Boolean,
    default: false
  },
  paper: {
    type: String
  },
  papername: {
    type: String
  },
  hiddenAction: {
    type: Boolean,
    default: false,
  }
})

const emits = defineEmits(['closeModal'])

const columns = ref([
  {
    title: '考试邀请码',
    dataIndex: 'name',
    key: 'name',
    width: 120,
    align: 'left',
    ellipsis: true,
    resizable: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    key: 'status',
    align: 'left',
    ellipsis: true,
    resizable: true,
    filters: [
      { text: '有效', value: 1 },
      { text: '无效', value: 0 },
    ],
  },
  {
    title: '使用数/容量',
    dataIndex: 'usedMax',
    width: 120,
    key: 'usedMax',
    align: 'right',
    ellipsis: true,
    resizable: true,
  },
  {
    title: '有效期',
    dataIndex: 'etime',
    width: 320,
    key: 'etime',
    sorter: true,
    align: 'left',
    ellipsis: true,
    resizable: true,
  },
  {
    title: '操作',
    fixed: 'right',
    key: 'action',
    align: 'left',
    width: 80,
  }
])
watch(() => props.hiddenAction, (value) => {
  console.log(value, 'value===')
  if (value) {
    columns.value = columns.value.filter(el => el.key !== 'action')
  }
}, {
  immediate: true,
})
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  order_field: 'etime',
  order_type: 'descend',
  size: 'small',
  status: null,
})

const handleTableChange = ({ pagination }: any, filters: any = {}, sort: any = {}) => {
  if (sort.field) {
    paginationConfig.value.order_field = sort.field
    paginationConfig.value.order_type = sort.order || 'descend'
  }
  // 处理筛选
  paginationConfig.value = {
    ...paginationConfig.value,
    ...filters,
  }
  // paginationConfig.value.current = pagination.current
  // paginationConfig.value.pageSize = pagination.pageSize
  getPaperList()
}

const inviteList = ref([])
const paperLoading = ref(false)
const getPaperList = () => {
  paperLoading.value = true
  let params = {
    action: 'query',
    basePager:{
      current: 1,
      size: 9999,
    },
    templeteUUID: props.paper,
    order_field: paginationConfig.value.order_field,
    order_type: paginationConfig.value.order_type,
    statusList: paginationConfig.value.status,
  }
  switch(paginationConfig.value.order_field){
    case "etime":
      params.etimeOrder = paginationConfig.value.order_type == 'ascend' ? "asc" : "desc"
      break;
  }
  papertoken(params)
    .then((res: any) => {
      inviteList.value = res.records
      paperLoading.value = false
    })
    .catch(() => {
      paperLoading.value = false
    })
}

const handleConfirm = (id: string) => {
  papertokenDelete({
    action: 'del',
    // ids: [id]
    id
  }).then(() => {
    message.success('邀请码删除成功!')
    getPaperList()
  })
}

const { toClipboard } = clipboard3()
const copy = (record: any) => {
  const text = `${record.name}`
  toClipboard(text)
  message.info('考试邀请码已成功复制至剪切板!')
}

const close = () => {
  emits('closeModal')
}

const visible = ref(false)

watch(
  () => props.invitationCodeModalVisible,
  (val) => {
    visible.value = val
    if (val) {
      getPaperList()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.paper-type-modal {
  .type-cards {
    :deep(.ant-form-item-label > label),
    .ant-form label {
      font-size: 12px;
      color: #626262;
    }
    :deep(.ant-col) {
      width: 100px;
    }
    .invitecode-input-wrapper {
      .ant-input {
        font-size: 12px;
        border-radius: 8px;
        height: 32px;
      }
      .desc {
        font-size: 12px;
        color: #b4b5ba;
        padding-top: 10px;
      }
    }
    .datetime-picker-wrapper {
      :deep(.ant-calendar-picker) {
        width: 100% !important;
      }
      :deep(.ant-calendar-picker-input.ant-input) {
        height: 32px;
        font-size: 12px;
        border-radius: 8px;
      }
    }
  }
  .btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 60px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      cursor: pointer;
      &:first-child {
        color: #fff;
        background: #5478ee;
        margin-right: 8px;
      }
    }
  }
}
</style>
<style lang="less">
.paper-type-modal .ant-modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #121633;
}
.paper-type-modal {
  .ant-modal-header,
  .ant-modal-body {
    padding-left: 32px !important;
    padding-right: 32px !important;
  }
  .ant-table-thead > tr > th {
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }

  .ant-table-tbody > tr > td {
    padding: 10px;
    font-family: PingFang HK;
    font-size: 14px;
    color: #121633;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-input {
    border-radius: 8px;
    height: 32px;
    font-size: 12px;
  }
  .code-name {
    cursor: pointer;
    &.active {
      text-decoration: line-through;
    }
  }
}
</style>
