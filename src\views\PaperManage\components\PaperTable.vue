<template>
  <div class="paper-table">
    <a-table
      :columns="data.columns"
      :rowKey="(record:any) => record.id"
      :data-source="data.paperList"
      :row-selection="{
        selectedRowKeys: selectedPaper,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        getCheckboxProps: getCheckboxProps
      }"
      :loading="data.paperLoading"
      :customRow="customRow"
      :scroll="{ x: 1200 }"
      :pagination="props.pagination"
      @change="handleTableChange"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div style="display: flex">
            <span
              :title="record.name"
              style="
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-right: 8px;
              "
            >
              {{ record.name }}
            </span>
            <a-tag v-if="record.recover" color="green">已恢复</a-tag>
          </div>
        </template>
        <template v-if="column.key === 'type'">
          <span>
            {{ getTypeText(record.type) }}
          </span>
        </template>
        <template v-if="column.key === 'startTime'">
          <span>
            {{ record.startTime?.slice(0, 16) + ' 至 ' + record.endTime?.slice(0, 16) }}
          </span>
        </template>
        <template v-else-if="column.key === 'duration'">
          <span>
            <span> {{ record.duration }}min </span>
          </span>
        </template>
        <template v-else-if="column.key === 'status'">
          <span v-html="getStatusHtml(record.status)"> </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-dropdown :trigger="['click']" @visibleChange="handleClick($event, record)">
              <span>考生 <caret-down-outlined /> </span>
              <template #overlay>
                <a-menu class="paper-dropdown">
                  <a-menu-item v-if="record.status !== 1" @click="allocatePaper(record)">
                    <span>关联考生</span>
                  </a-menu-item>
                  <a-menu-item v-if="record.status !== 1" @click="sendEmail(record)">
                    <span>发送邀请邮件</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="record.status !== 1"
                    :disabled="record.disabled"
                    :title="record.disabled ? '该场考试已经超过最晚进入时间，不可创建邀请码' : ''"
                    @click="createPaperCode(record)"
                  >
                    <span>创建考试邀请码</span>
                  </a-menu-item>
                  <a-menu-item v-if="record.status !== 1" @click="getPaperCodeList(record)">
                    <span>查看考试邀请码</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      record.status === 1 ||
                      record.status === 2 ||
                      record.status === 0 ||
                      (record.uniexam === '0' && record.status !== -1)
                    "
                    @click="getStuGrade(record.id, record.name)"
                  >
                    <span>查看成绩</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="store.getters.permiss.papermag.proctor && record.status !== 1 && moment().isAfter(moment(record.startTime).subtract(1, 'hour'))"
                    @click="getPaperStatus(record)"
                  >
                    <span>监考</span>
                  </a-menu-item>
                  <!-- <a-menu-item v-if="record.status === 1" @click="publishExam(record)">
                    <span>发布成绩</span>
                  </a-menu-item> -->
                </a-menu>
              </template>
            </a-dropdown>
            <a-divider type="vertical" />
            <a-dropdown :trigger="['click']">
              <span>更多操作 <caret-down-outlined /> </span>
              <template #overlay>
                <a-menu class="paper-dropdown">
                  <a-menu-item @click="previewPaper(record.id)">
                    <span>查看试卷</span>
                  </a-menu-item>
                  <a-menu-item @click="previewPaperConfig(record)">
                    <span>查看配置</span>
                  </a-menu-item>
                  <a-menu-item @click="imitationExam(record)">
                    <span>模拟考试</span>
                  </a-menu-item>
                  <a-menu-item v-if="record.status !== 1" @click="extendPaperTime(record)">
                    <span>延长考试</span>
                  </a-menu-item>
                  <a-menu-item @click="joinMarkPaper(record)">
                    <span>联合阅卷</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="store.getters.permiss.papermag.write"
                    @click="copyPaper(record)"
                  >
                    <span>克隆</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="store.getters.permiss.papermag.write"
                    :disabled="isDisabled(record)"
                    @click="editPaper(record)"
                  >
                    <span>编辑</span>
                  </a-menu-item>
                  <a-menu-item
                    v-if="
                      store.getters.permiss.papermag.write &&
                      store.getters.userid === record.teacher &&
                      record.status !== 0
                    "
                  >
                    <span style="display: block" @click="deletePaper(record.id)">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, createVNode } from 'vue'
import { Paper } from '@/models/paperModel'
import { delTestPaper, publishExamScore, mockexam, beforeclonepaper } from '@/api/admin/paperManage'
import { Modal, message } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import router from '@/router'
import { useStore } from 'vuex'
import moment from 'moment'
import { CaretDownOutlined, InfoCircleOutlined, InfoCircleFilled, ExclamationCircleFilled } from '@ant-design/icons-vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  pagination: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits([
  'getPaperList',
  'editPaper',
  'previewPaper',
  'previewPaperConfig',
  'downloadPaper',
  'allocatePaper',
  'sendEmail',
  'handleTableChange',
  'updateSelectedPaper',
  'joinMarkPaper',
  'createPaperCode',
  'copyPaper',
  'getPaperCodeList',
  'extendPaperTime'
])

const store = useStore()

// 判断试题是否在9月之后创建的
function isCreateAfter(createDate: any, date = '2023-09-01') {
  return moment(createDate).isAfter(date)
}

const getTypeText = (type: string) => {
  if (type === 'regular') return '指定试题'
  if (type === 'drawer') return '指定范围'
}
const getStatusHtml = (status: number) => {
  if (status === -1) return '<span class="unstart-status"></span> 未开始'
  if (status === 0) return '<span class="ing-status"></span> 考试中'
  if (status === 1) return '<span class="ended-status"></span> 已结束'
}
const joinMarkPaper = (paper: Paper) => {
  emits('joinMarkPaper', paper)
}

// 按生成时间排序
const handleTableChange = (pagination: any, filters: any, sort: any) => {
  emits('handleTableChange', { pagination, filters, sort })
}

const selectedPaper = <any>ref([])
// const onSelectChange = (selectedRowKeys: (string | number)[]) => {
//   // selectedPaper.value.length = 0
//   // selectedPaper.value.push(...selectedRowKeys)
//   // emits('updateSelectedPaper', selectedPaper.value)
// }
const onSelect = (record: any, selected: boolean) => {
  if (!selected) {
    // 取消勾选,删除对应的数组项
    selectedPaper.value.map((item: any, index: number) => {
      if (item === record.id) {
        selectedPaper.value.splice(index, 1)
      }
    })
  }
  if (selected) {
    // 点击勾选,添加到selectedPaper数组
    selectedPaper.value.push(record.id)
  }
}
const onSelectAll = (selected: boolean, selectedRows: any, changeRows: any) => {
  if (selected) {
    changeRows.map((item: any) => {
      selectedPaper.value.push(item.id)
    })
  }
  if (!selected) {
    changeRows.map((item: any) => {
      selectedPaper.value.map((x: any, indey: number) => {
        if (item.id === x) {
          selectedPaper.value.splice(indey, 1)
        }
      })
    })
  }
}

const getCheckboxProps = (record: any) => {
  return {
    disabled:
      !store.getters.permiss.papermag.write ||
      !(store.getters.userid === record.teacher) ||
      record.status === 0,
    selectedRowKeys: selectedPaper.value.includes(record.id)
  }
}

const customRow = (record: any, index: number) => {
  return {
    onMouseenter: () => {
      record.active = true
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = '#3158BD'
    },
    onMouseleave: () => {
      record.active = false
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = ''
    }
  }
}

const handleClick = (visible, record) => {
  if (visible) {
    const { startTime, endTime, limitlateness, duration, uniexam } = record
    if (uniexam === '1') {
      if (limitlateness > 0) {
        record.disabled = new Date(startTime).getTime() + limitlateness * 60 * 1000 < Date.now()
      } else {
        record.disabled = new Date(endTime).getTime() < Date.now()
      }
    } else {
      record.disabled = new Date(endTime).getTime() - duration * 60 * 1000 < Date.now()
    }
  }
}

// 关联考生
const allocatePaper = (record: Paper) => {
  // emits('allocatePaper', paper)
  router.push({ path: 'paperAllocate', query: { id: record.id, name: record.name } })
}
// 发送邮箱邀请
const sendEmail = (record: any) => {
  router.push({ path: 'paperEmailSend', query: { id: record.id, name: record.name } })
}
// 创建考试邀请码
const createPaperCode = (record: any) => {
  emits('createPaperCode', record)
}
const getPaperCodeList = (record: any) => {
  emits('getPaperCodeList', record)
}

// 编辑试卷
const editPaper = (paper: any) => {
  // const nowDate = new Date().getTime()
  // const startTime = new Date(paper.startTime).getTime()
  // if (nowDate >= startTime) {
  //   message.warning('试卷仅在考试开始前可以编辑!')
  //   return
  // }
  emits('editPaper', paper)
}
// 克隆试卷
async function copyPaper(paper: any) {
  if (isCreateAfter(paper.create_at)) {
    try {
      let hasDeletedQuestions = await beforeclonepaper({ templeteId: paper.id })
      if (!hasDeletedQuestions) {
        emits('copyPaper', paper)
      } else {
        let h = createVNode
        Modal.info({
          title: '试卷内容变更提醒',
          content: '因部分题目被删除，试卷内容已自动更新。',
          icon: () => h(InfoCircleOutlined),
          onOk() {
            emits('copyPaper', paper)
          },
          okText: '确认'
        })
      }
    } catch (error) {
      // message.error('克隆出错，请联系管理员')
    }
  } else {
    message.warning(
      '由于系统更新，暂时无法兼容此试卷的格式。克隆失败，给您造成不便，请谅解。请您重新创建试卷。'
    )
  }
}

// 删除试卷
const deletePaper = (id: string) => {
  Modal.confirm({
    title: '确定删除该考试？',
    icon: () => createVNode(ExclamationCircleFilled),
    onOk() {
      delTestPaper({ action: 'del', ids: [id] }).then(() => {
        message.success('删除成功！')
        emits('getPaperList')
      })
    }
  })
}

// 预览试卷
const previewPaper = (paper: Paper) => {
  emits('previewPaper', paper)
}

// 查看配置
const previewPaperConfig = (record: any) => {
  emits('previewPaperConfig', record)
}

// 模拟考试
const imitationExam = (record: any) => {
  let baseUrl: string
  if (location.href.includes('localhost')) {
    baseUrl = 'https://localhost:8888/'
  } else if (location.href.includes('teacher-preview')) {
    baseUrl = 'https://preview.exam.isrc.ac.cn/'
  } else {
    baseUrl = 'https://exam.isrc.ac.cn/'
  }
  mockexam({ templeteId: record.id }).then((res: any) => {
    window.open(
      `${baseUrl}#/pretest?id=${res.stupid}&stuid=${res.stuid}&token=${res.access_token}&stuname=${res.stuname}`,
      '_blank'
    )
  })
}

// // 下载试卷
// const downloadPaper = (paper: Paper) => {
//   emits('downloadPaper', paper)
// }

const extendPaperTime = (paper: Paper) => {
  emits('extendPaperTime', paper)
}

const getPaperStatus = (paper: any) => {
  router.push({
    path: 'stuMonitor',
    query: {
      id: paper.id,
      phoneMonitor: paper.phonemonitor,
      computerMonitor: paper.computermonitor,
      name: paper.name
    }
  })
}

const getStuGrade = (id: string, name: string) => {
  router.push({ path: 'examManagement', query: { id, name, page: props.pagination.current } })
}

const isDisabled = (record: any) => {
  if (record.name === '考前流程模拟') return false
  return new Date(record.startTime).getTime() - Date.now() - 3 * 60 * 1000 > 0 ? false : true
}

const publishExam = (record: any) => {
  Modal.confirm({
    title: `确认发布考试《${record.name}》的成绩给所有学生吗? `,
    icon: () => createVNode(InfoCircleFilled),
    content: '发布之后无法撤回。',
    onOk() {
      publishExamScore({ paper: record.id, publish: true }).then(() => {
        message.success('成绩发布成功!')
      })
    }
  })
}

watch(
  selectedPaper,
  (val) => {
    emits('updateSelectedPaper', JSON.parse(JSON.stringify(val)))
  },
  { deep: true }
)
</script>

<style lang="less">
.paper-table {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-table-thead > tr > th {
    text-align: center;
    font-weight: bold;
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 14px !important;
      font-weight: bold;
      color: #121633;
    }
    // .ant-table-header-column .ant-table-column-sorters:hover::before {
    //   background: none;
    // }
  }

  .ant-table-tbody > tr > td {
    padding: 10px;
    font-family: PingFang HK;
    font-size: 14px;
    color: #121633;
    text-align: center;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
  .ant-btn {
    font-size: 14px;
  }
}
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.arrow-icon {
  position: relative;
  transform: rotate(180deg);
  bottom: -2px;
  left: 2px;
}
.paper-dropdown {
  .ant-dropdown-menu-title-content {
    font-size: 12px !important;
  }
}
.unstart-status,
.ing-status,
.ended-status {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}
.unstart-status {
  background: #52c41a;
}
.ing-status {
  background: #ff4d4f;
}
.ended-status {
  background: #d9d9d9;
}
</style>
<style lang="less" scoped>
.anticon-caret-down {
  transition: all ease 0.2s;
}
.ant-dropdown-open {
  .anticon {
    transform: rotate(180deg);
  }
}
</style>
