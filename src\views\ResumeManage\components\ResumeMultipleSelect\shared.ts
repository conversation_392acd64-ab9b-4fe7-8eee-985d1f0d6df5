/**
 * 左侧的简历列表数据。
 * 本来是 props 传入的，但是因为父子孙组件，以及需要调用接口后，再返回出来更改
 * 嵌套太过于严重，所以重构出来
 * @description 用于筛选中，展示简历列表数据
 * @example
 */
export const resumeListDataInFilter = ref<{
  resumeId: string
  matchScore: number
  candidateName: string
  // ...其他字段
  [key: string]: any
}[]>([])

/**
 * 当前选中的简历 resumeId
 * @description 用于筛选中，展示简历列表数据
 * @example
 */
export const currentResumeId = ref(resumeListDataInFilter.value[0]?.resumeId)