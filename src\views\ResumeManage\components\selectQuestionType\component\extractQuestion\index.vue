<script lang="ts" setup>
import { nextTick, ref } from 'vue'
import { useStore } from 'vuex'
import QuestionList from './questionList.vue'
import SubjectTree from './subjectTree.vue'

const props = withDefaults(defineProps<{
  defaultType?: number | null
  defaultSelectedRows?: any[]
  defaultExcludedRowKeys?: string[]
  disabledRowKeys?: string[]
  initialOptions?: number // 选做题量
  allSelectRows?: any[]
  optionsDisabled?: boolean
  contentList?: any
}>(), {
  initialOptions: 0,
  optionsDisabled: true,
})

const emits = defineEmits<{
  (e: 'confirm', value: { addList?: string[], delList?: string[] }, options: number): void
  (e: 'close'): void
  (e: 'removeQuestion', id: string): void
}>()

function removeQuestion(id: string) {
  emits('removeQuestion', id)
}
const store = useStore()

const listRef = ref<InstanceType<typeof QuestionList>>()
const options = ref(props.initialOptions)

// 题库树
const subjectId = ref('')
const subjectPath = ref('')
const subjectUUID = ref('')
const selectItem = ref(null)
const isOfficialSubject = ref(!!store.getters.userInfo.officialCert)
function handleSelectSubject(selecItem: any) {
  subjectId.value = selecItem.id
  subjectPath.value = selecItem.path
  selectItem.value = selecItem
}
async function handleTreeVisibleChange() {
  if (!listRef.value) 
    return
  await nextTick()
  listRef.value.layoutTags()
}

// 列表选择后更新左侧题库树统计数字
const treeRef = ref<InstanceType<typeof SubjectTree>>()
const selectedRows = ref<any[]>(props.defaultSelectedRows || [])
function handleQuestionListSelect(record: any, selected: boolean) {
  if (selected) {
    selectedRows.value.push(record)
    treeRef.value?.addItemToCatgsQuesNumMap(record)
  }
  else {
    selectedRows.value = selectedRows.value.filter(i => i.id !== record.id)
    emits('removeQuestion', record.id)
    treeRef.value?.removeItemFromCatgsQuesNumMap(record)
  }
}
function getSelectQuestions() {
  return toRaw(listRef.value?.getSelectRows().value)
}
onMounted(() => {
  if (props.defaultSelectedRows?.length) {
    props.defaultSelectedRows.forEach((record) => {
      handleQuestionListSelect(record, true)
    })
  }
})
defineExpose({
  getSelectQuestions,
  clearTableSelection: () => listRef.value?.clearTableSelection(),
})
</script>

<template>
  <div class="question-manage-container">
    <div class="question-manage-content">
      <div class="questions-subjects">
        <SubjectTree
          ref="treeRef" 
          :createable="false"
          :footer="false"
          @select="handleSelectSubject"
          @visible-change="handleTreeVisibleChange"
        />
      </div>
      <div class="questions-list">
        <QuestionList
          ref="listRef"
          :subject-id="subjectId" 
          :default-selected-rows="selectedRows"
          :default-excluded-row-keys="defaultExcludedRowKeys" 
          :default-type="defaultType"
          :disabled-row-keys="disabledRowKeys" 
          :writable="true"
          :operation-flag="true" 
          :subject-path="subjectPath" 
          :select-item="selectItem" 
          :subject-u-u-i-d="subjectUUID" 
          :is-official-subject="isOfficialSubject"
          @select="handleQuestionListSelect"
          @remove-question="removeQuestion"
        />
      </div>
    </div>
  </div>
</template>
  
<style lang="less" scoped>
.question-manage-container {
    @media screen and (max-width: 1440px) {
      height: 400px;
    }
    @media screen and (min-width: 1441px) {
      height: 620px;
    }
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin-bottom:16px;

    .question-manage-title {
        line-height: 48px;
        font-size: 14px;
        font-weight: 600;
    }

    .question-manage-content {
        overflow: hidden;
        flex: 1;
        display: flex;

        .questions-subjects {
            top: 96px;
            bottom: 22px;
            z-index: 2;
            background-color: #fff;
            flex-shrink: 0;
            border: 1px solid #e8e8e8;
        }

        .questions-list {
            background-color: #fff;
            flex: 1;
            min-width: 530px;

            .locked-box {
                height: 100%;

                .locked {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: center;
                    align-items: center;

                    .desc {
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.45);
                        line-height: 20px;
                    }
                }
            }
        }
    }
}

.btn-group {
    display: flex;
    align-items: center;
    justify-content: center;

    .ant-btn {
        border-radius: 8px;

        &:first-child {
            margin-right: 8px;
        }
    }
}
</style>