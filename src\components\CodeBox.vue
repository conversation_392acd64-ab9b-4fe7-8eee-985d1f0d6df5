<template>
    <div class="code-box">
        <div class="tool-bar">
            <span>
                <template v-if="attrs.language">该考生使用</template>
                {{ attrs.language }}
                <template v-if="attrs.language">语言</template>
            </span>
            <span v-if="!copied" @click="handleCopy" style="cursor: pointer;">
                <CopyOutlined />
                复制
            </span>
            <span v-else>
                <CheckOutlined />
                复制成功!
            </span>
        </div>
        <highlightjs v-bind="attrs" autodetect />
    </div>
</template>

<script lang="ts" setup>
import { CopyOutlined, CheckOutlined } from '@ant-design/icons-vue'
import useClipboard from 'vue-clipboard3'
import { ref, useAttrs } from 'vue'

const attrs = useAttrs()
const { toClipboard } = useClipboard()

const copied = ref(false)
async function handleCopy() {
    try {
        await toClipboard(attrs.code as string)
        copied.value = true
        setTimeout(() => {
            copied.value = false
        }, 3000)
    } catch (error) {
        console.log(error)
    }
}

</script>

<style lang="less" scoped>
.code-box {
    overflow: hidden;
    position: relative;

    .tool-bar {
        font-size: 12px;
        height: 32px;
        background-color: rgb(52, 53, 65);
        color: rgb(217, 217, 227);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        transition: all 0.1s ease-in-out;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        font-weight: bold;
    }
    :deep(.hljs) {
        background: #000;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
    }
}
</style>