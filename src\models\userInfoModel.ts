export interface UserInfo {
  username: string
  avatar: string | null
  id_card_num: string
  graduated_school: string | null
  introduce: string | null
  phone: string
  email: string
  sex: string
  age: number
  teacher_id: string
  group: string
  resume: Record<string, any>
  roles: Array<string> | string
  newPassword: string
  fidcard: string
  bidcard: string
}

export interface Teacher {
  id: string
  name: string
}
