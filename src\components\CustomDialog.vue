<template>
  <div class="custom-dialog-container">
    <a-modal
      class="custom-dialog"
      :width="isFullScreen ? '100%' : ''"
      :wrap-class-name="isFullScreen ? 'full-screen-modal' : ''"
      v-model:visible="visible"
      :title="txtConfig.title"
      v-bind="$attrs"
      @ok="handleOk"
      @cancel="handleClose"
    >
      <template v-for="(item, key, index) in $slots" :key="index" v-slot:[key]>
        <slot :name="key"></slot>
      </template>
      <!-- <slot></slot> -->
      <template #footer>
        <a-button
          v-if="!needDoubleConfirmation"
          type="primary"
          :loading="loading"
          :disabled="disabled"
          @click="handleOk"
          >{{ txtConfig.confirm }}</a-button
        >
        <a-popconfirm
          v-else
          :title="`确定${txtConfig.confirm}？`"
          ok-text="确定"
          cancel-text="取消"
          @confirm="handleOk"
          :overlayInnerStyle="{ color: 'red' }"
        >
          <a-button type="primary" :loading="loading">{{ txtConfig.confirm }}</a-button>
        </a-popconfirm>
        <a-button v-if="showCancelBtn" @click="handleCancel"
          >{{ txtConfig.cancel || '取消' }}
        </a-button>
        <slot name="extra-btn"></slot>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { color } from 'echarts'
import { ref, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    dialogVisible?: boolean
    txtConfig?: any
    isFullScreen?: boolean
    loading?: boolean
    needDoubleConfirmation?: boolean
    disabled?: boolean
    showCancelBtn?: boolean
  }>(),
  {
    dialogVisible: false,
    txtConfig: () => ({}),
    isFullScreen: false,
    loading: false,
    disabled: false,
    needDoubleConfirmation: false,
    showCancelBtn: true,
  }
)

const emits = defineEmits(['updateDialog', 'closeDialog', 'cancelDialog'])

const handleOk = () => {
  emits('updateDialog')
}

const handleClose = () => {
  emits('closeDialog')
}
const handleCancel = () => {
  emits('cancelDialog')
}

const visible = ref(false)
watch(
  () => props.dialogVisible,
  (val) => {
    visible.value = val
  }
)
</script>

<style lang="less">
.custom-dialog {
  .ant-modal-content {
    border-radius: 8px !important;
  }
  .ant-modal-close-x {
    height: 70px;
    width: 70px;
    line-height: 90px;
  }
  .ant-modal-header {
    padding-top: 32px;
    padding-left: 32px;
    padding-right: 32px;
    border-radius: 8px !important;
    border: none;
  }
  .ant-modal-title {
    font-size: 20px;
    color: #121633;
    font-weight: bold;
  }
  .ant-modal-footer {
    border-radius: 8px;
    border: none;
    text-align: center;
    padding-bottom: 30px;
    .ant-btn {
      height: 32px;
      font-size: 14px;
      border-radius: 8px;
    }
  }
  .ant-modal-body {
    padding-bottom: 0;
    padding-left: 32px;
    padding-right: 32px;
  }
}
</style>
