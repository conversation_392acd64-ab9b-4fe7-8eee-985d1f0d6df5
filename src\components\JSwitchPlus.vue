<template>
    <div class="j-switch" :class="{checked, expanded, disabled}" @click="emitChange">
        <CheckOutlined v-if="checked" style="color: #fff;" />
        <CloseOutlined v-else style="color: #fff;" />
        <div class="switch-handle"></div>
    </div>
</template>

<script lang="ts" setup>
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'

const props = withDefaults(defineProps<{
    checked: boolean
    expanded: boolean
    disabled?: boolean
}>(),{
    checked: false,
    expanded: true,
    disabled: false
})

const emits = defineEmits(['update:checked', 'change'])

function emitChange () {
    if (props.disabled) return
    let checked = !props.checked
    emits('update:checked', checked)
    emits('change', checked)
}

</script>

<style lang="less" scoped>
.j-switch {
    display: flex;
    justify-content: space-around;
    align-items: center;
    vertical-align: middle;
    position: relative;
    width: 20px;
    height: 20px;
    background-color: #FF4D4F;
    border-radius: 100px;
    font-size: 12px;
    transition: all ease 0.2s;
    cursor: auto;
    &:not(.disabled) {
        cursor: pointer;
    }

    &.expanded:not(.disabled) {
        width: 40px;
        .switch-handle {
            width: 16px;
            height: 16px;
        }
    }
    &.checked {
        background-color: #52C41A;
        .switch-handle {
            left: 22px;
        }
    }
    .anticon {
        position: absolute;
        &.anticon-check {
            left: 4px;
        }
        &.anticon-close {
            right: 4px;
        }
    }

    .switch-handle {
        width: 0;
        height: 0;
        border-radius: 50%;
        background-color: #fff;
        position: absolute;
        left: 2px;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.1s ease-in-out;
    }
}

</style>