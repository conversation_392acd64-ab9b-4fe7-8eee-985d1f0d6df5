<script setup lang="ts">
import { motion, useMotionTemplate, useMotionValue } from 'motion-v'

const cardRef = ref<HTMLDivElement | null>(null)
const gradientSize = 200
const gradientFrom = '#9E7AFF'
const gradientTo = '#FE8BBB'
const mouseX = useMotionValue(0)
const mouseY = useMotionValue(0)
onMounted(() => {
  if (cardRef.value) {
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseout', handleMouseOut)
    document.addEventListener('mouseenter', handleMouseEnter)
  }
})

function handleMouseMove(e: MouseEvent) {
  if (cardRef.value) {
    const { left, top } = cardRef.value.getBoundingClientRect()
    const clientX = e.clientX
    const clientY = e.clientY
    mouseX.set(clientX - left)
    mouseY.set(clientY - top)
  }
}

function handleMouseOut() {
  mouseX.set(-gradientSize)
  mouseY.set(-gradientSize)
}

function handleMouseEnter() {
  mouseX.set(-gradientSize)
  mouseY.set(-gradientSize)
}
</script>

<template>
  <div 
    ref="cardRef" 
    class="!bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 relative min-h-[360px]"
  >
    <motion.div
      class="pointer-events-none z-1 absolute inset-0 rounded-[inherit] bg-border duration-300 group-hover:opacity-100"
      :style="{
        background: useMotionTemplate`
          radial-gradient(${gradientSize}px circle at ${mouseX}px ${mouseY}px,
          ${gradientFrom}, 
          ${gradientTo}, 
          #e5e5e5 100%
        )`,
      }"
    />
    
    <div class="absolute top-[2px] left-[2px]  w-[calc(100%-4px)] h-[calc(100%-4px)] bg-white z-10 rounded-lg p-6 flex flex-col justify-center items-center">
      <h1 class="text-2xl font-bold">
        Magic Card
      </h1>
    </div>
  </div>
</template>

<style scoped>

</style>
