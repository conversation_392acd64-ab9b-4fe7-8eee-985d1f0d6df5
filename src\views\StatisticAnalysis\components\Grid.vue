<template>
  <div class="grid">
    <template v-for="item in 228" :key="item">
      <div></div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.grid {
  position: absolute;
  z-index: 1;
  top: 10px;
  left: 10px;
  bottom: 0;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: 32px;
  grid-row-gap: 8px;
  grid-column-gap: 20px;
}
.grid div {
  height: 32px;
  background-color: #e9eaec;
  // border: 1px dashed red;
}
</style>
