<template>
    <a-spin :spinning="loading">
        <div class="option-generator">
            <a-alert class="tip" type="info" show-icon>
                <template #icon><InfoCircleFilled style="font-size: 16px;" /></template>
                <template #message>
                    温馨提示：AI生成的干扰项仅供参考。如果您对当前生成的选项不满意，可以点击【换一批】按钮，系统将为您重新生成新的选项。
                </template>
            </a-alert>
            <div style="font-size: 16px;font-weight: bold;">以下是根据题干为您生成的干扰项：</div>
            <ul class="option-wrapper">
                <li v-for="item in list">
                    <a-checkbox v-model:checked="item.checked">{{ item.content }}</a-checkbox>
                </li>
            </ul>
            <a-button class="refresh-btn" type="link" @click="getList(true)">换一批</a-button>
        </div>
    </a-spin>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { generateOptions } from '@/api/exam/index'
import { ExclamationCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'

const props = defineProps<{
    generateParams: {
        type: number
        body: string,
        options: any[]
    }
}>()

const list = ref<{ content: string; isTrue: boolean; checked: boolean }[]>([])
const loading = ref(false)
async function getList(retry = false) {
    loading.value = true
    try {
        let res = await generateOptions({
            ...props.generateParams,
            limit: 5,
            isTrue: false,
            retry
        })
        list.value = res.map(i => ({ ...i, checked: false }))
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}
getList()

function setList(val: any) {
    list.value = val
}

defineExpose({ list, setList })

</script>

<style lang="less" scoped>
.option-generator {
    .option-wrapper {
        display: flex;
        flex-direction: column;
        :deep(.ant-checkbox-wrapper ){
            margin-top: 12px;
            width: 100%;
            > span:last-child {
                flex: 1;
                min-width: 0;
            }
        }
    }
    .tip{
        margin-bottom: 16px;
    }
    .refresh-btn {
        margin-top: 8px;
        font-size: 14px;
        padding: 0;
    }
}

.ant-alert {
    align-items: baseline;
    .ant-alert-icon{
        position: absolute;
        margin-top: 4px;
        svg{
            width: 16px !important;
            height: 16px !important;
        }
    }
    :deep(.ant-alert-content){
        padding-left: 22px;
    }
}
</style>