<template>
    <div ref="chartRef" style="height: 100%;"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { ECharts, init } from 'echarts'
import { QUESTIONS } from '@/config/constants'
import { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'

const props = defineProps<{
    list?: PaperFbodyItemModelWithQuestionChildren[]
}>()

function getData(list: PaperFbodyItemModelWithQuestionChildren[]) {
    try {
        let data: PaperFbodyItemModelWithQuestionChildren[] = []
        list?.forEach((item) => {
            let findItem = data.find((d) => d.type === item.type)
            if (findItem) {
                findItem.children.push(...item.children)
            } else {
                data.push(item)
            }
        })
        // 将数据根据QUESTIONS的顺序进行排序
        data.sort((a, b) => QUESTIONS.findIndex((q) => q.value === a.type) - QUESTIONS.findIndex((q) => q.value === b.type))
        data.forEach((item) => {
            item.name = item.title.slice(0, 2)
            item.value = item.children.length
            // 添加颜色
            item.itemStyle = { color: QUESTIONS.find((q: any) => q.value === item.type)?.color }
        })
        return data
    } catch (error) {
        console.log(error)
        return []
    }
}

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
    let cloneList = JSON.parse(JSON.stringify(props.list))
    if (!chartRef.value) return
    let data = getData(cloneList!)
    chart.value = init(chartRef.value)
    chart.value?.setOption({
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}道`
        },
        grid: {
            left: 0,
            right: 0,
            top: 0
        },
        series: [
            {
                type: 'pie',
                radius: '60%',
                minAngle: 10,
                label: {
                    alignTo: 'edge',
                    edgeDistance: 10,
                    formatter: '{b}: {d}%',
                },
                labelLine: {
                    length: 2,
                    length2: 4
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                data
            }
        ]
    })
}


watch(() => props.list, async (val) => {
    draw()
}, { deep: true })

onMounted(() => {
    try {
        setTimeout(() => {
            // 等待dom过渡效果完成
            draw()
        }, 500);
    } catch (error) {
        console.log(error)
    }
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}
</style>