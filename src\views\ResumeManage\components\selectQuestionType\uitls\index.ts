type TYPE = 'pkg' | 'question' | 'manual'
interface PKGITEM {
  type: TYPE
  id: string
  name: string
  total: number
  extractNum: number | string | any
}
interface MANUALQUESITEM {
  type: TYPE
  id: string
  isEdit: boolean
  configSchema: {
    content: string
  }
  recordDuration: any
  correctAnswer: {
    reference: string
  }
}
type CONTENTLIST = PKGITEM | MANUALQUESITEM | any

interface BACKENDCONTETNLIST {
  selectedQuestionBankList: Array<{
    path: string
    total: number
  }>
  selectedQuestionList: Array<{
    bankPath: string
    depth: string
    skewness: string
    difficulty: number
    complexFlag: number
    readFlag: string
    readContent: string
    videoUrl: string
    recordDuration: number
    templateCode: string
    content: string
    configSchema: Record<string, any>
    correctAnswer: Record<string, any>
  }>
  newQuestionList: Array<{
    content: string
    templateCode: string
    configSchema: Record<string, any>
    correctAnswer: Record<string, any>
  }>

}

export function requestParamsPipe(contentList: Array<CONTENTLIST>): BACKENDCONTETNLIST {
  const result: BACKENDCONTETNLIST = {
    selectedQuestionBankList: [],
    selectedQuestionList: [],
    newQuestionList: [],
  }

  contentList.forEach((item: CONTENTLIST, index: number) => {
    item.seq = index
    if (item.type === 'pkg') {
      result.selectedQuestionBankList.push(item)
    }
    else if (item.type === 'manual') {
      // item.recordDuration = Number(item.recordDuration * 60)
      result.newQuestionList.push(item)
    }
    else if (item.type === 'question') {
      // item.recordDuration = Number(item.recordDuration * 60)
      result.selectedQuestionList.push(item)
    }
  })

  return result
}