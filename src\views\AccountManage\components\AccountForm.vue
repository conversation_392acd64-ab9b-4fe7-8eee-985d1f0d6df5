<template>
  <a-form
    class="account-form"
    ref="formRef"
    :model="formState"
    :rules="rules"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
  >
    <a-form-item v-show="props.formData!.id" ref="id" label="ID" name="id" :colon="false">
      <a-input v-model:value="formState.id" disabled placeholder="请输入" />
    </a-form-item>
    <a-form-item ref="name" label="姓名" name="username" :colon="false">
      <a-input v-model:value="formState.username" placeholder="请输入" />
    </a-form-item>
    <a-form-item ref="sex" label="性别" name="sex" :colon="false">
      <a-radio-group v-model:value="formState.sex">
        <a-radio value="male">男</a-radio>
        <a-radio value="female">女</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item ref="dept" label="部门" name="dept_id" :colon="false">
      <a-tree-select
        v-model:value="formState.dept_id"
        :tree-data="treeData"
        show-search
        allow-clear
        placeholder="选择部门"
        :replaceFields="replaceFields"
        treeNodeFilterProp="name"
        tree-default-expand-all
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
      >
        <template #title="{ name }">
          <span>{{ name }}</span>
        </template>
      </a-tree-select>
    </a-form-item>
    <a-form-item ref="group" label="小组" name="group" :colon="false">
      <a-select v-model:value="formState.group" placeholder="选择小组" allowClear>
        <a-select-option v-for="item in groups" :key="item.code" :value="item.code">{{
          item.name
        }}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item ref="email" label="邮箱" name="email" :colon="false">
      <a-input v-model:value="formState.email" placeholder="请输入" />
    </a-form-item>
    <a-form-item ref="phone" label="手机号" name="phone" :colon="false">
      <a-input v-model:value="formState.phone" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="">
      <div class="btn-groups">
        <a-button class="cancel-btn" @click="onCancel">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </div>
    </a-form-item>
  </a-form>
</template>
<script lang="ts" setup>
import { reactive, ref, UnwrapRef, onMounted, watch } from 'vue'
import { getDepList } from '@/api/admin/systemManage'
import { teacherGroup } from '@/api/login'
import { addAccount } from '@/api/admin/accountManage'

interface FormState {
  id: string
  username: string
  sex: string
  group: string
  dept_id: string
  email: string
  phone: string
}

const props = defineProps({
  formData: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['onsubmitSuccess', 'oncancelSuccess'])

const formRef = ref()
const groups = <any>ref([])
const formState: UnwrapRef<FormState> = reactive({
  id: '',
  sex: '',
  username: '',
  email: '',
  phone: '',
  group: '',
  dept_id: ''
})
const labelCol = { span: 4 }
const wrapperCol = { span: 20 }
const treeData = ref<any>([])
const replaceFields = { title: 'name', key: 'id', value: 'id' }
const rules = {
  username: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  group: [{ required: true, message: '请选择小组', trigger: 'change' }],
  dept_id: [{ required: true, message: '请选择部门', trigger: 'change' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
}

const resetFormState = () => {
  formState.id = ''
  formState.sex = ''
  formState.username = ''
  formState.email = ''
  formState.phone = ''
  formState.group = ''
  formState.dept_id = ''
}

const onSubmit = () => {
  return new Promise((resolve, reject) => {
    formRef.value
      .validate()
      .then(() => {
        if (!formState.id) {
          delete formState.id
        }
        const params = {
          action: formState.id ? 'modify' : 'add',
          teacher: { ...formState }
        }
        addTeacherAccount(params)
      })
      .catch((error: any) => {
        reject(error)
      })
  })
}

const onCancel = () => {
  emits('oncancelSuccess')
}

const addTeacherAccount = (params: Object) => {
  addAccount(params).then(() => {
    emits('onsubmitSuccess')
  })
}

const getDepData = () => {
  getDepList({ action: 'query' }).then((res) => {
    treeData.value = res
  })
}

watch(
  () => props.formData,
  (val) => {
    if (!Object.keys(val).length) {
      resetFormState()
    }
    Object.assign(formState, val)
  },
  {
    immediate: true
  }
)

onMounted(() => {
  getDepData()
  teacherGroup({ action: 'query' }).then((res: any) => {
    res.forEach((item: any) => {
      groups.value.push(item)
    })
  })
})
</script>

<style lang="less" scoped>
.account-form {
  .ant-input {
    font-size: 14px !important;
  }
  :deep(.ant-select) {
    font-size: 15px !important;
  }

  .btn-groups {
    display: flex;
    justify-content: center;
    margin: 20px auto 0;
    .cancel-btn {
      margin: 0 20px 0 60px;
    }
  }
}
</style>
