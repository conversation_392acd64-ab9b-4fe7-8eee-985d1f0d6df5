<template>
  <div class="resume-table">
    <a-table
      :columns="data.columns"
      :rowKey="(record:any) => record.id"
      :data-source="data.taskList"
      :loading="data.loading"
      :scroll="{ x: 1200 }"
      :pagination="props.pagination"
      @change="handleTableChange"
      @resizeColumn="(w: any, col: any) => col.width = w"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <span>
            {{ record.type === 'regular' ? '指定试题' : '指定范围' }}
          </span>
        </template>
        <template v-if="column.key === 'startTime'">
          <span>
            {{ record.startTime?.slice(0, 16) }}
          </span>
        </template>
        <template v-else-if="column.key === 'status'">
          <span>
            {{ record.status === true ? '已阅完' : '待阅卷' }}
          </span>
        </template>
        <template v-else-if="column.key === 'published'">
          <span>
            {{ record.published === true ? '已发布' : '未发布' }}
          </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a-button
              type="link"
              :disabled="!record.finished || record.status || record.published"
              class="operation left"
              @click="correctPaper(record)"
              >阅卷</a-button
            >
            <a-divider type="vertical" style="margin-left: 7px" />
            <a-button type="link" :disabled="!record.modifynum" class="operation right" @click="historyRecord(record)">历史记录</a-button>
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { needcorrected } from '@/api/admin/paperManage'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
  data: any
  pagination: any
  refreshFn: (...args: any) => void
}>()

const emits = defineEmits(['handleTableChange'])

const router = useRouter()
async function correctPaper(record: any) {
  // 查一下有没有题目可阅
  let res = (await needcorrected({ paper: record.id })) as any
  res = res.filter((item: any) => item.children?.length)
  if (res.length === 0) {
    // 如果没有题目可阅，则给提示
    message.warning('该场考试暂无题目需人工阅卷')
    // 如果已结束则提示后刷新列表
    if (record.exam_status === '已结束') props.refreshFn()
    return
  }
  router.push(`/admin/examManage/correctPaper?id=${record.id}&name=${record.name}&page=${props.pagination.current}`)
}

const historyRecord = ({ id, published, name }: any) => {
  router.push(`/admin/examManage/historyRecord?id=${id}&name=${name}&page=${props.pagination.current}&published=${published}`)
}

const handleTableChange = (pagination: any, filters: any, sort: any) => {
  emits('handleTableChange', { pagination, filters, sort })
}
</script>

<style lang="less">
.operation {
  width: 24px;
  height: 18px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: left;
  color: #5478ee;
  line-height: 18px;
}
.left {
  margin-right: 8px;
}

.right {
  margin-left: 8px;
}
.resume-table {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-table-thead > tr > th {
    text-align: center;
    font-weight: bold;
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }
  .ant-table-tbody > tr > td {
    padding: 10px;
    font-family: PingFang HK;
    font-size: 14px;
    color: #121633;
    text-align: center;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
  .ant-btn {
    font-size: 14px;
  }

  .ant-divider-vertical {
    margin: 0;
  }
}
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
.gray-style {
  color: #ccc;
}
.resume-modal {
  .ant-modal-title {
    font-size: 15px;
    color: #121633;
    font-weight: bold;
  }
  .ant-form-item-label > label {
    font-size: 15px;
    color: #121633;
  }
  .ant-input,
  .ant-select-selector,
  .ant-select-selection-item-content span {
    font-size: 13px;
    color: #121633;
  }
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
  }
  .ant-modal-footer {
    border: none;
    text-align: center;
    padding-bottom: 30px;
    .ant-btn {
      width: 90px;
      height: 36px;
      margin-left: 20px;
      font-size: 15px;
      &:first-child {
        color: #3158bd;
        border-color: #3158bd;
      }
    }
  }
  .ant-modal-body {
    padding-bottom: 0;
  }
}
</style>
