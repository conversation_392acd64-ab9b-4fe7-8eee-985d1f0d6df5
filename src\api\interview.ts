import { PageListParams } from '@/types/index';
import type { InterviewQuestionFormState, SelfUsefulSentenceDetail } from '@/types/interviewQuestion';
import request from '@/utils/http'

export const queryaiinterviewsubjcatgs = (data: { id?: string } = {}) => request({
    url: '/queryaiinterviewsubjcatgs',
    data
})

export const addaiinterviewsubjcatgs = (data: { name: string; parent: string; pname: string }): Promise<string> => request({
    url: '/addaiinterviewsubjcatgs',
    data
})

export const beforedelaiinterviewsubjcatg = (data: { id: string }) => request({
    url: '/beforedelaiinterviewsubjcatg',
    data
})

export const delaiinterviewsubjcatg = (data: { id: string }) => request({
    url: '/delaiinterviewsubjcatg',
    data
})

export const modifyaiinterviewsubjcatg = (data: { id: string; name: string }) => request({
    url: '/modifyaiinterviewsubjcatg',
    data
})

export const alteraiinterviewsubjcatgs = (data: { id: string; parent: string }) => request({
    url: '/alteraiinterviewsubjcatgs',
    data
})

export const queryaiinterviewquestion = (
    data: PageListParams<{
        body: string;
        category: string;
        notids: string[];
        types: string[] | null;
        current_catg: boolean;
    }>
): Promise<unknown> => request({
    url: '/queryaiinterviewquestion',
    data
});


export const addaiinterviewquestion = (data: { question: InterviewQuestionFormState }) => request({
    url: '/addaiinterviewquestion',
    data
})

export const modifyaiinterviewquestion = (data: { id: string; question: InterviewQuestionFormState }) => request({
    url: '/modifyaiinterviewquestion',
    data
})

export const delaiinterviewquestion = (data: { id: string[] }) => request({
    url: '/delaiinterviewquestion',
    data
})

export const aiinterviewquestioncontent = (data: { ques_id: string }) => request({
    url: '/aiinterviewquestioncontent',
    data
})

export const queryaiinterviewquestiontrash = (data: PageListParams<{
    title: string;
}>) => request({
    url: '/queryaiinterviewquestiontrash',
    data
})

export const delaiinterviewquestiontrash = (data: { ids: string[] }) => request({
    url: '/delaiinterviewquestiontrash',
    data
})

export const recoveraiinterviewquestiontrash = (data: { ids: string[] }) => request({
    url: '/recoveraiinterviewquestiontrash',
    data
})

export const queryaiinterviewdraftquestion = (data: PageListParams<{
    title: string;
    type?: number[]
}>) => request({
    url: '/queryaiinterviewdraftquestion',
    data
})

export const delaiinterviewdraftquestion = (data: { ids: string[] }) => request({
    url: '/delaiinterviewdraftquestion',
    data
})

export const addaiinterviewdraftquestion = (data: {
    question: InterviewQuestionFormState
}) => request({
    url: '/addaiinterviewdraftquestion',
    data
})

export const modifyaiinterviewdraftquestion = (data: {
    id: string
    question: InterviewQuestionFormState
}) => request({
    url: '/modifyaiinterviewdraftquestion',
    data
})

export const aiinterviewdraftquestioncontent = (data: {
    id: string
}) => request({
    url: '/aiinterviewdraftquestioncontent',
    data
})

export const queryaiinterviewquestionrecord = (data: {
    id: string
}) => request({
    url: '/queryaiinterviewquestionrecord',
    data
})

export const addaiinterviewquestionrecord = (data: {
    id: string
    teacher: string
    // 0 未校对 1 校对正确 2 校对错误
    proofreading: 0 | 1 | 2
    wrong_reason?: string
}) => request({
    url: '/addaiinterviewquestionrecord',
    data
})

export const getaiinterviewanswer = (data: {
    /** 题目id，如果传了题目id则会保存该答案结果 */
    question_id?: string
    /** 题型 */
    type: number
    /** 题干 */
    body: string
    /** 选项 */
    options?: {
        value: string
        content: string
    }[]
}): Promise<{
    answer: string
    explain: string
}> => request({
    url: '/getaiinterviewanswer',
    data
})

// 查询某一道题目的ai答题记录
export const getaiinterviewanswerrecord = (data: {
    /** 题目id */
    question_id: string
}): Promise<{
    answer: string
    explain: string
    ques: string
    update_at: string
    create_at: string
    create_by: string
}> => request({
    url: '/getaiinterviewanswerrecord',
    data
})

export const aiinterviewgeneratequestion = (data: {
    describe: string
    type: number
    limit: number
}) => request({
    url: '/aiinterviewgeneratequestion',
    data
})

export const previewvideo = (data: {
    question_id: string
}): Promise<string> => request({
    url: '/previewvideo',
    data
})

export const querycommonphrases = (): Promise<{
    system_common_phrases: string[]
    self_common_phrases: SelfUsefulSentenceDetail[]
}> => request({
    url: '/querycommonphrases'
})

export const addcommonphrases = (data: { phrases: string }) => request({
    url: '/addcommonphrases',
    data
})

export const modifycommonphrases = (data: { id: string; phrases: string }) => request({
    url: '/modifycommonphrases',
    data
})

export const delcommonphrases = (data: { id: string }) => request({
    url: '/delcommonphrases',
    data
})

export const addaiinterviewassistant = (data: { name: string, select_category_tiling: string[] }) => request({
    url: '/addaiinterviewassistant',
    data
})

export const delaiinterviewassistant = (data: { id: string }) => request({
    url: '/delaiinterviewassistant',
    data
})

export const beforemodifyaiinterviewassistant = (data: { id: string }) => request({
    url: '/beforemodifyaiinterviewassistant',
    data
})

export const modifyaiinterviewassistant = (data: { id: string, assistant: { name: string; select_category_tiling: string[] } }) => request({
    url: '/modifyaiinterviewassistant',
    data
})

export const queryaiinterviewassistant = (data: PageListParams<{
    name: string,
    start_time_range: string[]
}>) => request({
    url: '/queryaiinterviewassistant',
    data
})

export const aiinterviewcatgquestypecount = () => request({
    url: '/aiinterviewcatgquestypecount'
})

export const aiinterviewassistantcontent = (data: { id: string }) => request({
    url: '/aiinterviewassistantcontent',
    data
})

export const aiinterviewassistantselectedcatg = (data: { id: string }) => request({
    url: '/aiinterviewassistantselectedcatg',
    data
})

export const buildsubjcatgstree = (data: { id: string }) => request({
    url: '/buildsubjcatgstree',
    data
})

export const aiinterviewassistantrecommendques = (data: {
    /** 助手id */
    id: string
    select_category_tiling: string[]
    keys: string
}) => request({
    url: '/aiinterviewassistantrecommendques',
    data
})

export const aiinterviewassistantappendques = (data: {
    /** 助手id */
    id: string
    /** 追问题目的id */
    question_id: string
}) => request({
    url: '/aiinterviewassistantappendques',
    data
})

export const aiinterviewassistantrelatedques = (data: {
    /** 助手id */
    id: string
    /** 相关题目的id */
    question_id: string
}) => request({
    url: '/aiinterviewassistantrelatedques',
    data
})

export const addaiinterviewassistantrecord = (data: {
    assistant_id: string
    record: any[]
    uuid?: string
}) => request({
    url: '/addaiinterviewassistantrecord',
    data
})

export const queryaiinterviewassistantrecord = (data: {
    assistant_id: string
}) => request({
    url: '/queryaiinterviewassistantrecord',
    data
})

export const delaiinterviewassistantrecord = (data: {
    assistant_id: string
}) => request({
    url: '/delaiinterviewassistantrecord',
    data
})