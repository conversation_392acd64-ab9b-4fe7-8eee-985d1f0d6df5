<template>
    <div class="form-page">
        <div class="title-wrap">
            <div>
                <span style="cursor: pointer;color: rgba(0,0,0,0.45);" @click="router.back">面试助手 / </span>
                <span class="title">{{ ACTION[props.mode] }}面试助手</span>
            </div>
        </div>
        <a-form class="form" ref="formRef" :model="formState" :rules="rules" :hideRequiredMark="true" :colon="false"
            labelAlign="left">
            <a-form-item label="面试助手名称" name="name">
                <a-input style="width: 240px;" v-model:value="formState.name" autocomplete="off" placeholder="请输入" />
            </a-form-item>
        </a-form>
        <div class="bottom-panel">
            <div class="bottom-panel-main">
                <Tree ref="treeRef" @updateChecked="onUpdateChecked" @updateSingleChecked="onUpdateSingleChecked"></Tree>
                <div class="content">
                    <div class="content-title">已选题库范围</div>
                    <div class="content-wrapper">
                        <ul v-if="list.length">
                            <li class="card-item" v-for="(item, index) in list" :id="'card-item-' + item.id ">
                                <div class="head">
                                    <template v-if="item.isLeaf">
                                        <svg-icon name="file-official" v-if="item.is_official_cert"></svg-icon>
                                        <svg-icon name="file" v-else></svg-icon>
                                    </template>
                                    <template v-else>
                                        <svg-icon name="folder-official" v-if="item.is_official_cert"></svg-icon>
                                        <svg-icon name="folder" v-else></svg-icon>
                                    </template>
                                    <span class="card-title">
                                        <span class="card-title-cha" :title="item.name">
                                            {{ item.name }}
                                        </span>
                                        <svg-icon name="auth" v-if="item.is_official_cert"></svg-icon>
                                    </span>
                                    <CloseOutlined @click="handleRemove(index)" />
                                </div>
                                <div class="desc">
                                    <a-tooltip :title="item.pathName" placement="bottomLeft">
                                        {{ item.pathName }}
                                    </a-tooltip>
                                </div>
                                <div class="num">共{{ item.num }}道题</div>
                            </li>
                        </ul>
                        <div v-else class="common-no-data"></div>
                    </div>
                    <div class="total">
                        <svg-icon name="exam-m" style="margin-right: 6px;font-size: 20px;"></svg-icon>
                        合计：共<span style="color: #DE504E;">{{ total }}</span>道题
                    </div>
                </div>
            </div>
            <a-divider style="margin-bottom: 0;"></a-divider>
            <div class="bottom-panel-footer">
                <a-button type="primary" @click="handleSubmit" :loading="submitLoading">保存</a-button>
                <a-button @click="router.back">取消</a-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import Tree from './FormSubjectTree.vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { ACTION } from '@/config/constants'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { addaiinterviewassistant, aiinterviewassistantcontent, modifyaiinterviewassistant } from '@/api/interview';
import { FormInstance, message } from 'ant-design-vue';
import { Rule } from 'ant-design-vue/es/form';
import _ from 'lodash';
import { MyConfirm } from '@/utils/antdUtil';
import { useEventListener } from '@vueuse/core';

const props = withDefaults(defineProps<{
    mode?: 'create' | 'edit' | 'copy'
    id?: string
}>(), {
    mode: 'create',
})

const router = useRouter()

const formState = ref<{
    id: string
    name: string
    select_category_tiling: string[]
}>({
    id: '',
    name: '',
    select_category_tiling: []
})

// 设置初始的表单状态
let initialFormState = ref<typeof formState.value>();
function setInitialFormState() {
    initialFormState.value = _.cloneDeep(formState.value)
}
setInitialFormState()

/** 是否有改动 */
const isModified = computed(() => {
    return !_.isEqual(formState.value, initialFormState.value)
})

type ListType = {
    id: string
    name: string
    type_ques_count: Record<number, number>
    pathName: string
    children: ListType[]
    /** 共多少道题 */
    num: number
    isLeaf: boolean
    is_official_cert: boolean
}

const list = ref<ListType[]>([])
const total = computed(() => list.value.reduce((p, c) => p + c.num, 0))

function onUpdateChecked(arr: ListType[]) {
    list.value = arr
    formState.value.select_category_tiling = list.value.map(i => i.id)
}

function onUpdateSingleChecked(type: 'checked' | 'unchecked', value: ListType) {
    if (type === 'checked') {
        list.value.push(value)
    } else {
        list.value = list.value.filter(i => i.id !== value.id)
    }
    formState.value.select_category_tiling = list.value.map(i => i.id)
}

const treeRef = ref<InstanceType<typeof Tree>>()
function handleRemove(index: number) {
    list.value.splice(index, 1)
    treeRef.value?.setCheckedKeys({
        checked: list.value.map(i => i.id),
        halfChecked: []
    })
}

// 详情回显
async function getDetail() {
    if (props.id) {
        let res = await aiinterviewassistantcontent({ id: props.id })
        formState.value = _.pick(res, Object.keys(formState.value)) as any
        treeRef.value?.setCheckedKeys({
            checked: res.select_category_tiling,
            halfChecked: []
        }, setInitialFormState)
    }
}
getDetail()

// 表单校验
const rules: Record<string, Rule[]> = {
    name: [
        { required: true, message: '请输入面试助手名称', trigger: 'blur' },
        { max: 30, message: '输入超出限制，请调整至30字以内' },
    ],
};

// 表单提交
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
/** 是否已提交 */
const isSubmitted = ref(false)
async function handleSubmit() {
    submitLoading.value = true
    try {
        await formRef.value?.validate()

        const select_category_tiling = list.value.map(i => i.id)
        if (select_category_tiling.length === 0) {
            return message.warning('请选择题库')
        }

        if (props.mode === 'create') {
            await addaiinterviewassistant({
                name: formState.value.name,
                select_category_tiling: list.value.map(i => i.id),
            })
        } else if (props.mode === 'edit') {
            await modifyaiinterviewassistant({
                id: props.id!,
                assistant: {
                    name: formState.value.name,
                    select_category_tiling
                }
            })
        } else {
            return message.error('未知模式')
        }
        isSubmitted.value = true
        message.success('保存成功')
        router.back()

    } catch (error) {

    } finally {
        submitLoading.value = false
    }
}

onBeforeRouteLeave(async (to, from, next) => {

    // 如果没有改动或者已经提交了，直接放行
    if (!isModified.value || isSubmitted.value)
        return next()

    try {
        await MyConfirm({
            title: '确定退出此页面？',
            content: '系统可能不会保存您所做的更改',
            okText: '确认',
            cancelText: '取消'
        })
        next()
    } catch (error) {
        next(false)
    }
})

useEventListener('beforeunload', (event) => {
    if (isModified.value) {
        event.preventDefault()
    }
})

defineExpose({
    list
})

</script>

<style lang="less" scoped>
.form-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0 20px 20px;

    .title-wrap {
        height: 64px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            height: 48px;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            text-align: left;
            line-height: 48px;
        }
    }

    :deep(.form) {
        padding: 24px 24px 0 24px;
        background-color: #fff;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);

        label {
            width: 88px;
        }

        .ant-form-item-label>label {
            font-size: 12px;
            color: #626262;
        }
    }

    .bottom-panel {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.13);
        overflow: hidden;
        padding: 24px;

        .bottom-panel-main {
            flex: 1;
            min-height: 0;
            display: flex;

            .content {
                margin-left: 18px;
                flex: 1;
                min-width: 0;
                display: flex;
                flex-direction: column;

                .content-title {
                    height: 48px;
                    line-height: 48px;
                    font-weight: bold;
                    font-size: 14px;
                    padding-left: 2px;
                }

                .content-wrapper {
                    padding: 2px;
                    flex: 1;
                    min-height: 0;
                    overflow: auto;

                    >ul {
                        padding-right: 14px;
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                        gap: 20px;

                        .card-item {
                            border: 1px solid #dddddd;
                            padding: 20px;
                            border-radius: 8px;
                            height: max-content;
                            transition: all ease .2s;

                            &:hover {
                                border: 1px solid transparent;
                                box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.20);

                                .head .anticon-close {
                                    opacity: 1;
                                }
                            }

                            .head {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .svg-icon {
                                    font-size: 20px;
                                }

                                .card-title {
                                    font-size: 16px;
                                    font-weight: bold;
                                    margin-right: auto;
                                    margin-left: 8px;
                                    display: flex;
                                    align-items: center;
                                    flex: 1;
                                    min-width: 0;
                                    overflow: hidden;

                                    .card-title-cha {
                                        margin-right: 8px;
                                        white-space: nowrap;
                                        flex: 1;
                                        min-width: 0;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }
                                }

                                .anticon-close {
                                    opacity: 0;
                                    transition: all ease .2s;
                                    cursor: pointer;
                                }
                            }

                            .desc {
                                font-size: 12px;
                                color: #626262;
                                line-height: 18px;
                                margin-top: 8px;
                                text-wrap: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }

                            .num {
                                font-size: 12px;
                                line-height: 18px;
                                color: rgba(0, 0, 0, 0.85);
                                margin-top: 4px;
                            }
                        }
                    }

                }

                .total {
                    font-size: 14px;
                    margin-top: 16px;
                    height: 40px;
                    padding: 0 16px;
                    display: flex;
                    align-items: center;
                    background: #f1f4fe;
                    border: 1px solid #5377ee;
                    border-radius: 8px;
                    font-weight: bold;
                    margin-left: 2px;
                }
            }
        }


        .bottom-panel-footer {
            display: flex;
            align-items: center;
            margin-top: 24px;

            .ant-btn {
                margin-left: 10px;
            }
        }
    }
}
</style>