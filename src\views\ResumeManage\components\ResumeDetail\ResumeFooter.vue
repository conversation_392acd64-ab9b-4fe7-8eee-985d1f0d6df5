<script setup lang="ts">
import type { ProfileHeaderProps } from './shared'

// 模拟页脚数据
const props = defineProps<{
  userData: ProfileHeaderProps
}>()
const updatedUserData = ref(props.userData)
watch(() => props.userData, (newVal) => {
  updatedUserData.value = newVal
}, {
  deep: true,
})
</script>

<template>
  <div class="mb-8">
    <div class="text-center text-gray-400 text-sm mb-2">
      *本简历分析报告由 AI 生成，仅供参考
    </div>
    <div class="flex justify-center items-center text-sm text-gray-600">
      <div class="flex items-center">
        <span>{{ updatedUserData?.candidateName || '暂无' }}</span>
        <span class="mx-2">({{ updatedUserData?.englishName || '暂无' }})</span>
      </div>
      <div class="mx-3" />
      <div>{{ updatedUserData?.phone || '暂无' }}</div>
      <div class="mx-3" />
      <div>{{ updatedUserData?.email || '暂无' }}</div>
    </div>
  </div>
</template> 