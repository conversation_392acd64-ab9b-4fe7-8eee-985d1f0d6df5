<template>
  <div class="login-container">
    <a-form
      class="login-form"
      ref="formRef"
      :model="formState"
      :rules="rules"
      hideRequiredMark
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item>
        <h2>管理员登录</h2>
      </a-form-item>
      <a-form-item ref="username" name="username">
        <a-input v-model:value.trim="formState.username" placeholder="请输入账号">
          <template #prefix>
            <user-outlined type="user" />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item ref="password" name="password">
        <a-input
          v-model:value.trim="formState.password"
          :type="!passwdShow ? 'password' : ''"
          placeholder="请输入密码"
          @keyup.enter="login"
        >
          <template #prefix> <lock-outlined type="passwd" /> </template>
          <template #suffix>
            <eye-invisible-outlined v-if="!passwdShow" @click="showPasswd" />
            <eye-outlined v-else @click="showPasswd" />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <div class="btn-login">
          <a-button type="primary" @click="login" :loading="loading">登录</a-button>
        </div>
      </a-form-item>
    </a-form>
    <div class="blur-container"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, UnwrapRef } from 'vue'
import store from '@/store/index'
import { useRouter } from 'vue-router'
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
interface FormState {
  username: string
  password: string
}
export default defineComponent({
  components: {
    UserOutlined,
    LockOutlined,
    EyeInvisibleOutlined,
    EyeOutlined
  },
  emits: ['getRegisterStatus'],
  setup() {
    const formRef = ref()
    const loading = ref(false)
    const passwdShow = ref(false)
    const router = useRouter()
    const formState: UnwrapRef<FormState> = reactive({
      username: '',
      password: ''
    })
    const rules = {
      username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
    }
    const showPasswd = () => {
      passwdShow.value = !passwdShow.value
    }
    const login = () => {
      formRef.value.validate().then(() => {
        loading.value = true
        store
          .dispatch('USER_LOGIN', formState)
          .then(() => {
            // 登录成功跳转主页
            loading.value = false
            router.push('/admin')
          })
          .catch(() => {
            loading.value = false
          })
      })
    }
    return {
      formRef,
      labelCol: { span: 0 },
      wrapperCol: { span: 24 },
      loading,
      passwdShow,
      formState,
      rules,
      showPasswd,
      login
    }
  }
})
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background: url('@/assets/images/bg-login2.jpg') bottom no-repeat;
  overflow: hidden;
  background-size: 100% 100%;
  .blur-container {
    position: absolute;
    width: 100%;
    top: 0;
    bottom: 0;
    background: url('@/assets/images/bg-login2.jpg') bottom no-repeat;
    overflow: hidden;
    background-size: 100% 100%;
    -webkit-filter: blur(9px);
    filter: blur(9px);
  }
}
.login-form {
  position: absolute;
  z-index: 999;
  h2 {
    font-size: 24px;
  }
  .ant-btn-primary {
    width: 100%;
  }
  .btn-login {
    padding-top: 20px;
    box-sizing: border-box;
  }
  background-color: #fff;
  width: 5.2rem;
  border-radius: 10px;
  box-shadow: 0 0 10px 3px rgba(255, 255, 255, 0.5);
  padding: 0.3rem 0.8rem 0.1rem;
  overflow: hidden;
}
</style>
