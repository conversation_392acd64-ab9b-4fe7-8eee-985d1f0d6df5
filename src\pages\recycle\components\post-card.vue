<script setup>
import { DeleteOutlined, EditOutlined, RightOutlined } from '@ant-design/icons-vue'
</script>

<template>
  <div class="post_card w-[386px] bg-[red] h-[195px] rounded-[8px] p-[22px] flex flex-wrap content-between">
    <div class="flex flex-nowrap justify-between items-center box-border w-full">
      <div class="flex items-center">
        <svg-icon name="post_card_logo" width="26" height="26" style="margin-right: 10px;margin-left: -6px;" />
        <div class="text-[18px]">
          前端工程师
        </div>
      </div>
      <div class="flex items-center">
        <EditOutlined class="mr-[16px]" />
        <DeleteOutlined />
      </div>
    </div>
    <div class="my-[5px] ">
      1. 负责开发和持续改进文远自主研发的自动驾驶运行时框架2.负责车载算法底层优化，模型加速，车载相关工具链开发等职责，整合和提高工作流程，
    </div>
    <div class="text-[#5478ee]">
      查看更多
      <RightOutlined />
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>