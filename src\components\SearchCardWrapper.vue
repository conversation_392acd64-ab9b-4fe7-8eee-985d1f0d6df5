<template>
  <div class="search-card-warp">
    <div class="search-card">
      <div class="row-wrap fixed-row-wrap">
        <div class="left">
          <slot name="fixedSearch"></slot>
        </div>
        <div class="btn" @click="showmore = !showmore">
          <a-badge
            v-if="badgeCount"
            :count="badgeCount"
            :number-style="{
              backgroundColor: '#FF4D4F',
              color: '#fff',
              minWidth: '16px',
              width: '16px',
              height: '16px',
            }" />
          <img class="icon" src="@/assets/images/paper/filter.png" alt="" v-else>
          <div class="label">更多条件</div>
        </div>
      </div>
      <div class="row-wrap more-row-wrap" v-show="showmore">
        <div class="left">
          <slot name="moreSearch"></slot>
        </div>
        <div class="btn reset" @click="handleReset">
          <img class="icon" src="@/assets/images/paper/clear.png" alt="">
          <div class="label">清空条件</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  badgeCount: number
}>()

const emits = defineEmits(['handleReset'])

const showmore = ref(false) // 显示更多筛选条件

const handleReset = () => emits('handleReset')

</script>

<style lang="less" scoped>
.search-card-warp {
  padding: 0 20px;
  .search-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.10);
    padding: 24px;
    .row-wrap {
      width: 100%;
      display: flex;
      &:first-of-type {
        margin-top: 0;
      }
      &.fixed-row-wrap {
        .left {
          justify-content: space-between;
        }
      }
      .more-row-wrap {
        .search-item {
          margin-top: 16px;
        }
      }
      
      .left {
        flex: auto;
        display: flex;
        flex-wrap: wrap;
      }
      .btn {
        flex: none;
        width: 108px;
        height: 32px;
        margin-left: 8px;
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &.reset {
          margin-top: 16px;
        }
        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .ant-badge {
          margin-right: 4px;
          :deep(.ant-badge-count) {
            padding: 0;
            line-height: 16px;
            .ant-scroll-number-only {
              height: 16px;
            }
            .ant-scroll-number-only-unit {
              height: 16px;
              font-size: 10px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
