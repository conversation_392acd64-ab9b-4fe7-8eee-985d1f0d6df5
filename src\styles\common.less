* ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

* ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #dcdde1;
}

* ::-webkit-scrollbar-corner {
  background-color: transparent;
}

.theme-text-color {
  color: #5478EE
}

.ant-table {
  border-radius: 0 !important;

  .ant-table-cell {
    [class^=status_] {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 5px;

      &.status_green {
        background: #52c41a;
      }

      &.status_red {
        background: #ff4d4f;
      }

      &.status_gray {
        background: #d9d9d9;
      }
    }
  }
}

.common-table {
  flex: 1;
  min-height: 0;

  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-table {
        overflow: auto;

        .ant-table-container {
          height: 100%;
        }
      }
    }
  }

  .ant-table-thead>tr>th {
    background: #f1f4fe !important;
    padding-left: 10px;
    color: #121633;
    font-weight: bold;
    font-size: 15px;
    border-radius: 0 !important;

    .ant-table-column-title {
      color: #121633;
      font-weight: bold;
      font-size: 15px;
    }
  }

  .ant-table-tbody>tr:not(.ant-table-expanded-row)>td {
    padding: 10px;
    font-size: 14px;
    color: #121633;

    p,
    span {
      &:not(.ql-formula, .ql-formula span, .ant-btn-link span) {
        font-size: 14px;
        font-family: PingFang HK;
        color: #121633;
        text-align: center;
      }
    }
  }

  .ant-table-row-expand-icon:focus,
  .ant-table-row-expand-icon:hover {
    color: #5478ee;
  }
}

.common-page-title {
  height: 48px;
  line-height: 48px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.45);

  span {
    cursor: pointer;

    &:last-child {
      cursor: auto;
      color: #000,
    }
  }
}

.common-page-title-wrapper {
  height: 64px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    height: 48px;
    font-size: 14px;
    font-weight: 600;
    line-height: 48px;

    span {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.45);

      &:last-child {
        cursor: auto;
        color: #000,
      }
    }
  }

  .btns {
    height: 32px;

    .ant-btn {
      margin-left: 8px;
    }
  }
}


.common-no-data {
  flex: 1;
  height: 100%;
  background: url('../assets/images/nodata.png') no-repeat center;
  background-size: 160px;
}
.ant-btn::before {
  opacity: 1;
  background-color: transparent!important;
}
.common-ai-button {
  cursor: pointer;
  position: relative;
  border: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  padding: 0 16px;
  border-radius: 4px;

  &:is(body.moon .common-ai-button) {
    background: none;
    color: rgba(255, 255, 255, .85);
  }
//     border-image: linear-gradient(270deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%) 1 1;
  &::before {
    border-radius: 8px;
    display: block;
    content: '';
    border: 1.5px solid transparent;
    background: linear-gradient(270deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    animation: animatedgradient 2s cubic-bezier(0.66, 0.02, 0.39, 1) alternate infinite;
  }

  &[disabled],
  &.disabled {
    filter: grayscale(1);
    cursor: not-allowed;
  }
}

.common-full-spin {
  height: 100%;

  .ant-spin-container {
    height: 100%;
  }
}

.custom-tooltip .ant-tooltip-inner {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.85);
}

.common-info-icon {
  color: #D9D9D9;
  transition: all ease .2s;
  margin-left: 4px;
  position: relative;
  top: 1px;

  &:hover {
    color: #8CA7FF;
  }
}

.common-text-overflow {
  text-overflow: ellipsis;
  overflow: hidden;
  text-wrap: nowrap;
}

.common-btn--disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  background: #f5f5f5 !important;
  text-shadow: none !important;
  box-shadow: none !important;
}

// 打印界面预览时候，去除页眉页脚空间（margin 0 虽然没有页面页脚勾选项但是会很丑，margin 0.5 有勾选项，但是不会出现页面页脚）
@media print {
  @page {
    margin: 0.5cm;
  }
}

@keyframes animatedgradient {
  0% {
    background: linear-gradient(0deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
    // 改成radial-gradient
  }
  20% {
    background: linear-gradient(60deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
  }
  40% {
    background: linear-gradient(120deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
  }
  60% {
    background: linear-gradient(180deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
  }
  80% {
    background: linear-gradient(240deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
  }
  100% {
    background: linear-gradient(300deg, #b83bff 0%, #ecbfbf 17%, #6a33ff 46%, #8400ff 57%, #334eff 67%, #53fff3 100%, #822deb 100%)  border-box;
  }
}