<template>
  <a-form
    class="form"
    ref="basicInfoFormRef"
    :model="formState"
    :hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
    :labelWrap="true"
  >
  <a-form-item label="考试名称" v-if="preview">
    <span style="font-size: 12px;">{{ paperInfo.name }}</span>
  </a-form-item>
    <p class="subTitle">考试前</p>
    <a-form-item label="考试须知" name="name">
      <a-radio-group v-model:value="examnoteTab" :disabled="preview">
        <a-radio :value="0">默认</a-radio>
        <a-radio :value="1">自定义</a-radio>
      </a-radio-group>
      <a-editor
        ref="editorRef"
        v-show="examnoteTab === 1"
        style="margin-top: 48px;margin-right: 20px;"
        :content="formState.examnotes"
        :exclude-menus="[
          'head',
          'todo',
          'quote',
          'code',
          'table',
          'emoticon',
          'video',
          'fontName',
          'strikeThrough',
          'lineHeight',
          'backColor',
          'undo',
          'redo',
          'indent',
          'link',
          'list',
          'justify',
          'fullscreen'
        ]"
        @getHtml="getHtml"
      />
    </a-form-item>
    <a-form-item label="开启手机监控" name="phonemonitor">
      <a-radio-group v-model:value="formState.phonemonitor" :disabled="preview">
        <a-radio :value="1">是</a-radio>
        <a-radio :value="0">否</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="开启电脑监控" name="computermonitor">
      <a-radio-group v-model:value="formState.computermonitor" :disabled="preview">
        <a-radio :value="1">是</a-radio>
        <a-radio :value="0">否</a-radio>
      </a-radio-group>
    </a-form-item>

    <a-form-item v-if="!formState.examvideo" name="name" class="video">
      <template #label>
        <span>宣传视频（mp4格式）</span>
        <a-tooltip placement="right" overlayClassName="light">
          <template #title>
            <span>上传的视频将在考试等待页面中展示，可用于宣传报考单位和导师，仅支持mp4格式</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-upload
        v-show="!isshowvideo && !preview"
        v-model="formState.examvideo"
        :beforeUpload="beforeUploadmp4"
        accept=".mp4"
        name="avatar"
        list-type="picture"
        class="avatar-uploader"
        :show-upload-list="false"
        :file-list="downloadVideo"
        :customRequest="customRequestVideo"
      >
        <plus-outlined></plus-outlined>
      </a-upload>
      <span v-if="preview" style="font-size: 12px">无</span>
    </a-form-item>

    <a-form-item
      v-else-if="formState.examvideo && downloadVideo.length"
      name="name"
      class="video"
    >
      <template #label>
        <span>宣传视频（mp4格式）</span>
        <a-tooltip placement="right" overlayClassName="light">
          <template #title>
            <span>上传的视频将在考试等待页面中展示，可用于宣传报考单位和导师，仅支持mp4格式</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <div v-show="isshowvideo" class="video-wrapper">
        <video controls width="200" height="150" :src="downloadVideo[0].url"></video>
        <div class="mask">
          <delete-outlined class="iconimg" style="color: #ffffffd9" @click="closevideo" />
        </div>
      </div>
    </a-form-item>

    <a-form-item v-if="paperInfo.uniexam === '1'" name="limitlateness">
      <template #label>
        <span>限时迟到（分钟）</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>开考后，迟于设定的时间，不允许考生入场，考试中退出的考生不受此影响。留空或0表示不限制</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <div style="display: flex;align-items: center;">
        <a-input-number
          :disabled="preview"
          v-model:value="formState.limitlateness"
          :min="0"
          class="form-input"
          :controls="false"
        />
      </div>
    </a-form-item>

    <p class="subTitle">考试中</p>
    <a-form-item>
      <template #label>
        <span>题目顺序</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>（1）固定：所有考生的试卷中题目顺序一致；（2）随机：所有考生的试卷中题目顺序不同</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.quesorder" :disabled="preview">
        <a-radio :value="1">固定</a-radio>
        <a-radio :value="0">随机</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item>
      <template #label>
        <span>选项顺序</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>（1）固定：所有考生的试卷中选择题的选项内容与原题一致；（2）随机：所有考生的试卷中选择题的选项内容随机排序</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.opsorder" :disabled="preview">
        <a-radio :value="1">固定</a-radio>
        <a-radio :value="0">随机</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item>
      <template #label>
        <span>题目展示</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>（1）整卷：一次性向学生展示完整试卷；（2）逐题：学生需要逐题作答并且答题时只能看到当前题目的内容</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.showType" :disabled="preview || paperInfo.individualTiming == '1'">
        <a-radio :value="0">整卷</a-radio>
        <a-radio :value="1">逐题</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item v-if="formState.showType == '1'">
      <template #label>
        <span>返回修改</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>仅在“题目展示”=“逐题”时有效（1）允许：考生在提交答案后可点击题号返回该题修改答案并重新提交；（2）禁止：考生在提交答案后不可返回该题查看或修改答案</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.canBack" :disabled="preview || paperInfo.individualTiming == '1'">
        <a-radio :value="1">允许</a-radio>
        <a-radio :value="0">禁止</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item>
      <template #label>
        <span>计算器</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>提供计算器辅助计算，考生可在考试中自主使用</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.calculator" :disabled="preview">
        <a-radio :value="1">有</a-radio>
        <a-radio :value="0">无</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item name="uploadanswer">
      <template #label>
        <span>拍照上传答案</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>（1）允许：考生可将问答题答案写在纸上，并通过手机拍照上传答案，适用于正确答案中含有复杂公式和特殊图形等题目；（2）禁止：考生仅可将问答题答案输入在文本框中</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <a-radio-group v-model:value="formState.uploadanswer" :disabled="preview">
        <a-radio :value="1">允许</a-radio>
        <a-radio :value="0">禁止</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="考试时长">
      <span class="exam-time">
        {{ Math.floor(totalSeconds/60) }}分<template v-if="!(totalSeconds % 60)">钟</template><template v-if="totalSeconds % 60">{{ totalSeconds % 60 }}秒</template>
      </span>
    </a-form-item>
    <a-form-item name="presubmit">
      <template #label>
        <span>最短交卷时间（分钟）</span>
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>考生答卷完毕后，早于设定时间，不允许提前交卷。留空或0表示不限时间</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </template>
      <div class="presubmit-wrapper">
        <a-input-number
          :disabled="preview"
          :controls="false"
          :min="0"
          v-model:value="formState.presubmit"
          class="form-input"
        />
      </div>
    </a-form-item>
    <p class="subTitle">反作弊</p>
    <a-form-item label="" class="anomie">
      <div class="anomieItem">
        <a-checkbox
          v-model:checked="formState.isneterror"
          :disabled="preview"
          class="check-box"
          @change="handleChangeIsneterror"
        />
        <span>断网检测：允许学生最多因网络原因离开考试
          <a-input-number
            type="number"
            :disabled="preview || !formState.isneterror"
            v-model:value.number="formState.neterror"
            :min="1"
            :max="99"
            :controls="false"
            class="insertInput"/>
          次
        </span>
      </div>
      <div class="anomieItem" name="cutscreen">
        <a-checkbox
          v-model:checked="formState.iscutscreen"
          :disabled="preview"
          class="check-box"
          @change="handleChangeIscutscreen"
        />
        <span
          >禁止切屏：允许学生最多切屏
          <a-input-number
            type="number"
            :disabled="preview || !formState.iscutscreen"
            v-model:value="formState.cutscreen"
            :min="1"
            :max="99"
            :controls="false"
            class="insertInput"
          />
          次，超出次数后强制交卷</span
        >
      </div>
      <div class="anomieItem">
        <a-checkbox :disabled="preview" v-model:checked="formState.watermark" class="check-box" />
        <span>答题水印：考生作答页面，使用考生姓名和考生编号作为背景页面</span>
      </div>
      <div class="anomieItem">
        <a-checkbox :disabled="preview" v-model:checked="formState.nocopy" class="check-box" />
        <span>禁止复制：考试期间，禁止考生对试卷内容进行复制粘贴，且不得粘贴外部内容</span>
      </div>
    </a-form-item>
    <p class="subTitle">其他</p>
    <a-form-item label="" labelWidth="0">
      <a-checkbox :disabled="preview" v-model:checked="formState.hiddenname" class="check-box">
        阅卷时隐藏考生真实姓名
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>勾选后，阅卷老师将看不到考生的真实姓名</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </a-checkbox>
    </a-form-item>
    <a-form-item label="" labelWidth="0">
      <a-checkbox :disabled="preview || mode === '编辑'" v-model:checked="formState.shareToDept" class="check-box">
        本部门教师可编辑
        <a-tooltip placement="right" overlayClassName="custom-tooltip" color="#fff">
          <template #title>
            <span>勾选后，试卷将共享给本部门所有教师，本部门教师均可查看和编辑该试卷</span>
          </template>
          <svg-icon class="common-info-icon" name="info2"></svg-icon>
        </a-tooltip>
      </a-checkbox>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, reactive, ref, watch, watchEffect, computed } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
// import store from '@/store'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import AEditor from '@/components/Editor/index.vue'
import { client ,convertKeysToCamelCase} from '@/utils/common'
import { message } from 'ant-design-vue'
import { previewfile , uploadOBS } from '@/api/admin/paperManage'
import { Paper } from '@/models/paperModel'
import emitter from '@/utils/bus'
import { getTotalSeconds } from '@/utils'

const props = withDefaults(defineProps<{
  paperInfo?: any
  preview?: boolean
  mode?: string
}>(), {
  preview: false,
  mode: '新增'
})

const emits = defineEmits<{
  (e: 'change', value: any): void
}>()

let downloadVideo = reactive<any[]>([])

const isshowvideo = ref(false)

const closevideo = () => {
  isshowvideo.value = false
  formState.value.examvideo = ''
  downloadVideo.length = 0
}

const customRequestVideo = ({ file }: any) => {
  new Promise((resolve) => {
    const fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    fileReader.onload = async () => {
      resolve(fileReader.result)
      const time = Date.now() + ''
      const format = file.type.split('/')[1]

      const formData = new FormData()
      formData.append('file', file)
      formData.append('key', `examvideo/${time}.${format}`)
      const response = await uploadOBS(formData)
      const { objectUrl,objectKey } = response
      Promise.resolve(response)
        .then(() => {
          downloadVideo[0] = {
            name: `examvideo/${time}.${format}`,
            url: ''
          }
          formState.value.examvideo = objectUrl
          return Promise.resolve({ file: objectUrl })
          // return previewfile({ filename: objectUrl })
        })
        .then((resdata: any) => {
          isshowvideo.value = true
          downloadVideo[0].url = resdata.file
        })
        .catch((err) => {
          message.error('视频上传失败,请重新上传!')
        })
      // let clientObj = client(
      //   store.getters.ststoken.accessKeyId,
      //   store.getters.ststoken.accessKeySecret,
      //   store.getters.ststoken.stsToken
      // )
      // const response = uploadOBS({
      //   Bucket: 'zhanglibo-exam',
      //   Key: `examvideo/${time}.${format}`,
      //   SourceFile: file
      // })
      // response
      //   .then(() => {
      //     downloadVideo[0] = {
      //       name: `examvideo/${time}.${format}`,
      //       url: ''
      //     }
      //     formState.value.examvideo = `examvideo/${time}.${format}`
      //     return previewfile({ filename: `examvideo/${time}.${format}` })
      //   })
      //   .then((resdata: any) => {
      //     isshowvideo.value = true
      //     downloadVideo[0].url = resdata.file
      //   })
      //   .catch(() => {
      //     message.error('视频上传失败,请重新上传!')
      //   })
    }
  })
}

const beforeUploadmp4 = (file: any) => {
  const isVideo = file.type === 'video/mp4'
  if (!isVideo) {
    message.error('请上传mp4文件')
  }

  return isVideo
}

const examnoteTab = ref(0)
const formState = ref<{
  neterror: number | null
  cutscreen: number | null
  [propName: string]: any
}>({
  examnotes: '',
  phonemonitor: 1,
  computermonitor: 1,
  examvideo: '',
  limitlateness: 15,
  presubmit: 0,
  quesorder: 1,
  opsorder: 1,
  calculator: 0,
  hiddenname: true,
  shareToDept: true,
  uploadanswer: 0,
  reference: '',
  neterror: null,
  cutscreen: null,
  iscutscreen: false,
  isneterror: false,
  watermark: false,
  nocopy: false,
  /** 0整卷  1逐题 */
  showType: 0,
  /** 是否可以返回修改 */
  canBack: 1
})

// 计算时间总时长（秒）
const totalSeconds = computed(() => {
  if (props.paperInfo.individualTiming == '1') {
    return getTotalSeconds(props.paperInfo.fbody)
  } else {
    return props.paperInfo.duration * 60 ?? 0
  }
})

// 单题是否限时，对当前界面的影响 （这里只想处理用户手动点击的情况，所以使用事件总线通信，而不能用watch，否则编辑回显会出错）
emitter.on('paper-isindividualtime-swich', onIndividualChange)
function onIndividualChange(val: string) {
  if (val == '1') {
    formState.value.showType = 1
    formState.value.canBack = 0
  } else {
    formState.value.showType = 0
    formState.value.canBack = 1
  }
  emits('change', {
    showType: formState.value.showType,
    canBack: formState.value.canBack
  })
}

const handleChangeIsneterror = (value: boolean) => { // 断网状态修改
  if (value) {
    formState.value.neterror = null
  } else {
    formState.value.neterror = 0
  }
}
const handleChangeIscutscreen = (value: boolean) => { // 切屏状态修改
  if (value) {
    formState.value.cutscreen = null
  } else {
    formState.value.cutscreen = 0
  }
}

const checkcutscreen = async (rule: RuleObject, value: 0) => {
  const reg = /^[1-9][0-9]{0,}$/
  if (value == 0) {
    return Promise.reject('请输入')
  } else if (!reg.test(value)) {
    return Promise.reject('输入格式不正确')
  } else {
    return Promise.resolve()
  }
}

const getHtml = (val: any) => {
  formState.value.examnotes = val
}

watch(() => formState.value.cutscreen, (value) => {
  if (value && value > 0) {
    formState.value.iscutscreen = true
  } else {
    formState.value.iscutscreen = false
  }
})
watch(() => formState.value.neterror, (value) => {
  if (value && value > 0) {
    formState.value.isneterror = true
  } else {
    formState.value.isneterror = false
  }
})
// watchEffect(() => {
//   formState.value.iscutscreen = !!formState.value.cutscreen
//   formState.value.isneterror = !!formState.value.neterror
// })

const checkPresubmit = async (rule: RuleObject, value: string) => {
  if (Number(value) * 60 > Number(totalSeconds.value)) {
    return Promise.reject('最短交卷时间不能超过考试时长')
  } else {
    return Promise.resolve()
  }
}

const checkLimitlateness = async (rule: RuleObject, value: string) => {
  if (Number(value) * 60 > Number(totalSeconds.value)) {
    return Promise.reject('限时迟到时间不能超过考试时长')
  } else {
    return Promise.resolve()
  }
}

// 定义规则
const rules = {
  limitlateness: [{ required: true, validator: checkLimitlateness, trigger: 'blur' }],
  presubmit: [{ required: true, validator: checkPresubmit, trigger: 'blur' }]
}
// cutscreen: [{ required: true, validator: checkcutscreen, trigger: 'blur' }],
// neterror: [{ required: true, validator: checkcutscreen, trigger: 'blur' }],

const basicInfoFormRef = ref()
const validateForm = () => {
  return new Promise((resolve, reject) => {
    return basicInfoFormRef.value.validate().then(() => {
      if (formState.value.isneterror && !formState.value.neterror) {
        message.warning('请输入断网检测次数')
        return reject('请输入断网检测次数')
      }
      if (formState.value.iscutscreen && !formState.value.cutscreen) {
        message.warning('请输入禁止切屏次数')
        return reject('请输入禁止切屏次数')
      }
      const paperInfo: any = { ...formState.value }
      if (!paperInfo.iscutscreen) paperInfo.cutscreen = 0
      if (!paperInfo.isneterror) paperInfo.neterror = 0
      if (!examnoteTab.value) paperInfo.examnotes = ''
      
      emits('change', {
        ...paperInfo, 
        downloadVideo: JSON.parse(JSON.stringify(downloadVideo))
      })
      resolve('')
    })
  })
}

defineExpose({ validateForm })

const editorRef = ref()
watch(examnoteTab, (val) => {
  if (val === 1) {
    editorRef.value.updateContent()
  }
})

watch(() => props.paperInfo, (val) => {
  // 编辑、克隆回显
  Object.keys(formState.value).forEach((key) => {
    formState.value[key] = val[key]
  })
  if (formState.value.examnotes) {
    examnoteTab.value = 1
  }
  downloadVideo.push({
    url: val.examvideo_url,
    name: val.examvideo_url
  })
  if (downloadVideo[0].url) isshowvideo.value = true
})

const ststokenTimer = ref<any>(null)
onMounted(() => {
  // getIdcardInfo()
  // 获取ststoken密钥
  // store.dispatch('CHANGE_STS_TOKEN', '')
  // ststokenTimer.value = setInterval(() => {
  //   store.dispatch('CHANGE_STS_TOKEN', '')
  // }, 1000 * 60 * 120)
})

onBeforeUnmount(() => {
  clearInterval(ststokenTimer.value)
})
</script>

<style lang="less" scoped>
.video-wrapper {
  width: 100px;
  height: 100px;
  video {
    height: 100%;
    width: 100%;
  }

  &:hover .mask {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 102px;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    pointer-events: none;
    cursor: pointer;
    .iconimg {
      display: block;
      pointer-events: visible;
    }
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

/* 火狐 */
input {
  -moz-appearance: textfield;
}

:deep(.avatar-uploader) {
  border: 1px dashed #ccc;
  border-radius: 2px;
  cursor: pointer;
  display: inline-block;
  width: 100px;
  height: 100px;
  .ant-upload-select {
    height: 100%;
    display: block;
    span.ant-upload {
      display: flex;
      height: 100%;
      justify-content: center;
      align-items: center;
    }
  }
}

.form {
  overflow: auto;
  margin-bottom: auto;
  flex: 1;
  min-height: 0;
  overflow: auto;
  :deep(.ant-form-item) {
    margin-bottom: 10px;
  }
  :deep(.ant-col) {
    width: 160px;
  }
  :deep(.ant-input) {
    width: 220px;
    height: 32px;
    font-size: 12px;
    border-radius: 8px;
  }
  :deep(.ant-form-item-no-colon) {
    color: #626262;
    font-size: 12px;
  }
  .uploadFile {
    width: 88px;
    height: 32px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-size: 12px;
  }
  .exam-time {
    font-size: 12px;
  }
  .subTitle {
    width: 42px;
    height: 20px;
    font-size: 14px;
    font-weight: 600;
    color: rgb(0, 0, 0);
    line-height: 20px;
    margin-bottom: 19px;
  }
  :deep(.insertInput) {
    width: 40px;
    height: 24px;
    border-radius: 4px;
    margin: 0 4px;
    .ant-input-number-input-wrap {
      height: 100%;
      .ant-input-number-input {
        height: 100%;
      }
    }
  }
  .tip-icon {
    margin-left: 10px;
  }

  label {
    font-size: 12px;
  }
  .anomie span {
    font-size: 12px;
  }
  .anomie .anomieItem {
    height: 32px;
    display: flex;
    align-items: center;
    .check-box {
      margin-right: 8px;
    }

    label + span {
      display: flex;
      align-items: center;
    }
  }
  .presubmit-wrapper {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .form-input {
    font-size: 12px;
    border-radius: 8px;
    :deep(.ant-input-number-input) {
      font-size: 12px;
    }
    &.presubmit {
      width: 88px;
    }
  }
}
</style>

<style lang="less">
.custom-tooltip .ant-tooltip-inner {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.85);
}
.w-e-toolbar {
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}
.w-e-text-container {
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
</style>
