<script setup lang="ts">
import PostCard from './components/post-card.vue'
</script>

<template>
  <div class="manual-mark-page-wrap">
    <div class="common-page-title-wrapper">
      <span class="title">岗位管理</span>
      <div class="btns">
        <a-button type="primary" @click="router.push('/admin/postManange/create')">
          创建岗位
        </a-button>
      </div>
    </div>
    <div class="filter-wrapper">
      <a-input-search placeholder="请输入岗位名称" allow-clear style="width: 10%;" />
    </div>
    <div class="px-[20px]">
      <PostCard />
    </div>
  </div>
</template>

<style lang="less" scoped>
.post_card{
  background: #fff;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
}
.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-wrapper {
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  margin: 0px 20px 20px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }

  }

}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
</style>