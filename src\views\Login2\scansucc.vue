<template>
    <div class="exam-system">
        <bg></bg>
        <div class="content">
            <div class="corner" @click="toRegister">
                <div>注册</div>
            </div>
            <loginLeft></loginLeft>
            <div class="content-right">
                <div class="circle"></div>
                <p class="success">扫描成功</p>
                <p class="touchlogin">在微信中轻触允许即可登录</p>
                <p class="remind">登录视为您已同意第三方账号用户协议</p>
            </div>
        </div>
    </div>
</template>
  
<script setup lang="ts">

    import bg from './components/bg.vue'
    import loginLeft from './components/loginLeft.vue';
    import { useRouter } from 'vue-router';
    const router = useRouter();
    function toRegister () {
        router.push('/register')
    }

</script>
  
<style lang="less" scoped>
  .exam-system {
    position: relative;
    
    .content {
        width: 903px;
        height: 484px;
        position: absolute;
        top: 50%;
        left: 50%;
        translate: -50% -50%;
        background-color: white;
        border-radius: 16px;
        .corner {
            position: absolute;
            top: 0;
            right: 0;
            width: 92px;
            height: 92px;
            background: url('@/assets/images/triangle.png') center/100% 100% no-repeat;
            z-index: 999;
            cursor: pointer;
            div {
                position: absolute;
                top: 7.2px;
                right: 8.37px;
                width: 42px;
                height: 42px;
                font-size: 18px;
                font-family: PingFang SC, PingFang SC-6;
                font-weight: 500;
                color: #ffffff;
                line-height: 42px;
                text-align: center;
                transform: rotateZ(45deg);
            }
        }
        .content-right {
            position: absolute;
            right: 111px;
            top: 152px;
            text-align: center;
            .circle {
                width: 94px;
                height: 94px;
                border: 1px solid rgba(0,0,0,0.15);
                border-radius: 50%;
                margin-bottom: 22.69px;
                transform: translateX(50%);
            }
            .success {
                width: 96px;
                height: 32px;
                font-size: 24px;
                font-family: PingFang SC, PingFang SC-6;
                font-weight: 500;
                line-height: 32px;
                margin-bottom: 8px;
                transform: translateX(50%);
            }
            .touchlogin {
                height: 24px;
                font-size: 16px;
                font-family: PingFang SC, PingFang SC-5;
                font-weight: 400;
                line-height: 24px;
                margin-bottom: 71px;
            }
            .remind {
                font-size: 12px;
                color: #999;
                height: 20px;
                line-height: 20px;
            }
        }
    }
  }
</style>