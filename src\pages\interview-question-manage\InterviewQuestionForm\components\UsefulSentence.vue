<template>
    <div class="useful-sentence-container">
        <a-button @click="visible = !visible">常用语</a-button>
        <div class="useful-sentence-modal" v-if="visible">
            <div class="modal-title">
                <span>常用语</span>
                <CloseOutlined class="close-btn" @click="visible = false" />
            </div>
            <ul class="sentence-list">
                <li class="sentence-item" v-for="item in list" :key="item.id"
                    @click="emits('select', item.text), visible = false">
                    <span class="sentence-text"> {{ item.text }} </span>
                    <span v-if="item.id" class="icon-btns" @click.stop>
                        <svg-icon class="icon-btn" name="edit" @click="handleEdit(item)" />
                        <svg-icon class="icon-btn" name="delte" @click="handleRemove(item)" />
                    </span>
                </li>
            </ul>
            <div class="add-sentence-btn" @click="formVisible = true">
                <plus-outlined style="margin-left: 8px;" />
                <span style="margin-left: 8px;">新增常用语</span>
            </div>
        </div>

        <a-modal v-model:visible="formVisible" :title="formState.id ? '编辑常用语' : '添加常用语'" width="586px" :keyboard="false"
            :maskClosable="false" @cancel="onFormClose" @ok="onFormSubmit">
            <a-form ref="formRef" :model="formState" :hideRequiredMark="true" :rules="rules">
                <a-form-item label="" name="phrases">
                    <a-textarea style="height: 288px;" v-model:value="formState.phrases" placeholder="请输入" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { CloseOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue'
import { addcommonphrases, delcommonphrases, modifycommonphrases, querycommonphrases } from '@/api/interview';
import type { Rule } from 'ant-design-vue/es/form'
import { useState } from '@/hooks';
import { FormInstance, message, Modal } from 'ant-design-vue';
import { MyConfirm } from '@/utils/antdUtil';

const emits = defineEmits<{
    (e: 'select', text: string): void
}>()

// 常用语列表弹层
const visible = ref(false)

const list = ref<{
    /** 常用语id，如果没有id则表示系统常用语 */
    id?: string
    text: string
}[]>([])

// 列表查询
async function getList() {
    let res = await querycommonphrases()
    list.value = [
        ...res.system_common_phrases.map(text => ({ text })),
        ...res.self_common_phrases.map(item => ({ id: item.id, text: item.phrases })),
    ]
}
getList()

// 删除
async function handleRemove(item) {
    await MyConfirm({
        title: '您是否要删除该常用语？'
    })
    await delcommonphrases({ id: item.id })
    message.success('删除成功')
    getList()
}

// 常用语表单
const formVisible = ref(false)
const formRef = ref<FormInstance>()
const rules: Record<string, Rule[]> = {
    phrases: [
        { required: true, message: '请输入常用语内容', trigger: 'blur' },
        { max: 1000, message: '最多1000个字符', trigger: 'blur' },
    ]
}
const [formState, resetFormState] = useState({
    id: '',
    phrases: ''
})

const handleEdit = (item: any) => {
    formVisible.value = true
    formState.value.id = item.id
    formState.value.phrases = item.text
}


async function onFormSubmit() {
    await formRef.value?.validate()
    if (!formState.value.id) {
        await addcommonphrases({ phrases: formState.value.phrases })
        message.success('添加成功')
    } else {
        await modifycommonphrases(formState.value)
        message.success('编辑成功')
    }
    resetFormState()
    getList()
    formVisible.value = false
}

function onFormClose() {
    resetFormState()
    console.log(formState.value.id, formState.value.phrases)
}

defineExpose({
    list
})

</script>

<style lang="less" scoped>
.useful-sentence-container {
    position: relative;

    .useful-sentence-modal {
        position: absolute;
        left: 0;
        top: calc(100% + 4px);
        padding: 32px 8px 40px 32px;
        max-height: 324px;
        border-radius: 8px;
        box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.10);
        width: fit-content;
        background-color: #fff;
        z-index: 999;
        display: flex;
        flex-direction: column;

        .modal-title {
            padding-right: 24px;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 26px;
            display: flex;
            justify-content: space-between;
        }

        .sentence-list {
            padding-right: 18px;
            overflow: auto;
            flex: 1;
            min-height: 0;

            .sentence-item {

                box-sizing: border-box;
                width: 680px;
                height: 34px;
                margin-bottom: 8px;
                font-size: 12px;
                padding: 0 8px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 8px;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: rgba(1, 1, 1, 0.85);
                transition: all ease .2s;

                &:hover {
                    background-color: #f1f4fe;
                    .icon-btns {
                        opacity: 1;
                    }
                }

                .sentence-text {
                    flex: 1;
                    min-width: 0;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-wrap: nowrap;
                }

                .icon-btns {
                    height: 100%;
                    display: flex;
                    align-items: center;
                    opacity: 0;
                    transition: all ease .2s;

                    .icon-btn {
                        width: 16px;
                        height: 16px;
                        margin-right: 8px;
                        cursor: pointer;
                    }
                }
            }
        }

        .add-sentence-btn {
            cursor: pointer;
            color: rgba(1, 1, 1, 0.85);
            font-size: 12px;
            height: 34px;
            line-height: 34px;
            border-radius: 8px;
            transition: all ease .2s;
            width: 680px;

            &:hover {
                background-color: #f1f4fe;
            }
        }
    }
}
</style>