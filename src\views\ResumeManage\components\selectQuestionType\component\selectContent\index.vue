<script setup lang="ts">
import { ElMessage } from 'element-plus'
import ContentItem from './contentItem.vue'
import TypeButton from './createButton.vue'
import ManualQuestionContent from './manualInput.vue'
import PkgContentItem from './pkgContentItem.vue'
import QuestionContentItem from './questionContent.vue'

const props = defineProps({
  isEmpty: Boolean,
})

const emits = defineEmits(['delete', 'switchType'])
interface PKGITEM {
  type: string
  id: string
  name: string
  total: number
  extractNum: number | string | any
}
interface MANUALQUESITEM {
  type: string
  id: string
  isEdit: boolean
  content: string
  recordDuration: any
  correctAnswer: {
    reference: string
  }
}
const contentList = defineModel<Array<PKGITEM | MANUALQUESITEM | any>>({
  default: [],
})

function deletePackageItem(item: any) {
  const { id, type } = item
  contentList.value = contentList.value.filter((item: any) => {
    if (item.type !== type) {
      return true
    }
    else {
      return item.id !== id
    }
  })
  emits('delete', item)
}
function swtichType(type: number, attachBaseId?: any) {
  emits('switchType', type, attachBaseId)
}
function switchItemEdit(id: any) {
  contentList.value.forEach((item: any) => {
    if (item.id === id) {
      item.isEdit = !item.isEdit
    }
  })
}
const contentRefs = ref([])
const contentContainerRef = ref<any>([])
function focusItem(item: any) {
  contentRefs.value.forEach((ref: any, index: number) => {
    if (ref.item.id === item.id) {
      ref.$el.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      })
      contentContainerRef.value[index].highlightFlash()
    }
  })
}
function validateContentRequired(id: any) {
  return new Promise((resolve, rej) => {
    contentRefs.value.some((ref: any) => {
      if (ref.item.isEdit) {
        focusItem(ref.item)
        ElMessage({
          message: '请保存题目内容',
          type: 'warning',
        })
        rej('请保存题目内容')
        return true
      }
      else if (ref.item.type !== 'pkg' && !ref.item.configSchema.content) {
        focusItem(ref.item)
        ElMessage({
          message: '题干未填写，请补充',
          type: 'warning',
        })
        rej('题干内容未填写')
        return true
      }
      return null
    })
    resolve('success')
  })
}
defineExpose({
  focusItem,
  validateContentRequired,
})
</script>

<template>
  <div v-if="!isEmpty" class="w-full flex flex-wrap content-start h-full">
    <TransitionGroup name="list" class="w-full">
      <div v-for="item in contentList" :key="item" class="w-full ">
        <ContentItem ref="contentContainerRef" :item="item" :type="item.type" @switch-type="swtichType" @delete-pkg="deletePackageItem" @switch-item-edit="switchItemEdit">
          <PkgContentItem v-if="item.type === 'pkg'" ref="contentRefs" :item="item" @delete-pkg="deletePackageItem" />
          <QuestionContentItem
            v-if="item.type === 'question'"
            ref="contentRefs" 
            :item="item"
            :question-detail="item" 
            show-score
            show-type
            show-tag
            show-correct-answer
            show-question-difficulty
            show-question-points-and-basis
            show-question-proofreading-record
            option-letter-type="text"
            @switch-item-edit="switchItemEdit"
          />
          <ManualQuestionContent v-if="item.type === 'manual'" ref="contentRefs" :item="item" /> 
        </ContentItem>
      </div>
    </TransitionGroup> 
    <TypeButton class="w-full" @switch-type="swtichType" />
  </div>
</template>

<style scoped lang="less">
.list-move {
  transition: all 0.5s ease;
}

.list-leave-active {
  position: absolute;
}
</style>