<template>
    <div>
        <div v-if="list?.length === 0" class="common-no-data">
            <span class="desc">无子题库</span>
        </div>
        <a-spin v-else :spinning="loading">
            <div ref="chartRef" style="min-height: 100%;" :style="{ height: height + 'px' }"></div>
        </a-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, watch, nextTick, shallowRef } from 'vue'
import { quescatgrefproportion } from '@/api/admin/statisticAnalysis'
import { ECharts, init } from 'echarts'

const props = defineProps<{
    id?: string
}>()

// 获取数据
const list = ref<{
    name: string
    quantity: number
    refs: number
}[]>()

const loading = ref(false)
async function getList() {
    if (!props.id) return
    loading.value = true
    try {
        let res = await quescatgrefproportion({ categoryId: props.id }) as any
        list.value = res
    } finally {
        loading.value = false
    }
}

// 图表绘制
const chartRef = ref()
const chart = shallowRef<ECharts>()
const height = ref(0)

async function draw() {
    // 检查图表容器是否存在以及数据是否加载完成
    if (!chartRef.value || !list.value) return

    // 动态设置图表高度，确保每个条目有足够的显示空间
    height.value = list.value.length * 60
    await nextTick()

    // 初始化图表实例
    chart.value = init(chartRef.value)

    // 配置图表选项
    chart.value?.setOption({
        tooltip: {
            trigger: 'item', // 设置触发类型为单个数据项
            formatter: `{b}<br />{a}：{c}` // 格式化提示框内容
        },
        legend: {
            icon: "circle", // 图例图标样式
            top: 0, // 图例距离顶部的距离
            right: 20, // 图例距离右侧的距离
            textStyle: {
                color: 'rgba(0, 0, 0, 0.45)' // 图例文字颜色
            },
            itemWidth: 9, // 图例图标宽度
            itemHeight: 9, // 图例图标高度
        },
        grid: {
            left: 90, // 图表网格左边距
            top: 50, // 图表网格顶部边距
            bottom: 40 // 图表网格底部边距
        },
        xAxis: [
            {
                type: 'value', // 设置X轴为数值轴
                name: '（题量）', // X轴名称
                nameLocation: 'start', // 名称位置
                offset: 0, // 偏移量
                nameTextStyle: {
                    color: 'rgba(0, 0, 0, 0.45)', // 名称文字颜色
                    align: 'right', // 对齐方式
                    verticalAlign: 'top', // 垂直对齐方式
                    lineHeight: 28, // 行高
                },
                alignTicks: true, // 对齐刻度
            },
            {
                type: 'value', // 第二个X轴，数值轴
                name: '（频次）', // X轴名称
                nameLocation: 'start',
                offset: 0,
                alignTicks: true,
                nameTextStyle: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    align: 'right',
                    verticalAlign: 'bottom',
                    lineHeight: 28,
                },
            }
        ],
        yAxis: {
            type: 'category', // 设置Y轴为类目轴
            axisTick: { show: false }, // 隐藏刻度线
            axisLine: {
                lineStyle: {
                    color: 'rgba(0, 0, 0, 0.45)' // Y轴线颜色
                }
            },
            axisLabel: {
                width: 60, // 标签宽度
                overflow: 'break' // 标签溢出换行
            },
            data: list.value.map(item => item.name) // 设置Y轴数据
        },
        series: [
            {
                type: 'bar', // 设置系列类型为柱状图
                name: '频次', // 系列名称
                barMaxWidth: 20, // 最大柱宽
                xAxisIndex: 1, // 使用第二个X轴
                itemStyle: {
                    borderWidth: 2, // 边框宽度
                    borderColor: 'transparent' // 边框颜色
                },
                color: {
                    type: 'linear', // 渐变颜色
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                        { offset: 0, color: '#78c5ff' },
                        { offset: 1, color: '#5B8FF9' }
                    ],
                    global: false
                },
                label: { show: false }, // 隐藏标签
                data: list.value.map(item => ({
                    value: item.refs, // 数据值
                    name: item.name // 数据名称
                }))
            },
            {
                type: 'bar',
                name: '题量',
                barMaxWidth: 20,
                xAxisIndex: 0, // 使用第一个X轴
                itemStyle: {
                    borderWidth: 2,
                    borderColor: 'transparent'
                },
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                        { offset: 0, color: '#a6e9c4' },
                        { offset: 1, color: '#74d49a' }
                    ],
                    global: false
                },
                label: { show: false },
                data: list.value.map(item => ({
                    value: item.quantity,
                    name: item.name
                }))
            }
        ]
    })

    // 添加窗口大小变化监听器以调整图表大小
    window.addEventListener('resize', resizeChart)
}

function resizeChart() {
    // 调整图表大小
    chart.value?.resize()
}

watch(() => props.id, async (val) => {
    if (!val) return
    await getList()
    draw()
}, { immediate: true })

onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
        height: 100%;
    }
}

.common-no-data {
    display: flex;
    align-items: center;
    justify-content: center;

    .desc {
        position: relative;
        top: 60px;
        color: rgba(0, 0, 0, 0.3);
        font-size: 16px;
        user-select: none;
    }
}
.ant-spin-container > div {
    // overflow-x: clip;
    display: flex;
    justify-content: center;
}
</style>