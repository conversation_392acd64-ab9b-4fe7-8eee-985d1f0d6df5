<template>
  <div class="resume-manage-container">
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <h3 class="resume-manage-title">
        <span class="first-title" @click="router.back">人工阅卷 /</span>
        <span class="sub-title">阅卷《{{route.query.name}}》</span>
      </h3>
      <a-dropdown :trigger="['click']">
        <span style="cursor: pointer;">
          <svg-icon v-if="mode === 'sun'" name="sun" width="18px" height="18px" />
          <svg-icon v-else name="moon" width="18px" height="18px" />
        </span>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="handleChangeMode('sun')">
              <span class="mode-item">
                <svg-icon name="sun" width="18px" height="18px" />
                <span>默认模式</span>
              </span>
            </a-menu-item>
            <a-menu-item @click="handleChangeMode('moon')">
              <span class="mode-item">
                <svg-icon name="moon" width="14px" height="14px" />
                <span>护眼模式</span>
              </span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div class="resume-manage">
      <div class="left">
        <div style="flex: 1;min-height: 0;overflow: scroll;padding-right: 10px;" v-if="list.length">
          <template v-for="item in list">
            <template v-if="item.children.length">
              <span class="item-title">{{ item.title }}</span>
              <div class="item-answer-options">
                <div v-for="q in item.children" :key="q.id">
                  <span class="item-answer-option" :class="{ active: q.active }" @click="handleClickQues(q.id, q.index, item)">{{ q.index }}</span>
                </div>
              </div>
            </template>
          </template>
        </div>
        <div v-if="!allQuestions.length" class="noPaper">暂无阅卷试题</div>
        <div class="page-answer-option-desc">
          <div class="item undo-box">未评分</div>
          <div class="item done-box">完成评分</div>
        </div>
      </div>
      <div class="right">
        <div class="right-main">
          <template v-if="quesDetail">
              <div class="header">
                <p class="questionType">
                  <!-- {{ QuestionEnum[quesDetail.type] }}  -->
                  {{ currentTypeTitle && currentTypeTitle.title || QuestionEnum[quesDetail.type] }} 
                </p>
                <span class="score">{{ quesDetail.paper_ques_score }}分</span>
              </div>
              <div style="flex: 1;min-height: 0;overflow: auto;">
                <!-- 题目详情 -->
                <div class="questionDetail">
                  <div class="title-info">
                    <span class="question-index" style="font-size: 16px;margin-right: 8px;">{{ qIndex }}.</span>
                    <p class="stem" v-html="quesDetail.complicatedediting ? quesDetail.complexcontent : transferStr(quesDetail.body)" @click="$event => findImageAndPreview($event)"></p>
                  </div>
                  <template v-if="quesDetail.type == QuestionEnum['填空题']">
                    <div class="question-info-item">
                      <div class="label">正确答案</div>
                      <div class="question-info-list">
                        <div class="question-info-list-item" :class="{active: quesDetail.ordered && (fillblankHoverIndex === index)}" v-for="(item, index) in quesDetail.answer" :key="index">
                          <div class="index" v-if="quesDetail.ordered">{{ index+1 }}.</div>
                          <div class="index" v-else style="font-weight: bold;">·</div>
                          <div class="word">{{ item.keyword.join('/') }}</div>
                          <div class="score">{{ item.score }}分</div>
                        </div>
                      </div>
                    </div>
                    <div class="question-info-item">
                      <div class="label">题目配置</div>
                      <div>
                        <span style="color: #d71310;">{{ quesDetail.ordered ? '区分' : '忽略' }}</span>
                        <span :style="{color: mainTextColor}">答案先后顺序 / </span>
                        <span style="color: #d71310;">{{ quesDetail.ignorecase ? '忽略' : '区分' }}</span>
                        <span :style="{color: mainTextColor}">大小写</span>
                      </div>
                    </div>
                  </template>
                  <template v-if="quesDetail.type == QuestionEnum['问答题']">
                    <div class="accord">
                      <span class="accordScore">评分依据</span>
                      <span class="accordText" v-if="quesDetail.scorebasis" v-html="quesDetail.complicatedediting ? quesDetail.scorebasis : transferStr(quesDetail.scorebasis)" @click="$event => findImageAndPreview($event)"></span>
                      <span class="accordText" v-else>未填写</span>
                    </div>
                    <div class="scorePoints">
                      <div class="label">得分点</div>
                      <div class="wrapper">
                        <div class="content" v-for="(item, index) in quesDetail.answer" :key="index">
                          <div class="scorePointIndex">{{ index + 1 }}</div>
                            <div class="scoPointDetail">
                              <div
                                class="scorePoint"
                                v-for="(key, indey) in item.keyword"
                                :key="indey">
                                {{ key }}
                              </div>
                            </div>
                          <div class="scorePoint score">{{ item.score }}分</div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div v-if="quesDetail.type == QuestionEnum['算法题'] && ALGORITHM_DIFFICULTY[quesDetail.difficulty]" style="margin-bottom: 8px;">
                    <span>难度: {{ ALGORITHM_DIFFICULTY[quesDetail.difficulty].label }}</span>
                  </div>
                  <div v-if="quesDetail.type == QuestionEnum['复合题']" class="com-qa">
                    <!-- 复合中问答题 -->
                    <template v-for="item in composeQa" :key="item.id">
                      <div class="title-info">
                        <span class="score">{{ item.quesscore }}分</span>
                        <p
                          class="stem"
                          v-html="item.complicatedediting ? item.complexcontent : item.body"
                        ></p>
                      </div>
      
                      <div class="accord">
                        <span class="accordScore">评分依据</span>
                        <span class="accordText" v-if="quesDetail.scorebasis" v-html="quesDetail.complicatedediting ? quesDetail.scorebasis : transferStr(currentQuestionObj.scorebasis)" @click="$event => findImageAndPreview($event)"></span>
                        <span class="accordText" v-else>未填写</span>
                      </div>
                      <div class="scorePoints">
                        <span>得分点</span>
                        <div class="wrapper">
                          <div
                            class="content"
                            v-for="(subitem, index) in getItemPoints(item.answer)"
                            :key="index"
                          >
                            <div class="scorePointIndex">{{ index + 1 }}</div>
                            <div class="scoPointDetail">
                              <div
                                class="scorePoint"
                                v-for="(keyword, indey) in subitem.keywords"
                                :key="indey">
                                {{ keyword }}
                              </div>
                            </div>
                            <div>{{ subitem.score }}</div>
                          </div>
                        </div>
                      </div>
                      <div :class="['keyToTest', 'subitem', { activeAnimate: isActive }]">
                        <p>考生答案</p>
                        <div class="content">
                          <div
                            class="text"
                            v-html="highLightKeywords(item.stuanswer, getItemPoints(item.answer))"
                          />
                        </div>
                        <footer>
                          <div>
                            <span class="systemScore">系统评分</span
                            ><span style="color: red">{{ item.sysscore ? item.sysscore : 0 }}分</span>
                          </div>
                          <div class="manualScore">
                            <span>人工评分</span
                            ><a-input-number
                              :controls="false"
                              :formatter="(value: number) => Math.floor(value)"
                              v-model:value="item.score"
                              :min="0"
                            />分
                          </div>
                        </footer>
                      </div>
                    </template>
      
                    <div v-if="composeQa.length" class="confirm-btn">
                      <button class="confirm" @click="composeConfirm(composeQa)">确认评分</button>
                    </div>
      
                    <!-- 复合中客观题 -->
                    <a-collapse
                      v-if="
                        composeSingle.length +
                        composeMuti.length +
                        composeJudge.length +
                        composeFill.length +
                        composeSort.length
                      "
                      v-model:activeKey="activeKey"
                    >
                      <a-collapse-panel key="1" header="客观题（无需人工阅卷）">
                        <!-- 单选 -->
                        <div v-if="composeSingle.length">
                          <div class="sub-item-title">单选题</div>
                          <template v-for="item in composeSingle" :key="item.id">
                            <div class="title-info">
                              <span class="score">{{ item.quesscore }}分</span>
                              <p
                                class="stem"
                                v-html="item.complicatedediting ? item.complexcontent : item.body"
                              ></p>
                            </div>
                            <div class="sub-item-options">
                              <template v-for="option in item.options" :key="option.value">
                                <div class="item">
                                  <radio-btn
                                    class="radio-btn"
                                    :option="option.value"
                                    :class="getDynamicClass(item.answer, item.stuanswer, option.value)"
                                  />
                                  <span
                                    class="option-content"
                                    :class="
                                      getDynamiContentClass(item.answer, item.stuanswer, option.value)
                                    "
                                    v-html="option.content"
                                  ></span>
                                  <span
                                    class="correct-answer-tag"
                                    v-if="item.answer.includes(option.value)"
                                    >正确答案</span
                                  >
                                </div>
                              </template>
                            </div>
                            <a-divider />
                          </template>
                        </div>
                        <!-- 多选 -->
                        <div v-if="composeMuti.length">
                          <div class="sub-item-title">多选题</div>
                          <template v-for="item in composeMuti" :key="item.id">
                            <div class="title-info">
                              <span class="score">{{ item.quesscore }}分</span>
                              <p
                                class="stem"
                                v-html="item.complicatedediting ? item.complexcontent : item.body"
                              ></p>
                            </div>
                            <div class="sub-item-options">
                              <template v-for="option in item.options" :key="option.value">
                                <div class="item">
                                  <radio-btn
                                    class="radio-btn"
                                    :option="option.value"
                                    :class="getDynamicClass(item.answer, item.stuanswer, option.value)"
                                  />
                                  <span
                                    class="option-content"
                                    :class="
                                      getDynamiContentClass(item.answer, item.stuanswer, option.value)
                                    "
                                    v-html="option.content"
                                  ></span>
                                  <span
                                    class="correct-answer-tag"
                                    v-if="item.answer.includes(option.value)"
                                    >正确答案</span
                                  >
                                </div>
                              </template>
                            </div>
                            <a-divider />
                          </template>
                        </div>
                        <!-- 判断 -->
                        <div v-if="composeJudge.length">
                          <div class="sub-item-title">判断题</div>
                          <template v-for="item in composeJudge" :key="item.id">
                            <div class="title-info">
                              <span class="score">{{ item.quesscore }}分</span>
                              <p
                                class="stem"
                                v-html="item.complicatedediting ? item.complexcontent : item.body"
                              ></p>
                            </div>
                            <div class="sub-item-options">
                              <template v-for="option in item.options" :key="option.value">
                                <div class="item">
                                  <radio-btn
                                    class="radio-btn"
                                    :option="option.value"
                                    :class="getDynamicClass(item.answer, item.stuanswer, option.value)"
                                  />
                                  <span
                                    class="option-content"
                                    :class="
                                      getDynamiContentClass(item.answer, item.stuanswer, option.value)
                                    "
                                    v-html="option.content"
                                  ></span>
                                  <span
                                    class="correct-answer-tag"
                                    v-if="item.answer.includes(option.value)"
                                    >正确答案</span
                                  >
                                </div>
                              </template>
                            </div>
                            <a-divider />
                          </template>
                        </div>
                        <!-- 填空题 -->
                        <div v-if="composeFill.length">
                          <div class="sub-item-title">填空题</div>
                          <template v-for="item in composeFill" :key="item.id">
                            <div class="title-info">
                              <span class="score">{{ item.quesscore }}分</span>
                              <p
                                class="stem"
                                v-html="
                                  getBodyHtml(
                                    item.complicatedediting ? item.complexcontent : item.body,
                                    item.stuanswer
                                  )
                                "
                              ></p>
                            </div>
                            <p class="correct-answer">正确答案：{{ getCorrectAnswer(item.answer) }}</p>
                            <a-divider />
                          </template>
                        </div>
                        <!-- 排序题 -->
                        <div v-if="composeSort.length">
                          <div class="sub-item-title">排序题</div>
                          <template v-for="item in composeSort" :key="item.id">
                            <div class="title-info">
                              <span class="score">{{ item.quesscore }}分</span>
                              <p
                                class="stem"
                                v-html="item.complicatedediting ? item.complexcontent : item.body"
                              ></p>
                            </div>
                            <div class="sort-options">
                              <template v-for="option in item.options" :key="option.value">
                                <div class="item" v-html="option.content"></div>
                              </template>
                            </div>
                            <p class="correct-answer">
                              考生答案：{{
                                judgeAnswerCorrect(item.stuanswer, item.answer) ? '正确' : '错误'
                              }}
                            </p>
                          </template>
                        </div>
                      </a-collapse-panel>
                    </a-collapse>
                  </div>
                </div>
                <!-- 考生答案 -->
                <template v-if="!isFinished && currentQuestionObj?.stuanswer">
                  <div v-if="quesDetail.type != QuestionEnum['复合题']" :class="['keyToTest', { activeAnimate: isActive }]">
                    <p style="display: flex; justify-content: space-between">
                      <span v-if="currentQuestionObj.stuname" :title="currentQuestionObj.stuname" class="student-name">{{ currentQuestionObj.stuname }}</span>
                      <span v-else>考生答案</span>
                      <span style="font-weight: normal;padding-right: 20px;">
                        该题阅卷进度：
                        <span style="color: #5478ee">{{`${currentQuestionObj.t_count - currentQuestionObj.not_c_count + 1} / ${currentQuestionObj.t_count}`}}</span>
                      </span>
                    </p>
                    <div class="content">
                      <div class="question-info-list" v-if="quesDetail.type == QuestionEnum['填空题']">
                        <div class="question-info-list-item answer" :class="{active: fillblankHoverIndex === index}" v-for="(item,index) in currentQuestionObj.stuanswer" @mouseenter="handleFillblankMouseOver(index)" @mouseleave="handleFillblankMouseLeave">
                          <div class="switch-container">
                            <JSwitchPlus v-model:checked="item.is_true" :expanded="fillblankHoverIndex === index" @change="reCalcScore"></JSwitchPlus>
                          </div>
                          <div class="index" v-if="currentQuestionObj.ordered">{{ index+1 }}.</div>
                          <div class="index" v-else style="font-weight: bold;">·</div>
                          <span>{{ item['text'+index] }}</span>
                        </div>
                      </div>
                      <template v-if="currentQuestionObj.type == QuestionEnum['问答题']">
                        <pre class="text" v-html="highLightKeywords(currentQuestionObj.stuanswer, currentQuestionObj.answer)" />
                        <div class="answer-imgs">
                          <template v-for="img in getAnswerImgs(currentQuestionObj.stuanswer)" :key="img">
                            <div class="item">
                              <a-image :src="getImgUrl(img)" />
                            </div>
                          </template>
                        </div>
                      </template>
                      <CodeBox v-if="quesDetail.type == QuestionEnum['算法题']" autodetect :language="currentQuestionObj.language" :code="currentQuestionObj.stuanswer"></CodeBox>
                    </div>
                    <div class="out-answer" v-if="quesDetail.type !== QuestionEnum['问答题']">
                      <span>
                        系统评分 <span style="color: red">{{ currentQuestionObj.sysscore ?? 0 }}分</span>
                      </span>
                      <span v-if="quesDetail.type == QuestionEnum['算法题']">
                        通过测试用例数 <span :class="getPasscaseClass(currentQuestionObj.passcase)">{{ currentQuestionObj.passcase }}</span>
                      </span>
                    </div>
                    <footer>
                      <div>
                        <a-button class="common-ai-button" :loading="answerAnalysisLoading" :disabled="!!answerAnalysis.explain" style="font-size: 12px; height: 24px;padding: 0 8px;" @click="handleAnalysisAnswer">
                          <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;" />
                          答案分析
                        </a-button>
                      </div>
                      <div class="manualScore">
                        <span>人工评分</span
                        ><a-input-number
                          :controls="false"
                          :formatter="(value: number) => Math.floor(value)"
                          v-model:value="score"
                          :min="0"
                          :max="currentQuestionObj.quesscore"
                        />分
                        <button class="confirm" @click="confirm">确认</button>
                        <button
                          v-if="currentQuestionObj?.not_c_count !== 1"
                          class="confirm normal"
                          @click="handleSkip(ques)"
                        >
                          跳过
                        </button>
                      </div>
                    </footer>
                    <div class="ai-text-panel" v-if="answerAnalysis.explain">
                      <div>
                        <div class="label">AI评分</div>
                        <div class="value">{{ answerAnalysis.score }}分</div>
                      </div>
                      <div style="margin-top: 8px;">
                        <div class="label">评分理由</div>
                        <div class="value">
                          <FoldText 
                            :text="answerAnalysis.explain" 
                            :feedback-params="{ 
                              apiname: 'aiquesansweranalysis',
                              input: currentQuestionObj,
                              output: answerAnalysis
                            }">
                          </FoldText>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="finished-box">
                    <img src="@/assets/icons/svg/correct-finished.svg" alt="">
                    <span style="position: relative; top: -10px;color: rgba(0,0,0,0.45);">该题已阅完</span>
                    <a-button v-if="isFinished && !isAllActive" type="primary" size="small" style="border-radius: 4px;font-size: 10px;margin-top: 4px;height: 24px;width: 58px;" @click="getNextQuestion">下一题</a-button>
                  </div>
                </template>
                <!-- 历史记录 -->
                <HistoryCard
                  :key="quesDetail.id"
                  v-if="historyList.length"
                  :historyList="historyList"
                  :questionDetailInfo="quesDetail"
                  :published="published"
                  :defaultExpanded="isFinished" />
              </div>
            <!-- </template> -->
          </template>
        </div>
        <div class="bottom">
          <a-divider style="marginTop: 0;"></a-divider>
          <div class="btn-list">
            <button v-if="!isAllActive" class="nextQuestion" @click="getNextQuestion">下一题</button>
            <button class="close" @click="router.back">结束阅卷</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { manualmarking,addmanualmarking, needcorrected, markinghistory, markedques } from '@/api/admin/paperManage'
import { getPaperQuesDetail } from '@/api/admin/questionManage'
import RadioBtn from '@/components/RadioBtn/index.vue'
import router from '@/router'
import store from '@/store'
import { message } from 'ant-design-vue'
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { transferStr } from '@/utils/common'
import { PaperFbodyItemModel } from '@/models/paperModel'
import { findImageAndPreview } from '@/utils/common'
import { QuestionEnum } from '@/models/questionModel'
import JSwitchPlus from '@/components/JSwitchPlus.vue'
import { ALGORITHM_DIFFICULTY } from '@/config/constants'
import CodeBox from '@/components/CodeBox.vue'
import HistoryCard from '@/pages/manualMark/components/HistoryCard.vue'
import { aiquesansweranalysis } from '@/api/exam'
import FoldText from '@/components/FoldText.vue'
import { useState } from '@/hooks'
import { text } from 'stream/consumers'

const route = useRoute()
const paperId = route.query.id as string
const published = route.query.published == '1'
// 最终分数
const score = ref(0)
const ques = ref('')

const isActive = ref(false)
const isFinished = ref(false)
const qIndex = ref(1)  // 当前题目在试卷中的题号
const loading = ref(false)
// 题目详情
const quesDetail = ref<any>(null)
// 获取题目详细信息
const getQuesDetail = async (id: string) => { // 获取试题详情
  try {
    loading.value = true
    const res = await getPaperQuesDetail({ templeteId: paperId, quesId: id })
    let answer = {}
    try {
      answer = JSON.parse(res.answer)
    } catch (error) {
      // do nothing
    }
    quesDetail.value = {
      ...res,
      answer,
    }
  } catch (error) {
    console.log(error)
    quesDetail.value = null
  } finally {
    loading.value = false
  }
}

// 历史记录
const historyList = ref<any[]>([])
const getHistoryListFn = async (id: string) => {
  try {
    historyList.value = []
    const res = await markinghistory({
      action: 'query',
      quesId: id,
      templeteId: paperId,
      teacher: store.getters.userid
    })
    historyList.value = Array.isArray(res) ? res.map((item: any) => {
      let stuanswer = item.stuanswer
      if (item.type === 5) {
        try {
          stuanswer = JSON.parse(item.stuanswer)
        } catch (error) {
          stuanswer = { text0: item.stuanswer }
        }
      }
      return {
        correctiontime: item.correctiontime,
        examId: item.examId,
        answer: item.answer,
        stup: item.stup,
        stuname: item.stuname,
        stuanswer,
        score: item.score,
        newscore: item.score,
        corrector: item.corrector,
        passcase: item.passcase,
        ext: item.ext,
        fillblankHoverIndex: -1,
        loading: false
      }
    }) : []
  } catch (error) {
    historyList.value = []
  }
}

const defaultQusetionObj = {
  id: '',
  body: '',
  quesscore: 0,
  scorebasis: '',
  difficulty: '',
  passcase: '',
  subtopic: [],
  sysscore: 0,
  t_count: 0,
  not_c_count: 0,
  type: 0
}
// 题目作答信息
const currentQuestionObj = ref<{
  id: string
  answer?: { keyword: string[]; score: number }[]
  body: string
  complexcontent?: string
  complicatedediting?: boolean
  difficulty: string
  ignorecase?: boolean
  language?: string | null
  ordered?: boolean
  passcase: string
  subtopic: any[]
  /** 题目总分 */
  quesscore: number
  score?: number
  scorebasis: string
  stuanswer?: any
  stuname?: string | null
  stup?: string
  sysscore: number
  /** 本题目一共需要阅卷的数量 */
  t_count: number
  /** 本题目没阅过的数量 */
  not_c_count: number
  type: number
}>(defaultQusetionObj)
async function getTestDetail(itemId: string, index?: number) {
  loading.value = true
  ques.value = itemId
  isActive.value = true
  try {
    let res = await manualmarking({
      action: 'query',
      quesId: itemId,
      templeteId: paperId,
      teacher: store.getters.userid,
      examId: currentQuestionObj.value?.stup // 该字段为了跳过当前题目的当前学生的答案
    }) as any
    index && (qIndex.value = index)
    setTimeout(() => { isActive.value = false }, 300)
    if (res === 'finished') {
      console.log('wrewer')
      isFinished.value = true
      currentQuestionObj.value = defaultQusetionObj
      list.value.forEach(item => {
        item.children.forEach((k: any) => {
          if (k.id === itemId) {
            k.active = true
          }
        })
      })
      console.log('isFinished.value', isFinished.value)
    } else {
      if (res.answer) {
        res.answer = JSON.parse(res.answer).map((item: any) => ({ keyword: item.keyword, score: item.score }))
      }
      res.id = itemId
      currentQuestionObj.value = res

      // 回显ai答题分析
      if (res.answer_analysis) {
        answerAnalysis.value = typeof res.answer_analysis == 'string' ? JSON.parse(res.answer_analysis) : res.answer_analysis
      } else {
        resetAnswerAnalysis()
      }

      score.value = res.sysscore || 0
      isFinished.value = false
    }
  } catch (error) {
    currentQuestionObj.value = defaultQusetionObj
    console.log(error)
  } finally {
    loading.value = false
  }
}

const activeKey = ref(0)

// 复合题中的问答题
const composeQa = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 3)
})

const composeSingle = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 0)
})
const composeMuti = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 1)
})
const composeJudge = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 2)
})
const composeFill = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 5)
})
const composeSort = computed(() => {
  if (!currentQuestionObj.value.subtopic.length) return []
  return currentQuestionObj.value.subtopic.filter((item: { type: number }) => item.type === 6)
})
const getItemPoints = (answer: string) => {
  return JSON.parse(answer || '[]').map((item: any) => {
    return { keywords: item.keyword, score: item.score }
  })
}

// 动态样式class
const getDynamicClass = (answer: string, stuanswer: string, option: string) => {
  const formatStuanswer = stuanswer.replaceAll(',', '')
  const formatAnswer = answer.split('').sort().join('')
  if (formatAnswer === formatStuanswer && formatStuanswer.includes(option)) return 'correct'
  if (formatAnswer !== formatStuanswer && formatStuanswer.includes(option)) return 'error'
  return ''
}
const getDynamiContentClass = (answer: string, stuanswer: string, option: string) => {
  const formatStuanswer = stuanswer.replaceAll(',', '')
  const formatAnswer = answer.split('').sort().join('')
  if (formatAnswer === formatStuanswer && formatStuanswer.includes(option)) return 'correct-text'
  if (formatAnswer !== formatStuanswer && formatStuanswer.includes(option)) return 'error-text'
  return ''
}
const getPasscaseClass = (passcase: string) => {
  let arr = passcase.split('/')
  return arr[0] === arr[1] ? 'correct-text' : 'error-text'
}

const getBodyHtml = (body: string, stuanswer: string) => {
  const fillblanks = body.match(/_{2,}/g) as RegExpMatchArray
  let answerObj = JSON.parse(stuanswer)
  for (let i = 0; i < fillblanks.length; i++) {
    body = body.replace(
      fillblanks[i],
      '<span style="color:red;padding:0 6px;border-bottom: 1px solid rgba(0,0,0,0.86)">' +
        answerObj[`text${i}`] +
        '</span>'
    )
  }

  return body
}

const getCorrectAnswer = (answer: string) => {
  const answerObj = JSON.parse(answer)
  const correctAnswer: any[][] = []
  answerObj.forEach((item: { keyword: any }) => {
    correctAnswer.push([...item.keyword])
  })
  return correctAnswer.join('  ')
}

const judgeAnswerCorrect = (stuanswer: string, answer: string) => {
  const stuanswerFormat = stuanswer.split(',').join('')
  const answerFormat = JSON.parse(answer).join('')
  return stuanswerFormat === answerFormat
}




async function confirm() {
  if (score.value > currentQuestionObj.value.quesscore) return message.error('当前题目所得分值不能超过总分值')
  let params: any = {
    action: 'add',
    examId: currentQuestionObj.value.stup,
    quesScore: [{ ques: ques.value, score: Number(score.value) }],
    teacher: store.getters.userid
  }
  if (currentQuestionObj.value.type == QuestionEnum['填空题']) {
    // 将填空题的勾选状态传给后端
    params.quesScore[0].ext = currentQuestionObj.value.stuanswer
  }
  await addmanualmarking(params)
  getTestDetail(ques.value)
  getHistoryListFn(ques.value)
}

// 复合题确认评分
const composeConfirm = (quesInfo: any) => {
  for (let i = 0; i < quesInfo.length; i++) {
    const item = quesInfo[i]
    if (item.score > item.quesscore) {
      message.error('当前题目所得分值不能超过总分值')
      return
    }
  }
  const quesScore = quesInfo.map((item: { id: any; score: any }) => ({
    ques: item.id,
    score: item.score
  }))
  addmanualmarking({
    action: 'add',
    examId: currentQuestionObj.value.stup,
    quesScore,
    teacher: store.getters.userid
  })
    .then(() => {
      getTestDetail(ques.value)
      getHistoryListFn(ques.value)
    })
    .catch((error) => {
      console.log(error)
    })
}
const currentTypeTitle = ref(null)
// 下一题
const getNextQuestion = () => {
  const index = allQuestions.value.findIndex((item: any) => item.id === ques.value)
  const currentQues = allQuestions.value[index]

  for (let i = index + 1; i < allQuestions.value.length; i++) {
    // 找到后面第一个未批改的试题
    if (!allQuestions.value[i].active) {
      getQuesDetail(allQuestions.value[i].id)
      getTestDetail(allQuestions.value[i].id, allQuestions.value[i].index)
      getHistoryListFn(allQuestions.value[i].id)
      list.value.some(item => {
        return item.children.some(child => {
          if(child.id == allQuestions.value[i].id) {
          currentTypeTitle.value = item
          return true
          }
        })
      })
      return
    }
  }
  for (let i = 0; i < index; i++) {
    // 找到前面第一个未批改的试题
    if (!allQuestions.value[i].active) {
      getQuesDetail(allQuestions.value[i].id)
      getTestDetail(allQuestions.value[i].id, allQuestions.value[i].index)
      getHistoryListFn(allQuestions.value[i].id)
      list.value.some(item => {
        return item.children.some(child => {
          if(child.id == allQuestions.value[i].id) {
          currentTypeTitle.value = item
          return true
          }
        })
      })
      return
    }
  }
}

// 主题颜色设置
const resumeManageBg = computed(() => {
  return store.getters.mode === 'sun' ? '#fff' : '#181A20'
})
const firstTitleColor = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(0, 0, 0, 0.45)' : 'rgba(255,255,255,0.45)'
})
const subTitleColor = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(0, 0, 0, 0.85)' : '#fff'
})
const borderRightColor = computed(() => {
  return store.getters.mode === 'sun' ? '#e8e8e8' : '#2D2D2E'
})
const mainTextColor = computed(() => {
  return store.getters.mode === 'sun' ? '#121633' : 'rgba(255,255,255,0.85)'
})

const optionBorderColor = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(0, 0, 0, 0.1)' : 'rgb(193, 193, 193, 40%)'
})
const optionBg = computed(() => {
  return store.getters.mode === 'sun' ? '#ececec' : '#181A20'
})
const optionTextColor = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(0, 0, 0, 0.65)' : 'rgba(255,255,255,0.65)'
})
const tagBg = computed(() => {
  return store.getters.mode === 'sun' ? '#F5F5F5' : 'rgba(255,255,255,0.06)'
})
const tagscoreBg = computed(() => {
  return store.getters.mode === 'sun' ? '#f0f0f0' : 'rgba(255,255,255,0.1)'
})
const tagBorderColor = computed(() => {
  return store.getters.mode === 'sun' ? '#d9d9d9' : 'rgba(255,255,255,0.2)'
})
const answerBg = computed(() => {
  return store.getters.mode === 'sun' ? '#f5f5f5' : '#0d0d0d'
})
const inputBg = computed(() => {
  return store.getters.mode === 'sun' ? '#fff' : '#0d0d0d'
})
const collapseHeaderBg = computed(() => {
  return store.getters.mode === 'sun' ? '#F2F5FC' : '#181A20'
})
const collapseBodyBg = computed(() => {
  return store.getters.mode === 'sun' ? '#fff' : '#0d0d0d'
})
const scoreColor = computed(() => {
  return store.getters.mode === 'sun' ? '#838383' : 'rgba(255,255,255,0.65)'
})
const highlightBg = computed(() => {
  return store.getters.mode === 'sun' ? 'rgba(84, 120, 238, 0.3)' : 'rgba(84, 120, 238, 0.4)'
})
const highlightColor = computed(() => {
  return store.getters.mode === 'sun' ? '#0d0d0d' : '#fff'
})

// 高亮关键词
const highLightKeywords = (textContent: string, keywordstring: any) => {
// 一些数学题会出现 <x 这种符号，会被识别为未闭合的标签，所以需要将 <和> 替换为对应的转义字符
// 对于换行符也要替换成对应的<br>标签
  textContent = textContent.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\n/g, '<br>');
  const index = textContent.indexOf('#@$')
  if (index !== -1) {
    textContent = textContent.substring(0, index)
  }
  const keywordsArray: string[] = []
  keywordstring.forEach((item: any) => {
    item.keyword.forEach((key: string) => {
      keywordsArray.push(key)
    })
  })

  const escapedKeywords = keywordsArray.map((keyword) =>
    keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  )
  const pattern = new RegExp(`(${escapedKeywords.join('|')})`, 'gi')
  return textContent.replace(pattern, '<span class="highlight">$1</span>')
}

const getAnswerImgs = (sAnswer: string) => {
  const index = sAnswer.indexOf('#@$')
  if (index === -1) {
    return []
  } else {
    const answerImgsString = sAnswer.split('#@$')[1]
    console.log(answerImgsString.split(','))
    return answerImgsString.split(',')
  }
}

const getImgUrl = (item: string) => {
  return 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/' + item
}

// 获取可阅卷题目
const list = ref<
  (PaperFbodyItemModel & {
    children: (PaperFbodyItemModel['children'][number] & { active: boolean })[]
  })[]
>([])
const allQuestions = computed(() =>
  list.value.reduce(
    (prev, cur) => [...prev, ...cur.children],
    [] as (typeof list)['value'][number]['children']
  )
)

// 是否所有题目都阅完
const isAllActive = computed(() => allQuestions.value.every((i: any) => i.active))

// 获取教师有权限批改的试题
const getPaperQuestionList = async () => {
  try {
    const res = await markedques({ teacher: store.getters.userid, templeteId: paperId })
    list.value = Array.isArray(res) ? res.map((item: any, index: number) => ({
      ...item,
      children: Array.isArray(item.children) ? item.children.map((el: any) => ({
        ...el,
        active: el.marked_finish,
      })) : [],
    })) : []
    // 找到第一个需要批改的题目
    let firstQ = res?.flatMap((item: any) => item.children)?.find((item: any, index: number) => !item.marked_finish) || allQuestions.value[0]

    getQuesDetail(firstQ.id)
    getTestDetail(firstQ.id, firstQ.index)
    getHistoryListFn(firstQ.id)
  } catch (error) {
    list.value = []
  }
}
getPaperQuestionList()

const handleClickQues = (id: string, index?: number,item:object) => { // 点击左侧试题
  currentQuestionObj.value = defaultQusetionObj
  currentTypeTitle.value = item
  getQuesDetail(id)
  getTestDetail(id, index)
  getHistoryListFn(id)
}

const handleSkip = (id: string) => { // 跳过
  getTestDetail(id)
  getHistoryListFn(id)
}

// 填空题考生答案鼠标悬浮事件
const fillblankHoverIndex = ref(-1)
function handleFillblankMouseOver(index: number) {
  fillblankHoverIndex.value = index
}

function handleFillblankMouseLeave() {
  fillblankHoverIndex.value = -1
}

// 填空题开关切换事件，重新计算人工评分
function reCalcScore() {
  let newScore = 0
  currentQuestionObj.value.answer?.forEach((item, index) => {
    if (currentQuestionObj.value.stuanswer[index].is_true) {
      newScore += item.score
    }
  })
  score.value = newScore
}


// ai答案分析
const answerAnalysisLoading = ref(false)
const [answerAnalysis, resetAnswerAnalysis] = useState({
  score: 0,
  explain: ''
})
async function handleAnalysisAnswer() {
  try {
    answerAnalysisLoading.value = true
    let res = await aiquesansweranalysis({
      answer_info: currentQuestionObj.value
    })
    const aiScore = JSON.parse(res.aiScore) 
    answerAnalysis.value = aiScore
  } catch (error) {
    console.log(error)
  } finally {
    answerAnalysisLoading.value = false
  }
}

// 主题切换
const mode = computed(() => store.getters.mode)
function handleChangeMode(mode: 'sun' | 'moon') {
  if (store.getters.mode === 'sun') {
    store.commit('MODIFY_MODE', mode)
  } else {
    store.commit('MODIFY_MODE', mode)
  }
}

</script>

<style lang="less" scoped>
body.moon {
  .resume-manage-container {
    background-color: #000;
  }
  .ai-text-panel {
    background-color: #181A20!important;
    .value {
      color: rgba(255, 255, 255, 0.85)!important;
    }
  }
  .finished-box {
    background-color: #24262E;
    span {
      color: rgba(255, 255, 255, 0.85)!important;
    }
  }
}
.question-info-item {
  margin-bottom: 18px;
  display: flex;
  .label {
    color: #626262;
    margin-right: 32px;
    font-size: 14px;
  }
}

.question-info-list {
  .question-info-list-item {
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    margin-top: 2px;
    cursor: pointer;
    color: v-bind(mainTextColor);

    &.answer {
      margin-left: -20px;
    }

    &.active {
      // background-color: v-bind(hoverBgColor);
    }

    .index {
      margin-right: 4px;
    }
    .word {
      margin-right: 4px;
    }
  }
}

.switch-container {
  width: 40px;
  margin-right: 4px;
  display: flex;
  flex-direction: row-reverse;
  flex-shrink: 0;
}

:deep(.highlight) {
  background: v-bind(highlightBg) !important;
  color: v-bind(highlightColor);
}

.nextQuestion {
  margin-right: 8px;
  display: block;
  padding: 0 20px;
  height: 32px;
  background: #5478ee;
  border-radius: 8px;
  color: #fff;
  border: 0;
  cursor: pointer;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

/* 火狐 */
input {
  -moz-appearance: textfield;
}

* {
  font-family: PingFang SC, PingFang SC-Regular;
}

.item-answer-option.active {
  background: #5478ee;
  border: 1px solid #5478ee;
  border-radius: 8px;
  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
    -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
}

.resume-manage-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding: 0 20px 20px 20px;
}

.resume-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;

  .first-title {
    color: v-bind(firstTitleColor);
  }

  .sub-title {
    color: v-bind(subTitleColor);
  }
}

.resume-manage {
  min-width: 1200px;
  flex: 1;
  min-height: 0;
  display: flex;
  background-color: v-bind(resumeManageBg);
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);

  .left {
    position: relative;
    padding: 18px 8px 18px 24px;
    width: 234px;
    border-right: 1px solid v-bind(borderRightColor);
    display: flex;
    flex-direction: column;

    .item-title {
      &:not(:first-child) {
        margin-top: 16px;
      }
      display: inline-block;
      font-size: 14px;
      font-weight: bold;
      color: v-bind(mainTextColor);
    }

    .noPaper {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0.3;
      color: v-bind(mainTextColor);
    }

    .item-answer-options {
      padding-top: 16px;
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-row-gap: 6px;

      .item-answer-option {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        background-image: linear-gradient(180deg, v-bind(resumeManageBg), v-bind(optionBg) 100%);
        border: 1px solid v-bind(optionBorderColor);
        box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: v-bind(optionTextColor);
        text-align: center;
        font-weight: 400;
        cursor: pointer;

        &:nth-child(5n) {
          margin-right: 0;
        }
      }

      .active {
        background: #5478ee;
        background-image: none;
        border: 1px solid rgba(84, 120, 238, 1);
        box-shadow: inset -2px -2px 2px 0px rgba(255, 255, 255, 0.15);
        box-shadow: inset 2px 2px 1px 0px rgba(0, 0, 0, 0.15), 1px 1px 2px 0px rgba(0, 0, 0, 0.15);
        color: #fff;
      }
    }

    .page-answer-option-desc {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 19px 0;
      margin-top: 16px;

      .item {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: v-bind(firstTitleColor);
        font-weight: 400;
        &::before {
          content: '';
          display: inline-block;
          width: 12px;
          height: 12px;
          box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15);
          border-radius: 2px;
          margin-right: 4px;
        }
        &.undo-box::before {
          border: 1px solid v-bind(optionBorderColor);
          background-image: linear-gradient(180deg, v-bind(resumeManageBg), v-bind(optionBg) 100%);
        }
        &.done-box::before {
          background: #5478ee;
          border: 1px solid rgba(84, 120, 238, 1);
          box-shadow: inset -2px -2px 2px 0px rgba(255, 255, 255, 0.15);
          box-shadow: inset 2px 2px 1px 0px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    position: relative;
    flex: 1;
    overflow: hidden;
    padding-left: 24px;
    padding-right: 22px;

    .right-main {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
    }

    .ant-spin {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .questionType {
      margin-right: 8px;
      height: 28px;
      font-size: 20px;
      font-weight: 700;
      text-align: left;
      color: v-bind(mainTextColor);
    }
    .header {
      display: flex;
      align-items: center;
      margin: 24px 0px 16px 0px;
    }
    .questionDetail {
      white-space: pre-wrap;
      .title-info {
        display: flex;
        margin-bottom: 16px;
        .question-index {
          color: v-bind(mainTextColor);
        }
      }

      .sub-item-options {
        .item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          color: v-bind(mainTextColor);
        }

        .radio-btn {
          margin-right: 16px;
        }

        .correct-answer-tag {
          display: inline-block;
          font-size: 12px;
          color: #2f8c00;
          background: #f3f7f1;
          border-radius: 4px;
          margin-left: 16px;
          padding: 0 2px;
        }

        .correct {
          background: #52c41a;
          border: 1px solid #52c41a;
          box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
            -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
          color: #fff;
        }

        .error {
          background: #de504e;
          border: 1px solid #de504e;
          box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
            -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
          color: #fff;
        }
      }

      :deep(.stem) {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: v-bind(mainTextColor);
        flex: 1;
        min-width: 0;
        overflow-x: auto;
        overflow-y: hidden;
        text-align: justify;
        p {
          word-wrap: break-word;
          font-size: 16px;
          img {
            max-width: 100%;
            height: auto;
            cursor: zoom-in;
          }
        }
      }

      .correct-answer {
        color: #de504e;
      }

      .accord {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 400;
        display: flex;

        .accordScore {
          display: inline-block;
          margin-bottom: 10px;
          color: #626262;
          flex-shrink: 0;
        }

        :deep(.accordText) {
          color: v-bind(mainTextColor);
          min-width: 0;
          overflow: auto;
          padding-bottom: 6px;
          margin-left: 40px;
          p {
            word-wrap: break-word;
            img {
              max-width: 100%;
              height: auto;
              cursor: zoom-in;
            }
          }
        }
      }

      .scorePoints {
        font-size: 14px;
        font-weight: 400;
        text-align: left;
        color: #626262;
        display: flex;
        align-items: baseline;
        width: 100%;
        .label {
          flex: none;
          width: 70px;
          height: 28px;
        }

        .wrapper {
          flex: auto;
        }

        .content {
          display: flex;
          .scorePointIndex {
            flex: none;
            width: 10px;
            height: 28px;
            font-size: 14px;
            color: #121633;
            line-height: 28px;
          }
          .scoPointDetail {
            margin-bottom: 8px;
            display: flex;
            flex-wrap: wrap;
            .scorePoint {
              margin-left: 8px;
              margin-bottom: 8px;
              padding: 1px 4px;
              border-radius: 4px;
              font-size: 14px;
              line-height: 24px;
            }
          }
        }
        .scorePoint {
          border: 1px solid v-bind(tagBorderColor);
          border-radius: 4px;
          background-color: v-bind(tagBg);
          color: v-bind(optionTextColor);
          margin-left: 8px;
          padding: 1px 4px;
          font-size: 12px;
        }

        .scorePoint.score {
          position: relative;
          background: v-bind(tagscoreBg);
          border: none;
          color: v-bind(optionTextColor);
          bottom: -1px;
        }
      }

      .com-qa {
        .confirm-btn {
          text-align: right;
        }

        .confirm {
          width: 92px;
          height: 32px;
          background: #5478ee;
          border-radius: 4px;
          color: #fff;
          border: 0px;
          margin-left: 16px;
          margin-bottom: 16px;
          cursor: pointer;
        }

        .sort-options {
          margin-bottom: 16px;

          .item {
            display: flex;
            width: 568px;
            min-height: 34px;
            padding-left: 8px;
            align-items: center;
            border: 1px solid v-bind(borderRightColor);
            color: v-bind(mainTextColor);
            border-radius: 2px;
            margin-bottom: 8px;
          }
        }

        .correct-answer {
          margin-bottom: 16px;
        }

        :deep(.ant-collapse-header) {
          background: v-bind(collapseHeaderBg);
          color: v-bind(mainTextColor);
          font-size: 16px;
          font-weight: 600;
        }

        :deep(.ant-collapse-content-box) {
          background: v-bind(collapseBodyBg);
        }

        :deep(.ant-collapse) {
          background: v-bind(collapseBodyBg);
          border: 1px solid v-bind(borderRightColor);
        }

        :deep(.ant-collapse-content) {
          border-top: 1px solid v-bind(borderRightColor);
        }

        :deep(.ant-collapse-item) {
          border-bottom: 1px solid v-bind(borderRightColor);
        }
      }
    }

    .keyToTest {
      &.subitem {
        margin-bottom: 16px;
      }
      margin-top: 24px;
      margin-bottom: auto;
      padding: 24px;
      position: relative;
      background: v-bind(answerBg);
      border-radius: 8px;
      transition: all 0.3s;
      opacity: 1;

      &.activeAnimate {
        transform: translateX(-100%);
        opacity: 0;
      }

      .answer-imgs {
        margin-top: 16px;
        display: flex;

        .item {
          position: relative;
          width: 100px;
          height: 100px;
          margin-right: 8px;

          :deep(.ant-image) {
            height: 100%;
            width: 100%;
          }

          :deep(.ant-image-img) {
            height: 100%;
            object-fit: cover;
          }
        }
      }

      p {
        font-size: 14px;
        font-weight: 600;
        padding-left: 20px;
        margin-bottom: 14px;
        color: v-bind(mainTextColor);
      }

      .prevArrow {
        position: absolute;
        left: 4px;
        top: 75px;
        width: 24px;
        height: 24px;
      }

      .content {
        padding-left: 20px;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        overflow: hidden;

        .text {
          font-family: inherit; // 继承父元素字体
          margin: 0;
          white-space: pre-wrap; // 保留换行和空格
          word-break: break-word;

          font-size: 14px;
          color: v-bind(mainTextColor);
        }

        :deep(.ant-input) {
          background: none;
          box-shadow: none;
          font-size: 12px;
          border: none;
          color: v-bind(mainTextColor);
        }
      }

      .out-answer {
        font-size: 12px;
        color: v-bind(optionTextColor);
        margin-top: 6px;
        margin-left: 18px;
        display: flex;
        justify-content: space-between;
      }

      .nextArrow {
        position: absolute;
        right: 4px;
        top: 75px;
        width: 24px;
        height: 24px;
      }

      footer {
        margin: 16px 0 0 20px;
        display: flex;
        justify-content: space-between;

        .systemScore {
          text-align: left;
          color: #868686;
        }

        .manualScore {
          color: v-bind(mainTextColor);
          padding-right: 20px;
          :deep(.ant-input-number) {
            width: 44px;
            border: none;
            box-shadow: none;
            margin: 0 6px;
            background-color: transparent;
          }

          :deep(.ant-input-number-input) {
            height: 24px;
            border: 1px solid v-bind(optionBorderColor);
            background: v-bind(inputBg);
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
            color: v-bind(mainTextColor);
          }

          .confirm {
            width: 46px;
            height: 24px;
            background: #5478ee;
            border-radius: 4px;
            color: #fff;
            border: 0px;
            margin-left: 16px;
            cursor: pointer;

            &.normal {
              background-color: #fff;
              color: #000;
              margin-left: 8px;
              border: 1px solid rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      .ai-text-panel {
        margin: 16px 20px 0 20px;
        padding: 16px;
        background-color: #fff;
        border-radius: 8px;

        > div {
          display: flex;
          width: 100%;
        }

        .label {
          color: #626262;
          font-size: 14px;
          width: 60px;
          margin-right: 24px;
        }
        .value {
          color: rgba(0,0,0,0.85);
          font-size: 14px;
          flex: 1;
          min-width: 0;
        }
      }
    }

    .sub-item-title {
      font-size: 16px;
      font-weight: bold;
      padding: 9px 0 25px;
      color: v-bind(mainTextColor);
    }

    .bottom {
      padding: 24px 24px 24px 0;

      .ant-divider {
        border-top: 1px solid v-bind(tagBg);
      }

      .nextQ {
        width: 74px;
        height: 32px;
        background: #5478ee;
        border-radius: 8px;
        color: #fff;
        border: 0;
        cursor: pointer;
      }

      .close {
        padding: 0 16px;
        height: 32px;
        background-color: v-bind(resumeManageBg);
        border: 1px solid v-bind(optionBorderColor);
        border-radius: 8px;
        color: v-bind(mainTextColor);
        cursor: pointer;
      }
    }
  }

  .finish {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: v-bind(firstTitleColor);
  }
}

.correct-text {
  color: #52c41a;
}

.error-text {
  color: #de504e;
}

.score {
  display: inline-block;
  flex-shrink: 0;
  margin-right: 8px;
  text-align: center;
  width: 36px;
  height: 20px;
  line-height: 20px;
  background: v-bind(tagBg);
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  color: v-bind(scoreColor);
}
.btn-list {
  display: flex;
}
.finished-box {
  height: 182px;
  background-color: #F5F5F5;
  border: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
}
</style>
<style lang="less">
.confirm-skip-modal {
  .ant-modal-header {
    border: none;
  }

  .ant-modal-body {
    padding: 10px 0 10px 55px;
  }

  .ant-modal-footer {
    border: none;
    padding-bottom: 22px;
    padding-top: 22px;
    padding-left: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-btn {
      border-radius: 8px;
    }
  }
}

.student-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>