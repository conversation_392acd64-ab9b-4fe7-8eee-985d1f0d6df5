<script setup lang="ts">
import { useRouter } from 'vue-router'
import ResumeDetail from './components/ResumeDetail/index.vue'

const router = useRouter()
const resumeId = router.currentRoute.value.query.id as string
</script>

<template>
  <div class="h-screen overflow-auto">
    <div class="w-full h-full flex flex-col justify-start items-start">
      <h3 class="flex-shrink-0 px-5 h-[48px] leading-[48px] text-[14px] font-semibold">
        <span class="opacity-45">
          <span class="cursor-pointer" @click="router.back()">人才库 / </span>
        </span>
        <span>查看简历</span>
      </h3>
      <div class="paper-manage flex-1 px-5 pb-5 overflow-auto">
        <ResumeDetail :key="resumeId" :props-current-resume-id="resumeId" />
      </div>
    </div>
  </div>
</template>
