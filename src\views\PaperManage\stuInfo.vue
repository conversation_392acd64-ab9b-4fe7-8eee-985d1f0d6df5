<template>
  <div class="stu-monitor-container">
    <h3 class="stu-monitor-title">
      <span style="color: rgba(0, 0, 0, 0.45)">
        <span @click="backtoExam">试卷管理 /</span>
        <span @click="backtoStu">监考</span>
      </span>
      <span> / 考生信息《{{ route.query.name }}》</span>
    </h3>
    <div class="stu-monitor">
      <SingleStudentMonitor v-bind="singleStudentMonitorParams"></SingleStudentMonitor>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import SingleStudentMonitor from '@/pages/monitorManage/SingleStudentMonitor.vue'

const route = useRoute() as any

const singleStudentMonitorParams = {
  stupid: route.query.stu,
  paperId: route.query.paper,
  paperName: route.query.name,
  computerMonitor: route.query.computermonitor === 'yes',
  phoneMonitor: route.query.phonemonitor === 'yes',
  status: route.query.status
}

const router = useRouter()
const backtoStu = () => router.back()
const backtoExam = () => {
  router.push({ name: 'paperManage' })
}

</script>

<style lang="less" scoped>
.stu-monitor-container {
  height: 100%;
  padding: 0 20px 20px 20px;
}

.stu-monitor-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}

.stu-monitor {
  overflow: scroll;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  padding: 24px;
}
</style>
