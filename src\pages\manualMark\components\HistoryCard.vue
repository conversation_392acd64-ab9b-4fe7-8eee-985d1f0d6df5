<template>
  <div class="history-card">
    <div class="title-wrap" :style="{'border-bottom': expanded ? '1px solid #d9d9d9' : '0'}" @click="expanded = !expanded">
      <DownOutlined color="#575D6C" size="16" :class="{'icon': true, 'up': expanded}" />
      <div class="title">历史记录</div>
    </div>
    <div class="history-list" v-show="expanded">
      <div class="keyToTest" v-for="(item, index) in historyEditList" :key="item.stup">
        <p>{{ item.stuname ?? '考生答案' }}</p>
        <div class="content">
          <div class="question-info-list" v-if="questionDetailInfo.type == QuestionEnum['填空题']">

            <div class="question-info-list-item answer" :class="{active: item.fillblankHoverIndex === i}" v-for="(it,i) in item.ext" @mouseenter="handleFillblankMouseOver(index, i)" @mouseleave="handleFillblankMouseLeave(index)">
              <div class="switch-container">
                <JSwitchPlus v-model:checked="it.is_true" :disabled="!item.isEditing" :expanded="item.fillblankHoverIndex === i" @change="reCalcScore(index)"></JSwitchPlus>
              </div>
              <div class="index" v-if="questionDetailInfo.ordered">{{ i+1 }}.</div>
              <div class="index" v-else style="font-weight: bold;">·</div>
              <span>{{ it['text'+i] }}</span>
            </div>
          </div>

          <template v-if="questionDetailInfo.type == QuestionEnum['问答题']">
            <div class="text" v-html="highLightKeywords(item.stuanswer || '', getItemPoints(questionDetailInfo.answer))" />
            <div class="answer-imgs">
              <template v-for="img in getAnswerImgs(item.stuanswer)" :key="img">
                <div class="item">
                  <a-image :src="getImgUrl(img)" />
                </div>
              </template>
            </div>
          </template>
          <CodeBox v-if="questionDetailInfo.type == QuestionEnum['算法题']" autodetect :code="item.stuanswer" />
        </div>
        <div v-if="questionDetailInfo.type == QuestionEnum['算法题']" class="out-answer">
          通过测试用例数
          <span :class="getPasscaseClass(item.passcase)">{{ item.passcase }}</span>
        </div>

        <footer>
          <div>
            <span class="systemScore">{{item.correctiontime}}由{{ item.corrector }}确认评分为 </span
            ><span style="color: red; font-size: 12px">{{ item.score || 0 }}分</span>
          </div>
          <template v-if="!props.published">
            <div v-if="!item.isEditing" style="display: flex;align-items: center;">
              修改评分为<span style="color: red; font-size: 12px">{{ item.score || 0 }}分</span>
              <svg-icon v-if="!item.isEditing" class="score-edit-btn" name="edit" @click="item.isEditing = true"></svg-icon>
            </div>
            <div class="manualScore" v-else>
              <span>
                <template v-if="!props.published">修改</template>评分为
              </span>
              <a-input-number
                :controls="false"
                :formatter="(value:number) => Math.floor(value)"
                v-model:value="item.newscore"
                :min="0"
                :max="questionDetailInfo.quessocre"
              />分
              <a-button
                size="small"
                style="border-radius: 4px;margin-left: 16px;"
                type="primary"
                :loading="item.loading"
                @click="confirm(item)"
              >
                确认
              </a-button>
              <a-button
                size="small"
                style="border-radius: 4px;margin-left: 8px;"
                @click="cancel(item)"
              >
                取消
              </a-button>
            </div>
          </template>
          <div class="manualScore" v-else>
              <span>
                修改评分为{{ item.score || 0 }}分
              </span>
          </div>
        </footer>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useStore } from 'vuex'
import { DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { QuestionEnum } from '@/models/questionModel'
import { markinghistory , markinghistoryUpdateScore } from '@/api/admin/paperManage'
import JSwitchPlus from '@/components/JSwitchPlus.vue'
import CodeBox from '@/components/CodeBox.vue'
import moment from 'moment'
import _ from 'lodash'
import { useRoute } from 'vue-router'

const props = withDefaults(defineProps<{
  defaultExpanded?: boolean // 是否默认展开
  historyList: any[] // 阅卷历史
  questionDetailInfo: any // 题目详情
  published: boolean // 是否已发布
}>(), {
  defaultExpanded: false,
  published: false,
})

const store = useStore()

const expanded = ref<boolean>(false)
watch(() => props.defaultExpanded, (val) => expanded.value = val, {
  immediate: true,
})

const historyEditList = ref<any[]>([])
const lastConfirmHistoryList = ref<any[]>([])
watch(() => props.historyList, (val) => {
  historyEditList.value = val.map((item) => {
    return {
      ...item,
      newscore: item.score,
      loading: false,
      editing: false,
      fillblankHoverIndex: -1
    }
  })
  lastConfirmHistoryList.value = _.cloneDeep(historyEditList.value)
}, {
  immediate: true,
})

// 填空题answer解析
function getItemKeyWords(answer: string) {
  return JSON.parse(answer || '[]').map((item: any) => ({ keyword: item.keyword, score: item.score }))
}

// 填空题开关切换事件，重新计算人工评分
function reCalcScore(index: number) {
  let newScore = 0
  let answer = historyEditList.value[index].answer
  historyEditList.value[index].ext?.forEach((item: any, i: number) => {
    if (item.is_true) {
      newScore += getItemKeyWords(answer)[i].score
    }
  })
  historyEditList.value[index].newscore = newScore
}

// 填空题考生答案鼠标悬浮事件
const fillblankHoverIndex = ref(-1)
function handleFillblankMouseOver(index: number, i: number) {
  historyEditList.value[index].fillblankHoverIndex = i
  fillblankHoverIndex.value = i
}

// 填空题考生答案鼠标离开事件
function handleFillblankMouseLeave(index: number) {
  historyEditList.value[index].fillblankHoverIndex = -1
  fillblankHoverIndex.value = -1
}

// 问答题answer解析
const getItemPoints = (answer: string) => {
  return answer.map((item: any) => {
    return item.keyword
  })
}

// 获取问答题中的图片
const getAnswerImgs = (sAnswer: string) => {
  const index = sAnswer.indexOf('#@$')
  if (index === -1) {
    return []
  } else {
    const answerImgsString = sAnswer.split('#@$')[1]
    return answerImgsString.split(',')
  }
}

// 获取问答题中的图片url
const getImgUrl = (item: string) => {
  return 'https://zhanglibo-exam.obs.cn-north-4.myhuaweicloud.com/' + item
}

// 高亮关键词
const highLightKeywords = (textContent: string, keywordstring: any) => {
  // 转译特殊字符 <,>,&
  textContent = textContent.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')
  const index = textContent.indexOf('#@$')
  if (index !== -1) {
    textContent = textContent.substring(0, index)
  }

  const keywordsArray: string[] = []
  keywordstring.forEach((item: any) => {
    item.forEach((keyword: string) => {
      keywordsArray.push(keyword)
    })
  })

  const escapedKeywords = keywordsArray.map((keyword) =>
    keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  )
  const pattern = new RegExp(`(${escapedKeywords.join('|')})`, 'gi')
  return textContent.replace(pattern, '<span class="highlight">$1</span>')
}

// 是否通过的测试用例
const getPasscaseClass = (passcase: string) => {
  let arr = passcase.split('/')
  return arr[0] === arr[1] ? 'correct-text' : 'error-text'
}
const route = useRoute()
// 修改分值
const confirm = async (item: any) => {
  try {
    if (item.newscore > props.questionDetailInfo.paper_ques_score) {
      message.error('当前题目所得分值不能超过总分值')
      return
    }
    item.loading = true
    await markinghistoryUpdateScore({
      action: 'add',
      // stup: item.stup,
      examId:  item.examId,
      quesScore: [{
        quesId: props.questionDetailInfo.id, 
        score: Number(item.newscore), ext: item.ext }],
        teacher: store.getters.userid
    })
    item.score = item.newscore
    item.correctiontime = moment().format('YYYY-MM-DD HH:mm:ss')
    message.success('评分修改成功！')
    item.loading = false
    item.isEditing = false
    lastConfirmHistoryList.value = _.cloneDeep(historyEditList.value)
  } catch (error) {
    console.log(error)
  } finally {
    item.loading = false
  }
}

function cancel(item: any) {
  historyEditList.value = _.cloneDeep(lastConfirmHistoryList.value)
  item.isEditing = false
}
</script>

<style lang="less" scoped>
body.moon {
  .history-card {
    background-color: transparent;
    border: 1px solid #24262E;
    .title-wrap {
      background: #24262E;
      border-bottom: 1px solid #24262E;
      .icon,.title {
        color: rgba(255, 255, 255, .85);
      }
    }

    .history-list {
      background-color: #181A20;
      .keyToTest {
        background-color: #24262E;
        color: rgba(255, 255, 255, .85);

        footer {
          .systemScore {
            color: rgba(255, 255, 255, .85);
          }
          .score-edit-btn {
            cursor: pointer;
            margin-left: 10px;
            color: rgba(255, 255, 255, .85);
            &:hover {
              background: #181A20;
            }
          }
        }
      }
    }
  }
}
.history-card {
  margin-top: 24px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  .title-wrap {
    height: 42px;
    background: #f2f5fc;
    padding: 0 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #d9d9d9;
    .icon {
      margin-right: 18px;
      transition: transform 0.3s ease-in-out;
      &.up {
        transform: rotate(180deg);
      }
    }
    .title {
      height: 42px;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      color: #252b3a;
      line-height: 42px;
    }
  }
  .history-list {
    padding: 16px 16px 0 16px;
    overflow: hidden;
    .keyToTest {
      margin-bottom: 16px;
      padding: 24px 24px 24px 28px;
      position: relative;
      background: #f5f5f5;
      border-radius: 8px;
      // margin-left: 24px;
      p {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 14px;
      }
      .prevArrow {
        position: absolute;
        left: 4px;
        top: 75px;
        width: 24px;
        height: 24px;
      }
      .content {
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        :deep(.ant-input) {
          background: none;
          box-shadow: none;
          font-size: 12px;
          border: none;
        }
      }
      .answer-imgs {
        margin-top: 16px;
        display: flex;
        .item {
          position: relative;
          width: 100px;
          height: 100px;
          margin-right: 8px;
          :deep(.ant-image) {
            height: 100%;
            width: 100%;
          }
          :deep(.ant-image-img) {
            height: 100%;
            object-fit: cover;
          }
        }
      }
      .out-answer {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        margin-top: 6px;
      }
      :deep(.hljs) {
        border-radius: 3px;
        padding: 16px;
      }
      .nextArrow {
        position: absolute;
        right: 4px;
        top: 75px;
        width: 24px;
        height: 24px;
      }
      footer {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        height: 24px;
        display: flex;
        align-items: center;
        
        .systemScore {
          font-size: 12px;
          text-align: left;
          color: #868686;
        }

        .btn-wrap {
          cursor: pointer;
          margin-left: 10px;
          &:hover {
            background: #f5f5f5;
          }
        }
        .score-edit-btn {
          cursor: pointer;
          margin-left: 10px;
          &:hover {
            background: #f5f5f5;
          }
        }
        .manualScore {
          :deep(.ant-input-number) {
            width: 44px;
            border: none;
            box-shadow: none;
            margin: 0 3px;
          }
          :deep(.ant-input-number-input) {
            height: 24px;
            background: #ffffff;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
          }
          .confirm {
            width: 46px;
            height: 24px;
            background: #5478ee;
            border-radius: 4px;
            color: #fff;
            border: 0px;
            margin-left: 16px;
            cursor: pointer;
          }
        }
      }
    }
  }
  .question-info-list-item {
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    margin-top: 2px;
    cursor: pointer;

    &.answer {
      margin-left: -20px;
    }
    &.active {
      // background-color: #DAF1FF;
    }

    .index {
      margin-right: 4px;
    }
    .word {
      margin-right: 4px;
    }
  }
  .switch-container {
    width: 40px;
    margin-right: 4px;
    display: flex;
    flex-direction: row-reverse;
    flex-shrink: 0;
  }
}
</style>
