<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

// 表单数据
const formData = ref({
  positionName: '',
  department: '',
  manager: '',
  workLocation: '',
  workExperience: '',
  educationRequirement: '',
  positionDescription: '',
  requiredSkills: [] as string[],
  preferredSkills: [] as string[],
})

// 下拉选项数据
const departmentOptions = [
  { label: '技术部', value: 'tech' },
  { label: '产品部', value: 'product' },
  { label: '运营部', value: 'operation' },
  { label: '市场部', value: 'marketing' },
]

const workLocationOptions = [
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '深圳', value: 'shenzhen' },
  { label: '杭州', value: 'hangzhou' },
]

const workExperienceOptions = [
  { label: '不限', value: 'unlimited' },
  { label: '1年以下', value: '0-1' },
  { label: '1-3年', value: '1-3' },
  { label: '3-5年', value: '3-5' },
  { label: '5年以上', value: '5+' },
]

const educationOptions = [
  { label: '不限', value: 'unlimited' },
  { label: '大专', value: 'college' },
  { label: '本科', value: 'bachelor' },
  { label: '硕士', value: 'master' },
  { label: '博士', value: 'doctor' },
]

// 技能标签
const skillTags = [
  '计算机', 
  '2025新增', 
  '高效算法', 
  '营销', 
  '计算机',
  '2025新增', 
  '高效算法', 
  '营销', 
  '计算机', 
  '2025新增',
]

// 输入的技能
const requiredSkillInput = ref('')
const preferredSkillInput = ref('')

// 字符计数
const descriptionCount = ref(0)
const maxDescriptionLength = 5000

// 添加必备技能
function addRequiredSkill() {
  if (requiredSkillInput.value.trim()) {
    formData.value.requiredSkills.push(requiredSkillInput.value.trim())
    requiredSkillInput.value = ''
  }
}

// 添加加分技能
function addPreferredSkill() {
  if (preferredSkillInput.value.trim()) {
    formData.value.preferredSkills.push(preferredSkillInput.value.trim())
    preferredSkillInput.value = ''
  }
}

// 删除技能
function removeRequiredSkill(index: number) {
  formData.value.requiredSkills.splice(index, 1)
}

function removePreferredSkill(index: number) {
  formData.value.preferredSkills.splice(index, 1)
}

// 添加预设技能标签
function addSkillTag(skill: string, type: 'required' | 'preferred') {
  if (type === 'required' && !formData.value.requiredSkills.includes(skill)) {
    formData.value.requiredSkills.push(skill)
  }
  else if (type === 'preferred' && !formData.value.preferredSkills.includes(skill)) {
    formData.value.preferredSkills.push(skill)
  }
}

// 更新描述字符计数
function updateDescriptionCount() {
  descriptionCount.value = formData.value.positionDescription.length
}

// AI 技能解析
function aiAnalyzeSkills() {
  ElMessage.info('AI 技能解析功能开发中...')
}

// 表单提交
function submitForm() {
  // 表单验证
  if (!formData.value.positionName) {
    ElMessage.error('请填写岗位名称')
    return
  }
  if (!formData.value.department) {
    ElMessage.error('请选择招聘部门')
    return
  }

  ElMessage.success('岗位创建成功！')
  console.log('表单数据:', formData.value)
}
</script>

<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-sm">
      <!-- 岗位信息部分 -->
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900 mb-6">
          岗位信息
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 岗位名称 -->
          <div class="md:col-span-2">
            <div class="flex items-center mb-2">
              <span class="text-red-500 mr-1">*</span>
              <label class="text-sm font-medium text-gray-700">岗位名称</label>
            </div>
            <el-input
              v-model="formData.positionName"
              placeholder="请填写岗位名称，最多50字"
              maxlength="50"
              show-word-limit
              class="w-full"
            />
          </div>

          <!-- 招聘部门 -->
          <div>
            <div class="flex items-center mb-2">
              <span class="text-red-500 mr-1">*</span>
              <label class="text-sm font-medium text-gray-700">招聘部门</label>
            </div>
            <el-select
              v-model="formData.department"
              placeholder="请选择"
              class="w-full"
            >
              <el-option
                v-for="item in departmentOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 负责人 -->
          <div>
            <div class="flex items-center mb-2">
              <span class="text-red-500 mr-1">*</span>
              <label class="text-sm font-medium text-gray-700">负责人</label>
            </div>
            <el-select
              v-model="formData.manager"
              placeholder="请选择"
              class="w-full"
            >
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
            </el-select>
          </div>

          <!-- 工作地点 -->
          <div>
            <label class="text-sm font-medium text-gray-700 block mb-2">工作地点</label>
            <el-select
              v-model="formData.workLocation"
              placeholder="请选择"
              class="w-full"
            >
              <el-option
                v-for="item in workLocationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 工作经验 -->
          <div>
            <label class="text-sm font-medium text-gray-700 block mb-2">工作经验</label>
            <el-select
              v-model="formData.workExperience"
              placeholder="请选择"
              class="w-full"
            >
              <el-option
                v-for="item in workExperienceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 学历要求 -->
          <div>
            <label class="text-sm font-medium text-gray-700 block mb-2">学历要求</label>
            <el-select
              v-model="formData.educationRequirement"
              placeholder="请选择"
              class="w-full"
            >
              <el-option
                v-for="item in educationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 岗位描述 -->
          <div class="md:col-span-2">
            <div class="flex items-center mb-2">
              <span class="text-red-500 mr-1">*</span>
              <label class="text-sm font-medium text-gray-700">岗位描述</label>
            </div>
            <el-input
              v-model="formData.positionDescription"
              type="textarea"
              :rows="6"
              placeholder="请简单描述一下岗位描述"
              :maxlength="maxDescriptionLength"
              class="w-full"
              @input="updateDescriptionCount"
            />
            <div class="flex justify-between items-center mt-2">
              <el-button
                type="primary"
                size="small"
                class="flex items-center"
                @click="aiAnalyzeSkills"
              >
                <span class="mr-1">🤖</span>
                AI 技能解析
              </el-button>
              <span class="text-sm text-gray-500">{{ descriptionCount }}/{{ maxDescriptionLength }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 技能要求部分 -->
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">
          技能要求
        </h2>

        <!-- 必备技能 -->
        <div class="mb-8">
          <div class="flex items-center mb-3">
            <span class="text-red-500 mr-1">*</span>
            <label class="text-sm font-medium text-gray-700">必备技能</label>
          </div>

          <!-- 技能输入框 -->
          <div class="flex gap-2 mb-4">
            <el-input
              v-model="requiredSkillInput"
              placeholder="输入技能后回车"
              class="flex-1"
              @keyup.enter="addRequiredSkill"
            />
            <el-button type="primary" @click="addRequiredSkill">
              添加
            </el-button>
          </div>

          <!-- 已选择的必备技能 -->
          <div v-if="formData.requiredSkills.length > 0" class="mb-4">
            <el-tag
              v-for="(skill, index) in formData.requiredSkills"
              :key="index"
              closable
              type="primary"
              class="mr-2 mb-2"
              @close="removeRequiredSkill(index)"
            >
              {{ skill }}
            </el-tag>
          </div>

          <!-- 预设技能标签 -->
          <div class="space-y-2">
            <div class="flex flex-wrap gap-2">
              <el-button
                v-for="(tag, index) in skillTags"
                :key="index"
                size="small"
                plain
                type="info"
                :disabled="formData.requiredSkills.includes(tag)"
                @click="addSkillTag(tag, 'required')"
              >
                {{ tag }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 加分技能 -->
        <div class="mb-8">
          <label class="text-sm font-medium text-gray-700 block mb-3">加分技能</label>

          <!-- 技能输入框 -->
          <div class="flex gap-2 mb-4">
            <el-input
              v-model="preferredSkillInput"
              placeholder="输入技能后回车"
              class="flex-1"
              @keyup.enter="addPreferredSkill"
            />
            <el-button type="primary" @click="addPreferredSkill">
              添加
            </el-button>
          </div>

          <!-- 已选择的加分技能 -->
          <div v-if="formData.preferredSkills.length > 0" class="mb-4">
            <el-tag
              v-for="(skill, index) in formData.preferredSkills"
              :key="index"
              closable
              type="success"
              class="mr-2 mb-2"
              @close="removePreferredSkill(index)"
            >
              {{ skill }}
            </el-tag>
          </div>

          <!-- 预设技能标签 -->
          <div class="space-y-2">
            <div class="flex flex-wrap gap-2">
              <el-button
                v-for="(tag, index) in skillTags"
                :key="index"
                size="small"
                plain
                type="info"
                :disabled="formData.preferredSkills.includes(tag)"
                @click="addSkillTag(tag, 'preferred')"
              >
                {{ tag }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="flex justify-end gap-4 pt-6 border-t border-gray-200">
          <el-button size="large">
            取消
          </el-button>
          <el-button type="primary" size="large" @click="submitForm">
            创建岗位
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.el-select {
  width: 100%;
}

.el-input__wrapper {
  border-radius: 6px;
}

.el-select .el-input__wrapper {
  border-radius: 6px;
}

.el-button {
  border-radius: 6px;
}

.el-tag {
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .md\:col-span-2 {
    grid-column: span 1;
  }
}
</style>