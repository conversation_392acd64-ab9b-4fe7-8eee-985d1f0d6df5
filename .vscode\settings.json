{
  "cSpell.words": [
    "antfu",
    "demi",
    "iconify",
    "intlify",
    "Katex",
    "pinia",
    "pnpm",
    "unocss",
    "unplugin",
    "Vite",
    "vitejs",
    "Vitesse",
    "vitest",
    "vueuse"
  ],
  "i18n-ally.sourceLanguage": "en",
  "i18n-ally.keystyle": "nested",
  "i18n-ally.localesPaths": "locales",
  "i18n-ally.sortKeys": true,
  // Disable the default formatter
  "prettier.enable": false,
  "editor.formatOnSave": false,
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    {
      "rule": "style/*",
      "severity": "off"
    },
    {
      "rule": "format/*",
      "severity": "off"
    },
    {
      "rule": "*-indent",
      "severity": "off"
    },
    {
      "rule": "*-spacing",
      "severity": "off"
    },
    {
      "rule": "*-spaces",
      "severity": "off"
    },
    {
      "rule": "*-order",
      "severity": "off"
    },
    {
      "rule": "*-dangle",
      "severity": "off"
    },
    {
      "rule": "*-newline",
      "severity": "off"
    },
    {
      "rule": "*quotes",
      "severity": "off"
    },
    {
      "rule": "*semi",
      "severity": "off"
    }
  ],
  // The following is optional.
  // It's better to put under project setting `.vscode/settings.json`
  // to avoid conflicts with working with different eslint configs
  // that does not support all formats.
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ]
}