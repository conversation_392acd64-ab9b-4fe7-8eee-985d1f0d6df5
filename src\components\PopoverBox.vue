<template>
  <a-popover trigger="click" placement="bottom">
    <template #content>
      <div>
        <div style="line-height: 30px">
          <a-checkbox
            class="checkbox-content"
            v-model:checked="checkAll"
            :indeterminate="indeterminate"
            @change="onCheckAllChange"
            >全部显示</a-checkbox
          >
        </div>
        <a-checkbox-group v-model:value="selectedBox">
          <div style="line-height: 30px" v-for="item of props.columns" :key="item.value">
            <a-checkbox :value="item.value" class="checkbox-content">{{ item.label }}</a-checkbox>
          </div>
        </a-checkbox-group>
      </div>
    </template>
    <a-button class="pop-btn"
      ><span>{{ getInfo }}</span> <Icon icon="DownOutlined"
    /></a-button>
  </a-popover>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  selectedColumns: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['getDynamicColumns'])

const selectedBox: any = ref([])
const checkAll = ref(false)
const indeterminate = ref(true)
const onCheckAllChange = (e: any) => {
  if (e.target.checked) {
    const checkedList = props.columns.map((item: any) => item.value)
    selectedBox.value = checkedList
  } else {
    selectedBox.value = []
  }
  indeterminate.value = false
}

const getInfo = computed(() => {
  const count = selectedBox.value.length
  return count === props.columns.length ? '已选择全部' : `已选择${count}项`
})

watch(
  () => selectedBox.value,
  (val) => {
    indeterminate.value = !!val.length && val.length < props.columns.length
    checkAll.value = val.length === props.columns.length
    emits('getDynamicColumns', val)
  }
)

onMounted(() => {
  selectedBox.value.push(...props.selectedColumns)
})
</script>

<style lang="less" scoped>
.checkbox-content,
.pop-btn {
  font-size: 15px;
}
</style>
