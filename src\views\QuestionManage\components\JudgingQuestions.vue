<template>
  <a-form
    class="form"
    ref="createQuestionFormRef"
    :model="formState"
    hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
  >
    <a-form-item label="分值" name="score">
      <score-input
        class="scoreInp"
        ref="scoreInputTotalRef"
        v-model="formState.score"
        @getScore="getScore"
      />
    </a-form-item>
    <a-form-item label="题干内容" v-bind="validateInfos.body">
      <a-textarea
        v-if="!formState.complicatedediting"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      />
      <VueQuillEditor
        v-else
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      ></VueQuillEditor>
    </a-form-item>
    <a-form-item class="question-options">
      <a-radio-group v-model:value="formState.answer">
        <template v-for="(item, index) in judge" :key="index">
          <div class="item-option">
            <a-radio :value="item.value">
              <span class="option-radio">{{ item.value }}</span>
            </a-radio>
            <a-input class="option-content" v-model:value="item.content" />
          </div>
        </template>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import ScoreInput from './ScoreInput.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import { ref, watch } from 'vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { Form } from 'ant-design-vue'

const useForm = Form.useForm

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['update:modelValue'])

const judge = [
  {
    content: '对',
    value: 'A'
  },
  {
    content: '错',
    value: 'B'
  }
]
const formState = ref({
  complicatedediting: false,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  sepscore: true,
  options: [
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    }
  ],
  tags: []
})

// 校验分值
const checkScore = async (rule: RuleObject, value: string) => {
  if (value == '0') {
    return Promise.reject('请给该题目匹配合适的分值')
  } else {
    return Promise.resolve()
  }
}

// 校验题干
const checkBody = async (rule: RuleObject, value: '') => {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  } else {
    return Promise.resolve()
  }
}

// 定义规则
const rules = {
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }]
}

const { validate, validateInfos } = useForm(formState.value, rules)

// 获取分数
const getScore = (score: any) => {
  formState.value.score = score
}

watch(
  formState,
  (val) => {
    emits('update:modelValue', val)
  },
  {
    deep: true
  }
)

watch(
  () => props.modelValue,
  (val: any) => {
    formState.value = Object.assign(formState.value, val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.form {
  margin-bottom: auto;
  .question-options {
    .item-option {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 12px;
      position: relative;
      .option-radio {
        font-size: 12px;
        display: inline-block;
        width: 20px;
      }
      .option-content {
        margin-left: 4px;
        font-size: 12px;
        border: none;
        box-shadow: none;
        width: 220px;
      }
    }
  }
}
</style>
