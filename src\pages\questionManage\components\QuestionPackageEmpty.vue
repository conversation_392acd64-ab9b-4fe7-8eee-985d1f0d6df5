<script setup lang="ts">
const pkg = inject('pkg') as any
</script>

<template>
  <div class="flex flex-wrap empty-box w-[327px] justify-center ">
    <img style="width: 200px" src="@/assets/images/nodata.png">
    <div class="w-full text-center">
      您还没有创建任何题库
    </div>
    <div class="w-full text-center">
      请点击<span class="text-[#5478ee]">【创建题库】</span>为面试添加题目范围
    </div>
    <a-button type="primary" class="mt-[16px]" @click="pkg.openCreateDialog">
      创建题库
    </a-button>
  </div>
</template>

<style lang="scss" scoped>

</style>