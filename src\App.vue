<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { AnimatePresence } from 'motion-v'
// import { Vue3Lottie } from 'vue3-lottie'
// import 'vue3-lottie/dist/style.css'
// import AstronautJSON from '@/assets/loading.json'

dayjs.locale(zhCN.locale)
</script>

<template>
  <AnimatePresence>
    <router-view v-slot="{ Component }">
      <a-config-provider :locale="zhCN">
        <template #renderEmpty>
          <div class="empty-table" style="text-align: center;">
            <img style="width: 200px" src="@/assets/images/nodata.png" />
            <p style="color: #dcdde1">暂无数据</p>
          </div>
        </template>
        <component :is="Component" />
      </a-config-provider>
    </router-view>
  </AnimatePresence>
</template>

<style lang="less">
.ant-table-empty {
  height: 100%;

  .ant-table-body {
    display: flex;
    height: 90%;
    align-items: center;
  }
}
</style>
