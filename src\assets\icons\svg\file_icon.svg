<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/毛玻璃文件</title>
    <defs>
        <linearGradient x1="4.88101465%" y1="35.1414275%" x2="58.6913615%" y2="53.2945041%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="74.8198153%" y1="23.7781625%" x2="22.4374449%" y2="75.8524761%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="3.92092722%" y1="32.6821743%" x2="91.2911892%" y2="70.8419554%" id="linearGradient-3">
            <stop stop-color="#E6EBFC" offset="0%"></stop>
            <stop stop-color="#7BA7F5" offset="42.841508%"></stop>
            <stop stop-color="#77A5F4" offset="100%"></stop>
        </linearGradient>
        <path d="M6,4.8 L18,4.8 C19.6568542,4.8 21,6.14314575 21,7.8 L21,15.3 C21,16.9568542 19.6568542,18.3 18,18.3 L6,18.3 C4.34314575,18.3 3,16.9568542 3,15.3 L3,7.8 C3,6.14314575 4.34314575,4.8 6,4.8 Z" id="path-4"></path>
        <linearGradient x1="8.81880296%" y1="22.7583574%" x2="93.0787331%" y2="78.8625867%" id="linearGradient-5">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="83.4710744%" x2="-3.80099249%" y2="16.5289256%" id="linearGradient-6">
            <stop stop-color="#C0CEF9" offset="0%"></stop>
            <stop stop-color="#B5C4F4" stop-opacity="0" offset="39.2186756%"></stop>
            <stop stop-color="#B5C4F4" stop-opacity="0" offset="66.3161062%"></stop>
            <stop stop-color="#C0CEF9" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-7" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-8"></use>
        </pattern>
        <image id="image-8" width="22" height="18" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAASCAYAAABfJS4tAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAEgAAAADpoZWaAAACBElEQVQ4EbWUQWocMRBFK5aYxg02BHvlnQ/kK+UkOVAu4F1WXgQMAzEzICOhAr9f6h7aOBhCJhqqq7skvV8qSfPl2/fDwy6ZTVjKeD7mSd7CX9Ixz2bX2LzLdiUvY0ztWDN7rcPr21uPeHaCDagJivPkVmuyRKw73/yQxIawXtWn5kAb8zt+C3Wm5OqdsVA1MJ48JreMWgYwKQusY2piamVVWRKrTCr4RqYa0xCRzxqQUzfPWbxNczJLxupj2WuHoEqjCAikaMVavoSIKYnIWB0wbUfESacv2QuUKMsEvM4qyCiPai+I6hpQ0pWIYgKu8KzUFehAJdAl4DmUA05sXiYp04nHvpgdsCIo81XnFejAtDdZqlqeaqeasm/WgI8MhHJOQ7IbBieyLQB/H82Ox25H3jWuLbC1DFRjrDsCfFCqaMpCx6+wOxJWu/1qHLVsT89mz/sF2jzmxOlBWE2rV1NKH5qEtNOCvpDd/mUo3t+ZPf6skWmUj3BweCy8E+uP4FPv8iKBH48dO9jdbY4Lsl4olTDqiNvCtu8L5nP3izIkdvnqchxD7YuOpODhQum9yOfETa9zA1Si8srZZ0Mnrqz+FiQSXBT+OuMNn43rWI6jptKcykPB/wk8RICzB7q5umDcqfgvOQN44FWeQvbxD4nA2cBr9uOoZrvY1ux87/1/gc3eALoqG+EHzOpfAAAAAElFTkSuQmCC"></image>
        <path d="M4,3 L6.75681409,3 C7.17727366,3 7.57843041,3.17646954 7.8625453,3.48641306 L11.095788,7.01358694 C11.3799029,7.32353046 11.7810597,7.5 12.2015192,7.5 L20,7.5 C21.6568542,7.5 23,8.84314575 23,10.5 L23,18 C23,19.6568542 21.6568542,21 20,21 L4,21 C2.34314575,21 1,19.6568542 1,18 L1,6 C1,4.34314575 2.34314575,3 4,3 Z" id="path-9"></path>
        <linearGradient x1="34.3857943%" y1="43.787111%" x2="124.063702%" y2="61.6843538%" id="linearGradient-10">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <rect id="path-11" x="16.5" y="17" width="4" height="1.5" rx="0.5"></rect>
        <filter x="-25.0%" y="-33.3%" width="150.0%" height="233.3%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="问卷管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="面试管理-面试题库" transform="translate(-244.000000, -200.000000)">
            <g id="卡片/试卷列表/考试中备份-7" transform="translate(220.000000, 168.000000)">
                <g id="icon/毛玻璃文件" transform="translate(24.000000, 32.000000)">
                    <rect id="_mockplus_fix_" x="0" y="0" width="24" height="24"></rect>
                    <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                    <g id="矩形" fill-rule="nonzero">
                        <use fill="url(#linearGradient-1)" xlink:href="#path-4"></use>
                        <path stroke="url(#linearGradient-3)" stroke-width="0.25" d="M18,4.925 C18.7939093,4.925 19.5126593,5.24679534 20.032932,5.767068 C20.5532047,6.28734067 20.875,7.00609067 20.875,7.8 L20.875,7.8 L20.875,15.3 C20.875,16.0939093 20.5532047,16.8126593 20.032932,17.332932 C19.5126593,17.8532047 18.7939093,18.175 18,18.175 L18,18.175 L6,18.175 C5.20609067,18.175 4.48734067,17.8532047 3.967068,17.332932 C3.44679534,16.8126593 3.125,16.0939093 3.125,15.3 L3.125,15.3 L3.125,7.8 C3.125,7.00609067 3.44679534,6.28734067 3.967068,5.767068 C4.48734067,5.24679534 5.20609067,4.925 6,4.925 L6,4.925 Z" stroke-linejoin="square" fill-opacity="0.6" fill="url(#linearGradient-2)" fill-rule="evenodd"></path>
                    </g>
                    <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                        <path stroke="url(#linearGradient-5)" d="M6.75681409,3.25 C7.10719707,3.25 7.44149436,3.39705795 7.67825677,3.65534421 L7.67825677,3.65534421 L10.9114995,7.1825181 C11.2429669,7.54411887 11.7109831,7.75 12.2015192,7.75 L12.2015192,7.75 L20,7.75 C20.7593915,7.75 21.4468915,8.05780423 21.9445436,8.55545635 C22.4421958,9.05310847 22.75,9.74060847 22.75,10.5 L22.75,10.5 L22.75,18 C22.75,18.7593915 22.4421958,19.4468915 21.9445436,19.9445436 C21.4468915,20.4421958 20.7593915,20.75 20,20.75 L20,20.75 L4,20.75 C3.24060847,20.75 2.55310847,20.4421958 2.05545635,19.9445436 C1.55780423,19.4468915 1.25,18.7593915 1.25,18 L1.25,18 L1.25,6 C1.25,5.24060847 1.55780423,4.55310847 2.05545635,4.05545635 C2.55310847,3.55780423 3.24060847,3.25 4,3.25 L4,3.25 Z" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd"></path>
                        <path stroke="url(#linearGradient-6)" d="M6.75681409,3.25 C7.10719707,3.25 7.44149436,3.39705795 7.67825677,3.65534421 L7.67825677,3.65534421 L10.9114995,7.1825181 C11.2429669,7.54411887 11.7109831,7.75 12.2015192,7.75 L12.2015192,7.75 L20,7.75 C20.7593915,7.75 21.4468915,8.05780423 21.9445436,8.55545635 C22.4421958,9.05310847 22.75,9.74060847 22.75,10.5 L22.75,10.5 L22.75,18 C22.75,18.7593915 22.4421958,19.4468915 21.9445436,19.9445436 C21.4468915,20.4421958 20.7593915,20.75 20,20.75 L20,20.75 L4,20.75 C3.24060847,20.75 2.55310847,20.4421958 2.05545635,19.9445436 C1.55780423,19.4468915 1.25,18.7593915 1.25,18 L1.25,18 L1.25,6 C1.25,5.24060847 1.55780423,4.55310847 2.05545635,4.05545635 C2.55310847,3.55780423 3.24060847,3.25 4,3.25 L4,3.25 Z"></path>
                        <path stroke="url(#pattern-7)" d="M6.75681409,3.25 C7.10719707,3.25 7.44149436,3.39705795 7.67825677,3.65534421 L7.67825677,3.65534421 L10.9114995,7.1825181 C11.2429669,7.54411887 11.7109831,7.75 12.2015192,7.75 L12.2015192,7.75 L20,7.75 C20.7593915,7.75 21.4468915,8.05780423 21.9445436,8.55545635 C22.4421958,9.05310847 22.75,9.74060847 22.75,10.5 L22.75,10.5 L22.75,18 C22.75,18.7593915 22.4421958,19.4468915 21.9445436,19.9445436 C21.4468915,20.4421958 20.7593915,20.75 20,20.75 L20,20.75 L4,20.75 C3.24060847,20.75 2.55310847,20.4421958 2.05545635,19.9445436 C1.55780423,19.4468915 1.25,18.7593915 1.25,18 L1.25,18 L1.25,6 C1.25,5.24060847 1.55780423,4.55310847 2.05545635,4.05545635 C2.55310847,3.55780423 3.24060847,3.25 4,3.25 L4,3.25 Z"></path>
                    </g>
                    <g id="矩形" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                        <use fill="url(#linearGradient-10)" xlink:href="#path-11"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>