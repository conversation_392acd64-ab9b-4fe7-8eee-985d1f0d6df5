<template>
  <div class="paper-table">
    <a-config-provider :locale="zhCN">
      <a-table
        :columns="dynamicColumns"
        :rowKey="(record:any) => record.id"
        :data-source="$props.data!.paperList"
        :row-selection="{ selectedRowKeys: delPaperList, onChange: onSelectChange }"
        :loading="$props.data!.paperLoading"
        :customRow="customRow"
        :scroll="{ x: 1200 }"
        :locale="{ filterConfirm: '确定', filterReset: '重置', emptyText: '暂无数据' }"
        :pagination="props.pagination"
        @change="handleTableChange"
      >
        <template #action="{ record }">
          <span>
            <!-- <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
              <template #title>
                <span class="tooltip-text">编辑</span>
              </template>
              <a-button
                :disabled="!record.is_self"
                type="text"
                size="small"
                @click="editPaper(record)"
              >
                <svg-icon name="edit" color="#A8AAB4" />
              </a-button>
            </a-tooltip>
            <a-divider type="vertical" /> -->
            <a-tooltip placement="bottom" color="#fff" :destroyTooltipOnHide="true">
              <template #title>
                <span class="tooltip-text">删除</span>
              </template>
              <a-popconfirm
                :disabled="!record.is_self"
                title="确定删除该试卷？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deletePaper(record.id)"
              >
                <a-button :disabled="!record.is_self" type="text" danger size="small">
                  <svg-icon name="del" color="#A8AAB4" />
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </span>
        </template>
      </a-table>
    </a-config-provider>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { Paper } from '@/models/paperModel'
import { removeTag } from '@/api/admin/tagsManage'
import { message } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  pagination: {
    type: Object,
    default: () => {}
  }
})
const emits = defineEmits([
  'getPaperList',
  'editPaper',
  'previewPaper',
  'allocatePaper',
  'handleTableChange',
  'updateSelectedPaper'
])

// 按生成时间排序
const handleTableChange = (pagination: any, filters: any, sort: any) => {
  emits('handleTableChange', { pagination, filters, sort })
}

const delPaperList = <any>ref([])
const onSelectChange = (selectedRowKeys: (string | number)[]) => {
  delPaperList.value.length = 0
  delPaperList.value.push(...selectedRowKeys)
  emits('updateSelectedPaper', delPaperList.value)
}

const customRow = (record: any, index: number) => {
  return {
    onMouseenter: () => {
      record.active = true
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = '#3158BD'
    },
    onMouseleave: () => {
      record.active = false
      const tbody = document.querySelector('.ant-table-tbody')
      const checkboxContainer = tbody?.querySelectorAll('.ant-table-selection-column')[index]
      const checkbox: any = checkboxContainer?.querySelector('.ant-checkbox-inner')
      checkbox.style.borderColor = ''
    }
  }
}


// 编辑试卷
const editPaper = (paper: Paper) => {
  emits('editPaper', paper)
}

// 删除试卷
const deletePaper = (id: string) => {
  const params = {
    papers: [id],
    tag: props.data.tag
  }
  removeTag(params).then(() => {
    message.success('删除成功！')
    emits('getPaperList')
  })
}

// 动态列
const dynamicColumns = ref([])
watch(
  () => props.data.columns,
  (val) => {
    dynamicColumns.value = val.filter((item: any) => item.display)
  },
  {
    immediate: true,
    deep: true
  }
)

watch(() => props.data.tag, () => {
  delPaperList.value.length = 0
})
</script>

<style lang="less">
.paper-table {
  .ant-table-body,
  .ant-table-column-title {
    font-size: 16px;
  }
  .ant-table-selection-column {
    padding-left: 2px !important;
  }
  .ant-table-thead > tr > th {
    text-align: center;
    &:first-child {
      padding-left: 2px !important;
    }
    &:nth-of-type(2) {
      text-align: left;
    }
    background: #f0f4fe !important;
    .ant-table-column-title {
      font-family: PingFangSC-Regular;
      font-size: 15px;
      font-weight: bold;
      color: #121633;
    }
  }

  .ant-table-tbody > tr > td {
    padding: 10px;
    color: #121633;
    text-align: center;
    &:nth-of-type(2) {
      text-align: left;
    }
  }
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-selected td {
    background: #f0f4fe;
  }
  .ant-btn {
    font-size: 14px;
  }

  .ant-divider-vertical {
    margin: 0;
  }
}
.tooltip-text {
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  color: #666666;
}
</style>
