<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="25.1014566%" y1="23.58476%" x2="54.7962568%" y2="55.8568962%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="63.6966122%" y1="3.3834%" x2="34.7898292%" y2="95.9599574%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M15.5918545,3.22428745 L15.799,4 L10.5,4 C8.90231912,4 7.59633912,5.24891996 7.50509269,6.82372721 L7.5,7 L7.5,20 C7.5,20.304373 7.54532802,20.5981591 7.62960195,20.8749761 C6.1124963,21.145838 4.61500445,20.213675 4.20879564,18.697683 L1.1029671,7.10657312 C0.674141668,5.50617481 1.62388914,3.86116394 3.22428745,3.43233851 L11.9176199,1.1029671 C13.5180182,0.674141668 15.1630291,1.62388914 15.5918545,3.22428745 Z" id="path-3"></path>
        <linearGradient x1="4.28159365%" y1="2.30865314%" x2="39.3989445%" y2="45.4381083%" id="linearGradient-4">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="58.7720955%" y1="-8.83927032%" x2="17.0463267%" y2="92.2923202%" id="linearGradient-5">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M15.799,4 L18.697683,14.8153974 C19.1265085,16.4157957 18.176761,18.0608065 16.5763627,18.489632 L7.88303026,20.8190034 C7.79862387,20.84162 7.71409338,20.860402 7.62962377,20.8754561 C7.54532802,20.5981591 7.5,20.304373 7.5,20 L7.5,7 C7.5,5.34314575 8.84314575,4 10.5,4 L15.799,4 Z" id="path-6"></path>
        <filter x="-26.5%" y="-17.8%" width="153.1%" height="135.6%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="22.5934%" y1="9.30569435%" x2="78.6694339%" y2="93.1157159%" id="linearGradient-8">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-9" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-10"></use>
        </pattern>
        <image id="image-10" width="16" height="20" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAUCAYAAACEYr13AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAFAAAAADB1dcyAAABoElEQVQ4EcWTS07DQBBE23ErFpESKQdgz2U4J0fgGGzZsssiwlYsGdmMFV61PRBFZhMWjDTq+VRVV/fYxdshPQ69WT8y+2TDYNYl1oqd2cBFx7oH0wVGZ8KPNhJXXpqVziT6HFnG0F0ecG1EGB4TssSYvhYoA0e3VKZJkKwacKYJOIksdyKTXSKu7GshIYwIyYXHhQ6nEZnn7GE9r4lewQYfgz3ZcJGS9dkVZ9mu+hH1zz0ZlKgio4i5DIFHbHROKYhoH9aJYR1yF03kjugnOi0Xm+pbIxxVXGYTAxk6nkCvIrJeJX32hfJ6004CawS2CG03OgbQu9UtpZBZxJZZM1XC+TyRhfP6RNVqJOla6tneme13ujI7Ii7ysRntWCN6QZwQCAgEP0Y5P+EGJ/ud28O929PzYIf3n4yZmKM3NR+FdmoWQZ3t8V23g728RnutKMpzJlxHHMxfFbWpXtXa1CnsX4OX9l5Tghqjznb8BLlRS+Cls3gF/UR6nhaxKGcJ+cuZt7xCZEfgluGnD0qYenUL31Z/ISvj6qa0F6T/F/gCzSv/TXZFV+QAAAAASUVORK5CYII="></image>
        <path d="M10.5,4 L17.5,4 L17.5,4 L23,9.5 L23,20 C23,21.6568542 21.6568542,23 20,23 L10.5,23 C8.84314575,23 7.5,21.6568542 7.5,20 L7.5,7 C7.5,5.34314575 8.84314575,4 10.5,4 Z" id="path-11"></path>
        <linearGradient x1="34.3857943%" y1="34.0950041%" x2="124.063702%" y2="79.9119457%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M11.25,16 L19.75,16 C20.1642136,16 20.5,16.3357864 20.5,16.75 C20.5,17.1642136 20.1642136,17.5 19.75,17.5 L11.25,17.5 C10.8357864,17.5 10.5,17.1642136 10.5,16.75 C10.5,16.3357864 10.8357864,16 11.25,16 Z M11.25,11.5 L15.75,11.5 C16.1642136,11.5 16.5,11.8357864 16.5,12.25 C16.5,12.6642136 16.1642136,13 15.75,13 L11.25,13 C10.8357864,13 10.5,12.6642136 10.5,12.25 C10.5,11.8357864 10.8357864,11.5 11.25,11.5 Z" id="path-13"></path>
        <filter x="-20.0%" y="-16.7%" width="140.0%" height="166.7%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="5.75918868%" y1="15.1846029%" x2="100%" y2="106.660888%" id="linearGradient-15">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E4EAFF" offset="100%"></stop>
        </linearGradient>
        <path d="M17.5,4 L23,9.5 L19.5,9.5 C18.3954305,9.5 17.5,8.6045695 17.5,7.5 L17.5,4 L17.5,4 Z" id="path-16"></path>
        <filter x="-18.2%" y="-9.1%" width="136.4%" height="136.4%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-692.000000, -130.000000)">
            <g id="icon/06毛玻璃/24*24/11文件" transform="translate(692.000000, 130.000000)">
                <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-7)">
                    <use fill="url(#linearGradient-4)" xlink:href="#path-6"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                </g>
                <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                    <path stroke="url(#linearGradient-8)" d="M17.3964466,4.25 L22.75,9.60355339 L22.75,20 C22.75,20.7593915 22.4421958,21.4468915 21.9445436,21.9445436 C21.4468915,22.4421958 20.7593915,22.75 20,22.75 L20,22.75 L10.5,22.75 C9.74060847,22.75 9.05310847,22.4421958 8.55545635,21.9445436 C8.05780423,21.4468915 7.75,20.7593915 7.75,20 L7.75,20 L7.75,7 C7.75,6.24060847 8.05780423,5.55310847 8.55545635,5.05545635 C9.05310847,4.55780423 9.74060847,4.25 10.5,4.25 L10.5,4.25 L17.3964466,4.25 Z" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd"></path>
                    <path stroke="url(#pattern-9)" d="M17.3964466,4.25 L22.75,9.60355339 L22.75,20 C22.75,20.7593915 22.4421958,21.4468915 21.9445436,21.9445436 C21.4468915,22.4421958 20.7593915,22.75 20,22.75 L20,22.75 L10.5,22.75 C9.74060847,22.75 9.05310847,22.4421958 8.55545635,21.9445436 C8.05780423,21.4468915 7.75,20.7593915 7.75,20 L7.75,20 L7.75,7 C7.75,6.24060847 8.05780423,5.55310847 8.55545635,5.05545635 C9.05310847,4.55780423 9.74060847,4.25 10.5,4.25 L10.5,4.25 L17.3964466,4.25 Z"></path>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    <use fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                </g>
                <g id="矩形" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                    <use fill="url(#linearGradient-15)" xlink:href="#path-16"></use>
                </g>
            </g>
        </g>
    </g>
</svg>