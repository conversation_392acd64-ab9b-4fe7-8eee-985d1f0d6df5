<script setup lang="ts">
import QuestionStem from './QuestionStem.vue'

const props = defineProps<{
  formState: any
  optionEditorsRef: any
  handleOptionBlur: () => void
  delOption: (event: Event, index: number) => void
  addOption: () => void
  showGenerateOptionModal: () => void
}>()
</script>

<template>
  <QuestionStem />
  <a-form-item v-if="formState.type === 0" name="options" class="question-options">
    <!--  eslint-disable -->
    <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
      <template v-for="(item, index) in formState.options" :key="index">
        <div class="item-option">
          <a-radio :value="item.value">
            <span
              class="option-radio" 
              :style="{ 'margin-right': formState.complicatedediting ? '8px' : '18px' }"
            >
              {{ item.value }}</span>
          </a-radio>
          <a-textarea
            v-if="!formState.complicatedediting"
            v-model:value.trim="item.content"
            class="option-content"
            :auto-size="{ minRows: 1 }"
            placeholder="点击，编辑选项；选中即正确答案"
            @blur="handleOptionBlur"
          />
          <div v-else class="editor-wrapper">
            <VueQuillEditor
              :ref="optionEditorsRef"
              v-model:content="item.content"
              @blur="handleOptionBlur"
            />
          </div>
          <svg-icon
            class="del-icon"
            name="circle-del"
            width="16px"
            height="16px"
            @click.prevent="delOption($event, index)"
          />
        </div>
      </template>
    </a-radio-group>
    <div style="display: flex; align-items: center; margin-top: 16px;">
      <div class="add-option-btn" @click="addOption">
        <svg-icon name="plus" class="mr-[8px]" />
        <span>添加选项</span>
      </div>
      <a-button class="common-ai-button" style="font-size: 12px; height: 24px;margin-left: 8px;padding: 0 8px;" @click="showGenerateOptionModal">
        <img src="@/assets/icons/svg/ai.svg" style="margin-right: 4px;width: 12px;">
        生成干扰项
      </a-button>
      <a-tooltip placement="right" overlay-class-name="light">
        <template #title>
          <span>根据题干和正确答案，借助AI工具生成相关干扰项，作为选择题的错误选项</span>
        </template>
        <svg-icon class="common-info-icon" name="info2" />
      </a-tooltip>
    </div>
  </a-form-item>
</template>

<style lang="less" scoped>
@import url('./style/scope.less');
</style>