<template>
  <div class="manual-mark-page-wrap">
    <div class="common-page-title-wrapper">
      <span class="title">问卷管理</span>
      <div class="btns">
        <a-button type="primary" @click="router.push('/admin/surveyForm')">新增问卷</a-button>
        <a-button :disabled="!checkedIds.length" @click="handleBatchRemove(checkedIds)">批量删除</a-button>
      </div>
    </div>
    <div class="filter-wrapper">
      <div style="flex: 1;">
        <div class="filter-wrapper-row">
          <Switch :columns="stateColums" v-model="params.status"></Switch>
          <a-input-search v-model:value.trim="searchValue" placeholder="请输入问卷/教师名称" allow-clear @blur="handleSearch"
            @search="handleSearch" />
        </div>
        <div class="filter-wrapper-row more-filter" v-if="filterMoreVisible">
          <div class="filter-item">
            <span class="filter-label">创建人</span>
            <a-select placeholder="全部" allowClear v-model:value="params.create_by">
              <a-select-option :value="1">本人</a-select-option>
              <a-select-option :value="0">其他</a-select-option>
            </a-select>
          </div>
          <div class="filter-item">
            <span class="filter-label">创建时间</span>
            <a-range-picker class="time-range-wrap" v-model:value="params.create_time_range"
              :placeholder="['开始时间', '结束时间']" valueFormat="YYYY-MM-DD">
              <template #suffixIcon>
                <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
              </template>
            </a-range-picker>
          </div>
        </div>
      </div>
      <div class="filter-btns">
        <div class="filter-btn filter-more" @click="filterMoreVisible = !filterMoreVisible">
          <span v-if="filterMoreActiveNumber" class="filter-number">{{ filterMoreActiveNumber }}</span>
          <img v-else src="@/assets/icons/svg/filter2.svg" alt="">
          <span style="margin-left: 4px;">更多条件</span>
        </div>
        <div class="filter-btn" style="margin-top: 16px;" @click="handleReset" v-if="filterMoreVisible">
          <img width="16" height="16" style="margin-right: 4px;" src="@/assets/images/paper/clear.png" alt="">
          <div class="label">清空条件</div>
        </div>
      </div>
    </div>
    <ListWrapper ref="listWrapperRef" :card-min-width="386" :params="params" :getListFn="getListFn"
      :updateFn="questionnairebyids">
      <template #item="{ item }">
        <SurveyCard class="list-item" :cardInfo="item" :search-text="params.condition"
          :checked="checkedIds.includes(item.id)" @preview="handlePreview(item)" @remove="handleBatchRemove([item.id])"
          @check="handleCheck(item.id)" @update="listWrapperRef?.handleUpdate([item.id])"
          @share="showShareModal(item)" />
      </template>
    </ListWrapper>
    <PagePreview v-model:visible="previewVisible" title="查看" :back-to-top-container="surveyPreviewRef?.contentEle">
      <SurveyPreview ref="surveyPreviewRef" :token="currentToken"></SurveyPreview>
    </PagePreview>
    <a-modal title="分享问卷" v-model:visible="shareModalVisible" :footer="null" :width="648" centered destroyOnClose>
      <SurveyQrCode :name="selectItem.name" :url="shareUrl" :loginMethod="selectItem.loginMethod"></SurveyQrCode>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, createVNode } from 'vue'
import { useStore } from 'vuex'
import { Modal, message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import _ from 'lodash'
import Switch from '@/components/Switch.vue';
import { PaperStatus, PaperType, PaperTypeList } from '@/models/paperModel'
import { markingtask, delTestPaper } from '@/api/admin/paperManage'
import { questionnaires } from '@/api/admin/survey'
import ListWrapper from '@/components/ListWrapper.vue'
import SurveyCard from './SurveyCard.vue'
import { useState } from '@/hooks';
import router from '@/router'
import { delquestionnaire, questionnairebyids } from '@/api/admin/survey';
import PagePreview from '@/components/PagePreview.vue'
import SurveyPreview from './SurveyPreview.vue'
import SurveyQrCode from './SurveyQrCode.vue'
import { getStudentOrigin } from '@/utils/url'

const store = useStore()

// 更多条件
const filterMoreVisible = ref(false)
const filterMoreActiveNumber = computed(() => {
  let filterMoreKeys = ['create_by', 'create_time_range']
  return filterMoreKeys.reduce((pre, cur) => {
    if (params.value[cur] !== rawParams[cur]) pre++
    return pre
  }, 0)
})

// 模糊查询
const searchValue = ref('')
function handleSearch() {  
  params.value.condition = searchValue.value
}

const stateColums = [
  { label: '全部', value: null },
  { label: '待发布', value: -1 },
  { label: '进行中', value: 0 },
  { label: '已停止', value: 1 },
]

const checkedIds = ref<string[]>([]) // 已选试卷ids
function handleCheck(id: string) {
  if (checkedIds.value.includes(id)) {
    checkedIds.value = checkedIds.value.filter(el => el !== id)
  } else {
    checkedIds.value.push(id)
  }
}

// 接口查询
const [params, resetParams, rawParams] = useState<{
  action: 'query',
  condition: string
  status: boolean | null
  create_by?: number | null
  create_time_range: null | string[]
}>({
  action: 'query',
  condition: '',
  status: null, // 阅卷状态
  create_by: undefined, // 创建人
  create_time_range: null, // 开始结束时间
})

const getListFn = (params: any) => {
  return questionnaires({
    ...params,
    basePager:{
      current: params.page,
      size: params.per_page
    },
    status: params.status === null ? undefined : params.status,
    createBy: params.create_by === undefined ? null : params.create_by,
    startTime: params.create_time_range === null ? null : params.create_time_range[0],
    endTime: params.create_time_range === null ? null : params.create_time_range[1],
  })
}
const listWrapperRef = ref<InstanceType<typeof ListWrapper>>()

// 重置
function handleReset() {
  searchValue.value = ''
  resetParams()
}

const currentToken = ref('')

// 预览相关
const previewVisible = ref(false)
const surveyPreviewRef = ref<InstanceType<typeof SurveyPreview>>()
function handlePreview({ token }: any) {
  previewVisible.value = true
  currentToken.value = token
}

// 删除
async function handleBatchRemove(ids: string[]) {
  Modal.confirm({
    title: `确定删除${ids.length > 1 ? `勾选的${ids.length}份` : '该'}问卷？`,
    async onOk() {
      try {
        await delquestionnaire({ ids })
        message.success('删除成功')
        checkedIds.value = []

        listWrapperRef.value?.handleBatchDelete(ids)

      } catch (error) {
        console.log(error)
      }
    }
  })
}


// 分享
const shareModalVisible = ref(false)
const selectItem = ref()
const shareUrl = ref('')
function showShareModal(item: any) {
  shareModalVisible.value = true
  selectItem.value = item
  let origin = getStudentOrigin()
  shareUrl.value = `${origin}/#/surveyPaper/${item.token}`
}

</script>

<style lang="less" scoped>
.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-wrapper {
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  margin: 0px 20px 20px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }

  }


}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
</style>