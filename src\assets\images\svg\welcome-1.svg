<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="25px" viewBox="0 0 24 25" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="4.88101465%" y1="49.6344098%" x2="58.6913615%" y2="50.0810602%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="74.8198153%" y1="49.3548204%" x2="22.4374449%" y2="50.6360915%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M15.4695122,21.4756098 C15.3974665,21.49182 15.3238469,21.5 15.25,21.5 L8.75,21.5 C8.19771525,21.5 7.75,21.0522847 7.75,20.5 L16.2502167,20.5009428 C16.250207,20.958306 15.9344184,21.3710059 15.4695122,21.4756098 Z" id="path-3"></path>
        <filter x="-35.3%" y="-300.0%" width="170.6%" height="700.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="-52.5319735%" y1="33.4001861%" x2="64.7457493%" y2="54.5668301%" id="linearGradient-5">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="15.5810673%" y1="23.58476%" x2="56.6301887%" y2="55.8568962%" id="linearGradient-6">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.9337492%" y1="3.3834%" x2="28.9739569%" y2="95.9599574%" id="linearGradient-7">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M13,9.5 C13.4677081,9.5 13.8729421,9.82418724 13.9756098,10.2804878 L15.712,18 L8.288,18 L10.0243902,10.2804878 C10.1270579,9.82418724 10.5322919,9.5 11,9.5 L13,9.5 Z" id="path-8"></path>
        <filter x="-40.4%" y="-35.3%" width="180.8%" height="170.6%" filterUnits="objectBoundingBox" id="filter-9">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="8.81880296%" y1="28.4757392%" x2="93.0787331%" y2="72.8050068%" id="linearGradient-10">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-11" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-12"></use>
        </pattern>
        <image id="image-12" width="22" height="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAQCAYAAAAS7Y8mAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAFqADAAQAAAABAAAAEAAAAACTYcb6AAABdElEQVQ4EbWTQUvDMBiG3yxxYcGUFgYTdvKwP+CPEDx48ib4O/yrnj2LHoRKRkN933QbDpyMuX6kTZukz/c0/Woent/vMUJMRmAW5CjgvjfGndNYQGsBNpwFvAOSKKDl6d/gycQYL0uStrZSPhl8IaAHpoLwEEyd4iTjy5kxYUagDAvlFztOHG1cR+DxNuDlNfdtC+QMdAKzZ9sPThwFFvTpLmC1HJa/fQDtV0ZLQEkgeMmySXKMsaA3K4dFA8wCMGev+OQX8ykjrQd4Enhj37H/01jQa1ouFxYhuPKR1MdC4R6zDLTXayZQRRR7JtD1QbCgy7nDVWNRR9pxMRsCT8k7QoZ315jjjOVtJjlp4JCxoPPGoa6AyOvAspKFyOr9lHCCBjTHFRzv8pAgMcGeseovspSayqHhflY0jYSoXssPoOcF5zov659oWtrEhFzjmWUH1l6pPisC4/YgNPDdy08gK0aBEk45ZD1NoGwViQJeW8L7b8ReXBAumdDTAAAAAElFTkSuQmCC"></image>
        <rect id="path-13" x="1" y="2" width="22" height="16" rx="3"></rect>
        <linearGradient x1="4.88101465%" y1="29.2213063%" x2="58.6913615%" y2="54.6071378%" id="linearGradient-14">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="74.8198153%" y1="13.3305601%" x2="22.4374449%" y2="86.1529132%" id="linearGradient-15">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M2.57951166,0.0496534543 C2.86691897,0.145455891 3.09244767,0.370984595 3.18825011,0.658391904 L4.52320117,4.66324508 C4.61489125,4.93831532 4.46623225,5.2356333 4.19116201,5.32732338 C3.91609177,5.41901346 3.61877378,5.27035447 3.5270837,4.99528423 L3.238,4.129 L1.311,4.129 L1.02320117,4.99528423 C0.940680093,5.24284745 0.691600417,5.38801755 0.442083486,5.34773387 L0.359122857,5.32732338 C0.0840526147,5.2356333 -0.0646063783,4.93831532 0.0270837023,4.66324508 L1.36203476,0.658391904 C1.53013324,0.15409646 2.07521622,-0.118445027 2.57951166,0.0496534543 Z M2.27426743,1.23826465 L1.661,3.079 L2.888,3.079 L2.27426743,1.23826465 Z M5.51278487,0 C5.80273436,-5.32628579e-17 6.03778487,0.235050506 6.03778487,0.525 L6.03778487,4.83 C6.03778487,5.11994949 5.80273436,5.355 5.51278487,5.355 C5.22283537,5.355 4.98778487,5.11994949 4.98778487,4.83 L4.98778487,0.525 C4.98778487,0.235050506 5.22283537,5.32628579e-17 5.51278487,0 Z" id="path-16"></path>
        <linearGradient x1="34.3857943%" y1="42.8697489%" x2="124.063702%" y2="63.4096032%" id="linearGradient-17">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M19.6798407,8.11698942 L20.062863,8.43838322 C20.2744003,8.61588412 20.3019923,8.93126192 20.1244914,9.14279925 L19.8030976,9.52582147 L19.8030976,9.52582147 L18.6540309,8.56164006 L18.9754247,8.17861784 C19.1529256,7.9670805 19.4683034,7.93948852 19.6798407,8.11698942 Z M18.3321812,8.94520563 L19.4812478,9.90938705 L15.8852981,14.194873 C15.7557782,14.3492288 15.5826493,14.4608569 15.3885988,14.5151292 L14.6887658,14.7108592 C14.582391,14.7406102 14.4720391,14.6784943 14.4422881,14.5721194 C14.435609,14.5482385 14.4334293,14.5233238 14.43586,14.4986459 L14.5070917,13.7754569 C14.5268429,13.5749301 14.6067116,13.3850474 14.7362315,13.2306916 L18.3321812,8.94520563 L18.3321812,8.94520563 Z M4,13.7255956 L11,13.7255956 C11.2761424,13.7255956 11.5,13.9494532 11.5,14.2255956 C11.5,14.501738 11.2761424,14.7255956 11,14.7255956 L4,14.7255956 C3.72385763,14.7255956 3.5,14.501738 3.5,14.2255956 C3.5,13.9494532 3.72385763,13.7255956 4,13.7255956 Z M14.236,11.2255956 L13.397,12.2255956 L4,12.2255956 C3.72385763,12.2255956 3.5,12.001738 3.5,11.7255956 C3.5,11.4494532 3.72385763,11.2255956 4,11.2255956 L14.236,11.2255956 Z" id="path-18"></path>
        <filter x="-6.0%" y="-7.4%" width="111.9%" height="129.7%" filterUnits="objectBoundingBox" id="filter-19">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版风格2024" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="毛玻璃风格图标" transform="translate(-52.000000, -130.000000)">
            <g id="编组-2" transform="translate(52.000000, 130.000000)">
                <rect id="bg备份-14" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
                <path d="M7.77439024,20.2804878 L8.287,18 L15.712,18 L16.2256098,20.2804878 C16.2474501,20.3775561 16.2543695,20.4745031 16.2478848,20.5689309 L16.2502167,20.5009428 L7.75,20.5 L7.75,20.5 C7.75,20.4261531 7.75817996,20.3525335 7.77439024,20.2804878 Z" id="形状结合" fill="url(#linearGradient-5)" fill-rule="nonzero"></path>
                <g id="形状结合" fill-rule="nonzero" filter="url(#filter-9)">
                    <use fill="url(#linearGradient-6)" xlink:href="#path-8"></use>
                    <use fill-opacity="0.6" fill="url(#linearGradient-7)" xlink:href="#path-8"></use>
                </g>
                <rect id="矩形" fill-opacity="0.4" fill="#8CA7FF" fill-rule="nonzero" x="6" y="20.5" width="12" height="1.5" rx="0.5"></rect>
                <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                    <rect stroke="url(#linearGradient-10)" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd" x="1.25" y="2.25" width="21.5" height="15.5" rx="3"></rect>
                    <rect stroke="url(#pattern-11)" x="1.25" y="2.25" width="21.5" height="15.5" rx="3"></rect>
                </g>
                <g id="AI" transform="translate(3.500000, 4.000000)" fill-rule="nonzero">
                    <g id="形状结合">
                        <use fill="url(#linearGradient-14)" xlink:href="#path-16"></use>
                        <use fill-opacity="0.6" fill="url(#linearGradient-15)" xlink:href="#path-16"></use>
                    </g>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-18"></use>
                    <use fill="url(#linearGradient-17)" xlink:href="#path-18"></use>
                </g>
            </g>
        </g>
    </g>
</svg>