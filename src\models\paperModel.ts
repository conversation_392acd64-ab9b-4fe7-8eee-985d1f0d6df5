export enum PaperStatus { // 考试状态
  NOT_START = -1, // 未开始
  DURING_EXAMS = 0, // 考试中
  OVER = 1, // 已结束
}
export const PaperStatusList = [
  {
    value: PaperStatus.NOT_START,
    label: '未开始',
    backgroundColor: '#f3f7f1',
    color: '#2F8C00',
  },
  {
    value: PaperStatus.DURING_EXAMS,
    label: '考试中',
    backgroundColor: '#fcf0f0',
    color: '#D71310',
  },
  {
    value: PaperStatus.OVER,
    label: '已结束',
    backgroundColor: '#f5f5f5',
    color: '#626262',
  },
]

export enum PaperType { // 考试类型
  NOT_UEC = 0,
  IS_UEC = 1,
}
export const PaperTypeList = [
  {
    value: PaperType.IS_UEC,
    label: '统考',
  },
  {
    value: PaperType.NOT_UEC,
    label: '非统考',
  },
]

export interface Paper {
  id?: string
  type?: string
  name: string
  fbody: PaperFbodyItemModel[]
  body: Array<string>
  score: number
  duration: number
  startTime: string
  endTime: string
  status?: PaperStatus
  individualTiming: '0' | '1'
  [k: string]: any
}

export interface PaperFbodyItemModel {
  title: string
  type: number
  children: {
    id: string
    score: number
    ext?: any[]   // answer字段
    new_score?: number
    index?: number
    duration: number
  }[]
  options?: number // 选做
  isUnify: boolean // 是否统一分值
  unifyScore?: number // 统一分值
  sortable: boolean
  [k: string]: any
}

export type PaperFbodyItemModelWithQuestionChildren = Omit<PaperFbodyItemModel, 'children'> & { id: string, children: any[], [key: string]: any }

export enum PaperPowerType { // 试卷权限类型
  READ = 'read',
  WRITE = 'write'
}
export const PaperPowerTypeList = [
  {
    value: PaperPowerType.READ,
    label: '可查看',
  },
  {
    value: PaperPowerType.WRITE,
    label: '可编辑',
  }
]
export enum PaperShareUnit {
  TEACHER = 'teacher',
  DEPT = 'dept'
}

export interface PaperPowerUnit {
  id: string
  name: string
  type: PaperShareUnit
  powerType?: PaperPowerType
  children?: PaperPowerUnit[]
  [propName: string]: any
}
