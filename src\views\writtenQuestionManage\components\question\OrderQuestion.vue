<template>
  <a-form
    class="form"
    ref="createQuestionFormRef"
    :model="formState"
    hideRequiredMark="true"
    :rules="rules"
    :colon="false"
    labelAlign="left"
  >
    <a-form-item label="分值" name="score">
      <score-input
        class="scoreInp"
        ref="scoreInputTotalRef"
        v-model="formState.score"
        @getScore="getScore"
      />
    </a-form-item>
    <a-form-item label="题干内容" v-bind="validateInfos.body">
      <a-textarea
        v-if="!formState.complicatedediting"
        v-model:value="formState.body"
        :rows="4"
        placeholder="点击编辑"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      />
      <VueQuillEditor
        v-else
        v-model:text="formState.body"
        v-model:content="formState.complexcontent"
        @blur="validate('body', { trigger: 'blur' }).catch(() => {})"
      ></VueQuillEditor>
    </a-form-item>
    <a-form-item name="options" class="question-options">
      <a-radio-group v-model:value="formState.answer">
        <span class="optionContent">选项内容</span>
        <template v-for="(item, index) in formState.options" :key="index">
          <div class="sortingQuestionOptions item-option">
            <a-radio :value="numberAlpht[index]">
              <span class="option-radio">{{ numberAlpht[index] }}</span>
            </a-radio>
            <a-textarea
              v-if="!formState.complicatedediting"
              class="option-content order-option-content"
              v-model:value="item.content"
              :auto-size="{ minRows: 1 }"
              placeholder="点击，编辑选项"
            />
            <div v-else class="editor-wrapper">
              <VueQuillEditor
                ref="optionEditorsRef"
                v-model:content="item.content"
              ></VueQuillEditor>
            </div>
            <svg-icon
              class="del-icon"
              name="circle-del"
              width="16px"
              height="16px"
              @click.capture="delOption(index)"
            />
          </div>
        </template>
        <span class="rightOrder">（按正确顺序设置）</span>
      </a-radio-group>
      <div class="add-option-btn" @click="addOption">
        <svg-icon name="plus" />
        <span>添加选项</span>
      </div>
    </a-form-item>
    <div class="fill-blank-config">
      <div>
        <a-checkbox v-model:checked="formState.sepscore" class="check-box" />
        <span>本题适用于“部分回答正确时可得分”</span>
        <a-tooltip placement="right">
          <template #title>
            <span
              >当考试设置为“部分回答正确时可得分”，并且本题目勾选了此选项，则学生的答案中包含正确部分答案且没有包含错误答案的情况下，可以获得一半分值。否则，在没有完全回答正确的情况下不得分。</span
            >
          </template>
          <svg-icon name="gray-tip" class="tip-icon" />
        </a-tooltip>
      </div>
    </div>
  </a-form>
</template>

<script lang="ts" setup>
import ScoreInput from './ScoreInput.vue'
import VueQuillEditor from '@/components/VueQuillEditor/index.vue'
import { ref, watch } from 'vue'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { Form } from 'ant-design-vue'

const useForm = Form.useForm

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['update:modelValue'])

const optionEditorsRef = ref()
const handleComplexToNormal = (formState: any) => {
  if (optionEditorsRef.value) {
    formState.options.forEach((option: any, index: number) => {
      option.content = optionEditorsRef.value[index].getText()
    })
  }
}
defineExpose({ handleComplexToNormal })

const scoreInputTotalRef = ref()
const numberAlpht = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
const formState = ref({
  complicatedediting: false,
  score: '0',
  body: '',
  complexcontent: '',
  answer: '',
  sepscore: false,
  options: [
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    },
    {
      content: '',
      value: ''
    }
  ]
})

// 校验分值
const checkScore = async (rule: RuleObject, value: string) => {
  if (value == '0') {
    return Promise.reject('请给该题目匹配合适的分值')
  } else {
    return Promise.resolve()
  }
}

// 校验选项内容
const checkOptions = async (rule: RuleObject, value: []) => {
  if (!value.every((item: any) => item.content)) {
    return Promise.reject('选项内容都不能为空')
  } else {
    return Promise.resolve()
  }
}

// 校验题干
const checkBody = async (rule: RuleObject, value: '') => {
  if (value.trim() === '') {
    return Promise.reject('请输入题干内容')
  } else {
    return Promise.resolve()
  }
}

// 定义规则
const rules = {
  score: [{ required: true, validator: checkScore, trigger: 'blur' }],
  body: [{ required: true, validator: checkBody, trigger: 'blur' }],
  options: [{ required: true, validator: checkOptions, trigger: 'blur' }]
}

const { validate, validateInfos } = useForm(formState.value, rules)

// 获取分数
const getScore = (score: any) => {
  console.log(score)
  formState.value.score = score
}

// 删除选项
const delOption = (index: number) => {
  console.log(index)
  formState.value.options.splice(index, 1)
}

// 增加选项
const addOption = () => {
  formState.value.options.push({
    content: '',
    value: ''
  })
}

watch(
  formState,
  (val) => {
    emits('update:modelValue', val)
  },
  {
    deep: true
  }
)

watch(
  () => props.modelValue,
  (val: any) => {
    formState.value = Object.assign(formState.value, val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
:deep(.ant-radio) {
  display: none;
}

.fill-blank-config {
  div {
    display: flex;
    align-items: center;
    line-height: 28px;
  }
  .check-box {
    margin-right: 8px;
  }
  span {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.85);
  }
  .tip-icon {
    margin-left: 8px;
  }
}
.form {
  margin-bottom: auto;
  .question-options {
    .sortingQuestionOptions {
      margin-left: 72px;
      margin-top: -5px;
    }
    .rightOrder {
      font-size: 12px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: left;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.5);
      margin-left: 70px;
    }
    .optionContent {
      position: absolute;
      width: 48px;
      height: 18px;
      font-size: 12px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: left;
      color: #626262;
      line-height: 18px;
    }
    .item-option {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 12px;
      position: relative;
      .option-radio {
        font-size: 12px;
        display: inline-block;
        width: 20px;
      }
      .option-content {
        margin-left: 4px;
        font-size: 12px;
        border: none;
        box-shadow: none;
        width: 220px;
      }
      .order-option-content {
        width: 220px;
      }
      .del-icon {
        display: none;
        cursor: pointer;
      }
      .editor-wrapper {
        padding: 3px 11px;
      }
    }
    .item-option:hover .del-icon {
      position: relative;
      display: inline-block;
    }
    .add-option-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 82px;
      height: 24px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      margin-top: 8px;
      cursor: pointer;
    }
  }
}
</style>
