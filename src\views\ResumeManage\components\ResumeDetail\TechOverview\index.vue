<script setup lang="ts">
import { Orbit, ORBIT_DIRECTION } from './Orbit'
import OrbitItem from './OrbitShowItems/Item.vue'
</script>

<template>
  <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 h-full">
    <div class="flex items-center mb-4">
      <h3 class="text-xl font-medium">
        星轨图
      </h3>
    </div>

    <div
      class="relative orbit-box h-[300px] w-full flex flex-col items-center justify-center"
    >
      <span class="pointer-events-none text-center text-8xl font-semibold leading-none"> 
        <OrbitItem>React</OrbitItem>
      </span>

      <!-- Inner Circles -->
      <Orbit 
        class="size-[30px] items-center justify-center border-none bg-transparent" 
        :duration="20" 
        :delay="20"
        :radius="70" 
        :direction="ORBIT_DIRECTION.CounterClockwise"
        path
      >
        <img
          class="absolute top-0 left-0 z-[1]" src="@/assets/images/resume/Grinning%20Squinting%20Face.png"
          alt="Grinning Squinting Face" width="32" height="32"
        >
      </Orbit>
      <Orbit 
        class="size-[30px] items-center justify-center border-none bg-transparent" 
        :duration="20" 
        :delay="10"
        :radius="90" 
        :direction="ORBIT_DIRECTION.CounterClockwise"
      >
        <OrbitItem size="xs" style-type="pink" />
      </Orbit>

      <!-- Outer Circles (reverse) -->
      <Orbit
        class="size-[50px] items-center justify-center border-none bg-transparent" :radius="130" :duration="20"
        path
      >
        <OrbitItem size="sm" style-type="green">
          Git
        </OrbitItem>
      </Orbit>
      <Orbit
        class="size-[50px] items-center justify-center border-none bg-transparent" :radius="130" :duration="20"
        :delay="200" :direction="ORBIT_DIRECTION.CounterClockwise"
      >
        <span class="i-fluent-emoji-anguished-face" />
      </Orbit>
      <Orbit class="items-center justify-center border-none bg-transparent text-4xl" :radius="140" :delay="4">
        <OrbitItem size="lg">
          <span class="!text-red-400 line-through font-bold">MySQL</span>
        </OrbitItem>
      </Orbit>
    </div>
  </div>
</template>

<style>
.orbit-box::before {
  content: '';
  position: absolute;
  border-radius: 50%;
  inset: 0;
  background: rgba(55,83,177,0.05);
  pointer-events: none;
  filter: blur(20px);
  z-index: 0;
}
</style>