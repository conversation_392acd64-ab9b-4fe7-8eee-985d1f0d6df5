<template>
  <ul class="context-menu-container">
    <li @click="onAddTag">新增</li>
    <li v-if="props.nodeData.parent" @click="onEditTag">编辑</li>
    <li v-if="props.nodeData.parent" @click="onDelTag">删除</li>
  </ul>
</template>

<script setup lang="ts">
const props = defineProps({
  nodeData: {
    type: Object,
    default: () => {}
  }
})

const emits = defineEmits(['operateTag'])
const onAddTag = () => {
  emits('operateTag', 'add')
}
const onEditTag = () => {
  emits('operateTag', 'modify')
}
const onDelTag = () => {
  emits('operateTag', 'del')
}
</script>

<style lang="less" scoped>
.context-menu-container {
  position: fixed;
  background: #fff;
  z-index: 3000;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>
