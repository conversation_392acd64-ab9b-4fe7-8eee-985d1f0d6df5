<script setup lang="ts">
import ExtractNumInput from '../extractNumInput.vue'

const props = defineProps<{
  item: {
    type: string
    id: string
    name: string
    total: number
    correctTotal: number
    extractNum: number | string | any
  }
}>()
const emits = defineEmits(['deletePkg'])
const anchorRef = useTemplateRef('anchor')

defineExpose({
  item: props.item,
  $el: anchorRef,
})
const wrapItem = useModel(props, 'item')
wrapItem.value.extractNum = props.item.correctTotal < 5 ? props.item.correctTotal : 5
function deletePackageItem(item: any) {
  emits('deletePkg', item)
}

// const addBtnRef = useTemplateRef('addBtn')
// function popoverShowChange(vis: any) {
//   addBtnRef.value!.style.visibility = vis ? 'visible' : ''
// }
</script>

<template>
  <div ref="anchor" class="w-full flex flex-nowrap justify-between items-center">
    <div class="w-[50%] text-ellipsis overflow-hidden whitespace-nowrap">
      <img src="@/assets/icons/svg/file_icon.svg" class="mr-[12px]">
      <!-- <el-tooltip
        class="box-item"
        effect="dark"
        :content="wrapItem.name"
        placement="top"
      > -->
      {{ wrapItem.name }}
      <!-- </el-tooltip> -->
    </div>
    <div class="flex flex-nowrap items-center text-[16px] font-blod">
      <span class="mr-[60px]">
        <span class="text-[#5478ee] font-black mr-[5px]">{{ wrapItem.correctTotal }}</span>题
      </span>

      <span class="text-[rgba(0,0,0,0.65)] mr-[16px]">
        题目抽取
      </span>
      <ExtractNumInput
        v-model="wrapItem.extractNum"
        class="mr-[16px]"
        :max="wrapItem.correctTotal"
        @get-score="(val:any) => {
          wrapItem.extractNum = val
        }"
      />
      题
    </div>
  </div>
</template>

<style scoped lang="less">
.del-icon{
      visibility: hidden;
}
.pkg-container{
      &:hover .del-icon{
            visibility: visible;
      }
            &:hover .add-btn{
            visibility: visible;
      }
}
.select-type:hover{
    .add-btn{
            visibility: visible;
      }  
}
.add-btn{
      padding-top: 10px;
      height: 30px;
      width: 100%;
      top: -15px;
      left: -10px;
      position: absolute;
      visibility: hidden;
}
</style>