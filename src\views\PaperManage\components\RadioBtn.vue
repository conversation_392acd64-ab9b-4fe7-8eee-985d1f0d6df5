<template>
  <span class="radio-btn">{{ option }}</span>
</template>

<script lang="ts" setup>
import {} from 'vue'

defineProps({
  option: {
    type: String,
    required: true
  }
})
</script>

<style lang="less" scoped>
.radio-btn {
  flex-grow: 0;
  flex-shrink: 0;
  // flex-basis: 0;
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background-image: linear-gradient(180deg, #ffffff 0%, #ececec 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
}
</style>
