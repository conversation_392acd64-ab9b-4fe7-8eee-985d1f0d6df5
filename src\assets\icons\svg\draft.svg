<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="教师端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单栏图标" transform="translate(-48.000000, -222.000000)">
            <g id="手风琴教师端题库管理" transform="translate(0.000000, 48.000000)">
                <g id="Stacked-Group" transform="translate(0.000000, 62.000000)">
                    <g id="5二级菜单/未选中备份-7" transform="translate(0.000000, 96.000000)">
                        <g id="01界面图标16*16/题目管理" transform="translate(48.000000, 16.000000)">
                            <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                            <g id="编组" transform="translate(2.000000, 1.500000)">
                                <rect id="矩形" fill="currentColor" x="2.5" y="5" width="5" height="1" rx="0.5"></rect>
                                <rect id="矩形" fill="currentColor" x="2.5" y="2.5" width="5" height="1" rx="0.5"></rect>
                                <polyline id="路径" stroke="currentColor" stroke-linejoin="round" points="9 2.5 11 2.5 11 8.5"></polyline>
                                <polyline id="路径" stroke="currentColor" stroke-linejoin="round" points="1 8 1 0 9 0 9 8"></polyline>
                                <polygon id="矩形" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" points="2.28983499e-16 8 4 8 4.5 9.5 7.48 9.5 7.97787089 8 12 8 12 13 2.28983499e-16 13"></polygon>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>