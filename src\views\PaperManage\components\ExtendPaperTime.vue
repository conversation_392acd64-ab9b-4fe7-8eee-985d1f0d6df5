<template>
  <a-modal
    class="extend-paper-modal"
    v-model:visible="visible"
    title="延长考试"
    width="550px"
    :footer="null"
    :centered="true"
    :maskClosable="false"
    @cancel="close"
  >
    <div class="type-cards">
      <a-form
        class="form"
        ref="basicInfoFormRef"
        :model="formState"
        :rules="rules"
        hideRequiredMark
        :colon="false"
        labelAlign="left"
      >
      <a-form-item label="考试名称">
        <span style="font-size: 12px;">{{ papername }}</span>
      </a-form-item>
        <a-form-item name="stuType">
          <template #label>
            <span>延长对象</span>
            <a-tooltip placement="right" overlayClassName="light">
              <template #title>
                <span>（1）全部考生：所有考生都获得额外的考试时间（2）指定考生：仅为特定考生延长考试时间</span>
              </template>
              <svg-icon class="common-info-icon" name="info2"></svg-icon>
            </a-tooltip>
          </template>
          <a-radio-group v-model:value="formState.stuType">
            <a-radio value="all">全部考生</a-radio>
            <a-radio value="fixed">指定考生</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="延长时间(分钟)" name="delay">
          <a-input-number
            v-model:value="formState.delay"
            :delay="0"
            :formatter="(value:number) => Math.floor(value)"
          />
        </a-form-item>
      </a-form>

      <div v-show="formState.stuType === 'fixed'">
        <a-input-search
          style="width: 240px;"
          v-model:value.trim="searchContent"
          placeholder="请输入考生姓名"
          allow-clear
          @search="getSearchData"
        />
        <a-table
          class="paper-table"
          :columns="columns"
          :rowKey="(record:any) => record.studentId"
          :data-source="stuList"
          :row-selection="{ onChange: onSelectChange }"
          :pagination="paginationConfig"
          @change="handleTableChange"
          :scroll="{ x: 400 }"
          @resizeColumn="(w: any, col: any) => col.width = w"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a>
                {{ statusTextMap[record.status] }}
              </a>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <div class="btn-group">
      <span @click="comfirm">确认</span>
      <span @click="close">取消</span>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { delaystu, examdelay } from '@/api/admin/paperManage'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = withDefaults(defineProps<{
  extendPaperModalVisible: boolean
  paper: string // 试卷id
  papername: string
}>(), {
  extendPaperModalVisible: false,
  paper: '',
  papername: ''
})

const emits = defineEmits(['closeModal'])
const statusTextMap = {
  '-2': '未开始',
  '1': '考试中',
  '0': '缺考',
  '2': '已完成'
}
const columns = ref([
  {
    title: '考生姓名',
    dataIndex: 'username',
    key: 'username',
    width: 150,
    ellipsis: true,
    resizable: true,
  },
  {
    title: '考试状态',
    dataIndex: 'status',
    width: 160,
    key: 'status',
    align: 'left',
    filters: [
      {
        text: '未开始',
        value: -2
      },
      {
        text: '考试中',
        value: 1
      },
      {
        text: '缺考',
        value: 0
      },
      {
        text: '已完成',
        value: 2
      }
    ],
    ellipsis: true,
    resizable: true,
  },
  {
    title: '已延长时间',
    dataIndex: 'delay',
    width: 150,
    key: 'delay',
    align: 'left',
    sorter: true,
    ellipsis: true,
    resizable: true,
  }
])
const paginationConfig = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'small'
})

const formState = ref({
  stuType: '',
  delay: 0
})

const checkmin = async (rule, value) => {
  if (value === '') {
    return Promise.reject('请输入延长时间')
  }
  if (Number(value) <= 0) {
    return Promise.reject('延长时间不能为0')
  }
  return Promise.resolve()
}

const rules = {
  stuType: [{ required: true, message: '请选择延长对象', trigger: 'change' }],
  delay: [{ required: true, validator: checkmin, trigger: 'change', type: 'number' }]
}

const searchContent = ref('')
const getSearchData = () => {
  getPaperList()
}

const handleTableChange = (pagination: any, filters: any = {}, sort: any = {}) => {
  paginationConfig.value.current = pagination.current
  paginationConfig.value.pageSize = pagination.pageSize
  statusList.value = filters.status
  order_type.value = sort.order ? sort.order : 'ascend'
  getPaperList()
}

const selectedStu = ref<string[]>([])
const onSelectChange = (selectedRowKeys: string[]) => {
  selectedStu.value = selectedRowKeys
}

const stuList = ref([])
const paperLoading = ref(false)
const statusList = ref([])
const order_type = ref('ascend')
const getPaperList = () => {
  paperLoading.value = true
  delaystu({
    basePager:{
      current: paginationConfig.value.current,
      size: paginationConfig.value.pageSize
    },
    templeteId: props.paper,
    condition: searchContent.value,
    page: paginationConfig.value.current,
    per_page: paginationConfig.value.pageSize,
    statusList: statusList.value,
    order_type: order_type.value
  })
    .then((res: any) => {
      stuList.value = res.records
      paginationConfig.value.total = res.total
      paperLoading.value = false
    })
    .catch(() => {
      paperLoading.value = false
    })
}

const basicInfoFormRef = ref()
const comfirm = () => {
  basicInfoFormRef.value.validate().then(() => {
    examdelay({
      action: 'add',
      templeteId: props.paper,
      studentIdList:
        formState.value.stuType === 'all'
          ? stuList.value.map((item: any) => item.studentId)
          : selectedStu.value,
      delay: formState.value.delay
    }).then(() => {
      message.success('延长考试成功!')
      close()
    })
  })
}

const close = () => {
  basicInfoFormRef.value.resetFields()
  selectedStu.value.length = 0
  emits('closeModal')
}

watch(
  () => props.extendPaperModalVisible,
  (val) => {
    visible.value = val
    if (val) {
      selectedStu.value.length = 0
      getPaperList()
    }
  }
)

const visible = ref(false)
</script>

<style lang="less" scoped>
.extend-paper-modal {
  .type-cards {
    overflow:hidden;
    :deep(.ant-form-item-label > label),
    .ant-form label {
      font-size: 12px;
      color: #626262;
    }
    :deep(.ant-col) {
      width: 100px;
    }
    .ant-form-item {
      margin: 0 0 16px;
    }
    .ant-input-number {
      font-size: 12px;
      width: 200px;
      border-radius: 8px;
      height: 32px;
    }
  }

  .btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 60px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      cursor: pointer;
      &:first-child {
        color: #fff;
        background: #5478ee;
        margin-right: 8px;
      }
    }
  }
}

.paper-table {
  margin-top: 17px;
}
</style>
<style lang="less">
.extend-paper-modal .ant-modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #121633;
}
.extend-paper-modal {
  .ant-modal-header,
  .ant-modal-body {
    padding-left: 32px !important;
    padding-right: 32px !important;
  }
  .ant-input {
    border-radius: 8px;
    font-size: 12px;
  }
}
</style>
