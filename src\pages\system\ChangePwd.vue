<template>
    <div class="forget-pwd">
        <div class="forget-pwd-main">
            <a-form class="form-content" @keyup.enter="submit"
                :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
                <a-form-item class="form-item last-form-item" label="当前密码" v-bind="validateInfos.oldPasswd">
                    <a-input-password class="form-input" v-model:value.trim="formState.oldPasswd" placeholder="请输入当前密码" @blur="validate('oldPasswd')" />
                </a-form-item>
                <a-form-item class="form-item" label="新密码" v-bind="validateInfos.password">
                    <Popover trigger="click" placement="rightTop">
                        <template #content>
                            <PwdStrenthPanel :password="formState.password" />
                        </template>
                        <a-input-password class="form-input" v-model:value.trim="formState.password" placeholder="请输入新密码" @blur="validate('password').then(() => validate('confirmPasswd'))" />
                    </Popover>
                </a-form-item>
                <a-form-item class="form-item last-form-item" name="confirmPasswd" label="再次输入" v-bind="validateInfos.confirmPasswd">
                    <a-input-password class="form-input" v-model:value.trim="formState.confirmPasswd" placeholder="请确认新密码" @blur="validate('confirmPasswd')" />
                </a-form-item>
            </a-form>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { FormInstance, message } from 'ant-design-vue'
import { checkPassword, checkConfirmPwd } from '@/utils/validate'
import PwdStrenthPanel from './components/PwdStrenthPanel.vue';
import { Popover, Form } from 'ant-design-vue'
import { Rule } from 'ant-design-vue/es/form';
import { chgPwd } from '@/api/user'

const emits = defineEmits<{
    (e: 'success'): void
}>()

const formState = reactive({
    oldPasswd: '',
    password: '',
    confirmPasswd: ''
})

const rules = reactive<Record<string, Rule[]>>({
    oldPasswd: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
    password: [{ required: true, validator: checkPassword(formState), trigger: 'blur' }],
    confirmPasswd: [{ required: true, validator: checkConfirmPwd(formState), trigger: 'blur' }]
})

const { validate, validateInfos } = Form.useForm(formState, rules)

const submit = async () => {
    try {
        await validate()
        try {
            await chgPwd({
                oldPwd: formState.oldPasswd,
                npwd: formState.password,
                passwordHash: formState.password,
            })
            message.success('修改密码成功，下次请使用新密码登录。')
            emits('success')
        } catch (error) {
            // 当前密码错误
            console.log(error)
        }
    } catch (error: any) {
        // 表单检验不通过
        console.log(error)
    }
}

defineExpose({
    submit
})

</script>

<style lang="less" scoped>
.forget-pwd {

    .forget-pwd-main {
        background-color: #fff;
        padding-bottom: 50px;
        margin: auto;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        position: relative;

        .title {
            position: absolute;
            left: 24px;
            top: 24px;
            font-size: 20px;
            line-height: 28px;
        }

        .form-content {
            width: 90%;
            .form-item {
                height: 52px;
                margin-bottom: 32px;
                background: #ffffff;

                &:last-child {
                    margin-bottom: 0;
                }

                :deep(.ant-form-item-label) {
                    line-height: 52px;
                }

                .form-input {
                    height: 52px;
                    line-height: 52px;
                    font-size: 16px;
                    font-family: PingFang SC, PingFang SC-5;
                    font-weight: 400;
                    text-align: left;
                    border: 0.5px solid rgba(37, 43, 58, 0.5);
                    border-radius: 8px;
                }
            }
        }

        .submit-btn {
            width: 372px;
            height: 48px;
            border-radius: 8px;
            background-color: #5478ee;
            color: #fff;
            font-size: 16px;
            margin-top: 32px;
        }
    }
}
</style>