<template>
  <div class="manual-mark-page-wrap">
    <div class="common-page-title-wrapper">
      <span class="title">岗位管理</span>
      <div class="btns">
        <a-button type="primary" @click="showDialog = true">创建岗位</a-button>
      </div>
    </div>
    <div class="filter-wrapper">
        <a-input-search  placeholder="请输入岗位名称" allow-clear style="width: 20%;min-width:240px" />
    </div>
    <div class="px-[20px]">
      <PostCard />
    </div>

    <el-dialog
      title="创建岗位"
      v-model="showDialog"
      width="500px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="60px" hide-required-asterisk>
        <el-form-item label="岗位名称" prop="name">
          <el-input
            v-model="form.name"
            maxlength="50"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="岗位职责" prop="duty">
          <el-input
            type="textarea"
            v-model="form.duty"
            maxlength="2000"
            show-word-limit
            image.png='none'
            :rows="6"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, createVNode } from 'vue'
import PostCard from './components/post-card.vue'
import { ElMessage } from 'element-plus'

const showDialog = ref(false)
const formRef = ref()
const form = ref({
  name: '',
  duty: ''
})

const rules = {
  name: [
    { required: true, message: '请输入岗位名称', trigger: 'blur' },
    { max: 50, message: '岗位名称不能超过50个字符', trigger: 'blur' }
  ],
  duty: [
    { required: true, message: '请输入岗位职责', trigger: 'blur' },
    { max: 2000, message: '岗位职责不能超过2000个字符', trigger: 'blur' }
  ]
}

function submitForm() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // 这里可以提交表单数据
      ElMessage.success('创建成功')
      showDialog.value = false
      resetForm()
    }
  })
}

function resetForm() {
  form.value = { name: '', duty: '' }
  formRef.value?.clearValidate()
}
</script>

<style lang="less" scoped>
.post_card{
  background: #fff;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
}
.manual-mark-page-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-wrapper {
  padding: 24px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10);
  background: #ffffff;
  border-radius: 8px;
  margin: 0px 20px 20px;
  display: flex;

  :deep(.filter-wrapper-row) {
    display: flex;
    justify-content: space-between;

    &.more-filter {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: 16px;
    }

    +.filter-wrapper-row {
      margin-top: 16px;
    }

    .ant-select,
    .ant-picker,
    .ant-input-search {
      width: 240px;
    }
  }

  .filter-item {
    display: flex;
    align-items: center;

    .filter-label {
      margin-right: 16px;
      color: #626262;
    }

    &:nth-child(3n+2) {
      justify-content: center;
    }

    &:nth-child(3n+3) {
      justify-content: flex-end;
    }
  }

  .filter-btns {
    margin-left: 8px;
  }

  .filter-btn {
    width: 108px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.filter-more {
      .filter-number {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }

  }


}

.list-item {
  width: 100%;
  border-radius: 8px;
  position: relative;
  user-select: none;
}
</style>