<template>
  <div ref="myRef" :style="{ width, height }" class="sun-burst"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  data: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: '题库数量'
  }
})
const myRef = ref<any>(null)

onMounted(() => {
  setTimeout(() => {
    drawChart()
  }, 20)
})

// 绘制折线图
const Chart = ref<any>(null)
const drawChart = () => {
  // 初始化echarts实例
  Chart.value = echarts.init(myRef.value)
  // 父组件传来的实例参数
  Chart.value.setOption(
    {
      title: {
        text: props.name,

        textStyle: {
          fontSize: 14,
          align: 'center'
        }
      },
      series: {
        type: 'sunburst',
        data: props.data,
        highlightPolicy: 'descendant',
        radius: [0, '95%'],
        sort: undefined,
        emphasis: {
          focus: 'ancestor'
        },
        levels: [
          {},
          {
            r0: '15%',
            r: '35%',
            itemStyle: {
              borderWidth: 2
            },
            label: {
              rotate: 'tangential'
            }
          },
          {
            r0: '35%',
            r: '70%',
            label: {
              align: 'right'
            }
          },
          {
            r0: '70%',
            r: '72%',
            label: {
              position: 'outside',
              padding: 3,
              silent: false
            },
            itemStyle: {
              borderWidth: 3
            }
          }
        ]
      }
    },
    false
  )
  window.addEventListener('resize', () => {
    //页面大小变化后Echarts也更改大小
    Chart.value.resize()
  })
}

watch(
  () => props.width,
  (val) => {
    Chart.value.resize()
  }
)
watch(
  () => props.height,
  (val) => {
    Chart.value.resize()
  }
)
</script>

<style lang="less" scoped>
.sun-burst {
  padding: 16px;
  background: #fff;
}
</style>
