<script lang="ts" setup>
import type { PaperFbodyItemModelWithQuestionChildren } from '@/models/paperModel'
import { createTestPaper, editTestPaper } from '@/api/admin/paperManage'
import { onemorerollpaper, queryPaperQuestions } from '@/api/admin/questionManage'
import { QuestionEnum } from '@/models/questionModel'
import PaperQuestionSelect from '@/pages/paperManage/PaperQuestionSelect.vue'
import { getTotalSeconds } from '@/utils'
import { convertBoolean, convertJson, convertKeysToCamelCase } from '@/utils/common'
import PaperBasicInfo from '@/views/PaperManage/components/PaperBasicInfo.vue'
import PaperExamConfig from '@/views/PaperManage/components/PaperExamConfig.vue'
import PaperExamPublish from '@/views/PaperManage/components/PaperExamPublish.vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import _ from 'lodash'

import { computed, createVNode, h, nextTick, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 获取当前模式
const mode = ref<'新增' | '编辑' | '克隆' | '再来一卷'>('新增')
if (route.path.includes('copy')) {
  mode.value = '克隆'
}
else if (route.path.includes('edit')) {
  mode.value = '编辑'
}
else if (route.path.includes('oneMore')) {
  mode.value = '再来一卷'
}

const steps = reactive<{
  name: string
  status: 'wait' | 'process' | 'finish' | 'error'
}[]>([
  { name: '基本信息', status: 'wait' },
  { name: '选择试题', status: 'wait' },
  { name: '考试配置', status: 'wait' },
  { name: '发布考试', status: 'wait' },
])

const basicInfoRef = ref<InstanceType<typeof PaperBasicInfo>>()
const selectRef = ref<InstanceType<typeof PaperQuestionSelect>>()
const configRef = ref<InstanceType<typeof PaperExamConfig>>()

const paperInfo = ref<Record<string, any>>({})

let missQues: Record<string, number> | null = null // 丢失的题目
async function getDetail() {
  let res: any = {}
  if (mode.value === '再来一卷') {
    const { paper, miss_ques } = await onemorerollpaper({ templeteId: route.query.id as number })
    res = paper
    missQues = miss_ques
  }
  else {
    res = await queryPaperQuestions({ id: route.query.id, voidCol: 0 })
  }
  try {
    // 如果是单题限时，则编辑或者克隆时将考试时长设置为120分钟
    if (res.individualTiming == '1') {
      res.duration = 120
    }

    if (res.cutscreen > 0) {
      res.iscutscreen = true
    }
    else {
      res.iscutscreen = false
      res.cutscreen = null
    }
    if (res.neterror > 0) {
      res.isneterror = true
    }
    else {
      res.isneterror = false
      res.neterror = null
    }
    res.hiddenname = !res.showname
    if (mode.value === '编辑') {
      res.startTime = dayjs(res.startTime)
      res.endTime = dayjs(res.endTime)
    }
    if (mode.value === '克隆' || mode.value === '再来一卷') {
      res.fbody = res.fbodyJson
      res.ojlan = res.ojlanList

      delete res.id
      delete res.create_at
      delete res.endTime
      delete res.startTime
      delete res.examtime
      delete res.author
      delete res.teacher
    }
    if (res.notpaperids && typeof res.notpaperids === 'string') {
      res.notpaperids = JSON.parse(res.notpaperids)
    }
    paperInfo.value = res

    await nextTick()
    selectRef.value?.getPaperDetail()
  }
  catch (error) {
    console.log(error)
  }
}
if (mode.value !== '新增' && route.query.id) {
  getDetail()
}

// 当前步骤
const currentStep = ref(0)
const operationFlag = ref(false)
watch(currentStep, () => {
  if (currentStep.value === 1) {
    if (mode.value === '再来一卷' && missQues) {
      const arr = Object.entries(missQues).map(([type, num]) => {
        return h('p', [
          h('span', { style: { color: '#5478EE' } }, num),
          `道${QuestionEnum[type]}`,
        ])
      })
      try {
        if (arr?.length > 0) {
          Modal.info({
            title: '提示',
            width: 500,
            content: h('div', {}, [
              h('p', {}, '由于原题库被删除或题型数量不足，当前试卷相比原试卷减少了'),
              ...arr,
              h('p', {}, '请自行添加并完善'),
            ]),
            okText: '我已知晓',
          })
        }
        missQues = null
      }
      catch (error) {
        console.log(error)
      }
    }
  }
})

// eslint-disable-next-line ts/ban-ts-comment
/** @ts-ignore */ 
watch([() => paperInfo.value], (val) => {
  operationFlag.value = true
}, { deep: true })

watchPostEffect(() => {
  // eslint-disable-next-line ts/ban-ts-comment
  // @ts-ignore
  Object.keys(basicInfoRef.value?.formState).forEach((key) => {
    const temp = basicInfoRef.value?.formState[key]
    watch(() => basicInfoRef.value?.formState[key], () => {
      operationFlag.value = true
    })
  })
})
/**
 * 初始化步骤状态
 */
function initStepStatus() {
  steps.forEach(item => item.status = mode.value === '新增' ? 'wait' : 'finish')
  steps[currentStep.value].status = 'process'
}
initStepStatus()

// eslint-disable-next-line vue/return-in-computed-property
const isNextDisabled = computed(() => {
  switch (currentStep.value) {
    case 0:
      return !basicInfoRef.value?.canNext
    case 1:
      return !selectRef.value?.canNext
  }
})

/** 
 * @description: 步骤条点击处理
 *  1. 保存每个步骤的完成状态
 *  2. 完成状态间可任意跳转
 *  3. 点击【下一步】或上方步骤条跳转都需要重新对当前页面进行校验
 */
async function handleStepClick(index: number, isBtnClick = false) {
  // 判断目标步骤是否已完成
  if (steps[index].status !== 'finish' && !isBtnClick) 
    return
  try {
    // 执行当前组件的校验和保存
    if (currentStep.value === 0) {
      await basicInfoRef.value?.validateForm()
    }
    else if (currentStep.value === 1) {
      await selectRef.value?.validateForm()
    }
    else if (currentStep.value === 2) {
      await configRef.value?.validateForm()
    }
    steps[currentStep.value].status = 'finish'
    steps[index].status = 'process'
    currentStep.value = index
    await nextTick()
  }
  catch (error) {
    console.log(error)
    if (index < currentStep.value) {
      // 如果是之前的步骤，则跳转
      steps[index].status = 'process'
      steps[currentStep.value].status = 'wait'
      currentStep.value = index
    }
    else {
      steps[currentStep.value].status = 'error'
    }
  }
}

// 创建|编辑试卷
const loading = ref(false)
async function addPaper() {
  try {
    // 这里希望再次校验一下基本信息（主要是时间，因为可能当时间校验是通过的，但提交时时间不通过）
    await basicInfoRef.value?.validateForm()
  }
  catch (error) {
    steps[currentStep.value].status = 'finish'
    steps[0].status = 'error'
    currentStep.value = 0
    return
  }

  const param: any = {
    action: mode.value === '编辑' ? 'modify' : 'add',
    paper: _.cloneDeep(paperInfo.value), // 这里要深克隆一下，否则下面改动会造成原本数据的变化
  }
  param.paper.ojlan = 'c,cpp,python,java,js,go'
  param.paper.tags = []
  param.paper.cutscreen = param.paper.cutscreen || 0
  param.paper.neterror = param.paper.neterror || 0
  param.paper.fbody = param.paper.fbody.map((item: PaperFbodyItemModelWithQuestionChildren) => {
    return {
      title: item.title,
      type: item.type,
      children: item.children.map(q => ({
        id: q.id,
        questionUUID: q.questionUUID ? q.questionUUID : q.questionBankUUID,
        score: q.new_score,
        duration: q.duration,
        ext: [QuestionEnum['填空题'], QuestionEnum['问答题']].includes(q.type) ? q.answer : null,
      })),
      options: item.options || null,
    }
  })

  // 考试时长
  if (param.paper.individualTiming == '1') {
    const totalSeconds = getTotalSeconds(param.paper.fbody)
    param.paper.duration = Number.parseFloat((totalSeconds / 60).toFixed(2))
  }

  // 验证限时迟到时间（非统考没有限时迟到时间，无需校验）
  if (param.paper.uniexam == '1' && (param.paper.limitlateness > param.paper.duration)) {
    message.warning('限时迟到时间不能大于考试总时长')
    return
  }

  // 验证选做题的可选题量必须大于options
  if (param.paper.fbody.some((item: any) => item.options && item.children.length <= item.options)) {
    message.warning('选做试题的可选题量需大于必答题量')
    return
  }
  param.paper.startTime = dayjs(param.paper.startTime).format('YYYY-MM-DD HH:mm:ss')
  param.paper.endTime = param.paper.uniexam == '0' ? dayjs(param.paper.endTime).format('YYYY-MM-DD HH:mm:ss') : dayjs(param.paper.startTime).add(param.paper.duration, 'minutes').format('YYYY-MM-DD HH:mm:ss')
  param.paper.createTime = param.paper.startTime 
  delete param.paper.start_time
  delete param.paper.end_time

  param.paper.showname = !param.paper.hiddenname
  loading.value = true
  const _fbodyJson = param.paper.fbody
  const convertParams = convertKeysToCamelCase(convertBoolean(convertJson(param.paper))) as any
  convertParams.fbodyJson = _fbodyJson
  convertParams.type = convertParams.type == 'regular' ? 1 : convertParams.type

  const execFunc = param.action === 'modify' ? editTestPaper : createTestPaper
  execFunc({ ...convertParams })
    .then(() => {
      loading.value = false
      message.success(`${mode.value}成功!`)
      router.back()
    })
    .catch(() => {
      loading.value = false
    })
}

// 处理每个步骤的表单提交
function handlePaperInfoChange(kv: Record<string, any>) {
  Object.assign(paperInfo.value, kv)
}
function cancelCreate() {
  if (!operationFlag.value) {
    router.back()
  }
  Modal.confirm({
    title: '确认退出此页面？',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { }, '系统可能不会保存您所做的修改'),
    onOk() {
      router.back()
    },
    onCancel() {
      console.log('Cancel')
    },
    class: 'test',
  })
}
</script>
  
<template>
  <div class="create-paper-container">
    <h3 class="create-title">
      <span><span style="color: rgba(0, 0, 0, 0.45)" @click="$router.back()">试卷管理 /
      </span><span>{{ mode === '再来一卷' ? mode : `${mode}考试` }}</span>
      </span>
    </h3>
    <div class="create-paper-steps">
      <a-steps :current="currentStep" size="small">
        <!-- eslint-disable-next-line vue/valid-v-for -->
        <a-step
          v-for="(item, index) in steps" :title="item.name" :status="item.status" :disabled="item.status !== 'finish'"
          @click="handleStepClick(index)"
        />
      </a-steps>
    </div>
    <div class="create-paper-content">
      <div class="content-main">
        <PaperBasicInfo v-show="currentStep === 0" ref="basicInfoRef" :paper-info="paperInfo" @change="handlePaperInfoChange" />
        <PaperQuestionSelect v-show="currentStep === 1" ref="selectRef" :paper-info="paperInfo" @change="handlePaperInfoChange" />
        <PaperExamConfig v-show="currentStep === 2" ref="configRef" :paper-info="paperInfo" @change="handlePaperInfoChange" />
        <PaperExamPublish v-if="currentStep === 3" :paper-info="paperInfo" />
        <div class="footer">
          <a-button @click="cancelCreate">
            取消
          </a-button>
          <a-button v-if="currentStep > 0" @click="handleStepClick(currentStep - 1, true)">
            上一步
          </a-button>
          <a-button
            v-if="currentStep < steps.length - 1" type="primary" :disabled="isNextDisabled"
            @click="handleStepClick(currentStep + 1, true)"
          >
            下一步
          </a-button>
          <a-button v-if="currentStep === 3" type="primary" :loading="loading" @click="addPaper">
            立即发布
          </a-button>
        </div>
      </div>
      <div v-show="currentStep === 1" id="paper-form-teleport" />
    </div>
  </div>
</template>
  
<style lang="less" scoped>
.create-paper-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    padding: 0 20px 20px 20px;

    .create-title {
        line-height: 48px;
        font-size: 14px;
        font-weight: 600;
    }

    .create-paper-steps {
        width: 600px;
        padding-bottom: 12px;
        font-size: 12px;

        :deep(.ant-steps-item-title) {
            font-size: 12px;
            line-height: 20px;
            &::after {
                top: 10px;
            }
        }

        :deep(.ant-steps-item-icon) {
            width: 20px;
            height: 20px;
            line-height: 17px;
            &:is(.ant-steps-item-wait,.ant-steps-item-finish,.ant-steps-item-error .ant-steps-item-icon) {
                background-color: #f5f5f5;
            }
        }

        :deep(.ant-steps-item-title::after) {
            background: rgba(0, 0, 0, 0.65);
        }

        :deep(.ant-steps-item-wait .ant-steps-item-title) {
            color: rgba(0, 0, 0, 0.85);
        }

        :deep(.ant-steps-item-wait .ant-steps-item-icon) {
            border-color: rgba(0, 0, 0, 0.65);

            .ant-steps-icon {
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }

    .create-paper-content {
        min-height: 0;
        flex: 1;
        display: flex;
        .content-main {
            display: flex;
            flex-direction: column;
            position: relative;
            padding: 24px;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
            flex: 1;
            min-width: 0;
        }
        .footer {
            width: 100%;
            display: flex;
            margin-top: 24px;
            border-top: 1px solid #e8e8e8;
            padding-top: 24px;

            .ant-btn {
                border-radius: 8px;
                font-size: 14px;
                margin-right: 8px;
            }
        }
    }
}
</style>
