import os
import re
import json
import webbrowser
from datetime import datetime

# 尝试设置搜索的目录路径为当前运行目录下的src文件夹
code_directory = os.path.join(os.getcwd(), 'src')
start_directory = os.getcwd()
# 如果code_directory不存在，提示用户输入
if not os.path.exists(code_directory):
    print(f"The directory {code_directory} was not found.")
    code_directory = input("Please enter the correct path to the src directory: ")
    if not os.path.exists(code_directory):
        raise Exception(f"The directory {code_directory} does not exist.")

# 列出要搜索的代码文件扩展名和资源文件扩展名
code_exts = ['.vue', '.ts', '.js', '.css', '.less']
image_exts = ['.jpg', '.png', '.svg', '.webp', '.gif']

# 准备正则表达式，匹配资源文件路径
image_pattern = re.compile(r'["\']([^"\'>]+?(' + '|'.join(re.escape(ext) for ext in image_exts) + '))(\?[^"\']*["\'])?')
# resource_pattern = re.compile(r'(?:[.]{0,2}/)?([\w\-\d./@]+?)([\w\-\d]+?\.\w+)$')
resource_pattern = re.compile(r'(?:[.]{0,2}/|@/)?([\w\-\d./@]+?/)([\w\-\d]+?\.\w+)$')
# 用于存储结果的列表
files_with_images = []

# 遍历目录及其所有子目录
for root, dirs, files in os.walk(code_directory):
    relative_dir = os.path.relpath(root, code_directory)
    for file in files:
        if any(file.endswith(ext) for ext in code_exts):
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                matches = image_pattern.findall(content)
                if matches:
                    images_info = []  # 新的数组，用于存储对象信息
                    for match in matches:
                        match0 = match[0]
                        if match0.find('\n') != -1:
                            match0 = match0.strip()
                            last_newline_index = match0.rfind('\n')
                            match0 = match0[last_newline_index + 1:]
                            match0 = match0.strip()
                        resource_match = resource_pattern.search(match0)
                        if resource_match:
                            # 提取资源文件前的路径和文件名
                            path = resource_match.group(1)
                            filename = resource_match.group(2)
                            
                            # 根据前缀确定基础路径
                            if path.startswith('@/'):
                                base_path = start_directory
                                path = path[2:]  # 移除 '@/'
                            elif path.startswith('./') or path.startswith('../'):
                                base_path = code_directory
                                # 正确处理相对路径
                                path = os.path.join(os.path.dirname(root), path)
                            else:
                                base_path = code_directory

                            # 替换路径中的 '/' 为当前操作系统的分隔符
                            proper_path = path.replace('/', os.sep)
                            # 构建文件的绝对路径
                            filepath = os.path.normpath(os.path.join(base_path, proper_path))
                            
                            # 检查资源文件是否存在
                            exist = os.path.exists(os.path.join(filepath, filename))

                            relative_path = filepath
                            if relative_path.startswith(start_directory):
                                relative_path = relative_path[len(start_directory):]
                            # 添加到信息列表中，仅当文件名不为空
                            if filename:
                                img_path = os.path.join(relative_path, filename)
                                if img_path.startswith("\\") :
                                    img_path = img_path[1:]
                                image_info_to_add = {
                                    "filename": filename,
                                    "filepath": img_path.replace("\\","/"),
                                    "exist": exist,
                                    "sourceCode": match0
                                }

                                # 检查是否已存在相同数据
                                if not any(info['filename'] == image_info_to_add['filename'] and
                                        info['filepath'] == image_info_to_add['filepath'] for info in images_info):
                                    images_info.append(image_info_to_add)
                    # 构建每个文件的信息字典
                    file_info = {
                        "dir": relative_dir.replace("\\","/"),
                        "filename": file,
                        "resources": images_info
                    }

                    if len(images_info) > 0:
                        files_with_images.append(file_info)
            except Exception as e:
                print(f"Error reading {file_path}: {e}")

# 获取当前的日期和时间
current_datetime = datetime.now().strftime('%Y%m%d%H%M%S')

# 设置输出JSON文件的文件名
output_filename = f'代码中的图片.html'

# 创建一个集合，用于存储known_objects中所有文件的路径
known_files = set()

# 填充known_files集合
for item in files_with_images:
    for resource in item["resources"]:
        # 将标准化的路径加入到集合中
        known_files.add(os.path.normpath(resource["filepath"]))

# 用于存储未引用文件的列表
unused_files = []

# 遍历目录
for root, dirs, files in os.walk('src/assets'):
    for file in files:
        # 构建完整的文件路径
        filepath = os.path.join(root, file)
        # 标准化文件路径
        normalized_path = os.path.normpath(filepath)
        # 检查文件扩展名是否是我们要找的类型
        if normalized_path.endswith(('.svg', '.png', '.jpg', '.gif', '.webp')):
            # 检查文件是否在known_files集合中
            if normalized_path not in known_files:
                # 如果不在known_files中，则添加到未引用文件列表中
                unused_files.append(normalized_path)

code_report = '''
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>代码中的图片有效性检查结果</title>
    <style>
        /* 资源卡片样式更新 */
        .resource-item {
            box-sizing: border-box;
            width: 300px; /* 将宽度固定为300px */
            height: auto;
            margin: 0 10px 20px 0;
            border: 1px solid #ddd;
            padding: 10px;
            transition: box-shadow 0.3s ease-in-out;
            display: inline-block; /* 更改为横向排列 */
            vertical-align: top; /* 保持顶部对齐 */
        }
    
        .resource-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
    
        /* 代码样式保持不变 */
        .res-source {
            font-family: 'Courier New', Courier, monospace;
            color: #c7254e;
            padding: 5px;
            display: block;
            overflow-wrap: break-word;
        }
    
        /* 图片大小适应 */
        .resource-item img {
            max-width: 100%;
            height: auto;
        }
    
        /* 浅红色渐变背景色 */
        .img-notexist {
            background: linear-gradient(to right, #f8e3e3, #ffbaba);
        }
    
        /* 浅绿色渐变背景色 - 这里应该是.img-exist */
        .img-exist {
            background: linear-gradient(to right, #ddfadd, #c5ffc5);
        }
    </style>
</head>

<body>
    <h1>代码中的图片有效性检查结果</h1>
    <div id="resource-container"></div>

    <script>

        const json = `#JSON#`


        // 当文件被选中时处理文件

        const container = document.getElementById('resource-container');
        container.innerHTML = ''; // 清除旧内容
        const data = JSON.parse(json);

        // 遍历JSON数据并创建DOM元素
        data.forEach(component => {
            const compElem = document.createElement('div');
            compElem.classList.add('component-item');
            const compTitle = document.createElement('h2');
            compTitle.textContent = `${component.dir}/${component.filename}`;
            compElem.appendChild(compTitle);

            component.resources.forEach(resource => {
                const resElem = document.createElement('div');
                resElem.classList.add('resource-item');
                resElem.classList.add(`${resource.exist ? "img-exist" : "img-notexist"}`);

                const resTitle = document.createElement('p');
                resTitle.className = "res-title";
                resTitle.textContent = `文件名: ${resource.filename}`;
                resElem.appendChild(resTitle);

                const resExistence = document.createElement('p');
                resExistence.className = "res-existence";
                resExistence.textContent = `是否存在: ${resource.exist ? "是" : "否"}`;
                resElem.appendChild(resExistence);

                if (resource.exist) {
                    const resImg = document.createElement('img');
                    resImg.src = `${resource.filepath}`;
                    resElem.appendChild(resImg);
                }

                const resSource = document.createElement('p');
                resSource.className = "res-source";
                resSource.textContent = `代码原文: ${resource.sourceCode}`;
                resElem.appendChild(resSource);
                compElem.appendChild(resElem);
            });

            container.appendChild(compElem);
        });
    </script>
</body>

</html>
'''

# 创建一个HTML报告
html_report = '''<html><head><title>没有用到的图片</title>
<style>
  body {
    font-family: 'Arial', sans-serif;
    background: #f9f9f9;
    line-height: 1.6;
    padding: 20px;
    color: #333;
  }

  h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
  }

  .container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
  }

  .card {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 300px; /* Width of each card */
    margin-bottom: 20px;
    padding: 15px; /* Padding around the content */
    border-radius: 10px; /* Rounded corners for the card */
    transition: box-shadow 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .title {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #555;
    text-align: center;
    word-break: break-all; /* Prevents long text from overflowing */
  }

  img {
    max-width: 100px; /* Maximum width of image */
    height: auto;
    border: 1px solid #ddd;
    border-radius: 5px; /* Rounded corners for the image */
    padding: 5px;
    margin-bottom: 15px; /* Space between image and bottom of the card */
  }

</style>
</head><body>\n'''
html_report += "<h1>以下图片在代码中未明文出现</h1><p>要注意有的图片可能是通过变量拼接的方式引用，这类图片无法探测到。</p>\n<div class='container'>"

try:
    # 将每个未引用的文件的路径添加为img标签
    for unused_file in unused_files:
        img_tag = f"<p class='card'><span class='title'>{unused_file}</span><br/><img src='{unused_file}' alt='{unused_file}' style='max-width: 100px;' /></p>\n"
        html_report += img_tag

    # 结束HTML文档
    html_report += "</div></body></html>"

    # 写入到HTML文件中
    with open("可能不用的图片.html", "w", encoding="utf-8") as file:
        file.write(html_report)

    content = code_report.replace("#JSON#", json.dumps(files_with_images, indent=4).replace("\\\\","\\"))
    with open(output_filename, 'w', encoding='utf-8') as json_file:
        json_file.write(content)
    
    file1_path = os.path.join(os.getcwd(), "代码中的图片.html")
    file2_path = os.path.join(os.getcwd(), "可能不用的图片.html")
    webbrowser.open('file://' + file1_path)
    webbrowser.open('file://' + file2_path)

except Exception as e:
    print(f"Error writing to {output_filename}: {e}")
