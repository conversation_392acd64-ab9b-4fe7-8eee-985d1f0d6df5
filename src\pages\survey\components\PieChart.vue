<template>
    <div ref="chartRef" style="height: 100%;"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { ECharts, init } from 'echarts'
import { QUESTIONS } from '@/config/constants'
import type { ZRColor } from 'echarts/types/src/util/types'

const props = defineProps<{
    item?: any
}>()

// 调色板
const colorList: ZRColor[] = QUESTIONS.map((item) => ({
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color: item.color
    }, {
        offset: 1, color: item.color + '80'
    }],
    global: false
}))

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
    if (!chartRef.value) return
    let data = Object.entries(props.item.answer_analysis).map(([k, v]) => {
        let option = props.item.options.find((o: any) => o.value == k)
        return { name: option?.content, value: v, rate: option?.rate }
    });
    chart.value = init(chartRef.value)
    chart.value?.setOption({
        color: colorList,
        tooltip: {
            trigger: 'item',
            valueFormatter: (c: number) => `${c}`
        },
        grid: {
            left: 0,
            right: 0,
            top: 0
        },
        series: [
            {
                type: 'pie',
                radius: '60%',
                minAngle: 10,
                label: {
                    // alignTo: 'edge',
                    // edgeDistance: 10,
                    formatter: (e) => {
                        console.log(e.name, e)
                        return e.name + ': ' + e.data.rate.toFixed(2) + '%'
                    },
                },
                labelLine: {
                    length: 20,
                    length2: 20
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                data
            }
        ]
    })
}


watch(() => props.item, async (val) => {
    draw()
}, { deep: true })

onMounted(() => {
    try {
        setTimeout(() => {
            // 等待dom过渡效果完成
            draw()
        }, 500);
    } catch (error) {
        console.log(error)
    }
})

</script>

<style lang="less" scoped>
// .ant-spin-nested-loading {
//     height: 100%;

//     :deep(.ant-spin-container) {
//         height: 100%;
//     }
// }</style>