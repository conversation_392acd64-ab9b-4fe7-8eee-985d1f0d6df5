export const questionColumns = [
    {
        title: '题型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
        ellipsis: false,
        filters: [
            { text: '单选题', value: 0 },
            { text: '多选题', value: 1 },
            { text: '判断题', value: 2 },
            { text: '填空题', value: 5 },
            { text: '问答题', value: 3 },
            { text: '算法题', value: 4 },
            { text: '排序题', value: 6 }
        ],
        resizable: true,
    },
    {
        title: '题干',
        dataIndex: 'body',
        key: 'body',
        ellipsis: true,
        resizable: true,
        width: 500,
    },
    {
        title: '题库',
        dataIndex: 'category_name',
        key: 'category_name',
        width: 160,
        ellipsis: true,
        resizable: true
    },
    {
        title: '分值',
        dataIndex: 'score',
        key: 'score',
        width: 120,
        ellipsis: true,
        resizable: true,
    },
    {
        title: '正确率',
        dataIndex: 'accuracy',
        key: 'accuracy',
        width: 120,
        ellipsis: true,
        resizable: true,
        sorter: (a: any, b: any) => {
            let aAccuracy = a.accuracy,
                bAccuracy = b.accuracy
            if (a.accuracy === null) aAccuracy = -1
            if (b.accuracy === null) bAccuracy = -1
            return aAccuracy - bAccuracy
        }
    },
    {
        title: '引用次数',
        dataIndex: 'references',
        key: 'references',
        width: 100,
        ellipsis: true,
        resizable: true,
    },
    {
        title: '更新时间',
        dataIndex: 'update_at',
        key: 'update_at',
        width: 180,
        ellipsis: true,
        resizable: true,
    },
    {
        title: '创建人',
        dataIndex: 'author',
        key: 'author',
        width: 100,
        ellipsis: true,
        resizable: true,
    },
    {
        title: '操作',
        key: 'action',
        width: 170,
        align: 'center',
        fixed: 'right',
    }
]