<script setup lang="ts">
import TypeButton from './typeBtn.vue'

const emits = defineEmits(['switchType'])
const activeType = defineModel()
// const modelTitle = defineModel('model-title')
// const modalVis = defineModel('modal-vis')

function switchType(type: number) {
  emits('switchType', type)
}
</script>

<template>
  <div class="flex flex-wrap justify-center dmb">
    <img src="@/assets/icons/svg/no_data.svg">
    <p class="text-[rgba(0,0,0,0.45)] w-full text-center">
      当前面试暂无试题，请点击下方按钮添加
    </p>
  </div>
  <TypeButton @switch-type="switchType" />
</template>

<style scoped lang="scss">
.dmb{
  @media screen and (max-width: 1440px) {
    margin-bottom: 10px;
  }
    @media screen and (min-width: 1441px) {
    margin-bottom: 80px;
  }
}
.dh {
      height: calc(100vh - 400px);
}
.normal_bg{
      background: linear-gradient(180deg,#f1f4ff, #f8faff);
      border: 1px solid transparent;
}
.active_bg{
      // background: linear-gradient(180deg,#dde6ff, #f8faff);
      border: 1px solid #5478ee;
      // border-radius: 8px;
      // box-shadow: 0px 10px 40px 0px rgba(0,0,0,0.10);
}

// 修复点击时的 focus 样式覆盖问题
.transition-all {
  outline: none !important; // 移除默认的 focus outline

  &:focus {
    outline: none !important;
    border: 1px solid transparent !important;
  }

  &:focus.active_bg {
    border: 1px solid #5478ee !important;
  }

  // 确保 active 状态的边框优先级最高
  &.active_bg {
    border: 1px solid #5478ee !important;
  }
}
</style>