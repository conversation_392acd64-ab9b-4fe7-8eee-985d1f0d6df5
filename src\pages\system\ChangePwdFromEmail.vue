<template>
  <div class="exam-system">
    <div class="login-wrapper">
      <h1 class="login-logo">
        <svg-icon class="svgClass" style="width: 138px; height: 30px" name="newLogo" />
      </h1>

      <a-form class="login-form" hideRequiredMark>
        <a-tabs class="modifypwd-tabs" v-model:activeKey="activeKey">
          <a-tab-pane key="0" tab="修改密码" />
        </a-tabs>
        <div class="user-container">
          <a-form-item ref="password" v-bind="validateInfos.password">
            <Popover trigger="click" placement="rightTop">
                <template #content>
                    <PwdStrenthPanel :password="formState.password" />
                </template>
                <a-input
                  v-model:value.trim="formState.password"
                  :type="!passwdShow ? 'password' : ''"
                  placeholder="输入新密码"
                  autocomplete="off"
                  class="login-input2"
                  @blur="validate('password').then(() => validate('confirmPasswd'))"
                >
                  <template #suffix>
                    <svg-icon
                      v-if="!passwdShow"
                      @click="showPasswd"
                      name="invisible"
                      style="color: #999"
                    />
                    <eye-outlined v-else @click="showPasswd" style="color: #999" />
                  </template>
                </a-input>
            </Popover>
          </a-form-item>
          <a-form-item ref="confirmPasswd" v-bind="validateInfos.confirmPasswd">
            <a-input
              v-model:value.trim="formState.confirmPasswd"
              :type="!confirmPasswdShow ? 'password' : ''"
              placeholder="再次输入新密码"
              autocomplete="off"
              class="login-input2"
              @blur="validate('confirmPasswd')"
            >
              <template #suffix>
                <svg-icon
                  v
                  v-if="!confirmPasswdShow"
                  @click="showConfirmPasswd"
                  name="invisible"
                  style="color: #999"
                />
                <eye-outlined v-else @click="showConfirmPasswd" style="color: #999" />
              </template>
            </a-input>
          </a-form-item>
        </div>
        <a-form-item>
          <a-button type="primary" @click="onSubmit" :loading="loading" style="height: 40px"
            ><span>提交</span></a-button
          >
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, UnwrapRef } from 'vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import { checktoken, resetpwd, changepwd } from '@/api/user'
import { useRoute } from 'vue-router'
import { message, Popover, Form } from 'ant-design-vue'
import { Rule } from 'ant-design-vue/es/form';
import router from '@/router'
import PwdStrenthPanel from './components/PwdStrenthPanel.vue';
import { checkPassword, checkConfirmPwd } from '@/utils/validate'

interface FormState {
  password: string
  confirmPasswd: string
}

const loading = ref(false)
const passwdShow = ref(false)
const showPasswd = () => {
  passwdShow.value = !passwdShow.value
}
const confirmPasswdShow = ref(false)
const activeKey = ref('0')
let formState = reactive({
  password: '',
  confirmPasswd: ''
})

const showConfirmPasswd = () => {
  confirmPasswdShow.value = !confirmPasswdShow.value
}
const rules = reactive<Record<string, Rule[]>>({
  password: [{ required: true, validator: checkPassword(formState), trigger: 'blur' }],
  confirmPasswd: [{ validator: checkConfirmPwd(formState), trigger: 'blur' }]
})

const { validate, validateInfos } = Form.useForm(formState, rules)

const route = useRoute()
const len = route.path.split('/').length
const params = route.path.split('/')[len - 1]
let id = null
const token = route.path.split('/')[2]

// 校验token是否有效
async function checkTokenValidity() {
  try {
    const res = await checktoken({ t: token })
    id = res
    return true
  } catch (error) {
    if ((error as string).includes('失效')) {
      // window.location.href = '/overlate.html'
      router.replace({
        name: 'loseEfficacy',
      })
      return false
    }
  }
}
checkTokenValidity()

async function onSubmit() {
  try {
    loading.value = true
    await validate()
    try {
      let isValidate = await checkTokenValidity()
      if (!isValidate) return
      let email = await changepwd({ action: 'add', passwordHash: formState.password, token, id })
      message.success('密码修改成功！')
      router.replace('/login?email='+email)
    } catch (error) {
      // message.error(error as string)
    }
  } catch (error) {
    // 表单校验不通过
  } finally {
      loading.value = false
  }
}
</script>
<style lang="less" scoped>
.exam-system {
  min-height: 100vh;
  background: url('@/assets/images/bg-login.jpg') center/100% 100% no-repeat;
  overflow: auto;
  .login-wrapper {
    float: right;
    width: 609px;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 46px 66px;
    background-color: rgba(255, 255, 255, 0.3);
    .login-logo {
      text-align: right;
    }
  }
  .login-form {
    .login-input {
      height: 40px;
      border-radius: 2px;
      padding: 0;
      padding: 0 15px;
      background: #fafafa;
    }
    .login-input2 {
      height: 40px;
      border-radius: 2px;
      padding: 0;
      padding-right: 10px;
      background: #fafafa;
    }
    .user-container {
      padding: 35px 0;
    }
    .ant-btn-primary {
      width: 100%;
    }
    .modifypwd-tabs {
      padding: 0 63px 0 6px;
      :deep(.ant-tabs-tab-btn) {
        color: rgba(0, 0, 0, 0.65);
      }
    }
    .ant-form-item {
      padding: 0 90px;
    }
    margin-top: 180px;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
<style lang="less">
.login-form {
  .ant-tabs-nav .ant-tabs-tab {
    padding: 30px 0px 19px;
    margin-right: 30px;
  }
  .ant-input-affix-wrapper .ant-input-prefix {
    border-right: 1px solid #ddd;
    margin-right: 0;
    padding: 0 13px;
  }
  .ant-input-affix-wrapper > input.ant-input {
    &:focus {
      background-color: #fff;
    }
    padding-left: 10px;
    background: #fafafa;
  }
  input::-webkit-input-placeholder {
    font-size: 14px;
  }
  .ant-form-item-explain.ant-form-item-explain-error {
    text-align: right;
    color: #ff5040;
    div {
      font-size: 12px;
    }
  }
}
</style>
