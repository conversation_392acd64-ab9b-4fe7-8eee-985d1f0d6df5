<template>
  <a-modal
    class="paper-type-modal"
    v-model:visible="visible"
    title="请选择操作类型"
    width="562px"
    style="margin-top: 100px"
    :footer="null"
    :maskClosable="false"
    @cancel="close"
  >
    <div class="type-cards">
     <div class="item" v-for="(item,index) in typeList" :key="item.value" @click="handleClick(index)">
        <svg-icon v-if="!item.checked" :name="item.name" width="80px" height="80px" color="#A8AAB4" />
        <svg-icon v-else :name="item.activeName" width="80px" height="80px" color="#3158BD" />
        <a-radio class="select-radio" v-model:checked="item.checked">{{item.label}}</a-radio>
        <div class="type-desc">
          <p>{{item.desc}}</p>
        </div>
     </div>
    </div>
    <div class="btn-group">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" @click="createPaperType" :disabled="!type">下一步</a-button>
     </div>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  paperOperateModalVisible: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['closeModal', 'getPaperOperateType'])


const typeList = ref([
  {
    name: 'link',
    activeName: 'link-active',
    label: '关联考生',
    desc: '仅已经注册的考生可以参加',
    checked: false,
    value: 'allocate'
  },
  {
    name: 'invitation',
    activeName: 'invitation',
    label: '创建考试邀请码',
    desc: '已注册或未注册的考生均可参加',
    checked: false,
    value: 'invitecode'
  }
])

const handleClick = (index:number) => {
  typeList.value.forEach(item => {
    item.checked = false
  })
  typeList.value[index].checked = true
  type.value = typeList.value[index].value
}

const close = () => {
  emits('closeModal')
}

const type = ref('')
const createPaperType = () => {
  emits('getPaperOperateType', type.value)
}

watch(
  () => props.paperOperateModalVisible,
  (val) => {
    visible.value = val
  }
)

const visible = ref(false)
</script>

<style lang="less" scoped>
.paper-type-modal {
  .type-cards {
    display: flex;
    justify-content: space-between;
    padding: 20px 50px;
    .item {
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      width: 220px;
      text-align: center;
      .select-radio {
        margin: 20px 0;
        color: #121633;
        font-size: 15px;
      }
      .type-desc p{
        font-size: 14px;
        color: #b5b5b7;
      }
    }
    
  }
  .btn-group {
    padding: 30px 0 20px;
    text-align: center;
    .ant-btn {
      width: 80px;
      font-size: 15px;
      border-radius: 3px;
      margin-right: 20px;
      &:first-child {
        border-color: #3158BD;
        color: #3158BD;
      }
    }
  }
}

</style>
<style lang="less">
.paper-type-modal .ant-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #121633;
}
</style>
