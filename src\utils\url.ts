export function getQueryParamFromURL(url, paramName) {
    try {
        const parsedUrl = new URL(url);
        const paramValue = parsedUrl.searchParams.get(paramName) ?? '';
        return paramValue;
    } catch (error) {
        console.error(`Error processing URL or retrieving the parameter '${paramName}':`, error);
        return '';
    }
}

/** 获取学生端origin */
export function getStudentOrigin() {
    let origin = location.origin.includes('192.168') ? 'https://192.168.3.16:9001' :
        location.origin.includes('preview') ? 'https://preview.exam.isrc.ac.cn' :
            'https://exam.isrc.ac.cn'

    return origin
}