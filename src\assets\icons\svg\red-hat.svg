<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>路径 12备份</title>
    <defs>
        <path d="M16,0 C18.8057575,0 21.472041,0 23.9988506,0 C28.9772117,0 32,3.97834148 32,7.99945107 C32,10.6801908 32,13.3470404 32,16 L16,0 Z" id="path-1"></path>
        <filter x="-9.4%" y="-9.4%" width="118.8%" height="118.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.205091783 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="控件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="路径-12备份" transform="translate(-16.000000, 0.000000)">
            <use fill="#F66F6A" fill-rule="evenodd" xlink:href="#path-1"></use>
            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
            <path stroke="#F66F6A" stroke-width="1" d="M23.9988506,0.5 C26.2077329,0.5 28.0082855,1.3317377 29.2898174,2.61299762 C30.7154694,4.03834704 31.5,6.01368273 31.5,7.99945107 L31.5,7.99945107 L31.5,14.7928932 L17.2071068,0.5 Z" stroke-linejoin="square"></path>
        </g>
    </g>
</svg>