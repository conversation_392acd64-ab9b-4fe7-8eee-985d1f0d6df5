<script setup lang="ts">
import { useQuestionManage, useQuestionOption } from '../composable';
const { formState, handleOptionBlur } = useQuestionManage()
</script>

<template>
        <a-form-item v-if="formState.type == 2" class="question-options">
          <a-radio-group v-model:value="formState.answer" style="max-width:100%;">
            <template v-for="(item, index) in formState.options" :key="index">
              <div class="item-option">
                <a-radio :value="item.value">
                  <span class="option-radio">{{ item.value }}</span>
                  <a-input v-model:value="item.content" class="option-content" readonly />
                </a-radio>
              </div>
            </template>
          </a-radio-group>
        </a-form-item>
</template>

<style lang="less" scoped>
@import url('../QuestionBody/scoped.less');
</style>

<style lang="less">
@import url('../QuestionBody/wrap.less');
</style>