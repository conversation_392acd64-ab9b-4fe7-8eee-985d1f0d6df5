<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/毛玻璃文件</title>
    <defs>
        <linearGradient x1="15.6851603%" y1="23.58476%" x2="56.6101371%" y2="55.8568962%" id="linearGradient-1">
            <stop stop-color="#BECAFB" offset="0%"></stop>
            <stop stop-color="#5478EE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.8764879%" y1="3.3834%" x2="29.0375459%" y2="95.9599574%" id="linearGradient-2">
            <stop stop-color="#8FC8FA" offset="0%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="27.6840656%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0" offset="76.5779026%"></stop>
            <stop stop-color="#8FC8FA" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.9549744%" y1="19.2127543%" x2="81.4036436%" y2="87.0523651%" id="linearGradient-3">
            <stop stop-color="#E6EBFC" offset="0%"></stop>
            <stop stop-color="#7BA7F5" offset="42.841508%"></stop>
            <stop stop-color="#77A5F4" offset="100%"></stop>
        </linearGradient>
        <path d="M3.03893016,3.46689442 L11.8932502,1.09438651 C13.360282,0.701296529 14.8682086,1.57189837 15.2612986,3.03893016 L18.5828097,15.4349783 C18.9758997,16.90201 18.1052978,18.4099367 16.6382661,18.8030267 L7.78394599,21.1755346 C6.3169142,21.5686246 4.80898757,20.6980227 4.41589759,19.2309909 L1.09438651,6.83494282 C0.701296529,5.36791104 1.57189837,3.85998441 3.03893016,3.46689442 Z" id="path-4"></path>
        <linearGradient x1="23.0127756%" y1="9.30569435%" x2="78.2307344%" y2="93.1157159%" id="linearGradient-5">
            <stop stop-color="#F5F7FC" offset="0%"></stop>
            <stop stop-color="#F5F7FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-6" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-7"></use>
        </pattern>
        <image id="image-7" width="16" height="20" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAUCAYAAACEYr13AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAFAAAAADB1dcyAAABoElEQVQ4EcWTS07DQBBE23ErFpESKQdgz2U4J0fgGGzZsssiwlYsGdmMFV61PRBFZhMWjDTq+VRVV/fYxdshPQ69WT8y+2TDYNYl1oqd2cBFx7oH0wVGZ8KPNhJXXpqVziT6HFnG0F0ecG1EGB4TssSYvhYoA0e3VKZJkKwacKYJOIksdyKTXSKu7GshIYwIyYXHhQ6nEZnn7GE9r4lewQYfgz3ZcJGS9dkVZ9mu+hH1zz0ZlKgio4i5DIFHbHROKYhoH9aJYR1yF03kjugnOi0Xm+pbIxxVXGYTAxk6nkCvIrJeJX32hfJ6004CawS2CG03OgbQu9UtpZBZxJZZM1XC+TyRhfP6RNVqJOla6tneme13ujI7Ii7ysRntWCN6QZwQCAgEP0Y5P+EGJ/ud28O929PzYIf3n4yZmKM3NR+FdmoWQZ3t8V23g728RnutKMpzJlxHHMxfFbWpXtXa1CnsX4OX9l5Tghqjznb8BLlRS+Cls3gF/UR6nhaxKGcJ+cuZt7xCZEfgluGnD0qYenUL31Z/ISvj6qa0F6T/F/gCzSv/TXZFV+QAAAAASUVORK5CYII="></image>
        <path d="M10.4166667,3.75 L17.5,3.75 L17.5,3.75 L23,9.25 L23,20 C23,21.6568542 21.6568542,23 20,23 L10.4166667,23 C8.75981242,23 7.41666667,21.6568542 7.41666667,20 L7.41666667,6.75 C7.41666667,5.09314575 8.75981242,3.75 10.4166667,3.75 Z" id="path-8"></path>
        <linearGradient x1="5.75918868%" y1="15.1846029%" x2="124.063702%" y2="133.088738%" id="linearGradient-9">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E4EAFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M17.5,3.75 L17.5,7.25 C17.5,8.3545695 18.3954305,9.25 19.5,9.25 L23,9.25 L23,9.25 L17.5,3.75 Z" id="path-10"></path>
        <filter x="-18.2%" y="-9.1%" width="136.4%" height="136.4%" filterUnits="objectBoundingBox" id="filter-11">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="34.3857943%" y1="34.0950041%" x2="124.063702%" y2="79.9119457%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <path d="M11.25,11.5 L15.75,11.5 C16.1642136,11.5 16.5,11.8357864 16.5,12.25 C16.5,12.6642136 16.1642136,13 15.75,13 L11.25,13 C10.8357864,13 10.5,12.6642136 10.5,12.25 C10.5,11.8357864 10.8357864,11.5 11.25,11.5 Z M11.25,16 L19.75,16 C20.1642136,16 20.5,16.3357864 20.5,16.75 C20.5,17.1642136 20.1642136,17.5 19.75,17.5 L11.25,17.5 C10.8357864,17.5 10.5,17.1642136 10.5,16.75 C10.5,16.3357864 10.8357864,16 11.25,16 Z" id="path-13"></path>
        <filter x="-10.0%" y="-8.3%" width="120.0%" height="133.3%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="0.5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.25" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.329411765   0 0 0 0 0.470588235   0 0 0 0 0.933333333  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="问卷管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="人才库-创建面试-选择已有题目-确认" transform="translate(-252.000000, -404.000000)">
            <g id="编组-2备份-2" transform="translate(240.000000, 392.000000)">
                <g id="icon/毛玻璃文件" transform="translate(12.000000, 12.000000)">
                    <rect id="_mockplus_fix_" x="0" y="0" width="24" height="24"></rect>
                    <rect id="bg" fill="#000000" opacity="0" x="0" y="0" width="24" height="24"></rect>
                    <g id="矩形备份" fill-rule="nonzero">
                        <use fill="url(#linearGradient-1)" xlink:href="#path-4"></use>
                        <path stroke="url(#linearGradient-3)" stroke-width="0.25" d="M11.9256026,1.21512724 C12.6257769,1.02751611 13.3357101,1.14146781 13.9175026,1.47736585 C14.4992951,1.81326389 14.9529468,2.37110828 15.1405579,3.07128254 L15.1405579,3.07128254 L18.462069,15.4673306 C18.6496801,16.1675049 18.5357284,16.8774382 18.1998304,17.4592306 C17.8639323,18.0410231 17.3060879,18.4946748 16.6059137,18.6822859 L16.6059137,18.6822859 L7.7515936,21.0547938 C7.05141934,21.242405 6.34148609,21.1284533 5.75969361,20.7925552 C5.17790114,20.4566572 4.72424945,19.8988128 4.53663832,19.1986385 L4.53663832,19.1986385 L1.21512724,6.80259044 C1.02751611,6.10241618 1.14146781,5.39248292 1.47736585,4.81069045 C1.81326389,4.22889797 2.37110828,3.77524628 3.07128254,3.58763515 L3.07128254,3.58763515 Z" stroke-linejoin="square" fill-opacity="0.6" fill="url(#linearGradient-2)" fill-rule="evenodd"></path>
                    </g>
                    <g id="矩形" fill-rule="nonzero" stroke-linejoin="square" stroke-width="0.5">
                        <path stroke="url(#linearGradient-5)" d="M17.3964466,4 L22.75,9.35355339 L22.75,20 C22.75,20.7593915 22.4421958,21.4468915 21.9445436,21.9445436 C21.4468915,22.4421958 20.7593915,22.75 20,22.75 L20,22.75 L10.4166667,22.75 C9.65727514,22.75 8.96977514,22.4421958 8.47212302,21.9445436 C7.9744709,21.4468915 7.66666667,20.7593915 7.66666667,20 L7.66666667,20 L7.66666667,6.75 C7.66666667,5.99060847 7.9744709,5.30310847 8.47212302,4.80545635 C8.96977514,4.30780423 9.65727514,4 10.4166667,4 L10.4166667,4 L17.3964466,4 Z" fill-opacity="0.4" fill="#8CA7FF" fill-rule="evenodd"></path>
                        <path stroke="url(#pattern-6)" d="M17.3964466,4 L22.75,9.35355339 L22.75,20 C22.75,20.7593915 22.4421958,21.4468915 21.9445436,21.9445436 C21.4468915,22.4421958 20.7593915,22.75 20,22.75 L20,22.75 L10.4166667,22.75 C9.65727514,22.75 8.96977514,22.4421958 8.47212302,21.9445436 C7.9744709,21.4468915 7.66666667,20.7593915 7.66666667,20 L7.66666667,20 L7.66666667,6.75 C7.66666667,5.99060847 7.9744709,5.30310847 8.47212302,4.80545635 C8.96977514,4.30780423 9.65727514,4 10.4166667,4 L10.4166667,4 L17.3964466,4 Z"></path>
                    </g>
                    <g id="路径-13" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                        <use fill="url(#linearGradient-9)" xlink:href="#path-10"></use>
                    </g>
                    <g id="形状结合" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                        <use fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>