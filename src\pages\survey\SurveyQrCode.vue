<template>
    <div class="share-content" ref="qrcodeContainer">
        <QrcodeVue :title="name" :value="shareUrl" :size="140"></QrcodeVue>
        <div class="share-content-right">
            <div class="step-bar">
                <span class="step-num">1</span>
                <span class="step-char">复制链接</span>
                <a-divider style="width: 75px;height: 1px;background-color: #dfe1e6; min-width: auto;margin: 0;" />
                <span class="step-num">2</span>
                <span class="step-char">发送给调研对象</span>
            </div>
            <div style="display: flex;">
                <!-- <a-input v-model:value="shareUrl" disabled style="flex: 1;" /> -->
                <p class="url-box">{{ shareUrl }}</p>
                <a-button type="primary" style="margin-left: 8px;" @click="handleCopyUrl">复制链接</a-button>
            </div>
            <div>
                <a-button @click="handleOpenUrl">直接打开</a-button>
                <a-button style="margin-left: 8px;" @click="downloadQrCode">下载二维码</a-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import useClipboard from 'vue-clipboard3'
import QrcodeVue from 'qrcode.vue'
import { message } from 'ant-design-vue'
import { getStudentOrigin } from '@/utils/url';

const props = defineProps<{
    name: string
    url: string
    loginMethod: '' | 'wx'
}>()

const wechatUrl = `${getStudentOrigin()}/#/wechatAuth?redirectUri=${encodeURIComponent(props.url)}`
// const wechatUrl = `https://*************:9001/#/wechatAuth?redirectUri=https://*************:9001/surveyPaper/793d47e6`
const shareUrl = computed(() => props.loginMethod === 'wx' ? wechatUrl : props.url)

const { toClipboard } = useClipboard()
async function handleCopyUrl() {
    try {
        await toClipboard(shareUrl.value)
        message.success('复制成功')
    } catch (error) {
        console.log(error)
    }
}

function handleOpenUrl() {
    window.open(shareUrl.value, '_blank')
}

const qrcodeContainer = ref()
function downloadQrCode() {
    const canvas = qrcodeContainer.value.querySelector("canvas")
    const link = document.createElement('a')
    link.download = `${props.name}.png`
    link.href = canvas.toDataURL("image/png")
    link.click()
}

</script>

<style lang="less" scoped>
.share-content {
    display: flex;

    .share-content-right {
        margin-left: 24px;
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .step-bar {
            display: flex;
            align-items: center;
            gap: 8px;

            .step-num {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background-color: #f5f5f5;
                display: flex;
                align-items: center;
                justify-content: center;
                color: rgba(0, 0, 0, 0.85);
            }

            .step-char {
                color: rgba(0, 0, 0, 0.45)
            }
        }

        .url-box {
            border-radius: 8px;
            background: #f5f5f5;
            height: 32px;
            line-height: 32px;
            color: rgba(0, 0, 0, 0.85);
            flex: 1;
            min-width: 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 0 24.5px 0 8.5px;
        }
    }
}
</style>