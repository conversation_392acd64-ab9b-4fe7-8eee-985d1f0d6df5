<template>
  <div class="register-main">
    <bg></bg>
    <div v-if="!hasSendedSuccess" class="content">
      <div class="corner" @click="toLogin">
        <div>登录</div>
      </div>
      <div class="content-form">
        <h1>忘记密码</h1>
        <a-form :model="formState" ref="formRef" :rules="rules">
          <a-form-item class="form-item-email" ref="email" name="email">
            <a-input
              class="form-input"
              v-model:value.trim="formState.email"
              placeholder="注册邮箱"
            />
          </a-form-item>
          <a-form-item class="form-item-code" ref="code" name="code">
            <div class="image-code-wrapper">
              <a-input
                class="form-input"
                placeholder="输入验证码"
                v-model:value.trim="formState.code"
              />
              <div class="identify">
                <img :src="varifyCodeImg" style="height: 36px" />
                <p @click="getVarifyCode">看不清？换一张</p>
              </div>
            </div>
          </a-form-item>
        </a-form>
        <a-button class="register" type="primary" :loading="loading" @click="onSubmit"
          >提交</a-button
        >
        <a-button class="back" type="primary" @click="toLogin">返回</a-button>
      </div>
    </div>
    <div v-else class="send-email-notes">
      <div class="header">忘记密码</div>
      <div class="notes-content">
        <div class="send-email-info">
          验证邮件已经发送至您的邮箱 <span>{{ formState.email }}</span>
        </div>
        <div class="tip">请接收邮件，并按邮件提示操作</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import bg from '@/views/Login2/components/bg.vue'
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { varifyCode, valideCode } from '@/api/login'
import { RuleObject } from 'ant-design-vue/es/form/interface'
import { message } from 'ant-design-vue'
import { resetpwd } from '@/api/user'
import { checkEmail } from '@/utils/validate'

const route = useRoute()

const formRef = ref()
const varifyCodeImg = ref('')
const router = useRouter()
const loading = ref(false)
const formState = reactive({
  email: route.params.email ?? '',
  code: '' //输入的图形验证码
})

// 返回
function toLogin() {
  router.push('/login')
}

// 获取图像验证码
const getVarifyCode = () => {
  varifyCode({ action: 'query' }).then((res: any) => {
    varifyCodeImg.value = res.captchaUrl
  })
}
// 验证图形验证码
const checkVarifyCode = async () => {
  const res = await valideCode({ captcha: formState.code })
  return res
}
getVarifyCode()

// 验证码
const checkCode = async (rule: RuleObject, value: string) => {
  if (value === '') {
    return Promise.reject('请输入图像验证码')
  } else {
    return Promise.resolve()
  }
}

const rules = {
  email: [{ required: true, validator: checkEmail(), trigger: 'blur' }],
  code: [{ required: true, validator: checkCode, trigger: 'blur' }]
}

const hasSendedSuccess = ref(false)

// 提交
async function onSubmit() {
  loading.value = true
  try {
    await formRef.value.validate()
    try {
      await checkVarifyCode()
    } catch (error) {
      return getVarifyCode()
    }
    try {
      await resetpwd({ action: 'query', email: formState.email })
      hasSendedSuccess.value = true
    } catch (error) {
      await getVarifyCode()
    }
  } catch (error) {
    // 表单验证不通过
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.register-main {
  position: relative;
  .content {
    width: 572px;
    height: 540px;
    background: #ffffff;
    border-radius: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    translate: -50% -50%;
    .corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 92px;
      height: 92px;
      background: url('@/assets/images/triangle.png') center/100% 100% no-repeat;
      cursor: pointer;
      div {
        position: absolute;
        top: 7.2px;
        right: 8.37px;
        width: 42px;
        height: 42px;
        font-size: 18px;
        font-family: PingFang SC, PingFang SC-6;
        font-weight: 500;
        color: #ffffff;
        line-height: 42px;
        text-align: center;
        transform: rotateZ(45deg);
      }
    }
    .content-form {
      width: 372px;
      position: absolute;
      left: 100px;
      top: 64px;
      h1 {
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 56px;
      }
      .form-item-email {
        width: 372px;
        height: 52px;
        background: #ffffff;
        position: absolute;
        top: 80px;
        left: 0;
        .form-input {
          height: 52px;
          line-height: 52px;
          font-size: 16px;
          font-family: PingFang SC, PingFang SC-5;
          font-weight: 400;
          text-align: left;
          border: 0.5px solid rgba(37, 43, 58, 0.5);
          border-radius: 8px;
        }
      }
      .form-item-code {
        width: 372px;
        height: 52px;
        background: #ffffff;
        position: absolute;
        left: 0px;
        top: 164px;
        .image-code-wrapper {
          display: flex;
          p {
            font-family: PingFang-SC-Medium;
            font-size: 12px;
            color: #999999;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
          }
        }
        .form-input {
          height: 52px;
          line-height: 52px;
          font-size: 16px;
          font-family: PingFang SC, PingFang SC-5;
          font-weight: 400;
          text-align: left;
          border: 0.5px solid rgba(37, 43, 58, 0.5);
          border-radius: 8px;
        }
      }
      .register {
        position: absolute;
        left: 0;
        top: 260px;
        width: 372px;
        height: 48px;
        border-radius: 8px;
        background-color: #5478ee;
        color: #fff;
      }
      .back {
        position: absolute;
        left: 0;
        top: 337px;
        width: 372px;
        height: 48px;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 1);
        color: #5478ee;
      }
    }
  }
  .send-email-notes {
    position: absolute;
    left: 50%;
    top: 30%;
    translate: -50% -50%;
    width: 50%;
    min-width: 480px;
    height: 220px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
    padding: 24px;
    .header {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 38px;
    }
    .notes-content {
      width: 400px;
      margin: 0 auto;
      .send-email-info {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        span {
          font-size: 14px;
          color: #6182ef;
        }
      }
      .tip {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>
