<script setup lang="ts">
import { transferStr } from '@/utils/common'
import ScoreInput from '@/views/QuestionManage/components/ScoreInput.vue'
import cloneDeep from 'lodash/cloneDeep'

interface MANUALQUESITEM {
  type: string
  id: string
  configSchema: {
    content: string
  }
  isEdit: boolean
  recordDuration: any
  correctAnswer: {
    reference: string
  }
}

const props = withDefaults(defineProps<{
  /** 手动输入的题目数据 */
  item?: Partial<MANUALQUESITEM>
}>(), {
  item: () => ({
    configSchema: {
      content: '',
    },
    recordDuration: 3,
    correctAnswer: {
      reference: '',
    },
  }),
})
const emits = defineEmits<{
  /** 保存事件 */
  (e: 'save', data: Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>): void
  /** 取消事件 */
  (e: 'cancel'): void
  /** 表单数据变化事件 */
  (e: 'change', data: Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>): void
}>()

const formData = ref<Pick<MANUALQUESITEM, 'configSchema' | 'recordDuration' | 'correctAnswer'>>({
  configSchema: {
    content: '',
  },
  recordDuration: 3,
  correctAnswer: {
    reference: '',
  },
})

watch(formData, (val) => {
  Object.keys(val).forEach((key) => {
  // eslint-disable-next-line vue/no-mutating-props
    props.item[key as keyof typeof formData.value] = val[key as keyof typeof formData.value]
  })
}, {
  deep: true,
})

watch(() => props.item.isEdit, (val) => {
  if (val) {
    formData.value.recordDuration = formData.value.recordDuration / 60
  }
})
function getContent() {
  if (!formData.value.configSchema.content) {
    return '未填写题干'
  }
  let ans = ''
  ans = transferStr(formData.value.configSchema.content)
  return `${ans}`
}
const formRef = useTemplateRef<any>('form')

const revertFormDataFunc = ref<any>(null)
function assignRevert() {
  const rawData = cloneDeep(toRaw(formData.value)) 
  revertFormDataFunc.value = () => {
    Object.keys(rawData).forEach((key) => {
      if (key === 'isEdit') 
        return
      formData.value[key as keyof typeof formData.value] = rawData[key as keyof typeof rawData]
    })
  }
}

assignRevert()

function swtichEdit() {
  // eslint-disable-next-line vue/no-mutating-props
  props.item.isEdit = !props.item.isEdit
}

function validate() {
  return formRef.value?.validate()
}

async function save() {
  try {
    await validate()
    formData.value.recordDuration = formData.value.recordDuration * 60
    emits('save', { ...formData.value })
    swtichEdit()
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

function cancel() {
  // 重置表单到初始状态
  revertFormDataFunc.value()
  formData.value.recordDuration = formData.value.recordDuration * 60
  swtichEdit()
}

function resetForm() {
  formData.value.configSchema.content = ''
  formData.value.recordDuration = 3
  formData.value.correctAnswer.reference = ''
}
const anchorRef = useTemplateRef('anchor')
defineExpose({
  validate,
  save,
  cancel,
  resetForm,
  formData,
  item: props.item,
  $el: anchorRef,
})
</script>

<template>
  <div v-show="item.isEdit" ref="anchor" class="pt-[16px] pr-[24px] pb-[0] w-full">
    <el-form ref="form" :model="formData" label-width="80">
      <el-form-item label="题干内容" prop="configSchema.content" :rules="[{ required: true, message: '请输入题干内容', trigger: 'blur' }]">
        <el-input
          v-model="formData.configSchema.content"
          maxlength="1000"
          type="textarea"
          resize="none"
          :rows="3"
          placeholder="请输入题干内容"
        />
      </el-form-item>
      <el-form-item label="建议时长" prop="recordDuration">
        <ScoreInput
          v-model="formData.recordDuration"
          @get-score="(val) => {
            formData.recordDuration = val
          }"
        />
      </el-form-item>
      <el-form-item label="参考答案" prop="correctAnswer.reference">
        <el-input
          v-model="formData.correctAnswer.reference"
          maxlength="100"
          type="textarea"
          resize="none"
          :rows="3"
          placeholder="请输入参考答案"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="save">
          保存
        </el-button>
        <el-button size="small" @click="cancel">
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>

  <div v-show="!item.isEdit" class="test-question-display relative pl-[30px]">
    <div class="absolute left-0">
      <img src="@/assets/icons/svg/question_icon.svg" class="mr-[12px]">
    </div>

    <div class="test-question-content">
      <div class="test-question-stem">
        <div class="flex flex-nowrap items-start w-[90%]">
          <div class="body" :class="[formData.configSchema.content ? '' : 'text-[#ff4d4f]']" v-html="getContent()" />
        </div>
      </div>
      <template v-if="true">
        <div>
          <div class="test-question-scorebasis flex flex-nowrap justify-between w-[85%]">
            <div class="flex flex-nowrap">
              <span class="label">建议时长</span>
              <div
                class="scorebais-content"

                v-html="`${formData.recordDuration / 60}分钟`"
              />
            </div>
          </div>
          <div class="test-question-scorebasis">    
            <span class="label">参考答案</span>
            <div
              class="scorebais-content"
              v-html="transferStr(formData.correctAnswer.reference || '未填写')"
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped  lang="less">
.test-question-display {
  padding: 6px;
  padding-left: 30px;
  border-radius: 8px;
  width:100%;
  cursor: pointer;
  .btn-wrap {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      background: #f5f5f5;
    }
  }
}

.transparent {
  border-color: transparent !important;
  background-color: transparent !important;
}
.test-question-stem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  white-space: pre-wrap;
  &.deleted {
    margin-bottom: 0;
  }

  &.tool-bar {
    display: flex;
    justify-content: space-between;
  }
  .type {
    align-self: flex-start;
    flex-shrink: 0;
    width: 52px;
    text-align: center;
    line-height: 20px;
    background: #f1f4fe;
    border: 1px solid #5478ee;
    border-radius: 4px;
    font-size: 12px;
    color: #5478ee;
    margin-right: 8px;
  }
  :deep(.body) {
    font-size: 16px;
    line-height: 22px;
    // word-break: break-all;
    text-align: justify;
//     flex: 1;
    min-width: 0;
    // overflow: auto;
    overflow-x: auto;
    overflow-y: hidden;
    &.deleted {
      text-decoration: line-through;
    }
    p {
      font-size: 16px;
      word-break: break-word;
      img {
        max-width: 100%;
        cursor: zoom-in!important;
      }
    }
  }
  .score {
    align-self: flex-start;
    flex-shrink: 0;
    width: 41px;
    text-align: center;
    line-height: 20px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 8px;
    margin-top: 1px;
  }
}
.out-answer {
  color: rgba(0, 0, 0, 0.45);
  padding-left: 50px;
  margin-bottom: 10px;
  .correct-text {
    color: #52c41a;
  }
  .error-text {
    color: #de504e;
  }
}
.test-question-content {
  .test-question-options {
    .item-option {
      display: flex;
      align-items: center;
      .tag {
        color: #000;
        font-weight: bolder;
      }
      + .item-option {
        margin-top: 16px;
      }
      :deep(.option-content) {
        overflow-y: hidden  ;
        p {
          word-wrap: break-word;
          img {
            max-width: 100%;
            cursor: zoom-in!important;
          }
        }
      }
    }
    .option-btn {
      margin-right: 16px;
    }
    .correct-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .error-tag {
      background: #5478ee;
      border: 1px solid #5478ee;
      box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset,
        -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
      color: #fff;
    }
    .correct-answer-tag {
      font-size: 12px;
      color: #2f8c00;
      background: #f3f7f1;
      border-radius: 4px;
      padding: 0 2px;
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
  .test-question-scorebasis {
    display: flex;
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
      flex-shrink: 0;
    }

    :deep(.scorebais-content) {
      p {
        word-wrap: break-word;
        img {
          max-width: 100%;
          cursor: zoom-in!important;
        }
      }
    }
  }
  .test-question-points {
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #626262;
    margin-bottom: 8px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-items: baseline;
    .label {
      color: #626262;
    }
    
    .points-content {
      margin-left: 10px;
      flex: 1;
      min-width: 0;
      .item {
        display: flex;
        .scorePointIndex {
          flex: none;
          width: 18px;
          height: 28px;
          font-size: 14px;
          color: #121633;
          line-height: 28px;
        }
        .scoPointDetail {
          margin-bottom: 8px;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .scorePoint {
            margin-left: 8px;
            margin-bottom: 8px;
            padding: 1px 4px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            line-height: 24px;
          }
        }
        .score {
          height: 28px;
          margin-left: 8px;
          padding: 0 6px;
          background: #f0f0f0;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
          line-height: 28px;
          white-space: nowrap;
        }
      }
    }
    > div {
      margin-left: 52px;
    }
  }
  .test-question-testcase {
    .title {
      padding: 0 0 8px;
      font-size: 14px;
      font-weight: 600;
    }
    .content {
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 16px;

      .item {
        font-size: 14px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
      .item-text {
        font-weight: 600;
        padding-right: 6px;
      }
    }
  }
  .test-question-difficulty {
    margin-bottom: 16px;
    .label {
      color: #626262;
      margin-right: 32px;
    }
  }
  .test-question-sequence {
    margin-bottom: 16px;
    .item {
      display: flex;
      min-height: 34px;
      padding-left: 8px;
      align-items: center;
      border: 1px solid #dad8d8;
      color: #121633;
      border-radius: 2px;
    }
  }
  .sort-option-wrapper {
    margin-bottom: 12px;
    width: 100%;
    display: flex;
    align-items: center;
    .item {
      flex: 1;
      margin-left: 10px;
    }
  }
}
.test-question-tag {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  .item {
    text-align: center;
    line-height: 22px;
    padding: 0 8px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.disableKws {
  margin-top: 10px;
  .label {
    color: #626262;
    margin-right: 32px;
  }
  .kw-list {
    margin-top: 8px;
    padding-left: 16px;
    .kw-item {
      display: flex;
      .language {
        flex: none;
        width: 80px;
        height: 24px;
        margin-right: 8px;
        color: #626262;
        line-height: 24px;
      }
      .kw-wrap {
        line-height: 24px;
        display: flex;
        flex-wrap: wrap;
        color: #626262;
        .kw {
          margin-right: 4px;
          margin-bottom: 4px;
          padding: 0 4px;
          background: #f5f5f5;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
  .value {
    padding-left: 16px;
  }
}
.test-question-answer {
  padding: 16px 24px 16px 48px;
  border-radius: 8px;
  margin-top: 20px;
  .label {
    color: #626262;
    font-size: 12px;
    margin-right: 32px;
    white-space: nowrap;
  }
  .correct-answer,
  .stu-answer,
  .get-score {
    margin-bottom: 8px;
    font-size: 12px;
    display: flex;
    &:last-child {
      margin-bottom: 0;
    }
    .content {
      font-size: 12px;
      word-break: break-word;
    }
  }
  .score-record {
    font-size: 12px;
    color: rgba(0,0,0,0.45);
  }
  .answer-imgs {
    &:has(.item) {
      margin-top: 16px;
    }
    display: flex;
    :deep(.item) {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 8px;
      border-radius: 5px;
      overflow: hidden;
      .ant-image {
        height: 100%;
        width: 100%;
      }
      .ant-image-img {
        height: 100%;
        border-radius: 5px;
        object-fit: cover;
      }
    }
  }
  
  &.error {
    border: 1px solid #ff4d4f;
    background: url('@/assets/images/exam/error.png') no-repeat 95%, #fcf0f0;
  }
  &.part {
    border: 1px solid #faad14;
    background: url('@/assets/images/exam/part.png') no-repeat 95%, #faf4ee;
  }
  &.correct {
    background: #f6ffed;
    border: 1px solid #52c41a;
  }
}

.proofread-record {
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  position: relative;
  left: -3px;
}

.proofread-reason {
  display: flex;
  margin-top: 16px;

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 32px;
  }
}
</style>