<!-- 自适应宽度的选项排版 -->
<template>
    <ul class="options-box" ref="containerRef">
        <li class="option-item" v-for="item in options" :key="item.value">
            <span class="option-btn">{{ item.value }}.</span>
            <span class="option-content" v-html="renderFn(item.content)"  @click="$event => findImageAndPreview($event)"></span>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { findImageAndPreview } from '@/utils/common'

const props = defineProps<{
    options?: {
        value: string
        content: string
    }[]
    renderFn: (content: string) => string
}>();

const containerRef = ref()
const isOnline = ref(true) // 默认一行展示一个
const optionItemWidth = computed(() => isOnline.value ? '100%' : '50%')
// 计算每个option-item的宽度, 如果有任意一个超过了父元素宽度的50%则一行展示一个,否则一行展示2个
function layoutOptions() {
    const container = containerRef.value
    if (!container) return
    const containerWidth = container.offsetWidth
    const children = container.children
    isOnline.value = Array.from(children).some((child) => {
        // 获取child下类名是option-content的节点的宽度
        /* @ts-ignore */
        return (child.lastElementChild.offsetWidth + child.lastElementChild.offsetLeft) > containerWidth / 2
    })
}

onMounted(() => {
    layoutOptions()
});

</script>

<style lang="less" scoped>
.options-box {
    .option-item {
        position: relative;
        display: inline-block;
        font-size: 14px;
        margin-bottom: 10px;
        width: v-bind(optionItemWidth);

        .option-btn {
            margin-right: 10px;
        }

        :deep(.option-content) {
            word-wrap: break-word;
            // display: inline-block;
            word-break: break-all;
            p {
                word-wrap: break-word;
                word-break: break-all;
                display: inline-block;
                img {
                    max-width: 100%;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>