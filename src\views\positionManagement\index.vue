<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'

// 搜索表单数据
const recruitmentType = ref('')
const creator = ref('')
const createTime = ref<[string, string] | undefined>(['', ''])
const positionName = ref('')
const showMore = ref(false)
function showMoreFilter() {
  showMore.value = !showMore.value
}
</script>

<template>
  <div class="resume-manage-container">
    <h3 class="resume-manage-title">
      人才库
    </h3>
    <div class="resume-manage">
      <div class="p-[24px] bg-white shadow-sm border-b border-gray-200 mb-6 min-h-[80px] rounded-[8px] w-full flex flex-wrap items-center">
        <!-- 职位名称搜索 -->
        <div class="flex w-full items-center min-w-0 justify-between">
          <el-input
            v-model="positionName"
            placeholder="请输入岗位名称"
            class="max-w-xs"
            clearable
          >
            <template #suffix>
              <el-icon class="text-gray-400 cursor-pointer hover:text-gray-600">
                <Search />
              </el-icon>
            </template>
          </el-input>
          <!-- 更多条件按钮 -->
          <div class="flex items-center gap-2">
            <div class="filter-btn filter-more" @click="showMoreFilter">
              <img src="@/assets/icons/svg/filter2.svg" alt="">
              <span style="margin-left: 4px;">更多条件</span>
            </div>
          </div>
        </div> 
        <div v-show="showMore" class="w-full mt-[16px]">
          <div class="flex  flex-nowrap items-center w-full">
            <!-- 招聘类型下拉 -->
            <div class="flex flex-1 items-center">
              <span class="text-sm text-gray-600 whitespace-nowrap mr-[16px]">招聘类型</span>
              <el-select
                v-model="recruitmentType"
                placeholder="全部"
                class="w-[20%]"
                clearable
                style="width: 80%;"
              >
                <el-option label="全部" value="" />
                <el-option label="社招全职" value="social_fulltime" />
                <el-option label="应届校园招聘" value="campus_graduate" />
                <el-option label="实习生招聘" value="internship" />
                <el-option label="兼职招聘" value="part_time" />
              </el-select>
            </div>

            <!-- 创建人下拉 -->
            <div class="flex flex-1 items-center">
              <span class="text-sm text-gray-600 whitespace-nowrap mr-[16px]">创建人</span>
              <el-select
                v-model="creator"
                placeholder="全部"
                clearable
                style="width: 80%;"
              >
                <el-option label="全部" value="" />
                <el-option label="本人" value="self" />
                <el-option label="其他" value="others" />
              </el-select>
            </div>

            <!-- 创建时间选择 -->
            <div class="flex flex-1 items-center">
              <span class="text-sm text-gray-600 whitespace-nowrap mr-[16px]">创建时间</span>
              <a-range-picker v-model:value="createTime" :placeholder="['最早开始时间', '最晚开始时间']" value-format="YYYY-MM-DD">
                <template #suffixIcon>
                  <img width="16" height="16" src="@/assets/images/paper/time.png" alt="">
                </template>
              </a-range-picker>
            </div>
          </div>
        </div>
      </div>
      <div class="p-[24px] bg-white shadow-sm border-b border-gray-200 mb-6 min-h-[80px] rounded-[8px] w-full flex flex-wrap items-center">
        <a-tag color="#f1f4fe" style="color:#5478ee" class="mr-[12px]">
          社招
        </a-tag>
        <div class="font-bold text-[18px]">
          Java前端工程师
        </div>
        <div class="w-[50px] flex flex-wrap">
          <div class="w-full justify-center">
            1209
          </div>
          <div class="text-[12px] text-[rgba(0,0,0,0.45)]">
            投递数量
          </div>
        </div>
        <div>
          <span>张三三</span>
          <span>2025-08-08 10:09</span>
        </div>
        <div>
          <span>编辑</span>
          <span>删除</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.filter-btn {
        width: 108px;
        height: 32px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.filter-more {
            .filter-number {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #FF4D4F;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
            }
        }

}

.rotate {
  transform: rotate(-90deg);
}

.resume-manage-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px 20px 20px;
}

.resume-manage-title {
  line-height: 48px;
  font-size: 14px;
  font-weight: 600;
}

.resume-manage {
  flex: 1;
  min-height: 0;
  border-radius: 8px;
//   box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
//   padding: 10px 24px;
//   background-color: #fff;
  display: flex;
  flex-direction: column;

  .resume-manage-header {
    margin: 8px 0;
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      gap: 16px;
    }

    .header-right {
      display: flex;
      align-items: center;
      font-size: 14px;
      gap: 16px;

      span {
        width: 88px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 8px;
        font-size: 14px;
        margin-left: 8px;
        cursor: pointer;
      }

      .add-btn {
        background: #5478ee;
        color: #fff;
      }

      .import-btn,
      .del-btn {
        // border: 1px solid rgba(0, 0, 0, 0.15);
        background: #5478ee;
        color: #fff;
      }
    }
  }

  .table-bar {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ant-checkbox-wrapper {
      font-size: 15px;
    }

    .show-columns {
      font-size: 15px;

      span {
        margin-right: 10px;
      }
    }
  }

  .resumes {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
  }

  .resume-card {
    width: 360px;
    margin-bottom: 20px;
    margin-right: 20px;
    overflow: hidden;
  }

  .resume-content {
    display: flex;

    .avatar-img {
      width: 1rem;
      height: 1rem;
    }

    .resume-info {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;

      span {
        line-height: 1.5;
      }
    }
  }

  .dynamic-column {
    height: 60px;
    line-height: 62px;
    padding-left: 15px;
  }
}
</style>
