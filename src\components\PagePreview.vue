<template>
    <transition name="fade">
        <div class="page-preview" v-if="visible">
            <div class="page-preview-header">
                <span class="preview-title">{{ title }}</span>
                <div class="mode-switch" v-if="modes.length > 1">
                    <div class="mode-item" :class="{ active: mode === 'pc' }" @click="handleModeChange('pc')">
                        <desktop-outlined />
                    </div>
                    <div class="mode-item" :class="{ active: mode === 'mobile' }" @click="handleModeChange('mobile')">
                        <mobile-outlined />
                    </div>
                </div>

                <close-outlined style="font-size: 16px;" @click="emits('update:visible', false)" />

                <!-- 传送门 -->
                <div class="btns-wrapper" id="page-preview-teleport"></div>

            </div>
            <div class="page-preview-content">
                <div id="preview-frame" :class="`${mode}-frame`">
                    <span v-if="mode === 'pc'" class="pc-dots">
                        <span class="pc-dot"></span>
                        <span class="pc-dot"></span>
                        <span class="pc-dot"></span>
                    </span>
                    <div v-else class="mobile-line"></div>
                    <div class="frame-content">
                        <slot></slot>
                    </div>
                    <a-back-top :target="backToTopContainer" :visibilityHeight="120"
                        style="position: absolute;right: -72px; bottom: 50px;width: auto;height: auto;">
                        <img src="@/assets/images/back_to_top.png" alt="" />
                    </a-back-top>
                </div>
            </div>
        </div>
    </transition>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CloseOutlined, DesktopOutlined, MobileOutlined } from '@ant-design/icons-vue'

const props = withDefaults(defineProps<{
    visible?: boolean
    title?: string
    modes?: ['mobile', 'pc'?] | ['pc', 'mobile'?]
    /** 需要监听其滚动事件的元素，用于展示回到顶部按钮 */
    backToTopContainer?: () => Element | null
}>(), {
    visible: false,
    title: '预览',
    modes: () => (['pc', 'mobile']),
    backToTopContainer: () => document.querySelector('#preview-frame')
})

const emits = defineEmits<{
    (e: 'update:visible', val: boolean): void
}>()


const mode = ref(props.modes[0])
function handleModeChange(m: 'pc' | 'mobile') {
    mode.value = m

    // 将容器滚动到顶部
    let el = props.backToTopContainer()
    el?.scroll({ top: 0 })
}

</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.page-preview {
    width: 100vw;
    min-width: 1440px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    background: linear-gradient(230deg, #e9f4fb 8%, #f3f9fc 50%, #efeef8 97%);

    .page-preview-header {
        height: 56px;
        background: #ffffff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 32px;
        color: rgba(0, 0, 0, 0.85);

        .preview-title {
            width: 16px;
            text-wrap: nowrap;
            font-size: 20px;
            font-weight: bold;
        }

        .mode-switch {
            display: flex;
            gap: 16px;

            .mode-item {
                width: 32px;
                height: 32px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 8px;
                cursor: pointer;
                transition: all ease .2s;

                .anticon {
                    transition: all ease .2s;
                }

                &.active {
                    background-color: #f1f4fe;

                    .anticon {
                        color: #5478ee;
                    }
                }
            }
        }

        .btns-wrapper {
            position: absolute;
            right: 186px;
        }
    }

    .page-preview-content {
        flex: 1;
        min-height: 0;
        padding-top: 20px;
        padding-bottom: 20px;
        position: relative;
        height: auto;
        overflow: auto;

        #preview-frame {
            margin: auto;
            position: relative;

            .frame-content {
                position: absolute;
                overflow: auto;
            }
        }

        .pc-frame {
            width: 1000px;
            height: 100%;
            background: linear-gradient(43deg, #def0ff 0%, #e4eaff 53%, #e1e0ff 100%);
            border: 3px solid #ffffff;
            border-radius: 24px;

            .pc-dots {
                position: absolute;
                left: 24px;
                top: 24px;

                .pc-dot {
                    width: 16px;
                    height: 16px;
                    display: inline-block;
                    border-radius: 50%;
                    background: linear-gradient(180deg, #5570e1, rgba(85, 112, 225, 0.20));
                    margin-right: 16px;

                    &:first-child {
                        background: linear-gradient(180deg, #9ae4bd, rgba(154, 228, 189, 0.20));
                    }
                }
            }

            .frame-content {
                border-radius: 8px;
                top: 54px;
                left: 36px;
                right: 36px;
                bottom: 40px;
                background-color: #fff;
            }
        }

        .mobile-frame {
            width: 375px;
            height: 770px;
            border-radius: 32px;
            background: #cdd9ff;

            .mobile-line {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: 6px;
                width: 71px;
                height: 6px;
                background: #2a2a2a;
                border-bottom-left-radius: 16px;
                border-bottom-right-radius: 16px;
            }

            .frame-content {
                border-radius: 24px;
                top: 20px;
                left: 20px;
                right: 20px;
                bottom: 20px;
                background-color: #fff;
            }
        }
    }
}
</style>