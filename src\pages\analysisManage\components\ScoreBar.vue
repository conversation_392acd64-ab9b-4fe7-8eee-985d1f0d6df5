<!-- 分数段统计柱状图 -->
<template>
  <div class="chart" ref="chartRef"></div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ECharts, init } from 'echarts'

const props = defineProps<{
  data: any[]
  curveData: any[]
}>()

// 图表绘制
const chartRef = ref()
const chart = ref<ECharts>()
function draw() {
  chart.value = init(chartRef.value)
  chart.value?.setOption({
    grid: {
      top: 60,
      left: 50,
      right: 10,
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          show: true,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(0,0,0,0.45)',
          fontSize: 12,
          align: 'center',
          lineHeight: 14,
        },
      },
      {
        type: 'category',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        }
      },
    ],
    yAxis: [
      {
        type: 'value',
        // minInterval: 1,
        name: '人数',
        nameTextStyle: {
          height: 17,
          align: 'right',
          padding: [0, 8, 6, 0],
          fontSize: 12,
          color: 'rgba(0,0,0,0.45)',
          lineHeight: 17,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0,0,0,0.15)',
          }
        },
        axisLabel: {
          color: 'rgba(0,0,0,0.45)',
          fontSize: 12,
          align: 'right',
          lineHeight: 14,
        }
      },
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        }
      },
    ],
    series: [
      {
        type: 'bar',
        datasetIndex: 0,
        color: 'rgba(84,120,238,0.85)',
        label: {
          show: true,
          position: 'top',
          formatter: (value: any) => value.value.value > 0 ? value.value.value : '',
        },
      },
      {
        type: 'line',
        smooth: true,
        xAxisIndex: 1,
        yAxisIndex: 1,
        datasetIndex: 1,
        symbol: 'none',
        lineStyle: {
          color: 'rgba(250,191,69,1)',
          type: 'dotted',
        },
        seriesLayoutBy: 'row',
      }
    ]
  })
  window.addEventListener('resize', resizeChart)
}

function resizeChart() {
  chart.value?.resize()
}

onMounted(async () => {
  draw()
})

watch([() => props.data, () => props.curveData, chart], ([value, curveValue, myChart]) => {
  if (!myChart) return
  myChart.setOption({
    dataset: [
      {
        source: value,
      },
      {
        source: curveValue,
      }
    ]
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
})

</script>

<style lang="less" scoped>
  .chart {
    height: calc(100% - 30px);
    min-height: 300px;
  }
</style>