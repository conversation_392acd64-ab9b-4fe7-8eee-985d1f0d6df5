<template>
    <div class="monitor-records">
        <div class="common-page-title">
            <span @click="router.go(-2)">试卷管理 / </span>
            <span @click="router.go(-1)">查看成绩《{{ route.query.papername }}》/ </span>
            <span>查看监控</span>
        </div>
        <div class="main">
            <div class="top">
                <div style="display: flex;align-items: center;">
                    <span class="user-name" :title="monitorInfo.studentName">{{ monitorInfo.studentName }}</span>
                    <span class="stu-tag">{{ monitorInfo.sexName }}</span>
                    <span class="stu-tag">{{ monitorInfo.age }}</span>
                    <span class="stu-desc" style="margin-left: 28px;">{{ monitorInfo.graduated_school }}</span>
                    <a-divider type="vertical"></a-divider>
                    <span class="stu-desc">{{ monitorInfo.heducation }}</span>
                    <a-divider type="vertical"></a-divider>
                    <span class="stu-desc">{{ monitorInfo.idCardNum }}</span>
                </div>
                <a-button :class="{ disabled: !monitorInfo.idCardImg }" @click="showIdCard">证件比对</a-button>
            </div>
            <a-alert class="reserve-info" type="error" show-icon v-if="!isReserved && monitorInfo?.clearMonitorTime">
                <template #icon>
                    <InfoCircleFilled style="color: #FF4D4F;" />
                </template>
                <template #message>
                    <span v-if="moment(monitorInfo.clearMonitorTime).isBefore(moment())">该考生的监控画面已清除</span>
                    <template v-else>
                        该考生的监控画面将在<span>{{ monitorInfo.clearMonitorTime }}</span>清除，如需保留，请点击<span
                            style="color: #2cb5f2;text-decoration: underline;cursor: pointer;"
                            @click="handleReserve(true)">保留此监控</span>
                    </template>
                </template>
            </a-alert>
            <a-alert class="reserve-info" type="info" show-icon v-if="isReserved">
                <template #icon>
                    <CheckCircleFilled style="color: #52c41a;" />
                </template>
                <template #message>
                    该考生的监控画面已保留，如不再需要，请点击<span style="color: #2cb5f2;text-decoration: underline;cursor: pointer;"
                        @click="handleReserve(false)">取消保留</span>
                </template>
            </a-alert>
            <div class="records-content">
                <div class="records-content-left" v-if="monitorModel?.exam_model === 3">
                    <span class="my-btn" :class="{ active: !activeQId }" style="width: 88px;margin-bottom: 16px;"
                        @click="setMonitorImgs()">整场监控</span>
                    <div class="section-box-wrapper">
                        <div class="section-box" v-for="section in monitorModel?.images[0].questions_groups">
                            <template v-if="section.children?.length">
                                <div class="title">{{ section.title }}</div>
                                <div class="question-number-btn-wrapper">
                                    <span class="question-number-btn my-btn" :class="{ active: activeQId === q.id, disabled: q.min_frame === q.max_frame }"
                                        v-for="q in section.children" @click="(q.min_frame !== q.max_frame) && setMonitorImgs(q.id)">{{ q.number }}</span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                <a-tabs class="records-content-right" v-model:activeKey="activeKey" type="card">
                    <template v-for="item in monitorImgs">
                        <a-tab-pane v-if="item.isShow" :key="item.value">
                            <template #tab>
                                <img v-if="item.value == 1" src="@/assets/icons/svg/icon_monitor_pc.svg" alt="">
                                <img v-else-if="item.value == 2" src="@/assets/icons/svg/icon_monitor_phone.svg" alt="">
                                <span style="line-height: 20px;margin-left: 8px;color: rgba(0,0,0,0.85);">{{ item.label }}</span>
                            </template>
                            <PicturePlayer :imgs="item.imgs"></PicturePlayer>
                        </a-tab-pane>
                    </template>
                </a-tabs>
            </div>

        </div>


        <!-- 身份证卡片弹出层 -->
        <div ref="idcardRef" :style="style" id="idcard-modal" v-show="isIdCardShow">
            <div ref="idcardHeaderRef" class="idcard-modal-header">
                <span>证件比对</span>
                <span>
                    <ZoomInOutlined @click="myCanvas?.zoomIn()" />
                    <ZoomOutOutlined @click="myCanvas?.zoomOut()" />
                    <CloseOutlined @click="isIdCardShow = false" />
                </span>
            </div>
            <div class="idcard-modal-content">
                <canvas id="canvas"></canvas>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, reactive, createVNode } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MonitorModel, candsinfo, getPaperDetail, monitorimgs, reserveMonitor } from '@/api/admin/paperManage'
import { Modal, message } from 'ant-design-vue'
import { ZoomInOutlined, ZoomOutOutlined, CloseOutlined, InfoCircleOutlined, CheckCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue'
import { useDraggable } from '@vueuse/core'
import moment from 'moment'
import Scene from '@/utils/scene'
import { setFbodyQuestionNumber } from '@/utils'
import PicturePlayer from './components/PicturePlayer.vue'


const route = useRoute()
const router = useRouter()

// 获取监控信息
const monitorInfo = ref({
    age: 0,
    dept_name: '',
    email: '',
    facecheck_img: [],
    graduated_school: '',
    heducation: '',
    idCardNum: '',
    idCardImg: '',
    clearMonitorTime: '',
    phone: '',
    reserve: true,
    sexName: '',
    stupid: '',
    studentName: ''
})
const loading = ref(true)
async function getMonitorInfo() {
    try {
        loading.value = true
        const cand_status = await candsinfo({ id: route.query.studentid })
        monitorInfo.value = cand_status
        isReserved.value = cand_status.reserveMonitor == 1
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}
getMonitorInfo()


// 证件比对
const isIdCardShow = ref(false)
async function showIdCard() {
    if (!isIdCardShow.value && !monitorInfo.value.idCardImg) {
        return message.warning('该考生未上传身份证照片')
    }
    isIdCardShow.value = !isIdCardShow.value
    await nextTick()
    initCanvas(monitorInfo.value.idCardImg)
}

const idcardRef = ref<HTMLElement | null>(null)
const idcardHeaderRef = ref<HTMLElement | null>(null)
const mainWidth = ref(1680)
const mainHeight = ref(795)
const { x, y, style } = useDraggable(idcardRef, {
    initialValue: { x: mainWidth.value / 2, y: mainHeight.value / 2 - 100 },
    handle: idcardHeaderRef
})

let myCanvas = ref<Scene>()
function initCanvas(imgSrc: string) {
    let canvasEle = document.querySelector('.idcard-modal-content #canvas') as HTMLElement
    let canvasWidth = canvasEle.offsetWidth,
        canvasHeight = canvasEle.offsetHeight
    myCanvas.value = new Scene('canvas', {
        imgSrc,
        width: canvasWidth,
        height: canvasHeight
    })
}


// 保留监控
const isReserved = ref(false)
async function handleReserve(reserve: boolean) {
    try {
        if (!reserve && moment(monitorInfo.value.clearMonitorTime).isBefore(moment())) {
            Modal.confirm({
                title: '提醒',
                icon: () => createVNode(InfoCircleFilled),
                content: '取消保留后将清空所有监控图片，是否确认？',
                onOk: () => {
                    reserveSubmit(reserve)
                    monitorImgs.forEach(item => {
                        item.imgs = []
                        item.isShow = false
                    })
                }
            })
        } else {
            reserveSubmit(reserve)
        }
    } catch (error) {

    }
}
async function reserveSubmit(reserve: boolean) {
    await reserveMonitor({
        reserveMonitor: reserve ? 1 : 0,
        id: route.query.id,
        // paper: route.query.paperid as string,
        // student: route.query.studentid as string,   
    })
    message.success('操作成功')
    isReserved.value = reserve
}


// 获取监控照片
const monitorImgs = reactive<{
    label: string
    value: 1 | 2 | 3
    imgs: string[]
    isShow: boolean
}[]>([
    { label: '电脑摄像头', value: 1, imgs: [], isShow: false },
    { label: '手机摄像头', value: 2, imgs: [], isShow: false }
])
const activeKey = ref(1)
const monitorModel = ref<MonitorModel>()
async function getMonitorImgs() {
    let res = await monitorimgs({
        id: route.query.studentid,
    })

    // 设置题号
    res.images.forEach(item => setFbodyQuestionNumber(item.questions_groups))

    monitorImgs.forEach(item => {
        let index = res.images.findIndex(i => i.type === item.value)
        if (index !== -1) {
            item.isShow = true
        }
    })
    activeKey.value = monitorImgs.find(i => i.isShow)?.value!
    monitorModel.value = res

    // 默认是正常监控
    setMonitorImgs()
}
getMonitorImgs()

// 生成进度条图片数组
function generateFrameUrlList(imageData: {
    frames: string[]
    image_prefix: string
    min_frame: number
    max_frame: number
}) {
    let frameUrlList: string[] = [];

    if (imageData) {
        const { frames, min_frame, max_frame } = imageData;

        // 如果frames数组为空，则直接返回空列表
        if (frames.length === 0) {
            return frameUrlList;
        }

        let frameIndex = 0; // 设置游标为frames数组的起始位置
        let currentFrameNumber = parseInt(frames[frameIndex].split('.')[0], 10); // 初始化当前帧编号

        // 修复3-11, 但frames从1开始
        // while (currentFrameNumber && frameIndex < frames.length - 1 && currentFrameNumber < min_frame) {
        while (frameIndex < frames.length - 1 && currentFrameNumber < min_frame) {
            frameIndex++
            currentFrameNumber = parseInt(frames[frameIndex].split('.')[0], 10)
        }

        for (let frameNumber = min_frame; frameNumber <= max_frame; frameNumber++) {
            if (frameNumber === currentFrameNumber) {
                // 如果当前游标指向的帧的编号与遍历到的编号相等，创建URL
                frameUrlList.push(imageData.image_prefix + frames[frameIndex])
                frameIndex++; // 移动游标到下一个帧
                if (frameIndex < frames.length) { // 确保frameIndex不会超出frames数组的界限
                    currentFrameNumber = parseInt(frames[frameIndex].split('.')[0], 10); // 更新当前帧编号
                }
            } else {
                // 如果不匹配，使用默认图像
                frameUrlList.push('')
            }
        }
    }
    return frameUrlList;
}

/** 左侧题目active状态的id，如果没有则是整场监控 */
const activeQId = ref('')
/** 
 * @description 处理左侧按钮点击事件
 * @param id 题目id
 */
function setMonitorImgs(id?: string) {
    monitorImgs.forEach(item => {
        let model = monitorModel.value?.images.find(i => i.type === item.value)
        let frames = model?.frames
        // 去除帧号为负的部分
        frames = frames?.filter(i => !i?.startsWith('-'))

        if (!frames?.length) return

        if (!id) {
            // 整场监控
            activeQId.value = ''

            // 计算整卷时最小帧和最大帧
            let min_frame = 10000, max_frame = 0
            if (model?.questions_groups) {
                // 非单题限时模式
                min_frame = parseInt(model.frames[0].split('.')[0], 10)
                max_frame = parseInt(model.frames[model.frames.length - 1].split('.')[0], 10)
            } else {
                // 单题限时模式
                model?.questions_groups?.forEach(i => {
                    i.children.forEach(q => {
                        min_frame = Math.min(q.min_frame, min_frame)
                        max_frame = Math.max(q.max_frame, max_frame)
                    })
                })
            }

            item.imgs = generateFrameUrlList({
                frames,
                image_prefix: model?.image_prefix!,
                min_frame,
                max_frame
            })
        } else {
            // 按题查看监控
            activeQId.value = id

            let qModel = model?.questions_groups?.flatMap(i => i.children).find(i => i.id === id)
            if (!qModel) return message.error('没有该题监控')
            item.imgs = generateFrameUrlList({
                frames: frames!,
                image_prefix: model?.image_prefix!,
                min_frame: qModel?.min_frame,
                max_frame: qModel?.max_frame
            })
        }
    })
}

onMounted(() => {
    // dom加载完成后，给mainWidth赋值，用来初始化身份证卡片的位置
    let mainEle = document.querySelector('.monitor-records .main') as HTMLElement
    mainWidth.value = mainEle.offsetWidth
})

</script>

<style lang="less" scoped>
.monitor-records {
    padding: 0 20px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    .main {
        flex: 1;
        min-height: 0;
        padding: 24px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
        display: flex;
        flex-direction: column;

        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .user-name {
                font-size: 24px;
                font-weight: bold;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .stu-tag {
                margin-left: 16px;
                color: #D96E00;
                background-color: #FAF4EE;
                font-size: 12px;
                padding: 2px 4px;
                border-radius: 4px;
            }

            .stu-desc {
                color: rgba(0, 0, 0, 0.45);
                font-size: 12px;
            }
        }

        .records-content {
            flex: 1;
            min-height: 0;
            display: flex;
            margin-top: 8px;

            .records-content-left {
                border: 1px solid #e8e8e8;
                border-radius: 2px;
                width: 232px;
                padding: 20px 4px 20px 20px;
                margin-right: 24px;
                display: flex;
                flex-direction: column;
                flex-shrink: 0;
                .section-box-wrapper {
                    flex: 1;
                    min-height: 0;
                    overflow: scroll;
                    padding-right: 10px;
                    .section-box {
                        margin-bottom: 24px;
    
                        .title {
                            font-size: 14px;
                            font-weight: bold;
                            margin-bottom: 16px;
                        }
    
                        .question-number-btn-wrapper {
                            display: grid;
                            grid-template-columns: repeat(5, 1fr);
                            grid-column-gap: 8px;
                            grid-row-gap: 8px;
    
                            .question-number-btn {}
                        }
                    }
                }
            }

            :deep(.records-content-right) {
                width: 100%;
                overflow: visible;

                .ant-tabs-tab:not(.ant-tabs-tab-active) {
                    background-color: #fafafa;
                }

                .ant-tabs-content {
                    height: 100%;
                }
            }
        }

        :deep(.reserve-info) {
            margin-top: 16px;
            display: inline-flex;
            font-size: 12px;
            height: 24px;
            border-radius: 4px;

            .ant-alert-message {
                font-size: 12px;
            }
            .ant-alert-icon {
                margin-top: 0px;
            }
        }
        .reserve-info {
            border: 0;
            display: flex;
            align-items: center;
        }
    }

    #idcard-modal {
        position: fixed;
        border-radius: 8px;
        width: 486px;
        height: 313px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        user-select: none;

        .idcard-modal-header {
            background: #5478ee;
            height: 40px;
            padding: 0 12px;
            font-size: 16px;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;

            .anticon {
                margin-left: 12px;
                cursor: pointer;
            }
        }

        .idcard-modal-content {
            flex: 1;
            min-height: 0;

            #canvas {
                width: 100%;
                height: 100%;
                background-color: #554c4c;
            }
        }
    }
}

.my-btn {
    display: inline-block;
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #ffffff, #ececec);
    border: 1px solid rgba(0, 0, 0, 0.10);
    border-radius: 8px;
    box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    user-select: none;
    transition: all ease .1s;

    &.active {
        background: #5478ee;
        border: 1px solid #5478ee;
        color: #fff;
        box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.15), 2px 2px 1px 0px rgba(0, 0, 0, 0.15) inset, -2px -2px 2px 0px rgba(255, 255, 255, 0.15) inset;
    }

    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}
</style>