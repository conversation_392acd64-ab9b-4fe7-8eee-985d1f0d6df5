<template>
    <div>
        <div class="fold-text-wrapper">
            <input id="exp1" class="exp" type="checkbox">
            <div class="text">
                <label v-if="foldLineClamp < 99" class="btn" for="exp1">
                    <DoubleLeftOutlined />
                </label>
                {{ text }}
            </div>
        </div>
        <div class="feedback-icon-wrapper" v-if="feedbackParams">
            <LikeOutlined v-if="curFeedback === 1" style="color: #5478EE;" @click="curFeedback = -1"></LikeOutlined>
            <DislikeOutlined v-else-if="curFeedback === 0" style="color: #5478EE;" @click="curFeedback = -1"></DislikeOutlined>
            <template v-else>
                <!-- <LikeOutlined class="feedback-icon" @click="handleFeedback(1)"></LikeOutlined> -->
                <!-- <a-divider type="vertical"></a-divider> -->
                <!-- <DislikeOutlined class="feedback-icon" @click="handleFeedback(0)"></DislikeOutlined> -->
            </template>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { aifeedback } from '@/api/exam'
import { DoubleLeftOutlined, LikeOutlined, DislikeOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'
const props = withDefaults(defineProps<{
    text: string
    backgroundColor?: string
    /** 折叠时展示的行数，如果大于100则不需要此功能 */
    foldLineClamp?: number
    feedbackParams?: {
        apiname: string
        input: any
        output: any
    }
}>(), {
    backgroundColor: '#fff',
    foldLineClamp: 3
})

const curFeedback = ref(-1)
watch(() => props.text, () => {
    curFeedback.value = -1
})

/** 
 * @description ai反馈
 * @param feedback 1: 👍 0: 👎
 */
async function handleFeedback(feedback: 0 | 1) {
    if (!props.feedbackParams) return
    try {
        await aifeedback({
            ...props.feedbackParams,
            feedback
        })
        curFeedback.value = feedback
        message.success('感谢您的反馈')
    } catch (error) {
        console.log(error)
    }
}

</script>

<style lang="less" scoped>
body.moon {
    .fold-text-wrapper {
        background-color: #181A20;
    }

    .text {
        color: rgba(255, 255, 255, .85);
        &::after {
            box-shadow: inset calc(100px - 999vw) calc(24px - 999vw) 0 0 #181A20;
        }
    }
}
.fold-text-wrapper {
    display: flex;
    overflow: hidden;
    background-color: v-bind(backgroundColor);
}

.text {
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
    word-wrap: break-word;
    /* display: flex; */
    display: -webkit-box;
    -webkit-line-clamp: v-bind(foldLineClamp);
    -webkit-box-orient: vertical;
    position: relative;
}

.text::before {
    content: '';
    height: calc(100% - 16px);
    float: right;
}

.text::after {
    content: '';
    width: 999vw;
    height: 999vw;
    position: absolute;
    box-shadow: inset calc(100px - 999vw) calc(24px - 999vw) 0 0 v-bind(backgroundColor);
    margin-left: -100px;
}

.btn {
    float: right;
    clear: both;
    margin-left: 10px;
    font-size: 12px;
    border-radius: 4px;
    color: #5478EE;
    cursor: pointer;

    span.anticon {
        margin-right: 4px;
        transform: rotate(-90deg);
    }
}

.btn::after {
    content: '展开'
}

.exp {
    display: none;
}

.exp:checked+.text {
    -webkit-line-clamp: 999;

    &::after {
        visibility: hidden;
    }

    .btn {

        &::after {
            content: '收起'
        }

        span.anticon {
            transform: rotate(90deg);
        }
    }


}
.feedback-icon-wrapper {
    // position: absolute;
    margin-top: 10px;
}
.feedback-icon {
    cursor: pointer;
}
</style>