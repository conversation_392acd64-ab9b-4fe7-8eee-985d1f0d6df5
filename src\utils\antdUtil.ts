import { Modal, ModalFuncProps } from "ant-design-vue"

export function MyConfirm(config: ModalFuncProps) {
    return new Promise((resolve, reject) => {
        Modal.confirm({
            ...config,
            onOk() {
                resolve('ok')
            },
            onCancel() {
                reject('cancel')
            }
        })
    })
}

export function MyInfoModal(config: ModalFuncProps) {
    return new Promise((resolve, reject) => {
        Modal.info({
            ...config,
            onOk() {
                resolve('ok')
            },
            onCancel() {
                reject('cancel')
            }
        })
    })
}